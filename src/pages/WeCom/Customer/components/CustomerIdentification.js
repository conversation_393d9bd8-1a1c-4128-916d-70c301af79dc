import React, { useEffect, useRef, useState } from 'react'
import { Modal, Form, Select, Input, Collapse, Table, Row, Col, Divider, message, Spin, Button } from 'antd'
import moment from 'moment';
import { MinusCircleOutlined } from '@ant-design/icons';
import {nanoid} from 'nanoid'
import { get, post } from '@/utils/request';
import allUrl from '@/utils/url';



const { Option } = Select
const { Panel } = Collapse;




let years = []; let mounths = []; let days = []
for (let i = 1700; i <= 2200; i++) {
    years.push(String(i))
}
for (let i = 1; i <= 12; i++) {
    if (i <= 9) {
        mounths.push(`0${i}`)
    } else {
        mounths.push(String(i))
    }
}
for (let i = 1; i <= 31; i++) {
    if (i <= 9) {
        days.push(`0${i}`)
    } else {
        days.push(String(i))
    }
}


const CustomerIdentification = (props) => {
    const { visible, onCancel, recordObj } = props
    const [form] = Form.useForm()
    const [required, setRequired] = useState(true)
    const [isHiden, setisHiden] = useState(true)
    const [loading, setLoading] = useState(false)
    const [customerType, setCustomerType] = useState(false)
    const [dataSource, setDataSource] = useState([{ deliverOrder: '', key: 1, uid: nanoid(), name: 'deliverOrder^' + nanoid() }])
    const [dataSource1, setDataSource1 ] = useState([])

    const saveData = (params) =>{
        console.log(params)
        setLoading(true)
        post(allUrl.WeCom.saveAffirmForBackend, { ...params }).then(res => {
            if (res.success) {
                message.success(res.msg)
                setTimeout(() => {
                    onCancel()
                }, 200)
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }

    const handleOk = () => {
        let deliverOrderNos = dataSource.filter(item => item.deliverOrder).map(item => item.deliverOrder)
        form.validateFields().then(values => {
            console.log(99999, values, dataSource)

            let params = {
                mobilePhone: values.mobilePhone || '',
                trueName: values.trueName,
                customerType: values.customerType,
                externalUserId: recordObj.externalUserId,
            }
            // 车主类型是购车人或代理人时才传交付单号字段
            if(values.customerType == 5) {
                params.deliverOrderNos = deliverOrderNos
            }
            saveData(params)
        })
        // .catch(({errorFields,values})=>{
        //     console.log(errorFields,values)
        //     if (values.customerType != 5) return

        //     let arr1 = [] // 成功
        //     let arr2 = [] // 格式校验错误
        //     let arr3 = [] // 数据不存在
        //     console.log('dataSource', dataSource)
        //     dataSource.forEach(item => {
        //         if (item.checkResult == 1) arr1.push(item)
        //         if (item.checkResult == 2) arr2.push(item)
        //         if (item.checkResult == 3) arr3.push(item)
        //     })

        //     console.log('arr1', arr1)
        //     console.log('arr2', arr2)
        //     console.log('arr3', arr3)


        //     // 有格式错误
        //     if (arr2.length) {
        //         console.log(form)
        //         // this.$refs.form.scrollToField()
        //         form.scrollToField(arr2[0].name)
        //         return
        //     }

        //     // 无格式错误，有数据不存在
        //     if (!arr2.length && arr3.length) {
        //         Modal.confirm({
        //             title:'提示',
        //             content:'有未查询到的交付单，是否仍然保留？',
        //             okText:'保留',
        //             cancelText:'取消',
        //             onOk:()=>{
        //                 let params = {
        //                     mobilePhone: values.mobilePhone,
        //                     trueName: values.trueName,
        //                     customerType: values.customerType,
        //                     externalUserId: recordObj.externalUserId,
        //                     deliverOrderNos
        //                 }
        //                 saveData(params)
        //             }
        //         })
        //     }
   
        // })
    }

    const getData = () => {
        setLoading(true)
        post(allUrl.WeCom.getAffirmForBackend, { externalUserId: recordObj.externalUserId }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                console.log(Dt)

                form.setFieldsValue({
                    customerType: Dt.customerType || '',
                    mobilePhone: Dt.mobilePhone || '',
                    trueName: Dt.trueName || '',
                })
                customerTypeChange(Dt.customerType)
                if (Dt.deliverOrderNos && Dt.deliverOrderNos.length) {
                    let arr = Dt.deliverOrderNos.map((item,index)=>{
                        return {
                            key: index+1,
                            uid: nanoid(),
                            name : 'deliverOrder^' + nanoid(),
                            deliverOrder:item
                        }
                    })
                    arr.push({
                        key:arr.length+1,
                        uid: nanoid(),
                        name : 'deliverOrder^' + nanoid(),
                        deliverOrder:''
                    })
                    setDataSource(arr)
                }
            } else {
                // message.error(res.msg)
            }
        })
        post(allUrl.WeCom.queryAffirmLog, { objectKey: recordObj.externalUserId }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                console.log(Dt)
                let arr = []
                Dt.length && Dt.forEach((item) => {

                    arr.push(item.log || {})
                })
                setDataSource1(arr)
            } else {
                // message.error(res.msg)
            }
        })
        setLoading(false)
    }

    const customerTypeChange = val => {
        setCustomerType(val)
        if (val == '4') {
            setRequired(false)
        } else {
            setRequired(true)
        }
        if (val == '5') {
            setisHiden(false)
        } else {
            setisHiden(true)
        }
    }

    const deliverOrderChange = (e, record) => {
        let value = (e.target.value).trim()
        let dom = document.getElementsByClassName('ant-table-body')
        let newData = [...dataSource]
        let temp = newData.filter(item => item.uid === record.uid)[0]
        temp.deliverOrder = value
        if (newData[newData.length - 1].uid === record.uid && value) {
            newData.push({ key: newData.length + 1, uid: nanoid(), deliverOrder: '', name: 'deliverOrder^' + nanoid() })
            setTimeout(() => {
                if(dom.length===3){
                    dom[2].scrollTop = 60000
                }
            })
        }
        setDataSource(newData)
    }

    const rowDel = (record) => {
        let newData = [...dataSource]
        let temp = newData.filter(item => item.uid !== record.uid)
        setDataSource(temp)
    }

    const asyncValidator = (_, val) => {
        console.log(9999, _,val)
        // const arr = [...dataSource]
        // const temp = arr.filter(item=>item.name === _.field)[0]
        // console.log(temp,arr)
        if (!val) return Promise.resolve();
        return new Promise((resolve, reject) => {
            post(allUrl.WeCom.checkDeliveryOrderNoForBackend, { orderNo: val }).then(res => {
                if (res.success) {
                    console.log('校验交付单号返回', res)
                    if(!res.resp[0]) {
                        resolve()
                    } else {
                        reject(res.resp[0])
                    }
                    // resolve()
                    // if (res.resp[0].checkResult == 1) {
                    //     temp.checkResult = 1
                    //     resolve()
                    // }
                    // if (res.resp[0].checkResult == 2) {
                    //     temp.checkResult = 2
                    //     reject('单号格式错误无法保存，请核对后重输。')
                    // }
                    // if (res.resp[0].checkResult == 3) {
                    //     temp.checkResult = 3
                    //     reject('单号暂无，若核实无误可忽略提示保存~')
                    // }
                    // setDataSource(arr)
                } else {
                    // message.error(res.msg)
                }
            })

        })
    }
    // 验证手机号和客户类型相关性
    const asyncValidator1 = (_, val) => {
        
        if (val && !/^[1][2,3,4,5,6,7,8,9][0-9]{9}$/.test(val)) {
            return Promise.reject(new Error('请输入正确的手机号！'));
        } 
        return new Promise((resolve, reject) => {
            post(allUrl.WeCom.checkPhoneAndCustomerTypeForBackend, { phone: val, customerType, externalUserId: recordObj.externalUserId }).then(res => {
                if (res.success) {
                    if(!res.resp[0]) {
                        resolve()
                    } else {
                        reject(res.resp[0])
                    }
                } else {
                    // message.error(res.msg)
                }
            })
        })
    }
    const orderNoRowHidden = (record, key) => {
        let arr = []
        dataSource1.map((item,index) => {
            if(item.changeTime === record.changeTime) item[key] = true
            arr.push(item);
        })  
        setDataSource1(arr)
    }
    const renderOperation = (text, record) => {
        return <div key={record.changeTime}>
            <div>客户类型: {record.oldCustomerTypeName || '-'}</div>
            <div>姓名: {record.oldName || '-'}</div>
            <div>手机号: {record.oldPhone || '-'}</div>
            <div>交付单号:
            {
                record.oldDeliverOrderNos && record.oldDeliverOrderNos.length ? 
                record.oldDeliverOrderNos.length > 1 ?
                record.oldDeliverOrderNos.map((item, index) => {
                    return record._oldShow ?
                    <div>
                        <dic>{item}</dic>
                    </div>
                    : index === 0 && <>
                        <div>{item}</div>
                        <Button type="link" onClick={()=>orderNoRowHidden(record, '_oldShow')}>加载更多</Button>
                    </>
                }) 
                : <div>{record.oldDeliverOrderNos[0] || '-'}</div>
                : '-'
            }
            </div>
        </div>
    }
    const renderOperation1 = (text, record) => {
        return <div key={record.changeTime}>
            <div>客户类型: {record.newCustomerTypeName || '-'}</div>
            <div>姓名: {record.newName || '-'}</div>
            <div>手机号: {record.newPhone || '-'}</div>
            <div>交付单号：
            {
                record.newDeliverOrderNos && record.newDeliverOrderNos.length ? 
                record.newDeliverOrderNos.length > 1 ?
                record.newDeliverOrderNos.map((item, index) => {
                    return record._newShow ?
                    <div>
                        <dic>{item}</dic>
                    </div>
                    : index === 0 && <>
                        <div>{item}</div>
                        <Button type="link" onClick={()=>orderNoRowHidden(record, '_newShow')}>加载更多</Button>
                    </>
                }) 
                : <div>{record.newDeliverOrderNos[0] || '-'}</div>
                : '-'
            }
            </div>
        </div>
    }

    const columns = [
        { title: '时间', dataIndex: 'changeTime',width: 120 },
        { title: '修改人账号ID', dataIndex: 'userId', width: 140 },
        { title: '修改人姓名', dataIndex: 'userName', width: 140 },
        { title: '修改人岗位', dataIndex: 'positionName', width: 140 },
        { title: '修改人所属组织', dataIndex: 'orgName', width: 160 },
        { title: '修改人所属用户中心', dataIndex: 'dealerFullName', width: 180 },
        { title: '保存前内容', width: 360, render: (text, record) => renderOperation(text, record) },
        { title: '保存后内容', width: 360, render: (text, record) => renderOperation1(text, record) },
    ]
    const columns1 = [
        { title: '序号', dataIndex: 'index', width: 100, render: (text, record, index) => <div style={{ textAlign: 'center' }}>{index + 1}</div> },
        {
            title: <div className='RequireAsterisk'>交付单号</div>, dataIndex: 'deliverOrder', render: (text, record, index) => {
                return <Form.Item wrapperCol={{span:24}} name={record.name} initialValue={text} rules={[
                    { required: dataSource.length!==1 && dataSource[dataSource.length - 1].uid === record.uid ? false : true, message: '请输入交付单号！' },
                    { required: dataSource.length!==1 && dataSource[dataSource.length - 1].uid === record.uid ? false : true, validator: asyncValidator, },
                ]} style={{ marginBottom: '0' }}>
                    <Input placeholder='请输入' allowClear onChange={(e) => deliverOrderChange(e, record)} />
                </Form.Item>
            }
        },
        {
            title: '操作', dataIndex: 'action', width: 140, render: (text, record, index) => {
                return <div>
                    {
                        index !== 0 ?
                            <MinusCircleOutlined style={{ cursor: 'pointer' }} onClick={() => rowDel(record)} /> : null
                    }
                </div>
            }
        },
    ]
    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
    };

    useEffect(() => {
        getData()
    }, [])
    return <Modal title={'客户认定'} visible={visible} onCancel={onCancel} onOk={handleOk} maskClosable={false} width={1200} confirmLoading={loading}>
        <Spin spinning={loading}>
            <Collapse defaultActiveKey={['1']}>
                <Panel header="基本信息" key="1">
                    <Form form={form} {...layout} validateTrigger='onBlur' scrollToFirstError={true}>
                        <Row>
                            <Col span={12}>
                                <Form.Item label='客户类型' name='customerType' rules={[{ required: true, message: '请选择客户类型！', }]}>
                                    <Select onChange={customerTypeChange} placeholder='请选择'>
                                        <Option value={'1'}>车主</Option>
                                        <Option value={'2'}>购车人</Option>
                                        <Option value={'3'}>购车人&车主</Option>
                                        <Option value={'5'}>购车人/车主代理人</Option>
                                        <Option value={'4'}>其他</Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label='姓名' name='trueName' rules={[{ required: required, message: '请输入姓名！', }]}>
                                    <Input placeholder='请输入' allowClear />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label='手机号'
                                    name='mobilePhone'
                                    rules={[
                                        { required: required, message: '请输入手机号！', },
                                        {
                                            validator: asyncValidator1
                                        }
                                    ]}
                                >
                                    <Input placeholder='请输入' allowClear/>
                                </Form.Item>
                            </Col>
                        </Row>
                        <div><span style={{ color: 'red' }}>*</span>为必填，客户真实姓名和手机号以交付单为准，若客户类型为“购车人/车主代理人”，则需填写代表的交付单号。</div>
                        {
                            !isHiden ?
                                <Row>
                                    <Table rowKey='uid' scroll={{y:300}} className='customerIdentification-table' bordered columns={columns1} dataSource={dataSource} pagination={false} style={{ width: '100%', marginTop: '20px' }} size='small' />
                                </Row> : null
                        }
                    </Form>
                </Panel>
                <Panel header="认定日志" key="2">
                    <Table rowKey='key' columns={columns} dataSource={dataSource1} pagination={false} scroll={{ y: 500 }} />
                </Panel>
            </Collapse>
        </Spin>
    </Modal>
}

export default CustomerIdentification