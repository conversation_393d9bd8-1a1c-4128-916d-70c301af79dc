import React ,{useState,useEffect}from 'react'
import { Modal ,Form ,Select,message,Spin} from 'antd'
import allUrl from '@/utils/url'
import {get,post} from '@/utils/request'

const {Option} = Select

export default (props) =>{
    const {visible,onCancel,dealerCode,cb,rowData,locationParmas} = props
    const [projectManagerList,setProjectManagerList] = useState([])
    const [newDealerCode,setNewDealerCode] = useState([dealerCode])
    const [userName, setUserName] = useState('')
    const [userId, setUserId] = useState('')
    const [loading,setLoading] = useState(false)
    const [form] = Form.useForm()

    const hanldeOk = () =>{
        let str = Object.keys(rowData).length ? `${rowData.storeName}(${rowData.dealerCode})的项目经理将从${rowData.managerUserName}改为${userName}，是否确定？`
        :`${locationParmas.storeName}(${locationParmas.dealerCode})的项目经理将设置为${userName}，是否确定？`
        form.validateFields().then(values=>{
            Modal.confirm({
                title:str,
                onOk:()=>{
                    let data = {
                        userName,userId,
                        newDealerCode:newDealerCode
                    }
                    setLoading(true)
                    post(allUrl.StoreManage.bindStoreManager,data).then(res=>{
                        if(res.success){
                            message.success(res.msg)
                            setTimeout(()=>{
                                onCancel()
                                cb()
                            },300)
                        }else{
                            // message.error(res.msg)
                        }
                        setLoading(false)
                    }).catch(error=>{
                        setLoading(false)
                    })
                },
                onCancel:()=>{
                    return false
                }
            })
        })
    }
    const onChange = (LabeledValue,option) =>{
        setUserName(option.children)
        setUserId(option.value)
    }

    const getProjectManagerList = (userName) =>{
        get(allUrl.StoreManage.getProjectManagerList,{userName:userName?userName:''}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setProjectManagerList(Dt)
            }else{
                // message.error(res.msg)
            }
        })
    }

    useEffect(()=>{
        getProjectManagerList()
    },[])
    return <Modal title={'设置项目经理'} visible={visible} onOk={hanldeOk} onCancel={onCancel} maskClosable={false} confirmLoading={loading}>
        <Spin spinning={loading}>
            <Form layout='vertical' form={form}>
                <Form.Item label='项目经理姓名' name='dd' rules={[{ required:  true, message: '请选择项目经理'}]}>
                    <Select
                        showSearch
                        placeholder="请输入项目经理姓名搜索"
                        optionFilterProp="children"
                        onChange={onChange}
                        // onSearch={onSearch}
                        filterOption={(input, option) =>
                        option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {
                            projectManagerList.map((item,index)=>{
                                return <Option key={index} value={item.userId}>{item.userName}</Option>
                            })
                        }
                    </Select>
                </Form.Item>
            </Form>
        </Spin>
    </Modal>
}