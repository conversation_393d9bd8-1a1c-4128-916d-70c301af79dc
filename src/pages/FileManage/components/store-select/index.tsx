import { TreeSelect, TreeSelectProps } from 'antd';
import React, { FC, useMemo } from 'react';
import { useOrgStores } from '../../hooks';

const transformStoreDataToTreeData = (
  data: FileManage.StoreOrganizationVO[] = [],
  parentCode?: string
): FileManage.StoreOrganizationVO[] => {
  return data.map((item) => {
    const node = {
      ...item,
      code: item.area ? [parentCode, item.code].filter(Boolean).join('-') : item.code,
    };
    if (item.children) {
      node.children = transformStoreDataToTreeData(item.children, node.code);
    }

    return node;
  });
};

const StoreSelect: FC<Omit<TreeSelectProps<string, FileManage.StoreOrganizationVO>, 'options'>> = (props) => {
  const { data, loading } = useOrgStores();

  const treeData = useMemo(() => {
    return transformStoreDataToTreeData(data);
  }, [data]);
  return (
    <TreeSelect
      allowClear
      {...props}
      loading={loading}
      value={loading ? undefined : props.value}
      treeCheckable
      treeData={treeData}
      fieldNames={{ value: 'code', label: 'name' }}
      treeNodeFilterProp="name"
    />
  );
};

export default StoreSelect;
