import React, { useState } from 'react'
import {Modal,Radio,Spin,Space, message} from 'antd'
import { post } from '../../../../utils/request'
import allUrl from '../../../../utils/url'
import moment from 'moment'
import {fileDown} from '@/utils'
const ExportExcel = (props) =>{
    const {visible,onCancel,defaultQuery,exportDataObj} = props
    const [RadioValue,setRadioValue] = useState('part')
    const [loading,setLoading] = useState(false)
    const handleOk = () =>{
        console.log(RadioValue)
        let query = {}
        if(!RadioValue) return message.error('请选择导出数据类型！')
        if(RadioValue === 'part'){
            query = defaultQuery
            if(query.createTime && query.createTime.length){
                query.createTimeStart =  moment(query.createTime[0]).format('YYYY-MM-DD')
                query.createTimeEnd =  moment(query.createTime[1]).format('YYYY-MM-DD')
                delete query.createTime
            }
        }
        query.exoprtType = RadioValue
        setLoading(true)
        post(allUrl.StoreManage.exportStore,{...query},{responseType: "blob"}).then(res=>{
            fileDown(res,'AITO厅店人员信息')
            setLoading(false)
            onCancel()
        })
    }
    const onChange = (e) =>{
        setRadioValue(e.target.value)
    }
    return <Modal title={'导出为EXCEL'} visible={visible} onOk={handleOk} onCancel={onCancel} maskClosable={false} confirmLoading={loading}>
        <Spin spinning={loading}>
            <Radio.Group onChange={onChange} value={RadioValue}>
                <Space direction="vertical">
                    <Radio value='part'>满足当前筛选条件的数据，共 {exportDataObj?.storeCountByParm || 0} 条</Radio>
                    <Radio value='all'>所有数据，共 {exportDataObj?.storeCount || 0} 条</Radio>
                </Space>
            </Radio.Group>
            {/* <Alert style={{marginTop:'26px'}} message="注意：若导出的数据超过10000条，将分sheet导出。" type="info" showIcon /> */}
        </Spin>
    </Modal>
}
export default ExportExcel