import React,{useEffect, useState} from 'react'
import {Modal,Input,Button} from 'antd'
import {ExclamationCircleFilled} from '@ant-design/icons';
import baseURL from '@/baseURL'
import UploadFile from '@/components/Public/UploadFile'
const ImportID = (props) =>{
    const {title,visible,onCancel,UploadChange,type,url,loading} = props
    const [taskID,setTaskID] = useState('')
    const handleOk = () =>{

    }
    useEffect(()=>{
        setTaskID('')
    },[visible])
    return <Modal title={title} visible={visible} footer={null} maskClosable={false} onCancel={onCancel}>
                <div style={{display:'flex'}}>
                    <div style={{height:'34px',lineHeight:'50px'}}>
                        <ExclamationCircleFilled style={{marginRight:'7px',color:'#faad14',fontSize:'16px'}} />请输入任务ID：
                    </div>
                    <Input style={{marginTop:'10px',width:300}} allowClear value={taskID} onChange={(e)=>setTaskID(e.target.value)} />
                </div>
                <div style={{marginTop:'20px',textAlign:'right'}}>
                    <Button style={{margin:'0 10px'}} onClick={onCancel} >取消</Button>
                    <UploadFile
                        style={{ display: 'inline-block' }}
                        extension={['xls', 'xlsx']}
                        showUploadList={false}
                        size={5}
                        action={baseURL.Host + url}
                        UploadChange={UploadChange}
                        type={type}
                        data={{id:taskID}}
                    >
                        <Button loading={loading} style={{margin:'0 10px'}} type='primary' onClick={handleOk}>确定</Button>
                    </UploadFile>
                </div>

    </Modal>
}

const ImportButton = (props) =>{
    const {loading,children,style,url} = props
    const [ImportIDVisible,setImportIDVisible] = useState(false)
    const UploadChange = ({file,fileList},type) =>{
        setImportIDVisible(false)
        props.UploadChange({file,fileList},type)
    }
    return <>
        <Button loading={loading} onClick={()=>setImportIDVisible(true)} style={{...style}}>{children}</Button>
        <ImportID url={url} loading={loading} title='提示' UploadChange={UploadChange} visible={ImportIDVisible} onCancel={()=>setImportIDVisible(false)}/>
    </>
}


export default ImportButton