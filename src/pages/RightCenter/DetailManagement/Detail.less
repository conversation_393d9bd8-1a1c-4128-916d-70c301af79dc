.PublicListDetail {

    // padding-left: 24px;
    .queryArea {
        margin-left: 24px;
    }

    .flex {
        display: flex;
        align-items: center;
    }

    .border-bottom {
        border-bottom: solid 1px #e8e8e8;
    }

    .tableTitle {
        background-color: white;
        padding: 24px 24px 0px 32px;
        justify-content: space-between;

        .text {
            font-size: 20px;
            font-family: PingFangSC, PingFangSC-Medium, sans-serif;
            font-weight: 500;
            text-align: left;
            color: rgba(0, 0, 0, 0.85);
            line-height: 28px;
        }

        .bts {
            .ant-btn {
                margin: 0 7px;
            }
        }
    }

    .tableData {
        padding: 24px;
        background: #f0f2f5;

        .top-box {
            background-color: #D6EBFF;
            height: 50px;
            // margin-top: 24px;
            padding-left: 24px;
            line-height: 50px;

            .text {
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                font-weight: 500;
            }

            .red-text {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #F5222D;
                margin-left: 20px;
            }

            .icon-box {
                width: 16px;
                height: 16px;
                margin-right: 8px;
                cursor: pointer;
                margin-top: -2px;
            }
        }

        .info-box {
            background: white;
            padding: 24px;
        }

        .detailPanel {
            padding: 24px;
            background-color: white;
            margin-bottom: 24px;

            .detail-title {
                margin-right: 17px;
                height: 30px;
                border-bottom: 2px solid #5095D5;

                .left-title {
                    width: 122px;
                    height: 30px;
                    background: #5095D5;
                    font-size: 15px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #FFFFFF;
                    line-height: 30px;
                    text-align: center;
                }

                margin-bottom: 20px;
            }

            .detail-sence {
                line-height: 1;
                margin: 20px 0;

                .ant-descriptions-item-label,
                .ant-descriptions-item-content {
                    color: #2C73B4;
                    font-weight: 500;
                    font-size: 15px;
                    font-family: PingFangSC, PingFang SC;
                }
            }

            .strategy-box {
                background: #F7FBFF;
                margin-bottom: 24px;

                &:last-child {
                    margin-bottom: 0;
                }

                .ant-descriptions-header {
                    .ant-descriptions-title {
                        font-size: 14px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        color: rgba(0, 0, 0, 0.85);
                    }

                    height: 36px;
                    background: #D6EBFF;
                    line-height: 36px;
                    padding: 0 18px;
                }

                .ant-descriptions-view {
                    padding: 4px 18px 8px 18px;
                }

                .ant-descriptions-item-content {
                    position: relative;

                    .time-box {
                        position: absolute;

                        span.end-time:last-child {
                            margin-top: 16px;
                            display: inline-block;
                            margin-left: -10px;
                        }
                    }

                    .public-end-time {
                        color: #1890FF;

                        .box {
                            color: rgba(0, 0, 0, 0.85);
                        }
                    }
                }

                .m-top {
                    .ant-descriptions-item-container {
                        margin-top: 38px;
                    }
                }

                .tips {

                    .ant-descriptions-item-content {
                        height: 22px;
                        font-size: 15px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        color: #2C73B4;
                        line-height: 22px;
                    }
                }
            }

        }

        // .ant-table-wrapper {
        //     background-color: white;

        //     // .ant-table{
        //     //     padding: 24px;
        //     // }
        //     .ant-table-pagination {
        //         margin: 16px 24px;
        //     }
        // }
    }
}

.detail-overflow {
    height: calc(100vh - 48px - 54px - 80px);
    overflow-y: auto;
}