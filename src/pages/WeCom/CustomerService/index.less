
.CustomerService {
    .ant-tabs{
        .ant-tabs-nav{
            margin-left: 24px;
            margin-bottom: 0;
        }
    }
    .tableData{
        background-color: white;
        // padding: 24px 24px 72px 24px;

        border-top: solid 24px #f0f2f5;
        border-right: solid 24px #f0f2f5;
        border-bottom: solid 24px #f0f2f5;
        border-left: solid 24px #f0f2f5;
        .ant-table-wrapper{
            // background-color: white;
            .ant-table{
                padding: 24px;
                .ant-table-container{
                    border: 0;
                    .ant-table-content{
                        // overflow: auto hidden;
                        .ant-table-thead{
                            tr>th,tr>td{
                                // border: 0;
                            }
                        }
                        .ant-table-tbody{
                            .ant-table-cell{
                                .isReceivedWrapper{
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                    .ant-table-pagination{
                        margin: 16px 24px;
                    }
                }
            }
        }
        .tableTitle{
            padding: 24px 24px 0px 32px;
            justify-content: space-between;
            .text{
                font-size: 20px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.85);
                line-height: 28px;
            }
            .bts{
                .ant-btn{
                    margin:0 7px;
                }
            }
        }
    }
    .ant-pagination{
        padding-right: 20px;
    }
    .PublicList_FormQuery{
        padding-top: 30px;
        padding-left: 24px;
        .ant-col-7{
            .ant-form-item{
                .ant-form-item-control-input{
                    width: 90%;
                    .ant-form-item-control-input-content{
                        .ant-picker{
                            width: 100%;
                        }
                    }
                }
            }
        }
        .FormQuerySubmit{
            display:flex;
            justify-content: flex-end;
            .operationButtons{
                span{
                    color: #1890ff;
                    cursor: pointer;
                    .anticon{
                        margin-left: 6px;
                    }
                }
            }
        }
    }
    .customer-manage {
        padding: 32px;
        .info {
            display: flex;
            align-items: center;
            .photo {
                width: 60px;
                height: 60px;
                border-radius: 50%;
            }
            .name {
                color: #333;
                margin-left: 16px;
            }
            .unit {
                color: #1890FF;
                margin-left: 16px;
                .unit-text {
                    margin-right: 24px;
                }
            }
        }
        .info-create {
            .photo {
                width: 60px;
                height: 60px;
                border-radius: 8px;
                margin-right: 16px;
            }
            .form-item {
                display: flex;
                align-items: center;
                padding: 16px 0;
                .label {
                    width: 160px;
                }
            }
        }
        .list-box {
            margin-top: 32px;
            .item {
                display: flex;
                align-items: center;
                padding: 16px 0;
                .label {
                    width: 160px;
                }
                .values {
                    display: flex;
                    align-items: center;
                    .name {
                        color: #666;
                        font-size: 12px;
                        margin-right: 16px;
                        padding: 2px 6px;
                        border: 1px solid #999;
                        border-radius: 2px;
                        display: flex;
                        align-items: center;
                        span {
                            margin-left: 2px;
                        }
                    }
                    .edit {
                        margin-left: 24px;
                    }
                }
                .wel-val {
                    padding: 12px;
                    border: 1px solid #F2F2F2;
                    border-radius: 4px;
                    width: 600px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
            }
        }
    } 
}

.modalContainer {
    max-height: 640px;
    min-height: 320px;
    overflow-y: auto;
    .container {
        width: 100%;
        display: flex;
        .tree-depart {
            flex: 1;
        }
        
        .selected-box {
            flex: 1;
            margin-left: 24px;
            .title {
                color: #666;
                
            }
            .list {
                margin-top: 24px;
                .item {
                    padding: 8px 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .name {
                        color: #666;
                        .name-text {
                            margin-left: 4px;
                        }
                    }
                    .close-icon {
                        color: #666;
                        font-size: 12px;
                    }
                    
                }
            }
        }
    }
    .time-container {
        .form-item {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            .values {
                .line {
                    padding:  0 16px;
                }
                .time-text {
                    margin-right: 16px;
                }
            }
            
        } 
        .label {
            margin-right: 32px;
            color: #999;
        }

        .tips {
            border: 1px solid rgba(7, 15, 26, 0.15);
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .wel-list {
            .wel-item {
                display: flex;
                border: 1px solid rgba(7, 15, 26, 0.15);
                border-radius: 6px;
                padding: 16px;
                margin-bottom: 16px;
                .wel-content {
                    flex: 1;
                }
                .select-icon {
                    width: 20px;
                    height: 20px;
                }
            }
            .wel-item-active{
                border: 1px solid #1b5bb0;
            }

        }
        .add-wel {
            position: absolute;
            bottom: 16px;
            left: 30px;
            color: #1890FF;
            cursor: pointer;
        }

        .eval-content {
            border-top: 1px solid #f2f2f2;
            
            .title {
                font-size: 18px;
                color: #333;
                font-weight: 600;
                margin-top: 16px;
            }
            .item {
                margin-top: 16px;
                .i-label {
                    margin-bottom: 8px;
                }
            }
        }

    }
    .wel-container {
        display: flex;
        .left {
            flex: 1;
            .textarea-box {
                width: 100%;
                min-height: 240px;
                padding: 12px;
                background-color: #fbfbfb;
                border-radius: 6px;

                .tip-btn {
                    display: flex;
                    align-items: center;
                    margin-top: 10px;
                    padding: 8px 0;
                    border-top: 1px solid rgba(6, 15, 26, 0.07);;
                    .btn {
                        cursor: pointer;
                        color: #1890FF;
                        margin-right: 8px;
                    }
                    .tip {
                        font-weight: 400;
                        font-size: 12px;
                        color: rgba(9, 17, 26, 0.4);
                    }
                }
            }
            .form-item {
                display: flex;
                align-items: center;
                margin-top: 24px;
            } 
            .label {
                margin-right: 8px;
                color: #999;
            }
        }
        .right {
            border-radius: 4px;
            position: relative;
            margin-left: 40px;
            display: inline-block;
            font-size: 0;
            line-height: 0;
            background-image: url(https://wwcdn.weixin.qq.com/node/wework/images/202208021446.b0d1fdefcd.png);
            background-repeat: no-repeat;
            width: 240px;
            min-height: 420px;
            background-size: 240px auto;
        }
    }
}


// 弹窗样式，和上面的样式区域是同一级的DOM节点
.ant-modal-root{
    .ant-tabs{
        .ant-tabs-content-holder{
            .ant-tabs-content{
                .ant-tabs-tabpane{
                    .already-read-wrapper{
                        .already-read-list{
                            .more-info{
                                color:#888;
                                text-align:center;
                            }
                        }
                    }
                }
            }
        }
    }
}



.tree-box {
    max-height: 400px;
    overflow-y: auto;
}

.session-item {
    width: 100%;
    .time {
        text-align: center;
        color: #999;
        font-size: 12px;
    }
}
.ant-list-split .ant-list-item {
    border-bottom: none !important;
}
.avatar-name {
    display: flex;
    align-items: center;
    .photo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-right: 16px;
    }
}
