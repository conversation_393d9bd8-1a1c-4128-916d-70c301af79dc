/**设置localStorage过期时间 start */

/**
 *
 * @param {*} key 键名
 * @param {*} value 实际的值
 * @param {*} expire 过期时间（毫秒）
 */
Storage.prototype.setExpire = (key, value, expire) => {
  let obj = {
    data: value,
    time: Date.now(),
    expire: expire,
  };
  localStorage.setItem(key, JSON.stringify(obj));
};

Storage.prototype.getExpire = (key) => {
  let val = localStorage.getItem(key);
  if (!val) {
    return val;
  }
  val = JSON.parse(val);
  if (Date.now() - val.time > val.expire) {
    localStorage.removeItem(key);
    return null;
  }
  return val.data;
};

/**设置localStorage过期时间 end */
