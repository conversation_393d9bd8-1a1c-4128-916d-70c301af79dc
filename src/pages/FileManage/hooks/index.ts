import {
  getSeresFileOrganizationStoreList,
  getSeresFileOrganizationSupplierList,
  getSeresFilePostFrequentList,
  getSeresFilePostStoreList,
  getSeresFilePostSupplierList,
} from '@/apis/file-manage';
import { useRequest } from '@/hooks/request';

export function useOrgStores() {
  const res = useRequest(getSeresFileOrganizationStoreList, {
    cacheKey: 'orgStores',
  });

  return res;
}

export function useOrgSuppliers() {
  const res = useRequest(getSeresFileOrganizationSupplierList, {
    cacheKey: 'orgSuppliers',
  });

  return res;
}

export function useStoreJobs() {
  const res = useRequest(getSeresFilePostStoreList, {
    cacheKey: 'storeJobs',
  });

  return res;
}

export function useSupplierJobs() {
  const res = useRequest(getSeresFilePostSupplierList, {
    cacheKey: 'supplierJobs',
  });

  return res;
}

export function useCommonJobs() {
  const res = useRequest(getSeresFilePostFrequentList, {
    cacheKey: 'commonJobs',
  });

  return res;
}
