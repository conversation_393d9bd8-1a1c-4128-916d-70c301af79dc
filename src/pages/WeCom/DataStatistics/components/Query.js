import React, { useState, memo, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'
import { Form, Button, Input, Select, Row, Col, DatePicker } from 'antd'
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import _ from 'lodash'
import PropTypes from 'prop-types';
import {setStorage,getStorage,removeStorage} from '@/utils/Storage'

const { Option } = Select
const { RangePicker } = DatePicker;

const FormItem = (props) => {
    const { item, index, handleSearch } = props
    const { type, data = [], label = '', name, rules = [], initialValue = null, placeholder, allowClear = true, mode = '', showSearch = false, isPressEnter = true } = item
    return (
        <Col key={index}>
            <Form.Item label={label} name={name} rules={rules} initialValue={initialValue}>
                {
                    type === 'Input' ?
                        <Input placeholder={placeholder || '请输入...'} allowClear={allowClear} onPressEnter={isPressEnter ? handleSearch : null} />
                        : type === 'Select' ?
                            <Select placeholder={placeholder || '请选择...'} allowClear={allowClear} mode={mode} showSearch={showSearch} optionFilterProp='children' filterOption={(input, option) =>
                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }>
                                {data && data.length ? data.map((ele, i) => <Option key={i} value={ele.value}>{ele.name}</Option>) : null}
                            </Select>
                            : type === 'RangePicker' ?
                                <RangePicker placeholder={placeholder || ['开始时间','结束时间']} style={{ width: '100%' }} />
                                : type === 'DatePicker' ?
                                    <DatePicker placeholder={placeholder || '请选择...'} style={{ width: '100%' }} />
                                    : null
                }
            </Form.Item>
        </Col>
    )
}

FormItem.propTypes = {
    item:PropTypes.object.isRequired,
    index:PropTypes.number.isRequired,
    handleSearch:PropTypes.func.isRequired  
}


const PublicTableQuery = forwardRef((props, parentRef) => {
    const { onSearch, searchList, defaultQuery, initPage ,isCatch} = props
    const [form] = Form.useForm()
    const [formDown, setFormDown] = useState(false)
    const [searchFormList, setSearchList] = useState([])
    const [isPutItAway, setIsPutItAway] = useState(true)
    const [colNum, setColNum] = useState(4)
    const handleSearch = () => {
        form.validateFields().then(values => {
            for (let i in values) {
                if (!values[i]) {
                    values[i] = ''
                }
                if (!i) {
                    delete values[i]
                }
            }
            values._t = new Date().getTime()
            if(isCatch){
                setStorage({
                    Query:values,
                    Type:window.location.hash
                })
            }
            onSearch(values)
        })
    }
    const onReset = () => {
        form.resetFields()
        if(isCatch){
            removeStorage(window.location.hash)
        }
        onSearch({_t:new Date().getTime()})
    }
    const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
    };
    const onResize = useCallback(() => {
        const width = document.documentElement.clientWidth
        let num = 4;
        if (width >= 1600) {
            num = 4
        } else if (width >= 1200) {
            num = 3
        } else if (width >= 560) {
            num = 2
        } else {
            num = 1
        }
        setColNum(num)
    }, [searchList])
    useEffect(() => {
        let newSearchList = _.cloneDeep({ searchList }).searchList;
        if (newSearchList.length <= colNum - 1) {
            setIsPutItAway(false)
        } else {
            setIsPutItAway(true)
        }
        let row = Math.ceil(searchList.length / colNum)    //查询总行数
        let sumCol = row * colNum    //全部Col的个数
        let diffCol = sumCol - searchList.length   //差的Col的个数
        let addCol = 0  //添加Col的个数
        if (colNum > 1) {
            addCol = diffCol == 0 ? colNum - 1 : diffCol - 1
        } else {
            setFormDown(true)
            setIsPutItAway(false)
        }

        for (let i = 0; i < addCol; i++) {
            newSearchList.push({})
        }
        setSearchList(newSearchList)
    }, [colNum, searchList])
    useEffect(() => {
        window.addEventListener('resize', onResize);
        return (() => {
            window.removeEventListener('resize', onResize)
        })
    }, [])
    useEffect(()=>{
        onResize()
    },[searchList])
    useEffect(() => {
        if (JSON.stringify(defaultQuery) !== '{}') {
            getStorage(window.location.hash).then(res=>{
                if(res && res.Query){
                    delete res.Query._t
                    form.setFieldsValue(res.Query)
                }else{
                    form.setFieldsValue(defaultQuery)
                }
            })
        }
    }, [])
    useImperativeHandle(parentRef, () => {
        // return返回的值就可以被父组件获取到
        return {
            formDown
        }
    })
    return (
        <>
            <Form className='PublicList_FormQuery' form={form} {...layout}>
                <Row>
                    {
                        searchFormList.map((item, index) => {
                            return index <= colNum - 2 && <FormItem item={item} index={index} key={index} handleSearch={handleSearch} />
                        })
                    }
                    {
                        searchFormList.map((item, index) => {
                            return index > colNum - 2 && formDown && <FormItem item={item} index={index} key={index} handleSearch={handleSearch} />
                        })
                    }
                    <Col className='FormQuerySubmit'>
                        <Col className='Reset'>
                            <Button onClick={onReset} style={{marginRight: 10}}>重置</Button>
                            <Button type='primary' onClick={handleSearch}>查询</Button>
                        </Col>
                        {
                            isPutItAway ?
                                <Col span={8} xl={{ span: 6 }} className='operationButtons'>
                                    <div style={{ lineHeight: '30px' }}>
                                        {
                                            formDown ?
                                                <span onClick={() => {
                                                    setFormDown(false)
                                                    initPage && initPage()
                                                }}>
                                                    <span>收起</span><UpOutlined />
                                                </span>
                                                : <span onClick={() => {
                                                    setFormDown(true)
                                                    initPage && initPage()
                                                }}>
                                                    <span>展开</span><DownOutlined />
                                                </span>
                                        }
                                    </div>
                                </Col> : null
                        }
                    </Col>
                </Row>
            </Form>
        </>
    )
})

PublicTableQuery.propTypes = {
    onSearch:PropTypes.func.isRequired,
    searchList:PropTypes.array.isRequired,
    initPage:PropTypes.func,
    defaultQuery:PropTypes.object,
    
}
export default memo(PublicTableQuery)