import React, { useMemo, useEffect, useRef, useState, createContext } from 'react'
import _ from 'lodash'
import './CreateForm.less'
import { Form, Input, InputNumber, Select, Button, DatePicker, Radio, Space, Row, Col, Modal, message, Cascader } from 'antd';
import moment from 'moment'
import history from '@/utils/history'
import MaintenanceTipsModal from './maintenanceLadder/MaintenanceTipsModal'
import LadderPart from './maintenanceLadder/LadderPart';
import { get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import styles from '../antd.module.less'
const { Option } = Select;
const { RangePicker } = DatePicker;
const END_MILEAGE_OPTIONS = [{
	label: '计算到总里程和',
	value: 1,
}, {
	label: '计算到里程策略',
	value: 2,
}]

const RELATIVE_MILEAGE_OPTIONS = [{
	label: '车辆销售时里程',
	value: 1,
}, {
	label: '车辆交付时里程',
	value: 2,
}, {
	label: '车辆开票时里程',
	value: 3,
}]


const CreateStrategyCom = (props) => {
	const { formObj, strategyConfig, type, goodsCategory } = props
	const [limitYearRadioDisplay, setLimitYearRadioDisplay] = useState('') // 内外一致-权益限制年限
	const [frequencyRadioDisplay, setFrequencyRadioDisplay] = useState('') // 内外一致-享有权益频次
	const [trafficRadioDisplay, setTrafficRadioDisplay] = useState('') // 内外一致-车联网流量
	const [timeRadioChangeDisplay, setTimeRadioChangeDisplay] = useState(1) // 内外一致-权益生效时间
	const [mileageRadioChangeDisplay, setMileageRadioChangeDisplay] = useState('0')
	const [amountDeductionDisplay, setAmountDeductionDisplay] = useState('0') // 对外-权益金额抵扣策略
	const [maintenanceLadderDisplay, setMaintenanceLadderDisplay] = useState('0') // 内外一致-全保养阶梯策略
	const [limitLegendDisplay, setLimitLegendDisplay] = useState(0) // 对外-权益限制里程策略
	const [paintFrameDisplay, setPaintFrameDisplay] = useState('0') // 内外一致-补漆面策略

	const [paintFrameOpt, setPaintFrameOpt] = useState([]) //内外一致-补漆面选项
	const [selected, setSelected] = useState([])  //内外一致-补漆面选项已选
	const [allDealerList, setAllDealerList] = useState([])
	const [selectedSum, setSelectedSum] = useState(0) // 内外一致-补漆面共有面数

	const [effectMileageFrameFlag, setEffectMileageFrameFlag] = useState(0)  //内外一致-权益生效里程

	const [relativeStrategyTips, setRelativeStrategyTips] = useState(0)  //控制相对里程结束里程的提示文字显示 0:不显示，1:总里程和，2:里程策略
	const [fixedStrategyTips, setFixedStrategyTips] = useState(0)  //控制固定里程结束里程的提示文字显示  0:不显示，1:总里程和，2:里程策略

	const [entryList, setEntryList] = useState({})

	const [maintenanceModalVisible, setMaintenanceModalVisible] = useState(false)
	const [maintenanceSteps, setMaintenanceSteps] = useState(1)
	const [maintenanceLadderDisabled, setMaintenanceLadderDisabled] = useState(false)  //全保养阶梯策略
	const [ladderLimitFrameDisabled, setLadderLimitFrameDisabled] = useState({   //阶梯限制策略选项
		childrenDisabled1: false,
		childrenDisabled2: false,
		childrenDisabled3: false
	})



	//formConfig取出key值
	const { limitTimeFrame, limitYear, limitMonth, limitDay } = strategyConfig['limitTimeFrame'].c
	const { timeRadio, relativeTime, fixedRelativeTime } = strategyConfig['timeRadio'].c
	const { limitLegendFrame, limitLegend, extenderMileage } = strategyConfig['limitLegendFrame'].c
	const { effectMileageFrame, relativeBeginMileageStrategy, relativeEndMileageStrategy, fixedBeginMileage, fixedEndMileageStrategy } = strategyConfig['effectMileageFrame'].c
	const { frequencyRadio, frequency } = strategyConfig['frequencyRadio'].c
	const { identity } = strategyConfig['identity'].c
	const { carIdentity } = strategyConfig['carIdentity'].c
	const { maintenanceLadderFrame, maintenanceLadder, maintenanceLadderLimitFrame, maintenanceLadderDetail } = strategyConfig['maintenanceLadderFrame'].c
	const { paintFrame, paintList } = strategyConfig['paintFrame'].c
	const { trafficRadio, traffic, trafficUnit } = strategyConfig['trafficRadio'].c
	const { deductionFrame, payAmount, deduction } = strategyConfig['deductionFrame'].c
	let goodsType = null
	if (strategyConfig.goodsType) {
		goodsType = strategyConfig['goodsType'].c.goodsType
	}


	// 补漆面选项
	const paintListFormChange = (arr, type) => {
		formObj.setFieldsValue({
			[paintList.value]: arr
		})
	}
	// 权益频次
	const _frequencyNum = Form.useWatch(`frequencyNum-${frequency.value}`, formObj)
	if (_frequencyNum) {
		formObj.setFieldValue(frequency.value, _frequencyNum)
	}
	// 车联网流量
	const _trafficNum = Form.useWatch(`trafficNum-${traffic.value}`, formObj)
	const _trafficUnitNum = Form.useWatch(`trafficUnitNum-${trafficUnit.value}`, formObj)
	if (_trafficNum) {
		formObj.setFieldValue(traffic.value, _trafficNum)
	}
	if (_trafficUnitNum) {
		formObj.setFieldValue(trafficUnit.value, _trafficUnitNum)
	}

	useEffect(() => {
		let { dataSource } = props

		if (dataSource) {

			// if (dataSource[limitYear.value] && dataSource.yearUnit) {
			// 	dataSource.limitYearRadio = 1
			// }
			// 享有权益频次
			dataSource[frequencyRadio.value] = dataSource[frequency.value] > 0 ? 1 : dataSource[frequency.value]
			// dataSource[frequency.value] = dataSource[frequency.value] < 1 ? null : dataSource[frequency.value]    //解决未选其他选项时，其他选项关联的题有值
			dataSource[`frequencyNum-${frequency.value}`] = dataSource[frequency.value] < 1 ? null : dataSource[frequency.value]    //解决未选其他选项时，其他选项关联的题有值

			// 车联网流量
			dataSource[trafficRadio.value] = dataSource[traffic.value] > 0 ? 1 : dataSource[traffic.value]
			//解决未选其他选项时，其他选项关联的题有值
			// dataSource[traffic.value] = dataSource[traffic.value] < 1 ? null : dataSource[traffic.value]
			// dataSource[trafficUnit.value] = dataSource[trafficUnit.value] < 1 ? null : dataSource[trafficUnit.value]
			dataSource[`trafficNum-${traffic.value}`] = dataSource[traffic.value] < 1 ? null : dataSource[traffic.value]
			dataSource[`trafficUnitNum-${trafficUnit.value}`] = dataSource[trafficUnit.value] < 1 ? null : dataSource[trafficUnit.value]

			if (dataSource[relativeTime.value]) {
				dataSource[timeRadio.value] = 0
			}
			if (dataSource.fixedBeginTime && dataSource.fixedEndTime) {
				dataSource[timeRadio.value] = 1

			}
			//行驶里程
			if (dataSource[limitLegend.value]) {
				dataSource.mileageRadio = 0
			}
			if (dataSource.extenderMileage) {
				dataSource.mileageRadio = 1
			}

			// 享有权益频次
			// dataSource.outsideFrequencyRadio = dataSource.outsideFrequency > 0 ? 1 : dataSource.outsideFrequency
			// dataSource.outsideFrequency = dataSource.outsideFrequency < 1 ? null : dataSource.outsideFrequency  //解决未选其他选项时，其他选项关联的题有值
			// // 车联网流量
			// dataSource.outsideTrafficRadio = (dataSource.outsideTraffic > 0 ? 1 : dataSource.outsideTraffic)
			// //解决未选其他选项时，其他选项关联的题有值
			// dataSource.outsideTraffic = dataSource.outsideTraffic < 1 ? null : dataSource.outsideTraffic
			// dataSource.outsideTrafficUnit = dataSource.outsideTrafficUnit < 1 ? null : dataSource.outsideTrafficUnit



			// 内外一致
			// 权益金额抵扣
			amountDeductionChange(dataSource[deductionFrame.value])
			// 权益限制里程
			mileageRadioChange(dataSource[limitLegendFrame.value])
			// 权益限制年限
			limitYearRadioChange(dataSource[limitTimeFrame.value])
			// 享有权益频次
			frequencyRadioChange(dataSource[frequencyRadio.value])
			// 全保养阶梯策略
			maintenanceLadderFrameChange(dataSource[maintenanceLadderFrame.value])
			// 车联网流量
			trafficRadioChange(dataSource[trafficRadio.value])
			// 权益生效时间
			timeRadioChange(dataSource[relativeTime.value] ? 0 : 1)
			//权益生效里程
			effectMileageFrameRadioChange(dataSource[effectMileageFrame.value])
			//根据阶梯数生成阶梯保养
			setMaintenanceSteps(dataSource[maintenanceLadder.value])
			// dataSource[maintenanceLadderFrame.value] == 1 && maintenanceLadderChange(dataSource[maintenanceLadder.value], dataSource[maintenanceLadderDetail.value])
			//阶梯保养策略，初始化阶梯保养策略和阶梯限制策略的选项disabled
			const { fn1, fn2, fn3 } = getRelationdData(dataSource)
			if (fn1() || (fn2() && fn3())) {
				setMaintenanceLadderDisabled(true)
			} else {
				setMaintenanceLadderDisabled(false)
			}
			setLadderLimitFrameDisabled(prev => {
				return {
					...prev,
					childrenDisabled1: fn2(),
					childrenDisabled2: fn3(),
					childrenDisabled3: fn2() || fn3()
				}
			})

			formObj.setFieldsValue({
				// ...dataSource,
				[limitTimeFrame.value]: Number(dataSource[limitTimeFrame.value]),
				[limitYear.value]: Number(dataSource[limitYear.value] || ''),
				[limitMonth.value]: Number(dataSource[limitMonth.value] || ''),
				[limitDay.value]: Number(dataSource[limitDay.value] || ''),
				[timeRadio.value]: dataSource[relativeTime.value] ? 0 : 1,
				[relativeTime.value]: dataSource[relativeTime.value],
				[fixedRelativeTime.value]: !dataSource[relativeTime.value] && dataSource[fixedRelativeTime.fixedBeginTime] && dataSource[fixedRelativeTime.fixedEndTime] ? [moment(dataSource[fixedRelativeTime.fixedBeginTime], 'YYYY-MM-DD HH:mm:ss'), moment(dataSource[fixedRelativeTime.fixedEndTime], 'YYYY-MM-DD HH:mm:ss')] : null,
				[limitLegendFrame.value]: dataSource[limitLegendFrame.value],
				[limitLegend.value]: dataSource[limitLegend.value],
				[extenderMileage.value]: dataSource[extenderMileage.value],
				[effectMileageFrame.value]: dataSource[effectMileageFrame.value],
				[relativeBeginMileageStrategy.value]: dataSource[relativeBeginMileageStrategy.value],
				[relativeEndMileageStrategy.value]: dataSource[relativeEndMileageStrategy.value],
				[fixedBeginMileage.value]: dataSource[fixedBeginMileage.value],
				[fixedEndMileageStrategy.value]: dataSource[fixedEndMileageStrategy.value],
				[frequencyRadio.value]: dataSource[frequencyRadio.value],
				[frequency.value]: dataSource[frequency.value],
				[`frequencyNum-${frequency.value}`]: dataSource[`frequencyNum-${frequency.value}`], // 权益频次数值
				[identity.value]: dataSource[identity.value],
				[carIdentity.value]: dataSource[carIdentity.value],
				[maintenanceLadderFrame.value]: dataSource[maintenanceLadderFrame.value],
				[maintenanceLadder.value]: dataSource[maintenanceLadder.value],
				[paintFrame.value]: dataSource[paintFrame.value],
				[trafficRadio.value]: dataSource[trafficRadio.value],
				[traffic.value]: dataSource[traffic.value],
				[trafficUnit.value]: dataSource[trafficUnit.value],
				[`trafficNum-${traffic.value}`]: dataSource[`trafficNum-${traffic.value}`],
				[`trafficUnitNum-${trafficUnit.value}`]: dataSource[`trafficUnitNum-${trafficUnit.value}`],
				[deductionFrame.value]: dataSource[deductionFrame.value],
				[payAmount.value]: dataSource[payAmount.value],
				[deduction.value]: dataSource[deduction.value],
				[maintenanceLadderDetail.value]: dataSource[maintenanceLadderDetail.value],
				[maintenanceLadderLimitFrame.value]: dataSource[maintenanceLadderLimitFrame.value],
			})
			if (goodsType) {
				formObj.setFieldsValue({
					[goodsType.value]: dataSource[goodsType.value],
				})

			}


			// 编辑权益明细
			if (dataSource.id || type === 'upgrade') {
				// 内外一致-补漆面部位

				setPaintFrameDisplay(dataSource[paintFrame.value])
				if (dataSource[paintList.value]) {
					setPaintFrameOpt(_.cloneDeep(dataSource[paintList.value]))
					let selected = []
					let sum = 0
					dataSource[paintList.value].map(item => {
						if (item.num > 0) {
							selected.push(item.name)
							sum += item.num
						}
					})
					setSelected(_.cloneDeep(selected))
					setSelectedSum(sum)
				} else {
					entryList['equity_paint'] && setPaintFrameOpt(_.cloneDeep(entryList['equity_paint']))
				}
				paintListFormChange(_.cloneDeep(dataSource[paintList.value]), paintList.value)
			}
		}
	}, [props.dataSource])

	// 权益限制年限
	const limitYearRadioChange = (value) => {

		const _timeRadio = timeRadio.value
		setLimitYearRadioDisplay(value)
		if (value === 0) {
			formObj.setFieldValue(_timeRadio, 1)
			timeRadioChange(1)
		} else {
			formObj.setFieldValue(_timeRadio, 0)
			timeRadioChange(0)
		}
		// if(value == '1' && form.getFieldValue('yearUnit') == ''){
		//     props.limitYearRadioChange()
		// }
	}
	// 享受权益频次
	const frequencyRadioChange = (value) => {
		setFrequencyRadioDisplay(value)
	}

	// 车联网流量
	const trafficRadioChange = value => {
		setTrafficRadioDisplay(value)
		if (value == '1' && formObj.getFieldValue(trafficUnit.value) == '') {
			formObj.setFieldsValue({ [trafficUnit.value]: 'G' })
		}
	}

	// 权益生效时间
	const timeRadioChange = value => {

		setTimeRadioChangeDisplay(value)
		// formRef.current.resetFields(['relativeTime','fixedRelativeTime'])
	}

	// 权益金额抵扣策略
	const amountDeductionChange = value => {
		setAmountDeductionDisplay(value)
	}
	// 全保养阶梯策略
	const maintenanceLadderFrameChange = value => {
		setMaintenanceLadderDisplay(value)
	}

	// 补漆面策略
	const paintFrameChange = value => {
		setPaintFrameDisplay(value)
	}

	// 权益限制里程策略
	const mileageRadioChange = value => {
		setLimitLegendDisplay(value)
		//v13版本需求：若权益限制里程选择无此策略，权益生效里程固定选择无此策略
		if (!value) {
			formObj.setFieldValue(effectMileageFrame.value, 0)
			setEffectMileageFrameFlag(0)
			//重置权益生效里面的一些选项
			formObj.setFieldValue(relativeBeginMileageStrategy.value, null)
			formObj.setFieldValue(relativeEndMileageStrategy.value, null)
			formObj.setFieldValue(fixedBeginMileage.value, null)
			formObj.setFieldValue(fixedEndMileageStrategy.value, null)
			setRelativeStrategyTips(0);
			setFixedStrategyTips(0);
		}
	}
	// 补漆面选项
	const paintListChangeHandler = (value, index) => {
		let arr = _.cloneDeep(paintFrameOpt)

		arr[index].num = value
		setPaintFrameOpt(_.cloneDeep(arr))
		paintListFormChange(_.cloneDeep(arr), paintList.value)

		let temp = _.cloneDeep(selected)
		let num = temp.indexOf(arr[index].name)

		if (value > 0 && num == -1) {
			temp.push(arr[index].name)
			setSelected(_.cloneDeep(temp))
		}
		if (value == 0 & num > -1) {
			temp.splice(num, 1)
			setSelected(temp)
		}
		// 计算总面数
		let sum = 0
		if (arr) {
			arr.map(item => {
				sum += item.num
			})
			setSelectedSum(sum)
		}
	}

	//权益生效里程change
	const effectMileageFrameRadioChange = (val) => {
		setEffectMileageFrameFlag(val)
		if (val === 1) {
			tipsShow(val, formObj.getFieldValue(fixedEndMileageStrategy.value) ?? props?.dataSource?.[fixedEndMileageStrategy.value])
			formObj.setFieldValue(relativeBeginMileageStrategy.value, null)
			formObj.setFieldValue(relativeEndMileageStrategy.value, null)
		} else if (val === 2) {
			tipsShow(val, formObj.getFieldValue(relativeEndMileageStrategy.value) ?? props?.dataSource?.[relativeEndMileageStrategy.value])
			formObj.setFieldValue(fixedBeginMileage.value, null)
			formObj.setFieldValue(fixedEndMileageStrategy.value, null)
		}
	}


	//权益生效里程：结束里程
	const endMileageStrategyChange = (val) => {
		tipsShow(formObj.getFieldValue(effectMileageFrame.value), val)
	}


	/**
	 * 
	 * @param {*} radioVal 权益生效里程单选的值 1=固定， 2=相对
	 * @param {*} selectVal 结束里程的值 1=总里程和， 2=里程策略
	 */
	const tipsShow = (radioVal, selectVal) => {
		if (radioVal === 2) {
			setRelativeStrategyTips(selectVal)
		} else {
			setRelativeStrategyTips(0)
		}
		if (radioVal === 1) {
			setFixedStrategyTips(selectVal)
		} else {
			setFixedStrategyTips(0)
		}
	}

	//全保养阶梯交互------------------------------------start
	//全保养阶梯弹窗
	const allLadderShow = () => {
		setMaintenanceModalVisible(!maintenanceModalVisible)
	}

	const watchField = (field) => {
		return Form.useWatch([field.value], formObj)
	}

	//重置全阶梯保养策略
	const resetMaintenanceLadder = () => {
		formObj.setFieldValue(maintenanceLadderFrame.value, 0)
		formObj.setFieldValue(maintenanceLadder.value, 1)
		formObj.setFieldValue(maintenanceLadderDetail.value, [{}])
		maintenanceLadderFrameChange(0)
		setMaintenanceSteps(1)
	}

	const getRelationdData = (dataObj) => {
		//1.享受权益频次frequencyRadio选择不限次数（-1）和其他（1）时，全保养阶梯策略只能选择无此策略,重置全阶梯保养策略
		//2.权益限制年限limitTimeFrame配置为无此策略 0 or 不限年数 -1 && 权益限制里程limitLegendFrame配置为无此策略 0，全保养阶梯策略只能选择无此策略,重置全阶梯保养策略
		//权益频次
		const fn1 = () => {
			if (dataObj[frequencyRadio.value] === -1 || dataObj[frequencyRadio.value] === 1) {
				return true
			}
			return false
		}

		/**
		 * 权益限制年限
		 * 权益限制年限limitTimeFrame配置为无此策略 0 or 不限年数 -1 or 限制年限日期数>0, 阶梯保养策略，不能选年限相关的选项
		 * @returns 
		 */
		const fn2 = () => {
			if (dataObj[limitTimeFrame.value] === 0 ||
				dataObj[limitTimeFrame.value] === -1 ||
				(dataObj[limitTimeFrame.value] === 1 && dataObj[limitDay.value]) > 0) {
				return true
			}
			return false
		}

		/**
		 * 权益限制里程
		 * 权益限制里程limitLegendFrame配置为无此策略 0  阶梯保养策略，不能选里程相关的选项
		 * @returns 
		 */
		const fn3 = () => {
			if (dataObj[limitLegendFrame.value] === 0) {
				return true
			}
			return false
		}

		return { fn1, fn2, fn3 }
	}

	//设置阶梯保养策略相关属性
	const handleMaintenanceLadder = () => {
		const { fn1, fn2, fn3 } = getRelationdData(formObj.getFieldsValue())
		setLadderLimitFrameDisabled(prev => {
			return {
				...prev,
				childrenDisabled1: fn2(),
				childrenDisabled2: fn3(),
				childrenDisabled3: fn2() || fn3()
			}
		})

		if (!fn2() && fn3()) {
			//阶梯保养只能选限制年限，默认选中限制年限
			formObj.setFieldValue(maintenanceLadderLimitFrame.value, 1)
		}
		if (fn2() && !fn3()) {
			//阶梯保养只能选限制里程，默认选中限制里程
			formObj.setFieldValue(maintenanceLadderLimitFrame.value, 2)
		}
		if (!fn2() && !fn3()) {
			formObj.setFieldValue(maintenanceLadderLimitFrame.value, 3)
		}
		if (fn1() || (fn2() && fn3())) {
			resetMaintenanceLadder()
			setMaintenanceLadderDisabled(true)
		} else {
			setMaintenanceLadderDisabled(false)
		}
	}

	useEffect(() => {
		!props.dataSource && handleMaintenanceLadder()
	}, [])

	//组装数据供阶梯保养使用

	const _relativeTime = watchField(relativeTime)
	const _relativeBeginMileageStrategy = watchField(relativeBeginMileageStrategy)

	const formPartObj = {
		data: {
			// [limitTimeFrame.value]: watchField(limitTimeFrame),
			// [limitYear.value]: watchField(limitYear),
			// [limitMonth.value]: watchField(limitMonth),
			// [relativeTime.value]: _relativeTime,
			// [limitLegendFrame.value]: watchField(limitLegendFrame),
			// [limitLegend.value]: watchField(limitLegend),
			// [extenderMileage.value]: watchField(extenderMileage),
			[effectMileageFrame.value]: watchField(effectMileageFrame),
			// [relativeBeginMileageStrategy.value]: _relativeBeginMileageStrategy,
			[fixedBeginMileage.value]: watchField(fixedBeginMileage),
			[maintenanceLadderLimitFrame.value]: watchField(maintenanceLadderLimitFrame),
			[maintenanceLadderDetail.value]: watchField(maintenanceLadderDetail),
			rTimeName: _relativeTime && entryList['equity_relativeTime']?.filter(item => item.entryValue == _relativeTime)[0]?.entryMeaning,
			rBeginMileagegName: _relativeBeginMileageStrategy && RELATIVE_MILEAGE_OPTIONS.filter(item => item.value == _relativeBeginMileageStrategy)[0]?.label,
			[relativeEndMileageStrategy.value]: watchField(relativeEndMileageStrategy),
			[fixedEndMileageStrategy.value]: watchField(fixedEndMileageStrategy)
		},
	}

	//生成阶梯保养list表单
	const maintenanceLadderChange = (value, ladderDetail) => {
		let mLadderDetailList = ladderDetail ?? [{}];
		// let _valueFormatter = Math.trunc(value < 0 ? 0 : value > 20 ? 20 : value);
		// let _value;
		// if (_.isNumber(Number(value))) {
		// 	_value = Math.trunc(value < 0 ? 0 : value > 20 ? 20 : value)
		// } else {
		// 	_value = 1
		// }
		const _value = Math.trunc(value)
		setMaintenanceSteps(prev => {
			if (prev < _value) {
				for (let i = prev; i < _value; i++) {
					mLadderDetailList.push({})
				}
			}
			if (prev > _value) {
				mLadderDetailList = mLadderDetailList.slice(0, _value)
			}
			return _value
		})
		formObj.setFieldsValue({ [maintenanceLadderDetail.value]: [...mLadderDetailList] })
	}
	//全保养阶梯交互---------------------------------------end

	useEffect(() => {
		get(allUrl.DetailedManageInterests.getAllDealerList).then(res => {
			if (res.success) {
				setAllDealerList(res.resp)
			} else {
				// message.error(res.msg)
			}
		})
	}, [])

	// useEffect(() => {
	// 	get(allUrl.DetailedManageInterests.getAllDealerList).then(res => {
	// 		if (res.success) {
	// 			setAllDealerList(res.resp)
	// 		} else {
	// 			// message.error(res.msg)
	// 		}
	// 	})
	// }, [])
	const getEntryLists = (setPaint) => {
		// 'equity_car_attr,equity_belong,equity_identity,equity_car_identity,equity_detail_refund,equity_detail_performance,equity_business_code, equity_relativeTime, equity_performing, equity_paint, equity_paint_frame'
		get(allUrl.common.entryLists, { codes: 'equity_identity,equity_car_identity, equity_relativeTime, equity_paint,equity_relative_begin_mileage_strategy,equity_goods_type' }).then(res => {
			if (res.success) {
				let Dt = res.resp[0]
				for (let i in Dt) {
					Dt[i].forEach(item => {
						item.name = item.entryMeaning
						item.value = item.entryValue
					})
					if (i == 'equity_paint') {
						let arr = []
						Dt[i].forEach(item => {
							arr.push({
								id: item.id,
								name: item.entryMeaning,
								numerical: item.entryValue,
								num: 0
							})
						})
						Dt['equity_paint'] = arr

						// 新建权益明细
						if ((props.dataSource == undefined || props.dataSource.id == undefined) && type === undefined) {
							setPaintFrameOpt(_.cloneDeep(arr))
						} else if (setPaint) {
							setPaintFrameOpt(_.cloneDeep(arr))
						}
					}
				}

				setEntryList(Dt || {})
			} else {
				//   message.error(res.message)
			}
		})
	}
	const paintFocus = () => {
		// 如果获取焦点时无值则需要手动获取
		if (paintFrameOpt.length === 0) {
			getEntryLists(true)
		}
	}
	useEffect(() => {
		// console.log('props.dataSource', props);
		// 'equity_car_attr,equity_belong,equity_identity,equity_car_identity,equity_detail_refund,equity_detail_performance,equity_business_code, equity_relativeTime, equity_performing, equity_paint, equity_paint_frame'
		// 当type位upgrade时是升级用，如果设置过了会有默认值，这里不需要设置
		getEntryLists()
	}, [])
	return (
		<>
			{/* 权益限制年限 */}
			<Form.Item label={strategyConfig['limitTimeFrame'].l} style={{ marginBottom: 0 }} className={strategyConfig['limitTimeFrame'].hide ? styles.globalHide : ''}>
				<Form.Item
					name={limitTimeFrame.value}
					style={{ display: 'inline-block', width: '300px' }}
					initialValue={0}
				>
					<Radio.Group
						onChange={(e) => {
							limitYearRadioChange(e.target.value);
							handleMaintenanceLadder()
						}}
						disabled={limitTimeFrame.disabled}>
						<Radio value={0} disabled={limitTimeFrame.childrenDisabled1}>无此策略</Radio>
						<Radio value={-1} disabled={limitTimeFrame.childrenDisabled2}>不限年数</Radio>
						<Radio value={1} disabled={limitTimeFrame.childrenDisabled3}>其他</Radio>
					</Radio.Group>
				</Form.Item>
				<>
					<Form.Item
						name={limitYear.value}
						style={{ display: 'inline-block', width: '90px', marginLeft: '-40px' }}
						initialValue={0}
					>
						<InputNumber min={0} style={{ width: 90 }} disabled={limitYearRadioDisplay !== 1} />
					</Form.Item>
					<span style={{ marginLeft: 10, marginRight: 10 }}>年</span>
					<Form.Item
						name={limitMonth.value}
						style={{ display: 'inline-block', width: '90px' }}
						initialValue={0}
					>
						<InputNumber min={0} style={{ width: 90, lineHeight: '30px' }} disabled={limitYearRadioDisplay !== 1} />
					</Form.Item>
					<span style={{ marginLeft: 10, marginRight: 10, lineHeight: '30px' }}>月</span>
					<Form.Item
						name={limitDay.value}
						style={{ display: 'inline-block', width: '90px' }}
						initialValue={0}
					>
						<InputNumber min={0} style={{ width: 90 }} disabled={limitYearRadioDisplay !== 1} onChange={handleMaintenanceLadder} />
					</Form.Item>
					<span style={{ marginLeft: 10, lineHeight: '30px' }}>日</span>
					<span style={{ marginLeft: 10, color: 'red', fontSize: 12, position: 'absolute', whiteSpace: 'nowrap', top: '8px' }}>权益限制年限为具体自然日时，无法配置阶梯限制年限策略，请知悉</span>
				</>
			</Form.Item>
			<Form.Item label={strategyConfig['timeRadio'].l} rules={[{ required: false }]} style={{ marginBottom: 0 }} className='prefix-red'>
				<Row>
					{/* 权益生效时间 */}
					<Col span={3}>
						{/* V13.0.4 权益限制年限选择“无此策略”，权益生效时间只可选择“固定时间”，权益限制年限选择“不限年限”或者“其他”，权益生效时间只可选择“相对时间” */}
						<Form.Item
							name={timeRadio.value}
							style={{ display: 'inline-block', width: '200px', marginTop: '6px' }}
							initialValue={1}
						>
							<Radio.Group onChange={e => timeRadioChange(e.target.value)} disabled={timeRadio.disabled}>
								<Space direction="vertical" align='center' size={20} >
									<Radio value={0} disabled={limitYearRadioDisplay == 0}>相对时间</Radio>
									<Radio value={1} style={{ marginTop: '6px' }} disabled={limitYearRadioDisplay != 0}>固定时间</Radio>
								</Space>
							</Radio.Group>
						</Form.Item>
					</Col>
					<Col>
						<Space direction="vertical" align='center' size={16}>
							<div style={{ position: 'relative' }}>
								<Form.Item
									name={relativeTime.value}
									style={{ display: 'inline-block', width: '400px', margin: '0 8px' }}
									rules={[{ required: timeRadioChangeDisplay === 0, message: '请选择权益生效时间！' }]}
								>
									<Select style={{ display: 'inline-block', width: '380px' }} allowClear placeholder='开票时间' disabled={timeRadioChangeDisplay !== 0 || relativeTime.disabled}>
										{
											entryList['equity_relativeTime'] && entryList['equity_relativeTime'].length ?
												entryList['equity_relativeTime'].filter(i => i.extendField1 === '').map((item, index) => <Option key={index} value={Number(item.value)}>{item.name}</Option>)
												: null
										}
									</Select>
								</Form.Item>
								<span style={{ color: 'red', fontSize: 12, position: 'absolute', whiteSpace: 'nowrap', top: '8px' }}>若选择相对时间，权益会基于车辆实际情况生效</span>
							</div>
							<div style={{ position: 'relative' }}>
								<Form.Item
									name={fixedRelativeTime.value}
									style={{ display: 'inline-block', width: '400px' }}
									rules={[{ required: timeRadioChangeDisplay === 1, message: '请选择权益生效时间！' }]}
								>
									<RangePicker
										showTime
										style={{ width: '380px' }}
										placeholder={['权益开始时间', '权益结束时间']}
										disabled={timeRadioChangeDisplay !== 1 || fixedRelativeTime.disabled}
									/>
								</Form.Item>
								<span style={{ color: 'red', fontSize: 12, marginLeft: '10px', position: 'absolute', whiteSpace: 'nowrap', top: '8px' }}>若选择固定时间，权益只在固定时间范围内生效</span>
							</div>
						</Space>
					</Col>
				</Row>
			</Form.Item>
			{/* 权益限制里程 */}
			<Form.Item label={strategyConfig['limitLegendFrame'].l} rules={[{ required: false }]} style={{ marginBottom: 0 }} className={strategyConfig['limitLegendFrame'].hide ? styles.globalHide : ''}>
				<Row>
					<Col span={3}>
						<Form.Item
							name={limitLegendFrame.value}
							style={{ display: 'inline-block', width: '200px' }}
							initialValue={0}
						>
							<Radio.Group
								onChange={e => {
									mileageRadioChange(e.target.value);
									handleMaintenanceLadder()
								}}
								disabled={limitLegendFrame.disabled}>
								<Space direction="vertical" align='start' size={24} >
									{limitLegendFrame.childrenDisabled1 ? '' : <Radio value={0} style={{ lineHeight: '32px' }}> 无此策略 </Radio>}
									{limitLegendFrame.childrenDisabled2 ? '' : <Radio value={1}>行驶里程</Radio>}
									{limitLegendFrame.childrenDisabled3 ? '' : <Radio value={2}>增程器里程</Radio>}
								</Space>
							</Radio.Group>
						</Form.Item>
					</Col>
					<Col span={15} style={{ width: 700 }}>
						<Space direction="vertical" size={15}>
							{limitLegendFrame.childrenDisabled1 ? '' : <div style={{ height: 32 }}>
								{limitLegendFrame.childrenTips1 ? <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap', lineHeight: '32px' }}>{limitLegendFrame.childrenTips1}</span> : ''}
							</div>}
							{limitLegendFrame.childrenDisabled2 ? '' : <Space>
								<Form.Item
									name={limitLegend.value}
									noStyle
								>
									<InputNumber min={1} placeholder='只能输入数字，例如：160000' style={{ display: 'inline-block', width: '250px' }} disabled={limitLegendDisplay !== 1 || limitLegend.disabled} />
								</Form.Item>
								<span style={{ marginLeft: 6, whiteSpace: 'nowrap' }}>公里</span>
								<span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap' }}>{limitLegendFrame.childrenTips2 ? limitLegendFrame.childrenTips2 : '行驶里程是汽车行驶的总里程数，填写后将作为权益生效里程计算的基准'}</span>
							</Space>}
							{limitLegendFrame.childrenDisabled3 ? '' : <Space>
								<Form.Item
									name={extenderMileage.value}
									noStyle
								>
									<InputNumber min={1} placeholder='只能输入数字，例如：160000' style={{ width: '250px' }} disabled={limitLegendDisplay !== 2 || extenderMileage.disabled} />
								</Form.Item>
								<span style={{ marginLeft: 6, whiteSpace: 'nowrap' }}>公里</span>
								<span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap' }}>{limitLegendFrame.childrenTips3 ? limitLegendFrame.childrenTips3 : '增程器里程是汽车仅使用增程器运行时行驶的里程数，填写后将作为权益生效里程计算的基准'}</span>
							</Space>}
						</Space>
					</Col>
				</Row>
			</Form.Item>
			{/* 权益生效里程 */}
			<Form.Item label={strategyConfig['effectMileageFrame'].l} rules={[{ required: false }]} className={strategyConfig['effectMileageFrame'].hide ? `${styles.globalHide} effectMileageFrameWrap` : 'effectMileageFrameWrap'}>
				<Row>
					<Col span={3}>
						<Form.Item
							className='effectMileageFrameCls'
							name={effectMileageFrame.value}
							initialValue={0}
						>
							<Radio.Group onChange={e => effectMileageFrameRadioChange(e.target.value)} disabled={effectMileageFrame.disabled}>
								<Space direction="vertical" align='start' size={37} >
									<Radio value={0}>无此策略</Radio>
									<Radio value={2} disabled={limitLegendDisplay === 0}>相对里程</Radio>
									<Radio value={1} disabled={limitLegendDisplay === 0}>固定里程</Radio>
								</Space>
							</Radio.Group>
						</Form.Item>
					</Col>
					<Col span={15} style={{ width: 700 }}>
						<Space direction="vertical" align="start" size={relativeStrategyTips > 0 ? 16 : 28}>
							<div style={{ height: 32 }}>&nbsp;</div>
							<Space>
								<Form.Item
									name={relativeBeginMileageStrategy.value}
									label="开始里程"
									className='mb0 w250 mr15'
									rules={[{ required: effectMileageFrameFlag === 2, message: '请选择' }]}
								>
									<Select allowClear placeholder='请选择' disabled={effectMileageFrameFlag !== 2 || relativeBeginMileageStrategy.disabled}>

										{
											entryList['equity_relative_begin_mileage_strategy'] && entryList['equity_relative_begin_mileage_strategy'].length ?
												entryList['equity_relative_begin_mileage_strategy'].filter(i => i.extendField1 === '').map(item => <Option key={item.value} value={Number(item.value)}>{item.name}</Option>)
												: ''
										}
										{/* {
											RELATIVE_MILEAGE_OPTIONS.map(item => <Option key={item.value} value={item.value}>{item.label}</Option>)
										} */}
									</Select>
								</Form.Item>
								<Form.Item
									name={relativeEndMileageStrategy.value}
									label="结束里程"
									className='mb0 w250'
									rules={[{ required: effectMileageFrameFlag === 2, message: '请选择' }]}
								>
									<Select allowClear placeholder='请选择' disabled={effectMileageFrameFlag !== 2 || relativeEndMileageStrategy.disabled} onChange={value => endMileageStrategyChange(value)}>
										{
											END_MILEAGE_OPTIONS.map(item => <Option key={item.value} value={item.value}>{item.label}</Option>)
										}
									</Select>
								</Form.Item>
								{
									relativeStrategyTips === 2 ? <span className='tips' style={{ width: 520 }}>若开始里程选择相对里程，则账户关联的权益生效里程将根据账户实际里程数作为权益开始里程，结束里程选择计算到里程策略，则权益生效里程范围为：相对里程～权益限制里程（行驶里程/增程器里程），例：相对开始里程为5000，限制里程为10000，则权益有效里程为5000～10000</span> : null
								}
								{
									relativeStrategyTips === 1 ? <span className='tips' style={{ width: 540 }}>若开始里程选择相对里程，则账户关联的权益生效里程将根据账户实际里程数作为权益开始里程，结束里程选择计算到总里程和，则权益生效里程范围为：相对里程～相对里程+权益限制里程（行驶里程/增程器里程），例：相对开始里程为5000，限制里程为10000，则权益有效里程为5000～15000</span> : null
								}
							</Space>
							<Space>
								<Form.Item
									name={fixedBeginMileage.value}
									label="开始里程"
									className='mr15 w250'
									rules={[{ required: effectMileageFrameFlag === 1, message: '请输入' }]}
								>
									<InputNumber style={{ width: 170 }} min={0} placeholder="固定开始里程" disabled={effectMileageFrameFlag !== 1 || fixedBeginMileage.disabled} />
								</Form.Item>
								<Form.Item
									name={fixedEndMileageStrategy.value}
									label="结束里程"
									className='w250'
									rules={[{ required: effectMileageFrameFlag === 1, message: '请选择' }]}
								>
									<Select allowClear placeholder='请选择' disabled={effectMileageFrameFlag !== 1 || fixedEndMileageStrategy.disabled} onChange={value => endMileageStrategyChange(value)}>
										{
											END_MILEAGE_OPTIONS.map(item => <Option key={item.value} value={item.value}>{item.label}</Option>)
										}
									</Select>
								</Form.Item>
								{
									fixedStrategyTips === 2 ? <span className='tips' style={{ width: 520, marginTop: -24 }}>若开始里程选择固定里程，则所有账户关联的权益生效里程均以填写的该里程作为权益开始里程，结束里程选择计算到里程策略，则权益生效里程范围为：固定里程～权益限制里程（行驶里程/增程器里程），例：固定开始里程为5000，限制里程为10000，则权益有效里程为5000～10000</span> : null
								}
								{
									fixedStrategyTips === 1 ? <span className='tips' style={{ width: 540, marginTop: -24 }}>若开始里程选择固定里程，则所有账户关联的权益生效里程均以填写的固定里程作为权益开始里程，结束里程选择计算到总里程和，则权益生效里程范围为：固定里程～固定里程+权益限制里程（行驶里程/增程器里程），例：固定开始里程为5000，限制里程为10000，则权益有效里程为5000～15000；手动导入的数据若填写了开始里程，则开始里程以填写的为准，结束里程为明细策略配置的结束里程。</span> : null
								}
							</Space >
						</Space >
					</Col >
				</Row >
			</Form.Item >
			{/* 享有权益频次 */}
			<Form.Item label={strategyConfig['frequencyRadio'].l} style={{ marginBottom: 0 }} className={strategyConfig['frequencyRadio'].hide ? styles.globalHide : ''}>
				<Form.Item
					name={frequencyRadio.value}
					style={{ display: 'inline-block', width: '300px' }}
					initialValue={0}
				>
					<Radio.Group
						onChange={e => {
							frequencyRadioChange(e.target.value);
							handleMaintenanceLadder()
						}}
						disabled={frequencyRadio.disabled}>
						<Radio value={0} disabled={frequencyRadio.childrenDisabled1}>无此策略</Radio>
						<Radio value={-1} disabled={frequencyRadio.childrenDisabled2}>不限次数</Radio>
						<Radio value={1} disabled={frequencyRadio.childrenDisabled3}>其他</Radio>
					</Radio.Group>
				</Form.Item>

				<Form.Item style={{ display: 'inline-block', width: '550px' }}>
					<Space>
						<Form.Item name={frequency.value} style={{ display: 'none' }}></Form.Item>
						<Form.Item
							name={`frequencyNum-${frequency.value}`}
							noStyle
							rules={[{ required: frequencyRadioDisplay === 1, message: '请输入次数!' }]}
						>
							<InputNumber min={1} placeholder='只能输入数字，例如：16' style={{ width: 180, marginLeft: -40 }} disabled={frequencyRadioDisplay !== 1} />
						</Form.Item>
						<span style={{ marginLeft: 6 }}>次数</span>
						<span style={{ color: 'red', fontSize: 12 }}>免费首保，代步车，取送车等需要限制使用次数的策略在此处配置</span>
					</Space>
				</Form.Item>
			</Form.Item>
			{/* 车辆权益变更属性 */}
			<Form.Item label={strategyConfig['identity'].l} style={{ marginBottom: 0 }}>
				{/* <Space> */}
				<Form.Item name={identity.value} rules={[]} initialValue={1} style={{ display: 'inline-block' }}>

					<Radio.Group>
						{
							entryList['equity_identity'] && entryList['equity_identity'].length ?
								entryList['equity_identity'].map((item, index) => <Radio key={index} value={Number(item.value)}>{item.name}</Radio>)
								: null
						}
					</Radio.Group>


				</Form.Item>
				<span className='tips' style={{ lineHeight: '32px', color: 'red', fontSize: 12 }}>该策略适用于二手车权益变更</span>
				{/* </Space> */}
			</Form.Item>



			{/* 享有车主身份-实物类权益隐藏 */}
			<Form.Item name={carIdentity.value} label={strategyConfig['carIdentity'].l} rules={[]} initialValue={2} className={goodsCategory === 1 ? styles.globalHide : ''}>
				<Radio.Group>
					{
						entryList['equity_car_identity'] && entryList['equity_car_identity'].length ?
							entryList['equity_car_identity'].map((item, index) => <Radio key={index} value={Number(item.value)}>{item.name}</Radio>)
							: null
					}
				</Radio.Group>
			</Form.Item>
			{/* 权益所属-只有实物类 */}
			{goodsType && props.goodsCategory && props.goodsCategory === 1 ? <>
				<Form.Item label={strategyConfig['goodsType'].l} style={{ marginBottom: 0 }}>
					<Form.Item name={goodsType.value} rules={[]} initialValue={1} style={{ display: 'inline-block' }}>
						<Radio.Group>
							{
								entryList['equity_goods_type'] && entryList['equity_goods_type'].length ?
									entryList['equity_goods_type'].map((item, index) => <Radio key={index} value={Number(item.value)}>{item.name}</Radio>)
									: null
							}
						</Radio.Group>
					</Form.Item>
					<span className='tips' style={{ lineHeight: '32px', color: 'red', fontSize: 12 }}>该策略适用于精品权益，随车履约即交付履约，不随车履约即交付后存在邮寄和安装动作，需交付后才可履约</span>
				</Form.Item>

			</> : null}

			{/* 全保养阶梯策略 - 实物类权益隐藏*/}
			<Form.Item
				label={strategyConfig['maintenanceLadderFrame'].l}
				style={{ marginBottom: 0 }}
				className={(strategyConfig['maintenanceLadderFrame'].hide || goodsCategory === 1) ? styles.globalHide : ''}>
				<Form.Item
					name={maintenanceLadderFrame.value}
					style={{ display: 'inline-block', width: '210px' }}
					initialValue={0}
				>
					{/* <Radio.Group onChange={e => {
						maintenanceLadderFrameChange(e.target.value)
						handleMaintenanceLadder()
					}} disabled={maintenanceLadderDisabled}>
						<Radio value={0}>无此策略</Radio>
						<Radio value={1}>非周期保养</Radio>
					</Radio.Group> */}
					{/* 
					* 阶梯保养暂时不上，修改点：
					* 1.disabled属性为true，后期上线，放开上面的注释代码
					* 2.阶梯保养查看弹窗页面
					*   2.1  权益明细详情 DetailManagement/detailComponents/BaseStrategy文件 “全保养阶梯策略”字段去掉详情
					*   2.2  权益包列表 PackManagement/PublicTable  和 PackManagement/DetailPubliTable “全保养阶梯策略”字段去掉详情
					*   2.3  权益账户列表 AccountManagement/basePayTable/PublicTable “全保养阶梯策略”字段去掉详情
					 */ }
					{/* ********临时不上阶梯保养功能，该选项默认无此策略，且disabled，后期上线，放开上面注释的代码 */}
					<Radio.Group onChange={e => {
						maintenanceLadderFrameChange(e.target.value)
						handleMaintenanceLadder()
					}} disabled={true}>
						<Radio value={0}>无此策略</Radio>
						<Radio value={1}>非周期保养</Radio>
					</Radio.Group>
				</Form.Item>
				<Form.Item label="阶梯数：" style={{ display: 'inline-block', width: '615px' }}>
					<Space style={{ marginTop: -15 }}>
						{/* <span style={{ marginRight: 6 }}>阶梯数：</span> */}
						<Form.Item
							name={maintenanceLadder.value}
							noStyle
							rules={[{ required: maintenanceLadderDisplay === 1, message: '请输入全保养阶梯数值' }]}
							initialValue={1}
						>
							<InputNumber
								min={1}
								max={20}
								formatter={(value) => Math.trunc(value)}
								placeholder='支持整数，不能小于1'
								style={{ width: 100 }}
								disabled={maintenanceLadderDisplay !== 1}
								onChange={e => maintenanceLadderChange(e, formObj.getFieldValue(maintenanceLadderDetail.value))}
							/>
						</Form.Item>
						<span style={{ marginLeft: 6 }}>次</span>
						<span style={{ color: 'red', fontSize: 12 }}>该阶梯保养配置策略适用于指定核销某一次保养的场景，目前系统自动生成，在权益明细里体现。全保养明细配置阶梯数后，核销时会按阶梯规则进行核销</span>
						<Button type='link' style={{ color: '#FFA500' }} onClick={allLadderShow}>策略说明</Button>
					</Space>
				</Form.Item>
			</Form.Item >
			{/* 阶梯保养-实物类权益隐藏  */}
			{
				maintenanceLadderDisplay === 1 &&
				<Form.Item
					label="阶梯限制策略"
					name={maintenanceLadderLimitFrame.value}
					style={{ position: 'relative' }}
					className={goodsCategory === 1 ? styles.globalHide : ''}
					extra={<span style={{ color: 'red', fontSize: 12, position: 'absolute', top: 7, left: 240 }}>阶梯限制年限、里程的总和必须等于权益限制年限、里程</span>}
				>
					<Radio.Group>
						{/* <Radio value={1} disabled={fn2()}>年限</Radio>
						<Radio value={2} disabled={fn3()}>里程</Radio>
						<Radio value={3} disabled={fn2() || fn3()}>年限和里程</Radio> */}
						<Radio value={1} disabled={ladderLimitFrameDisabled.childrenDisabled1}>年限</Radio>
						<Radio value={2} disabled={ladderLimitFrameDisabled.childrenDisabled2}>里程</Radio>
						<Radio value={3} disabled={ladderLimitFrameDisabled.childrenDisabled3}>年限和里程</Radio>
					</Radio.Group>
				</Form.Item>
			}

			{
				maintenanceLadderDisplay === 1 &&
				<Form.List name={maintenanceLadderDetail.value} initialValue={[{}]} className={goodsCategory === 1 ? styles.globalHide : ''}>
					{(fields) => {
						if (!maintenanceSteps) return
						return [...Array(maintenanceSteps)].map((e, i) => {
							return <LadderPart
								{...formPartObj}
								key={i}
								index={i}
								form={formObj}
								strategyConfig={strategyConfig} />
						})
					}
					}

				</Form.List>
			}

			{/* 补漆面部位-实物类权益隐藏 */}
			<Form.Item
				label={strategyConfig['paintFrame'].l}
				style={{ marginBottom: 0 }}
				className={(strategyConfig['paintFrame'].hide || goodsCategory === 1) ? styles.globalHide : ''}>
				<Form.Item
					name={paintFrame.value}
					style={{ display: 'inline-block', width: '300px' }}
					initialValue={0}
				>
					<Radio.Group onChange={e => paintFrameChange(e.target.value)} disabled={paintFrame.disabled}>
						<Radio value={0} disabled={paintFrame.childrenDisabled1}>无此策略</Radio>
						<Radio value={-1} disabled={paintFrame.childrenDisabled2}>不限面数</Radio>
						<Radio value={1} disabled={paintFrame.childrenDisabled3}>其他</Radio>
					</Radio.Group>
				</Form.Item>

				<Form.Item style={{ display: 'inline-block', width: '500px', marginLeft: '-40px' }}>
					<Select
						mode="multiple"
						style={{ width: '300px' }}
						placeholder="请选择，支持多选"
						value={selected}
						onFocus={paintFocus}
						// onChange={handleChange}
						disabled={paintFrameDisplay !== 1}
						optionLabelProp="label"
						dropdownRender={() => <div className='select-dropdown'>
							{paintFrameOpt.map((item, index) => {
								return <div className="select-item checked" key={index}>
									<span className='item-text'>{item.name}</span>
									<InputNumber className='input-number' min={0} value={item.num} onChange={e => paintListChangeHandler(e, index)} />
								</div>
							})}
						</div>}
					>
					</Select>
					<span style={{ marginLeft: '10px' }}><span style={{ marginRight: '10px' }}>共</span><InputNumber value={selectedSum} disabled={true} /><span style={{ marginLeft: '10px' }}>面</span></span>
				</Form.Item>
				<Form.Item name={paintList.value} style={{ display: 'none' }}></Form.Item>
			</Form.Item>
			{/* 车联网流量-实物类权益隐藏 */}
			<Form.Item label={strategyConfig['trafficRadio'].l} style={{ marginBottom: 0 }} className={(strategyConfig['trafficRadio'].hide || goodsCategory === 1) ? styles.globalHide : ''}>
				<Form.Item
					name={trafficRadio.value}
					style={{ display: 'inline-block', width: '300px' }}
					initialValue={0}
				>
					<Radio.Group onChange={e => trafficRadioChange(e.target.value)} disabled={trafficRadio.disabled}>
						<Radio value={0} disabled={trafficRadio.childrenDisabled1}>无此策略</Radio>
						<Radio value={-1} disabled={trafficRadio.childrenDisabled2}>不限流量</Radio>
						<Radio value={1} disabled={trafficRadio.childrenDisabled3}>其他</Radio>
					</Radio.Group>
				</Form.Item>

				<>
					<Form.Item name={traffic.value} style={{ display: 'none' }}></Form.Item>
					<Form.Item name={trafficUnit.value} style={{ display: 'none' }}></Form.Item>
					<Form.Item
						name={`trafficNum-${traffic.value}`}
						style={{ display: 'inline-block', width: '210px', marginLeft: -40 }}
						rules={[{ required: trafficRadioDisplay === 1, message: '请输入流量!' }]}
					>
						<InputNumber min={1} placeholder='只能输入数字，例如：16' style={{ width: 210 }} disabled={trafficRadioDisplay !== 1} />
					</Form.Item>
					<Form.Item
						name={`trafficUnitNum-${trafficUnit.value}`}
						style={{ display: 'inline-block', width: '100px', margin: '0 8px' }}
						initialValue={'G'}
					>
						<Select style={{ display: 'inline-block', width: '60px' }} allowClear placeholder='G' disabled={trafficRadioDisplay !== 1}>
							<Option value={"G"}>G</Option>
							<Option value={"M"}>M</Option>
							<Option value={"KB"}>KB</Option>
						</Select>
					</Form.Item>
				</>
			</Form.Item>
			{/* 权益金额抵扣-实物类权益隐藏 */}
			<Form.Item label={strategyConfig['deductionFrame'].l} className={(strategyConfig['deductionFrame'].hide || goodsCategory === 1) ? styles.globalHide : ''}>
				<Row>
					<Col span={3}>
						<Form.Item
							name={deductionFrame.value}
							style={{ display: 'inline-block', width: '200px', marginTop: '6px', marginBottom: 0 }}
							initialValue={0}
						>
							<Radio.Group onChange={e => amountDeductionChange(e.target.value)} disabled={deductionFrame.disabled}>
								<Space direction="vertical" align='center' size={20} >
									<Radio value={0}>无此策略</Radio>
									<Radio value={1}>具体金额</Radio>
								</Space>
							</Radio.Group>
						</Form.Item>
					</Col>
					<Col>
						<Space direction="vertical" align='center' size={16}>
							<Space>&nbsp;</Space>
							<Space size={16}>
								<Form.Item style={{ display: 'inline-block', width: '310px' }}
									help={<div style={{ color: 'red', fontSize: '12px', marginTop: '5px', marginBottom: '10px' }}>实际支付金额为用户付款金额</div>}>
									<Space>
										<Form.Item
											name={payAmount.value}
											noStyle
										>
											<InputNumber min={1} placeholder='实际支付金额只能输入数字，例如：160000' style={{ width: 300 }} disabled={amountDeductionDisplay !== 1} />
										</Form.Item>
										<span style={{ marginLeft: 6 }}>元</span>
										<span style={{ marginLeft: 6, marginRight: 6 }}>-</span>
									</Space>
								</Form.Item>

								<Form.Item style={{ display: 'inline-block', width: '300px', marginLeft: 30 }}
									help={<div style={{ color: 'red', fontSize: '12px', marginTop: '5px', marginBottom: '10px' }}>支付抵扣金额为用户实际享有金额</div>}>
									<Space>
										<Form.Item
											name={deduction.value}
											noStyle
										>
											<InputNumber min={1} placeholder='支付抵扣金额只能输入数字，例如：160000' style={{ width: 300 }} disabled={amountDeductionDisplay !== 1} />
										</Form.Item>
										<span style={{ marginLeft: 6 }}>元</span>
									</Space>
								</Form.Item>
							</Space>
						</Space>
					</Col>
				</Row>

			</Form.Item>
			{
				maintenanceModalVisible && <MaintenanceTipsModal maintenanceModalVisible={maintenanceModalVisible} allLadderShow={allLadderShow} />
			}

		</>
	)
}
export default CreateStrategyCom
