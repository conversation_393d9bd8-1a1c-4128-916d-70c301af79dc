import React, { useState, useEffect, useRef } from "react";
import allUrl from "@/utils/url";
import BraftEditor from "@/components/Public/BraftEditor";
import Editor from '@/components/Public/WangEditor';
import baseURL from "@/baseURL";
import UploadFile from "@/components/Public/UploadFile";
import DragableUpload from "@/components/Public/DragableUpload";
import { Form, Button, Input, Modal, Spin, Row, Col, Radio, message, Tooltip } from "antd";
import { CloseOutlined, PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import MessageApplication from "./MessageApplication";
import SendingRange from "./SendingRange";
import AddLink from "./AddLink";
import PublicTooltip from "@/components/Public/PublicTooltip";
import { post, get } from "../../../../utils/request";
import BatchImportModal from "./BatchImportModal";
import _ from "lodash";
import "./SendMessage.less";

const { TextArea } = Input;
const SendMessage = (props) => {
  const { Type, ID, fromTab, setID, setType, getSaveSuccess, editorFlag, setEditorFlag } = props;
  const interValRef = useRef();
  const braftEditorRef = useRef(null); // BrafrEditor element
  const [form] = Form.useForm();
  const sendingRangeRef = useRef();
  const [sendApplicationVisible, setSendApplicationVisible] = useState(false);
  const [sendingRangeVisible, setSendingRangeVisible] = useState(false);
  const [fmPic, setFMPic] = useState([]);
  const [fmPicLoading, setFMPicLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [braftEditorHtmlCon, setBraftEditorHtmlCon] = useState("");
  const [defaultHtmlCon, setDefaultHtmlCon] = useState("");
  const [braftEditorHtmlText, setBraftEditorHtmlText] = useState("");
  const [agentObj, setAgentObj] = useState({});
  const [formData, setFormData] = useState({});
  const [targetList, setTargetList] = useState([]);
  // const [defaultTargetList,setDefaultTargetList] = useState([])
  const [removeFileList, setRemoveFileList] = useState([]);
  const [targetAllPositions, setTargetAllPositions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [addLinkVisible, setAddLinkVisible] = useState(false);
  const [addLinkText, setAddLinkText] = useState("");
  const [isBatchUsed, setIsBatchUsed] = useState(false); // 是否使用了批量导入，批量导入和选择发送范围互斥
  const [batchImportNum, setBatchImportNum] = useState(0);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [errorImportData, setErrorImportData] = useState([]);
  const [successImportData, setSuccessImportData] = useState([]);
  const [targetChannel, setTargetChannel] = useState(0); // 选择发送目标的渠道，0 页面筛选，1 用户导入
  const [importLoading, setImportLoading] = useState(false)
  const Save = (type) => {
    form.validateFields().then((values) => {
      console.log(values, fmPic, targetList);
      // if(JSON.stringify(agentObj) === '{}'){
      //     if(type!==3) message.error('请选择发送应用！')
      //         return
      // }
      if (!targetList.length) {
        if (type !== 3) message.error("请选择发送范围！");
        return;
      }
      if (!values.title) {
        if (type !== 3) message.error("请输入标题！");
        return;
      }
      if (!braftEditorHtmlText) {
        if (type !== 3) message.error("请输入正文内容！");
        return;
      }
      if (fmPic.length == 0 || (fmPic.length && fmPic[0].url == undefined)) {
        if (type !== 3) message.error("请添加封面图！");
        return;
      }

      let removeList_Temp = [];
      removeFileList.forEach((item) => {
        removeList_Temp.push(item.id);
      });

      let delTargetList_Temp = [];
      let targetListID = [],
        defaultTargetListID = [];
      targetList.forEach((item) => {
        if (item.id) {
          targetListID.push(item.id);
        }
      });
      // defaultTargetList.forEach(item=>{
      //     if(item.id){
      //         defaultTargetListID.push(item.id)
      //     }
      // })
      // defaultTargetListID.forEach(item=>{
      //     if(targetListID.indexOf(item)<=-1){
      //         delTargetList_Temp.push(item)
      //     }
      // })

      console.log("封面", fmPic);
      let data = {
        id: Type === "ADD" ? null : ID,
        // agentId:agentObj.agentId,
        title: values.title || "",
        // thumbMediaId:fmPic[fmPic.length-1].mediaId,
        picUrl: fmPic[fmPic.length - 1].url,
        author: values.author || "",
        content: braftEditorHtmlCon,
        digest: values.digest ? values.digest : braftEditorHtmlText.substr(0, 45),
        isReceipt: values.isReceipt,
        safe: values.safe,
        // contentSourceUrl:addLinkText || '',
        attachList: fileList,
        targetList,
        targetChannel,
        // delArrachList:removeList_Temp,
        // delTargetList:delTargetList_Temp
      };
      console.log(type);
      setLoading(true);
      if (type === 2) {
        Modal.confirm({
          title: "提示",
          content: "是否确认发送消息！",
          onOk: () => {
            post(allUrl.MessageManage.saveMessage, { ...data }).then((res) => {
              if (res.success) {
                let id = res.resp[0];
                setType("EDIT");
                setID(id);
                post(allUrl.MessageManage.sendMessage + "/" + id).then((res) => {
                  if (res.success) {
                    message.success(res.msg);
                    setTargetAllPositions([])
                    setTargetList([]);
                    setIsBatchUsed(false)
                    setErrorImportData([])
                    setSuccessImportData([])
                    getSaveSuccess();
                    setBatchImportNum(0)
                  }
                  setLoading(false);
                });
              } else {
                setLoading(false);
              }
            });
          },
          onCancel: () => {
            setLoading(false);
            return false;
          },
        });
      } else {
        post(allUrl.MessageManage.saveMessage, { ...data }).then((res) => {
          if (res.success) {
            if (type === 3) {
              let Dt = res.resp[0];
              setType("EDIT");
              setID(Dt.id);
              message.success("已自动保存为草稿！");
            } else {
              if (type === 1) {
                message.success("草稿保存成功！");
              }
            }
          } else {
          }
          setLoading(false);
        });
      }
    });
  };

  const getBraftEditorContent = ({ htmlCon, textCon }) => {
    setBraftEditorHtmlCon(htmlCon);
    setBraftEditorHtmlText(textCon);
  };
  const imgDel = () => {
    setFMPic([]);
  };
  const MessageApplicationCB = (data) => {
    setAgentObj({
      agentIdName: data.entryMeaning,
      agentId: data.entryValue,
      url: data.extendField1,
    });
  };
  const SendingRangeCB = (data, qwAgentMessageId) => {
    console.log("data", data, qwAgentMessageId);
    let users = [];

    data.forEach((i) => {
        users.push({
          type: 3, // type3为用户
          userId: i.userId,
          name: i.userName,
          orgName: i.orgName,
          orgId: i.orgId,
          positionName: i.positionName,
          position: i.position,
          qwUserId: i.qywxUserId,
          userOneId: i.userOneId,
          
        });
    });
    filterPositionInfo(data);
    setTargetList(users);
  };
  const batchImportCB = (data) => {
    console.log("批量导入回调", data);
    setBatchImportNum(data.length)
    let users = [];

    data.forEach((i) => {
        users.push({
          type: 3, // type3为用户
          userId: i.userInfo.userId,
          name: i.userInfo.userName,
          orgName: i.userInfo.orgName,
          orgId: i.userInfo.orgId,
          positionName: i.userInfo.positionName,
          position: i.userInfo.position,
          qwUserId: i.userInfo.qywxUserId,
          userOneId: i.userInfo.userOneId,
          
        });
    });
    setTargetList(users);
    data.length > 0 ? setIsBatchUsed(true) :  setIsBatchUsed(false)
    data.length > 0 ? setTargetChannel(1) : setTargetChannel(0)
  };
  const clearBatchImport = () => {
    setBatchImportNum(0)
    setIsBatchUsed(false)
    setTargetChannel(0) 
    setTargetAllPositions([])
    setTargetList([]);
    setErrorImportData([])
    setSuccessImportData([])

  }
  // 获取岗位人数
  const filterPositionInfo = (data) => {
    console.log('data1111', data)
    let postions = []; // 岗位列表
    let postionNames = [];
    data.forEach((i) => {
      // 添加展示用的岗位消息
      if (postions.length == 0 || postionNames.indexOf(i.positionName) == -1) {
        postions.push({
          name: i.positionName,
          num: 1,
        });
        postionNames.push(i.positionName);
      } else {
        postions.map((j) => {
          if (j.name == i.positionName) {
            j.num++;
          }
        });
      }
    });
    setTargetAllPositions(postions);
  };

  const FJPicUploadChange = (info) => {
    const { fileList } = info;
    if(info.file.status) {
      setFileList(fileList);
    }
    if (info.file.status === "done") {
      if (info.file.response && info.file.response.success) {
        message.success(info.file.response.msg);
        fileList.forEach((item) => {
          if (item.response) {
            let Dt = item.response.resp;
            item.fileName = Dt[0];
            item.fileId = Dt[1];
          }
        });
        fileList.forEach((item) => {
          let obj = {};
          if (Type !== "ADD") {
            obj.id = item.id;
          }

          obj.fileName = item.fileName;
          obj.fileId = item.fileId;
        });
        setFileList(fileList);
      } else {
        message.error(info.file.response.msg);
      }
    } else if (info.file.status === "error") {
      message.error(`${info.file.name}上传失败！`);
    }
    // setFileList(fileList);
  };

  const UploadRemove = (file) => {
    let temp = [...removeFileList];
    temp.push(file);
    setRemoveFileList(temp);
  };
  const FMPicUploadChange = (info) => {
    const { fileList } = info;
    setFMPic([fileList[fileList.length - 1]]);
    if (info.file.status === "uploading") {
      setFMPicLoading(true);
    } else if (info.file.status === "done") {
      setFMPicLoading(false);
      if (info.file.response && info.file.response.success) {
        message.success(info.file.response.msg);
        fileList.forEach((item) => {
          if (item.response && item.response.success) {
            item.url = item.response.resp[0];
          }
        });
        console.log("fff", fileList[fileList.length - 1]);
        setFMPic([fileList[fileList.length - 1]]);
      } else {
        message.error(info.file.response.msg);
      }
    } else if (info.file.status === "error") {
      message.error(`${info.file.name}上传失败！`);
      setFMPicLoading(false);
      setFMPic([]);
    } else {
      setFMPicLoading(false);
    }
  };
  // 处理页面是否可以操作
  const setPageAvailablity = (type) => {
    console.log(33, this);
    // 如果是编辑，页面可以操作
    if (type === "EDIT") {
      // code:页面可以操作
      // this.state.display='block'
    } else {
      // code:禁止操作
      // this.state.display='none'
    }
  };
  const initForm = (data) => {
    form.setFieldsValue({
      title: data.title || null,
      digest: data.digest || null,
      // contentSourceUrl:data.contentSourceUrl || null,
      author: data.author || null,
      isReceipt: String(data.isReceipt) || null,
      safe: String(data.safe) || null,
    });
    setAddLinkText(data.contentSourceUrl || "");
  };
  const getAgentObj = (data) => {
    get(allUrl.common.entryLists, { codes: "qw-agent" }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0]["qw-agent"];
        if (data.agentId) {
          let target = Dt.filter((item) => item.entryValue === data.agentId)[0];
          console.log("getAgentObj", data, target);
          setAgentObj({
            agentIdName: target.entryMeaning,
            agentId: target.entryValue,
            url: target.extendField1,
          });
        }
      } else {
        // message.error(res.message)
      }
    });
  };

  const resetData = () => {
    form.resetFields();
    setDefaultHtmlCon("");
    setBraftEditorHtmlCon("");
    setFileList([]);
    setTargetList([]);
    setFMPic([]);
    setFormData({});
    setAgentObj({});
    setBatchImportNum(0)
    setSuccessImportData([])
    setErrorImportData([])
    setIsBatchUsed(false)
    setTargetAllPositions([])
    setImportLoading(false)
  };

  const targetListDel = (row) => {
    let newTargetList = _.cloneDeep({ obj: targetList }).obj;
    newTargetList = newTargetList.filter((item) => item.userId !== row.userId);
    filterPositionInfo(newTargetList);
    setTargetList(newTargetList);
  };

  const agentObjDel = () => {
    setAgentObj({});
  };
  const getAddLink = (value) => {
    console.log(value);
    setAddLinkText(value);
  };
   //   批量导入用户
   const BatchUserUpload = ({ file, fileList }, type) => {
    console.log(file, type, "file的数据");
    const { response } = file;
    if (file.status === "uploading") {
      setImportLoading(true);
    }
    if (file.status === "done") {
      if (response.success) {
        let key = 2;
        message.loading({ content: "正在导入中，请稍后", key });
        setTimeout(() => {
          message.success({ content: "导入完成!", key, duration: 2 });
        }, 1000);
        console.log("导入返回", response);
        setSuccessImportData(response.resp[0].data);
        // mock
        // const mockData = Array.from({ length: 100 }, (_, index) => {
        //   return {
        //     userOneId: `user_${index + 1}`,
        //   };
        // });
        // setSuccessImportData(mockData)
        setErrorImportData(response.resp[0].errorData);
        setImportModalVisible(true);
      } else {
        message.error(response.msg);
      }
      setImportLoading(false);
    }
    if (file.status === "error") {
      message.error(response.msg || response.msgCode);
      setImportLoading(false);
    }
  };
  const downloadImportUserTemplate = () => {
    get(
      allUrl.MessageManage.userExportTemplate,
      {},
      {
        responseType: "blob",
      }
    ).then((res) => {
      if (res) {
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        console.log("blob", blob);
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容ie
          window.navigator.msSaveBlob(blob, "企微公告接收人导入模板.xlsx");
        } else {
          let downloadElement = document.createElement("a");
          let href = window.URL.createObjectURL(blob); //创建下载的链接
          downloadElement.href = href;
          downloadElement.download = "企微公告接收人导入模板" + ".xlsx"; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放掉blob对象
        }
        message.success("模板下载成功！");
      } else {
        // message.error(res.msg)
      }
    });
  }
  const importSuccessColumns = [
    {
      title: '序号',
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      dataIndex: "username",
    },
    {
      title: "工号",
      dataIndex: "userOneId",
      key: "userOneId",
    },
  ];
  const importErrorColumns = [
    {
      title: '序号',
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      dataIndex: "username",
    },
    {
      title: "工号",
      dataIndex: "userOneId",
      key: "userOneId",
    },
    {
      title: "失败原因",
      dataIndex: "failReason",
    },
  ];
  useEffect(() => {
    if(document.getElementById('viewContent')){
      document.getElementById('viewContent').innerHTML = ''
    }
    // 点击编辑或者查看按钮，均走此逻辑
    if ((Type === "EDIT" || Type === "CHECK") && fromTab !== 0) {
      setEditorFlag(false)
      setLoading(true);
      setPageAvailablity(Type);
      const url =
        fromTab == "2"
          ? allUrl.MessageManage.getMessageDetail + "/" + ID
          : allUrl.MessageManage.getMessageDetail2 + "/" + ID;
      get(url).then((res) => {
        setEditorFlag(true)
        if (res.success) {
          let Dt = res.resp[0];
          Dt.qwAgentMessageId = Dt.targetList[0].qwAgentMessageId;
          initForm(Dt);
          setTargetChannel(Dt.targetChannel || 0)
          if(Type == 'CHECK'){
            document.getElementById('viewContent').innerHTML = Dt.content
          } else {
            setDefaultHtmlCon(Dt.content);
            setBraftEditorHtmlCon(Dt.content);
          }
          Dt.attachList.forEach((item, index) => {
            item.uid = String(item.fileId);
            item.name = `${index + 1}: ${item.fileName}`;
            // item.url = item.fileUrl;
            item.status = "done";
          });
          setFileList(Dt.attachList);
          let picArr = [
            {
              uid: 1,
              status: "done",
              // mediaId:Dt.thumbMediaId,
              url: Dt.picUrl,
            },
          ];
          Dt.targetChannel == 0 ? filterPositionInfo(Dt.targetList) : setTargetAllPositions([])
          setTargetList(Dt.targetList);
          // setDefaultTargetList(Dt.targetList)
          setFMPic(picArr);
          // setAgentId(Dt.agentId)
          setFormData(Dt);

          getAgentObj(Dt);
        } else {
          // message.error(res.msg)
        }
        setLoading(false);
      });
    } else {
      setEditorFlag(true)
      resetData();
    }
  }, [ID]);

  useEffect(() => {
    const id = setInterval(() => {
      Save(3);
    }, 180000);
    interValRef.current = id;
    return () => {
      clearInterval(interValRef.current);
    };
  }, []);

  // Hooks，处理 BraftEditor 逻辑
  useEffect(() => {
    console.log("componentDidMount");
    console.log("---Type", Type); // 判断上面 Type 的值
    const node = braftEditorRef.current; // 获取 BraftEditor 节点
    if(Type == 'ADD') {
      setBatchImportNum(0)

    }
  }, []);
  return (
    <div style={{ padding: "32px", background: "white" }} className="SendMessage">
      <Spin spinning={loading}>
        {Type !== "CHECK" ? (
          <Row style={{ marginBottom: "32px", lineHeight: "34px" }} gutter={10}>
            {/* <Col><Button onClick={() => {
                        setSendApplicationVisible(true)
                    }}>选择发送应用</Button></Col> */}
            <Col>
              <Button
                type="primary"
                onClick={() => {
                  setSendingRangeVisible(true);
                }}
                disabled={isBatchUsed}
              >
                选择发送范围
              </Button>
            </Col>
            <Col>
              <UploadFile
                style={{ display: "inline-block" }}
                extension={["xls", "xlsx"]}
                showUploadList={false}
                size={10}
                action={baseURL.Host + allUrl.MessageManage.userImportFile}
                UploadChange={BatchUserUpload}
              >
                <Button disabled={targetAllPositions.length} type="primary" loading={importLoading}>
                批量导入接收人
                </Button>
              </UploadFile> 
            </Col>
            <Col>
              <Button
                type="link"
                onClick={downloadImportUserTemplate}
              >
                批量导入人员模版下载
              </Button>
            </Col>
          </Row>
        ) : null}
        {JSON.stringify(agentObj) !== "{}" ? (
          <Row>
            <span style={{ marginTop: "6px" }}>已选择的应用：</span>
            <Row
              style={{
                lineHeight: "34px",
                marginBottom: "20px",
                width: "150px",
                position: "relative",
              }}
            >
              <img src={agentObj.url} style={{ width: "34px", height: "34px" }} />
              <span style={{ fontSize: "14px", color: "rgba(0,0,0,0.65)", marginLeft: "10px" }}>
                {agentObj.agentIdName}
              </span>
              {Type !== "CHECK" ? (
                <span
                  style={{ position: "absolute", right: 0, color: "#b7b7b7", cursor: "pointer" }}
                  onClick={() => agentObjDel()}
                >
                  <CloseOutlined />
                </span>
              ) : null}
            </Row>
            {Type !== "CHECK" ? (
              <span style={{ display: Type !== "CHECK" ? "block" : "none" }}>
                <a
                  style={{ marginLeft: "20px", display: "inline-block", marginTop: "6px" }}
                  onClick={() => {
                    setSendApplicationVisible(true);
                  }}
                >
                  修改
                </a>
                <a
                  style={{ marginLeft: "20px", display: "inline-block", marginTop: "6px" }}
                  onClick={() => {
                    setTargetAllPositions([])
                    setTargetList([]);
                    setIsBatchUsed(false)
                    setErrorImportData([])
                    setSuccessImportData([])
                  }}
                >
                  清空
                </a>
              </span>
            ) : null}
          </Row>
        ) : null}
        {targetAllPositions.length && targetChannel == 0 ? (
          <Row>
            <Col
              span={24}
              style={{
                display: "flex",
                maxHeight: 300,
                overflowY: "auto",
                overflowX: "hidden",
                marginBottom: 16,
              }}
            >
              <span style={{ marginTop: "6px" }}>已选择的范围：</span>
              <Row style={{ lineHeight: "34px", marginBottom: "20px", flex: 1 }} gutter={[16, 16]}>
                {targetAllPositions.map((item, index) => {
                  return (
                    <Col key={index + 1} span={3}>
                      <div
                        style={{
                          background: "#F4FAFF",
                          padding: "2px 22px 2px 10px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {item.name + "(" + item.num + ")"}
                      </div>
                    </Col>
                  );
                })}
                {Type !== "CHECK" ? (
                  <span>
                    <a
                      style={{marginLeft: "10px" }}
                      onClick={() => {
                        setSendingRangeVisible(true);
                      }}
                    >
                      修改
                    </a>
                    <a
                  style={{ marginLeft: "20px", display: "inline-block", marginTop: "6px" }}
                  onClick={() => {
                    setTargetAllPositions([])
                    setTargetList([]);
                    setIsBatchUsed(false)
                    setErrorImportData([])
                    setSuccessImportData([])
                  }}
                >
                  清空
                </a>
                  </span>
                ) : null}
              </Row>
            </Col>
          </Row>
        ) : null}
        <Form form={form}>
         {
          // 编辑或者查看，取targetList; 新增取batchImportNum
          ID && Type == 'CHECK' && targetChannel == 1 ? <Col span={8}>
          <Form.Item label="导入名单" name="title">
            <span style={{marginRight: 15}}>{targetList.length} 人</span>
          </Form.Item>
          `</Col> : 
          ID && Type == 'EDIT' && targetChannel == 1 ? <Col span={8}>
          <Form.Item label="导入名单" name="title">
            <span style={{marginRight: 15}}>{batchImportNum} 人</span><CloseOutlined onClick={clearBatchImport} />
          </Form.Item>
        </Col> : 
          batchImportNum > 0 ?
          <Col span={8}>
          <Form.Item label="导入名单" name="title">
            <span style={{marginRight: 15}}>{batchImportNum} 人</span><CloseOutlined onClick={clearBatchImport} />
          </Form.Item>
        </Col> : null
         }
          <Row>
            <Col span={8}>
              <Form.Item label="标题" name="title">
                <Input allowClear={Type !== "CHECK"} disabled={Type === "CHECK"} />
              </Form.Item>
            </Col>
          </Row>
          {Type !== "CHECK" && editorFlag ? (
            <div className={braftEditorRef} ref={braftEditorRef}>
              {/* <BraftEditor
                size={100}
                url={baseURL.Host + allUrl.MessageManage.uploadCoverImg}
                cb={getBraftEditorContent}
                defaultHtmlCon={defaultHtmlCon}
                disable={Type !== "EDIT"}
              /> */}
               <Editor 
                cb={getBraftEditorContent}
                url={baseURL.Host + allUrl.MessageManage.uploadCoverImg}
                defaultHtmlCon={defaultHtmlCon} />
            </div>
          ) : (
            <div id="viewContent" className="braft-editor-raw-content">
              {braftEditorHtmlCon ? braftEditorHtmlCon.replace(/<[^>]+>/gi, "") : ""}
            </div>
          )}
          {Type !== "CHECK" ? (
            <Form.Item label="添加" wrapperCol={{ span: 6 }} className="labelCol2_Width add">
              <DragableUpload
                showUploadList={true}
                action={baseURL.Host + allUrl.MessageManage.uploadMedia}
                size={50}
                UploadChange={FJPicUploadChange}
                fileList={fileList}
                setFileList={setFileList}
                UploadRemove={UploadRemove}
                iconRender={() => (
                  <div>
                    <img
                      src={require("@/assets/img/originalLink2.png")}
                      style={{ width: "18px" }}
                    />
                  </div>
                )}
              >
                <a>
                  <img src={require("@/assets/img/file.png")} style={{ width: "18%" }} />
                  添加附件
                </a>
              </DragableUpload>
              {/* <div style={{padding:'4px 0'}}>
                            <DisconnectOutlined />
                            <img src={require('@/assets/img/originalLink3.png')} style={{width:'18px'}} />
                            <a style={{marginLeft:'4px'}} onClick={()=>setAddLinkVisible(true)}>添加原文链接</a>
                        </div> */}
              {addLinkText ? (
                <div className="addlink">
                  {/* <PaperClipOutlined style={{color:'rgba(0, 0, 0, 0.45)'}} target='_blank' /> */}
                  <img src={require("@/assets/img/originalLink4.png")} style={{ width: "18px" }} />
                  {/* <a style={{padding:'0 8px'}} href={addLinkText} target='_black' >{addLinkText}</a> */}
                  <DeleteOutlined onClick={() => setAddLinkText("")} />
                </div>
              ) : null}
            </Form.Item>
          ) : null}
          {Type !== "CHECK" ? (
            <Form.Item style={{ marginLeft: "86px" }} wrapperCol={{ span: 10 }}>
              <UploadFile
                showUploadList={false}
                action={baseURL.Host + allUrl.MessageManage.uploadCoverImg}
                size={2}
                extension={["jpg", "jpeg", "png"]}
                UploadChange={FMPicUploadChange}
                fileList={fmPic}
              >
                <Button icon={<PlusOutlined />} loading={fmPicLoading}>
                  添加封面图
                </Button>{" "}
                <span style={{ color: "rgba(0,0,0,0.25)", marginLeft: "10px" }}>
                  建议尺寸：1068*455
                </span>
              </UploadFile>
            </Form.Item>
          ) : (
            <Form.Item label="附件">
              {fileList.length
                ? fileList.map((item) => {
                    return (
                      <div style={{ marginBottom: 6 }}>
                        <span>
                          {item.name || item.fileName}
                        </span>
                      </div>
                    );
                  })
                : null}
            </Form.Item>
          )}
          {fmPic.length && fmPic[0].status === "done" ? (
            <Form.Item style={{ marginLeft: "80px" }}>
              <div style={{ position: "relative", height: "150px", width: "350px" }}>
                {Type === "EDIT" ? (
                  <img
                    src={require("@/assets/img/del.png")}
                    style={{
                      width: "7%",
                      position: "absolute",
                      cursor: "pointer",
                      right: "-12px",
                      top: "-12px",
                    }}
                    onClick={imgDel}
                  />
                ) : null}
                <img src={fmPic[0].url} style={{ width: "100%", height: "100%" }} />
              </div>
            </Form.Item>
          ) : null}
          <Row>
            <Col span={8}>
              <Form.Item
                label="摘要"
                name="digest"
                labelCol={{ span: 4 }}
                className="labelCol2_Width"
              >
                <TextArea
                  autoSize={{ minRows: 5, maxRows: 8 }}
                  allowClear={Type !== "CHECK"}
                  disabled={Type === "CHECK"}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <span
                style={{
                  marginLeft: "10px",
                  color: "rgba(0,0,0,0.25)",
                  marginTop: "50px",
                  display: "block",
                }}
              >
                如不填会自动抓取正文前45字
              </span>
            </Col>
          </Row>
          {/* <Row>
                    <Col span={8}>
                        <Form.Item label='原文链接' name='contentSourceUrl' labelCol={{ span: 4 }}>
                            <Input allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <span style={{ marginLeft: '10px', color: 'rgba(0,0,0,0.25)', marginTop: '5px', display: 'block' }}>选填</span>
                    </Col>
                </Row> */}
          <Row>
            <Col span={8}>
              <Form.Item label="发布者" name="author" labelCol={{ span: 4 }}>
                <Input allowClear={Type !== "CHECK"} disabled={Type === "CHECK"} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <span
                style={{
                  marginLeft: "10px",
                  color: "rgba(0,0,0,0.25)",
                  marginTop: "5px",
                  display: "block",
                }}
              >
                选填
              </span>
            </Col>
          </Row>
          {Type == "CHECK" && fromTab == "3" ? (
            <>
              <div className="receiptWrapper">
                <Form.Item
                  label="是否需要添加回执"
                  name="isReceipt"
                  initialValue={"0"}
                  className="labelCol3_Width"
                >
                  <Radio.Group disabled={Type === "CHECK"}>
                    <Radio value={"1"}>是</Radio>
                    <Radio value={"0"}>否</Radio>
                  </Radio.Group>
                </Form.Item>
                <div className="tooltipWrapper">
                  <Tooltip title="选择“是”，接收人将可点击“发送回执”以确认已收到公告推送；选择“否”，则将发送无回执的公告。">
                    <img
                      className="icon-receipt-reminder"
                      src={require("@/assets/img/add_receipt_reminder.png")}
                      style={{ width: "18px", cursor: "pointer" }}
                    />
                  </Tooltip>
                </div>
              </div>
              <Form.Item
                label="分享设置"
                name="safe"
                initialValue={"2"}
                className="labelCol2_Width"
              >
                <Radio.Group disabled={Type === "CHECK"}>
                  <Radio value={"2"}>仅限在企业内分享</Radio>
                  <Radio value={"1"}>不能分享且内容显示水印</Radio>
                  <Radio value={"0"}>可对外分享</Radio>
                </Radio.Group>
              </Form.Item>
            </>
          ) : null}
          {Type !== "CHECK" ? (
            <Row>
              <Col span={24} style={{ textAlign: "center" }}>
                <Button type="primary" onClick={() => Save(2)}>
                  发送
                </Button>
                <Button style={{ marginLeft: "20px" }} onClick={() => Save(1)}>
                  存草稿
                </Button>
              </Col>
            </Row>
          ) : null}
        </Form>
        <div className="detail_btns">
          <Button onClick={getSaveSuccess}>取消</Button>
        </div>
        {sendApplicationVisible && (
          <MessageApplication
            agentObj={agentObj}
            visible={sendApplicationVisible}
            onCancel={() => setSendApplicationVisible(false)}
            cb={MessageApplicationCB}
          />
        )}
       
          <SendingRange
            ref={sendingRangeRef}
            Type={Type}
            formData={formData}
            targetList={targetList}
            visible={sendingRangeVisible}
            onCancel={() => setSendingRangeVisible(false)}
            cb={SendingRangeCB}
          />
        
        {addLinkVisible && (
          <AddLink
            visible={addLinkVisible}
            defaultValue={addLinkText}
            cb={getAddLink}
            onCancel={() => setAddLinkVisible(false)}
          />
        )}
      </Spin>
      {importModalVisible && (
        <BatchImportModal
          title="企微公告接收人"
          visible={importModalVisible}
          onCancel={() => setImportModalVisible(false)}
          successImportData={successImportData}
          importErrorColumns={importErrorColumns}
          importSuccessColumns={importSuccessColumns}
          errorImportData={errorImportData}
          importDataUrl={allUrl.MessageManage.userImportFile}
          exportErrorDataUrl={allUrl.MessageManage.userExportFromData}
          cb={batchImportCB}
        />
      )}
    </div>
  );
};
export default SendMessage;
