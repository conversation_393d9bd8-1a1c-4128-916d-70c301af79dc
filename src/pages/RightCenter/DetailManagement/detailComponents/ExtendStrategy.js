import React, { useEffect, useState } from 'react'
import { Descriptions, Tag } from 'antd'
import moment from 'moment'
import { utilsDict } from '@/utils/utilsDict'
import { endTimeRender, endTimeRenderPolymerization } from '@/pages/RightCenter/DetailManagement/publicMethods'
import { get } from '@/utils/request';
import allUrl from '@/utils/url';

const ExtendStrategy = (props) => {
    const { detail, styles, originDetail } = props
    const { detailDisplay } = detail
    const [entryList, setEntryList] = useState({})

    useEffect(() => {
        get(allUrl.common.entryLists, { codes: 'equity_relativeTime' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setEntryList(Dt)
            }
        })
    }, [])

    /**
    * 计算权益限制里程
    * @param {*} limitLegendFrame 权益限制里程（基准）下拉选的值
    * @param {*} limitLegend 行驶里程输入框的值
    * @param {*} extenderMileage 增程器里程输入框的值
    * @returns str 权益限制里程展示的值
    */
    const computedMileage = ({ limitLegendFrame, limitLegend, extenderMileage }) => {
        const _mileageFrame = {
            //无此策略
            0: () => '-',
            //行驶里程
            1: () => {
                return <>
                    {limitLegend}公里
                    <Tag className={styles['custom-ant-tag-green']} style={{ marginLeft: 5 }}>行驶</Tag>
                </>
            },
            //增程器里程
            2: () => {
                return <>
                    {extenderMileage}公里
                    <Tag className={styles['custom-ant-tag-yellow']} style={{ marginLeft: 5 }}>增程器</Tag>
                </>
            },
            null: () => '-'
        }
        return _mileageFrame[limitLegendFrame]?.()
    }

    //延保生效时间
    /**
     * 
     * @param {*} detail 权益明细数据
     * @param {*} fn 计算生效结束时间的方法
     * @param {*} isLine 开始时间，结束时间是否折行展示
     * @returns DOM
     */
    const showEffectTime = (detail, fn, isLine = false) => {
        if (detail.relativeTime) {
            //相对时间
            return (
                <div className='time-box'>
                    <span className='begin-time'>
                        <Tag className={styles['custom-ant-tag-blue']}>开始时间</Tag>
                        {detail.relativeTimeName}
                    </span>

                    <br />

                    <span className='end-time'>
                        <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束时间</Tag>
                        {entryList['equity_relativeTime'] && fn({ value: detail.relativeTime, originDetail, currentFormData: detail, entryList })}
                    </span>
                </div>
            )
        }
        //固定时间
        return (
            <div className='time-box'>
                <span className='begin-time'>
                    <Tag className={styles['custom-ant-tag-blue']}>开始时间</Tag>
                    {detail.fixedBeginTime ? moment(detail.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') : ''}
                </span>

                <br />

                <span className='end-time'>
                    <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束时间</Tag>
                    {detail.fixedEndTime ? moment(detail.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss') : ''}
                </span>
            </div>
        )
    }

    // //聚合生效时间
    // const showClientEffectTime = (detail) => {
    //     if (detail.relativeTime) {
    //         //相对时间
    //         return (
    //             <span>
    //                 <Tag className={styles['custom-ant-tag-blue']}>开始时间</Tag>{detail.relativeTimeName}
    //                 <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束时间</Tag>{entryList['equity_relativeTime'] && endTimeRenderPolymerization({ value: detail.relativeTime, originDetail, currentFormData: detail, entryList })}
    //             </span>
    //         )
    //     }
    //     //固定时间
    //     return <>
    //         {detail.fixedBeginTime ? moment(detail.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') : ''} ~ {detail.fixedEndTime ? moment(detail.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss') : ''}
    //     </>
    // }
    return (
        <>
            <Descriptions title="延保生效策略" className='strategy-box'>
                <Descriptions.Item label='延保限制年限'>
                    {detail.limitTimeFrame == 0 && <span>-</span>}
                    {detail.limitTimeFrame == -1 && <span>永久有效</span>}
                    {detail.limitTimeFrame == 1 && <span>{`${Number(detail.limitYear)}年${Number(detail.limitMonth)}月${Number(detail.limitDay)}日`}</span>}
                </Descriptions.Item>
                <Descriptions.Item label='延保生效时间' span={2}>
                    {showEffectTime(detail, endTimeRender)}
                </Descriptions.Item>
                <Descriptions.Item label='延保激活时间' span={3}>{detail.extendWarrantyActiveTimeName}</Descriptions.Item>
                <Descriptions.Item label='延保限制里程(基准)'>
                    {
                        computedMileage({
                            limitLegendFrame: detail.limitLegendFrame,
                            limitLegend: detail.limitLegend,
                            extenderMileage: detail.extenderMileage
                        })
                    }
                </Descriptions.Item>
                {/* <Descriptions.Item label='延保行驶里程'>
                    {detail.limitLegendFrame == 1 ? `${detail.limitLegend}公里` : '-'}
                </Descriptions.Item>
                <Descriptions.Item label='延保增程器里程'>
                    <span>{detail.limitLegendFrame == 2 ? `${detail.extenderMileage}公里` : '-'}</span>
                </Descriptions.Item> */}
                <Descriptions.Item label='延保生效里程' span={2}>
                    {detail.effectMileageFrame === 0 && <span>-</span>}
                    {detail.effectMileageFrame === 1 && <span>
                        <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detail.fixedBeginMileage}公里
                        <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detail.fixedEndMileageStrategy)}</span>}
                    {detail.effectMileageFrame === 2 && <span>
                        <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detail.relativeBeginMileageStrategyName}
                        <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detail.relativeEndMileageStrategy)}</span>}
                </Descriptions.Item>
                <Descriptions.Item label='车辆权益变更属性'>{detail.identityName || '-'}</Descriptions.Item>
                <Descriptions.Item label='享有车主身份' span={2}>{detail.carIdentityName || '-'}</Descriptions.Item>
            </Descriptions>

            <Descriptions title={`客户端展示策略：${utilsDict('displayFrameMap', detailDisplay.displayFrame)}`} className='strategy-box'>
                {
                    detailDisplay.displayFrame === 2 ?
                        <>
                            <Descriptions.Item label='聚合权益明细名称' span={3}>{detailDisplay.name || '-'}</Descriptions.Item>
                            <Descriptions.Item label='聚合权益声明' span={3}>{detailDisplay.statement || '-'}</Descriptions.Item>
                            <Descriptions.Item label='聚合权益限制年限'>
                                {detailDisplay.limitTimeFrame == 0 && <span>-</span>}
                                {detailDisplay.limitTimeFrame == -1 && <span>永久有效</span>}
                                {detailDisplay.limitTimeFrame == 1 && <span>{`${Number(detailDisplay.limitYear)}年${Number(detailDisplay.limitMonth)}月${Number(detailDisplay.limitDay)}日`}</span>}
                            </Descriptions.Item>
                            <Descriptions.Item label='聚合权益生效时间' span={2}>
                                {
                                    showEffectTime(detailDisplay, endTimeRenderPolymerization)
                                }
                            </Descriptions.Item>

                            <Descriptions.Item label='聚合权益限制里程' className='m-top'>
                                {
                                    computedMileage({
                                        limitLegendFrame: detailDisplay.limitLegendFrame,
                                        limitLegend: detailDisplay.limitLegend,
                                        extenderMileage: detailDisplay.extenderMileage
                                    })
                                }
                            </Descriptions.Item>
                            <Descriptions.Item label='聚合权益生效里程' span={2} className='m-top'>
                                {detailDisplay.effectMileageFrame === 0 && <span>-</span>}
                                {detailDisplay.effectMileageFrame === 1 && <span>
                                    <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detailDisplay.fixedBeginMileage}公里
                                    <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detailDisplay.fixedEndMileageStrategy)}</span>}
                                {detailDisplay.effectMileageFrame === 2 && <span>
                                    <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detailDisplay.relativeBeginMileageStrategyName}
                                    <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detailDisplay.relativeEndMileageStrategy)}</span>}
                            </Descriptions.Item>
                        </> :
                        <div className='tips'>客户端展示形式选择并列，会在客户端上同时展示前权益和延保权益共2条权益明细</div>
                }
            </Descriptions>
        </>
    )
}

export default ExtendStrategy