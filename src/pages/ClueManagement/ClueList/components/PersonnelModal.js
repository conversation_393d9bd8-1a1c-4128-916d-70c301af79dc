import React,{useState,useRef,forwardRef,useEffect} from 'react'
import {message, Modal,Form,Row,Col,Radio, Space} from 'antd'
import PublicTable from '@/components/Public/PublicTable'
import PublicTooltip from '@/components/Public/PublicTooltip'
import allUrl from '@/utils/url'
import {post,get} from '@/utils/request'

const PersonnelModal = (props) =>{
    const {visible,onCancel,tableSelectedRows,PersionType,getData,recordData} = props
    const tableRef = useRef()
    const [form] = Form.useForm()
    const [saleUserIds, setsaleUserIds] = useState([])
    const [modalVisable, setmodalVisable] = useState(false)
    const [StorePersonOptions, setStorePersonOptions] = useState([])
    const [StorePerson, setStorePerson] = useState([])
    const [StorePersonName, setStorePersonName] = useState([])
    const handleOk = () =>{
                let onlyLeadIds =tableSelectedRows.map((item,index)=>{
                    return {onlyLeadId:item.onlyLeadId,saleConsultantUserId:item.saleConsultantUserId}
                })
                let params = {
                    onlyLeadIds:PersionType == 1 ?[{onlyLeadId:recordData.onlyLeadId,saleConsultantUserId:recordData.saleConsultantUserId}] : onlyLeadIds,
                    newSaleConsultantUserId:StorePerson,
                    newSaleConsultantUserName:StorePersonName,
                    dealerCode:PersionType == 2 ?tableSelectedRows[0].dealerCode:recordData.dealerCode
                }
                post(allUrl.ClueManagement.batchtTansferLeadToSale,params).then(res=>{
                    if(res.success){
                        message.success(res.msg)
                        onCancel()
                        getData()
                    }else{
                        // message.error(res.msg)
                    }
                })
            
    }
    const findSalesmanList =()=>{
        get(allUrl.ClueManagement.findSalesmanList).then(res => {
            if (res.success) {
                let Dt =res.resp.map((item,index)=>{
                    return{value :item.userId,
                        name :item.userName,key:index+1}
                })
                setStorePersonOptions(Dt)
            } else {
                // message.error(res.msg)
            }
        })
    }
    const  StorePersonChange =(e)=>{
        setStorePerson(e.target.value)
        setStorePersonName(e.target.userName)
        console.log(e);
    }
    useEffect(()=>{
        let saleConsultantUserIds =tableSelectedRows.map((item,index)=>{
            return item.saleConsultantUserId
        })
        setsaleUserIds(saleConsultantUserIds)
        findSalesmanList()
    },[])
    return<Modal visible={visible} width={600} title='转移给' onOk={()=>setmodalVisable(true) } onCancel={onCancel} maskClosable={false}>
            <Form labelCol={{span:6}} form={form}>
                <Row>
                    <Col span={24}>
                        <Radio.Group onChange={StorePersonChange} value={StorePerson} >
                            <Space direction="vertical">
                                {
                                    StorePersonOptions&&
                                    StorePersonOptions.map((item,index)=><Radio value={item.value} userName={item.name}>{item.name}</Radio>)
                                }

                            </Space>
                        </Radio.Group>
                    </Col>
                </Row> 
            </Form>
            {
                        modalVisable ? 
                        <Modal visible={modalVisable} title='转移确定' onCancel={() => setmodalVisable(false)} maskClosable={false}width={500} onOk={handleOk}>
                            <Row>
                                {(PersionType === 1 &&recordData.saleConsultantUserId !== StorePerson)&&(PersionType === 2 &&saleUserIds.indexOf(StorePerson) == -1) ?
                                <Col span={24} >该销售顾问已有部分所选线索，若转移原销售顾问将战败，是否继续？</Col> :null
                                }
                                <Col span={24} >线索及跟进记录转移成功之后，原销售顾问将战败，是否转移？</Col>
                                {PersionType === 1 &&recordData.saleConsultantUserId == StorePerson&&
                                <Col span={24} >所选销售已有该线索，若转移原销售顾问将战败，是否继续？</Col>
                                }
                                {PersionType === 2 &&saleUserIds.indexOf(StorePerson) > -1&&
                                <Col span={24} >该销售顾问已有部分所选线索，若转移原销售顾问将战败，是否继续？</Col> 
                                }
                           </Row>
                        </Modal>:null
                    }
    </Modal>
}
export default PersonnelModal