import { getSeresFilePostSeresList, postSeresFileUserSeresSave } from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { useRequest } from '@/hooks/request';
import { Form, Input, message, Modal, ModalProps, Select } from 'antd';
import React, { FC, useEffect } from 'react';
import HeadquarterDivisionSelect from '../components/headquarter-division-select';
export interface HeadquartersModalProps extends ModalProps {
  data?: FileManage.SeresUserVO | null;
  onSuccess?: () => void;
}
const HeadquartersModal: FC<HeadquartersModalProps> = ({ data, onSuccess, ...props }) => {
  const [form] = Form.useForm<FileManage.SeresUserVO>();

  const { data: positions = [], loading: positionsLoading } = useRequest(() => getSeresFilePostSeresList());
  const handleOk = async () => {
    try {
      await form.validateFields();
      await postSeresFileUserSeresSave({
        ...data,
        ...form.getFieldsValue(),
      });
      onSuccess?.();
      message.success('保存成功');
      form.resetFields();
    } catch (error) {}
  };
  useEffect(() => {
    if (data) {
      console.log('data', data);
      form.setFieldsValue(data);
    }
  }, [data, form]);

  return (
    <Modal
      maskClosable={false}
      title={data ? '编辑总部人员' : '新增总部人员'}
      onOk={handleOk}
      afterClose={form.resetFields}
      {...props}
    >
      <Form form={form} {...modalFormLayout}>
        <Form.Item label="姓名" name="userName" rules={[{ required: true, message: '请输入姓名' }]}>
          <Input placeholder="请输入姓名" />
        </Form.Item>
        <Form.Item label="工号" name="userOneId" rules={[{ required: true, message: '请输入工号' }]}>
          <Input placeholder="请输入工号" />
        </Form.Item>
        <Form.Item label="总部部门" name="seresOrgId" rules={[{ required: true, message: '请选择总部部门' }]}>
          <HeadquarterDivisionSelect />
        </Form.Item>
        <Form.Item label="总部岗位" name="seresPostId" rules={[{ required: true, message: '请选择总部岗位' }]}>
          <Select
            placeholder="请选择总部岗位"
            loading={positionsLoading}
            options={positions.map(item => {
              return { label: item.name, value: item.id };
            })}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default HeadquartersModal;
