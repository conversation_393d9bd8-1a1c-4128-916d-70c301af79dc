export const columns = [
  {
    title: '权益账户编码',
    dataIndex: 'accountNo',
    width: 100,
    fixed: 'left',
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '权益账户',
    dataIndex: 'account',
    width: 100,
    fixed: 'left',
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },

  {
    title: '购车人',
    dataIndex: 'carBuyerName',
    width: 80,
    fixed: 'left',
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },

  {
    title: '车型',
    dataIndex: 'carTypeName',
    width: 50,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '车辆VIN',
    dataIndex: 'vin',
    width: 100,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '基础配置',
    dataIndex: 'carConfigName',
    width: 190,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '意向订单号',
    dataIndex: 'bookOrderNo',
    width: 110,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '正式订单号',
    dataIndex: 'formalOrderNo',
    width: 140,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '华为订单号',
    dataIndex: 'hwOrderNo',
    width: 110,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '销售订单号',
    dataIndex: 'saleOrderNo',
    width: 160,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '交付订单号',
    dataIndex: 'deliverOrderNo',
    width: 200,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
];

const partStatus_MAP = {
  0: '有效',
  1: '失效',
};

export const columnsPart = [
  {
    title: '车型',
    dataIndex: 'carType',
    width: 30,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '配件名称',
    dataIndex: 'partName',
    width: 70,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },

  {
    title: '配件编码',
    dataIndex: 'partNo',
    width: 80,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },

  {
    title: '配件包修类型',
    dataIndex: 'partTypeName',
    width: 80,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '原车保修期（月）',
    dataIndex: 'originalPartTime',
    width: 60,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '原车保修里程（公里）',
    dataIndex: 'originalPartMileage',
    width: 60,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '配件保修期（月）',
    dataIndex: 'partTime',
    width: 60,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },
  {
    title: '配件保修里程（公里）',
    dataIndex: 'partMileage',
    width: 70,
    render: (text, record) => {
      if (text) {
        return text;
      } else {
        return '—';
      }
    },
  },

  { title: '状态', dataIndex: 'partStatus', width: 30, render: (text, record) => partStatus_MAP[text] || '-' },
  { title: '更新批次', dataIndex: 'partVersion', width: 90, render: (text, record) => text || '-' },
];
