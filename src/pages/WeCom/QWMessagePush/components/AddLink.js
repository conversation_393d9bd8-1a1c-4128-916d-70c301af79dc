import React,{useEffect, useState} from 'react'
import {Modal,Input, message} from 'antd'
const AddLink = props =>{
    const {visible,onCancel,cb,defaultValue} = props
    const [value,setValue] = useState('')
    const handleOk = () =>{
        if(!value){
            message.error('请输入原文链接！')
            return
        }
        cb(value)
        onCancel()
    }
    useEffect(()=>{
        setValue(defaultValue)
    },[defaultValue])
    return <Modal title='添加' visible={visible} onCancel={onCancel} onOk={handleOk} maskClosable={false}>
        原文链接：<Input placeholder='例如：http://xxxx' value={value} onChange={(e)=>setValue(e.target.value)} style={{width:'90%',marginTop:'10px'}} allowClear />
    </Modal>
}
export default AddLink