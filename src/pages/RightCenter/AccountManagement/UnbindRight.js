import React, { useState, memo, useMemo, useEffect, useContext, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Modal, Form, Input, Row, Col, Table, message, Button } from 'antd';
import { axiosDelete } from '../../../utils/request';
import { getWidth, offsetLeft } from '@/utils/index';
import allUrl from '../../../utils/url';
import styles from '../antd.module.less';
import style from './reason.module.less';

const ChangeDeliveryTime = props => {
  const { handleCancel, visible, reloadChangeInfo, dataSource, accountNo, type } = props;
  const [form] = Form.useForm();
  const [selectedList, setSelectedList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [btnDisabled, setBtnDisabled] = useState(true);
  const [reasonStatus, setReasonStatus] = useState('');
  const formRef = useRef(null);

  const columns = [
    {
      title: '权益包编码',
      dataIndex: 'packNo',
    },
    {
      title: '权益包名称',
      dataIndex: 'name',
    },
    {
      title: '权益包价格',
      dataIndex: 'price',
      render: text => <p>{text ? `${text}元` : '-'}</p>,
    },
    {
      title: '权益描述',
      dataIndex: 'description',
      render: text => <p>{text || '-'}</p>,
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      setSelectedList(selectedRows);
      if (selectedRows.length) {
        const { reason } = form.getFieldsValue();
        if (!reason) {
          setBtnDisabled(true);
        } else {
          setBtnDisabled(false);
        }
      } else {
        setBtnDisabled(true);
      }
    },
  };
  const onChangeReason = () => {
    const { reason } = form.getFieldsValue();
    if (reason) {
      if (selectedList.length) {
        setBtnDisabled(false);
      }
      setReasonStatus('');
      return true;
    } else {
      setBtnDisabled(true);
      setReasonStatus('error');
      return false;
    }
  };

  const handleUnbind = () => {
    Modal.confirm({
      title: '提示',
      content: '确认该解绑该权益？',
      okText: '确认',
      onOk() {
        handleOk();
      },
    });
  };

  const handleOk = () => {
    if (!onChangeReason()) return;
    setLoading(true);
    const { reason } = form.getFieldsValue();
    // 将selectedList按packNo分组，并收集每个packNo下的所有accountPackRelationId
    const unbindingPackageList = selectedList.reduce((acc, current) => {
      const packNo = current.packNo;
      const accountPackRelationIdList = current.sortMap.reduce((ids, sortItem) => {
        return [...ids, ...sortItem.content.map(item => item.accountPackRelationId)];
      }, []);

      // const existingPack = acc.find(item => item.packNo === packNo);
      // if (existingPack) {
      //   existingPack.accountPackRelationIdList.push(...accountPackRelationIdList);
      // } else {
      //   acc.push({
      //     packNo,
      //     accountPackRelationIdList,
      //   });
      // }
      acc.push({
        packNo,
        accountPackRelationIdList,
      });
      return acc;
    }, []);

    const params = {
      unbindingPackageList,
      unbindingCause: reason,
    };

    // 权益账户解除权益包
    axiosDelete(allUrl.AccountManagement.unbindRightPackage({ accountNo }), { ...params }).then(res => {
      const { success, msgCode, msg, resp } = res;
      if (success) {
        if (['0000', '3108'].includes(msgCode)) {
          Modal.warning({
            content: resp[0],
            okText: '我知道了',
          });
        }
        reloadChangeInfo();
      } else {
        // Modal.error({
        //     content: msg,
        //     okText: '我知道了'
        // })
        // message.error('更新失败！')
      }
      setLoading(false);
      handleCancel();
    });
  };
  const layout = {
    // labelCol: { span: 6 },
    wrapperCol: { span: 15 },
  };
  return (
    <Modal
      open={visible}
      width={getWidth()}
      style={{ left: offsetLeft() }}
      onCancel={handleCancel}
      maskClosable={false}
      title="权益包解绑"
      footer={[
        <Button key="back" onClick={handleCancel} style={{ marginRight: '10px' }}>
          取消
        </Button>,
        // <Popconfirm placement="topLeft" title='确认该解绑该权益？' disabled={btnDisabled} onConfirm={handleOk} okText="确认" cancelText="取消">
        //     <Button key="submit" type="primary" disabled={btnDisabled} loading={loading}>
        //         解绑
        //     </Button>
        // </Popconfirm>
        <Button key="submit" type="primary" disabled={btnDisabled} loading={loading} onClick={handleUnbind}>
          解绑
        </Button>,
      ]}
    >
      <Table
        className={styles['blue-table']}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        columns={columns}
        dataSource={dataSource}
      />
      <div className={style.reason}>
        <Form name="unbind" ref={formRef} form={form} {...layout}>
          <Row>
            <Col span={24}>
              <Form.Item label="解绑原因" name="reason" validateStatus={reasonStatus} hasFeedback required={true}>
                <Input allowClear placeholder="请输入解绑原因" maxLength={50} onChange={onChangeReason} />
              </Form.Item>
              <p style={{ color: 'red', fontSize: '12px', position: 'absolute', top: '38px', left: '84px' }}>
                最多支持50个字，不支持其它表情和特殊符号，如配置则无法正常展示
              </p>
            </Col>
          </Row>
        </Form>
      </div>
    </Modal>
  );
};
export default memo(ChangeDeliveryTime);
