import { useCheckPermission } from '@/hooks/auth';
import { UserPermission } from '@/interfaces/permission';
import { FC, ReactElement, cloneElement, useMemo } from 'react';

export enum UserPermissionBehavior {
  /**
   * @description 禁用
   */
  Disabled = 'disabled',
  /**
   * @description 隐藏
   */
  Hide = 'hide',
}

export interface UserPermissionAuthorizeProps extends UserPermission {
  behavior?: UserPermissionBehavior;
  children: ReactElement;
}

const UserPermissionAuthorize: FC<UserPermissionAuthorizeProps> = ({
  children,
  code,
  behavior = UserPermissionBehavior.Hide,
}) => {
  const checkPermission = useCheckPermission();

  const authorized = useMemo(() => checkPermission(code), [checkPermission, code]);

  return authorized
    ? children
    : behavior === UserPermissionBehavior.Disabled
    ? cloneElement(children, {
        disabled: true,
      })
    : null;
};

export default UserPermissionAuthorize;
