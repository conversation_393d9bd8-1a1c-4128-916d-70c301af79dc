// @import "../tableLayout.less";

.account-PublicList {

    // padding-left: 24px;
    // .queryArea {
    //     margin-left: 24px;
    // }

    // .flex {
    //     display: flex;
    //     align-items: center;
    // }

    // .border-bottom {
    //     border-bottom: solid 1px #e8e8e8;
    // }

    // .tableTitle {
    //     background-color: white;
    //     padding: 24px 32px 0px 32px;
    //     justify-content: space-between;

    //     .text {
    //         font-size: 20px;
    //         font-family: PingFangSC, PingFangSC-Medium;
    //         font-weight: 500;
    //         text-align: left;
    //         color: rgba(0, 0, 0, 0.85);
    //         line-height: 28px;
    //     }

    //     .bts {
    //         .ant-btn {
    //             margin: 0 7px;
    //         }
    //     }
    // }

    // .TablePanele {
    //     width: 100%;
    //     margin-left: -20px;

    //     .status {
    //         min-width: 40px;
    //         display: inline-block;
    //         vertical-align: middle;
    //         text-align: center;
    //         padding: 0 8px;
    //     }
    // }

    // .tableData {
    //     padding: 24px;
    //     background: #f0f2f5;

    //     .info-box {
    //         background: white;
    //         padding: 24px;
    //     }

    //     .detailPanel {
    //         padding: 24px 24px 0 24px;
    //         background-color: white;
    //     }

    //     .marg {
    //         margin-top: 24px
    //     }

    //     .ant-table-wrapper {
    //         background-color: white;

    //         .ant-table {
    //             padding: 20px 32px;
    //         }

    //         .ant-table-pagination {
    //             margin: 16px 24px;
    //         }
    //     }
    // }

    // .PublicList_FormQuery {
    //     padding-top: 16px;
    //     padding-left: 24px;

    //     .ant-col-7 {
    //         .ant-form-item {
    //             .ant-form-item-control-input {
    //                 width: 90%;

    //                 .ant-form-item-control-input-content {
    //                     .ant-picker {
    //                         width: 100%;
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     .FormQuerySubmit {
    //         display: flex;
    //         justify-content: flex-end;

    //         .operationButtons {
    //             span {
    //                 color: #1890ff;
    //                 cursor: pointer;

    //                 .anticon {
    //                     margin-left: 6px;
    //                 }
    //             }
    //         }
    //     }
    // }

    .mark {
        // { name: '已交付', value: 1, color: ['#1890FF', '#1890FF'] },
        // { name: '未交付', value: 2, color: ['#5DA9F0', '#EBF5FF'] },
        // { name: '已退订', value: 3, color: ['#F4C836', '#FFFBE6'] },
        // { name: '异常关单', value: 4, color: ['#54AA9D', '#EAF8F6'] },
        // { name: '已注销', value: 5, color: ['rgba(0, 0, 0, 0.18)', 'rgba(0, 0, 0, 0.04)'] },

        width: 44px;
        height: 44px;
        position: absolute;
        top: 0;
        left: 0;

        &.bg1 {
            background-image: linear-gradient(135deg, #1890FF 50%, rgba(255, 255, 255, 0) 50%);

            .mark-content {
                background-image: linear-gradient(135deg, #1890FF 50%, rgba(255, 255, 255, 0) 50%);
                color: #fff;
            }
        }

        &.bg2 {
            background-image: linear-gradient(135deg, #5DA9F0 50%, rgba(255, 255, 255, 0) 50%);

            .mark-content {
                background-image: linear-gradient(135deg, #EBF5FF 50%, rgba(255, 255, 255, 0) 50%);
                color: rgba(24, 144, 255, 1);
            }
        }

        &.bg3 {
            background-image: linear-gradient(135deg, #F4C836 50%, rgba(255, 255, 255, 0) 50%);

            .mark-content {
                background-image: linear-gradient(135deg, #FFFBE6 50%, rgba(255, 255, 255, 0) 50%);
                color: rgba(169, 125, 38, 1);
            }
        }

        &.bg4 {
            background-image: linear-gradient(135deg, #54AA9D 50%, rgba(255, 255, 255, 0) 50%);

            .mark-content {
                background-image: linear-gradient(135deg, rgba(234, 248, 246, 1) 50%, rgba(255, 255, 255, 0) 50%);
                color: rgba(63, 150, 137, 1);
            }
        }

        &.bg5 {
            background-image: linear-gradient(135deg, rgba(0, 0, 0, 0.18) 50%, rgba(255, 255, 255, 0) 50%);

            .mark-content {
                background-image: linear-gradient(135deg, rgb(245 245 245) 50%, rgba(255, 255, 255, 0) 50%);
                color: rgba(0, 0, 0, 0.65);
            }
        }

        .mark-content {
            width: 40px;
            height: 40px;
            position: absolute;
            top: 1px;
            left: 1px;
            font-size: 12px;
            overflow: hidden;

            span {
                display: block;
                width: 100%;
                transform: rotate(-45deg) scale(.76);
                text-align: center;
                position: absolute;
                left: 0;
                top: 0;
                transform-origin: 21px 19px;
                white-space: nowrap;
                display: flex;
                justify-content: center;
                font-weight: bold;
            }
        }
    }
}

// .detail-overflow {
//     height: calc(100vh - 48px - 54px - 80px);
//     overflow-y: auto;
// }

// .batch-container {
//     .package-info {
//         .info-top {
//             background-color: #d6ebff;
//             height: 50px;
//             padding-top: 13px;
//             padding-left: 22px;
//         }
//     }
// }

//关联弹窗样式
// .batch-add {
//     .FormQuerySubmit {
//         display: flex;
//         justify-content: flex-end;

//         .operationButtons {
//             span {
//                 color: #1890ff;
//                 cursor: pointer;

//                 .anticon {
//                     margin-left: 6px;
//                 }
//             }
//         }

//         .Reset {
//             margin-right: 20px;
//         }
//     }
// }

//导出弹窗样式
.export-content {
    background-color: #F1F8FF;

    .export-top {
        height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 36px;
        background-color: #DAEDFF;
        padding-left: 20px;
    }

    .export-bottom {
        padding-left: 16px;
        padding-right: 16px;
        padding-bottom: 24px;
        padding-top: 24px;
    }

    .export-add {
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 24px;
    }

    .export-last {
        padding: 0 16px 24px 16px;
    }
}

//导出进入下载中心提示弹窗样式
.modal-result-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .modal-top {
        margin-top: 20px;
        // margin-left: 45px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 26px;

        .bule-text {
            color: #1890FF;
        }

        .left-text {
            margin-left: 22px;
        }
    }

    .modal-bottom {
        margin-top: 20px;
        // margin-left: 75px;
        width: 150px;
        height: 32px;

        .btn {
            width: 150px;
            height: 32px;
        }
    }
}