import React, { useEffect, useState } from 'react'
// import styled from 'styled-components'
import { useSelector } from 'react-redux'

import { Table, message, Button, Row, Col, Divider } from 'antd'
// import FormQuery from './FormQuery'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { roleJudgment } from '@/utils/authority'
import { PlusOutlined } from '@ant-design/icons';
import EditCarMarketType from './EditCarMarketType';
import AddCarMarketType from './AddCarMarketType';
import styles from '../antd.module.less'
import tableModuleStyles from '../tableLayout.module.less'

const BusinessCodeMaintain = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const [loading, setLoading] = useState(false)
    const [dataSource, changeDataSource] = useState([])
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)
    const [total, changeTotal] = useState(0)
    const [tableHeight, setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({})
    const [DictData, setDictData] = useState([])
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [codeInfoSource, changeCodeInfo] = useState([])
    const [addDisabled, setAddDisabled] = useState(false);
    const [isAddModalVisible, setIsAddModalVisible] = useState(false);

    useEffect(() => {
        get(allUrl.common.entryLists, { codes: 'equity_car_type_all' }).then(res => {
            if (res.success && res.resp[0] !== null && res.resp !== null) {
                let Dt = res.resp[0]
                Dt['equity_car_type_all'].forEach(item => {
                    item.name = item.entryMeaning
                    item.value = item.entryValue
                })
                setDictData(Dt['equity_car_type_all'])
            } else {
                let Data = [
                    { name: '全部', value: '0' },
                ]
                setDictData(Data)
                // message.error(res.message)
            }
        })
    }, [])
    useEffect(() => {
        if (userInfo) {
            setLoading(true)
            let query = { ...defaultQuery }
            let params = { pageNum: current, pageSize, ...query }
            post(allUrl.CarMarketType.getCarMarketTypeList, { ...params }).then(res => {
                if (res.success && res.resp !== null) {
                    res.resp.map((item, index) => item.key = index + 1)
                    changeDataSource(res.resp)
                    changeTotal(res.total)
                } else {
                    changeDataSource([])
                    changeTotal(0)
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }, [defaultQuery, userInfo, current, pageSize])


    const reloadList = () => {
        if (userInfo) {
            setLoading(true)
            let query = { ...defaultQuery }
            let params = { pageNum: current, pageSize, ...query }
            post(allUrl.CarMarketType.getCarMarketTypeList, { ...params }).then(res => {
                if (res.success && res.resp !== null) {
                    res.resp.map((item, index) => item.key = index + 1)
                    changeDataSource(res.resp)
                    changeTotal(res.total)
                } else {
                    changeDataSource([])
                    changeTotal(0)
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }

    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const resetPage = () => {
        changeCurrent(1)
        changePageSize(10)
    }
    const onSearch = (values) => {
        setDefaultQuery(values)
        resetPage()
    }
    const initPage = (flag) => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if (!flag) {
            h = winH - 47 - 54 - 345
        } else {
            h = winH - 47 - 54 - 400
        }
        setTableHeight(h)
    }
    useEffect(() => {
        initPage()
    }, [])
    // 创建
    const AddBusinessCode = (record) => {

        setIsAddModalVisible(true);
        console.log(addDisabled, 'chuangjian')
    }

    // 编辑
    const EditAt = (record) => {
        setIsEditModalVisible(true);
        setAddDisabled(true)
        console.log(addDisabled, 'bianji')
        let params = {
            id: record.id
        }
        get(allUrl.CarMarketType.getCarMarketTypeDetail, { ...params }).then(res => {
            if (res.success) {
                res.resp.map((item, index) => item.key = index + 1)
                changeCodeInfo(res.resp[0])
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }

    console.log(DictData)
    const handleCancel = () => {
        setIsEditModalVisible(false);
        setIsAddModalVisible(false)
        console.log('quxiao');
    };
    let searchList = [
        { label: '车辆市场类型', name: 'name', type: 'Input', placeholder: '请输入车辆市场类型', colSpan: 6 },
        {
            label: '车型', name: 'carType', type: 'Select', placeholder: '请选择', colSpan: 6, data: DictData || [], initialValue: '0'
        },
    ]
    const InforData = {
        rowKey: record => record.key,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content' },
        columns: [
            { title: '序号', dataIndex: 'index', fixed: 'left', width: 30, render: (text, record, index) => <div style={{ textAlign: 'left' }}>{(current - 1) * pageSize + index + 1}</div> },
            {
                title: '车辆市场类型', dataIndex: 'name', width: 100, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '车型', dataIndex: 'carTypeName', width: 260, render: (text, record) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '操作', width: 120, fixed: 'right', dataIndex: 'Operation', render: (text, record) => (<>
                    {roleJudgment(userInfo, 'CAR_MARKET_TYPE_EDIT') ?
                        <Button type='link' size='small' onClick={() => EditAt(record)} >编辑 </Button> : null
                    }
                </>)
            }
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: null,
    };
    return (
        <div className={`${tableModuleStyles.PublicList}`}>
            {/* <FormQuery initPage={initPage} onSearch={onSearch} resetPage={resetPage} searchList={searchList} defaultQuery={defaultQuery} /> */}
            <PublicTableQuery isCatch={false} isFormDown={false} onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery} />
            <div className="tableData">
                <Row className='tableTitle'>
                    <Col className='text' style={{ color: 'black', fontSize: 20 }}>车辆市场类型列表</Col>
                    {roleJudgment(userInfo, 'CAR_MARKET_TYPE_BUILD') ?
                        <Col className='bts'>
                            <Button type='primary' icon={<PlusOutlined />} onClick={AddBusinessCode}>创建</Button>
                        </Col> : null}
                </Row>
                <Table {...InforData} className={styles['blue-table']} />
            </div>
            {isAddModalVisible &&
                <AddCarMarketType handleCancel={handleCancel} visible={isAddModalVisible} codeInfoSource={codeInfoSource} reloadList={reloadList} />
            }
            {isEditModalVisible &&
                <EditCarMarketType handleCancel={handleCancel} visible={isEditModalVisible} codeInfoSource={codeInfoSource} onSearch={onSearch} reloadList={reloadList} />
            }
        </div>
    )
}
export default BusinessCodeMaintain
