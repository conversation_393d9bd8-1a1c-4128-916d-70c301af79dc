import {
  postSeresFileUserSeresDelete,
  postSeresFileUserSeresImport,
  postSeresFileUserSeresPage,
} from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import ProTable, { ProTableActions } from '@/components/pro-table';
import UserPermissionAuthorize from '@/components/user-permission-authorize';
import { modalFormLayout } from '@/constants/form';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { headquartersPermissions } from '@/permissions/file';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Modal, Space, Typography, Upload, UploadFile } from 'antd';
import React, { FC, useCallback, useRef, useState } from 'react';
import HeadquartersModal, { HeadquartersModalProps } from './headquarters-modal';

const Headquarters: FC = () => {
  const actionRef = useRef<ProTableActions>(null);
  const [headquartersModal, setHeadquartersModal] = useState<HeadquartersModalProps>({
    open: false,
  });
  const [form] = Form.useForm<{ file: UploadFile }>();
  const [open, setOpen] = useState(false);
  const [pending, setPending] = useState(false);
  const tableRequest = usePaginationTableListStyleRequest(({ current: pageNum, pageSize, params }) => {
    return postSeresFileUserSeresPage({ ...params, pageNum, pageSize });
  });
  const handleDeleteClick = useCallback((data: FileManage.SeresUserDTO) => {
    Modal.confirm({
      title: '删除总部人员',
      content: `您正在删除【${data.userName}】，请确认是否删除`,
      onOk: async () => {
        try {
          await postSeresFileUserSeresDelete({ id: data.id as unknown as number });
          actionRef.current?.reload();
          message.success('删除成功');
        } catch (error) {}
      },
    });
  }, []);

  const handleEditClick = useCallback((data: FileManage.SeresUserDTO) => {
    setHeadquartersModal({ open: true, data });
  }, []);

  const handleModalOk = useCallback(async () => {
    try {
      const { file } = await form.validateFields();
      const formData = new FormData();
      formData.append('file', file.originFileObj as File);
      await postSeresFileUserSeresImport(formData);
      message.success('导入成功');
      actionRef.current?.reload();
      setOpen(false);
    } catch (error) {
    } finally {
      setPending(false);
    }
  }, [form]);

  const handleAddClick = useCallback(() => {
    setHeadquartersModal({ open: true });
  }, []);

  const handleImportClick = useCallback(() => {
    setOpen(true);
  }, []);

  return (
    <PageContainer>
      <ProTable<FileManage.FrequentPost>
        request={tableRequest}
        rowKey="id"
        actionRef={actionRef}
        columns={[
          {
            key: 'index',
            valueType: 'index',
            title: '序号',
            fixed: 'left',
            width: 80,
          },
          {
            title: '姓名',
            dataIndex: 'userName',
          },
          {
            title: '人员',
            dataIndex: 'searchText',
            hideInTable: true,
            search: {
              type: Input,
              fieldProps: {
                placeholder: '请输入姓名/工号',
              },
            },
          },

          {
            title: '工号',
            dataIndex: 'userOneId',
          },
          {
            title: '总部部门',
            dataIndex: 'seresOrgName',
          },
          {
            title: '总部岗位',
            dataIndex: 'seresPostName',
          },
          {
            title: '操作',
            fixed: 'right',
            width: 100,
            render: (_, record) => {
              return (
                <Space>
                  <UserPermissionAuthorize {...headquartersPermissions.edit}>
                    <Typography.Link onClick={() => handleEditClick(record)}>编辑</Typography.Link>
                  </UserPermissionAuthorize>
                  <UserPermissionAuthorize {...headquartersPermissions.delete}>
                    <Typography.Link type="danger" onClick={() => handleDeleteClick(record)}>
                      删除
                    </Typography.Link>
                  </UserPermissionAuthorize>
                </Space>
              );
            },
          },
        ]}
        actions={[
          {
            type: 'primary',
            children: '添加总部人员',
            icon: <PlusOutlined />,
            onClick: handleAddClick,
            permission: headquartersPermissions.add,
          },
          {
            type: 'primary',
            children: '批量导入人员',
            onClick: handleImportClick,
            permission: headquartersPermissions.import,
          },
        ]}
        actionPosition="toolbar"
      />
      <HeadquartersModal
        {...headquartersModal}
        onSuccess={() => {
          actionRef.current?.reload();
          setHeadquartersModal({ open: false });
        }}
        onCancel={() => {
          setHeadquartersModal({ open: false });
        }}
      />
      <Modal
        maskClosable={false}
        title="批量导入人员"
        open={open}
        onCancel={() => setOpen(false)}
        forceRender
        afterClose={form.resetFields}
        onOk={handleModalOk}
        confirmLoading={pending}
      >
        <Form {...modalFormLayout} form={form}>
          <Form.Item
            label="导入人员文件"
            name="file"
            rules={[{ required: true, type: 'object', message: '请导入人员文件' }]}
            valuePropName="fileList"
            normalize={value => value?.fileList?.[0]}
            getValueProps={value => ({
              fileList: value ? [value] : [],
            })}
          >
            <Upload accept=".xlsx" showUploadList maxCount={1} beforeUpload={() => false}>
              <Button>上传文件</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default Headquarters;
