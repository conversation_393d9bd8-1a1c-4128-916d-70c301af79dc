import 'braft-editor/dist/index.css'
import './index.less'
import React,{useEffect,useState} from 'react'
import BraftEditor from 'braft-editor'
import { message } from 'antd'
import Cookies from 'js-cookie'


const BasicDemo = (props) =>{
    const {cb,size,url,defaultExcludeControls,defaultHtmlCon} = props
    const [editorState,setEditorState] = useState('')
    const [outputHTML,setOutputHTML] = useState(null)
    const [outputText,setOutputText] = useState(null)
    const  controls = [
        'undo', 'redo','separator', 'font-size', 'line-height', 'letter-spacing', 'text-color', 'bold', 'italic', 'underline', 'strike-through',
        'superscript', 'subscript', 'remove-styles', 'text-align', 'text-indent', 'headings', 'list-ul', 'list-ol', 'hr', 'clear', 'separator', 'fullscreen','media',
    ];
    const excludeControls = [

    ]
    const handleChange = (editorState) => {
        setEditorState(editorState)
        setOutputHTML(editorState.toHTML())
        setOutputText(editorState.toText())
        if(cb) {
            cb({
                htmlCon:editorState.toHTML(),
                textCon:editorState.toText()
            })
        }
    }

    const myUploadFn = (param)=>{
        const serverURL = url
        const xhr = new XMLHttpRequest
        const fd = new FormData()
        const successFn = (response) => {
            // 假设服务端直接返回文件上传后的地址
            // 上传成功后调用param.success并传入上传后的文件地址

            console.log(param)
            if(JSON.parse(xhr.responseText).success){
                param.success({
                    url: JSON.parse(xhr.responseText).resp[0].url,
                    meta: {
                        id: 'img' + Math.random()*10,
                        title: '上传成功！',
                        alt: '上传成功！',
                        loop: false, // 指定音视频是否循环播放
                        autoPlay: false, // 指定音视频是否自动播放
                        controls: true, // 指定音视频是否显示控制栏
                    }
                })
            }else{
                message.error('上传失败！')
            }
        }

        const progressFn = (event) => {
            // 上传进度发生变化时调用param.progress
            param.progress(event.loaded / event.total * 100)
        }

        const errorFn = (response) => {
            // 上传发生错误时调用param.error
            param.error({
                msg: 'unable to upload.'
            })
        }

        xhr.upload.addEventListener("progress", progressFn, false)
        xhr.addEventListener("load", successFn, false)
        xhr.addEventListener("error", errorFn, false)
        xhr.addEventListener("abort", errorFn, false)

        fd.append('file', param.file)
        xhr.open('POST', serverURL, true)
        xhr.setRequestHeader("appid", "1");
        xhr.setRequestHeader("Authorization", Cookies.get('scrm_token'));
        xhr.send(fd)
    };
    const myValidateFn = (file) => {
        if (size) {
            const isLt2M = file.size / 1024 / 1024 < size;
            if (!isLt2M) {
                message.error(`文件大小不能超过${size}MB!`);
                return false
            }
        }
        return true
    }
    useEffect(()=>{
        setEditorState(BraftEditor.createEditorState(defaultHtmlCon))
    },[defaultHtmlCon])
    return <BraftEditor
            value={editorState}
            className='my_Editor'
            // controls={controls}
            excludeControls={defaultExcludeControls ? defaultExcludeControls : excludeControls}
            media={{
                uploadFn: myUploadFn,
                validateFn: myValidateFn,
                accepts :{
                    image: 'image/png,image/jpeg,image/gif,image/webp,image/apng,image/svg',
                    video: 'video/mp4',
                    audio: 'audio/mp3'
                }
            }}
            onChange={handleChange}
            placeholder={'请输入正文内容'}
        />
}
export default BasicDemo