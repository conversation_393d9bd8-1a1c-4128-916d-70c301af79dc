import { getSeresFileItemSeresUserQuery, postSeresFileItemSeresUserSave } from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { useRequest } from '@/hooks/request';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Form, Modal, ModalProps, Row, Spin } from 'antd';
import React, { FC, useCallback, useEffect, useState } from 'react';
import HeadquarterDivisionSelect from '../components/headquarter-division-select';
import HeadQuarterPersonSelector from './headquarter-person-selector';
export interface HeadquarterPersonModalProps extends ModalProps {
  data?: { id: number | string };
  onSuccess?: () => void;
}
const HeadquarterPersonModal: FC<HeadquarterPersonModalProps> = ({ data, onSuccess, ...restProps }) => {
  const { data: [editData] = [], loading } = useRequest(
    () => {
      if (data?.id) {
        return getSeresFileItemSeresUserQuery({ id: data.id as number });
      }
      return Promise.resolve([]);
    },
    {
      deps: [data?.id],
    }
  );
  const [form] = Form.useForm();
  useEffect(() => {
    if (editData) {
      form.setFieldsValue({
        person: {
          enable: (editData.seresUserList?.length ?? 0) > 0,
          value: editData.seresUserList,
        },
        division: { enable: (editData.seresOrgIdList?.length ?? 0) > 0, value: editData.seresOrgIdList },
      });
      if ((editData.seresUserList ?? []).length > 0) {
        setShowHeadquarterPerson(true);
      }
    }
  }, [editData, form]);

  const handleOk = useCallback(async () => {
    try {
      const { division, person } = await form.validateFields();
      await postSeresFileItemSeresUserSave({
        fileId: data?.id as number,
        seresOrgIdList: division.enable ? division.value : [],
        seresUserOneIdList: person.enable ? person.value.map(item => item.userOneId) : [],
      });
      onSuccess?.();
    } catch (error) {
      console.log('error', error);
    }
  }, [data, form, onSuccess]);

  const [showHeadquarterPerson, setShowHeadquarterPerson] = useState<boolean>(false);
  return (
    <Modal
      maskClosable={false}
      afterClose={() => {
        form.resetFields();
        setShowHeadquarterPerson(false);
      }}
      width={800}
      onOk={handleOk}
      title={null}
      {...restProps}
    >
      <Spin spinning={loading}>
        <Form form={form} {...modalFormLayout}>
          <Form.Item
            label="总部人员"
            required
            name="headquarter"
            rules={[
              ({ getFieldValue }) => ({
                validator() {
                  const departmentEnable = getFieldValue(['division', 'enable']);
                  const personsEnable = getFieldValue(['person', 'enable']);
                  if (!departmentEnable && !personsEnable) {
                    return Promise.reject('至少选中1个部门或者1个人员');
                  }
                  return Promise.resolve();
                },
              }),
            ]}
            validateStatus=""
          >
            <Row>
              <Form.Item valuePropName="checked" name={['division', 'enable']}>
                <Checkbox
                  onChange={() => {
                    form.validateFields(['headquarter', ['division', 'value']]);
                  }}
                >
                  按部门
                </Checkbox>
              </Form.Item>
              <Col span={18}>
                <Form.Item>
                  <Form.Item
                    name={['division', 'value']}
                    noStyle
                    rules={[
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          const departmentEnable = getFieldValue(['division', 'enable']);
                          if (departmentEnable && (!value || value.length === 0)) {
                            return Promise.reject('请选择部门');
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <HeadquarterDivisionSelect showAllSelect mode="multiple" />
                  </Form.Item>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Form.Item valuePropName="checked" name={['person', 'enable']}>
                <Checkbox
                  onChange={() => {
                    form.validateFields(['headquarter', ['person', 'value']]);
                  }}
                >
                  按人员
                </Checkbox>
              </Form.Item>

              <Form.Item
                name={['person', 'value']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const departmentEnable = getFieldValue(['person', 'enable']);
                      if (departmentEnable && (!value || value.length === 0)) {
                        return Promise.reject('请选择人员');
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
                noStyle
              >
                <Col span={12}>
                  <Button
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setShowHeadquarterPerson(true);
                    }}
                  >
                    添加人员
                  </Button>
                </Col>
              </Form.Item>
            </Row>
            {showHeadquarterPerson && (
              <Form.Item
                name={['person', 'value']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const departmentEnable = getFieldValue(['person', 'enable']);
                      if (departmentEnable && (!value || value.length === 0)) {
                        return Promise.reject('请选择人员');
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
                noStyle
              >
                <HeadQuarterPersonSelector rowKey="userOneId" />
              </Form.Item>
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default HeadquarterPersonModal;
