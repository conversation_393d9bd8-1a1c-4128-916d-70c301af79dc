import React, { useState,memo, useEffect} from 'react'
import { Form, Button, Input, Select, Row, Col ,DatePicker} from 'antd'
import {UpOutlined,DownOutlined} from '@ant-design/icons';
const { Option } = Select
const { RangePicker } = DatePicker;

const FormQuery = (props) => {
    const {onSearch,searchList,defaultQuery,tabsKey,initPage} = props
    const [form] = Form.useForm()
    const [formDown,ChangeFormDown] = useState(false)
    const [searchFormList,changeSearchList] = useState([])
    const [isPutItAway,changeIsPutItAway] = useState(false)
    const handleSearch = () =>{
        form.validateFields().then(values => {
            for(let i in values){
                if(!values[i]){
                    values[i] = ''
                }
                if(!i){
                    delete values[i]
                }
            }
            onSearch(values)
        })
    }
    const onReset = () =>{
        form.resetFields()
        onSearch()
    }
    const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
    };
    useEffect(()=>{
        searchList.forEach(item=>{
            item.onPressEnter = handleSearch
        })
        let colSpanNum=searchList.reduce((num,item)=>num+item.colSpan,0);
        let ColNum = 24 * Math.ceil(colSpanNum/24) - colSpanNum -  6
        if(colSpanNum === 18){
            changeIsPutItAway(false)
        }else{
            if(ColNum <0){
                searchList.push({colSpan:18})
            }else if(ColNum  === 12){
                searchList.push({colSpan:12})
            }else if(ColNum  === 6){
                searchList.push({colSpan:6})
            }
        }
        changeSearchList(searchList)
    },[searchList])
    useEffect(()=>{
        if(JSON.stringify(defaultQuery) !== '{}'){
            form.setFieldsValue(defaultQuery)
        }
    },[])
    useEffect(()=>{
        form.resetFields()
    },[tabsKey])
    return (
        <>
            <Form className='PublicList_FormQuery' form={form} {...layout}>
                <Row style={{paddingRight:'20px'}}>
                    {
                        searchFormList.map((item,index)=>{
                            return ( index <=2 &&
                            <Col span={item.colSpan} key={index}>
                                <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []}>
                                    {
                                        item.type === 'Input'?
                                        <Input placeholder={item.placeholder} allowClear onPressEnter={item.onPressEnter} />
                                        :item.type === 'Select' ?
                                        <Select placeholder='请选择' allowClear mode={item.mode || ''}>
                                            {
                                                item.data.map((ele,i)=>{
                                                    return <Option key={i} value={ele.value}>{ele.name}</Option>
                                                })
                                            }
                                        </Select>
                                        :item.type === 'RangePicker' ?
                                        <RangePicker placeholder={item.placeholder} />
                                        :null
                                    }
                                </Form.Item>
                            </Col>)
                        })
                    }
                    {
                        searchFormList.map((item,index)=>{
                            return ( index >2 && formDown &&
                            <Col span={item.colSpan} key={index}>
                                <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []}>
                                    {
                                        item.type === 'Input'?
                                        <Input placeholder={item.placeholder} allowClear onPressEnter={item.onPressEnter} />
                                        :item.type === 'Select' ?
                                        <Select placeholder='请选择' allowClear>
                                            {
                                                item.data.map((ele,i)=>{
                                                    return <Option key={i} value={ele.value}>{ele.name}</Option>
                                                })
                                            }
                                        </Select>
                                        :item.type === 'RangePicker' ?
                                        <RangePicker placeholder={item.placeholder} />
                                        :null
                                    }
                                </Form.Item>
                            </Col>)
                        })
                    }
                    {/* <Col span={6}>
                        <Form.Item>
                            <Button onClick={handleSearch}>查询</Button>
                        </Form.Item>
                    </Col> */}
                    <Col span={6} className='FormQuerySubmit'>
                        <Col span={6} className='Reset'>
                            <Button onClick={onReset}>重置</Button>
                        </Col>
                        <Col span={6} className='Search'>
                            <Button type='primary' onClick={handleSearch}>查询</Button>
                        </Col>
                        {
                            isPutItAway?
                            <Col span={6} className='operationButtons'>
                                <Form.Item>
                                    {
                                        formDown ?
                                        <span onClick={()=>{
                                            ChangeFormDown(false)
                                            if(initPage) initPage()
                                            }}>
                                            <span>收起</span><UpOutlined />
                                        </span>
                                        :<span onClick={()=>{
                                            ChangeFormDown(true)
                                            if(initPage) initPage(true)
                                        }}>
                                            <span>展开</span><DownOutlined />
                                        </span>
                                    }
                                </Form.Item>
                            </Col>:null
                        }
                    </Col>
                </Row>
            </Form>
        </>
    )
}
export default memo(FormQuery)