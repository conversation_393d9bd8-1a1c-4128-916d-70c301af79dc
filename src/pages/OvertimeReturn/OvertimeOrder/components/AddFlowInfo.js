import React, { useState } from 'react'
import { Modal, Form, Input, DatePicker, message, Spin, Radio,InputNumber } from 'antd'
import moment from 'moment'
import allUrl from '../../../../utils/url'
import { post } from '../../../../utils/request'
const AddFlowInfo = (props) => {
    const { visible, title, onCancel, GetData } = props
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false)
    const [subsidyType, setOrderType] = useState(1)
    const [carToStoreTime,setCarToStoreTime] = useState(null)
    const limitDecimals = value=> {
        if(!value) return null
        if (typeof value === 'string') {
            return !isNaN(Number(value)) ? parseInt(value || 0) : 0
        } else if (typeof value === 'number') {
            return !isNaN(value) ? String(parseInt(value || 0)) : 0
        } else {
            return 0
        }
    };
    const handleOK = () => {
        form.validateFields().then(values => {
            if(values.payTime){
                values.payTime = values.payTime ? moment(values.payTime).format('YYYY-MM-DD') : ''
            }
            if(values.deliverTime){
                values.deliverTime = values.deliverTime ? moment(values.deliverTime).format('YYYY-MM-DD') : ''
            }
            if(values.carToStoreTime){
                values.carToStoreTime = values.carToStoreTime ? moment(values.carToStoreTime).format('YYYY-MM-DD') : ''
            }
            if(values.userTakeCarTime){
                values.userTakeCarTime = values.userTakeCarTime ? moment(values.userTakeCarTime).format('YYYY-MM-DD') : ''
            }
            let data = {
                ...values
            }
            console.log(data)
            setLoading(true)
            post(allUrl.orderTimeOut.OvertimeOrderAdd, { ...data }).then(res => {
                if (res && res.success) {
                    onCancel()
                    GetData()
                    message.success(res.msg)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        })
    }
    const disabledDate = (current) => {
        let value = form.getFieldValue('payTime')
        return value ? current && current < moment(value).endOf('day') : current
    }
    const payTimeChange = () => {
        form.resetFields(['deliverTime'])
    }
    const carToStoreTimeChange = (value) =>{
        setCarToStoreTime(value)
        if(!value) form.resetFields(['userTakeCarTime'])
    }
    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };

    return (
        <>
            <Modal
                visible={visible}
                title={title}
                onOk={() => handleOK()}
                onCancel={onCancel}
                maskClosable={false}
                centered={true}
                keyboard={false}
                confirmLoading={loading}
            >
                <Spin spinning={loading}>
                    <Form
                        {...layout}
                        // name="vertical"
                        form={form}
                    >
                        <Form.Item
                            label='订单类型'
                            name="subsidyType"
                            rules={[{ required: true, message: '请选择订单类型！' }]}
                            initialValue={1}
                        >
                            <Radio.Group onChange={(e) => setOrderType(e.target.value)}>
                                <Radio value={1}>返利补贴</Radio>
                                <Radio value={2}>其他</Radio>
                            </Radio.Group>
                        </Form.Item>
                        {
                            subsidyType == 1 ?
                                <>
                                    <Form.Item
                                        label='姓名'
                                        name="name"
                                        rules={[{ required: true, message: '请填写姓名！' }]}
                                    >
                                        <Input allowClear />
                                    </Form.Item>
                                    <Form.Item
                                        name="phone"
                                        label='手机号'
                                        rules={[
                                            {
                                                required: true,
                                                message: '请输入手机号！',
                                            },
                                            {
                                                validator(_, value) {
                                                    if (value && !/^[1][1,2,3,4,5,6,7,8,9][0-9]{9}$/.test(value)) {
                                                        return Promise.reject(new Error('请输入正确的手机号！'));
                                                    } else {
                                                        return Promise.resolve();
                                                    }
                                                },
                                            }
                                        ]}
                                    >
                                        <Input allowClear />
                                    </Form.Item>
                                    <Form.Item
                                        label='订单号'
                                        name="orderNumber"
                                        rules={[
                                            { required: true, message: '请填写订单号！' },
                                            {
                                                validator(_, value) {
                                                    if (value && !/^[a-zA-Z0-9]{19}$/.test(value)) {
                                                        return Promise.reject(new Error('订单号有19位的数字和字母组成'));
                                                    } else {
                                                        return Promise.resolve();
                                                    }
                                                },
                                            }
                                        ]}
                                    >
                                        <Input allowClear />
                                    </Form.Item>
                                    <Form.Item
                                        label='大定支付时间'
                                        name="payTime"
                                        rules={[{ required: true, message: '请填写大定支付时间！' }]}
                                    >
                                        <DatePicker format='YYYY-MM-DD' style={{ width: '100%' }} onChange={payTimeChange} />
                                    </Form.Item>
                                    <Form.Item
                                        label='预计交付时间'
                                        name="deliverTime"
                                        rules={[{ required: true, message: '请填写预计交付时间！' }]}
                                    >
                                        <DatePicker format='YYYY-MM-DD' style={{ width: '100%' }} disabledDate={disabledDate} />
                                    </Form.Item>
                                    <Form.Item
                                        label='车辆到店时间'
                                        name="carToStoreTime"
                                        rules={[{ required: false, message: '请填写车辆到店时间！' }]}
                                    >
                                        <DatePicker format='YYYY-MM-DD' style={{ width: '100%' }} onChange={carToStoreTimeChange} />
                                    </Form.Item>
                                    <Form.Item
                                        label='用户提车时间'
                                        name="userTakeCarTime"
                                        rules={[{ required: false, message: '请填写用户提车时间！' }]}
                                    >
                                        <DatePicker format='YYYY-MM-DD' style={{ width: '100%' }} disabled={carToStoreTime ? false :true} />
                                    </Form.Item>
                                </>
                                : <>
                                    <Form.Item
                                        label='类型名称'
                                        name="typeName"
                                        rules={[
                                            { required: true, message: '请填写类型名称！' },
                                            {
                                                validator(_, value) {
                                                    if(value &&value.length>20){
                                                        return Promise.reject(new Error('类型名称最多20个字符！'));
                                                    }else {
                                                        return Promise.resolve();
                                                    }
                                                },
                                            }]}
                                    >
                                        <Input allowClear />
                                    </Form.Item>
                                    <Form.Item
                                        label='姓名'
                                        name="name"
                                        rules={[{ required: true, message: '请填写姓名！' },
                                        {
                                            validator(_, value) {
                                                if(value && value.length>20){
                                                    return Promise.reject(new Error('姓名最多20个字符！'));
                                                }else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }
                                        ]}
                                    >
                                        <Input allowClear />
                                    </Form.Item>
                                    <Form.Item
                                        name="phone"
                                        label='手机号'
                                        rules={[
                                            {
                                                required: true,
                                                message: '请输入手机号！',
                                            },
                                            {
                                                validator(_, value) {
                                                    if (value && !/^[1][1,2,3,4,5,6,7,8,9][0-9]{9}$/.test(value)) {
                                                        return Promise.reject(new Error('请输入正确的手机号！'));
                                                    } else {
                                                        return Promise.resolve();
                                                    }
                                                },
                                            }
                                        ]}
                                    >
                                        <Input allowClear />
                                    </Form.Item>
                                    <Form.Item
                                        label='订单号'
                                        name="orderNumber"
                                        rules={[
                                            { required: true, message: '请填写订单号！' },
                                            {
                                                validator(_, value) {
                                                    if (value && !/^[a-zA-Z0-9]{19}$/.test(value)) {
                                                        return Promise.reject(new Error('订单号有19位的数字和字母组成'));
                                                    } else {
                                                        return Promise.resolve();
                                                    }
                                                },
                                            }
                                        ]}
                                    >
                                        <Input allowClear />
                                    </Form.Item>
                                    <Form.Item
                                        label='补贴金额(元)'
                                        name="rebateAmount"
                                        rules={[
                                            { required: true, message: '请填写补贴金额！' },
                                        ]}
                                    >
                                        <InputNumber 
                                            allowClear 
                                            step={1} 
                                            stringMode style={{width:'100%'}}
                                            formatter={limitDecimals}
                                            parser={limitDecimals}
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        label='是否可提现'
                                        name="orderState"
                                        rules={[
                                            { required: true, message: '请选择是否可提现！' },
                                        ]}
                                    >
                                        <Radio.Group>
                                            <Radio value={4}>可提现</Radio>
                                            <Radio value={8}>不可提现</Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                </>
                        }
                    </Form>
                </Spin>
            </Modal>
        </>
    )
}
export default AddFlowInfo