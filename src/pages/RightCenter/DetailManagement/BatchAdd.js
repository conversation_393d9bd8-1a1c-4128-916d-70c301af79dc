import React, { useEffect, useState, useRef, useReducer } from 'react'
import { Modal, Table } from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import allUrl from '@/utils/url'
import { columnsPart } from '@/utils/columns'
// import './BatchAdd.less'
import _ from 'lodash'
import { post, get } from '@/utils/request'
import { getWidth, offsetLeft } from '@/utils/index'
import styles from '../antd.module.less'
import publicQueryStyle from '../tableLayout.module.less'

function reducer(state, action) {
    switch (action.type) {
        case 'add': {

            let rows = _.cloneDeep(state.rows)
            let idChecked = _.cloneDeep(state.idChecked)
            let checked = _.cloneDeep(state.checked)

            rows.push(action.payload.row)
            idChecked.push(action.payload.row.key)
            checked.push(action.payload.row.key)

            return {
                ...state,
                ...{
                    rows,
                    idChecked,
                    checked
                }
            }
        }
        case 'remove': {
            let { row } = action.payload

            let rows = _.cloneDeep(state.rows)
            let idChecked = _.cloneDeep(state.idChecked)
            let checked = _.cloneDeep(state.checked)

            let deleteIndex = checked.indexOf(row.key)
            checked.splice(deleteIndex, 1)
            idChecked.splice(deleteIndex, 1)
            rows.splice(deleteIndex, 1)

            return {
                ...state,
                ...{
                    rows,
                    idChecked,
                    checked
                }
            }
        }
        case 'addAll': {
            let { row } = action.payload
            let rows = _.cloneDeep(state.rows)
            let idChecked = _.cloneDeep(state.idChecked)
            let checked = _.cloneDeep(state.checked)


            row.map(item => {
                if (checked.indexOf(item.key) === -1) {
                    rows.push(item)
                    idChecked.push(item.key)
                    checked.push(item.key)
                }
            })

            return {
                ...state,
                ...{
                    rows,
                    idChecked,
                    checked
                }
            }
        }
        case 'removeAll': {
            let { row } = action.payload
            let rows = _.cloneDeep(state.rows)
            let idChecked = _.cloneDeep(state.idChecked)
            let checked = _.cloneDeep(state.checked)

            row.map(item => {
                let deleteIndex = checked.indexOf(item.key)
                if (deleteIndex !== -1) {
                    checked.splice(deleteIndex, 1)
                    idChecked.splice(deleteIndex, 1)
                    rows.splice(deleteIndex, 1)
                }
            })

            return {
                ...state,
                ...{
                    rows,
                    idChecked,
                    checked
                }
            }
        }
        case 'update': {
            let { rows, idChecked, checked } = action.payload
            return {
                rows,
                idChecked,
                checked
            }
        }
        default:
            return state;
    }
}
//配件包修类型枚举值
const PART_TYPE_OPTIONS = [
    { name: '全部', value: null },
    { name: '整车件', value: 1 },
    { name: '核心零配件', value: 2 },
    { name: '易损易耗件', value: 3 },
    { name: '不保件', value: 4 }
]
const BatchAdd = (props) => {
    const tableRef = useRef()
    const [state, dispatch] = useReducer(reducer, { idChecked: props.state.idChecked, rows: props.state.rows, checked: props.state.checked })
    const [dataSource, setDataSource] = useState([])
    const [defaultQuery, setDefaultQuery] = useState({})
    const [pageSize, setPageSize] = useState(10)
    const [current, setCurrent] = useState(1)
    const [total, setTotal] = useState(0)
    const [loading, setLoading] = useState(false)
    const [DictData, setDictData] = useState([]) //车型


    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
    }

    const onCancel = () => {
        props.batchAddHandle(false)
    }

    const onSearch = (values) => {
        console.log(values);

        PageChange(1, 10)
        setDefaultQuery(values)
    }

    const onOk = () => {
        props.batchAddSubmit(state)
    }

    useEffect(() => {
        if (props.showBatchAdd) {
            let { rows, idChecked, checked } = props.state
            dispatch({ type: 'update', payload: { rows, idChecked, checked } })
        }
    }, [props.showBatchAdd])
    useEffect(() => {
        get(allUrl.common.entryLists, { codes: 'equity_car_type' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt['equity_car_type'].forEach(item => {
                    item.name = item.entryMeaning
                    item.value = item.entryMeaning
                })
                setDictData(Dt['equity_car_type'])
            } else {
                // message.error(res.message)
            }
        })
    }, [])
    const searchList = [
        { label: '配件名称', name: 'partName', type: 'Input', placeholder: '请输入配件名称', colSpan: 6 },
        { label: '配件编码', name: 'partNo', type: 'Input', placeholder: '请输入配件编码', colSpan: 6 },
        { label: '车型', name: 'carType', type: 'Select', placeholder: '请选择', colSpan: 6, data: DictData || [] },
        { label: '配件包修类型', name: 'partType', type: 'Select', colSpan: 6, data: PART_TYPE_OPTIONS },
    ]

    const onSelect = (record, selected, selectedRows, nativeEvent) => {
        dispatch({ type: selected ? 'add' : 'remove', payload: { row: record } })
    }
    const onSelectAll = (selected, selectedRows, changeRows) => {
        dispatch({ type: selected ? 'addAll' : 'removeAll', payload: { row: changeRows } })
    }

    useEffect(() => {
        if (props.showBatchAdd) {
            setLoading(true)
            getTableData()
        }
    }, [current, pageSize, defaultQuery, props.showBatchAdd])

    const getTableData = async () => {
        let params = { pageSize, pageNum: current, ...defaultQuery }
        const url = props.scene === 2 ? allUrl.DetailedManageInterests.queryExtendDetailPartList : allUrl.DetailedManageInterests.queryDetailPartList
        post(url, params).then(res => {
            if (res.success) {
                if (res.resp) {
                    res.resp.map((item, index) => item.key = item.carType + item.partNo + item.partVersion)
                    setDataSource(res.resp)
                } else {
                    setDataSource([])
                }
                setTotal(res.total)
                setLoading(false)
            } else {
            }
        })
    }

    const pagination = {
        pageSize: pageSize,
        onChange: PageChange,
        current: current,
        total: total,
        showTotal: () => `共${total}条，${pageSize}条/页`,
        showSizeChanger: true,
        showQuickJumper: true,
        onShowSizeChange: PageChange,
    }
    const rowSelection = {
        selectedRowKeys: state.checked,
        onSelect,
        onSelectAll,
    }
    return (
        <Modal
            width={getWidth()} style={{ left: offsetLeft() }}
            // width={1300}
            visible={props.showBatchAdd}
            onCancel={onCancel}
            title={<>权益配件关联<span style={{ color: 'red', marginLeft: '10px', fontSize: '10px' }}>{props.scene === 2 ? '需选上所有车型的相关配件' : '整车、三包、三电、增程器无需额外配置，除此之外其他配件与权益相关在此进行关联，如无此配件请及时联系管理员进行维护。'}</span></>}
            onOk={onOk}
        >
            <div className={`batch-add ${publicQueryStyle.PublicList}`}>
                <PublicTableQuery isCatch={true} defaultQuery={defaultQuery} isFormDown={false} onSearch={onSearch} searchList={searchList} />

                <Table
                    bordered
                    className={styles['blue-table']}
                    rowKey={'key'}
                    dataSource={dataSource}
                    loading={loading}
                    ref={tableRef}
                    scroll={{ x: 1300 }}
                    columns={columnsPart}
                    rowSelection={rowSelection}
                    pagination={pagination}>
                </Table>

            </div>
        </Modal>
    )
}
export default BatchAdd