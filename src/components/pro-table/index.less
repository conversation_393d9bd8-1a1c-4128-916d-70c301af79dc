.pro-table {
  display: flex;
  flex-direction: column;
  row-gap: 24px;

  &-filter {
    padding: 24px;
    padding-bottom: 0;
    background-color: #fff;

    &-sticky {
      position: sticky;
      top: 0;
      z-index: 9;
    }
  }

  &-toolbar {
    padding: 0 24px;
  }

  &-content {
    background: #f0f2f5;
    padding: 24px;
    padding-top: 0;
  }

  &-head {
    margin-bottom: 24px;
  }

  &-title {
    font-size: 20px;
    font-weight: 500;
    text-align: left;
    color: rgb(0 0 0 / 85%);
    line-height: 28px;
  }

  &-body {
    padding: 24px;
    background: #fff;
  }
}
