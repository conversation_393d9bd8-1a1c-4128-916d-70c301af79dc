import React,{useEffect, useState} from 'react'
import {Modal,Table,message} from 'antd'
import {utilsDict} from '../../../../utils/utilsDict'
import { connect } from 'react-redux'
import { get } from '../../../../utils/request'
import allUrl from '../../../../utils/url'
import './PostStandard.less'


const dataSource =[
    {modular:'交付',postName:'总经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'交付',postName:'交付经理',S:'1',A:'1',B:'1',C:'1',D:'1',B1:'1',C1:'1',D1:'1',remarks:''},
    {modular:'交付',postName:'交付专员A、交付专员B',S:'6',A:'5',B:'4',C:'3',B1:'3',C1:'2',D1:'2',D:'2',remarks:''},
    {modular:'交付',postName:'交付保障专员',S:'2',A:'2',B:'2',C:'兼职（非交付侧人员）',B1:'兼职',C1:'兼职',D1:'兼职',D:'1',remarks:''},
    {modular:'交付',postName:'金融保险专员',S:'1',A:'1',B:'1',C:'1',D:'1',B1:'兼职',C1:'兼职',D1:'兼职',remarks:''},
    {modular:'服务',postName:'服务经理',S:'1', remarks:''}, 
    {modular:'服务',postName:'服务顾问',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''}, 
    {modular:'服务',postName:'机电技师',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'钣金技师',S:'1', remarks:''}, 
    {modular:'服务',postName:'喷漆技师',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''}, 
    {modular:'服务',postName:'技术专家',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'保修专员',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'配件主管',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'用户关爱经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'合计',S:'20',A:'19',B:'18',C:'14',D:'13',B1:'14',C1:'13',D1:'13',remarks:''},  
]
dataSource.map((item,index)=>item.key=index+1)

const PostStandardDown = (props) =>{
    const {visible,onCancel,userInfo} = props
    const [PostStandardInfo,setPostStandardInfo] = useState({})
    const [InforData4,setInforData4] = useState({
        rowKey: record => record.key,
        bordered: true,
        size:'small',
        dataSource,
        scroll: { x: 'max-content' },
        columns: [
            { title: '岗位名称', dataIndex: 'modular',colSpan: 2,render:(text,record,index)=>{
                const obj = {
                    children: <div style={{textAlign:'center'}}>{text}</div>,
                    props: {
                        rowSpan:1,
                        colSpan:1
                    },
                };
                if(index === 0 || index === 14){
                    obj.props.colSpan = 0
                }
                if(index === 1){
                    obj.props.rowSpan = 4
                }
                if(index > 1 && index < 5){
                    obj.props.rowSpan = 0
                }
                if(index === 5){
                    obj.props.rowSpan = 9
                }
                if(index > 5 && index < 14){
                    obj.props.rowSpan = 0
                }
                return obj
            },},
            { title: '岗位名称2', dataIndex: 'postName',colSpan: 0,render:(text,record,index)=>{
                const obj = {
                    children: <div style={{textAlign:'center'}}>{text}</div>,
                    props: {
                        rowSpan:1,
                        colSpan:1
                    },
                };
                if(index === 0 || index === 14){
                    obj.props.colSpan = 2
                    obj.children=<div style={{paddingLeft:'250px'}}>{text}</div>
                }
                return obj
             } ,onCell(record, rowIndex) {
               
                
            }},
            { 
                title: <div>
                <p>战略城市/重点城市</p>
            </div>, 
            colSpan: 4,
            dataIndex: 'configurationRules',children:[
                { title: 'S', dataIndex: 'S',
                render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 11
                    }
                    return obj
                }},
                { title: 'A', dataIndex: 'A' , render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0                    
                    }
                    return obj
                }},
                { title: 'B', dataIndex: 'B', render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }

                    return obj
                }},
                { title: 'C', dataIndex: 'C',colSpan:1, render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }
                    if(index === 3){
                        obj.props.rowSpan = 2

                    }
                    if(index === 4){
                        obj.props.rowSpan = 0

                    }
                    return obj
                }}
            ]},
            { 
                title: <div>
                <p>次重点城市</p>
            </div>, 
            colSpan: 3,
            dataIndex: 'configurationRules',children:[
                
                { title: 'A', dataIndex: 'B1' , render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }
                    
                    return obj
                }},
                { title: 'B', dataIndex: 'C1', render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }
                    
                    return obj
                }},
                { title: 'C', dataIndex: 'C1', render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }
                   
                    return obj
                }},       
            ]},
            { 
                title: <div>
                <p>其他城市</p>
            </div>, 
            colSpan: 4,
            dataIndex: 'configurationRules',children:[
                { title: 'B', dataIndex: 'C1', render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }

                    return obj
                }},
                { title: 'C', dataIndex: 'C1', render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }
                    
                    return obj
                }},
                { title: 'D', dataIndex: 'D1' , render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(index === 0 || (index > 4 &&  index < 14)){
                        obj.props.colSpan = 0
                    }
                    
                    return obj
                }},
                // { title: '', dataIndex: 'F' , render:(text,record,index)=>{
                //     const obj = {
                //         children: <div style={{textAlign:'center'}}>{text}</div>,
                //         props: {
                //             colSpan:1,
                //         },
                //     };
                   
                //     if(index === 0 || (index > 4 &&  index < 14)){
                //         obj.props.colSpan = 0
                //     }
                    
                //     return obj
                // }},
                
                
            ]},
        ],
        pagination: false,
    })
    const handleOk = () =>{

    }

    const getlistByOrganizationIds = () =>{
        if(userInfo && userInfo.organizationIds && userInfo.organizationIds.length){
            get(allUrl.StoreManage.listByOrganizationIds,{organizationIds:userInfo?userInfo.organizationIds.join(','):''}).then(res=>{
                if(res && res.success){
                    let Dt = res.resp?res.resp[0]:{}
                    // Dt.dealerType = 1
                    // let newInforData1 = {...InforData2}
                    // let newInforData2 = {...InforData3}
                    // let target1 = newInforData1.dataSource.filter(item=>item.grade === Dt.level)
                    // let target2 = newInforData2.dataSource.filter(item=>item.grade === Dt.level)
                    // newInforData1.dataSource = target1
                    // newInforData2.dataSource = target2
                    // setInforData2(newInforData1)
                    // setInforData3(newInforData2)
                    setPostStandardInfo(Dt)
                }else{
                    // message.error(res.msg)
                }
            })
        }else{
            setPostStandardInfo({})
        }
    }

    useEffect(()=>{
        getlistByOrganizationIds()
    },[userInfo])

    return <div>
        <Table {...InforData4} />
    </div>
}
export default connect(({ common }) => {
    const {userInfo } = common
    return {
        userInfo
    }
})(PostStandardDown)