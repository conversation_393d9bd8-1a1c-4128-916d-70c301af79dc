import React,{useEffect, useState} from 'react'
import {Modal} from 'antd'

const PostStandardImg = (props) => {
    const {visible,onCancel} = props

    return <Modal title={'岗位和人员标准'} wrapClassName='PostStandardImg' open={visible} onCancel={onCancel} width={1300} footer={false} maskClosable={true}>
    {<>
       <img style={{width: '100%', display: 'table-cell', margin: '0 auto', marginBottom: 0}} src={require('@/assets/img/p1.jpg')} alt="" />
       {/* <img style={{width: '100%', marginBottom: 20}} src={require('@/assets/img/position2.jpg')} alt="" /> */}
       {/* <img style={{width: '100%', marginBottom: 20}} src={require('@/assets/img/position3.jpg')} alt="" /> */}
    </>
    }
</Modal>
}

export default PostStandardImg