import React, { useEffect, useState } from 'react'
import { message, But<PERSON>, Card, Table, Divider, Form, Row, Col, Timeline, Spin } from 'antd'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get } from '@/utils/request'
import allUrl from '@/utils/url'
import { useSelector } from 'react-redux'
import { DownOutlined } from '@ant-design/icons';
import '../index.less'
import './Detail.less'
const CarrierDetail = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [id, setId] = useState(null)
    const [locationParmas, setLocationParmas] = useState({})
    const [Type, setType] = useState('')
    const [dataSource, setDataSource] = useState([])
    const [formData, setFormData] = useState(null)
    const [clockList, setClockList] = useState([])
    const [isExpand, setIsExpand] = useState(false)
    const [loading, setLoading] = useState(false)

    const onCancel = () => {
        history.push('/truckLogistics/CarrierList')
    }
    const getData = (params) => {
        let query = { id: params.id }
        setLoading(true)
        get(allUrl.truckLogistics.getCarriageObj, { ...query }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setFormData(Dt)
                setDataSource(Dt.carList)
                setClockList(Dt.clockList?Dt.clockList:[])
                if (Dt.clockList && Dt.clockList.length >= 5) {
                    setIsExpand(true)
                } else {
                    setIsExpand(false)
                }
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }

    useEffect(() => {
        const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}
        setLocationParmas(locationParmas)
        setId(locationParmas.id)
        setType(locationParmas.Type)
        console.log(locationParmas)
        if (locationParmas.id && userInfo) {
            getData(locationParmas)
        } else {


        }
    }, [userInfo])

    const InforData = {
        rowKey: record => record.key,
        bordered: true,
        dataSource,
        scroll: { x: 'max-content' },
        columns: [
            { title: '序号', dataIndex: 'index', width: 80, render: (text, record, index) => index + 1 },
            { title: '车架号(VIN)', dataIndex: 'vin', width: 180 },
            { title: '大定订单号', dataIndex: 'orderNo', width: 180 },
            { title: '整车物流', dataIndex: 'vehicleMaterial', width: 200 },
            { title: '物流描述', dataIndex: 'itemDescription', width: 200 },
            { title: '基本车型', dataIndex: 'baseModel', width: 180 },
            { title: '推荐人', dataIndex: 'referrer', width: 100 },
            { title: '推荐人手机号', dataIndex: 'referrerPhone', width: 120 },
        ],
        pagination: false,
        rowSelection: null,
    };
    return <div className='PublicDetail CarrierDetail'>
        <div className='DetailBox'>
            <div className='DetailTitle'>{locationParmas.title}</div>
            <div className='DetailCon'>
                <div className='CarrierDetail_Box'>
                    <Card bordered={false}>
                        <div className='CarrierDetail_Con'>
                            <Spin spinning={loading}>
                                <Form>
                                    <div className='CarrierDetail_Info_Title'>承运信息</div>
                                    <div className='CarrierDetail_Info_Con'>
                                        <Row>
                                            <Col span={8}><Form.Item label='承运单编号'>{formData ? formData.carriageNo : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='承运路线'>{formData ? formData.carriageRoute : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='经销商编号'>{formData ? formData.dealer : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='承运板车'>{formData ? formData.carrierPallet : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='发车地址'>{formData ? formData.departureAddress : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='目的地厅店'>{formData ? formData.destination : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='板车司机'>{formData ? formData.driverName : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='经销商收货地址'>{formData ? formData.dealerAddress : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='厅店仓库'>{formData ? formData.dealerWarehouse : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='司机手机号'>{formData ? formData.driverPhone : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='承运台数'>{formData ? formData.carriageNum : ''}</Form.Item></Col>
                                            <Col span={8}><Form.Item label='厅店仓库编号'>{formData ? formData.dealerWarehouseNo : ''}</Form.Item></Col>
                                        </Row>
                                    </div>
                                    <Divider />
                                    <div className='CarrierDetail_Info_Title'>车辆信息</div>
                                    <Table {...InforData} />
                                    <div className='CarrierDetail_Info_Title' style={{ margin: '32px 0 20px 0' }}>位置打卡记录</div>
                                    <div className='PunchInRecord'>
                                        <Timeline>
                                            {
                                                clockList.map((item, index) => {
                                                    return (isExpand ? index < 5 : index >= 0)
                                                        && <Timeline.Item key={index} color={index === 0 ? 'blue' : 'gray'}>
                                                            <div className='TimelineItem_Con'>
                                                                <p className='time'>{item.time}</p>
                                                                <p className='address'>位置：{item.address}</p>
                                                                <p className='statusCn'>车辆运输状态：{item.statusCn}</p>
                                                            </div>
                                                        </Timeline.Item>
                                                })
                                            }
                                        </Timeline>
                                            <p className='more'>
                                                {isExpand ?
                                                    <span onClick={() => {
                                                        setIsExpand(false)
                                                    }}>点击查看更多<DownOutlined /></span>
                                                    : <span>没有更多打卡记录啦～</span>
                                                }
                                            </p>
                                    </div>
                                </Form>
                            </Spin>
                        </div>
                    </Card>
                </div>
            </div>
        </div>
        <div className='DetailBtns'>
            <Button onClick={onCancel}>取消</Button>
        </div>

    </div>
}
export default CarrierDetail