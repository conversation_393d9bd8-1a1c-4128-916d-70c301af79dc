import {
  getSeresFileOrganizationSeresDelete,
  getSeresFileUserSeresCountOrg,
  postSeresFileOrganizationSeresPage,
} from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import ProTable, { ProTableActions } from '@/components/pro-table';
import UserPermissionAuthorize from '@/components/user-permission-authorize';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { divisionPermissions } from '@/permissions/file';
import { PlusOutlined } from '@ant-design/icons';
import { message, Modal, Space, Typography } from 'antd';
import React, { FC, useCallback, useRef, useState } from 'react';
import DivisionModal, { DivisionModalProps } from './division-modal';

const Division: FC = () => {
  const tableRequest = usePaginationTableListStyleRequest(({ current: pageNum, pageSize, params }) => {
    return postSeresFileOrganizationSeresPage({ ...params, pageNum, pageSize });
  });

  const actionRef = useRef<ProTableActions>(null);
  const handleDeleteClick = useCallback(async (data: FileManage.SeresOrganization) => {
    try {
      const [num = 0] = await getSeresFileUserSeresCountOrg({ orgId: data.id as unknown as number });
      Modal.confirm({
        title: '删除总部部门',
        content: `您正在删除【${data.name}】，已关联${num}名总部人员，删除后，该部分人员将缺失部门信息，且无法查看该部门已关联的文件`,
        onOk: async () => {
          try {
            await getSeresFileOrganizationSeresDelete({ id: data.id as unknown as number });
            actionRef.current?.reload();
            message.success('删除成功');
          } catch (error) {}
        },
        okText: '确认删除',
        okButtonProps: {
          children: '确认删除',
          type: 'primary',
          danger: true,
        },
      });
    } catch (error) {}
  }, []);

  const handleEditClick = useCallback((data: FileManage.SeresOrganization) => {
    setDivisionModal({ open: true, data });
  }, []);

  const [divisionModal, setDivisionModal] = useState<DivisionModalProps>({ open: false });
  const handleAddClick = useCallback(() => {
    setDivisionModal({ open: true });
  }, []);

  const handleSave = useCallback(() => {
    setDivisionModal({ open: false });
    actionRef.current?.reload();
  }, []);

  return (
    <PageContainer>
      <ProTable<FileManage.SeresOrganization>
        request={tableRequest}
        rowKey="id"
        actionRef={actionRef}
        actions={[
          {
            type: 'primary',
            children: '添加总部部门',
            icon: <PlusOutlined />,
            onClick: handleAddClick,
            permission: divisionPermissions.add,
          },
        ]}
        columns={[
          {
            title: '序号',
            dataIndex: 'index',
            valueType: 'index',
          },
          {
            title: '部门',
            dataIndex: 'name',
          },
          {
            title: '操作',
            render: (_, record) => {
              return (
                <Space>
                  <UserPermissionAuthorize {...divisionPermissions.edit}>
                    <Typography.Link onClick={() => handleEditClick(record)}>编辑</Typography.Link>
                  </UserPermissionAuthorize>
                  <UserPermissionAuthorize {...divisionPermissions.delete}>
                    <Typography.Link type="danger" onClick={() => handleDeleteClick(record)}>
                      删除
                    </Typography.Link>
                  </UserPermissionAuthorize>
                </Space>
              );
            },
          },
        ]}
      ></ProTable>
      <DivisionModal
        {...divisionModal}
        onSave={handleSave}
        onCancel={() => {
          setDivisionModal({ open: false });
        }}
      />
    </PageContainer>
  );
};

export default Division;
