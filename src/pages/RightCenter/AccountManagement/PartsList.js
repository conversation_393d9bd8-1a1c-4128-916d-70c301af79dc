import React, { useState, memo, useEffect, useCallback } from 'react';

import { useSelector } from 'react-redux';
import { Table, Button, Modal, Descriptions, Row, Col, Spin, Tabs, Divider } from 'antd';
import moment from 'moment';
import { DecryptByAES } from '@/components/Public/Decrypt';
import PublicTooltip from '@/components/Public/PublicTooltip';
import allUrl from '../../../utils/url';
import { get } from '../../../utils/request';
import styles from '../antd.module.less';

const UPDATE_TYPE = {
  1: '自动',
  2: '手动',
};

const PartsList = props => {
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const { partsDataSource } = props;
  const [open, setOpen] = useState(false);
  const [detail, setDetail] = useState({});
  const getYearUnitName = key => {
    return (
      {
        year: '年',
        month: '月',
        day: '日',
      }[key] || ''
    );
  };
  const layout = {
    labelCol: { span: 14 },
    wrapperCol: { span: 14 },
  };
  const openDetail = record => {
    setOpen(true);
    // getAccountPartDetail
    get(allUrl.AccountManagement.getAccountPartDetail, { id: record.vinPartRelationId }).then(res => {
      const { success, resp } = res;
      if (success) {
        setDetail(resp[0]);
      }
    });
  };

  return (
    <>
      <Table
        className={`${styles['gray-table']} parts-no-pad`}
        dataSource={partsDataSource}
        // rowKey={record => record.formalOrderNo.toString()}
        loading={props.loading || false}
        size="small"
        bordered={true}
        pagination={false}
        scroll={{ x: 3140 }}
      >
        <Table.Column
          title="订单编号"
          key="formalOrderNo"
          dataIndex="formalOrderNo"
          width={120}
          fixed="left"
          render={text => text || '-'}
        />
        <Table.Column
          title="配件名称"
          key="partName"
          dataIndex="partName"
          width={120}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column title="配件编码" key="partNo" dataIndex="partNo" render={text => text || '-'} width={120} />
        <Table.Column
          title="配件包修类型"
          key="partTypeName"
          dataIndex="partTypeName"
          width={70}
          render={text => text || '-'}
        />
        <Table.Column
          title="配件包修所属"
          key="partBelongsName"
          dataIndex="partBelongsName"
          width={70}
          render={text => text || '-'}
        />
        <Table.Column
          title="保修情况"
          key="partModeName"
          dataIndex="partModeName"
          width={60}
          render={text => text || '-'}
        />
        <Table.Column
          title="保修开始时间"
          key="partBeginTime"
          dataIndex="partBeginTime"
          width={120}
          render={text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')}
        />
        <Table.Column
          title="保修结束时间"
          key="partEndTime"
          dataIndex="partEndTime"
          width={120}
          render={text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')}
        />
        <Table.Column
          title="保修开始里程"
          key="partMileageBegin"
          dataIndex="partMileageBegin"
          width={120}
          render={text => (text == null ? '-' : text)}
        />
        <Table.Column
          title="保修结束里程"
          key="partMileageEnd"
          dataIndex="partMileageEnd"
          width={120}
          render={text => (text == null ? '-' : text)}
        />
        <Table.Column title="状态" key="effectName" dataIndex="effectName" width={50} render={text => text || '-'} />
        <Table.Column
          title="配件更新时间"
          key="refreshTime"
          dataIndex="refreshTime"
          width={120}
          render={text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')}
        />
        <Table.Column
          title="配件更新版本"
          key="refreshVersion"
          dataIndex="refreshVersion"
          width={120}
          render={text => text ?? '-'}
        />
        <Table.Column
          title="更新方式"
          key="refreshType"
          dataIndex="refreshType"
          width={120}
          render={text => (text ? UPDATE_TYPE[text] : '-')}
        />
        <Table.Column
          title="权益明细编码"
          key="detailNo"
          dataIndex="detailNo"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益明细名称"
          key="detailName"
          dataIndex="detailName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="业务编码"
          key="businessCode"
          dataIndex="businessCode"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          fixed="right"
          title="操作"
          width={50}
          ellipsis
          render={(text, record) => {
            return (
              <Button
                type="link"
                size="small"
                onClick={() => {
                  openDetail(record);
                }}
              >
                详情
              </Button>
            );
          }}
        />
      </Table>

      {/* 详情弹窗 */}
      <Modal
        title="配件详情"
        centered
        open={open}
        width={800}
        onCancel={() => setOpen(false)}
        footer={[
          <Button key="back" onClick={() => setOpen(false)}>
            返回
          </Button>,
        ]}
      >
        <Descriptions
          column={1}
          layout={layout}
          contentStyle={{ width: '60%', display: 'block' }}
          labelStyle={{ paddingLeft: '60px' }}
        >
          <Descriptions.Item label="配件名称">{detail.partName || '-'}</Descriptions.Item>
          <Descriptions.Item label="配件编码">{detail.partNo || '-'}</Descriptions.Item>
          <Descriptions.Item label="配件包修类型">{detail.partTypeName || '-'}</Descriptions.Item>
          <Descriptions.Item label="配件包修所属">{detail.partBelongsName || '-'}</Descriptions.Item>
          <Descriptions.Item label="保修情况">
            {detail.partMode === 1 ? '保内' : detail.partMode === 2 ? '保外' : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="保修开始时间">
            {detail.partBeginTime ? moment(detail.partBeginTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="保修结束时间">
            {detail.partEndTime ? moment(detail.partEndTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="保修开始里程">
            {detail.partMileageBegin == null ? '-' : detail.partMileageBegin}
          </Descriptions.Item>
          <Descriptions.Item label="保修结束里程">
            {detail.partMileageEnd == null ? '-' : detail.partMileageEnd}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            {detail.effect === 2 ? '生效' : detail.effect === 4 ? '失效' : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="权益明细编码">{detail.detailNo || '-'}</Descriptions.Item>
          <Descriptions.Item label="权益明细名称">{detail.detailName || '-'}</Descriptions.Item>
          <Descriptions.Item label="业务编码">{detail.businessCode || '-'}</Descriptions.Item>
          <Descriptions.Item label="关联时间">
            {detail.relationTime ? moment(detail.relationTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="关联批次">{detail.partVersion || '-'}</Descriptions.Item>
          <Descriptions.Item label="配件更新时间">
            {detail.refreshTime ? moment(detail.refreshTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="配件更新版本">{detail.refreshVersion || '-'}</Descriptions.Item>
          <Descriptions.Item label="更新方式">
            {detail.refreshType ? UPDATE_TYPE[detail.refreshType] : '-'}
          </Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  );
};
export default memo(PartsList);
