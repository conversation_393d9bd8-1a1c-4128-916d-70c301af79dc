/**
 * 总部人员明细：文件未浏览人员明细、文件未下载人员明细
 */
import { getSeresFileOperateSeresUserDetailExport, postSeresFileOperateInnerOptDetailQuery } from '@/apis/file-manage';
import ProTable from '@/components/pro-table';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { downloadBlob } from '@/utils/file';
import { Modal, ModalProps } from 'antd';
import React, { useCallback } from 'react';

export enum InnerUserModalType {
  unViewed = 'unViewed',
  unDownload = 'unDownload',
}
export const innerUserMap = {
  [InnerUserModalType.unViewed]: 1,
  [InnerUserModalType.unDownload]: 2,
};
export const titleMap = {
  [InnerUserModalType.unViewed]: '文件未浏览人员明细',
  [InnerUserModalType.unDownload]: '文件未下载人员明细',
};
export interface InnerUserModalProps extends ModalProps {
  type: InnerUserModalType;
  fileId: number;
}

const InnerUserModal: React.FC<InnerUserModalProps> = ({ type, fileId, ...props }) => {
  const tableRequest = usePaginationTableListStyleRequest(({ current: pageNum, pageSize, params }) => {
    return postSeresFileOperateInnerOptDetailQuery({
      ...params,
      pageNum,
      pageSize,
      queryType: innerUserMap[type],
      fileId,
    });
  });

  const handleExport = useCallback(async () => {
    try {
      const blob = await getSeresFileOperateSeresUserDetailExport(
        {
          downloadType: innerUserMap[type],
          fileId,
        },
        {
          responseType: 'blob',
        }
      );
      const filename = titleMap[type];
      downloadBlob(blob as unknown as Blob, `${filename}.xlsx`);
    } catch (error) {}
  }, [fileId, type]);

  return (
    <Modal maskClosable={false} width={860} title={titleMap[type]} {...props} footer={null}>
      <ProTable
        ghost
        request={tableRequest}
        rowKey={'userNo'}
        columns={[
          {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            valueType: 'index',
          },
          {
            title: '文件名',
            dataIndex: 'fileName',
          },
          {
            title: '姓名',
            dataIndex: 'userName',
          },
          {
            title: '工号',
            dataIndex: 'userNo',
          },
        ]}
        actions={[
          {
            children: '导出',
            type: 'primary',
            onClick: handleExport,
          },
        ]}
      />
    </Modal>
  );
};

export default InnerUserModal;
