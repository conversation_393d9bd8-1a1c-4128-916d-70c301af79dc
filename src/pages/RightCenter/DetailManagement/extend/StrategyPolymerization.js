import React, { useEffect, useState, memo, useContext } from 'react'
import { InjectContext } from '../CreateForm'
import StrategyFormStyle from './strategyForm.module.less'
import { Form, Input, InputNumber, Select, Button, Radio, Space, Modal, message, Table, Row, Col, DatePicker } from 'antd';
import { endTimeRenderPolymerization, mileageEndRenderPolymerization } from '../publicMethods'
import moment from 'moment'

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

const StrategyPolymerization = (props) => {

    const { form, usable, entryList, formData, ...rest } = useContext(InjectContext)
    const [limitYearRadioDisplay, setLimitYearRadioDisplay] = useState('') // 内外一致-权益限制年限
    const [timeRadioChangeDisplay, setTimeRadioChangeDisplay] = useState(1) // 内外一致-权益生效时间
    const [endTimeStr, setEndTimeStr] = useState()
    const [mileageEndStr, setMileageEndStr] = useState()
    const [limitLegendDisplay, setLimitLegendDisplay] = useState(0) // 对外-权益限制里程策略
    const [effectMileageFrameFlag, setEffectMileageFrameFlag] = useState(0)  //内外一致-权益生效里程
    const [relativeTimeList, setRelativeTimeList] = useState([])
    const [currentFormData, setCurrentFormData] = useState({})

    const { strategyConfig, originDetail } = formData

    const RELATIVE_MILEAGE_OPTIONS = [{
        label: '前权益的相对结束里程',
        remark: '前一个权益的相对开始里程+权益限制里程（基准）作为延保权益的开始里程',
        value: 12,
    }, {
        label: '前权益的实际结束里程',
        remark: '前一个权益因时间到期、里程超出、次数用完原因失效时刻的里程值',
        value: 11,
    }]

    const END_MILEAGE_OPTIONS = [{
        label: '计算到总里程和',
        value: 1,
    }, {
        label: '计算到里程策略',
        value: 2,
    }]

    useEffect(() => {
        setCurrentFormData(form.getFieldsValue())

        // 如果当前是编辑状态
        if (sessionStorage.getItem('createType') === 'edit') {
            const _data = rest.dataSource
            if (_data) {
                const _detailDisplay = _data.detailDisplay
                init(_detailDisplay)
                // form.setFieldsValue({
                //     detailDisplay: _detailDisplay
                // })

                endTimeChange(_detailDisplay.relativeTime)
                mileageEndStrFn()
                form.setFieldsValue({
                    detailDisplay: {
                        fixedRelativeTime: !_detailDisplay.relativeTime && _detailDisplay.fixedBeginTime && _detailDisplay.fixedEndTime ? [moment(_detailDisplay.fixedBeginTime, 'YYYY-MM-DD HH:mm:ss'), moment(_detailDisplay.fixedEndTime, 'YYYY-MM-DD HH:mm:ss')] : null,
                    }
                })
            }
        } else {
            // 根据前权益设置当前默认选项
            if (originDetail) {

                init(originDetail)

            }
        }

    }, [originDetail])

    const init = (detail) => {
        // console.log('detail', detail);
        // 权益限制年限
        limitYearRadioChange(detail['limitTimeFrame'])
        // 权益生效时间
        timeRadioChange(detail['relativeTime'] ? 0 : 1)
        // 权益限制里程
        mileageRadioChange(detail['limitLegendFrame'])

        //权益生效里程
        effectMileageFrameRadioChange(detail['effectMileageFrame'])

        // 若前权益明细、延保权益明细都选择相对时间，则选项有：前权益所选的相对开始时间（例如交付时间、开票时间等）、前权益的相对结束时间、前权益的实际结束时间；
        if (detail['relativeTime']) {
            const originRelativeTime = entryList['equity_relativeTime'].filter(i => i.value == originDetail.relativeTime)
            const currentRelativeTime = entryList['equity_relativeTime'].filter(i => i.businessScene == 2)
            setRelativeTimeList([...originRelativeTime, ...currentRelativeTime])
        }

        const { limitTimeFrame, limitLegendFrame } = detail
        form.setFieldsValue({
            detailDisplay: {
                limitTimeFrame,
                timeRadio: detail['relativeTime'] ? 0 : 1,
                limitLegendFrame
            }
        })
    }




    // 权益限制年限
    const limitYearRadioChange = (value) => {

        setLimitYearRadioDisplay(value)
        if (value === 0) {
            form.setFieldsValue({
                detailDisplay: {
                    timeRadio: 1
                }
            })
            timeRadioChange(1)
        } else {
            form.setFieldsValue({
                detailDisplay: {
                    timeRadio: 0
                }
            })
            timeRadioChange(0)
        }

    }

    // 权益生效时间
    const timeRadioChange = value => {
        setTimeRadioChangeDisplay(value)
        endTimeChange(form.getFieldValue(['detailDisplay', 'relativeTime']))
    }
    const endTimeChange = value => {
        const _currentFormData = form.getFieldsValue()
        const detailDisplay = _currentFormData.detailDisplay

        setEndTimeStr(endTimeRenderPolymerization({ value, originDetail, currentFormData: detailDisplay, entryList }))
    }

    // 权益限制里程策略
    const mileageRadioChange = value => {
        setLimitLegendDisplay(value)
        //v13版本需求：若权益限制里程选择无此策略，权益生效里程固定选择无此策略
        if (!value) {
            setEffectMileageFrameFlag(0)
            //重置权益生效里面的一些选项

            form.setFieldsValue({
                detailDisplay: {
                    effectMileageFrame: 0,
                    relativeBeginMileageStrategy: null,
                    relativeEndMileageStrategy: null,
                    fixedBeginMileage: null,
                    fixedEndMileageStrategy: null,
                }
            })
        }
    }

    //权益生效里程change
    const effectMileageFrameRadioChange = (val) => {
        setEffectMileageFrameFlag(val)
        if (val === 1) {
            // form.setFieldValue('relativeBeginMileageStrategy', null)
            // form.setFieldValue('relativeEndMileageStrategy', null)

            form.setFieldsValue({
                detailDisplay: {
                    relativeBeginMileageStrategy: null,
                    relativeEndMileageStrategy: null,
                }
            })
        } else if (val === 2) {
            // form.setFieldValue('fixedBeginMileage', null)
            // form.setFieldValue('fixedEndMileageStrategy', null)
            form.setFieldsValue({
                detailDisplay: {
                    fixedBeginMileage: null,
                    fixedEndMileageStrategy: null,
                    relativeBeginMileageStrategy: 13,   //默认选中前权益的开始里程
                }
            })
        }
        mileageEndStrFn()
    }

    const timeChange = () => {
        endTimeChange(form.getFieldValue(['detailDisplay', 'relativeTime']))
    }

    // 设置延保生效里程-结束里程
    const mileageEndStrFn = () => {
        let _detail
        const limitLegendFrame = form.getFieldValue(['detailDisplay', 'limitLegendFrame'])

        if (sessionStorage.getItem('createType') === 'edit') {
            _detail = rest.dataSource
        } else {
            _detail = originDetail
        }

        const currentFormData = limitLegendFrame ? form.getFieldsValue().detailDisplay : _detail.detailDisplay
        const str = mileageEndRenderPolymerization({ originDetail, currentFormData, entryList })

        setMileageEndStr(str)
    }

    return (
        <>
            <h4 className='s-title'>聚合权益信息</h4>
            <Form.Item label='聚合权益明细名称' name={['detailDisplay', 'name']} rules={[{ required: true }]}>
                <Input className='input' maxLength={100} placeholder='请填写标题，最多100个字，建议50字以内，不影响页面展示' />
            </Form.Item>
            <Form.Item label='聚合权益声明' name={['detailDisplay', 'statement']} help={<div style={{ color: 'red', fontSize: '12px', marginBottom: '20px', marginTop: '8px' }}>如配置在声明里的权益策略，例如：一次补胎、两次补漆等，不作为权益策略，无法进行核销</div>}>
                <TextArea className='input' placeholder='此处需要对外展示，建议描述时谨慎，例如：赛力斯为您所购买的赛力斯汽车提供整车及增程包修期为4年不限里程（以先到者为准，运营车辆除外），详见《保修及保养手册》质量担保明细' autoSize={{ minRows: 2, maxRows: 6 }} />
            </Form.Item>


            <h4 className='s-title'>聚合权益策略</h4>
            {/* 聚合权益限制年限 */}
            <Form.Item label='聚合权益限制年限' style={{ marginBottom: 0 }}>
                <Form.Item
                    name={['detailDisplay', 'limitTimeFrame']}
                    style={{ display: 'inline-block' }}
                    initialValue={0}
                >
                    <Radio.Group onChange={(e) => limitYearRadioChange(e.target.value)} disabled={strategyConfig?.limitTimeFrame?.disabled}>
                        <Radio value={0} disabled={strategyConfig?.limitTimeFrame?.childrenDisabled1}>无此策略 </Radio>
                        <Radio value={-1}>不限年数（延保为终身）</Radio>
                        <Radio value={1} >其他</Radio>
                    </Radio.Group>
                </Form.Item>
                <>
                    <Form.Item
                        name={['detailDisplay', 'limitYear']}
                        style={{ display: 'inline-block', width: '90px' }}
                        initialValue={0}
                    >
                        <InputNumber min={0} style={{ width: 90 }} disabled={limitYearRadioDisplay !== 1} onChange={timeChange} />
                    </Form.Item>
                    <span style={{ marginLeft: 10, marginRight: 10 }}>年</span>
                    <Form.Item
                        name={['detailDisplay', 'limitMonth']}
                        style={{ display: 'inline-block', width: '90px' }}
                        initialValue={0}
                    >
                        <InputNumber min={0} style={{ width: 90, lineHeight: '30px' }} disabled={limitYearRadioDisplay !== 1} onChange={timeChange} />
                    </Form.Item>
                    <span style={{ marginLeft: 10, marginRight: 10, lineHeight: '30px' }}>月</span>
                    <Form.Item
                        name={['detailDisplay', 'limitDay']}
                        style={{ display: 'inline-block', width: '90px' }}
                        initialValue={0}
                    >
                        <InputNumber min={0} style={{ width: 90 }} disabled={limitYearRadioDisplay !== 1} onChange={timeChange} />
                    </Form.Item>
                    <span style={{ marginLeft: 10, lineHeight: '30px' }}>日</span>
                </>
            </Form.Item>

            {/* 权益生效时间 */}
            <Form.Item label='聚合权益生效时间' rules={[{ required: false }]} style={{ marginBottom: 0 }} className='prefix-red'>
                <Row>
                    <Col span={3}>
                        {/* V13.0.4 权益限制年限选择“无此策略”，权益生效时间只可选择“固定时间”，权益限制年限选择“不限年限”或者“其他”，权益生效时间只可选择“相对时间” */}
                        <Form.Item
                            name={['detailDisplay', 'timeRadio']}
                            style={{ display: 'inline-block', width: '200px', marginTop: '6px' }}
                            initialValue={1}
                        >
                            <Radio.Group onChange={e => timeRadioChange(e.target.value)} >
                                <Space direction='vertical' size={20} >
                                    <Radio value={0} disabled={limitYearRadioDisplay == 0}>
                                        开始时间
                                        <div style={{ marginTop: endTimeStr ? '32px' : '26px' }}>结束时间 </div>
                                    </Radio>

                                    <Radio value={1} style={{ marginTop: '14px' }} disabled={limitYearRadioDisplay != 0}>固定时间</Radio>
                                </Space>
                            </Radio.Group>
                        </Form.Item>
                    </Col>
                    <Col>
                        <Space direction='vertical' align='center' size={16}>
                            <div style={{ position: 'relative' }}>
                                <Form.Item
                                    name={['detailDisplay', 'relativeTime']}
                                    style={{ display: 'inline-block', width: '400px', margin: '0' }}
                                    rules={[{ required: timeRadioChangeDisplay === 0, message: '请选择权益生效时间！' }]}
                                >
                                    <Select style={{ display: 'inline-block', width: '380px' }} allowClear disabled={timeRadioChangeDisplay !== 0} onChange={endTimeChange}>
                                        {
                                            relativeTimeList.map((item, index) => <Option key={index} value={Number(item.value)}>
                                                {item.name}
                                                {item.description ? <span className={StrategyFormStyle.remark}>{item.description}</span> : ''}
                                            </Option>)
                                        }
                                    </Select>
                                </Form.Item>
                            </div>
                            {/* 结束时间 */}
                            <div className='timeEndRadio polymerization'>
                                {endTimeStr || '-'}
                            </div>
                            <div style={{ position: 'relative', marginTop: '46px' }}>
                                <Form.Item
                                    name={['detailDisplay', 'fixedRelativeTime']}
                                    style={{ display: 'inline-block', width: '400px' }}
                                    rules={[{ required: timeRadioChangeDisplay === 1, message: '请选择权益生效时间！' }]}
                                >
                                    <RangePicker
                                        showTime
                                        style={{ width: '380px' }}
                                        placeholder={['权益开始时间', '权益结束时间']}
                                        disabled={timeRadioChangeDisplay !== 1}
                                    />
                                </Form.Item>
                                <span style={{ color: 'red', fontSize: 12, marginLeft: '10px', position: 'absolute', whiteSpace: 'nowrap', top: '8px' }}>若选择固定时间，权益只在固定时间范围内生效</span>
                            </div>
                        </Space>
                    </Col>
                </Row>
            </Form.Item>


            {/* 权益限制里程 */}
            {/* 前权益和延保权益都为无此策略，不展示此条 */}
            {(originDetail?.limitLegendFrame == 0 && currentFormData?.limitLegendFrame == 0) ? '' :
                <Form.Item label='聚合权益限制里程（基准）' rules={[{ required: false }]} style={{ marginBottom: 0 }}>
                    <Row>
                        <Col span={3}>
                            <Form.Item
                                name={['detailDisplay', 'limitLegendFrame']}
                                style={{ display: 'inline-block', width: '200px' }}
                                initialValue={0}
                            >
                                <Radio.Group onChange={e => mileageRadioChange(e.target.value)} className='limit-legend-radio'>
                                    <Space direction='vertical' align='start' size={24} >
                                        {strategyConfig?.limitLegendFrame?.currentShow === 0 ? <Radio value={0}>无此策略</Radio> : ''}
                                        {strategyConfig?.limitLegendFrame?.currentShow === 1 ? <Radio value={1}>行驶里程</Radio> : ''}
                                        {strategyConfig?.limitLegendFrame?.currentShow === 2 ? <Radio value={2}>增程器里程</Radio> : ''}
                                    </Space>
                                </Radio.Group>
                            </Form.Item>
                        </Col>
                        <Col span={15} style={{ width: 700 }}>
                            <Space direction='vertical' size={15}>
                                {strategyConfig?.limitLegendFrame?.currentShow === 0 ? <div style={{ height: 32 }}><span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap', lineHeight: '32px' }}>限制里程（基准）基于前权益生成，前权益配置无此策略，则聚合策略默认为无此策略，无法修改</span></div> : ''}
                                {strategyConfig?.limitLegendFrame?.currentShow === 1 ? <Space>
                                    <Form.Item
                                        name={['detailDisplay', 'limitLegend']}
                                        noStyle
                                    >
                                        <InputNumber min={1} placeholder='只能输入数字，例如：160000' style={{ display: 'inline-block', width: '250px' }} disabled={limitLegendDisplay !== 1} onChange={mileageEndStrFn} />
                                    </Form.Item>
                                    <span style={{ marginLeft: 6, whiteSpace: 'nowrap' }}>公里</span>
                                    <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap' }}>限制里程（基准）基于前权益生成，前权益配置行驶里程，则聚合策略默认为行驶里程，无法修改</span>
                                </Space> : ''}
                                {strategyConfig?.limitLegendFrame?.currentShow === 2 ? <Space>
                                    <Form.Item
                                        name={['detailDisplay', 'extenderMileage']}
                                        noStyle
                                    >
                                        <InputNumber min={1} placeholder='只能输入数字，例如：160000' style={{ width: '250px' }} disabled={limitLegendDisplay !== 2} onChange={mileageEndStrFn} />
                                    </Form.Item>
                                    <span style={{ marginLeft: 6, whiteSpace: 'nowrap' }}>公里</span>
                                    <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap' }}>限制里程（基准）基于前权益生成，前权益配置增程器里程，则聚合策略默认为增程器里程，无法修改</span>
                                </Space> : ''}
                            </Space>
                        </Col>
                    </Row>
                </Form.Item>
            }

            {/* 权益生效里程 */}
            {/* 前权益和延保权益都为无此策略，不展示此条 */}
            {(originDetail?.effectMileageFrame == 0 && currentFormData?.effectMileageFrame == 0) ? '' :
                <Form.Item label='聚合权益生效里程' rules={[{ required: false }]} className='effectMileageFrameWrap'>
                    <Row>
                        <Col span={3}>
                            <Form.Item
                                className='effectMileageFrameCls'
                                name={['detailDisplay', 'effectMileageFrame']}
                                initialValue={0}
                            >
                                <Radio.Group onChange={e => effectMileageFrameRadioChange(e.target.value)} >
                                    <Space direction='vertical' align='start' size={38} >
                                        <Radio value={0}>无此策略</Radio>
                                        <Radio value={2} disabled={limitLegendDisplay === 0}>相对里程</Radio>
                                        <Radio value={1} style={{ marginTop: '60px' }} disabled={limitLegendDisplay === 0}>固定里程</Radio>
                                    </Space>
                                </Radio.Group>
                            </Form.Item>
                        </Col>
                        <Col span={15} style={{ width: 650 }}>
                            <Space direction='vertical' align='start' size={28} className='mileage'>
                                <div style={{ height: 32 }}>&nbsp;</div>
                                <Space>
                                    <Form.Item
                                        name={['detailDisplay', 'relativeBeginMileageStrategy']}
                                        label='开始里程'
                                        className='mb0 w350 mr15'
                                        rules={[{ required: effectMileageFrameFlag === 2, message: '请选择' }]}
                                    >

                                        <Select allowClear placeholder='请选择' disabled={effectMileageFrameFlag !== 2} onChange={mileageEndStrFn}>
                                            {/* {
                                                RELATIVE_MILEAGE_OPTIONS.map(item => <Option key={item.value} value={item.value}>
                                                    {item.label}
                                                    <span className={StrategyFormStyle.remark}>{item.remark}</span>

                                                </Option>)
                                            } */}
                                            {/* Number(i.extendField1) === 2 聚合需要的字典 */}
                                            {
                                                entryList['equity_relative_begin_mileage_strategy'] && entryList['equity_relative_begin_mileage_strategy'].length ?
                                                    entryList['equity_relative_begin_mileage_strategy'].filter(i => Number(i.extendField1) === 2).map(item => <Option key={item.value} value={Number(item.value)}>
                                                        {item.name}
                                                        <span className={StrategyFormStyle.remark}>{item.description}</span>
                                                    </Option>)
                                                    : ''
                                            }
                                        </Select>
                                    </Form.Item>
                                    <Form.Item
                                        name={['detailDisplay', 'relativeEndMileageStrategy']}
                                        label='结束里程'
                                        className='mb0 w250'
                                        rules={[{ required: effectMileageFrameFlag === 2, message: '请选择' }]}
                                    >
                                        <Select allowClear placeholder='请选择' disabled={effectMileageFrameFlag !== 2} onChange={mileageEndStrFn}>
                                            {
                                                END_MILEAGE_OPTIONS.map(item => <Option key={item.value} value={item.value}>{item.label}</Option>)
                                            }
                                        </Select>
                                        {/* <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap' }}>增程器里程是汽车仅使用增程器运行时行驶的里程数，填写后将作为权益生效里程计算的基准</span> */}
                                    </Form.Item>

                                </Space>
                                <Space>
                                    <div className='mileageEnd'>
                                        {
                                            effectMileageFrameFlag === 2 ? (mileageEndStr || '-') : null
                                        }
                                    </div>
                                </Space>
                                <Space>
                                    <Form.Item
                                        name={['detailDisplay', 'fixedBeginMileage']}
                                        label='开始里程'
                                        className='mr15 w350'
                                        rules={[{ required: effectMileageFrameFlag === 1, message: '请输入' }]}
                                    >
                                        <InputNumber style={{ width: 270 }} min={0} placeholder='固定开始里程' disabled={effectMileageFrameFlag !== 1} onChange={mileageEndStrFn} />
                                    </Form.Item>
                                    <Form.Item
                                        name={['detailDisplay', 'fixedEndMileageStrategy']}
                                        label='结束里程'
                                        className='w250'
                                        rules={[{ required: effectMileageFrameFlag === 1, message: '请选择' }]}
                                    >
                                        <Select allowClear placeholder='请选择' disabled={effectMileageFrameFlag !== 1} onChange={mileageEndStrFn}>
                                            {
                                                END_MILEAGE_OPTIONS.map(item => <Option key={item.value} value={item.value}>{item.label}</Option>)
                                            }
                                        </Select>
                                    </Form.Item>
                                </Space>
                                <Space>
                                    <div className='mileageEnd'>
                                        {
                                            effectMileageFrameFlag === 1 ? (mileageEndStr || '-') : null
                                        }
                                    </div>
                                </Space>
                            </Space >
                        </Col >
                    </Row >
                </Form.Item >
            }

        </>
    )
}

export default memo(StrategyPolymerization)