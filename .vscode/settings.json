{
    // "prettier.semi": true, //句末加分号
    // "prettier.singleQuote": true, //使用单引号代替双引号
    // "prettier.trailingComma": "es5", //在对象或数组最后一个元素后面加逗号
    // "prettier.printWidth": 120, //一行的字符数，如果超过会换行，默认120
    // "prettier.tabWidth": 4, //缩进大小
    // "prettier.useTabs": false, //使用空格代替tab缩进
    // "prettier.endOfLine": "auto", //行尾换行符
    // "prettier.bracketSpacing": true, //对象大括号直接是否有空格，默认true，效果：{ foo: bar }
    // "prettier.jsxBracketSameLine": false, //在jsx中把'>' 是否单独放一行
    // "prettier.arrowParens": "avoid", //箭头函数，只有一个参数的时候，省略括号
    // "editor.tabSize": 4, //Tab宽度设置为4个空格
    "editor.formatOnSave": true, // 保存的时候自动格式化
    "editor.defaultFormatter": "esbenp.prettier-vscode", // 默认格式化工具选择prettier
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
}