import React, { memo, useState } from 'react'
import { Table, Tag } from 'antd'
import moment from 'moment'
import PublicTooltip from '@/components/Public/PublicTooltip'
import styles from '../../antd.module.less'
import StrategyFormStyle from './strategyForm.module.less'

const OriginalTable = (props) => {
    const { dataSource } = props

    return (
        <Table
            className={StrategyFormStyle.originTable}
            dataSource={dataSource}
            size='small'
            scroll={{ x: 3140 }}
            pagination={false}
            bordered
        >
            <Table.Column title='权益明细编码' key='detailNo' dataIndex='detailNo' width={200} fixed='left' render={text => text || '-'} />
            <Table.Column title='权益明细分类' key='sortCodeName' dataIndex='sortCodeName' width={140} render={text => text || '-'} fixed={'left'} />
            <Table.Column title='权益明细名称' key='name' dataIndex='name' render={text => text || '-'} width={200} fixed='left' />
            <Table.Column title='权益类别' key='typeCodeName' dataIndex='typeCodeName' width={120} render={text => text || '-'} />
            <Table.Column title='车辆属性' key='carAttrName' dataIndex='carAttrName' width={120} render={text => text || '-'} />
            <Table.Column title='权益归属' key='belongName' dataIndex='belongName' width={120} render={text => text || '-'} />
            <Table.Column title='权益抵扣金额' key='deduction' dataIndex='deduction' width={200} render={(text, record) => {
                return record.payAmount && record.deduction ?
                    <span>{record.payAmount}  元  ~  {record.deduction}  元</span>
                    : '-'
            }} />
            {/* detailVersion */}
            <Table.Column title='权益明细版本编码' key='detailVersion' dataIndex='detailVersion' width={160} render={text => text || '-'} />
            <Table.Column title='权益限制里程(公里)' key='limitLegend' dataIndex='limitLegend' width={140} render={(text, record) => {
                const limitLegendFrameMap = {
                    0: () => '-',    //无此策略
                    1: () => <span>{record.limitLegend ?? '-'}<Tag className={[styles['custom-ant-tag-green'], styles['ml5']]}>行驶</Tag></span>,  //行驶里程
                    2: () => <span>{record.extenderMileage ?? '-'}<Tag className={[styles['custom-ant-tag-yellow'], styles['ml5']]}>增程器</Tag></span> //增程器里程
                }
                if (record.limitLegendFrame === null) {
                    return record.limitLegend || record.extenderMileage
                } else {
                    return limitLegendFrameMap[record.limitLegendFrame]()
                }
            }} />
            {/* <Table.Column title='权益包属性' key='sortCodeName' dataIndex='sortCodeName' render={text=>text || '-'} /> */}
            {/* <Table.Column title='权益车型' key='carAttrName' dataIndex='carAttrName' /> */}
            <Table.Column title='权益限制年限' key='limitYear' dataIndex='limitYear' width={120} render={(text, record) => {
                let span = <span>-</span>
                if (record.limitTimeFrame == -1) {
                    span = <span>永久有效</span>
                } else if (record.limitTimeFrame == 1) {
                    span = <span>{`${Number(record.limitYear)}年${Number(record.limitMonth)}月${Number(record.limitDay)}日`}</span>
                }
                return span

                // return record.limitYear && record.yearUnit ? record.limitYear + getYearUnitName(record.yearUnit) :'不限年限'
            }} />
            <Table.Column title='车辆权益变更属性' key='identityName' dataIndex='identityName' width={120} render={text => text || '-'} />
            <Table.Column title='享有车主身份' key='carIdentityName' dataIndex='carIdentityName' width={120} render={text => text || '-'} />
            <Table.Column title='享有权益频次' key='frequency' dataIndex='frequency' width={120} render={text => {
                let a = ''
                if (text) {
                    a = text < 0 ? '不限次数' : text + '次';
                } else {
                    a = '-'
                }
                return a
            }} />
            <Table.Column title='补漆面部位' key='paintDesc' dataIndex='paintDesc' width={200} ellipsis render={(text, record) => {
                if (text) {
                    return <PublicTooltip title={text}>{text}</PublicTooltip>
                } else {
                    switch (record.paintFrame) {
                        case 0:
                            return '-'
                        case -1:
                            return '不限面数'
                        case 1:
                            const filter = record?.paintList?.filter(i => i.num > 0) || []
                            let sum = 0
                            const strArr = filter.map(item => {
                                sum += item.num
                                return `${item.name}*${item.num} `
                            }) || []
                            const str = strArr.join(',') + `, 共${sum}面`
                            return <PublicTooltip title={str}>{str}</PublicTooltip>
                    }

                }
            }} />
            <Table.Column title='全保养阶梯策略' key='maintenanceLadderDesc' dataIndex='maintenanceLadderDesc' width={120} render={(text, record) => {
                if (text) {
                    return text
                } else {
                    switch (record.maintenanceLadderFrame) {
                        case 0:
                            return '-'
                        case 1:
                            return record.maintenanceLadder + '次'
                    }
                }
            }} />
            <Table.Column title='车联网流量' key='traffic' dataIndex='traffic' width={120} render={(text, record) => {

                let a = ''
                if (text) {
                    a = text < 0 ? '不限流量' : text + record.trafficUnit;
                } else {
                    a = '-'
                }
                return a

            }} />
            <Table.Column title='权益生效时间' key='relativeTime' dataIndex='relativeTime' width={210} render={(text, record) => {
                return record.relativeTimeName ?
                    record.relativeTimeName
                    : <>
                        {
                            record.fixedBeginTime && record.fixedEndTime ? moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') + ' ~ ' + moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')
                                : '-'
                        }
                    </>

            }} />
            <Table.Column title='是否支持退款' key='refundName' dataIndex='refundName' width={120} render={text => text || '-'} />
            <Table.Column title='是否三方履约' key='performanceName' dataIndex='performanceName' width={120} render={text => text || '-'} />
            <Table.Column title='权益履约方' key='performingDesc' dataIndex='performingDesc' width={200} ellipsis render={(text, record) => {
                if (text) {
                    return <PublicTooltip title={text}>{text}</PublicTooltip>
                } else {
                    let arr = []
                    record.performingList && record.performingList.map(item => {
                        arr.push(item.label || item.name)
                    })
                    const str = arr.join(',')
                    return <PublicTooltip title={str}>{str}</PublicTooltip>
                }

            }} />
            <Table.Column title='履约结算价格' key='settlementPriceDesc' dataIndex='settlementPriceDesc' width={120} render={(text, record) => {
                if (text) {
                    return text
                } else {
                    switch (record.settlementPriceFrame) {
                        case 0:
                            return '-'
                        case 1:
                            return '实际结算'
                        case 2:
                            const price = record.settlementPrice
                            return price.toFixed(2) + '元'
                    }
                }
            }} />
            <Table.Column title='延保承保方' key='extendWarrantyAssurerName' dataIndex='extendWarrantyAssurerName' width={120} render={text => text || '-'} />
            <Table.Column title='业务编码' key='businessCodeDesc' dataIndex='businessCodeDesc' width={120} render={(text, record) => {
                return record.businessCodeDesc || record.businessCodeName || '-'
            }} />
            <Table.Column title='权益声明' key='statement' dataIndex='statement' width={300} ellipsis render={text => text ? <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>{text}</PublicTooltip> : '-'} />
            <Table.Column title='权益备注' key='remark' dataIndex='remark' width={300} ellipsis render={text => text ? <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>{text}</PublicTooltip> : '-'} />
        </Table>
    )
}
export default memo(OriginalTable)