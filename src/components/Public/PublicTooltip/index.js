import React, { useEffect, useRef, useState } from 'react'
import { Tooltip } from 'antd'
const PublicTooltip = props => {
    const { placement = 'topLeft', title, children } = props
    const container = useRef(null)
    const [overlayInnerStyle, setOverlayInnerStyle] = useState({})
    useEffect(() => {
        const styles = {
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            width: container.current.clientWidth + 'px',
        }
        setOverlayInnerStyle(styles)
    }, [])
    return <Tooltip placement={placement} title={title} color='rgba(26,144,255,1)'>
        <div ref={container} style={overlayInnerStyle} title=''>{children}</div>
    </Tooltip>

}
export default PublicTooltip