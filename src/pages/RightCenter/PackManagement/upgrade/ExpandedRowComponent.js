import React, { useState, memo, useEffect, useRef } from 'react'
import { Table, Spin, Form, message, Row, Col, Button, Radio, Input } from 'antd'
import allUrl from '../../../../utils/url';
import { post, get } from '@/utils/request';
import CreateStrategyCom from '../../DetailManagement/CreateStrategyCom';
import PublicTable from '../PublicTable'
import style from './upgrade.module.less'
import styles from '../../antd.module.less'
import { outsideStrategyConfig } from './upgradeFormConfig'
import { UPGRADE_DETAILAPI } from '../upgradeSetting'
import moment from 'moment'
import _ from 'lodash'
const ExpandedRowComponent = (props) => {
  const { tableList, setTableList, oriRecord, typeValue, editUpgradeModal, detailDisplay, expandedDisplayHash, bodyRefs, refIndex } = props
  // 获取当前明细
  const currentList = tableList.filter(item => item.originDetail.detailNo === oriRecord.detailNo)

  const [strategyConfig, setStrategyConfig] = useState()
  // const [newDisplay, setNewDisplay] = useState(currentList?.[0]?.[UPGRADE_DETAILAPI.DISPLAY]||{})
  const [newDisplay, setNewDisplay] = useState(detailDisplay || {})
  const [loading, setLoading] = useState(true)
  const [showStyle, setShowStyle] = useState(1)

  const [form] = Form.useForm()
  const { TextArea } = Input;


  const _props = {
    formObj: form,
    strategyConfig: strategyConfig,
    dataSource: newDisplay
  }
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 16 },
  };

  //
  const isHas = (obj, name) => {
    return Object.prototype.hasOwnProperty.call(obj, name)
  }


  const selectShowStyle = (val) => {
    setShowStyle(val)
  }
  // 添加升级聚合编辑逻辑
  const editDefault = () => {
    // console.log('---', form, detailDisplay);
    // console.log('expandedDisplayHash.current', expandedDisplayHash.current);
    // let newDisplay = detailDisplay

    if (!newDisplay) return {}
    if (expandedDisplayHash.current.hasOwnProperty(newDisplay.originDetailNo) && expandedDisplayHash.current[newDisplay.originDetailNo].hasOwnProperty('displayFrame')) {
      setNewDisplay({ ...newDisplay, ...expandedDisplayHash.current[newDisplay.originDetailNo] })
      // _props.dataSource = newDisplay
    } else {
      // 给当前组件的5个表单赋默认值
      form.setFieldsValue({
        detailNo: newDisplay.detailNo,
        originDetailNo: newDisplay.originDetailNo,
        displayFrame: newDisplay.displayFrame ?? showStyle,
        originIsDisplay: newDisplay.originIsDisplay ?? 1,
        isDisplay: newDisplay.isDisplay ?? 1,
        name: newDisplay.name || '',
        statement: newDisplay.statement || ''
      })
      selectShowStyle(newDisplay.displayFrame ?? showStyle)
    }


    // 给其余表单赋默认值
    const _config = _.cloneDeep(outsideStrategyConfig)

    // 所有策略 -- 升级前为无此策略 直接隐藏
    if (Number(newDisplay.limitTimeFrame) === 0) {
      _config.limitTimeFrame.hide = true
    }

    // 权益限制里程不可修改选项，但是可以修改数值
    _config.limitLegendFrame.c.limitLegendFrame.disabled = true
    if (Number(newDisplay.limitLegendFrame) === 0) {
      _config.limitLegendFrame.hide = true
    }
    // 权益生效里程
    if (Number(newDisplay.effectMileageFrame) === 0) {
      _config.effectMileageFrame.hide = true
    }
    //  聚合权益频次
    // || Number(newDisplay.frequency) === 0
    if (Number(newDisplay.frequencyRadio) === 0 || Number(newDisplay.frequency) === 0) {
      _config.frequencyRadio.hide = true
    }

    //  聚合全保养阶梯策略
    if (Number(newDisplay.maintenanceLadderFrame) === 0) {
      _config.maintenanceLadderFrame.hide = true
    }

    // 补漆面部位
    if (Number(newDisplay.paintFrame) === 0) {
      _config.paintFrame.hide = true
    }
    // 车联网流量 无此策略和不限次数不可修改，其他可选不限次数
    // || Number(newDisplay.traffic) === 0
    if (Number(newDisplay.trafficRadio) === 0 || Number(newDisplay.traffic) === 0) {
      _config.trafficRadio.hide = true
    }
    // 权益金额抵扣 为无此策略不可修改
    if (Number(newDisplay.deductionFrame) === 0) {
      _config.deductionFrame.hide = true
    }
    // console.log(_config);

    setLoading(false)
    setStrategyConfig(_config)

  }

  // editDefault()
  // useEffect(() => {
  //   if (detailDisplay.originDetailNo) {
  //     console.log('detailDisplay', detailDisplay.originDetailNo, oriRecord.detailNo);
  //     editDefault()

  //   }

  // }, [])
  useEffect(() => {
    // console.log('detailDisplay', detailDisplay.originDetailNo, oriRecord.detailNo);
    // setNewDisplay({ ...newDisplay, ...expandedDisplayHash.current[newDisplay.originDetailNo] })

    editDefault()
  }, [detailDisplay])
  useEffect(() => {
    /** 
     * 该hook进来是因为父对象tableList有变化（编辑升级明细后），
     * 编辑不重新给聚合明细赋值，因为不知道具体修改了哪个值
     * 如果聚合明细想变化请重新升级
     */
    // 给tablelist 展开项中填充聚合信息
    setTableList((prev) => {
      const _prev = [...prev]
      _prev.map(item => {
        if (item.originDetail.detailNo === oriRecord.detailNo) {
          item.detailDisplay = detailDisplay // 这里写原来的值，目的是防止死循环
        }
      })
      return _prev
    })
    expandedDisplayHash.current = { ...expandedDisplayHash.current, [oriRecord.detailNo]: expandedDisplayHash.current[oriRecord.detailNo] ?? {} }
    // setExpandedDisplays({ ...expandedDisplays, [oriRecord.detailNo]: expandedDisplays[oriRecord.detailNo] ?? {} })
  }, [oriRecord])

  // 修改表单监听
  const onValuesChange = (changedValues, allValues) => {
    console.log((changedValues, allValues));
    expandedDisplayHash.current[oriRecord.detailNo] = { ...expandedDisplayHash.current[oriRecord.detailNo], ...allValues }
  }


  return (
    bodyRefs &&
    <div className={style.upgradeWrapper} >
      {/* 升级-基础权益明细 */}
      < div className="detail-upgrade-table" >
        <h4>升级明细信息</h4>
        <PublicTable
          className={`TablePanel ${style['blueUpgradeTable']}`}
          dataSource={currentList}
          type={UPGRADE_DETAILAPI.UPGRADEVIEW} //这里显示取这个字段
          rowKey={(record) => record.detailNo}
          pagination={false}
        >
          <Table.Column title='操作' key='' dataIndex='' width={100} fixed='right'
            render={record =>
              <div className='btn-wrapper'>
                <Button type='link' disabled={typeValue != '1'} onClick={() => editUpgradeModal(record)}>编辑</Button>
              </div>
            } />
        </PublicTable>
      </div >
      {/* 升级-展示形式编辑 */}
      <Spin spinning={loading}>
        <Form {...layout} name="expandedRow" form={form} ref={ele => { bodyRefs.current[refIndex] = ele }} className='show-form-wrapper' onValuesChange={onValuesChange}>
          <Form.Item name='originDetailNo' style={{ display: 'none' }}></Form.Item>
          <Form.Item name='detailNo' style={{ display: 'none' }}></Form.Item>
          <div className='method'>
            <Form.Item
              label="客户端展示形式"
              style={{ display: 'inline-block', width: '260px' }}
              name='displayFrame'
              initialValue={0}
              labelCol={{ span: 11 }}
            >
              <Radio.Group onChange={(e) => selectShowStyle(e.target.value)}>
                <Radio value={1}>并列</Radio>
                <Radio value={2}>聚合</Radio>
              </Radio.Group>
            </Form.Item >
            <Button type='link' style={{ color: '#FFA500' }}>示例展示?</Button>
          </div>
          {/* 并列 */}
          {showStyle === 1 ?
            <div className='abreast'>
              <Form.Item label="基础明细是否在客户端展示" name="originIsDisplay" labelCol={{ span: 4 }}>
                <Radio.Group>
                  <Radio value={1}>在客户端上展示</Radio>
                  <Radio value={2}>不在客户端上展示</Radio>
                </Radio.Group>
              </Form.Item>


              <Form.Item label="升级明细是否在客户端展示" name="isDisplay" labelCol={{ span: 4 }}>
                <Radio.Group>
                  <Radio value={1}>在客户端上展示</Radio>
                  <Radio value={2}>不在客户端上展示</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
            :
            <>
              <h4 className='s-title'>聚合权益信息</h4>
              <Form.Item label="聚合权益明细名称" name='name' rules={[{ required: true }]}>
                <Input className='input' maxLength={100} placeholder="请填写标题，最多100个字，建议50字以内，不影响页面展示" />
              </Form.Item>
              <Form.Item label="聚合权益声明" name='statement' help={<div style={{ color: 'red', fontSize: '12px', marginBottom: '20px', marginTop: '8px' }}>如配置在声明里的权益策略，例如：一次补胎、两次补漆等，不作为权益策略，无法进行核销</div>}>
                <TextArea className='input' placeholder="此处需要对外展示，建议描述时谨慎，例如：赛力斯为您所购买的赛力斯汽车提供整车及增程包修期为4年不限里程（以先到者为准，运营车辆除外），详见《保修及保养手册》质量担保明细" autoSize={{ minRows: 2, maxRows: 6 }} />
              </Form.Item>
              <h4 className='s-title'>聚合权益策略</h4>
              {strategyConfig ? <CreateStrategyCom type='upgrade' {..._props} /> : null}
            </>
          }
        </Form>
      </Spin>
    </div >
  )
}
export default memo(ExpandedRowComponent)
