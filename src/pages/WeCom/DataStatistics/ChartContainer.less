.chartContainer-card {
  .chartContainer-card-header {
    margin-bottom: 24px;
    .title {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      display: flex;
      // justify-content: space-between;
      .title-con {
        display: flex;
        align-items: center;
        flex: 1;
        .title-con-text {
          // margin-right: 20px;
        }
        .ant-tabs {
          .ant-tabs-nav {
            margin-bottom: 0;
          }
        }
      }
      .title_warp {
        display: flex;
        align-items: center;
        flex: 1;
        .theme {
          width: 12px;
          height: 12px;
          background: #c04eff;
          border-radius: 3px;
          margin-right: 8px;
        }
      }
      .extended {
        font-size: 14px;
      }
    }
    .subtitle {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: rgba(0, 0, 0, 0.45);
      display: flex;
      justify-content: space-between;
      .subtitle-statisticalTime {
        font-size: 13px;
      }
    }
  }

  .chartContainer-card-content {
    .content-con {
      .content-left {
        .content-left-item {
          height: 130px;
          width: 90%;
          border: solid 1px #bbbbbb;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;
          margin-bottom: 20px;
          .title {
            color: rgba(16, 16, 16, 100);
            font-size: 16px;
            font-family: SourceHanSansSC-black;
            display: flex;
            justify-content: space-around;
            width: 100%;
            align-items: flex-end;
            .title-max {
              font-weight: 500;
              font-size: 18px;
            }
            .title-min {
              font-size: 14px;
              font-weight: 400;
            }
          }
          .con {
            color: rgba(162, 239, 77, 100);
            font-size: 36px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
            display: flex;
            justify-content: space-around;
            width: 100%;
            sup {
              font-size: 16px;
              color: rgba(16, 16, 16, 100);
              font-size: 14px;
              vertical-align: super;
            }
            sub {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}
