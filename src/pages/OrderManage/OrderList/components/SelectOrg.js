import React, { useState, useEffect } from 'react'
import { Modal, Tree, message } from 'antd'
import { get,post } from '@/utils/request'
import allUrl from '@/utils/url'
import './SelectOrg.less'
const SelectOrg = (props) => {
    const { title, visible, onCancel ,selectedRows,getTableData} = props
    const [treeData, setTreeData] = useState([])
    const [treeselectedKeys, setTreeselectedKeys] = useState([])
    const [treeSelectedRows, setTreeSelectedRows] = useState([])
    const handleOk = () => {
        if(!treeSelectedRows.length){
            message.error('请先选择组织！')
            return
        }
        let dealerCodes = []
        selectedRows.forEach(item=>{
            dealerCodes.push(item.dealerCode)
        })

        post(allUrl.StoreManage.updateOrganization,{dealerCodes:dealerCodes,organizationId:treeselectedKeys[0]}).then(res=>{
            if(res.success){
                message.success(res.msg)
                onCancel()
                getTableData()
            }else{
                // message.error(res.msg)
            }
        })
        console.log(treeSelectedRows)
    }
    const updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }
            if (node.children) {
                return { ...node, children: updateTreeData(node.children, key, children) };
            }
            return node;
        });
    }
    const treeSelect = (selectedKeys, selectedNodes, event) => {
        setTreeselectedKeys(selectedKeys)
        setTreeSelectedRows(selectedNodes.selectedNodes)
    }
    useEffect(() => {
        post(allUrl.Authority.orgPageList).then(res => {
            if (res.success) {
                setTreeData(res.resp)
            } else {
                // message.error(res.msg)
            }
        })
    }, [])
    return <Modal wrapClassName='SelectOrgOfStore' visible={visible} title={title} onCancel={onCancel} onOk={handleOk} width={600} maskClosable={false}>
        {
            treeData && treeData.length ?
                <Tree
                    defaultExpandedKeys={[1]}
                    defaultSelectedKeys={[1]}
                    treeData={treeData}
                    onSelect={treeSelect}
                    selectedKeys={treeselectedKeys}
                />
                : null
        }
    </Modal>

}
export default SelectOrg