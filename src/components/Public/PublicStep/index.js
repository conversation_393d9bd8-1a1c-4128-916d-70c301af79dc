import React, { useEffect, useState, useRef } from 'react'
import style from './style.module.less'
const PublicStep = (props) => {
  const { current, items } = props
  // console.log(current,items);

  return (
    <div className={style.steps}>
      <div className='content'>
        {items.map((item, index) => {
          return (
            <div key={index} className={`step ${current>=index?'done':''}`}>
              <span>{index + 1}</span>
              <b>{item.title}</b>
            </div>
          )
        })}
      </div>
    </div>
  )

}
export default PublicStep