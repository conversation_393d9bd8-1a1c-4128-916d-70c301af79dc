import React, { useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import moment from 'moment'
import allUrl from '../../../utils/url';
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import './index.less'
import styles from '../antd.module.less'
import tableModuleStyles from '../tableLayout.module.less'

const TabBase = (props) => {
    const { DictCarData, PART_TYPE_OPTIONS } = props
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const [tableHeight, setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({})
    const [DictData, setDictData] = useState([])
    const [isModalVisibleDetail, setIsModalVisibleDetail] = useState(false);
    const [detailInfoSource, setDetailInfoSource] = useState([])
    const ref = useRef()

    const onSearch = (values) => {
        if (values.updateTime) {
            values.updateTime = moment(values.updateTime).format('YYYY-MM-DD')
        }
        setDefaultQuery(values)
    }

    const initPage = (flag) => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if (!flag) {
            h = winH - 47 - 54 - 345
        } else {
            h = winH - 47 - 54 - 400
        }
        setTableHeight(h)
    }

    useEffect(() => {
        initPage();
    }, [])

    const searchList = [
        { label: '配件编码', name: 'partNo', type: 'Input', placeholder: '请输入配件编码', colSpan: 6 },
        {
            label: '车型', name: 'carType', type: 'Select', placeholder: '请选择', colSpan: 6, data: DictCarData || []
        },
        { label: '配件名称', name: 'partName', type: 'Input', placeholder: '请输入配件名称', colSpan: 6 },
        { label: '配件包修类型', name: 'partType', type: 'Select', colSpan: 6, data: PART_TYPE_OPTIONS },
        { label: '更新时间', name: 'updateTime', type: 'DatePicker', colSpan: 6, placeholder: '请选择日期' },
    ]

    const columns = [
        { title: '序号', dataIndex: 'index', width: 70, fixed: 'left', render: (text, record, index) => (ref?.current?.current - 1) * ref?.current?.pageSize + index + 1 },

        {
            title: '车型', dataIndex: 'carType', width: 120, fixed: 'left', render: (text) => text || '-'
        },
        {
            title: '配件名称', dataIndex: 'partName', width: 230, fixed: 'left', render: (text) => text || '-'
        },
        {
            title: '配件编码', dataIndex: 'partNo', width: 180, fixed: 'left', render: (text) => text || '-'
        },
        {
            title: '配件包修类型', dataIndex: 'partTypeName', width: 120, render: (text) => text || '-'
        },
        { title: '更新时间', dataIndex: 'updateTime', width: 180, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-' },
    ]

    return (
        <div className={`${tableModuleStyles.PublicList}`}>
            <PublicTableQuery
                isCatch={false}
                isFormDown={false}
                onSearch={onSearch}
                searchList={searchList}
            />
            <div className="tableData">
                <PublicTable
                    ref={ref}
                    type={6}
                    rowSelection={false}
                    defaultQuery={defaultQuery}
                    url={allUrl.PartsManagement.getCustomerPartList}
                    columns={columns}
                    className={styles["blue-table"]}
                />
            </div>
        </div>
    )
}
export default TabBase
