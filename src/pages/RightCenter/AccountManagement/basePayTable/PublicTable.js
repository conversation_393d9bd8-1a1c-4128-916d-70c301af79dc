import React, { useState, memo, useEffect, useCallback, useContext } from 'react';
import { Table, Button, Tag, Descriptions, Row, Col, Spin, Tabs, Divider, Tooltip } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
// import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import moment from 'moment';
// import history from '@/utils/history'
import { get } from '@/utils/request';
import allUrl from '@/utils/url';
import PublicTooltip from '@/components/Public/PublicTooltip';
import LadderDetail from '../LadderDetail';
import styles from '../../antd.module.less';
import { InjectMessage } from '@/pages/RightCenter/AccountManagement/Detail';
import { extendFlag, groupFlag } from '../../publicMethods';

const effectMap = {
  1: {
    color: '#F5222D',
    name: '未生效',
  },
  2: {
    color: '#52C41A',
    name: '已生效',
  },
  3: {
    color: '#872EF1',
    name: '已使用',
  },
  4: {
    color: '#BBBBBB',
    name: '已失效',
  },
  5: {
    color: '#F87E02',
    name: '激活中',
  },
  //精品权益生效状态 ：6=》待履约  7=》已履约
  6: {
    color: '#F5222D',
    name: '待履约',
  },
  7: {
    color: '#872EF1',
    name: '已履约',
  },
  null: {
    color: '',
    name: '',
  },
};

const effectStatusMap = {
  1: {
    // color: '#F5222D',
    name: '未使用',
  },
  2: {
    // color: '#52C41A',
    name: '使用中',
  },
  3: {
    // color: '#872EF1',
    name: '已使用',
  },
  4: {
    // color: '#BBBBBB',
    name: '已用完',
  },
  5: {
    // color: '#F87E02',
    name: '不可用',
  },
  null: {
    // color: '',
    name: '',
  },
};
const limitLegendMap = {
  0: {
    type: '',
    name: '',
    style: '',
  },
  1: {
    type: 'limitLegend',
    name: '行驶',
    style: 'custom-ant-tag-green',
  },
  2: {
    type: 'extenderMileage',
    name: '增程器',
    style: 'custom-ant-tag-yellow',
  },
  null: {
    type: '',
    name: '',
    style: '',
  },
};
const effectMileageFrameMap = {
  0: '',
  1: '固定里程：',
  2: '相对里程：',
  null: '-',
};
const beginMileageTypeMap = {
  0: {
    name: '默认',
    style: 'custom-ant-tag-grey',
  },
  1: {
    name: 'DMS',
    style: 'custom-ant-tag-pink',
  },
  2: {
    name: '智库',
    style: 'custom-ant-tag-purple',
  },
  null: {
    name: '',
    style: '',
  },
};
const extendWarrantyAssurerMap = {
  0: '-',
  1: '保险',
  2: '车厂',
  null: '-',
};
const PublicTable = props => {
  const { dataSource, strategy, expandableProps, insideTable, className, expandedRowKeys, children } = props;
  const [isLadderVisible, setIsLadderVisible] = useState(false);
  const [ladderData, setLadderData] = useState({});
  const { type } = useContext(InjectMessage);
  //阶梯保养
  const handleLadderCancle = () => {
    setIsLadderVisible(!isLadderVisible);
  };
  //查询阶梯保养详情
  const getLadderData = record => {
    get(allUrl.AccountManagement.getAccountEquityLadderDetailList, {
      relationID: record.accountPackRelationId,
      detail_No: record.detailNo,
      inOutFlag: strategy === 'out' ? 2 : 1, //对内对外标识  对内为1 对外为2
      maintenanceLadderNum: record.maintenanceLadder, //对内对外条数
    })
      .then(res => {
        const { success, resp } = res;
        if (success) {
          if (!!resp.length) {
            setLadderData({ maintenanceLadderLimitFrame: record.maintenanceLadderLimitFrame, resp });
          }
        }
      })
      .finally(() => {
        handleLadderCancle();
      });
  };

  //table-columns
  const insideUpgrade = (text, record) => {
    return (
      <div>
        <div className={`${styles['custom-mark']} ${styles['bg1']}`}>
          <div className={styles['custom-mark-content']}>
            <span>已升级</span>
          </div>
        </div>
        <span>{text}</span>
      </div>
    );
  };

  const insideExtend = (text, record) => {
    return (
      <div>
        <div className={`${styles['custom-mark']} ${styles['bg2']}`}>
          <div className={styles['custom-mark-content']}>
            <span>延保</span>
          </div>
        </div>
        <span>{text}</span>
        {expandedRowKeys.includes(record.detailNo) ? (
          <Button
            type="link"
            size="small"
            className={`${styles['more-info-btn']}`}
            onClick={() => expandableProps?.onExpand(false, record)}
          >
            <UpOutlined />
            前权益
          </Button>
        ) : (
          <Button
            type="link"
            size="small"
            className={`${styles['more-info-btn']}`}
            onClick={() => expandableProps?.onExpand(true, record)}
          >
            <DownOutlined />
            前权益
          </Button>
        )}
      </div>
    );
  };

  const outsideUpgrade = record => {
    return (
      <div style={{ position: 'relative' }}>
        <div className={`${styles['custom-mark']} ${styles['bg1']}`} style={{ top: -25, left: -25 }}>
          <div className={styles['custom-mark-content']}>
            <span>已升级</span>
          </div>
        </div>
        <div style={{ marginTop: 15 }}>{record.originDetailNo}</div>
        <div style={{ marginLeft: -25, marginRight: -17, marginTop: 20 }}>
          <Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} />
        </div>
        <div>{record.detailNo}</div>
      </div>
    );
  };

  const outsideExtend = record => {
    return (
      <div style={{ position: 'relative' }}>
        <div className={`${styles['custom-mark']} ${styles['bg2']}`} style={{ top: -25, left: -25 }}>
          <div className={styles['custom-mark-content']}>
            <span>延保</span>
          </div>
        </div>
        <div style={{ marginTop: 15 }}>{record.originDetailNo}</div>
        <div style={{ marginLeft: -25, marginRight: -17, marginTop: 20 }}>
          <Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} />
        </div>
        <div>{record.detailNo}</div>
      </div>
    );
  };

  const renderHtml = data => (
    <div className="pack-table-merge-wrap">
      {data.map((item, index) => {
        return (
          <>
            {/* 明细信息 */}
            <div className="pack-table-row">
              <div className="detai">{item}</div>
            </div>
          </>
        );
      })}
    </div>
  );

  const outsideCombination = data => {
    return (
      <>
        <div style={{ position: 'relative' }}>
          <div className={`${styles['custom-mark']} ${styles['bg3']}`} style={{ top: -8, left: -25 }}>
            <div className={styles['custom-mark-content']}>
              <span>组合</span>
            </div>
          </div>
        </div>
        {renderHtml(data)}
      </>
    );
  };

  return (
    <>
      <Table
        className={className}
        dataSource={dataSource?.filter(item => Boolean(item))}
        rowKey={record => record.detailNo.toString()}
        size="small"
        bordered={true}
        pagination={false}
        scroll={{ x: 3140 }}
        expandable={expandableProps}
        rowClassName={(record, index) => {
          if (!insideTable) {
            if (record.businessScene === 3) {
              if ((record.displayFrame === 1 && strategy === 'out') || strategy === 'in' || strategy === 'same')
                return `${styles['table-row-blue']}`;
            }
            return `${styles['table-row-height72']}`;
          }
        }}
      >
        {type === 'driver' && strategy === 'out' && (
          <Table.Column
            title="组合明细编码"
            key="groupDetailNo"
            dataIndex="groupDetailNo"
            width={160}
            render={text => (text ? groupFlag({ text }) : '-')}
            fixed={'left'}
          />
        )}
        {/* <Table.Column title='权益明细编码' key='detailNo' dataIndex='detailNo' width={200} fixed='left' render={text => text || '-'} /> */}
        <Table.Column
          title="权益明细编码"
          key="detailNo"
          dataIndex="detailNo"
          width={200}
          fixed="left"
          className={`${styles['table-pad-left']} ${styles['pack-table-merge-column']}`}
          render={(text, record) => {
            //insideTable 是否内嵌表格
            if (!insideTable) {
              if (record.groupDetailNo && !!record.groupDetailNos?.length) {
                return outsideCombination(record.groupDetailNos);
              }
              if (record.displayFrame === 2) {
                if (strategy === 'out') {
                  if (record.businessScene === 2) {
                    return outsideExtend(record);
                  }
                  if (record.businessScene === 3) {
                    return outsideUpgrade(record);
                  }
                  // return (
                  //     <div className={styles['merge-column']}>
                  //         <p >{record.originDetailNo}</p>
                  //         <br />
                  //         <p >{record.detailNo}</p>
                  //     </div>
                  // )
                }
              }
              // 对内升级
              if (record.businessScene === 3) {
                return insideUpgrade(text);
              }
              // 对内延保
              if (record.businessScene === 2) {
                return insideExtend(text, record);
              }
            }
            // 其他
            return (
              <div>
                <span>{text}</span>
              </div>
            );
          }}
        />
        <Table.Column
          title="权益分类"
          key="sortCodeName"
          dataIndex="sortCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细名称"
          key="name"
          dataIndex="name"
          render={text => text || '-'}
          width={200}
          fixed="left"
        />
        {/* v12.0 */}
        {/* <Table.Column title='业务编码' key='businessCode' dataIndex='businessCode' width={100} render={text => `${businessCodeMap[text]} (${text})`} /> */}
        <Table.Column
          title="业务编码"
          key="businessCodeName"
          dataIndex="businessCodeName"
          width={130}
          render={(text, record) =>
            strategy === 'out' && record.groupDetailNo ? record.businessCodes : record.businessCodeName
          }
        />
        {/* v12.0 */}
        <Table.Column
          title="权益生效状态"
          key="effect"
          dataIndex="effect"
          width={120}
          render={(text, record) => {
            const index = record?.effect || false;
            const RENDER_DOM = (
              <div style={{ fontSize: '12px', display: 'flex', alignItems: 'center' }}>
                <b
                  style={{
                    background: effectMap[index]?.color,
                    width: '6px',
                    height: '6px',
                    borderRadius: '6px',
                    marginRight: '8px',
                  }}
                ></b>
                <span style={{ color: effectMap[index]?.color }}>{effectMap[index]?.name}</span>
              </div>
            );
            if (type === 'driver' && record.groupDetailNos) {
              //组合
              return typeof index === 'number' ? groupFlag({ text: RENDER_DOM }) : groupFlag({ text: '-' });
            }
            return typeof index === 'number' ? RENDER_DOM : '-';
            // const index = record?.effect || false;
            // if (typeof index === 'number') {
            //   return (
            //     <div style={{ fontSize: '12px', display: 'flex', alignItems: 'center' }}>
            //       <b
            //         style={{
            //           background: effectMap[index]?.color,
            //           width: '6px',
            //           height: '6px',
            //           borderRadius: '6px',
            //           marginRight: '8px',
            //         }}
            //       ></b>
            //       <span style={{ color: effectMap[index]?.color }}>{effectMap[index]?.name}</span>
            //     </div>
            //   );
            // } else {
            //   return '-';
            // }
          }}
        />

        <Table.Column
          title="权益使用状态"
          key="status"
          dataIndex="status"
          width={120}
          render={(text, record) => {
            const index = record?.status || false;
            if (typeof index === 'number') {
              return (
                <div style={{ fontSize: '12px', display: 'flex', alignItems: 'center' }}>
                  <span>{effectStatusMap[index]?.name}</span>
                </div>
              );
            } else {
              return '-';
            }
          }}
        />
        <Table.Column
          title="权益业务场景"
          key="businessSceneName"
          dataIndex="businessSceneName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益限制年限"
          key="limitYear"
          dataIndex="limitYear"
          width={120}
          className={styles['table-pad-left']}
          render={(text, record) => {
            // 无此策略 -1 ; 不限制，永久有效 0 ; 其他 1
            let span = <span>-</span>;
            if (record.limitTimeFrame == -1) {
              span = <span>永久有效</span>;
            } else if (record.limitTimeFrame == 1) {
              span = (
                <span>{`${Number(record.limitYear)}年${Number(record.limitMonth)}月${Number(record.limitDay)}日`}</span>
              );
            }
            if (type === 'driver' && record.groupDetailNos) {
              //组合
              return groupFlag({ text: span });
            }
            // return span
            if (record.businessScene === 2) {
              return extendFlag({ text: span });
            }
            return span;
          }}
        />
        {/**
                 * 权益生效时间：需要联合两个条件判断 1.权益限制年限limitTimeFrame（0-无此策略  1-不限年限  2-其他） 2.生效时间的类型（固定 相对）
                    -》limitTimeFrame===0（无此策略）&& 固定 ：开始时间 结束时间
                    -》limitTimeFrame===0（无此策略）&& 相对 ：-
                            * 
                    -》limitTimeFrame===1（不限年限）&& 固定 ：开始时间 永久有效（这种场景不存在）
                    -》limitTimeFrame===1（不限年限）&& 固定 ：开始时间 结束时间 （因为当前控件必须要选择结束时间）
                    -》limitTimeFrame===1（不限年限）&& 相对 ：开始时间 永久有效
                            * 
                    -》 limitTimeFrame===2（其他）&& 固定 ：开始时间 结束时间
                    -》 limitTimeFrame===2（其他）&& 相对 ：开始时间 结束时间
                */}
        <Table.Column
          title="权益生效时间"
          key="relativeTime"
          dataIndex="relativeTime"
          width={280}
          className={styles['table-pad-left']}
          render={(text, record) => {
            let tmpl = '';
            const renderDom = ({ defaultText, styleCss }) => {
              if (record.effectTimeStr && record.effectTimeStr.includes('~')) {
                const [start, end] = record.effectTimeStr.split('~');
                tmpl = (
                  <>
                    {
                      <div style={{ lineHeight: '30px' }}>
                        <Tag className={styles['custom-ant-tag-blue']}>开始时间</Tag>
                        <span style={styleCss}>{start}</span>
                        {/* {moment(record.effectTime).format('YYYY年MM月DD日 HH:mm:ss')} */}
                        <br />
                        <Tag className={styles['custom-ant-tag-blue']}>结束时间</Tag>
                        <span style={styleCss}>{end}</span>
                        {/* {record.limitTimeFrame == -1 ? '永久有效' : moment(record.endTime).format('YYYY年MM月DD日 HH:mm:ss')} */}
                      </div>
                    }
                  </>
                );
              } else if (record.effectTimeStr === null) {
                tmpl = defaultText;
              } else {
                tmpl = <span style={styleCss}>{record.effectTimeStr}</span>;
              }
            };

            if ([2, 3, 4].includes(record.effect)) {
              // if (record.effectTimeStr && record.effectTimeStr.includes('~')) {
              //     const [start, end] = record.effectTimeStr.split('~')
              //     tmpl = <>{
              //         <div style={{ lineHeight: '30px' }}>
              //             <Tag className={styles['custom-ant-tag-blue']}>开始时间</Tag>{start}
              //             {/* {moment(record.effectTime).format('YYYY年MM月DD日 HH:mm:ss')} */}
              //             <br />
              //             <Tag className={styles['custom-ant-tag-blue']}>结束时间</Tag>{end}
              //             {/* {record.limitTimeFrame == -1 ? '永久有效' : moment(record.endTime).format('YYYY年MM月DD日 HH:mm:ss')} */}
              //         </div>
              //     }</>
              // } else if (record.effectTimeStr === null) {
              //     tmpl = '-'
              // } else {
              //     tmpl = record.effectTimeStr
              // }
              renderDom({ defaultText: '-', styleCss: {} });
            } else {
              if (type === 'base') {
                tmpl = '未生效';
              } else {
                // tmpl = record.effectTimeStr ? <span style={{ color: '#FF4D4F' }}>{record.effectTimeStr}</span> : '未生效'
                renderDom({ defaultText: '未生效', styleCss: { color: '#FF4D4F' } });
              }
            }

            if (type === 'driver' && record.groupDetailNos) {
              //组合
              return groupFlag({ text: tmpl });
            }
            if (record.businessScene === 2) {
              return extendFlag({ text: tmpl });
            }
            return tmpl;
          }}
        />
        {/* limitLegendType */}
        <Table.Column
          title="权益限制里程（基准）"
          key="limitLegend"
          dataIndex="limitLegend"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            let tmpl = '-';
            if (record.limitLegendType) {
              tmpl = (
                <div className={limitLegendMap[record.limitLegendType]?.type}>
                  {record[limitLegendMap[record.limitLegendType]?.type] || '-'}{' '}
                  {record.limitLegendType ? (
                    <Tag className={styles[limitLegendMap[record.limitLegendType]?.style]}>
                      {limitLegendMap[record.limitLegendType]?.name}
                    </Tag>
                  ) : (
                    ''
                  )}
                </div>
              );
            }
            if (record.businessScene === 2) {
              return extendFlag({ text: tmpl });
            } else {
              return tmpl;
            }
            // if (record.limitLegendType === null) {
            //     return '-'
            // }
            // return <div className={limitLegendMap[record.limitLegendType]?.type}>
            //     {record[limitLegendMap[record.limitLegendType]?.type] || '-'} {record.limitLegendType ? <Tag className={styles[limitLegendMap[record.limitLegendType]?.style]}>{limitLegendMap[record.limitLegendType]?.name}</Tag> : ''}
            // </div>
          }}
        />
        <Table.Column
          title="车辆当前里程"
          key="thinkMileage"
          dataIndex="thinkMileage"
          width={120}
          render={(text, record) => {
            if (record.limitLegendType === null) {
              return '-';
            }
            return (
              <div className={limitLegendMap[record.limitLegendType]?.type}>
                {record.limitLegendType == 1
                  ? record['thinkMileage'] || '-'
                  : record.limitLegendType == 2
                    ? record['rangeExtenderMileage'] || '-'
                    : ''}{' '}
                {record.limitLegendType ? (
                  <Tag className={styles[limitLegendMap[record.limitLegendType]?.style]}>
                    {limitLegendMap[record.limitLegendType]?.name}
                  </Tag>
                ) : (
                  ''
                )}
              </div>
            );
          }}
        />
        <Table.Column
          title="权益开始里程"
          key="fixedBeginMileage"
          dataIndex="fixedBeginMileage"
          width={200}
          render={(text, record) => {
            // beginMileageType：权益生效里程类型    effectMileageFrame：权益生效里程策略  相对里程才展示标签
            return (
              <div>
                {effectMileageFrameMap[record.effectMileageFrame]}
                {record.fixedBeginMileage === null ? '-' : record.fixedBeginMileage}
                {record.beginMileageType === null || [0, 1].includes(record.effectMileageFrame) ? (
                  ''
                ) : (
                  <Tag className={[styles[beginMileageTypeMap[record.beginMileageType]?.style], styles['ml5']]}>
                    {beginMileageTypeMap[record.beginMileageType]?.name}
                  </Tag>
                )}
              </div>
            );
          }}
        />
        <Table.Column
          title="权益结束里程"
          key="fixedEndMileage"
          dataIndex="fixedEndMileage"
          width={170}
          render={(text, record) => {
            // return <div>{effectMileageFrameMap[record.effectMileageFrame]}{record.fixedEndMileage?record.fixedEndMileage:''} </div>
            if (record.effectMileageFrame === null) {
              return '-';
            }
            return (
              <div>
                {effectMileageFrameMap[record.effectMileageFrame]}
                {record.fixedEndMileage === null ? '-' : record.fixedEndMileage}{' '}
              </div>
            );
          }}
        />
        {strategy === 'out' ? null : (
          <Table.Column
            title="延保激活时间"
            key="activationTime"
            dataIndex="activationTime"
            width={120}
            render={text => {
              return text ? moment(text).format('YYYY年MM月DD日 HH:mm:ss') : '-';
            }}
          />
        )}
        <Table.Column
          title="权益类别"
          key="typeCodeName"
          dataIndex="typeCodeName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="车辆属性"
          key="carAttrName"
          dataIndex="carAttrName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益归属"
          key="belongName"
          dataIndex="belongName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="享有权益频次"
          key="frequency"
          dataIndex="frequency"
          width={120}
          render={text => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限次数' : text + '次';
            } else {
              a = '-';
            }
            return a;
          }}
        />
        <Table.Column
          title="车辆权益变更属性"
          key="identityName"
          dataIndex="identityName"
          width={140}
          render={text => text || '-'}
        />
        <Table.Column
          title="享有车主身份"
          key="carIdentityName"
          dataIndex="carIdentityName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="商品分类"
          key="goodsCategoryName"
          dataIndex="goodsCategoryName"
          width={140}
          render={text => text || '-'}
        />
        {
          //type: 基础==base;付费==pay;赠送==give;精品==boutique;增值==gain;驾行==driver
          (type === 'pay' || type === 'give' || type === 'gain' || type === 'driver') && (
            <>
              <Table.Column
                title="激活单号"
                key="activationNo"
                dataIndex="activationNo"
                width={140}
                render={text => text || '-'}
              />
              <Table.Column
                title="零售订单号"
                key="saleOrderNo"
                dataIndex="saleOrderNo"
                width={140}
                render={text => text || '-'}
              />
              <Table.Column
                title="唯一核销码"
                key="uniqueVerificationCode"
                dataIndex="uniqueVerificationCode"
                width={140}
                render={text => text || '-'}
              />
            </>
          )
        }
        {
          // 精品权益
          type === 'boutique' ? (
            <>
              <Table.Column
                title="权益所属"
                key="goodsTypeName"
                dataIndex="goodsTypeName"
                width={140}
                render={text => text || '-'}
              />
              <Table.Column
                title="华为权益活动编码"
                key="hwActivityCode"
                dataIndex="hwActivityCode"
                width={140}
                render={text => text || '-'}
              />
              <Table.Column
                title="华为权益活动名称"
                key="hwActivityName"
                dataIndex="hwActivityName"
                width={140}
                render={text => text || '-'}
              />
              <Table.Column
                title="华为权益明细编码"
                key="hwDetailNo"
                dataIndex="hwDetailNo"
                width={140}
                render={text => text || '-'}
              />
              <Table.Column
                title="权益销售备注"
                key="hwRemark"
                dataIndex="hwRemark"
                width={140}
                render={text => text || '-'}
              />
            </>
          ) : null
        }
        <Table.Column
          title="全保养阶梯策略"
          key="maintenanceLadderFrame"
          dataIndex="maintenanceLadderFrame"
          width={200}
          render={(text, record) => {
            if (text == 0) {
              return '-';
            }
            // return <>{record?.maintenanceLadder}次<Button type='link' size='small' onClick={() => getLadderData(record)}>详情</Button></>
            return <>{record?.maintenanceLadder}次</>;
          }}
        />
        {/* v12.0 */}
        <Table.Column
          title="补漆面部位"
          key="relativeTime"
          dataIndex="relativeTime"
          width={220}
          render={(text, record) => {
            if (record?.paintFrame == 0) {
              return '-';
            }
            if (record?.paintFrame == -1) {
              return '不限面数';
            }
            const filter = record?.paintList?.filter(i => i.num > 0) || [];
            const str =
              filter.map(item => {
                return `${item.name}*${item.num} `;
              }) || [];
            return str.join('，') + `， 共${record?.paintFrameLadder}面`;
          }}
        />
        <Table.Column
          title="车联网流量"
          key="traffic"
          dataIndex="traffic"
          width={120}
          render={(text, record) => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限流量' : text + record.trafficUnit;
            } else {
              a = '-';
            }
            return a;
            // return record.traffic  && record.trafficUnit ? record.traffic + ' ' + record.trafficUnit : '不限流量'
          }}
        />
        <Table.Column
          title="权益抵扣金额"
          key="deduction"
          dataIndex="deduction"
          width={200}
          render={(text, record) => {
            return record.payAmount && record.deduction ? (
              <span>
                {record.payAmount} 元 ~ {record.deduction} 元
              </span>
            ) : (
              '-'
            );
          }}
        />
        {/* v12.0 */}
        <Table.Column
          title="权益履约方"
          key="performingList"
          dataIndex="performingList"
          width={150}
          render={(text, record) => {
            if (record.performingList) {
              const str = record.performingList?.map(item => item.name) || [];
              return <PublicTooltip title={str.join('，')}>{str.join('，')}</PublicTooltip>;
            } else {
              return '-';
            }
          }}
        />
        {/* v12.0 */}
        <Table.Column
          title="履约结算价格"
          key="settlementPrice"
          dataIndex="settlementPrice"
          width={120}
          render={(text, record) => {
            if (record.settlementPriceFrame == 0 || !record.settlementPriceFrame) {
              return '-';
            } else if (record.settlementPriceFrame == 1) {
              // 实际结算
              return record.settlementPriceFrameName;
            }

            return `${text}元`;
          }}
        />
        {/* 延保承保方 */}
        <Table.Column
          title="延保承保方"
          key="extendWarrantyAssurer"
          dataIndex="extendWarrantyAssurer"
          width={120}
          render={(text, record) => {
            return extendWarrantyAssurerMap[text] || '-';
          }}
        />
        <Table.Column
          title="是否支持退款"
          key="refundName"
          dataIndex="refundName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="是否三方履约"
          key="performanceName"
          dataIndex="performanceName"
          width={120}
          render={text => text || '-'}
        />

        {/* <Table.Column title='权益支持区域' key='provinceName' dataIndex='provinceName' width={200} ellipsis render={(text, record) => {
                    let str = ''
                    if (record.areas && record.areas.length) {
                        let obj = record.areas[0]
                        str = obj?.provinceName + ' ' + obj?.cityName + ' ' + obj?.areaName
                    } else {
                        str = '-'
                    }
                    return <div>
                        {
                            record.allArea == '0' ? '全部区域' : <PublicTooltip placement="topLeft" title={str}>{str}</PublicTooltip>
                        }
                    </div>
                }} />
                <Table.Column title='权益支持门店' key='dealersArr' dataIndex='dealersArr' width={200} ellipsis={true} render={(text, record) => {
                    let temp = []
                    if (record.dealers && record.dealers.length) {
                        record.dealers.forEach(item => {
                            temp.push(item.dealerCodeName)
                        })
                    } else {
                        temp = ['-']
                    }
                    return <div>
                        {
                            record.allDealer == '0' ? '全部门店' : <PublicTooltip placement="topLeft" title={temp.join(',')}>{temp.join(',')}</PublicTooltip>
                        }
                    </div>
                }} /> */}
        {['in', 'same'].includes(strategy) ? (
          <Table.Column
            title="能否重复购买"
            key="isRepeatablePurchaseName"
            dataIndex="isRepeatablePurchaseName"
            width={120}
            render={text => text || '-'}
          />
        ) : null}

        <Table.Column
          title="权益声明"
          key="statement"
          dataIndex="statement"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="使用说明"
          key="explanation"
          dataIndex="explanation"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益备注"
          key="remark"
          dataIndex="remark"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="客户端展示形式"
          key="displayFrame"
          dataIndex="displayFrame"
          width={120}
          ellipsis
          render={(text, record) => {
            const obj = {
              1: '并列',
              2: '聚合',
              3: '组合',
            };
            return text ? obj[text] : '-';
          }}
        />
        <Table.Column
          title="是否在客户端展示"
          key="isDisplay"
          dataIndex="isDisplay"
          width={150}
          ellipsis
          render={(text, record) => {
            const obj = {
              1: '在客户端展示',
              2: '不在客户端展示',
            };
            return record.isDisplay ? obj[record.isDisplay] : '-';
          }}
        />

        {children}
      </Table>

      {isLadderVisible && (
        <LadderDetail
          handleLadderCancle={handleLadderCancle}
          isLadderVisible={isLadderVisible}
          ladderData={ladderData}
        />
      )}
    </>
  );
};
export default memo(PublicTable);
