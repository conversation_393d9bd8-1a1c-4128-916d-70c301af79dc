import React, { useState, useEffect } from 'react'
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import '@wangeditor/editor/dist/css/style.css'
import { message } from 'antd'
import Cookies from 'js-cookie'

export default function MyEditor(props) {
    const { cb,url,defaultHtmlCon} = props
    const [editor, setEditor] = useState(null) // 存储 editor 实例
    const [html, setHtml] = useState('')

    // 模拟 ajax 请求，异步设置 html
    useEffect(() => {

    }, [])
    const handleChange = (editor) => {
        setHtml(editor.getHtml())
        if(cb) {
            cb({
                htmlCon:editor.getHtml(),
                textCon:editor.getText()
            })
        }
    }
    const toolbarConfig = {}
    const myUploadFn = (param,insertFn,type)=>{
        const serverURL = url
        const xhr = new XMLHttpRequest
        const fd = new FormData()
        const successFn = (response) => {
            // 假设服务端直接返回文件上传后的地址
            // 上传成功后调用param.success并传入上传后的文件地址
            if(JSON.parse(xhr.responseText).success){
                let res={
                    url: JSON.parse(xhr.responseText).resp[0],
                }
                insertFn(res.url,type)
            }else{
                message.error('上传失败！')
            }
        }
        const errorFn = (response) => {
            // 上传发生错误时调用param.error
            message.error('上传失败！')
        }
        xhr.addEventListener("load", successFn, false)
        xhr.addEventListener("error", errorFn, false)
        xhr.addEventListener("abort", errorFn, false)

        fd.append('file', param)
        xhr.open('POST', serverURL, true)
        xhr.setRequestHeader("appid", "1");
        xhr.setRequestHeader("Authorization", Cookies.get('scrm_token'));
        xhr.send(fd)
    };

    const editorConfig = {
        placeholder: '请输入内容...',
        scroll: true,

        // 继续其他配置
        MENU_CONF: {            // 配置上传图片
            uploadImage: {
                 // 自定义图片上传
                async customUpload(file, insertFn) {
                    myUploadFn(file,insertFn,'image')
                }
            },
            uploadVideo:{
                //自定义视屏上传
                async customUpload(file, insertFn) {
                    myUploadFn(file,insertFn,'video')
                }
            }

            // 继续其他菜单配置
        }

    }
    editorConfig.onChange = (editor) => {            // JS 语法
        // editor changed
        //当前文本的获取,获取纯文本可以使用getText
        //console.log(editor.getHtml());
        localStorage.setItem("editor", editor.getHtml())

    }
    useEffect(()=>{
        setHtml(defaultHtmlCon)
    },[defaultHtmlCon])
    // 及时销毁 editor ，重要！
    useEffect(() => {
        return () => {
            if (editor == null) return
            editor.destroy()
            setEditor(null)
        }
    }, [editor])
    return (
        <>
            <div style={{ border: '1px solid #ccc', zIndex: 100 }}>
                <Toolbar
                    editor={editor}
                    defaultConfig={toolbarConfig}
                    mode="default"
                    style={{ borderBottom: '1px solid #ccc' }}
                />
                <Editor
                    defaultConfig={editorConfig}
                    value={html}
                    onCreated={setEditor}
                    onChange={editor=>handleChange(editor)}
                    mode="default"
                    style={{ height: '500px', overflowY: 'hidden' }}
                />
            </div>
        </>

    )
}
