import React, { useEffect, useState, memo, useContext } from 'react';
import { InjectContext } from '../CreateForm';
import StrategyPolymerization from './StrategyPolymerization';
import StrategyFormStyle from './strategyForm.module.less';
import { endTimeRender, mileageEndRender } from '../publicMethods';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Radio,
  Space,
  Modal,
  message,
  Table,
  Row,
  Col,
  DatePicker,
} from 'antd';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

const StrategyForm = props => {
  const { form, usable, entryList, formData, ...rest } = useContext(InjectContext);
  const [limitYearRadioDisplay, setLimitYearRadioDisplay] = useState(''); // 内外一致-权益限制年限
  const [timeRadioChangeDisplay, setTimeRadioChangeDisplay] = useState(1); // 内外一致-权益生效时间
  const [endTime, setEndTime] = useState();
  const [endTimeStr, setEndTimeStr] = useState();
  const [mileageEndStr, setMileageEndStr] = useState();
  const [limitLegendDisplay, setLimitLegendDisplay] = useState(0); // 对外-权益限制里程策略
  const [relativeStrategyTips, setRelativeStrategyTips] = useState(0); //控制相对里程结束里程的提示文字显示 0:不显示，1:总里程和，2:里程策略
  const [effectMileageFrameFlag, setEffectMileageFrameFlag] = useState(0); //内外一致-权益生效里程
  const [fixedStrategyTips, setFixedStrategyTips] = useState(0); //控制固定里程结束里程的提示文字显示  0:不显示，1:总里程和，2:里程策略
  const [fixedStartStrategyTips, setFixedStartStrategyTips] = useState(0); //控制固定里程开始里程的提示文字显示  0:不显示，12:总前权益的相对结束里程，11:前权益的实际结束里程
  const [showStyle, setShowStyle] = useState(1); //客户端展示形式
  const { strategyConfig, originDetail } = formData;

  // const RELATIVE_MILEAGE_OPTIONS = [{
  //     label: '前权益的相对结束里程',
  //     remark: '前一个权益的相对开始里程+权益限制里程（基准）作为延保权益的开始里程',
  //     value: 12,
  // }, {
  //     label: '前权益的实际结束里程',
  //     remark: '前一个权益因时间到期、里程超出、次数用完原因失效时刻的里程值',
  //     value: 11,
  // }]

  const END_MILEAGE_OPTIONS = [
    {
      label: '计算到总里程和',
      value: 1,
    },
    {
      label: '计算到里程策略',
      value: 2,
    },
  ];
  useEffect(() => {
    // 根据前权益设置当前默认选项
    if (originDetail && Object.keys(originDetail).length) {
      // 如果当前是编辑状态
      if (sessionStorage.getItem('createType') === 'edit') {
        const _data = rest.dataSource;
        if (_data) {
          // 设置延保信息
          const _detailDisplay = _data.detailDisplay;
          // form.setFieldsValue(_data)

          init(_data);
          // 设置change事件的默认值
          startMileageStrategyChange(_data.relativeBeginMileageStrategy);
          endMileageStrategyChange(_data.relativeEndMileageStrategy);
          endTimeChange(_data.relativeTime);
          mileageEndStrFn();
          // 聚合展现形式在这里设置，其余在聚合组件中设置
          selectShowStyle(_detailDisplay.displayFrame);
        }
      } else {
        init(originDetail);
      }
    }
  }, [originDetail]);

  const init = detail => {
    // console.log('detail', detail)

    const { limitTimeFrame, limitLegendFrame } = detail;
    form.setFieldsValue({
      limitTimeFrame,
      timeRadio: detail.relativeTime ? 0 : 1,
      limitLegendFrame,
      // limitYear, limitMonth, limitDay
    });

    // 权益限制年限
    limitYearRadioChange(detail.limitTimeFrame);
    // 权益生效时间
    timeRadioChange(detail.relativeTime ? 0 : 1);
    // 权益限制里程
    mileageRadioChange(detail.limitLegendFrame);
    // 前权益配置了权益限制里程（基准）为行驶里程、增程器里程时，延保权益的延保生效里程无法选择「无此策略」（无此策略选项隐藏）
    // console.log(detail.effectMileageFrame);

    // if (detail.limitLegendFrame != 0 && !detail.effectMileageFrame) {
    //     form.setFieldValue('effectMileageFrame', 2)
    //     //权益生效里程
    //     effectMileageFrameRadioChange(2)
    // } else {
    //权益生效里程
    // form.setFieldValue('effectMileageFrame', detail.effectMileageFrame)
    // effectMileageFrameRadioChange(detail.effectMileageFrame)
    // }

    // 如果权益生效里程选择了固定，则手动改为相对
    if (Number(detail.effectMileageFrame) === 1) {
      effectMileageFrameRadioChange(2);
      form.setFieldValue('effectMileageFrame', 2);
    } else {
      form.setFieldValue('effectMileageFrame', detail.effectMileageFrame);
      effectMileageFrameRadioChange(detail.effectMileageFrame);
    }
  };

  // 延保激活时间
  const activationTimeChange = val => {};
  // 权益限制年限
  const limitYearRadioChange = value => {
    setLimitYearRadioDisplay(value);
    if (value === 0) {
      form.setFieldValue('timeRadio', 1);
      timeRadioChange(1);
    } else {
      form.setFieldValue('timeRadio', 0);
      timeRadioChange(0);
    }
  };

  // 权益生效时间
  const timeRadioChange = value => {
    setTimeRadioChangeDisplay(value);

    if (!form.getFieldValue('relativeTime')) {
      form.setFieldValue('relativeTime', 12);
      endTimeChange(12);
    } else {
      endTimeChange(form.getFieldValue('relativeTime'));
    }
    // formRef.current.resetFields(['relativeTime','fixedRelativeTime'])
  };
  const endTimeChange = value => {
    let currentFormData;
    let _detail;
    const { relativeTime } = form.getFieldsValue();

    if (sessionStorage.getItem('createType') === 'edit') {
      _detail = rest.dataSource;
    } else {
      // 如果没有修改过可以用前权益的值
      // console.log(form.getFieldsValue());
      if (!relativeTime) {
        form.setFieldsValue({
          limitYear: 0,
          limitMonth: 0,
          limitDay: 0,
        });
        _detail = { ...originDetail, limitYear: 0, limitMonth: 0, limitDay: 0 };
      } else {
        _detail = originDetail;
      }
    }

    currentFormData = relativeTime ? form.getFieldsValue() : _detail;

    setEndTime(value);
    setEndTimeStr(endTimeRender({ value, originDetail, currentFormData, entryList, showTips: true }));
  };

  // 权益限制里程策略
  const mileageRadioChange = value => {
    setLimitLegendDisplay(value);
    //v13版本需求：若权益限制里程选择无此策略，权益生效里程固定选择无此策略
    if (!value) {
      form.setFieldValue('effectMileageFrame', 0);
      setEffectMileageFrameFlag(0);
      //重置权益生效里面的一些选项
      form.setFieldValue('relativeBeginMileageStrategy', null);
      form.setFieldValue('relativeEndMileageStrategy', null);
      form.setFieldValue('fixedBeginMileage', null);
      form.setFieldValue('fixedEndMileageStrategy', null);
      setRelativeStrategyTips(0);
      setFixedStrategyTips(0);
    }
  };

  //权益生效里程change
  const effectMileageFrameRadioChange = val => {
    setEffectMileageFrameFlag(val);
    if (val === 1) {
      endTipsShow(val, form.getFieldValue('fixedEndMileageStrategy') ?? props?.dataSource?.['fixedEndMileageStrategy']);
      form.setFieldValue('relativeBeginMileageStrategy', null);
      form.setFieldValue('relativeEndMileageStrategy', null);
    } else if (val === 2) {
      endTipsShow(
        val,
        form.getFieldValue('relativeEndMileageStrategy') ?? props?.dataSource?.['relativeEndMileageStrategy']
      );
      form.setFieldValue('fixedBeginMileage', null);
      form.setFieldValue('fixedEndMileageStrategy', null);
    }
  };
  /**
   *
   * @param {*} radioVal 权益生效里程单选的值 1=延保固定里程， 2=延保相对里程
   * @param {*} selectVal 结束里程的值 1=总里程和， 2=里程策略
   */
  const endTipsShow = (radioVal, selectVal) => {
    if (radioVal === 2) {
      setRelativeStrategyTips(selectVal);
    } else {
      setRelativeStrategyTips(0);
    }
    if (radioVal === 1) {
      setFixedStrategyTips(selectVal);
    } else {
      setFixedStrategyTips(0);
    }
  };
  const startTipsShow = (radioVal, selectVal) => {
    if (radioVal === 2) {
      setFixedStartStrategyTips(selectVal);
    } else {
      setFixedStartStrategyTips(0);
    }
  };
  const startMileageStrategyChange = val => {
    startTipsShow(form.getFieldValue('effectMileageFrame'), val);
    mileageEndStrFn();
  };
  //权益生效里程：结束里程
  const endMileageStrategyChange = val => {
    endTipsShow(form.getFieldValue('effectMileageFrame'), val);
    mileageEndStrFn();
  };
  // 客户端展示形式
  const selectShowStyle = val => {
    setShowStyle(val);
  };
  const timeChange = () => {
    endTimeChange(form.getFieldValue('relativeTime'));
  };
  // 延保生效开始里程提示文案
  const mileageStartTips = () => {
    let tips = '';
    if (fixedStartStrategyTips === 12) {
      // 前权益的相对结束里程
      tips =
        '延保开始里程选择前权益的相对结束里程，则账户关联的延保生效里程为前一个权益策略配置的开始里程加上限制里程值作为延保权益开始里程';
    } else if (fixedStartStrategyTips === 11) {
      // 前权益的实际结束里程
      tips =
        '延保开始里程选择前权益的实际结束里程，则账户关联的延保生效里程为前一个权益因时间到期、里程超出、次数用完使权益失效那一刻的里程值';
    }
    return tips;
  };
  // 延保生效开始里程提示文案
  const mileageEndTips = () => {
    let tips = '';
    if (relativeStrategyTips === 1) {
      // 结束里程选择计算到里程和
      tips = '延保结束里程选择计算到里程和，延保结束里程根据所填写的限制里程（基准）加和计算';
    } else if (relativeStrategyTips === 2) {
      // 结束里程选择计算到里程策略
      tips = '延保结束里程选择计算到里程策略，延保结束里程为填写的限制里程（基准）';
    }
    return tips;
  };
  // 设置延保生效里程-结束里程
  const mileageEndStrFn = () => {
    let _detail;
    const { limitLegendFrame } = form.getFieldsValue();

    if (sessionStorage.getItem('createType') === 'edit') {
      _detail = rest.dataSource;
    } else {
      _detail = originDetail;
    }

    const currentFormData = limitLegendFrame ? form.getFieldsValue() : _detail;
    const str = mileageEndRender({ originDetail, currentFormData, entryList });

    setMileageEndStr(str);
  };

  return (
    <>
      {usable ? (
        <div className={StrategyFormStyle.StrategyForm}>
          <div style={{ height: 20, backgroundColor: '#F0F2F5' }}></div>
          <div
            style={{
              backgroundColor: '#D6EBFF',
              height: 50,
              paddingLeft: 32,
              paddingTop: 13,
              marginBottom: 30,
              fontSize: 16,
              color: 'rgba(0,0,0,0.85)',
              fontWeight: 500,
            }}
          >
            权益明细策略
          </div>
          <div className="strategy-title">
            <div className="left-title">延保生效策略</div>
          </div>
          <div style={{ paddingTop: '20px' }}>
            <Form.Item label="延保激活时间">
              <Row>
                <Col span={7}>
                  <Form.Item name="extendWarrantyActiveTime" style={{ marginTop: '6px' }} initialValue={11}>
                    <Radio.Group onChange={e => activationTimeChange(e.target.value)}>
                      <Space direction="vertical" size={20}>
                        {entryList['equity_extend_warranty_active_time'] &&
                        entryList['equity_extend_warranty_active_time'].length
                          ? entryList['equity_extend_warranty_active_time'].map((item, index) => (
                              <Radio value={Number(item.value)} disabled={item.value == 12}>
                                {item.name}
                                <span style={{ display: 'block', color: '#F5222D', fontSize: '12px' }}>
                                  {item.description}
                                </span>
                              </Radio>
                            ))
                          : null}

                        {/* <Radio value={2}>前权益的相对结束时间
                                                <span style={{ display: 'block', color: '#F5222D', fontSize: '12px' }}>前一个权益的相对开始时间+权益策略年限，作为延保权益的激活时间</span>
                                            </Radio> */}
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={17}>
                  <Space direction="vertical" size={18}>
                    <span style={{ color: '#F5222D', fontSize: '12px' }}>
                      若延保激活时间选择前权益的实际结束时间，则账户关联的延保权益将根据前一个权益因时间到期、里程超出、次数用完使权益失效那一刻时间，作为延保激活时间，例：前一个权益因里程超出在2024年1月1日00:00:00失效，则延保权益的激活时间为2024年1月1日00:00:00
                    </span>
                    <span style={{ color: '#F5222D', fontSize: '12px' }}>
                      若延保激活时间选择前权益的相对结束时间，则账户关联的延保权益将根据前一个权益策略配置的相对时间与权益限制年限的和，作为延保激活时间，例：前一个权益配置的是交付时间，权益限制年限为4年，则延保权益的激活时间为：交付时间+4年
                    </span>
                    <span style={{ color: '#F5222D', fontSize: '12px' }}>
                      单条明细的激活策略为此处设置的延保激活时间，绑定车辆后，同一类延保在不同权益包之间的激活顺序：基础&gt;付费&gt;增值&gt;赠送
                    </span>
                  </Space>
                </Col>
              </Row>
            </Form.Item>

            {/* 权益限制年限 */}
            <Form.Item label="延保限制年限" style={{ marginBottom: 0 }}>
              <Form.Item name="limitTimeFrame" style={{ display: 'inline-block' }} initialValue={0}>
                <Radio.Group
                  onChange={e => limitYearRadioChange(e.target.value)}
                  disabled={strategyConfig?.limitTimeFrame?.disabled}
                >
                  <Radio value={0} disabled={strategyConfig?.limitTimeFrame?.childrenDisabled1}>
                    无此策略{' '}
                  </Radio>
                  <Radio value={-1}>不限年数（延保为终身）</Radio>
                  <Radio value={1}>其他</Radio>
                </Radio.Group>
              </Form.Item>
              <>
                <Form.Item name="limitYear" style={{ display: 'inline-block', width: '90px' }} initialValue={0}>
                  <InputNumber
                    min={0}
                    style={{ width: 90 }}
                    disabled={limitYearRadioDisplay !== 1}
                    onChange={timeChange}
                  />
                </Form.Item>
                <span style={{ marginLeft: 10, marginRight: 10 }}>年</span>
                <Form.Item name="limitMonth" style={{ display: 'inline-block', width: '90px' }} initialValue={0}>
                  <InputNumber
                    min={0}
                    style={{ width: 90, lineHeight: '30px' }}
                    disabled={limitYearRadioDisplay !== 1}
                    onChange={timeChange}
                  />
                </Form.Item>
                <span style={{ marginLeft: 10, marginRight: 10, lineHeight: '30px' }}>月</span>
                <Form.Item name="limitDay" style={{ display: 'inline-block', width: '90px' }} initialValue={0}>
                  <InputNumber
                    min={0}
                    style={{ width: 90 }}
                    disabled={limitYearRadioDisplay !== 1}
                    onChange={timeChange}
                  />
                </Form.Item>
                <span style={{ marginLeft: 10, lineHeight: '30px' }}>日</span>
              </>
            </Form.Item>

            {/* 权益生效时间 */}
            <Form.Item
              label="延保生效时间"
              rules={[{ required: false }]}
              style={{ marginBottom: 0 }}
              className="prefix-red"
            >
              <Row>
                <Col span={3}>
                  {/* V13.0.4 权益限制年限选择“无此策略”，权益生效时间只可选择“固定时间”，权益限制年限选择“不限年限”或者“其他”，权益生效时间只可选择“相对时间” */}
                  <Form.Item
                    name="timeRadio"
                    style={{ display: 'inline-block', width: '200px', marginTop: '6px' }}
                    initialValue={1}
                  >
                    <Radio.Group onChange={e => timeRadioChange(e.target.value)}>
                      <Space direction="vertical" size={20}>
                        <Radio value={0} disabled={limitYearRadioDisplay == 0}>
                          延保开始时间
                          <div style={{ marginTop: endTimeStr ? '32px' : '26px' }}>延保结束时间 </div>
                          {/* {endTimeStr || '-'} */}
                          {/* <span>前权益开始时间：</span> 交付时间 + <span>前权益限制年限：</span><b>8</b>年<b>8</b>月<b>8</b>日 + <span>延保限制年限：</span><b>8</b>年<b>8</b>月<b>8</b>日 <font className='red-tips'>该结束时间只做展示，如需修改请修改以上时间相关内容，如延保限制年限</font> */}
                        </Radio>

                        <Radio
                          value={1}
                          style={{ marginTop: endTimeStr ? '36px' : '8px' }}
                          disabled={limitYearRadioDisplay != 0}
                        >
                          固定时间
                        </Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col>
                  <Space direction="vertical" align="center" size={16}>
                    <div style={{ position: 'relative' }}>
                      <Form.Item
                        name="relativeTime"
                        style={{ display: 'inline-block', width: '400px', margin: '0 8px' }}
                        rules={[{ required: timeRadioChangeDisplay === 0, message: '请选择延保权益生效时间！' }]}
                      >
                        <Select
                          style={{ display: 'inline-block', width: '380px' }}
                          allowClear
                          disabled={timeRadioChangeDisplay !== 0}
                          onChange={endTimeChange}
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                        >
                          {entryList['equity_relativeTime'] && entryList['equity_relativeTime'].length
                            ? entryList['equity_relativeTime']
                                .filter(i => i.businessScene == 2)
                                .map((item, index) => (
                                  <Option key={index} value={Number(item.value)}>
                                    {item.name}
                                    <span className={StrategyFormStyle.remark}>{item.description}</span>
                                  </Option>
                                ))
                            : null}
                          {/* <Option key={12} value={12}>前权益的相对结束时间
                                                    <span className={StrategyFormStyle.remark}>前一个权益的相对开始时间+权益策略年限，作为延保权益的开始时间</span>
                                                </Option>
                                                <Option key={11} value={11}>前权益的实际结束时间
                                                    <span className={StrategyFormStyle.remark}>前一个权益失效的那一刻时间，例如因时间到期、里程超出、次数用完</span>
                                                </Option> */}
                        </Select>
                      </Form.Item>
                      {endTime === 12 ? (
                        <span className={StrategyFormStyle.endTimeTips}>
                          前一个权益的相对结束时间作为延保权益的开始时间，例：前一个权益配置的是交付时间，权益限制年限为4年，则延保的开始时间为交付时间+4年
                        </span>
                      ) : endTime === 11 ? (
                        <span className={StrategyFormStyle.endTimeTips}>
                          若延保开始时间选择前权益的实际结束时间，则账户关联的延保生效时间将根据前一个权益因时间到期、里程超出、次数用完使权益失效那一刻时间，作为延保开始时间，例：前一个权益因里程超出在2024年1月1日00:00:00失效，则延保权益的开始时间为2024年1月1日00:00:00
                        </span>
                      ) : (
                        ''
                      )}
                    </div>
                    <div className="timeEndRadio">{endTimeStr || '-'}</div>
                    <div style={{ position: 'relative', marginTop: endTimeStr ? '70px' : '35px' }}>
                      <Form.Item
                        name="fixedRelativeTime"
                        style={{ display: 'inline-block', width: '400px' }}
                        rules={[{ required: timeRadioChangeDisplay === 1, message: '请选择权益生效时间！' }]}
                      >
                        <RangePicker
                          showTime
                          style={{ width: '380px' }}
                          placeholder={['权益开始时间', '权益结束时间']}
                          disabled={timeRadioChangeDisplay !== 1}
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                        />
                      </Form.Item>
                      <span
                        style={{
                          color: 'red',
                          fontSize: 12,
                          marginLeft: '-7px',
                          position: 'absolute',
                          whiteSpace: 'nowrap',
                          top: '8px',
                        }}
                      >
                        若选择固定时间，延保只在固定时间范围内生效，优先执行固定时间策略，激活以固定时间为准
                      </span>
                    </div>
                  </Space>
                </Col>
              </Row>
            </Form.Item>

            {/* 权益限制里程 */}
            <Form.Item label="延保限制里程（基准）" rules={[{ required: false }]} style={{ marginBottom: 0 }}>
              <Row>
                <Col span={3}>
                  <Form.Item
                    name="limitLegendFrame"
                    style={{ display: 'inline-block', width: '200px' }}
                    initialValue={0}
                  >
                    {/* disabled={strategyConfig?.limitLegendFrame?.disabled} */}
                    <Radio.Group onChange={e => mileageRadioChange(e.target.value)} className="limit-legend-radio">
                      <Space direction="vertical" align="start" size={24}>
                        {/* <Radio value={0}>无此策略</Radio>
                                            <Radio value={1}>行驶里程</Radio>
                                            <Radio value={2}>增程器里程</Radio> */}
                        {strategyConfig?.limitLegendFrame?.currentShow === 0 ? <Radio value={0}>无此策略</Radio> : ''}
                        {strategyConfig?.limitLegendFrame?.currentShow === 1 ? <Radio value={1}>行驶里程</Radio> : ''}
                        {strategyConfig?.limitLegendFrame?.currentShow === 2 ? <Radio value={2}>增程器里程</Radio> : ''}
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={15} style={{ width: 700 }}>
                  <Space direction="vertical" size={15}>
                    {strategyConfig?.limitLegendFrame?.currentShow === 0 ? (
                      <div style={{ height: 32 }}>
                        <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap', lineHeight: '32px' }}>
                          限制里程（基准）基于前权益生成，前权益配置无此策略，则延保策略默认为无此策略，无法修改
                        </span>
                      </div>
                    ) : (
                      ''
                    )}
                    {strategyConfig?.limitLegendFrame?.currentShow === 1 ? (
                      <Space>
                        <Form.Item name="limitLegend" noStyle>
                          <InputNumber
                            min={1}
                            placeholder="只能输入数字，例如：160000"
                            style={{ display: 'inline-block', width: '250px' }}
                            disabled={limitLegendDisplay !== 1}
                            onChange={mileageEndStrFn}
                          />
                        </Form.Item>
                        <span style={{ marginLeft: 6, whiteSpace: 'nowrap' }}>公里</span>
                        <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap' }}>
                          限制里程（基准）基于前权益生成，前权益配置行驶里程，则延保策略默认为行驶里程，无法修改
                        </span>
                      </Space>
                    ) : (
                      ''
                    )}
                    {strategyConfig?.limitLegendFrame?.currentShow === 2 ? (
                      <Space>
                        <Form.Item name="extenderMileage" noStyle>
                          <InputNumber
                            min={1}
                            placeholder="只能输入数字，例如：160000"
                            style={{ width: '250px' }}
                            disabled={limitLegendDisplay !== 2}
                            onChange={mileageEndStrFn}
                          />
                        </Form.Item>
                        <span style={{ marginLeft: 6, whiteSpace: 'nowrap' }}>公里</span>
                        <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap' }}>
                          限制里程（基准）基于前权益生成，前权益配置增程器里程，则延保策略默认为增程器里程，无法修改
                        </span>
                      </Space>
                    ) : (
                      ''
                    )}
                  </Space>
                </Col>
              </Row>
            </Form.Item>

            {/* 权益生效里程 */}
            <Form.Item label="延保生效里程" rules={[{ required: false }]} className="effectMileageFrameWrap">
              <Row>
                <Col span={3}>
                  <Form.Item className="effectMileageFrameCls" name="effectMileageFrame" initialValue={0}>
                    <Radio.Group onChange={e => effectMileageFrameRadioChange(e.target.value)}>
                      <Space direction="vertical" align="start" size={38}>
                        {strategyConfig?.effectMileageFrame?.childrenHidden1 ? '' : <Radio value={0}>无此策略</Radio>}
                        <Radio value={2} disabled={limitLegendDisplay === 0}>
                          相对里程
                        </Radio>
                        {/* <Radio value={1} disabled={limitLegendDisplay === 0}>固定里程</Radio> */}
                        <Radio value={1} disabled={true} style={{ paddingTop: '120px' }}>
                          固定里程
                          <span
                            style={{
                              color: 'red',
                              fontSize: '12px',
                              whiteSpace: 'nowrap',
                              position: 'absolute',
                              marginTop: '27px',
                              left: 0,
                            }}
                          >
                            因存在配置里程过大无法衔接风险，故暂不支持配置
                          </span>
                        </Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col style={{ width: 650 }}>
                  <Space direction="vertical" align="start" size={28} className="mileage">
                    {strategyConfig?.effectMileageFrame?.childrenHidden1 ? (
                      ''
                    ) : (
                      <div style={{ height: 32 }}>&nbsp;</div>
                    )}
                    <Space>
                      <Form.Item
                        name="relativeBeginMileageStrategy"
                        label="开始里程"
                        className="mb0 w350 mr15"
                        rules={[{ required: effectMileageFrameFlag === 2, message: '请选择' }]}
                      >
                        <Select
                          allowClear
                          placeholder="请选择"
                          disabled={effectMileageFrameFlag !== 2}
                          onChange={value => {
                            startMileageStrategyChange(value);
                          }}
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                        >
                          {/* Number(i.extendField1) === 2 && Number(i.extendField3) !== 2 延保需要的字典 && 不需要聚合的字典 */}
                          {entryList['equity_relative_begin_mileage_strategy'] &&
                          entryList['equity_relative_begin_mileage_strategy'].length
                            ? entryList['equity_relative_begin_mileage_strategy']
                                .filter(i => Number(i.extendField1) === 2 && Number(i.extendField3) !== 2)
                                .map(item => (
                                  <Option key={item.value} value={Number(item.value)}>
                                    {item.name}
                                    <span className={StrategyFormStyle.remark}>{item.description}</span>
                                  </Option>
                                ))
                            : ''}
                        </Select>
                      </Form.Item>
                      {<span className="tips relative-tips">{mileageStartTips()}</span>}
                    </Space>
                    <Space>
                      <Form.Item
                        name="relativeEndMileageStrategy"
                        label="结束里程"
                        className="mb0 w250"
                        rules={[{ required: effectMileageFrameFlag === 2, message: '请选择' }]}
                      >
                        <Select
                          allowClear
                          placeholder="请选择"
                          disabled={effectMileageFrameFlag !== 2}
                          onChange={value => {
                            endMileageStrategyChange(value);
                          }}
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                        >
                          {END_MILEAGE_OPTIONS.map(item => (
                            <Option key={item.value} value={item.value}>
                              {item.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                      {<span className="tips relative-tips">{mileageEndTips()}</span>}
                    </Space>
                    <Space>
                      <div className="mileageEnd">{mileageEndStr}</div>
                    </Space>

                    <Space>
                      <Form.Item
                        name="fixedBeginMileage"
                        label="开始里程"
                        className="mr15 w350"
                        rules={[{ required: effectMileageFrameFlag === 1, message: '请输入' }]}
                      >
                        <InputNumber
                          style={{ width: 270 }}
                          min={0}
                          placeholder="固定开始里程"
                          disabled={effectMileageFrameFlag !== 1}
                        />
                      </Form.Item>
                      <Form.Item
                        name="fixedEndMileageStrategy"
                        label="结束里程"
                        className="w250"
                        rules={[{ required: effectMileageFrameFlag === 1, message: '请选择' }]}
                      >
                        <Select
                          allowClear
                          placeholder="请选择"
                          disabled={effectMileageFrameFlag !== 1}
                          onChange={value => endMileageStrategyChange(value)}
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                        >
                          {END_MILEAGE_OPTIONS.map(item => (
                            <Option key={item.value} value={item.value}>
                              {item.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                      {fixedStrategyTips === 2 ? (
                        <span className="tips" style={{ width: 520, marginTop: -24 }}>
                          若开始里程选择固定里程，则所有账户关联的权益生效里程均以填写的该里程作为权益开始里程，结束里程选择计算到里程策略，则权益生效里程范围为：固定里程～权益限制里程（行驶里程/增程器里程），例：固定开始里程为5000，限制里程为10000，则权益有效里程为5000～10000
                        </span>
                      ) : null}
                      {fixedStrategyTips === 1 ? (
                        <span className="tips" style={{ width: 540, marginTop: -24 }}>
                          若开始里程选择固定里程，则所有账户关联的权益生效里程均以填写的固定里程作为权益开始里程，结束里程选择计算到总里程和，则权益生效里程范围为：固定里程～固定里程+权益限制里程（行驶里程/增程器里程），例：固定开始里程为5000，限制里程为10000，则权益有效里程为5000～15000；手动导入的数据若填写了开始里程，则开始里程以填写的为准，结束里程为明细策略配置的结束里程。
                        </span>
                      ) : null}
                    </Space>
                  </Space>
                </Col>
              </Row>
            </Form.Item>

            {/* 车辆权益变更属性 */}
            <Form.Item label="车辆权益变更属性" style={{ marginBottom: 0 }}>
              <Form.Item
                name="identity"
                rules={[]}
                initialValue={1}
                help={
                  <div style={{ color: 'red', fontSize: '12px', marginBottom: '20px' }}>该策略适用于二手车权益变更</div>
                }
              >
                <Radio.Group>
                  {entryList['equity_identity'] && entryList['equity_identity'].length
                    ? entryList['equity_identity'].map((item, index) => (
                        <Radio key={index} value={Number(item.value)}>
                          {item.name}
                        </Radio>
                      ))
                    : null}
                </Radio.Group>
              </Form.Item>
              {/* <span className="tips" style={{ width: 520, marginTop: -24, color: 'red', fontSize: 12 }}>
                该策略适用于二手车权益变更
              </span> */}
            </Form.Item>

            {/* 享有车主身份 */}
            <Form.Item name="carIdentity" label="享有车主身份" rules={[]} initialValue={2}>
              <Radio.Group>
                {entryList['equity_car_identity'] && entryList['equity_car_identity'].length
                  ? entryList['equity_car_identity'].map((item, index) => (
                      <Radio key={index} value={Number(item.value)}>
                        {item.name}
                      </Radio>
                    ))
                  : null}
              </Radio.Group>
            </Form.Item>
          </div>

          <div className="strategy-title">
            <div className="left-title">客户端展示策略</div>
          </div>
          <div className="clientShow">
            <div className="method">
              <Form.Item label="客户端展示形式" style={{ width: '1136px' }}>
                <Form.Item
                  name={['detailDisplay', 'displayFrame']}
                  initialValue={1}
                  style={{ display: 'inline-block', marginBottom: '0' }}
                >
                  <Radio.Group onChange={e => selectShowStyle(e.target.value)}>
                    <Radio value={1}>并列</Radio>
                    <Radio value={2}>聚合</Radio>
                  </Radio.Group>
                </Form.Item>
                {/* <Button type='link' style={{ color: '#FFA500' }}>示例展示?</Button> */}
                {showStyle === 2 ? (
                  <span style={{ color: '#F5222D', fontSize: '12px', lineHeight: '32px' }}>
                    当聚合展示时，前一条明细属于什么类型的权益包，则延保明细在客户端上会以同类型的权益包进行展示
                  </span>
                ) : (
                  ''
                )}
              </Form.Item>
            </div>

            {showStyle === 2 ? (
              <StrategyPolymerization />
            ) : (
              <div className="abreast">客户端展示形式选择并列，会在客户端上同时展示前权益和延保权益共2条权益明细</div>
            )}
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default memo(StrategyForm);
