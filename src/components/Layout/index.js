import React from "react";
import { Layout, Breadcrumb } from "antd";
import { connect } from "react-redux";
import "../../assets/style/index.less";
import "./index.less";
import Sider from "./Sider";
import Header from "./Header";
import { GetUserInfo } from "../../actions/async";
import { setSiderNormal } from "@/actions/normal";
import watermark from "@/utils/watermark";
import moment from "moment";

const { Content } = Layout;

class MainLayout extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      userInfo: {},
    };
  }
  componentWillMount() {
    if(!this.props.userInfo){
      this.props.dispatch(GetUserInfo()).then((v) => {
        this.setState({ userInfo: v.payload });
      });
    }
  }
  componentDidMount() {
    window.addEventListener("resize", this.handleResize);
  }
  componentDidUpdate(prevProps){
    if(prevProps.userInfo && prevProps.userInfo.id) {
      watermark.init({
        watermark_txt: `${prevProps.userInfo.userName} ${moment().format("YYYY-MM-DD HH:mm:ss")}`,
        watermark_alpha: 0.1,
        watermark_width: 400,
        watermark_color: "gray",
      });
    }
  }
  componentWillUnmount() {
    window.removeEventListener("resize", this.handleResize);
  }
  handleResize = (e) => {
    if (e.target.innerWidth <= 1200) {
      this.props.dispatch(setSiderNormal(true));
    } else {
      this.props.dispatch(setSiderNormal(false));
    }
  };
  render() {
    const { SiderType, TabList } = this.props;
    return (
      <div className="mainLayout">
        <Layout style={{ minHeight: "100vh" }}>
          <Header/>
          <div>
            <Sider />
            <Content
              className="Content_box"
              style={{ left: SiderType ? "80px" : "208px", right: 0 }}
            >
              <Breadcrumb>
                {TabList.length ? (
                  TabList.map((item) => {
                    return [<Breadcrumb.Item>{item.title}</Breadcrumb.Item>];
                  })
                ) : (
                  <Breadcrumb.Item>首页</Breadcrumb.Item>
                )}
              </Breadcrumb>
              <div className="Content-con">{this.props.children}</div>
            </Content>
          </div>
        </Layout>
      </div>
    );
  }
}
export default connect(({ common }) => {
  const { SiderType, TabList, userInfo, changePwdVisible } = common;
  return {
    SiderType,
    TabList,
    userInfo,
    changePwdVisible,
  };
})(MainLayout);
