import { getSeresFilePostFrequentDelete, postSeresFilePostFrequentPage } from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import ProTable, { ProTableActions } from '@/components/pro-table';
import UserPermissionAuthorize from '@/components/user-permission-authorize';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { commonJobManagePermissions } from '@/permissions/file';
import { message, Modal, Space, Typography } from 'antd';
import React, { FC, useCallback, useRef, useState } from 'react';
import { COMMON_JOB_TYPE_MAP } from '../constants';
import CommonJobModal, { CommonJobModalProps } from './common-job-modal';

/**
 * 常用岗位管理
 */
const CommonJobManage: FC = () => {
  const actionRef = useRef<ProTableActions>(null);
  const tableRequest = usePaginationTableListStyleRequest(async ({ current, pageSize }) =>
    postSeresFilePostFrequentPage({ pageNum: current, pageSize })
  );
  const [commonJobModal, setCommonJobModal] = useState<Pick<CommonJobModalProps, 'open' | 'data'>>({
    open: false,
  });

  const handleModalSuccess = useCallback(() => {
    setCommonJobModal({
      open: false,
    });
    actionRef.current?.reload();
  }, []);

  const handleAddClick = useCallback(() => {
    setCommonJobModal({
      open: true,
    });
  }, []);

  const handleEditClick = useCallback((data: FileManage.FrequentPostVO) => {
    setCommonJobModal({
      open: true,
      data,
    });
  }, []);

  const handleDeleteClick = useCallback((data: FileManage.FrequentPostVO) => {
    Modal.confirm({
      title: '删除常用岗位',
      content: `您正在删除常用岗位【${data.name}】，请确认是否删除`,
      onOk: async () => {
        try {
          await getSeresFilePostFrequentDelete({ id: data.id as number });
          actionRef.current?.reload();
          message.success('删除成功');
        } catch (error) {
          //
        }
      },
    });
  }, []);

  return (
    <PageContainer>
      <ProTable<FileManage.FrequentPost>
        request={tableRequest}
        rowKey="id"
        actionRef={actionRef}
        columns={[
          {
            key: 'index',
            valueType: 'index',
            title: '序号',
            fixed: 'left',
            width: 80,
          },
          {
            title: '类型',
            dataIndex: 'type',
            valueMap: COMMON_JOB_TYPE_MAP,
          },
          {
            title: '常用岗位名称',
            dataIndex: 'name',
          },
          {
            title: '岗位明细',
            dataIndex: 'nameList',
            ellipsis: true,
          },
          {
            title: '操作',
            fixed: 'right',
            width: 100,
            render: (_, record) => {
              return (
                <Space>
                  <UserPermissionAuthorize {...commonJobManagePermissions.edit}>
                    <Typography.Link onClick={() => handleEditClick(record)}>编辑</Typography.Link>
                  </UserPermissionAuthorize>
                  <UserPermissionAuthorize {...commonJobManagePermissions.delete}>
                    <Typography.Link type="danger" onClick={() => handleDeleteClick(record)}>
                      删除
                    </Typography.Link>
                  </UserPermissionAuthorize>
                </Space>
              );
            },
          },
        ]}
        actions={[
          {
            type: 'primary',
            children: '添加常用岗位',
            onClick: handleAddClick,
            permission: commonJobManagePermissions.add,
          },
        ]}
        actionPosition="toolbar"
      />
      <CommonJobModal
        {...commonJobModal}
        onSuccess={handleModalSuccess}
        onCancel={() => setCommonJobModal({ open: false })}
      />
    </PageContainer>
  );
};

export default CommonJobManage;
