import React from 'react'
import {Modal, message ,Form,Input, Button ,Spin} from 'antd'
import allUrl from '../../../../utils/url'
import {post} from '../../../../utils/request'
import {connect} from 'react-redux'
import { GetMenuList } from '../../../../actions/async'
import './index.less'
import {roleJudgment} from '@/utils/authority'
class OrgEdit extends React.PureComponent{
    constructor(props){
        super(props)
        this.state = {
            MentData:{},
            loading:false
        }
        this.MenuID = ''
        this.ParentID = ''
    }
    formRef = React.createRef();
    handleOk = () =>{
        const {onCancel,treeSelectedRows,GetData} = this.props
        this.formRef.current.validateFields().then(values=>{
            if(values){
                let data = {
                    id:treeSelectedRows[0].key,
                    parent:treeSelectedRows[0].parentId,
                    name:values.name,
                    code:values.code,
                    icon:values.icon,
                    path:values.path
                    
                }
                post(allUrl.Authority.updateMenu,data).then(res=>{
                    if(res.success){
                        message.success(res.msg)
                        GetData()
                        this.props.dispatch(GetMenuList())
                        onCancel()
                    }else{
                        // message.error(res.msg)
                    }
                })
            }
        })
    }
    Del = () =>{
        const {onCancel,treeSelectedRows,GetData} = this.props
        post(allUrl.Authority.deleteMenu + '/' + treeSelectedRows[0].key,null).then(res=>{
            if(res.success){
                message.success(res.msg)
                GetData()
                this.props.dispatch(GetMenuList())
                onCancel()
            }else{
                // message.error(res.msg)
            }
        })
    }

    ininForm = () =>{
        const {treeSelectedRows} = this.props
        setTimeout(()=>{
            this.formRef.current.setFieldsValue({
                name:treeSelectedRows[0].title,
                code:treeSelectedRows[0].code,
                path:treeSelectedRows[0].path,
                icon:treeSelectedRows[0].icon,
            })
        },200)
        
    }
    render(){
        const {visible,onCancel,title,userInfo} = this.props
        const {loading} = this.state
        const layout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 18 },
        };
        return(
            <Modal
                visible={visible}
                title={title}
                onOk={this.handleOk}
                onCancel={onCancel}
                maskClosable={false}
                footer={false}
                // width={600}
                bodyStyle={{padding:'50px'}}
                
            >
                <Spin spinning={loading}>
                    <Form
                        {...layout}
                        name="vertical"
                        onFinish={this.onFinish}
                        ref={this.formRef}
                        className='MenuEdit'
                        >
                        <Form.Item
                            label="菜单名称"
                            name="name"
                            rules={[{ required: true, message: '请填写菜单名称！' }]}
                        >
                            <Input autoComplete="off" allowClear />
                        </Form.Item>
                        <Form.Item
                            label="菜单编码"
                            name="code"
                            rules={[{ required: true, message: '请填写菜单编码！' }]}
                        >
                            <Input autoComplete="off" allowClear />
                        </Form.Item>
                        <Form.Item
                            label="Path"
                            name="path"
                            rules={[{ required: true, message: '请填写Path！' }]}
                        >
                            <Input autoComplete="off" allowClear />
                        </Form.Item>
                        <Form.Item
                            label="Icon"
                            name="icon"
                            rules={[{ required: false, message: '请填写Icon！' }]}
                        >
                            <Input autoComplete="off" allowClear />
                        </Form.Item>
                        <Form.Item
                            label={<span>菜单删除限制</span>}
                            rules={[{ required: false}]}
                        >
                            <p>1、有下级菜单的菜单无法删除；</p>
                        </Form.Item>
                        <div className='btns'>
                            <Button type="primary" onClick={this.handleOk}>保存</Button>
                            {
                                roleJudgment(userInfo,'PERM_MENU_DELETE') ?
                                    <Button onClick={this.Del}>删除</Button>
                                :null
                            }
                        </div>
                    </Form>
                </Spin>
            </Modal>
        )
    }
    componentDidMount(){
        this.ininForm()
    }
    
}
export default connect()(OrgEdit)