import React, { useEffect, useImperativeHandle, forwardRef, useState, useRef } from "react";
import { useSelector } from "react-redux";
import "../Staff.less";
import "./ChatRow.less";
import {
  Modal,
  Button,
  Avatar,
  Card,
  Image,
  message,
} from "antd";
import { UserOutlined } from "@ant-design/icons";
import AmrPlayer from "./AmrPlayer";
import { post } from "../../../../utils/request";
import allUrl from "../../../../utils/url";
import Cookies from 'js-cookie'
import { roleJudgment } from "@/utils/authority";


const MEDIA_STATUS = {
  DOADING: 0,
  SUCCESS: 1,
  FAIL: 2,
  NO_NEED: 3,
};
const NightChatRow = forwardRef((props, ref) => {
  const { userInfo } = useSelector((state) => state.common);
  const listRef = React.createRef();
  const amrPlayerRef = useRef();
  const videoRef = useRef();
  const [imageOssUrl, setImageOssUrl] = useState('')
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [visible2, setVisible2] = useState(false);
  const [clearAll, setClearAll] = useState(false);
  const [voiceUrl, setVoiceUrl] = useState('')
  const [videoUrl, setVideoUrl] = useState('')
  // record 接收客户检索传入的信息，其中beginMsgId作为首条，endMsgId作为最后一条数据，需要标红
  const { list, qwUserId, beginId, endId, pageSpin, setPageSpin } = props;
  const onContextMenuHanlder = (e) => {
    e.preventDefault()
    return false
  }
  
  const geFiletOssUrl = (row) => {
    getMediaUrlByFileId(row, (data) => {
      let blob = new Blob([data], {type: data.type});
      if (window.navigator.msSaveOrOpenBlob) {
        //兼容ie
        window.navigator.msSaveBlob(blob, row.filename || '未知文件');
      } else {
          let downloadElement = document.createElement('a');
          let href = window.URL.createObjectURL(blob); //创建下载的链接
          downloadElement.href = href;
          downloadElement.download = row.filename || '未知文件'; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放掉blob对象
      }
    })
  }
  /**
   * 根据参数获取访问多媒体文件的权限，然后再二次请求获取多媒体资源
   * 飞书文档 https://k3nofaut8f.feishu.cn/docx/Oy9Jd365CofF4ex7sIEc4ClKnHd
   * 接口文档 https://yapiprd.seres.cn/project/52/interface/api/11437
   * @param {*} row 参数对象
   * @param {*} fn  回调函数
   */
  // 
  const getMediaUrlByFileId = (row, fn) => {
    post(allUrl.WeCom.NightgetMediaUrlByFileId, { msgid: row.msgId, fileId: row.sdkfileid }).then((res) => {
      if (res.success && res.resp[0]) {
        getOssBuffer(res.resp[0], fn)
        } else {
          // message.error(res.msg);
        }
    });
  }
  const getVideoOssUrl = (row) => {
    setPageSpin(true)
    setClearAll(false)
    getMediaUrlByFileId(row, (data) => {
      // let blob = new Blob([data],{type: data.type});
      console.log('视频完成2', pageSpin, data)
      setVideoUrl(window.URL.createObjectURL(data))
      setPageSpin(false)

    })
  }

  const getVoiceOssUrl = (row) => {
    setPageSpin(true)
    setClearAll(false)
    getMediaUrlByFileId(row, (data) => {
      console.log('音频完成2', pageSpin, data)
      // var wavBlob = new Blob([ data ], {type: data.type});
      setVoiceUrl(window.URL.createObjectURL(data))
      setPageSpin(false)

    })
  }

  const getImageOssUrl = (row) => {
      setPageSpin(true)
      setClearAll(false)
      getMediaUrlByFileId(row, (data) => {
      setImageOssUrl(window.URL.createObjectURL(data))
      setPageSpin(false)

    })
  }
  // 获取资源
 /**
  * 
  * @param {*} url 后台返回的请求地址
  * @param {*} fn 回调函数
  */
  const getOssBuffer = (url,fn) => {
    var xhr = new XMLHttpRequest();
    xhr.responseType = "blob";
    xhr.open('get',url,true);
    xhr.setRequestHeader("x-oss-meta-authorization", Cookies.get('scrm_token'));
    xhr.setRequestHeader("authorization", Cookies.get('scrm_token'));
    xhr.onreadystatechange = function(){
        if(xhr.readyState == 4 && xhr.status == 200){
          console.log('xhr', xhr)
          fn(xhr.response)
        } else {
            return false;
        }
    };
    xhr.send();

   
  }
  const hideVoiceModal = () => {
    console.log('hide voice')
    setVisible1(false)
    setVoiceUrl('')
  }
  
  const hideVideoModal = () => {
    console.log('hide video')

    setVisible2(false)
    setVideoUrl('')
  }
  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    closeSpin: () => { // 暴露给父组件的方法
      setPageSpin(false)
      setClearAll(true)
    }
  }));
  useEffect(() => {
    if(clearAll) {
      setVideoUrl('')
      setVoiceUrl('')
      setImageOssUrl('')
    }
    console.log('useEffect clearAll', clearAll)

  }, [clearAll]);
  useEffect(() => {
    if(voiceUrl && pageSpin) {
        setVisible1(true)
    }
    if(videoUrl && pageSpin) {
        setVisible2(true)
    }
    if(imageOssUrl && pageSpin) {
        setVisible(true)
    }
    console.log('useEffect-pageSpin', pageSpin)
  }, [voiceUrl, videoUrl, imageOssUrl, pageSpin]);
  useEffect(() => {
    console.log('useEffect visible1', visible1)
  }, [visible1]);
  useEffect(() => {
    console.log('useEffect visible2', visible2)
  }, [visible2]);

  // 渲染列表
  const renderRow = (arr) => {
    return arr && arr.length
      ? arr.map((row, index) => {
          // const hasOssUrlTypes = ["emotion", "image", "vioce", "video", "file"];
          // 消息类型为以上几种类型时，可能存在媒体文件下载中或下载失败的情况
          // 如果mediaOssUrl为空，则不展示该条消息
          // if (hasOssUrlTypes.indexOf(row.msgType) !== -1 && !row.mediaOssUrl) {
          //   return false;
          // }
          return (
            <div key={row.msgId} className={`${row.fromId === qwUserId ? "flex-end" : "flex"} row`}>
              {
                // 非本人，头像在左边；混合消息不显示头像
                row.fromId !== qwUserId ? (
                  <div className="avatar">
                    <Avatar src={row.fromNameAvatar} icon={<UserOutlined />} />
                  </div>
                ) : null
              }
              <div className={row.fromId === qwUserId ? "detail text-align-right" : "detail"}>
                {row.fromId !== qwUserId ? (
                  <div className="userInfo flex">
                    <div
                      className={`from-name ellipsis mr10 ${
                        row.msgId == beginId || row.msgId == endId ? "bg-red" : ""
                      }`}
                    >
                      {row.fromName || "未知昵称"}
                      { // fromPosition有值则为内部员工，显示内部员工岗位
                        row.fromPosition ? <span className="text-blue">{row.fromPositionName ? `@${row.fromPositionName}` : ''}</span>
                        : null
                      }
                      { // 发送消息人外部联系人（微信），以wo或wm开头 
                        // 企微文档：https://developer.work.weixin.qq.com/document/path/91774#%E6%B6%88%E6%81%AF%E6%A0%BC%E5%BC%8F
                        row.fromId && (row.fromId.startsWith('wo') || row.fromId.startsWith('wm')) ? <span className="text-green">@微信</span> 
                        : null
                      }
                      { // 发送消息人为机器人，以wb开头
                        row.fromId && row.fromId.startsWith('wb') ? <span className="text-blue">@机器人</span> 
                        : null
                      }
                    </div>
                    <div
                      className={`msgTime ${
                        row.msgId == beginId || row.msgId == endId ? "bg-red" : ""
                      }`}
                    >
                      {row.msgTime || "未知时间"}
                    </div>
                  </div>
                ) : (
                  <div className="userInfo flex-right">
                    <div
                      className={`msgTime mr10 ${
                        row.msgId == beginId || row.msgId == endId ? "bg-red" : ""
                      }`}
                    >
                      {row.msgTime || "未知时间"}
                    </div>
                    <div
                      className={`from-name ellipsis mr10 ${
                        row.msgId == beginId || row.msgId == endId ? "bg-red" : ""
                      }`}
                    >
                      {row.fromName || "未知昵称"}
                      { // fromPosition有值则为内部员工，显示内部员工岗位
                        row.fromPosition ? <span className="text-blue">{row.fromPositionName ? `@${row.fromPositionName}` : ''}</span>
                        : null
                      }
                      { // 发送消息人外部联系人（微信），以wo或wm开头 
                        // 企微文档：https://developer.work.weixin.qq.com/document/path/91774#%E6%B6%88%E6%81%AF%E6%A0%BC%E5%BC%8F
                        row.fromId && (row.fromId.startsWith('wo') || row.fromId.startsWith('wm')) ? <span className="text-green">@微信</span> 
                        : null
                      }
                      { // 发送消息人为机器人，以wb开头
                        row.fromId && row.fromId.startsWith('wb') ? <span className="text-blue">@机器人</span> 
                        : null
                      }
                    </div>
                  </div>
                )}
                {/* revoked 为1是已撤回消息，样式单独处理，为0是正常消息 */}
                <div className={`${row.fromId === qwUserId ? "from-content" : "content"} ${row.revoked == 1 ? "revoked-bg" : ""}`}>
                  {
                    row.revoked == 1 ? <div className="revoked-tip">消息已撤回</div> : null
                  }
                  {row.msgType == "text" || row.type === "ChatRecordText" ? (
                    <div
                      className={
                        row.fromId === qwUserId ? "content-text text-right mr10" : "content-text"
                      }
                      onContextMenu={onContextMenuHanlder}
                    >
                      {row.content}
                    </div>
                  ) : (row.msgType === "image" || row.type === "ChatRecordImage") || row.msgType === "emotion" ? (
                    roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_IMAGE") ? 
                       <div
                      className={
                        row.fromId === qwUserId ? "content-text text-right mr10 pointer" : "content-text pointer"
                      }
                    >
                      <Button style={{marginLeft: 4, fontSize: 12}} type="link" onClick={() => getImageOssUrl(row)}>查看图片</Button>

                    </div>
                    : 
                    <div className="content-text">
                     #图片消息，暂不开放查看#
                   </div>
                  ) : row.msgType === "mixed" ? (
                    row.complexMsg.item.map((subItem, subIndex) => {
                      return (
                        <div key={subIndex} onContextMenu={onContextMenuHanlder}>
                          {subItem.type === "text" ? (
                            <div className="content-text">{subItem.content}</div>
                          ) : subItem.type === "image" ? (
                            roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_IMAGE") ? 
                            <div
                              className="content-text pointer"
                            >
                              <Button style={{marginLeft: 4, fontSize: 12}} type="link" onClick={() => getImageOssUrl(row)}>查看图片</Button>
                            </div>
                             : 
                            <div className="content-text">
                             #图片消息，暂不开放查看#
                           </div>
                          ) : null}
                        </div>
                      );
                    })
                  ) : row.msgType === "voice" ? (
                    roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_AUDIO") ? 
                    <div
                      className={
                        row.fromId === qwUserId ? "content-text text-right mr10 pointer" : "content-text pointer"
                      }
                    >
                      <Button style={{marginLeft: 4, fontSize: 12}} type="link" onClick={() => getVoiceOssUrl(row)}>查看语音</Button>
                    </div>
                    : 
                    <div className="content-text">
                     #语音消息，暂不开放查看#
                   </div>
                  ) : row.msgType === "video" || row.type === "ChatRecordVideo" ? (
                    roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_VIDEO") ? 
                    <div
                      className={
                        row.fromId === qwUserId ? "content-text text-right mr10 pointer" : "content-text pointer"
                      }
                    >
                      <Button style={{marginLeft: 4, fontSize: 12}} type="link" onClick={() => getVideoOssUrl(row)}>查看视频</Button>
                    </div>
                    : 
                    <div className="content-text">
                     #视频消息，暂不开放查看#
                   </div>
                  ) : row.msgType === "file" || row.type === "ChatRecordFile" ? (
                    // 有权限的可以查看并下载文件
                    roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_FILE_DOWNLOAD") ? 
                      <div className="content-file content-text">
                          {row.filename}
                          <Button style={{marginLeft: 4, fontSize: 12}} type="link" onClick={() => geFiletOssUrl(row)}>查看文件</Button>
                      </div>
                    : 
                     <div className="content-file content-text" onContextMenu={onContextMenuHanlder}>
                      <p className="" onClick={()=> message.info('没有权限查看，如需权限请联系系统管理员！')}>
                        {row.filename}
                      </p>
                    </div>
                  ) : 
                  row.msgType === "link" || row.type === "ChatRecordLink" ? (
                    row.linkUrl ? 
                    <div className="content-file content-text">
                      <a className="content-text" href={row.linkUrl} target="_blank">
                        {row.title}
                      </a>
                    </div>
                    : <div className="content-text">未知链接</div>
                  ) : // 同意会话聊天
                  row.msgType === "agree" ? (
                    <div
                      className={
                        row.fromId === qwUserId ? "content-text text-right mr10" : "content-text"
                      }
                    >
                      {row.content || "客户已同意会话"}
                    </div>
                  ) : // 待办
                  row.msgType === "todo" ? (
                    <div className="content-todo">
                      <Card title={row.title}>
                        <p>{row.content}</p>
                      </Card>
                      <div>待办</div>
                    </div>
                  ) : // 日程 二期做
                  row.msgType === "calendar" ? (
                    <div className="content-todo">
                      <Card title={row.title}>
                        <p>{row.content}</p>
                      </Card>
                      <div>待办</div>
                    </div>
                  ) : // 转发消息
                  row.msgType === "chatrecord" || row.type === "chatrecord" ? (
                    <div className="chatrecord">
                      <div className="chatrecord-title">{row.title || "合并转发的消息"}</div>
                      {row.complexMsg && row.complexMsg.item
                        ? renderRow(row.complexMsg.item)
                        : null}
                    </div>
                  ) : (
                    <div
                      className={
                        row.fromId === qwUserId ? "content-text text-right mr10" : "content-text"
                      }
                    >
                      {row.content || "未知消息"}
                    </div>
                  )}
                </div>
              </div>
              {
                // 是本人， 头像在右边
                row.fromId === qwUserId ? (
                  <div className="avatar-right">
                    <Avatar src={row.fromNameAvatar} icon={<UserOutlined />} />
                  </div>
                ) : null
              }
            </div>
          );
        })
      : null;
  };

  return (
    <>
        <div id="list" className="chat-row" ref={listRef}>
          {renderRow(list)}
        </div>
        <div className="content-img" onContextMenu={onContextMenuHanlder}>
          <Image rootClassName={'image-modal'}  width={0}
            placeholder
              preview={{
                visible,
                forceRender:true,
                src: imageOssUrl,
                onVisibleChange: value => {
                  setVisible(value);
                  setImageOssUrl('')
                },
              }}>
            </Image> 
        </div>
        <Modal forceRender wrapClassName="voice-modal"  destroyOnClose open={visible1} footer={null} onOk={hideVoiceModal} onCancel={hideVoiceModal}>
              {
                voiceUrl ? 
                    <div className="content-audio" onContextMenu={onContextMenuHanlder}>
                      <AmrPlayer id="voicePlayer" ref={amrPlayerRef}  url={voiceUrl}></AmrPlayer>
                    </div> 
                    : null
              }
        </Modal>
        <Modal forceRender width={1200} open={visible2} destroyOnClose onOk={hideVideoModal} footer={null} onCancel={hideVideoModal}>
            <div className="content-video" onContextMenu={onContextMenuHanlder}>
              {
                videoUrl ? 
                  <video width={'100%'} height={600} ref={videoRef}  src={videoUrl} controls="controls" controlsList="nodownload"></video>
                : null
              }
            </div>
        </Modal>
    </>
   
  );
});

export default NightChatRow;
