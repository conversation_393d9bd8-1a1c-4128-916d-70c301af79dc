import { Checkbox, Row, Spin, Tooltip } from 'antd';
import { CheckboxGroupProps, CheckboxOptionType } from 'antd/lib/checkbox';
import React, { FC, useMemo } from 'react';
import { CommonJobType } from '../../constants';
import { useCommonJobs } from '../../hooks';

export interface CommonJobSelectProps extends Omit<CheckboxGroupProps, 'type' | 'value' | 'onChange'> {
  type?: CommonJobType;
  value?: (CheckboxOptionType & FileManage.PostVO)[];
  onChange?: (value: CheckboxGroupProps['options'] | undefined) => void;
}

const CommonJobSelect: FC<CommonJobSelectProps> = ({ type, value, onChange, ...props }) => {
  const { data, loading } = useCommonJobs();

  /**
   * 根据类型过滤数据
   */
  const dataByType = useMemo(() => {
    if (typeof type === 'undefined') {
      return data;
    } else {
      return data?.filter((item) => item.type === type);
    }
  }, [data, type]);

  const options = useMemo(() => {
    return dataByType
      ?.filter((item) => item.id !== undefined)
      .map((item) => ({
        value: item.id !== undefined ? item.id : '',
        label: <Tooltip title={item.list?.map((i) => i.name).join(',')}>{item.name}</Tooltip>,
        ...item,
      }));
  }, [dataByType]);

  const checkedValues = useMemo(() => {
    return value?.map((item) => item.value);
  }, [value]);

  const deletedSubItems = useMemo(() => {
    return value?.filter((item) => item.deleted);
  }, [value]);

  const handleOnChange = (values: any[]) => {
    const checkedItems = options?.filter((item) => values.includes(item.value));
    onChange?.(checkedItems);
  };
  return (
    <Spin spinning={loading}>
      <Checkbox.Group {...props} options={options} value={checkedValues} onChange={handleOnChange} />
      <Row gutter={24}>
        {deletedSubItems?.length ? (
          <div>提示：岗位 “{deletedSubItems.map((item) => item.name).join('、')}” 已在协同系统中被删除</div>
        ) : null}
      </Row>
    </Spin>
  );
};

export default CommonJobSelect;
