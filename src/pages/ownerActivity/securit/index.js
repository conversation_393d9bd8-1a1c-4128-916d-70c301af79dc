import './index.less'
import React, { useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Row, Col, Button, Spin, Divider, Modal } from 'antd'
import { DownloadOutlined } from '@ant-design/icons';
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import { utilsDict } from '@/utils/utilsDict'
import PublicTable from '@/components/Public/PublicTable'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTooltip from '@/components/Public/PublicTooltip'
import _ from 'lodash'
import moment from 'moment';
import { roleJudgment } from '@/utils/authority'
import html2canvas from 'html2canvas';
const SecuritList = (props) => {
    const tableRef = useRef()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [tableHeight, setTableHeight] = useState(0)
    const [codeVisible, setCodeVisible] = useState(false)
    const [couponCodeUrl, setCouponCodeUrl] = useState('')
    const [codeTitle, setCodeTitle] = useState('')
    const [codeTag, setCodeTag] = useState('')
    const [spinLoading, setSpinLoading] = useState(false);
    
    
    const [defaultQuery, setDefaultQuery] = useState({})
    const [columns, setColums] = useState([
        { title: '序号', dataIndex: 'index', width: 80,  render: (text, record, index) => <div style={{ textAlign: 'center' }}>{index + 1}</div> },
        { title: '卡券外显名称', dataIndex: 'couponName', width: 140, },
        { title: '发放有效起期', dataIndex: 'validStartTime', width: 160,  render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''  },
        { title: '发放有效止期', dataIndex: 'validEndTime', width: 160,  render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''  },
        { title: '发放方式', dataIndex: 'issueType', width: 100,render: text => utilsDict('issueType', text)   },
        { title: '是否允许转赠', dataIndex: 'giveawayAllowed', width: 120,render: text => text ? '允许' : '不允许' },
        { title: '福利券数量', dataIndex: 'issueNum', width: 120,},
        { title: '已核销数量', dataIndex: 'verificationNum', width: 120 },
        { title: '门店名称', dataIndex: 'storeName', width: 180, render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '创建时间', dataIndex: 'createTime', width: 160,  render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''  },
        { title: '操作', width: 160, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
    ])
    
    const onSearch = (values) => {
        if (values.validStartTime && values.validStartTime.length) {
            values.validStartTime1 = moment(values.validStartTime[0]).format('YYYY-MM-DD')
            values.validStartTime2 = moment(values.validStartTime[1]).format('YYYY-MM-DD')
            delete values.validStartTime
        }
        if (values.validEndTime && values.validEndTime.length) {
            values.validEndTime1 = moment(values.validEndTime[0]).format('YYYY-MM-DD')
            values.validEndTime2 = moment(values.validEndTime[1]).format('YYYY-MM-DD')
            delete values.validEndTime
        }
        setDefaultQuery(values)
    }
    const RowCode = (record, type) => {
        setCodeVisible(true)
        if(type == 1){
            setCouponCodeUrl(record.couponCodeUrl)
            setCodeTitle(record.couponName)
            setCodeTag("领券码")
        }else {
            setCouponCodeUrl(record.verificationCodeUrl)
            setCodeTitle(record.couponName)
            setCodeTag("核销码")
        } 
    }
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff',textAlign:'center' }}>
            {
                roleJudgment(userInfo, 'CONPON_CODE') && record.couponCodeUrl ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowCode(record, 1)}>领券码</span>,
                        record.verificationCodeUrl && <Divider key={1} type="vertical" />,
                    ] : null
            }
            {
                roleJudgment(userInfo, 'CANCEL_CODE') && record.verificationCodeUrl ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowCode(record, 2)}>核销码</span>
                        
                    ] : null
            }

        </div>
    }

    const initPage = () => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let FormQueryH = document.getElementsByClassName('PublicList_FormQuery')[0].clientHeight
        let h = winH - FormQueryH - 47 - 54 - 305
        setTableHeight(h)
    }
    const handleCancel = () => {
        setCodeVisible(false);
    };

    const downImg = () => {
        setSpinLoading(true)
        let fileName = codeTitle + '.png';
        html2canvas(document.getElementById('aphoto'), { 
            allowTaint: false,
            useCORS: true, // 支持跨域图片的截取，不然图片截取不出来
          }).then(canvas => {
            const link = document.createElement('a'); // 建立一个超连接对象实例
            const event = new MouseEvent('click'); // 建立一个鼠标事件的实例
            link.download = fileName; // 设置要下载的图片的名称
            link.href = canvas.toDataURL(); // 将图片的URL设置到超连接的href中
            link.dispatchEvent(event); // 触发超连接的点击事件
            setSpinLoading(false)
          })
    }
    useEffect(() => {
        // onSearch()
        initPage()
    }, [])

    let searchList = [
        { label: '卡券外显名称', name: 'couponName', type: 'Input', colSpan: 6 },
        { label: '发放方式', name: 'issueType', type: 'Select', colSpan: 6, data: [{ name: '直接推送', value: '1' }, { name: '扫码领券', value: '2' }] },
        { label: '门店名称', name: 'storeName', type: 'Input', colSpan: 6 },
        { label: '发放有效起期', name: 'validStartTime', type: 'RangePicker', colSpan: 6 },
        { label: '发放有效止期', name: 'validEndTime', type: 'RangePicker', colSpan: 6 },
    ]
    let newColumns = _.cloneDeep({ columns }).columns
    for (let i = 0; i < newColumns.length; i++) {
        if (JSON.stringify(newColumns[i]['checked']) !== undefined && !newColumns[i].checked) {
            newColumns.splice(i, 1)
            i--
        }
    }

    return (
        <div className='SecuritList'>
            <PublicTableQuery isCatch={true}  isFormDown={false} onSearch={onSearch} searchList={searchList} />
            <div className='tableData'>
                <Row className='tableTitle'>
                    <Col className='text'>车主福利券列表</Col>
                </Row>
                <PublicTable isCatch={true} type = {4} scroll={{x:'max-content',y:tableHeight}} sticky={true} ref={tableRef} rowSelection={false} columns={newColumns} defaultQuery={defaultQuery} url={allUrl.OwnerActivity.securitList} />
            </div> 
            <Modal className="codeContainer" visible={codeVisible} closable={false} footer={null} width={448} maskClosable={true} onCancel={handleCancel}>
                <Spin spinning={spinLoading} tip="图片生成中...">
                    <div id="aphoto" className="aphoto"> 
                        <div className="codeTag">{ codeTag }</div>
                        <div className="codeTitle">{ codeTitle }</div>  
                        <div className="codeBox">
                            <img className="codeImg" src={couponCodeUrl} alt={codeTitle}></img>
                        </div>
                    </div>
                    <div className="downBtn" onClick={downImg}>
                        <DownloadOutlined />
                    </div> 
                </Spin>
            </Modal>
          
        </div>
    )
}
export default SecuritList
