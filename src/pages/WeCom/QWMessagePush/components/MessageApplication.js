import React,{useEffect, useState} from 'react'
import {Modal,Row,Col,message} from 'antd'
import {get} from '@/utils/request'
import allUrl from '@/utils/url'
import { CheckCircleFilled,} from '@ant-design/icons';

const MessageApplication = (props) =>{
    const {visible,onCancel,cb,agentObj} = props
    const [entryLists,setEntryLists] = useState([])
    const [entryListID,setEntryListID] = useState('')
    const MessageApplicationItemClick = (row) =>{
        setEntryListID(row.entryValue)
    }
    const handleOk = () =>{
        let temp = entryLists.filter(item=>item.entryValue === entryListID )[0]
        console.log(temp)
        if(temp){
            cb(temp)
            onCancel()
        }else{
            message.error('请选择应用！')
        }
    }
    useEffect(()=>{
        get(allUrl.common.entryLists, {codes:'qw-agent'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setEntryLists(Dt['qw-agent'])
            } else {
                // message.error(res.message)
            }
        })
    },[])
    useEffect(()=>{
        setEntryListID(agentObj.agentId)
    },[])
    return <Modal
        visible={visible}
        title='选择发消息的应用'
        onCancel={onCancel}
        onOk={handleOk}
        maskClosable={false}
        width={900}
    >
        <Row gutter={[26,26]}>
            {
                entryLists.map((item,index)=>{
                    return <Col span={8} key={index}>
                        <div style={{padding:'10px',border:'1px solid #efefef',cursor:'pointer',background:item.entryValue === entryListID ?'#E6F7FF':'white',
                                border:item.entryValue === entryListID ?'1px solid #91D5FF':'1px solid rgb(239, 239, 239)'}} onClick={()=>MessageApplicationItemClick(item)}>
                            <img src={item.extendField1} style={{width:'60px',height:'60px'}} />
                            <span style={{marginLeft:'20px',color:'rgba(0,0,0,0.65)',fontSize:'14px'}}>{item.entryMeaning}</span>
                            {
                                item.entryValue === entryListID ?
                                <span style={{position: 'absolute',right: '20px',bottom: '4px'}}>
                                    <CheckCircleFilled style={{color:'#1890ff',fontSize:'24px'}} />
                                </span>:null
                            }
                        </div>
                    </Col>
                })
            }
        </Row>
    </Modal>
}
export default MessageApplication