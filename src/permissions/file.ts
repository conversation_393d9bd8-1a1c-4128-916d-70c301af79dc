import { definePermissions } from '@/utils/permission';
export const menuPermissions = definePermissions({
  fileManage: {
    code: 'FILE_MANAGE',
    name: '文件管理',
    type: 'MENU',
  },
  watermark: {
    code: 'WATERMARK',
    name: '水印管理',
    type: 'MENU',
  },
  watermarkTrace: {
    code: 'WATERMARK_TRACE',
    name: '水印溯源',
    type: 'MENU',
    parent: 'watermark',
  },
  fileManageList: {
    code: 'MERCHANT_INFO_LIST',
    name: '文件管理',
    type: 'MENU',
    parent: 'fileManage',
  },
  commonJobManage: {
    code: 'COMMON_JOB_MANAGE',
    name: '常用岗位设置',
    type: 'MENU',
    parent: 'fileManage',
  },
  headquarters: {
    code: 'HEADQUARTERS',
    name: '总部管理',
    type: 'MENU',
  },
  division: {
    code: 'DIVISION',
    name: '总部部门设置',
    type: 'MENU',
  },
  position: {
    code: 'POSITION',
    name: '总部岗位设置',
    type: 'MENU',
  },
});

export const fileManageListPermissions = definePermissions({
  // 交付类
  delivery: {
    code: 'FILE_MANAGE_LIST_DELIVERY',
    name: '交付类',
    parent: 'fileManageList',
  },
  // 服务类
  afterSale: {
    code: 'FILE_MANAGE_LIST_AFTER_SALE',
    name: '服务类',
    parent: 'fileManageList',
  },
  // 查看文件列表
  view: {
    code: 'FILE_MANAGE_LIST_VIEW',
    name: '查看文件列表',
    parent: 'fileManageList',
  },
  // 添加文件
  addFile: {
    code: 'FILE_MANAGE_LIST_ADD_FILE',
    name: '添加文件',
    parent: 'fileManageList',
  },
  // 添加目录
  addDirectory: {
    code: 'FILE_MANAGE_LIST_ADD_DIRECTORY',
    name: '添加目录',
    parent: 'fileManageList',
  },
  // 编辑目录
  editDirectory: {
    code: 'FILE_MANAGE_LIST_EDIT_DIRECTORY',
    name: '编辑目录',
    parent: 'fileManageList',
  },
  // 删除目录
  deleteDirectory: {
    code: 'FILE_MANAGE_LIST_DELETE_DIRECTORY',
    name: '删除目录',
    parent: 'fileManageList',
  },
  // 移动文件
  moveFile: {
    code: 'FILE_MANAGE_LIST_MOVE_FILE',
    name: '移动文件',
    parent: 'fileManageList',
  },
  // 删除文件
  deleteFile: {
    code: 'FILE_MANAGE_LIST_DELETE_FILE',
    name: '删除文件',
    parent: 'fileManageList',
  },
  // 查看详情
  viewDetail: {
    code: 'FILE_MANAGE_LIST_VIEW_DETAIL',
    name: '查看详情',
    parent: 'fileManageList',
  },
  // 下发
  issue: {
    code: 'FILE_MANAGE_LIST_ISSUE',
    name: '下发',
    parent: 'fileManageList',
  },
  // 撤回
  recall: {
    code: 'FILE_MANAGE_LIST_RECALL',
    name: '撤回',
    parent: 'fileManageList',
  },
  // 作废
  invalid: {
    code: 'FILE_MANAGE_LIST_INVALID',
    name: '作废',
    parent: 'fileManageList',
  },
  // 编辑文件
  editFile: {
    code: 'FILE_MANAGE_LIST_EDIT_FILE',
    name: '编辑文件',
    parent: 'fileManageList',
  },
  // 复制url
  copyUrl: {
    code: 'FILE_MANAGE_LIST_COPY_URL',
    name: '复制url',
    parent: 'fileManageList',
  },
});

export const commonJobManagePermissions = definePermissions({
  // 添加
  add: {
    code: 'COMMON_JOB_MANAGE_ADD',
    name: '添加',
    parent: 'commonJobManage',
  },
  // 编辑
  edit: {
    code: 'COMMON_JOB_MANAGE_EDIT',
    name: '编辑',
    parent: 'commonJobManage',
  },
  // 删除
  delete: {
    code: 'COMMON_JOB_MANAGE_DELETE',
    name: '删除',
    parent: 'commonJobManage',
  },
});

export const watermarkTracePermissions = definePermissions({
  // 查看水印溯源
  view: {
    code: 'WATERMARK_TRACE_VIEW',
    name: '查看水印溯源',
    parent: 'watermarkTrace',
  },
  // 上传材料
  uploadMaterial: {
    code: 'WATERMARK_TRACE_UPLOAD_MATERIAL',
    name: '上传材料',
    parent: 'watermarkTrace',
  },
});

export const headquartersPermissions = definePermissions({
  // 查看总部人员管理
  view: {
    code: 'HEADQUARTERS_VIEW',
    name: '查看总部管理',
    parent: 'headquarters',
  },
  // 添加人员
  add: {
    code: 'HEADQUARTERS_ADD',
    name: '添加总部',
    parent: 'headquarters',
  },
  // 删除人员
  delete: {
    code: 'HEADQUARTERS_DELETE',
    name: '删除总部',
    parent: 'headquarters',
  },
  // 导入人员
  import: {
    code: 'HEADQUARTERS_IMPORT',
    name: '导入总部',
    parent: 'headquarters',
  },
  edit: {
    code: 'HEADQUARTERS_EDIT',
    name: '编辑总部',
    parent: 'headquarters',
  },
});

export const divisionPermissions = definePermissions({
  // 查看总部部门设置
  view: {
    code: 'DIVISION_VIEW',
    name: '查看总部部门设置',
    parent: 'division',
  },
  // 添加部门
  add: {
    code: 'DIVISION_ADD',
    name: '添加部门',
    parent: 'division',
  },
  // 删除部门
  delete: {
    code: 'DIVISION_DELETE',
    name: '删除部门',
    parent: 'division',
  },
  // 编辑部门
  edit: {
    code: 'DIVISION_EDIT',
    name: '编辑部门',
    parent: 'division',
  },
});

export const positionPermissions = definePermissions({
  // 查看总部岗位设置
  view: {
    code: 'POSITION_VIEW',
    name: '查看总部岗位设置',
    parent: 'position',
  },
  // 添加岗位
  add: {
    code: 'POSITION_ADD',
    name: '添加岗位',
    parent: 'position',
  },
  // 删除岗位
  delete: {
    code: 'POSITION_DELETE',
    name: '删除岗位',
    parent: 'position',
  },
  // 编辑岗位
  edit: {
    code: 'POSITION_EDIT',
    name: '编辑岗位',
    parent: 'position',
  },
});
