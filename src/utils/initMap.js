import AMapLoader from '@amap/amap-jsapi-loader';
const initMap = async (config) => {
  return new Promise((resolve, reject) => {
    AMapLoader.load({
      key: config.key,
      version: '2.0',
      plugins: [
        // AMap.Pixel
        // 'AMap.PolygonEditor', // 插件
      ],
      AMapUI: {
        version: '1.1',
        plugins: [],
      },
      Loca: {
        version: '2.0',
      },
    })
      .then((AMap) => {
        resolve(AMap);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
export default initMap;
