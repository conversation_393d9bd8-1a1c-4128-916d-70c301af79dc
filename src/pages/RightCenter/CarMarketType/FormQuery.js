import React, { useState, memo, useEffect, useCallback } from 'react'
import { Form, Button, Input, Select, Row, Col, DatePicker } from 'antd'
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux'
import { roleJudgment } from '@/utils/authority'
const { Option } = Select
const { RangePicker } = DatePicker;

const FormQuery = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const { onSearch, searchList, defaultQuery, initPage, resetPage } = props
    const [form] = Form.useForm()
    const [formDown, ChangeFormDown] = useState(false)
    const [searchFormList, changeSearchList] = useState([])
    const [isPutItAway, changeIsPutItAway] = useState(true)
    const handleSearch = useCallback(() => {
        form.validateFields().then(values => {
            values.name && (values.name = values.name.trim());
            for (let i in values) {
                if (!values[i]) {
                    values[i] = null
                }
                if (!i) {
                    delete values[i]
                }
            }
            onSearch(values)
        })
    })
    const onReset = () => {
        form.resetFields()
        onSearch()
        resetPage()
    }
    const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
    };
    useEffect(() => {
        searchList.forEach(item => {
            item.onPressEnter = handleSearch
        })
        let colSpanNum = searchList.reduce((num, item) => num + item.colSpan, 0);
        let ColNum = 24 * Math.ceil(colSpanNum / 24) - colSpanNum - 6
        if (colSpanNum === 18) {
            changeIsPutItAway(false)
        } else {
            if (ColNum < 0) {
                searchList.push({ colSpan: 18 })
            } else if (ColNum === 12) {
                searchList.push({ colSpan: 12 })
            } else if (ColNum === 6) {
                searchList.push({ colSpan: 6 })
            }
        }
        changeSearchList(searchList)
    }, [handleSearch, searchList])
    useEffect(() => {
        if (JSON.stringify(defaultQuery) !== '{}') {
            form.setFieldsValue(defaultQuery)
        }
    }, [defaultQuery, form])
    // useEffect(()=>{
    //     form.resetFields()
    // },[tabsKey])
    return (
        <>
            <Form className='PublicList_FormQuery' form={form} {...layout}>
                <Row style={{ paddingRight: '20px', paddingLeft: '8px' }}>
                    {
                        searchFormList.map((item, index) => {
                            return (index <= 2 &&
                                <Col span={item.colSpan} key={index}>
                                    <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []}>
                                        {
                                            item.type === 'Input' ?
                                                <Input placeholder={item.placeholder} allowClear onPressEnter={item.onPressEnter} />
                                                : item.type === 'Select' ?
                                                    <Select placeholder='请选择' defaultValue={'0'} allowClear mode={item.mode || ''}>
                                                        {
                                                            item.data.map((ele, i) => {
                                                                return <Option key={i} value={ele.value}>{ele.name}</Option>
                                                            })
                                                        }
                                                    </Select>
                                                    : item.type === 'RangePicker' ?
                                                        <RangePicker placeholder={item.placeholder} showTime={item.showTime || false} />
                                                        : item.type === 'DatePicker' ?
                                                            <DatePicker placeholder={item.placeholder} style={{ width: '100%' }} showTime={item.showTime || false} />
                                                            : null
                                        }
                                    </Form.Item>
                                </Col>)
                        })
                    }
                    {
                        searchFormList.map((item, index) => {
                            return (index > 2 && formDown &&
                                <Col span={item.colSpan} key={index}>
                                    <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []}>
                                        {
                                            item.type === 'Input' ?
                                                <Input placeholder={item.placeholder} allowClear onPressEnter={item.onPressEnter} />
                                                : item.type === 'Select' ?
                                                    <Select placeholder='请选择' allowClear>
                                                        {
                                                            item.data.map((ele, i) => {
                                                                return <Option key={i} value={ele.value}>{ele.name}</Option>
                                                            })
                                                        }
                                                    </Select>
                                                    : item.type === 'RangePicker' ?
                                                        <RangePicker placeholder={item.placeholder} showTime={item.showTime || false} />
                                                        : item.type === 'DatePicker' ?
                                                            <DatePicker placeholder={item.placeholder} style={{ width: '100%' }} showTime={item.showTime || false} />
                                                            : null
                                        }
                                    </Form.Item>
                                </Col>)
                        })
                    }
                    {/* <Col span={6}>
                        <Form.Item>
                            <Button onClick={handleSearch}>查询</Button>
                        </Form.Item>
                    </Col> */}
                    <Col xs={24} sm={12} xl={8} xxl={6} className='FormQuerySubmit'>
                        {roleJudgment(userInfo, 'CAR_MARKET_TYPE_RESET') ?
                            <Col span={5} className='Reset'>
                                <Button onClick={onReset}>重置</Button>
                            </Col> : null
                        }
                        {roleJudgment(userInfo, 'CAR_MARKET_TYPE_SEARCH') ?
                            <Col span={5} className='Search' style={{ marginRight: 24 }}>
                                <Button type='primary' onClick={handleSearch}>查询</Button>
                            </Col> : null
                        }
                        {
                            isPutItAway ?
                                <Col span={8} xl={{ span: 6 }} className='operationButtons' style={{ cursor: 'pointer' }}>
                                    <Form.Item>
                                        {
                                            formDown ?
                                                <span style={{ color: '#1890ff' }} onClick={() => {
                                                    ChangeFormDown(false)
                                                    initPage()
                                                }}>
                                                    <span style={{ marginRight: 6 }}>收起</span><UpOutlined />
                                                </span>
                                                : <span style={{ color: '#1890ff' }} onClick={() => {
                                                    ChangeFormDown(true)
                                                    initPage(true)
                                                }}>
                                                    <span style={{ marginRight: 6 }}>展开</span><DownOutlined />
                                                </span>
                                        }
                                    </Form.Item>
                                </Col> : null
                        }
                    </Col>
                </Row>
            </Form>
        </>
    )
}
export default memo(FormQuery)