import React, { useEffect ,useState} from 'react'
import { post } from '../../utils/request'
import allUrl from '../../utils/url'
import history from '@/utils/history'
import {Spin,message} from 'antd'
import Cookies from 'js-cookie'
const Transfer = (props) => {
    const [loading,setLoading] = useState(false)
    const getQueryVariable = (paramName) => {
        const url = document.location.toString()
        const arrObj = url.split("?")
        if (arrObj.length > 1) {
            const arrPara = arrObj[1].split("&");
            for (var i = 0; i < arrPara.length; i++) {
                const arr = arrPara[i].split("=");
                if (arr != null && arr[0] == paramName) {
                    return arr[1];
                }
            }
            return ""
        }
        else {
            return ""
        }
    }
    useEffect(() => {
        let authorization = getQueryVariable('authorization')
        if(!authorization){
            history.push('/')
            message.success('登陆失败！')
        }else{
            Cookies.set('scrm_token', authorization)
            Cookies.set('isTransfer', '1')
            history.push('/home')
        }
        console.log(authorization)
    }, [window.location.href])
    return <Spin spinning={loading} >
        正在跳转中...
    </Spin>
}
export default Transfer