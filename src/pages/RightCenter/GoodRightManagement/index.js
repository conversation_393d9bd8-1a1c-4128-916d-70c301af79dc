import React, { useEffect, useState, useRef } from 'react';
import PublicTableQuery from '@/components/Public/PublicTableQuery';
import PublicTable from '@/components/Public/PublicTable';
import { Table, message, Modal, Button, Row, Col, Divider, Spin } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment';
import allUrl from '@/utils/url';
import { post, get } from '@/utils/request';
import CreateForm from './create';
import styles from '../antd.module.less';
import tableModuleStyles from '../tableLayout.module.less';

const STATUS = [
  {
    name: '已发布',
    value: 1,
  },
  {
    name: '未发布',
    value: 2,
  },
];

const GoodRightManagement = props => {
  const tableRef = useRef();
  const [loading, setLoading] = useState(false);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [dictData, setDictData] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [rightOptions, setRightOptions] = useState([]); // 权益分类

  const searchList = [
    { label: '精品权益名称', name: 'materialName', type: 'Input', placeholder: '请输入', colSpan: 6 },
    {
      label: '车型',
      name: 'carType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: (dictData && dictData['equity_car_type']) || [],
    },
    { label: 'SE物料编码', name: 'materialCode', type: 'Input', placeholder: '请输入精准SE物料编码', colSpan: 6 },
    {
      label: '精品状态',
      name: 'isPublished',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: STATUS,
    },
  ];

  const columns = [
    { title: '精品权益编码', dataIndex: 'detailNo', width: 180, fixed: 'left', render: text => text ?? '-' },
    {
      title: '精品权益名称',
      dataIndex: 'materialName',
      width: 250,
    },
    {
      title: '权益分类',
      dataIndex: 'sortCodeName',
      width: 120,
      render: text => text ?? '-',
    },
    {
      title: '精品权益命名时间',
      dataIndex: 'createTime',
      width: 180,
      render: text =>
        text ? <div style={{ whiteSpace: 'nowrap' }}>{moment(text).format('YYYY年MM月DD日 HH:mm:ss')}</div> : '',
    },
    {
      title: '业务编码',
      dataIndex: 'businessCodeName',
      width: 110,
      render: text => {
        if (text) {
          return <div style={{ whiteSpace: 'nowrap' }}>{text}</div>;
        } else {
          return <div>{'-'}</div>;
        }
      },
    },
    {
      title: '车型',
      dataIndex: 'carTypeName',
      width: 100,
      render: text => text ?? '-',
    },
    {
      title: 'SE物料编码',
      dataIndex: 'materialCode',
      width: 180,
      render: text => text ?? '-',
    },
    {
      title: '权益所属',
      dataIndex: 'goodsTypeName',
      width: 100,
      render: text => text ?? '-',
    },
    {
      title: '精品权益状态',
      dataIndex: 'isPublished',
      width: 140,
      render: text => (text === 1 ? '已发布' : '未发布'),
    },
    {
      title: '同步情况',
      dataIndex: 'syncStatus',
      width: 180,
    },
    {
      title: '操作',
      width: 140,
      fixed: 'right',
      dataIndex: 'Operation',
      render: (text, record) => (
        <div>
          {
            //权益所属为“随车=》goodsType=1”才有上下架按钮
          }
          <Button
            type="link"
            size="small"
            onClick={() => publishHandle(record)}
            disabled={!((record.goodsType === 1 || record.goodsType === null) && record.isPublished === 2)}
          >
            上架
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => downHandle(record)}
            disabled={!(record.goodsType === 1 && record.isPublished === 1)}
          >
            下架
          </Button>
        </div>
      ),
    },
  ];

  //上架
  const publishHandle = record => {
    Modal.confirm({
      title: '确认上架该精品权益？',
      onOk: async () => {
        const { detailNo, id, goodsType } = record;
        const { success, resp } = await post(allUrl.BoutiqueMaterial.publish, {
          detailNo,
          boutiqueMaterialId: id,
          goodsType,
        });
        if (success) {
          message.success('上架成功');
          tableRef.current.getTableData();
        }
      },
      onCancel: () => {},
    });
  };

  //下架
  const downHandle = async record => {
    Modal.confirm({
      title: '确认下架该精品权益？',
      onOk: async () => {
        const { detailNo, id, goodsType } = record;
        const { success, resp } = await post(allUrl.BoutiqueMaterial.down, {
          detailNo,
          boutiqueMaterialId: id,
          goodsType,
        });
        if (success) {
          message.success('下架成功');
          tableRef.current.getTableData();
        }
      },
      onCancel: () => {},
    });
  };

  const onSearch = query => {
    setDefaultQuery(query);
  };

  const onModalSuccess = () => {
    setOpenModal(false);
    tableRef.current.getTableData();
  };

  const onModalCancel = () => {
    setOpenModal(false);
  };

  //查询数据字典
  const getEntryLists = () => {
    get(allUrl.common.entryLists, { codes: 'equity_car_type' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }
        setDictData(Dt || {});
      }
    });
  };

  //获取权益分类
  const getSortList = async () => {
    const { success, resp } = await get(allUrl.DetailedManageInterests.getSortList);
    if (success) {
      setRightOptions(resp);
    }
  };

  useEffect(() => {
    getEntryLists();
    getSortList();
  }, []);
  return (
    <Spin spinning={loading}>
      <div className={`${tableModuleStyles.PublicList}`}>
        <PublicTableQuery isCatch={true} isFormDown={false} onSearch={onSearch} searchList={searchList} />
        <div className="tableData">
          <Row className="tableTitle">
            <Col className="text" style={{ color: 'black', fontSize: 20 }}>
              <span style={{ marginRight: '20px' }}>精品权益列表</span>
            </Col>
            <Col className="bts">
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setOpenModal(true)}>
                创建
              </Button>
            </Col>
          </Row>
          <PublicTable
            isCatch={true}
            ref={tableRef}
            type={6}
            rowSelection={false}
            defaultQuery={defaultQuery}
            url={allUrl.BoutiqueMaterial.queryList}
            columns={columns}
            className={styles['blue-table']}
          />
        </div>
        <CreateForm
          openModal={openModal}
          onModalSuccess={onModalSuccess}
          onModalCancel={onModalCancel}
          rightOptions={rightOptions}
        />
      </div>
    </Spin>
  );
};

export default GoodRightManagement;
