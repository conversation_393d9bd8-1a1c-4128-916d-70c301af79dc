import React from 'react'
import { Mo<PERSON>, But<PERSON> } from 'antd'
import './index.less'

const ResultNotify = (props) => {
    const onClick = () => {
        props.onOk()
    }
    return (
        <Modal
            onCancel={onClick}
            footer={[]}
            visible={props.showModal}
            style={{ width: '420px', height: '220px' }}
            closable={false}
            centered={true}
        >
            <div className="modal-notify">
                <div className='top-item'>
                    <img className='icon' style={{ width: '24px', height: '24px' }} src={require('@/assets/img/warning-icon.png')} alt='' />
                    <span className='text'>{props.modalMessage}</span>
                </div>
                <Button className='btn' type='primary' onClick={onClick}>我知道了</Button>
            </div>
        </Modal>
    )
}

export default ResultNotify