import React, { useState,memo,useMemo,useEffect,useRef} from 'react'
import { useSelector } from 'react-redux'
import { Modal,Form,Row,Col,Select,Input,message} from 'antd'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
const CarConfigDetail = (props) => {
    const {handleCancel,visible,detailInfoSource} = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const dataSourceForm = useMemo(() => props.detailInfoSource, [props.detailInfoSource])
    const formRef = useRef(null)
    const layout = {
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
    };
    useEffect(() => {
        console.log(dataSourceForm,'888888',detailInfoSource);
        if (dataSourceForm) {
            formRef.current && form.setFieldsValue({
              ...dataSourceForm,
             
            })
          }
	}, [dataSourceForm,form])
    return (
        <Modal visible={visible} footer={null} onCancel={handleCancel} maskClosable={false} width='650px' title='车型配置信息'>
            <Form name='change-codeInfo' ref={formRef} form={form} {...layout} >
                <Row style={{marginLeft:50}}>
                    <Col span={24}>
                        <Form.Item label=''><span style={{marginRight:10}}>车型名称 :</span>{detailInfoSource.type ||'-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label=''><span style={{marginRight:10}}>车型配置代码 :</span>{dataSourceForm?dataSourceForm.baseMaterialNo || '-' :'-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label=''><span style={{marginRight:10}}>车型配置名称 :</span>{dataSourceForm?dataSourceForm.config || '-' :'-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='' ><span style={{marginRight:10}}>车型市场名称 :</span>{dataSourceForm?dataSourceForm.carMarketName || '-' :'-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='' ><span style={{marginRight:10}}>车型年款 :</span>{dataSourceForm?dataSourceForm.carOfYear || '-' :'-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='' ><span style={{marginRight:10}}>动力方式 :</span>{dataSourceForm?dataSourceForm.powerModel || '-' :'-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='' ><span style={{marginRight:10}}>品牌 :</span>{dataSourceForm?dataSourceForm.brand || '-' :'-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='' ><span style={{marginRight:10}}>汽车厂商 :</span>{dataSourceForm?dataSourceForm.manufacturer || '-' :'-'}</Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(CarConfigDetail)