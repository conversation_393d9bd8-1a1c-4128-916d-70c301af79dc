/*
 * @Author: geyulan <EMAIL>
 * @Date: 2024-03-06 17:29:39
 * @LastEditors: geyulan <EMAIL>
 * @LastEditTime: 2025-08-14 17:30:40
 * @FilePath: /scrm-web/src/pages/WeCom/ChatRecord/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import './index.less';
import React, { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Row, Col, Tabs, Spin, Button, Modal, message } from 'antd';
import { post, get } from '@/utils/request';
import allUrl from '@/utils/url';
import PublicTable from '@/components/Public/PublicTable';
import _ from 'lodash';
import moment from 'moment';
import Staff from './Staff';
import Timeout from './Timeout';
import { roleJudgment } from '@/utils/authority';
import NightAndMorningTimeout from './NightAndMorningTimeout';
import Feedback from './Feedback';

const { TabPane } = Tabs;

const ChatRecord = props => {
  const [tabsKey, setTabsKey] = useState('1');
  const { userInfo } = useSelector(state => state.common);
  const [defaultActiveKey, setDefaultActiveKey] = useState('1');
  const tabsCallback = key => {
    setTabsKey(key);
  };
  const getDefaultActiveKey = userInfo => {
    const rules = [
      ['WECOM_CHAT_RECORDS_FEEDBACK', '1'],
      ['WECOM_CHAT_RECORDS_CHAT_STAFF', '2'],
      ['WECOM_CHAT_RECORDS_TIMEOUT', '3'],
      ['WECOM_CHAT_NIGHT_RECORDS_TIMEOUT', '4'],
    ];
    for (const [perm, key] of rules) {
      if (roleJudgment(userInfo, perm)) return key;
    }
    return '1';
  };

  // useEffect(() => {
  //   if(roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_STAFF")){
  //     console.log('1')
  //   }else{
  //     if(roleJudgment(userInfo, "WECOM_CHAT_RECORDS_TIMEOUT")){
  //       console.log('3')
  //       setDefaultActiveKey('2')
  //     }else{
  //       if(roleJudgment(userInfo, "WECOM_CHAT_NIGHT_RECORDS_TIMEOUT") ){
  //         console.log('3')
  //         setDefaultActiveKey('3')
  //       }
  //     }
  //   }
  // }, [defaultActiveKey]);
  return (
    <div className="ChatRecord">
      <>
        <Tabs className="aiTab" onChange={tabsCallback} defaultActiveKey={getDefaultActiveKey}>
          {roleJudgment(userInfo, 'WECOM_CHAT_RECORDS_FEEDBACK') && (
            <TabPane tab="问题闭环监督" key="1">
              <Feedback />
            </TabPane>
          )}
          {roleJudgment(userInfo, 'WECOM_CHAT_RECORDS_CHAT_STAFF') && (
            <TabPane tab="聊天记录检索" key="2">
              <Staff />
            </TabPane>
          )}
          {roleJudgment(userInfo, 'WECOM_CHAT_RECORDS_TIMEOUT') && (
            <TabPane tab="超时消息检索" key="3">
              <Timeout />
            </TabPane>
          )}
          {roleJudgment(userInfo, 'WECOM_CHAT_NIGHT_RECORDS_TIMEOUT') && (
            <TabPane tab="夜间清晨超时消息监督" key="4">
              <NightAndMorningTimeout />
            </TabPane>
          )}
        </Tabs>
      </>
    </div>
  );
};

export default ChatRecord;
