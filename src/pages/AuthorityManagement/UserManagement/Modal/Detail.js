import React, { useEffect, useState } from 'react'
import { Modal, Form, Row, Col, Spin, Button, message,Table  } from 'antd'
import moment from 'moment'
import { get } from '../../../../utils/request'
import allUrl from '../../../../utils/url'
import './Detail.less'
import {utilsDict} from '@/utils/utilsDict'
import {MOTOR_MECHANIC_CODE, TM_CODE, QYJSJL_CODE, FJSZJ} from '../constant'
import { set } from 'lodash'

const Detail = (props) => {
    const { title, onCancel, visible, rowData } = props
    const [data, setData] = useState({})
    const [currentUrl, setCurrentUrl ] = useState('')
    const [previewVisible, setPreviewVisible] = useState(false)
    const [interviewResult,setInterviewResult]=useState()
    const [dataSource,setDataSource] = useState([])
    const [interviewVisible,setInterviewVisible]=useState(false)
    const [loading, setLoading] = useState(false)
    const getQYWXInfo = (arr,data) =>{
        get(allUrl.Authority.getUserInfo, {userIds:arr.join(',')}).then(res => {
            if (res && res.success) {
                let Dt = res.resp[0]
                data.qywxUserId = Dt[arr[0]]?.qywxUserId
                data.partTimePosition = Dt[arr[0]]?.partTimePosition
                data.partTimePositionName = Dt[arr[0]]?.partTimePositionName
                data.qywxStatus = Dt[arr[0]]?.qywxStatus
                setData(data)
            } else {
                // message.error(res.msg)
            }
        })
    }
    const getFileExtendingName = (filename)=> {
        // 文件扩展名匹配正则
        var reg = /\.[^\.]+$/;
        var matches = reg.exec(filename);
        if (matches) {
          return matches[0];
        }
        return '';
    }
    const isImage = (url) => {
        let imgSuffix = ['.png','.gif','.jpeg','.jpg','.svg','.bmp']
        const extend = getFileExtendingName(url)
        console.log('e',imgSuffix.indexOf(extend) > -1)
        return imgSuffix.indexOf(extend) > -1
    }
    const getFileName = (str) => {
        return str.substr(str.lastIndexOf('/') + 1)
    }
    const lookAt = (url) => {
        if(isImage(url)){
            console.log('url', url)
            setCurrentUrl(url)
        } else {
            window.open(url)
        }
    }
     // 表格
     const InforData = {
        rowKey: record => record.id,
        bordered: true,
        pagination:false,
        dataSource,
        columns: [
            { title: '面试时间', dataIndex: 'interviewTime', width: 100 ,render:text=> text?moment(text).format('YYYY-MM-DD'):''},
            { title: '面试官账号ID', dataIndex: 'interviewerId', width: 100 },
            { title: '面试官', dataIndex: 'interviewer', width: 100 },
            { title: '面试官所属组织', dataIndex: 'interviewerOrgName', width: 150 },
            { title: '面试成绩', dataIndex: 'score', width: 100 },
            // { title: '面试结果', dataIndex: 'score', width: 100 ,render:text=>text>=60?'通过':'不通过'},
        ],
    };
    useEffect(() => {
       // 数据获取接口
       get(allUrl.Authority.getStoreKeyPostInterviewDetail,{userId:rowData.id}).then(res=>{
        if(res.success){
            let Dt = res.resp[0]
            Dt.list.map((item,index)=>item.key = index)
            setDataSource(Dt.list)
            setInterviewResult(Dt.interviewResult)
        }else{
            // message.error(res.msg)
        }
    })
    },[]);
    useEffect(() => {
        if(currentUrl) {
            setPreviewVisible(true)
        }
    }, [currentUrl]);
    useEffect(() => {
        setLoading(true)
        get(allUrl.Authority.userDetail + '/' + rowData.id).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                let rolesName = []
                if(Dt.roles && Dt.roles.length){
                    Dt.roles.map(item => rolesName.push(item.roleName))
                }
                Dt.rolesName = rolesName

                let organizationName = []
                if(Dt.organizations && Dt.organizations.length){
                    Dt.organizations.map(item => organizationName.push(item.name))
                }
                Dt.organizationName = organizationName
                // setData(Dt)
                getQYWXInfo([Dt.id],Dt)
            } else {
                // message.error(res.msg || '获取用户详情失败！')
            }
            setLoading(false)
        })
    }, [])
    return <Modal wrapClassName='UserDetail' width={1000} visible={visible} title={title} onCancel={onCancel} maskClosable={false} footer={null}>
        <Spin spinning={loading}>
            <Row>
                <Col span={8}><Form.Item label='UserID'>{data.id ? data.id :'-' }</Form.Item></Col>
                <Col span={8}><Form.Item label='统一登录账号'>{data.employeeNumber ? data.employeeNumber :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='姓名'>{data.userName ? data.userName :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='手机号'>{data.mobilePhone ? data.mobilePhone :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='岗位'>{data.positionName ? data.positionName :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='兼职岗位'>{data.partTimePositionName ? data.partTimePositionName:'-'}</Form.Item></Col>
                {
                    (data.position == MOTOR_MECHANIC_CODE
                    || data.position == TM_CODE
                    || data.position == QYJSJL_CODE
                    || data.position == FJSZJ)
                     && data.extendJson ?
                        <Col span={8}><Form.Item label='是否持有低压电工作证'>{data.extendJson.hasElecCert == 1 ? '是' :'否'}</Form.Item></Col>
                    : null
                }
                <Col span={8}><Form.Item label='入职时间'>{data.inductionTime ? moment(data.inductionTime).format('YYYY-MM-DD') : '-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='是否在职'>{data.isOnJob ? '在职' : '不在职'}</Form.Item></Col>
                <Col span={8}><Form.Item label='邮箱'>{data.email ? data.email :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='身份证'>{data.idNum ? data.idNum :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='开户行'>{data.bankName ? data.bankName :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='银行卡'>{data.bankAccount ? data.bankAccount :'-'}</Form.Item></Col>
                <Col span={8}><Form.Item label='企微激活状态'>{utilsDict('qywxStatus',data.qywxStatus)}</Form.Item></Col>
                <Col span={8}><Form.Item label='是否启用'>{data.status ? '是' : '否'}</Form.Item></Col>
                {
                     data.position == 'POST_SHOP_MANAGER' //总经理
                     || data.position == 'POST_DELIVERY_MANAGER' //交付经理
                     || data.position == 'POST_TM'      //技术专家
                     || data.position == 'POST_SERVICESTATION_MASTER'  //服务经理
                     || data.position == 'POST_MANAGER' //用户关爱经理
                     || data.position == 'POST_FINANCIALINSURANCE_SPECIALIST' //金融保险专员
                     || data.position == 'POST_MM' //市场经理
                     ?
                [
                <Col span={8}><Form.Item label='面试结果'>{interviewResult ? interviewResult: '-'}</Form.Item></Col>,
                interviewResult ? <Col span={8}><Form.Item label='面试详情'> 点击查看<a onClick={() =>setInterviewVisible(true)}>面试详情</a></Form.Item></Col> : null
                ]: null
                }
                {
                    data.position == MOTOR_MECHANIC_CODE
                    || data.position == TM_CODE
                    || data.position == QYJSJL_CODE
                     || data.position == FJSZJ
                    ?
                    <Col span={8}>
                        <Form.Item label='查看证书'>{
                            (data.position == MOTOR_MECHANIC_CODE
                            || data.position == TM_CODE
                            || data.position == QYJSJL_CODE
                            || data.position == FJSZJ
                          )
                            && data.extendJson && data.extendJson.hasElecCert == 1 ? data.extendJson.elecCertUrls.map((item) => {
                            return <><Button type='link' onClick={() => lookAt(item)}><span className="ellipsis">{getFileName(item)}</span></Button></>
                            }) : null
                        }</Form.Item>
                    </Col>
                    : null
                }
                <Col span={24}>
                    <Form.Item label='所属组织'>
                    {
                        data.organizationRoles && data.organizationRoles.length ?
                        data.organizationRoles.map((item,index)=>{
                            return<div style={{display:'flex'}}key={index}>
                                <div style={{flexShrink: 0,color:'#000000'}}>{item.organizationName}：</div>
                                <div>{item.roles.map(ele=>ele.roleName).join(' / ')}</div>
                            </div>
                        }):null
                    }
                    </Form.Item>
                </Col>
            </Row>
        </Spin>
        {
                    interviewVisible &&
                    <Modal title='面试详情' width={700} visible={interviewVisible} onCancel={()=>setInterviewVisible(false)} rowData={rowData} onOk={()=>setInterviewVisible(false)}>
                        <Table {...InforData}/>
                    </Modal>
        }
        {
            <Modal visible={previewVisible} width={'90%'} footer={null} onCancel={() => {
              serCurrentUrl('')
              setPreviewVisible(false)
            }}>
                <img src={currentUrl} alt="" style={{ width: '100%' }}/>

            </Modal>
        }
    </Modal>
}
export default Detail
