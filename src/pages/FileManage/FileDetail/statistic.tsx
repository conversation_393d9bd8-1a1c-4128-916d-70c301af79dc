import {
  getSeresFileOperateInnerUserSummary,
  getSeresFileStatisticSummary,
  getSeresFileStatisticSummaryExport,
} from '@/apis/file-manage';
import { useRequest } from '@/hooks/request';
import { downloadBlob } from '@/utils/file';
import { formatCount, formatFloat, formatPercentage } from '@/utils/format';
import { Button, Card, Descriptions, Typography } from 'antd';
import React, { FC, useCallback, useState } from 'react';
import { useParams } from 'react-router';
import { FilePermission } from '../constants';
import DownloadedModal from './downloaded-modal';
import { useDeliverTypeLabel } from './hooks';
import InnerUserModal, { InnerUserModalType } from './inner-user-modal';
import UnDownloadModal from './undonwload-modal';
import UnViewedModal from './unviewed-modal';
import ViewedModal from './viewed-modal';

export interface FileStatisticProps {
  file?: FileManage.FileItemDetailVO;
}

const FileStatistic: FC<FileStatisticProps> = ({ file }) => {
  const { id } = useParams<{ id: string }>();

  const [downloading, setDownloading] = useState(false);
  const { data: [statistic] = [], loading } = useRequest(() =>
    getSeresFileStatisticSummary({ id: id as unknown as number })
  );
  const { data: [innerUserSummary] = [], loading: innerUserLoading } = useRequest(() =>
    getSeresFileOperateInnerUserSummary({ fileId: id as unknown as number })
  );

  const deliverTypeLabel = useDeliverTypeLabel(file);

  const handleExportClick = useCallback(async () => {
    try {
      setDownloading(true);
      const blob = await getSeresFileStatisticSummaryExport(
        { id: id as unknown as number },
        {
          responseType: 'blob',
        }
      );
      const filename = file?.name?.split('.')[0] ?? '';
      downloadBlob(blob as unknown as Blob, `${filename}下发汇总数据.xlsx`);
    } catch (error) {
      console.error(error);
    } finally {
      setDownloading(false);
    }
  }, [file, id]);

  const [unViewedModalOpen, setUnViewedModalOpen] = useState(false);
  const [unDownloadModalOpen, setUnDownloadModalOpen] = useState(false);
  const [viewedModelOpen, setViewedModelOpen] = useState(false);
  const [downloadedModelOpen, setDownloadedModelOpen] = useState(false);
  const [innerUnViewedModalOpen, setInnerUnViewedModalOpen] = useState(false);
  const [innerUnDownloadModalOpen, setInnerUnDownloadModalOpen] = useState(false);

  return (
    <Card
      title="汇总数据"
      loading={loading || innerUserLoading}
      extra={
        <Button loading={downloading} type="primary" onClick={handleExportClick}>
          导出数据
        </Button>
      }
    >
      <Descriptions layout="vertical" bordered column={7} style={{ marginBottom: 20 }}>
        <Descriptions.Item label={`下发${deliverTypeLabel}数量`}>
          {formatCount(statistic?.sendStoreCount)}
        </Descriptions.Item>
        <Descriptions.Item label="下发人数">{formatCount(statistic?.sendPeopleCount)}</Descriptions.Item>
        <Descriptions.Item label="已浏览人数">
          {statistic?.previewCount ? (
            <Typography.Link onClick={() => setViewedModelOpen(true)}>
              {formatCount(statistic?.previewCount)}
            </Typography.Link>
          ) : (
            0
          )}
        </Descriptions.Item>
        <Descriptions.Item label="未浏览人数">
          {statistic?.notPreviewCount ? (
            <Typography.Link onClick={() => setUnViewedModalOpen(true)}>
              {formatCount(statistic?.notPreviewCount)}
            </Typography.Link>
          ) : (
            0
          )}
        </Descriptions.Item>
        <Descriptions.Item label="浏览率">{formatPercentage(statistic?.previewRate)}</Descriptions.Item>
        <Descriptions.Item label="总浏览次数">{formatCount(statistic?.totalPreviewCount)}</Descriptions.Item>
        <Descriptions.Item label="人均浏览次数">{formatFloat(statistic?.previewAvgCount)}</Descriptions.Item>
        <Descriptions.Item label="总浏览时长(分)">{formatFloat(statistic?.totalPreviewDuration)}</Descriptions.Item>
        <Descriptions.Item label="人均浏览时长(分)">{formatFloat(statistic?.previewAvgDuration)}</Descriptions.Item>
        <Descriptions.Item label="已下载人数">
          {file?.permission === FilePermission.DOWNLOAD ? (
            statistic?.downloadCount ? (
              <Typography.Link onClick={() => setDownloadedModelOpen(true)}>
                {formatCount(statistic?.downloadCount)}
              </Typography.Link>
            ) : (
              0
            )
          ) : (
            '-'
          )}
        </Descriptions.Item>
        <Descriptions.Item label="未下载人数">
          {file?.permission === FilePermission.DOWNLOAD ? (
            statistic?.notDownloadCount ? (
              <Typography.Link onClick={() => setUnDownloadModalOpen(true)}>
                {formatCount(statistic?.notDownloadCount)}
              </Typography.Link>
            ) : (
              0
            )
          ) : (
            '-'
          )}
        </Descriptions.Item>
        <Descriptions.Item label="下载率">
          {file?.permission === FilePermission.DOWNLOAD ? formatPercentage(statistic?.downloadRate) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="总下载次数">
          {file?.permission === FilePermission.DOWNLOAD ? formatCount(statistic?.totalDownloadCount) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="人均下载次数">
          {file?.permission === FilePermission.DOWNLOAD ? formatFloat(statistic?.downloadAvgCount) : '-'}
        </Descriptions.Item>
      </Descriptions>
      <Descriptions layout="vertical" bordered column={7}>
        <Descriptions.Item label=" "> </Descriptions.Item>
        <Descriptions.Item label="下发总部人员">{formatCount(innerUserSummary?.sendPeopleCount)}</Descriptions.Item>
        <Descriptions.Item label="已浏览人数">{formatCount(innerUserSummary?.previewCount)}</Descriptions.Item>
        <Descriptions.Item label="未浏览人数">
          {innerUserSummary?.notPreviewCount ? (
            <Typography.Link onClick={() => setInnerUnViewedModalOpen(true)}>
              {formatCount(innerUserSummary?.notPreviewCount)}
            </Typography.Link>
          ) : (
            0
          )}
        </Descriptions.Item>
        <Descriptions.Item label="浏览率">{formatPercentage(innerUserSummary?.previewRate)}</Descriptions.Item>
        <Descriptions.Item label="总浏览次数">{formatCount(innerUserSummary?.totalPreviewCount)}</Descriptions.Item>
        <Descriptions.Item label="人均浏览次数">{formatFloat(innerUserSummary?.previewAvgCount)}</Descriptions.Item>
        <Descriptions.Item label="总浏览时长(分)">
          {formatFloat(innerUserSummary?.totalPreviewDuration)}
        </Descriptions.Item>
        <Descriptions.Item label="人均浏览时长(分)">
          {formatFloat(innerUserSummary?.previewAvgDuration)}
        </Descriptions.Item>
        <Descriptions.Item label="已下载人数">
          {file?.permission === FilePermission.DOWNLOAD ? formatCount(innerUserSummary?.downloadCount) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="未下载人数">
          {file?.permission === FilePermission.DOWNLOAD && innerUserSummary?.notDownloadCount ? (
            <Typography.Link onClick={() => setInnerUnDownloadModalOpen(true)}>
              {formatCount(innerUserSummary?.notDownloadCount)}
            </Typography.Link>
          ) : (
            '-'
          )}
        </Descriptions.Item>
        <Descriptions.Item label="下载率">
          {file?.permission === FilePermission.DOWNLOAD ? formatPercentage(innerUserSummary?.downloadRate) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="总下载次数">
          {file?.permission === FilePermission.DOWNLOAD ? formatCount(innerUserSummary?.totalDownloadCount) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="人均下载次数">
          {file?.permission === FilePermission.DOWNLOAD ? formatFloat(innerUserSummary?.downloadAvgCount) : '-'}
        </Descriptions.Item>
      </Descriptions>

      <UnViewedModal open={unViewedModalOpen} file={file} onCancel={() => setUnViewedModalOpen(false)} />
      <UnDownloadModal open={unDownloadModalOpen} file={file} onCancel={() => setUnDownloadModalOpen(false)} />
      <ViewedModal open={viewedModelOpen} file={file} onCancel={() => setViewedModelOpen(false)} />
      <DownloadedModal open={downloadedModelOpen} file={file} onCancel={() => setDownloadedModelOpen(false)} />
      <InnerUserModal
        type={InnerUserModalType.unDownload}
        open={innerUnDownloadModalOpen}
        onCancel={() => setInnerUnDownloadModalOpen(false)}
        fileId={id as unknown as number}
      />
      <InnerUserModal
        type={InnerUserModalType.unViewed}
        open={innerUnViewedModalOpen}
        onCancel={() => setInnerUnViewedModalOpen(false)}
        fileId={id as unknown as number}
      />
    </Card>
  );
};

export default FileStatistic;
