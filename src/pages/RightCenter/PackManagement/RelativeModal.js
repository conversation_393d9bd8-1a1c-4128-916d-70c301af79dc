import React, { useState, memo, useEffect, useContext } from 'react';
import { useSelector } from 'react-redux';
import { Table, Modal, Form, message, Row, Col, Button, Select, Input } from 'antd';
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
// import FormQuery from './FormQuery'
import styles from '../antd.module.less';
import { getWidth, offsetLeft } from '@/utils/index';
import PublicTable from './PublicTable';
import { InjectData } from '@/pages/RightCenter/PackManagement/Create';

const { Option } = Select;
const RelativeModal = props => {
  const { handleCancel, visible, cb, tableList } = props;
  const equityType = useContext(InjectData)?.equityType;
  const [dataSource, setDataSource] = useState([]);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [selectedRowKeys, setSelectedRowKey] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [sortCodeOptions, setAttributeOptions] = useState([]);
  const [nameOptions, setNameOptions] = useState([]);
  const [form] = Form.useForm();
  const [entryList, setEntryList] = useState({});
  const [loading, setLoading] = useState(false);

  const handleOk = () => {
    if (!selectedRows.length) return message.error('请选择一条数据！');
    selectedRows.map(item => (item.flag = true));
    cb(selectedRows);
    handleCancel();
  };

  const hanldeSearch = () => {
    form.validateFields().then(values => {
      setDefaultQuery(values);
    });
  };
  const getYearUnitName = key => {
    return (
      {
        year: '年',
        month: '月',
        day: '日',
      }[key] || ''
    );
  };

  const PageChange = (current, pageSize) => {
    setCurrent(current);
    setPageSize(pageSize);
  };

  const sortCodeSelect = code => {
    get(allUrl.DetailedManageInterests.getDetailName, { sortCode: code }).then(res => {
      if (res.success) {
        let Dt = res.resp;
        setNameOptions(Dt);
      } else {
        // message.error(res.msg)
      }
    });
    form.resetFields(['name']);
  };

  useEffect(() => {
    console.log('equityType', equityType);
    setLoading(true);
    let goodsCategory = null;
    //goodsCategory 商品分类（1-实物类=>对应精品权益包，0-虚拟类=》对应其他权益包）
    if (equityType === 6) {
      goodsCategory = 1;
    } else {
      goodsCategory = 0;
    }

    const query = {
      ...defaultQuery,
      goodsCategory,
      pageSize: pageSize,
      pageNum: current,
    };
    // 驾行安心包：默认选择基础权益
    if (equityType === 7) {
      query.businessScene = 1;
    }
    post(allUrl.EquityPackageManage.queryDetailRelation, {
      ...query,
    }).then(res => {
      if (res.success) {
        let Dt = res.resp;
        setDataSource(Dt);
        setTotal(res.total);
      } else {
        // message.error(res.msg)
      }
      setLoading(false);
    });
  }, [defaultQuery, current, pageSize]);
  useEffect(() => {
    get(allUrl.DetailedManageInterests.getSortList, { ...defaultQuery }).then(res => {
      if (res.success) {
        setAttributeOptions(res.resp);
      }
    });
  }, [defaultQuery]);
  useEffect(() => {
    get(allUrl.common.entryLists, { codes: 'equity_business_scene' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }
        setEntryList(Dt || {});
      } else {
        //   message.error(res.message)
      }
    });

    //table勾选回显,仅针对单选的精品权益包，其他可多选的权益包因为历史遗留暂不更改
    // if (equityType === 6) {
    //     if (!!tableList.length) {
    //         const sRows = tableList.map(({ originDetail }) => originDetail)
    //         setSelectedRows(sRows)
    //         setSelectedRowKey(sRows.map(({ detailNo }) => detailNo))
    //     }
    // }
  }, []);
  const layout = {
    labelCol: { span: 7 },
    wrapperCol: { span: 16 },
  };
  return (
    <Modal
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      width={getWidth()}
      style={{ left: offsetLeft() }}
      title={
        <>
          添加关联
          <span style={{ color: '#F5222D', fontSize: 12, marginLeft: 10 }}>
            {equityType == 6
              ? '精品权益包只允许关联一条实物类权益'
              : equityType == 7
              ? '驾行安心包无法添加延保权益，已将延保权益过滤'
              : ''}
          </span>
        </>
      }
    >
      <Form name="add-detailNo" form={form} {...layout}>
        <Row>
          <Col span={7}>
            <Form.Item label="权益明细分类" name="sortCode">
              <Select allowClear placeholder="请选择" onChange={sortCodeSelect}>
                {sortCodeOptions.map((item, index) => (
                  <Option value={item.code} key={index}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={7}>
            <Form.Item label="权益明细名称" name="name">
              <Select allowClear placeholder="请选择">
                {nameOptions.map((item, index) => (
                  <Option value={item.name} key={index}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {/* 630 */}
          <Col span={7}>
            <Form.Item label="权益明细编码" name="detailNo">
              <Input allowClear placeholder="请输入权益明细编码" />
            </Form.Item>
          </Col>
          {/* entryList 如果为驾行安心包，不需要该参数，默认为基础场景*/}
          {equityType == 7?'':<Col span={7}>
            <Form.Item label="权益业务场景" name="businessScene">
              <Select allowClear placeholder="请选择">
                {entryList['equity_business_scene'] && entryList['equity_business_scene'].length
                  ? entryList['equity_business_scene']
                      .filter(i => i.value < 3)
                      .map((item, index) => (
                        <Option value={Number(item.value)} key={index}>
                          {item.name}
                        </Option>
                      ))
                  : ''}
              </Select>
            </Form.Item>
          </Col>}
          <Col span={3} style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button
              onClick={() => {
                form.resetFields();
                setDefaultQuery({});
              }}
            >
              重置
            </Button>
            <Button type="primary" onClick={hanldeSearch}>
              查询
            </Button>
          </Col>
        </Row>
        <PublicTable
          className={`${styles['blue-table']} 'TablePanel'`}
          dataSource={dataSource}
          loading={loading}
          rowKey={'detailNo'}
          pagination={{
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
          }}
          rowSelection={{
            type: equityType == 6 ? 'radio' : 'checkbox',
            selectedRowKeys,
            selectedRows,
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRowKey(selectedRowKeys);
              setSelectedRows(selectedRows);
            },
          }}
        />
      </Form>
    </Modal>
  );
};
export default memo(RelativeModal);
