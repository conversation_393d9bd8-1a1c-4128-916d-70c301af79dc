import './PerforKPI.less'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import moment from 'moment'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import {fileDown} from '@/utils'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import {roleJudgment} from '@/utils/authority'
import { Table, Button, Input, Form, DatePicker, message, Radio, Spin, Row, Col } from 'antd';
const { RangePicker } = DatePicker;
const PerforData = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading, setLoading] = useState(false)
    const [exportLoading, setExportLoading] = useState(false)
    const [dataSource, changeDataSource] = useState([])
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)
    const [total, changeTotal] = useState(0)
    const [tableHeight,setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({})
    const [radioValue, setRadioValue] = useState(0);
    
    const onChangeRadio = (e) => {
        console.log('radio checked', e.target.value);
        let val = e.target.value
        let startTime = null, endTime = null
        if(val == 1) {
            startTime = moment().format('YYYY-MM-DD')
            endTime = moment().format('YYYY-MM-DD')
        }else if(val == 2) {
            startTime = moment().day(moment().day() - 6).format('YYYY-MM-DD')
            endTime = moment().format('YYYY-MM-DD')
        }else if(val == 3) {
            startTime = moment().subtract(1, 'months').format('YYYY-MM-DD') 
            endTime = moment().format('YYYY-MM-DD')
        }else if(val == 4) {
            startTime = moment().subtract(1, 'quarters').format('YYYY-MM-DD')
            endTime = moment().format('YYYY-MM-DD')
        }
        defaultQuery.startTime = startTime
        defaultQuery.endTime = endTime
        setDefaultQuery(defaultQuery)
        setRadioValue(e.target.value);
    };
    const onChangePicker = (value, dateString) => {
        let startTime = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        let endTime = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
        defaultQuery.startTime = startTime
        defaultQuery.endTime = endTime
        setDefaultQuery(defaultQuery)
        setRadioValue(0);
    }
    const handleSearch = () => {
        getData()
    }
    const onReset = () => {
        setRadioValue(0);
        setDefaultQuery({
            startTime: null,
            endTime: null,
            servicerName: null
        })
        getData()
    }
    let searchList = [
        { label: '客服名称', name: 'servicerName', type: 'Input', placeholder: '请输入' },
        // { label: '时间', name: 'dateTime', type: 'RangePicker' },
    ]
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }

    const InforData = {
        rowKey: record => record.servicerName,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content'},
        columns: [
            { title: '接待坐席姓名', dataIndex: 'servicerName', width: 100 },
            { title: '接起会话数', dataIndex: 'sessionNum', width: 120 },
            { title: '有效会话数', dataIndex: 'sessionNumValid', width: 120 },
            { title: '无效会话数', dataIndex: 'sessionNumInvalid', width: 120 },
            { title: '客户消息量', dataIndex: 'totalMsgNumCustomer', width: 120 },
            { title: '客服消息量', dataIndex: 'totalMsgNumServicer', width: 120 },
            { title: '系统消息量', dataIndex: 'totalMsgNumSystem', width: 120 },
            { title: '最大会话时长（秒）', dataIndex: 'sessionDurationMax', width: 140 },
            { title: '平均会话时长（秒）', dataIndex: 'sessionDurationAvg', width: 140 },
            { title: '最大消息量', dataIndex: 'msgNumMax', width: 120 },
            { title: '平均消息量', dataIndex: 'msgNumAvg', width: 120 },
            { title: '首次响应最大时长（秒）', dataIndex: 'firstResponseDurationMax', width: 160 },
            { title: '首次响应平均时长（秒）', dataIndex: 'firstResponseDurationAvg', width: 160 },
            { title: '平均响应最大时长（秒）', dataIndex: 'avgResponseDurationMax', width: 160 },
            { title: '平均响应平均时长（秒）', dataIndex: 'avgResponseDurationAvg', width: 160 },
            { title: '非常满意', dataIndex: 'satisfaTimes5', width: 100 },
            { title: '满意', dataIndex: 'satisfaTimes4', width: 80 },
            { title: '一般', dataIndex: 'satisfaTimes3', width: 80 },
            { title: '不满意', dataIndex: 'satisfaTimes2', width: 80 },
            { title: '非常不满意', dataIndex: 'satisfaTimes1', width: 110 },
            { title: '净满意度', dataIndex: 'satisfaTimesNet', width: 100, render: text => text ? (text*100).toFixed(2)+'%' : '' },
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: null,
    };
    const exportExcel = () => {
        setExportLoading(true)
        setLoading(true)
        post(allUrl.WeCom.exportServicerPerfor, { ...defaultQuery }, {responseType: "blob"}).then(res => {
            fileDown(res,'客服人员数据')
            setExportLoading(false)
            setLoading(false)
        })
    }
    const getData = () => {
        setLoading(true)
        let query = { ...defaultQuery }
        let params = { pageIndex: current, pageSize, ...query }
        post(allUrl.WeCom.servicerPerfor, { ...params }).then(res => {
            if (res.success) {
                // 处理回执情况字段，把两个字段包到一个字段中
                changeDataSource(res.resp[0].list)
                changeTotal(res.resp[0].total)
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }
  
    useEffect(()=>{
        initPage()
    },[])
    useEffect(() => {
        if (userInfo) {
            getData()
        }
    }, [defaultQuery, userInfo, current, pageSize])

    const onChangeName = (e) => {
        let val = e.target.value
        defaultQuery.servicerName = e.target.value
        setDefaultQuery(defaultQuery)
    }
    
    return (
        <div className='CustomerKPI'>
            <div className='tableData'>
                {/* <PublicTableQuery  onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery} /> */}
                <Form className='PublicList_FormQuery' >
                <Row style={{ paddingRight: '20px' }}>
                    <Col xs={24} sm={12} xl={8} xxl={6} style={{ marginBottom: '24px' }}>
                        <Radio.Group onChange={onChangeRadio} value={radioValue}>
                            <Radio value={1}>今天</Radio>
                            <Radio value={2}>近一周</Radio>
                            <Radio value={3}>近一月</Radio>
                            <Radio value={4}>近三月</Radio>
                        </Radio.Group>
                    </Col>
                    <Col xs={24} sm={12} xl={8} xxl={6} style={{ marginBottom: '24px' }}>
                        <RangePicker placeholder={['开始时间','结束时间']} onChange={onChangePicker} />
                    </Col>
                    <Col xs={24} sm={12} xl={8} xxl={6} style={{ marginBottom: '24px' }}>
                        <Form.Item label="客服名称" style={{ marginBottom: 0 }}>
                            <Input
                                placeholder="请输入..."
                                allowClear={true}
                                onChange={onChangeName}
                            />
                        </Form.Item>
                    </Col>
                    <Col xs={24} sm={12} xl={8} xxl={6} className='FormQuerySubmit' style={{ marginBottom: '24px' }}>
                        <Col span={6} className='Reset'>
                            <Button onClick={onReset}>重置</Button>
                        </Col>
                        <Col span={6} className='Search'>
                            <Button type='primary' onClick={handleSearch}>查询</Button>
                        </Col>
                    </Col>
                </Row>
            </Form>
                <div className='btn-box' style={{textAlign: 'right', paddingRight: '30px'  }}>
                {
                    roleJudgment(userInfo,'WECOM_DATA_STATISTICS_CUSTOMER_SERVICE_DATA_EXPORT') ?
                    <Button type='primary' onClick={exportExcel}>导出表格</Button>
                    : null
                }
                </div>
                <Table {... InforData} />
            </div>
        </div>
    )
}
export default PerforData
