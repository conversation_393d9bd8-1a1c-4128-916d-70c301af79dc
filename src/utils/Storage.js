// 设置缓存/获取缓存
let RawData = {};
export const setStorage = ({ Query, pagination, Ary, Type, List }) => {
  return new Promise((resolve, reject) => {
    try {
      RawData[Type] = {};
      if (Query) RawData[Type].Query = Query || null;
      if (pagination) RawData[Type].pagination = pagination || null;
      if (Ary) RawData[Type].Ary = Ary || null;
      if (List) RawData[Type].List = List || null;
      resolve(true);
    } catch (error) {
      reject(false);
    }
  });
};
export const getStorage = (Type, noAsync) => {
  return noAsync
    ? RawData[Type]
    : new Promise((resolve, reject) => {
        try {
          resolve(RawData[Type] || null);
        } catch (error) {
          reject(null);
        }
      });
};
export const removeStorage = (Type) => {
  return new Promise((resolve, reject) => {
    try {
      if (RawData[Type]) {
        delete RawData[Type];
        resolve(true);
      }
    } catch (error) {
      reject(false);
    }
  });
};
