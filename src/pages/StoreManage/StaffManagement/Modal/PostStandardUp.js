import React,{useEffect, useState} from 'react'
import {Modal,Table,message} from 'antd'
import {utilsDict} from '../../../../utils/utilsDict'
import { connect } from 'react-redux'
import { get } from '../../../../utils/request'
import allUrl from '../../../../utils/url'
import './PostStandard.less'
import PostStandardDown from './PostStandardDown'
const dataSource =[
    {
        key: '1',
        name: '总经理',
        age: '',
        tel: '1',
        address: '厂端',
      },
      {
        key: '2',
        name: '交付',
        tel: '1',
        age:'交付经理',
        address: '厂端',
      },
      {
        key: '3',
        name: '交付专员A',
        age: '交付专员A',
        tel: '按35台：1人增配；月交付＞200台，分AB岗',
        address: '门店',
      },
      {
        key: '4',
        name: '交付专员B',
        age: '交付专员B',
        tel: '按35台：1人增配；月交付＞200台，分AB岗',
        address: '门店',
      },
      {
        key: '5',
        name: '交付保障专员',
        age: '交付保障专员',
        tel: '按月交付100台：1人增配',
        address: '门店',
      },
      {
        key: '6',
        name: '金融保险专员',
        age: '金融保险专员',
        tel: '按月交付150台：1人增配',
        address: '门店',
      },
      {
        key: '7',
        name: '服务',
        tel: '1',
        age: '服务经理',
        address: '厂端',
      },
      {
        key: '8',
        name: '服务总监',
        age: '按需配',
        tel: '按需配',
        address: '门店',
      },
      {
        key: '9',
        name: '前台主管',
        age: '保有量≤2500台，服务经理兼职；保有量＞2500台，配置1人',
        tel: '保有量≤2500台，服务经理兼职；保有量＞2500台，配置1人',
        address: '门店',
      },
      {
        key: '10',
        name: '服务顾问',
        age: '按保有量700台：1增配',
        tel: '按保有量700台：1增配',
        address: '门店',
      },
      {
        key: '11',
        name: '理赔顾问',
        age: '按保有量2000台：1增配',
        tel: '按保有量2000台：1增配',
        address: '门店',
      },
      {
        key: '12',
        name: '保有量≤500台，机电技师兼职；保有量＞500台，配置1人',
        tel: '保有量≤500台，机电技师兼职；保有量＞500台，配置1人',
        age:'车间主管',
        address: '门店',
      },
      {
        key: '13',
        name: '机电技师',
        age: '按保有量500台：1  增配',
        tel: '按保有量500台：1  增配',
        address: '门店',
      },
      {
        key: '14',
        name: '钣金技师',
        age: '按保有量1400台：1  增配',
        tel: '按保有量1400台：1  增配',
        address: '门店',
      },
      {
        key: '15',
        name: '喷漆技师',
        age: '按保有量2000台：1  增配',
        tel: '按保有量2000台：1  增配',
        address: '门店',
      },
      {
        key: '16',
        name: 'PDI专员',
        age: '按保有量800台：1  增配',
        tel: '按保有量800台：1  增配',
        address: '门店',
      },
      {
        key: '17',
        name: '调度员',
        tel: ' 保有量≤3000台，车间主管兼职；保有量＞3000台，配置1人',
        age: '保有量≤3000台，车间主管兼职；保有量＞3000台，配置1人',
        address: '门店',
      },
      {
        key: '18',
        name: '洗车工',
        age: '保有量1600台：1  增配',
        tel: '保有量1600台：1  增配',
        address: '门店',
      },
      {
        key: '19',
        name: '技术专家',
        age: '1',
        tel: '1',
        address: '厂端',
      },
      {
        key: '20',
        name: '保修专员',
        age: '按保有量800台：1  增配',
        tel: '按保有量800台：1  增配',
        address: '门店',
      },
      {
        key: '21',
        name: '技术内训师',
        age: '技术专家兼',
        tel: '技术专家兼',
        address: '门店',
      },
      {
        key: '22',
        name: '质检专员',
        tel: '按保有量3500台：1  增配',
        age:'按保有量3500台：1  增配',
        address: '门店',
      },
      {
        key: '23',
        name: '配件主管',
        age: '1',
        tel: '1',
        address: '门店',
      },
      {
        key: '24',
        name: '配件计划专员',
        age: '按保有量2000台：1  增配',
        tel: ' 按保有量2000台：1  增配',
        address: '门店',
      },
      {
        key: '25',
        name: '配件仓储管理专员',
        age: '按保有量4000台：1  增配',
        tel: '按保有量4000台：1  增配',
        address: '门店',
      },
      {
        key: '26',
        name: '精品附件经理',
        age: '验收3个月后配置1人',
        tel: '验收3个月后配置1人',
        address: '门店',
      },
      {
        key: '27',
        name: '精品附件专员（售前）',
        tel: ' 按保有量2000台：1  增配',
        age: '按保有量2000台：1  增配',
        address: '门店',
      },
      {
        key: '28',
        name: '用户关爱经理',
        age: '1',
        tel: '1',
        address: '厂端',
      },
      {
        key: '29',
        name: '用户关爱专员',
        age: '按保有量200台：1  增配，最少为1人',
        tel: '按保有量200台：1  增配，最少为1人',
        address: '门店',
      },
      {
        key: '30',
        name: '服务体验专员',
        age: '战略城市必备1人',
        tel: '战略城市必备1人',
        address: '门店',
      },
      {
        key: '31',
        name: '服务体验专员',
        age: '非战略城市按保有量＞300，配置1人',
        tel: ' 非战略城市按保有量＞300，配置1人',
        address: '门店',
      },
      {
        key: '32',
        name: '用户活动专员',
        tel: '战略城市必备1人',
        age: '战略城市必备1人',
        address: '门店',
      },
      {
        key: '33',
        name: '用户活动专员',
        age: '非战略城市按保有量＞300，配置1人',
        tel: ' 非战略城市按保有量＞300，配置1人',
        address: '门店',
      },
]
dataSource.map((item,index)=>item.key=index+1)

const PostStandardUp = (props) =>{
    const {visible,onCancel,userInfo} = props
    const [PostStandardInfo,setPostStandardInfo] = useState({})
    const [InforData4,setInforData4] = useState({
        rowKey: record => record.key,
        bordered: true,
        size:'small',
        dataSource,
        scroll: { x: 'max-content' },
        columns: [
            {
                title: '分类',
                dataIndex: 'name',
                align:'center',
                width:'100px',
                render: (text, row, index) => {

                    if (index === 0) {

                        return {

                            children: <div style={{paddingLeft:'95px'}}>{text}</div>,

                            props:{colSpan:2}

                        }

                    } else if (index === 6){
                          return {

                            children: <div style={{textAlign:'center'}}>{text}</div>,

                            props:{rowSpan:27}

                        }
                    }
                    else if (index === 1){
                        return {

                          children: <div style={{textAlign:'center'}}>{text}</div>,

                          props:{rowSpan:5}

                      }
                  }
                    else {
                        return text
                    }
                    

                }
              },
              {
                title: '岗位',
                dataIndex: 'age',
                align:'center',
                render: (text, row, index) => {

                    if (index === 0) {

                        return {

                            children:<b>{text}</b>,

                            props:{colSpan:0}

                        }

                    } else if (31>index > 6){
                        return {

                          children: <div style={{textAlign:'center'}}>{text}</div>,

                          props:{rowSpan:0}

                      }
                  } else if (index === 29){
                    return {

                      children: <div style={{textAlign:'center'}}>{text}</div>,

                      props:{rowSpan:2}

                  }
              }
              else if (index === 30){
                return {

                  children: <div style={{textAlign:'center'}}>{text}</div>,

                  props:{rowSpan:0}

              }
          }
          
                  else if (index === 31){
                    return {

                      children: <div style={{textAlign:'center'}}>{text}</div>,

                      props:{rowSpan:2}

                  }
              }
              else if (index ===32){
                return {

                  children: <div style={{textAlign:'center'}}>{text}</div>,

                  props:{rowSpan:0}

              }
          }
                    else if (index === 6){
                        return {

                          children: <div>{text}</div>,

                          props:{colSpan:1}

                      }
                  }
                  
                    else if (index === 2){
                        return {

                          children: <div style={{textAlign:'center'}}>{text}</div>,

                          props:{rowSpan:0}

                      }
                  }
                  else if (index === 3){
                    return {

                      children: <div style={{textAlign:'center'}}>{text}</div>,

                      props:{rowSpan:0}

                  }
              }
              else if (index === 4){
                return {

                  children: <div style={{textAlign:'center'}}>{text}</div>,

                  props:{rowSpan:0}

              }
          }else if (index === 5){
            return {

              children: <div style={{textAlign:'center'}}>{text}</div>,

              props:{rowSpan:0}

          }
      }else if (index === 5){
            return {

              children: <div style={{textAlign:'center'}}>{text}</div>,

              props:{rowSpan:0}

          }
      }else if (index === 32){
        return {

          children: <div style={{textAlign:'center'}}>{text}</div>,

          props:{rowSpan:2}

      }
  }
                    else {
                        return text
                    }

                }
              },
              {
                title: '备注',
                // colSpan: 2,
                align:'center',
                dataIndex: 'tel',
                render: (text, row, index) => {

                    if (index === 2) {

                        return {

                            children: <div style={{textAlign:'center'}}>{text}</div>,

                            props:{rowSpan:2}

                        }

                    } 
                    else if (index > 6){
                        return {

                          children: <div style={{textAlign:'center'}}>{text}</div>,

                          props:{colSpan:0}

                      }
                  }
                    else if (index === 3){
                          return {

                            children: <div style={{textAlign:'center'}}>{text}</div>,

                            props:{rowSpan:0}

                        }
                    }else if (index === 7){
                        return {

                          children: <div style={{textAlign:'center'}}>{text}</div>,

                          props:{colSpan:1}

                      }
                  }
                    else {
                        return text
                    }
                    

                }
              },
              // {
              //   title: '备注2',
              //   dataIndex: 'address',
              //   render: (text, row, index) => {

              //       if (index === 3) {

              //           return {

              //               children: <div>{text}</div>,

              //               props:{rowSpan:1}

              //           }

              //       } 
              //       else {
              //           return text
              //       }
                    

              //   }
              // },
        ],
        pagination: false,
    })
    const handleOk = () =>{

    }

    const getlistByOrganizationIds = () =>{
        if(userInfo && userInfo.organizationIds && userInfo.organizationIds.length){
            get(allUrl.StoreManage.listByOrganizationIds,{organizationIds:userInfo?userInfo.organizationIds.join(','):''}).then(res=>{
                if(res && res.success){
                    let Dt = res.resp?res.resp[0]:{}
                    // Dt.dealerType = 1
                    // let newInforData1 = {...InforData2}
                    // let newInforData2 = {...InforData3}
                    // let target1 = newInforData1.dataSource.filter(item=>item.grade === Dt.level)
                    // let target2 = newInforData2.dataSource.filter(item=>item.grade === Dt.level)
                    // newInforData1.dataSource = target1
                    // newInforData2.dataSource = target2
                    // setInforData2(newInforData1)
                    // setInforData3(newInforData2)
                    setPostStandardInfo(Dt)
                }else{
                    // message.error(res.msg)
                }
            })
        }else{
            setPostStandardInfo({})
        }
    }

    useEffect(()=>{
        getlistByOrganizationIds()
    },[userInfo])

    return <Modal title={'岗位和人员标准'} wrapClassName='PostStandardUp' visible={visible} onOk={handleOk} onCancel={onCancel} width={1300} footer={false} maskClosable={false}>
        {<>
            <div style={{marginBottom: 30}}>
              <h4 style={{marginBottom: 10}}>用户中心岗位配置</h4>
                <Table {...InforData4} />
              
            </div>
            <div>
              <h4 style={{marginBottom: 10}}>用户中心营业验收配置标准</h4>
              <PostStandardDown/>
        </div>
            </>
        }
    </Modal>
}
export default connect(({ common }) => {
    const {userInfo } = common
    return {
        userInfo
    }
})(PostStandardUp)