/**
 * 包含组织，岗位，账户三个tab的选择弹框，历史版本留着备用。
 */
import React, { useState, useEffect } from 'react'
import { Modal, Row, Col, Tabs, Table, Input, Divider, message, Tree, Checkbox, Button } from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import _ from 'lodash'

import { DeleteOutlined } from '@ant-design/icons';
import './SendingRange.less'
const { TabPane } = Tabs;
const SendingRange = (props) => {
    const { onCancel, visible,Type,cb,targetList,formData } = props
    const [loading, setLoading] = useState(false)
    const [pageSize2, setPageSize2] = useState(10)
    const [pageSize3, setPageSize3] = useState(10)
    const [current2, setCurrent2] = useState(1)
    const [current3, setCurrent3] = useState(1)
    const [total2, setTotal2] = useState(0)
    const [total3, setTotal3] = useState(0)
    const [OrgtreeData, setOrgtreeData] = useState([])
    const [dataSource2, setDataSource2] = useState([])
    const [dataSource3, setDataSource3] = useState([])
    const [defaultDataSource3, setDefaultDataSource3] = useState([])
    const [tabsKey, setTabsKey] = useState('1')
    const [defaultQuery, setDefaultQuery] = useState({
        pageNum: 1,
        pageSize: 10
    })
    const [selectedRowKeys1, setSelectedRowKeys1] = useState([])
    const [selectedRowKeys2, setSelectedRowKeys2] = useState([])
    const [selectedRowKeys3, setSelectedRowKeys3] = useState([])
    const [selectedRows1, setSelectedRows1] = useState([])
    const [selectedRows2, setSelectedRows2] = useState([])
    const [selectedRows3, setSelectedRows3] = useState([])
    const [orgtreeselectedKeys, setOrgtreeselectedKeys] = useState([])
    const [orgtreeSelectedRows, setOrgtreeSelectedRows] = useState([])
    const [allRows, setAllRows] = useState([])
    const [isOrgAll, setIsOrgAll] = useState(false)
    const [OrgTreeDataFlatten, setOrgTreeDataFlatten] = useState([])
    const [isIndeterminate, setIsIndeterminate] = useState(false)
    const [entryList, setEntryList] = useState({})
    const hanldeOk = (props) => {
        let data = [...selectedRows3,...selectedRows2,...orgtreeSelectedRows,...allRows]
        let temp = []
        data.forEach(item=>{
            let obj = {}
            if(Type==='EDIT'){
                obj.id = item.id
                obj.qwAgentMessageId = formData.qwAgentMessageId
            }else{
                delete obj.id
                delete obj.qwAgentMessageId
            }
            if(item.isAll){
                obj.type = 4
                obj.targetId = 0
                obj.targetName = item.title
            }
            else if(item.userId){
                obj.type = 3
                obj.targetId = item.userId
                obj.targetName = item.userName
                obj.qwId = item.qywxUserId
            }else if(item.entryMeaning){
                obj.type = 2
                obj.targetId = item.entryValue
                obj.targetName = item.entryMeaning
            }else if(item.title){
                obj.type = 1
                obj.targetId = item.key
                obj.targetName = item.title
            }
            temp.push(obj)
        })
        cb(temp)
        onCancel()
    }
    const PageChange2 = (current, pageSize) => {
        setCurrent2(current)
        setPageSize2(pageSize)
    }
    const PageChange3 = (current, pageSize) => {
        setCurrent3(current)
        setPageSize3(pageSize)
        setDefaultQuery({
            ...defaultQuery,
            pageNum: current,
            pageSize: pageSize
        })
    }
    const InforData2 = {
        rowKey: record => record.entryValue,
        bordered: true,
        dataSource: dataSource2,
        loading,
        // scroll: { x: 'max-content',y:tableHeight },
        scroll: { x: 'max-content', y: 470 },
        columns: [
            { title: '序号', dataIndex: 'index', width: 100,render:(text,record,index)=>index+1 },
            { title: '岗位名称', dataIndex: 'entryMeaning', width: 180 },
            // { title: '岗位编码', dataIndex: 'entryValue', width: 100 },
        ],
        pagination: {
            pageSize: pageSize2,
            onChange: PageChange2,
            current: current2,
            total: total2,
            showTotal: () => `共${total2}条，${pageSize2}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange2,
        },
        rowSelection: {
            selectedRowKeys: selectedRowKeys2,
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRowKeys2(selectedRowKeys)
                setSelectedRows2(selectedRows)
            },
            getCheckboxProps:()=>{
                return{disabled:isOrgAll?true:false}
            }
        },
    };
    // 账号标签页，数据集
    const InforData3 = {
        rowKey: record => record.userId,
        bordered: true,
        dataSource: dataSource3,
        loading,
        // scroll: { x: 'max-content',y:tableHeight },
        scroll: { x: 'max-content', y: 470 },
        columns: [
            { title: '姓名', dataIndex: 'userName', width: 100 },
            { title: '手机号', dataIndex: 'mobilePhone', width: 100 },
            { title: '岗位', dataIndex: 'positionName', width: 100 },
            // { title: '所属组织', dataIndex: 'orgName', width: 100 },
        ],
        size: 'middle',
        pagination: {
            pageSize: pageSize3,
            onChange: PageChange3,
            current: current3,
            total: total3,
            showTotal: () => `共${total3}条，${pageSize3}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange3,
        },
        rowSelection: {
            selectedRowKeys: selectedRowKeys3,
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRowKeys3(selectedRowKeys)
                let arr = [], arr2 = []
                defaultDataSource3.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userId)!==-1)arr.push(item)
                })
                dataSource3.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userId)!==-1)arr.push(item)
                })
                // 过滤取消的账号
                arr = selectedRows3.concat(arr)
                arr.length && arr.forEach((i) => {
                    selectedRowKeys.forEach((j) => {
                        if(i.userId == j) {
                            arr2.push(i)
                        }
                    })
                })
                // 在重置或者切换搜索条件的时候，需要记录之前已经勾选过的数据
                setSelectedRows3( unique([...new Set(arr2)],'userId') )
            },
            getCheckboxProps:()=>{
                return{disabled:isOrgAll?true:false}
            }
        },
    };
    // 添加筛选条件后，勾选账号时会出现重复数据，需要给对象数组去重
    const unique = (arr, key) => {
        if (!arr) return arr
        if (key === undefined) return [...new Set(arr)]
        const map = {
            'string': e => e[key],
            'function': e => key(e),
        }
        const fn = map[typeof key]
        const obj = arr.reduce((o,e) => (o[fn(e)]=e, o), {})
        return Object.values(obj)
    }
    const TabsChange = (key) => {
        setDefaultQuery({})
        setTabsKey(key)
    }
    const handleSearch = (value, type, field) => {
        let obj = {}
        obj[field] = value
        setDefaultQuery(obj)
    }
    const PersonnelDel1 = (row) => {
        let rows = [...orgtreeSelectedRows]
        let keys = [...orgtreeselectedKeys]
        rows = rows.filter(item => item.key !== row.key)
        keys = keys.filter(item => item !== row.key)
        setOrgtreeSelectedRows(rows)
        setOrgtreeselectedKeys(keys)
    }
    const PersonnelDel2 = (row) => {
        let rows = [...selectedRows2]
        let keys = [...selectedRowKeys2]
        rows = rows.filter(item => item.entryID !== row.entryID)
        keys = keys.filter(item => item !== row.entryValue)
        console.log(row)
        console.log(rows)
        console.log(keys)
        setSelectedRows2(rows)
        setSelectedRowKeys2(keys)
    }
    const PersonnelDel3 = (row) => {
        let rows = [...selectedRows3]
        let keys = [...selectedRowKeys3]
        rows = rows.filter(item => item.userId !== row.userId)
        keys = keys.filter(item => item !== row.userId)
        setSelectedRows3(rows)
        setSelectedRowKeys3(keys)
    }
    const PersonnelDel4 = (row) => {
        setAllRows([])
        setIsOrgAll(false)
    }
    const getQYWXInfo = (arr, newData) => {
        get(allUrl.Authority.getUserInfo, { userIds: arr.join(',') }).then(res => {
            if (res && res.success) {
                let Dt = res.resp[0]
                newData.forEach(item => {
                    for (let i in Dt) {
                        if (item.id === Number(i)) {
                            item.qywxUserId = Dt[i]?.qywxUserId
                            item.partTimePosition = Dt[i]?.partTimePosition
                            item.qywxStatus = Dt[i]?.qywxStatus
                        }
                    }
                })
                setDataSource3(newData)
            } else {
                // message.error(res.msg)
            }
        })
    }
    // 获取所有子节点
    const getAllChild = nodeData => {
        let arr = []
        arr.push(nodeData)
        
        if (nodeData.children && nodeData.children.length) {
            const fn = arr1 => {
                arr1.forEach(item => {
                    if (item.children && item.children.length) {
                        arr.push(item)
                        fn(item.children)
                    } else {
                        arr.push(item)
                    }
                })
            }

            fn(nodeData.children)
        }

        return arr
    }
    // 删除重复的节点（当选择父节点时会需要使用到来计算已选择对象的和）
    const removeDuplicateObj = (arr, key) => {
        let obj = {};

        arr = arr.reduce((newArr, next) => {
        //   obj[next[key]] ? "" : (obj[next[key]] = true && newArr.push(next));

            if ( obj[next[key]] ) {
                obj[next[key]] = ""
            } else {
                obj[next[key]] = true
                newArr.push(next)
            }

            return newArr;
        }, []);

        return arr;
    };
    // 处理点击树节点标题
    const treeTitleClick = (nodeData) => {
        if (!isOrgAll) {
            const allChild = getAllChild(nodeData)
            let newOrgtreeSelectedRows = [...orgtreeSelectedRows]
            let newOrgtreeselectedKeys = [...orgtreeselectedKeys]

            console.log(allChild)
            console.log(newOrgtreeSelectedRows)
            console.log(newOrgtreeselectedKeys)

            if (newOrgtreeselectedKeys.indexOf(nodeData.key) > -1) {  //取消
                let index = newOrgtreeselectedKeys.indexOf(nodeData.key)
                newOrgtreeSelectedRows.splice(index, 1)
                newOrgtreeselectedKeys.splice(index, 1)

                // 取消选中所有子节点
                allChild.forEach(ele => {
                    newOrgtreeSelectedRows = newOrgtreeSelectedRows.filter(item => item.key !== ele.key)
                    newOrgtreeselectedKeys = newOrgtreeselectedKeys.filter(item => item !== ele.key)
                })
            } else {  //勾选
                newOrgtreeSelectedRows.push(nodeData)
                newOrgtreeselectedKeys.push(nodeData.key)

                // 选中所有子节点
                newOrgtreeSelectedRows.push(...allChild)
                newOrgtreeselectedKeys.push(...allChild.map(item=>item.key))
            }

            setOrgtreeSelectedRows(newOrgtreeSelectedRows)
            setOrgtreeselectedKeys(newOrgtreeselectedKeys)

            // 点击父节点的名称，会添加一次父节点本身数据，在此删除掉已选的重复的节点
            setOrgtreeSelectedRows(removeDuplicateObj(newOrgtreeSelectedRows, 'key'))
            setOrgtreeselectedKeys([...new Set(newOrgtreeselectedKeys)])
        }


        // if (newOrgtreeSelectedRows.length) {
        //     setIsIndeterminate(true)
        // } else if (newOrgtreeSelectedRows.length === OrgTreeDataFlatten.length) {
        //     setIsIndeterminate(false)
        // } else if (!newOrgtreeSelectedRows.length) {
        //     setIsIndeterminate(false)
        // }
    }
    // 处理点击节点名称，如果点击父节点名称，则只取消勾选父节点，不取消已勾选的子节点
    const Uncheck = row => {
        let rows = [...orgtreeSelectedRows]
        let keys = [...orgtreeselectedKeys]

        if (keys.indexOf(row.key) !== -1) {
            rows = rows.filter(item => item.key !== row.key)
            keys = keys.filter(item => item !== row.key)
        } else {
            rows.push(row)
            keys.push(row.key)
        }

        setOrgtreeSelectedRows(rows)
        setOrgtreeselectedKeys(keys) 
    }
    const treeTitleRender = (nodeData) => {
        return <>
            <div style={{ marginRight: '40px', flex: 1 }}>
                <Checkbox checked={orgtreeselectedKeys.indexOf(nodeData.key) > -1 ? true : false} disabled={isOrgAll?true:false} onClick={() => treeTitleClick(nodeData)}>
                    <div onClick={()=>Uncheck(nodeData)}>{nodeData.title}</div>
                </Checkbox>
            </div>
        </>
    }
    const OrgCleckAll = (e) => {
        console.log(e.target.checked)
        // if (e.target.checked) {
        //     // console.log(treeDatafilter())
        //     let rows = [...OrgTreeDataFlatten]
        //     let rowKeys = []
        //     OrgTreeDataFlatten.forEach(item => rowKeys.push(item.key))
        //     console.log(rows, rowKeys)
        //     setOrgtreeSelectedRows(rows)
        //     setOrgtreeselectedKeys(rowKeys)
        // } else {
        //     setOrgtreeSelectedRows([])
        //     setOrgtreeselectedKeys([])
        // }
        // setIsIndeterminate(false)
        // setIsOrgAll(e.target.checked)

        if(e.target.checked){
            setOrgtreeSelectedRows([])
            setOrgtreeselectedKeys([])
            setSelectedRowKeys2([])
            setSelectedRowKeys3([])
            setSelectedRows2([])
            setSelectedRows3([])
            setAllRows([{
                title:'全部数据',
                isAll:true
            }])
        }else{
            setAllRows([])
        }
        setIsOrgAll(e.target.checked)
    }
    const treeDatafilter = (arr) => {
        let newArr = _.cloneDeep({ obj: arr }).obj
        let res = []  // 用于存储递归结果（扁平数据）
        // 递归函数
        const fn = (source) => {
            source.forEach(el => {
                res.push(el)
                if(el.children && el.children.length > 0){
                    fn(el.children)
                }
            })
        }
        fn(newArr)
        return res
    }
    const onSearch = (values) => {
        const obj = {...values, pageNum: 1, pageSize: 10}
        setDefaultQuery(obj)
    }
    useEffect(() => {
        setLoading(true)
        if (tabsKey === '3') {
            post(allUrl.Authority.messageSendOrgList, { ...defaultQuery }).then(res => {
                if (res && res.success) {
                    let Dt = res.resp
                    setOrgtreeData(Dt)
                    setOrgTreeDataFlatten(treeDatafilter(Dt))
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
        if (tabsKey === '2') {
            get(allUrl.common.entryLists, { codes: 'scrm_position' }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    for (let i in Dt) {
                        Dt[i].forEach(item => {
                            item.name = item.entryMeaning
                            item.value = item.entryValue
                            item.entryID = item.id
                            delete item.id
                        })
                    }
                    setDataSource2(Dt['scrm_position'])
                    setTotal2(Dt['scrm_position'].length)
                } else {
                    // message.error(res.message)
                }
                setLoading(false)
            })
        }
        if (tabsKey === '1') {
            get(allUrl.MessageManage.getUser, { ...defaultQuery }).then(res => {
                if (res && res.success) {
                    // 手机号脱敏
                    res.resp[0].list.forEach(function(item){
                        if(item && item.mobilePhone){
                            item.mobilePhone=item.mobilePhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                        }
                    })
                    setTotal3(res.resp[0].total)
                    setDataSource3(res.resp[0].list)
                    if(!defaultQuery.userName || defaultQuery.userName === ''){
                        setDefaultDataSource3(res.resp[0].list)
                    }
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }, [defaultQuery, tabsKey])

    useEffect(() => {
        // 获取岗位接口数据
        get(allUrl.common.entryLists, { codes: 'scrm_position' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setEntryList(Dt || {})
            } else {
                // message.error(res.message)
            }
        })
    }, [])

    // Effect Hook
    useEffect(()=>{
        console.log(targetList)
        // if(Type === 'EDIT'){
            let OrgRows = [],OrgKeys = [];
            let RoleRows = [],RoleKeys = [];
            let AccountRows = [],AccountKeys = [];
            let AllRows = [];
            targetList.forEach(item=>{
                if(item.type === 1){    //组织
                    let obj = {
                        key:Number(item.targetId),
                        title:item.targetName,
                        qwAgentMessageId:item.qwAgentMessageId,
                        id:item.id,
                        type:item.type
                    }
                    OrgRows.push(obj)
                    OrgKeys.push(Number(item.targetId))
                }
                if(item.type === 2){    //岗位
                    let obj = {
                        entryMeaning:item.targetName,
                        entryValue:item.targetId,
                        id:item.id,
                        qwAgentMessageId:item.qwAgentMessageId,
                        type:item.type
                    }
                    RoleRows.push(obj)
                    RoleKeys.push(item.targetId)
                }
                if(item.type === 3){    //账号
                    let obj = {
                        id:item.id,
                        qwAgentMessageId:item.qwAgentMessageId,
                        type:item.type,
                        qywxUserId:item.qwId,
                        userId:Number(item.targetId),
                        userName:item.targetName
                    }
                    AccountRows.push(obj)
                    AccountKeys.push(Number(item.targetId))
                }
                if(item.type === 4){
                    let obj = {
                        title:item.targetName
                    }
                    AllRows.push(obj)

                }
            })
            setOrgtreeSelectedRows(OrgRows)
            setOrgtreeselectedKeys(OrgKeys)

            setSelectedRows2(RoleRows)
            setSelectedRowKeys2(RoleKeys)

            setSelectedRows3(AccountRows)
            setSelectedRowKeys3(AccountKeys)

            if(AllRows.length){
                setAllRows(AllRows)
                setIsOrgAll(true)
            }
        // }
    },[targetList])

    // 筛选条件项
    let searchList = [
        { label: '姓名', name: 'userName', type: 'Input', placeholder: '请输入姓名', colSpan: 6 },
        { label: '门店状态', name: 'businessStatus', type: 'Select', placeholder: '请选择', colSpan: 6,
          data: [
            { name:'营业中', value:'*********' },
            { name:'停业', value:'*********' },
            { name:'搬迁', value:'*********' },
            { name:'改建', value:'*********' },
            { name:'在建', value:'*********' },
            { name:'拟退网', value:'*********' }
          ]
        },
        { label: '岗位', name: 'position', type: 'Select', placeholder: '请选择', colSpan: 6, data: entryList['scrm_position'] || [] }
    ]

    return <Modal wrapClassName='SendingRange' title='选择发消息的范围' visible={visible} onCancel={onCancel} onOk={hanldeOk} width={1000} maskClosable={false}>
        <Row gutter={[12]}>
            <Col span={18} className='SendingRangeLeft'>
                {/* <Checkbox style={{position:'absolute',top:'14px',right:'0'}} checked={isOrgAll} indeterminate={isIndeterminate} onClick={(e) => OrgCleckAll(e)}>全选</Checkbox> */}
                <Tabs defaultActiveKey="1" onChange={TabsChange} active={tabsKey}>
                    <TabPane tab="账号" key="1">
                        <PublicTableQuery tabsKey={tabsKey} onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery} />
                        <Table {...InforData3} />
                    </TabPane>
                    <TabPane tab="岗位" key="2">
                        <Table {...InforData2} />
                    </TabPane>
                    <TabPane tab="组织" key="3">
                        <Input.Search style={{ marginBottom: '16px' }} placeholder='搜组织关键字' onSearch={(value) => handleSearch(value, 3, 'name')} allowClear='true' />
                        {
                            OrgtreeData.length ?
                                <Tree multiple={true} defaultSelectedKeys={orgtreeselectedKeys} defaultExpandedKeys={[1]} treeData={OrgtreeData} titleRender={treeTitleRender} />
                                : null
                        }
                    </TabPane>
                </Tabs>
            </Col>
            <Col span={6} className='SendingRangeRight'>
                <div className='title'>已选择对象<span className='selectedSum'>（{ orgtreeSelectedRows.length+selectedRows2.length+selectedRows3.length }）</span></div>
                <div className='contentBox'>
                    {
                        allRows.length ?allRows.map((item, index) => {
                            return <Row key={index} className='content_Item'>
                                <Col span={21} className='name'>{item.title}</Col>
                                <Col span={3} className='del'>
                                    <img src={require('../../../../assets/img/trash.png')} onClick={() => PersonnelDel4(item)} />
                                </Col>
                            </Row>
                        }):null
                    }

                    {/* 组织区域 */}
                    {
                        orgtreeSelectedRows.length ?orgtreeSelectedRows.map((item, index) => {
                            return <Row key={index} className='content_Item'>
                                <Col span={21} className='name'>{item.title}</Col>
                                <Col span={3} className='del'>
                                    <img src={require('../../../../assets/img/trash.png')} onClick={() => PersonnelDel1(item)} />
                                </Col>
                            </Row>
                        }):null
                    }

                    {/* 分割线 */}
                    {
                        orgtreeSelectedRows.length && selectedRows2.length ?
                            <div className='separate-line'></div>
                            : null
                    }

                    {/* 岗位区域 */}
                    {
                        selectedRows2.map((item, index) => {
                            return <Row key={index} className='content_Item'>
                                <Col span={21} className='name'>{item.entryMeaning}</Col>
                                <Col span={3} className='del'>
                                    <img src={require('../../../../assets/img/trash.png')} onClick={() => PersonnelDel2(item)} />
                                </Col>
                            </Row>
                        })
                    }

                    {/* 分割线 */}
                    {
                        (orgtreeSelectedRows.length || selectedRows2.length) && selectedRows3.length ?
                            <div className='separate-line'></div>
                            : null
                    }

                    {/* 账号区域 */}
                    {
                        selectedRows3.map((item, index) => {
                            return <Row key={index} className='content_Item'>
                                <Col span={21} className='name'>{item.userName}</Col>
                                <Col span={3} className='del'>
                                    <img src={require('../../../../assets/img/trash.png')} onClick={() => PersonnelDel3(item)} />
                                </Col>
                            </Row>
                        })
                    }
                </div>
            </Col>
        </Row>
    </Modal>
}
export default SendingRange
