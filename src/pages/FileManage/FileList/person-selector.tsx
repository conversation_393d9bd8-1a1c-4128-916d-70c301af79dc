import { postSeresFileUserPartnerPage } from '@/apis/file-manage';
import { useRequest } from '@/hooks/request';
import { Button, Input, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

interface PersonSelectorProps {
  value?: FileManage.SeresUserDTO[];
  onChange?: (value: FileManage.SeresUserDTO[]) => void;
  rowKey?: string; // 唯一键属性名称, 默认为'id',
  orgCodeList?: string[];
}

const PersonSelector: React.FC<PersonSelectorProps> = ({ value, onChange, rowKey = 'id', orgCodeList }) => {
  const [selectedRow, setSelectedRow] = useState<FileManage.SeresUserDTO[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [query, setQuery] = useState('');

  useEffect(() => {
    if (value) {
      setSelectedRow(value);
    }
  }, [value]);

  const { data: [{ list: data = [] } = { list: [] }] = [], loading } = useRequest(
    () => {
      console.log('orgCodeList:', orgCodeList);
      if (!orgCodeList || orgCodeList?.length === 0) {
        return Promise.resolve([]);
      }
      return postSeresFileUserPartnerPage({ pageNum: 1, pageSize: 9999, searchText: query, orgCodeList });
    },
    { deps: [query, orgCodeList] }
  );

  // 表格列配置
  const columns: ColumnsType<FileManage.SeresUserDTO> = [
    { title: '供应商', dataIndex: 'organization', key: 'organization' },
    { title: '姓名', dataIndex: 'userName', key: 'userName' },
    { title: '工号', dataIndex: 'userOneId', key: 'userOneId' },
    { title: '岗位', dataIndex: 'position', key: 'position' },
  ];

  // 表格行选择配置
  const rowSelection = {
    preserveSelectedRowKeys: true,
    onChange: (_, selectedRows: FileManage.SeresUserDTO[]) => {
      setSelectedRow(selectedRows);
      if (onChange) {
        onChange(selectedRows);
      }
    },
    hideSelectAll: true,
    selectedRowKeys: selectedRow.map(row => row[rowKey]).filter((key): key is React.Key => key !== undefined),
  };

  const handleSearch = () => {
    setQuery(searchValue); // 更新查询条件
  };

  // 重置查询和选择
  const handleReset = () => {
    setSearchValue('');
    setQuery('');
  };

  const handleRemoveFile = (key: React.Key) => {
    setSelectedRow(prev => {
      const updatedFiles = prev.filter(row => row[rowKey] !== key);
      if (onChange) {
        onChange(updatedFiles);
      }
      return updatedFiles;
    });
  };

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder="请输入姓名或工号"
          value={searchValue}
          onChange={e => setSearchValue(e.target.value)}
          style={{ width: 350 }}
          onPressEnter={handleSearch}
        />
        <Button type="primary" onClick={handleSearch}>
          查询
        </Button>
        <Button onClick={handleReset}>重置</Button>
      </Space>

      <Table
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        rowKey={record => record[rowKey] as string}
        scroll={{ y: 275 }}
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
      />

      <div style={{ marginTop: 16 }}>
        {selectedRow.map(row => (
          <Tag
            key={row[rowKey]}
            closable
            onClose={() => handleRemoveFile(row[rowKey] as React.Key)}
            title={row.userName}
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              maxWidth: '100%',
              marginBottom: 8,
            }}
          >
            <span
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: 150,
              }}
            >
              {`${row.userName}${row.userOneId}`}
            </span>
          </Tag>
        ))}
      </div>
    </div>
  );
};

export default PersonSelector;
