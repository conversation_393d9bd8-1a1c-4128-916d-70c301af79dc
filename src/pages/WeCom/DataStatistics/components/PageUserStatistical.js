import React, { useState } from "react";
import { Mo<PERSON>, <PERSON>, But<PERSON>, message, Table } from "antd";
import { useSelector } from "react-redux";
import allUrl from "@/utils/url";
import { post } from "@/utils/request";
import PublicTooltip from "@/components/Public/PublicTooltip";
import DeliveryOrderList from "./DeliveryOrderList";
import moment from "moment";
import { roleJudgment } from "@/utils/authority";
import { useEffect } from "react";
import PublicTable from "@/components/Public/PublicTable";
import Query from "./Query";
import PublicTableQuery from "@/components/Public/PublicTableQuery";
import Cookies from "js-cookie";

export default (props) => {
  const { visible, title, onCancel } = props;
  const { userInfo } = useSelector((state) => state.common);
  const [recordObj, setRecordObj] = useState({});
  const [visible2, setVisible2] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportVisible, setExportVisible] = useState(false);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  
  const [defaultQuery, setDefaultQuery] = useState({
    // lastFollowTime:[moment().subtract(1,'months'),moment()],
    // createTime: [moment().subtract(1, 'months'), moment()],
  });
  /**
   *
   * @param {*} url 后台返回的请求地址
   * @param {*} fn 回调函数
   */
  const getOssBuffer = (url, fn) => {
    var xhr = new XMLHttpRequest();
    xhr.responseType = "blob";
    xhr.open("get", url, true);
    xhr.setRequestHeader("x-oss-meta-authorization", Cookies.get("scrm_token"));
    xhr.setRequestHeader("authorization", Cookies.get("scrm_token"));
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status == 200) {
        console.log("xhr", xhr);
        fn(xhr.response);
      } else {
        return false;
      }
    };
    xhr.send();
  };
  const ExportExcel = () => {
    setExportLoading(true);
    post(allUrl.WeCom.exprotNoGroup, {}).then((res) => {
      if (res.success && res.resp[0]) {
        getOssBuffer(res.resp[0].url, (data) => {
          message.success("导出成功！");
          let blob = new Blob([data], { type: data.type });
          console.log("blob", moment(new Date()).format("YYYY-MM-DD HH:MM:SS"));
          if (window.navigator.msSaveOrOpenBlob) {
            //兼容ie
            window.navigator.msSaveBlob(
              blob,
              `未建群明细${moment(new Date()).format("YYYY-MM-DD HH:MM:SS")}` || "未知文件"
            );
          } else {
            let downloadElement = document.createElement("a");
            let href = window.URL.createObjectURL(blob); //创建下载的链接
            downloadElement.href = href;
            downloadElement.download =
              `未建群明细${moment(new Date()).format("YYYY-MM-DD HH:MM:SS")}` || "未知文件"; //下载后文件名
            document.body.appendChild(downloadElement);
            downloadElement.click(); //点击下载
            document.body.removeChild(downloadElement); //下载完成移除元素
            window.URL.revokeObjectURL(href); //释放掉blob对象
          }
        });
      } else {
        // message.error(res.msg);
      }
      setExportLoading(false);
      setExportVisible(false);
    });
  };
  const serialNumber = (pageIndex, pageSize, index) => {
    return (current - 1) * pageSize + index;
  };

  const PageChange = (current, pageSize) => {
    setCurrent(current);
    setPageSize(pageSize);
  };

  const getTableData = () => {
    setLoading(true);
    post(allUrl.WeCom.pageUserStatistical, { current, pageSize, ...defaultQuery }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        setDataSource(Dt.list);
        setTotal(Dt.total);
      } else {
        // message.error(res.msg)
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    getTableData();
  }, [current, pageSize, defaultQuery]);

  const columns = [
    // { title: '序号', dataIndex: 'index',width:100, render: (text, record, index) => index + 1 },
    {
      title: "排名",
      dataIndex: "index",
      width: 100,
      render: (text, record, index) => serialNumber(current, pageSize, index + 1),
    },
    {
      title: "门店编码",
      dataIndex: "dealerCode",
      width: 200,
      render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
    },
    {
      title: "用户中心",
      dataIndex: "dealerName",
      width: 300,
      render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
    },
    {
      title: "交付专员",
      dataIndex: "delivererName",
      render: (text, record) => record.delivererName,
    },
    // { title: '车主姓名', dataIndex: 'ownerName' },
    // { title: '交付状态', dataIndex: 'deliverStatusName' },
    // { title: '交付完成日期', dataIndex: 'deliveredTime' },
    {
      title: "交付单信息",
      dataIndex: "orderInfo",
      width: 120,
      render: (text, record) => (
        <div>
          <a
            onClick={() => {
              setRecordObj(record);
              setVisible2(true);
            }}
          >
            查看
          </a>
        </div>
      ),
    },
    { title: "合计", dataIndex: "totalNum" },
  ];

  const InforData = {
    columns,
    dataSource,
    bordered: true,
    rowKey: "id",
    size: "small",
    loading,
    sticky: true,
    pagination: {
      pageSize: pageSize,
      onChange: PageChange,
      current: current,
      total: total,
      showTotal: () => `共${total}条，${pageSize}条/页`,
      showSizeChanger: true,
      showQuickJumper: true,
      onShowSizeChange: PageChange,
    },
  };
  const onSearch = (values) => {
    setDefaultQuery(values);
  };
  let searchList = [
    { label: "用户中心编码：", name: "dealerCode", type: "Input", placeholder: "请输入" },
    { label: "交付专员：", name: "delivererName", type: "Input", placeholder: "请输入" },
  ];

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={onCancel}
      maskClosable={false}
      footer={null}
      width={"90%"}
    >
      <Row>
        {roleJudgment(userInfo, "WECOM_CUSTOMER_NO_GROUP_EXPORT") ? (
          <Button
            type="primary"
            style={{ marginRight: "20px" }}
            loading={exportLoading}
            onClick={() => setExportVisible(true)}
          >
            未建群明细导出
          </Button>
        ) : null}
        <Query onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery} />
      </Row>

      <Table {...InforData} />

      {visible2 && (
        <DeliveryOrderList
          type={2}
          title={`${recordObj?.delivererName}：交付单信息`}
          recordObj={recordObj}
          data={recordObj.orderInfo || []}
          visible={visible2}
          onCancel={() => setVisible2(false)}
        />
      )}
      <Modal
        title="导出提示"
        visible={exportVisible}
        onCancel={() => setExportVisible(false)}
        onOk={() => ExportExcel()}
        confirmLoading={exportLoading}
      >
        <div>
          当前数据统计截至{moment(new Date()).subtract(1, "days").format("YYYY-MM-DD 23:59:59")}
          ，是否确认导出？
        </div>
      </Modal>
    </Modal>
  );
};
