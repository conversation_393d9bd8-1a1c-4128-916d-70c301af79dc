import React, { useState, useEffect } from 'react'
import { Row, Col, Tabs } from 'antd'
import List from './components/list'
import FunnelDiagram from './components/funnelDiagram'
import Pie<PERSON><PERSON> from './components/pieChart'
import allUrl from '@/utils/url'
import './index.less'
import { roleJudgment } from '@/utils/authority'
import { useSelector } from 'react-redux'
const { TabPane } = Tabs

const StoreProjectManage = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [tabsKey, setTabskey] = useState('')
    const tabsCallback = (key) => {
        setTabskey(key)
    }

    useEffect(() => {
        if (roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROJECT_MANAGER_LIST')) {
            setTabskey('1')
        } else if (roleJudgment(userInfo, 'PERM_STORE_PROJECT_NETWORK_DEPARTMENT_LIST')) {
            setTabskey('2')
        } else {
            setTabskey('1')
        }
    }, [])

    return <div className='storeProjectManage'>
        {
            roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROJECT_MANAGER_LIST') && !roleJudgment(userInfo, 'PERM_STORE_PROJECT_NETWORK_DEPARTMENT_LIST') ?
                <>
                    <Row className='storeProjectManage_graphic'>
                        <Col span={24} style={{display:'flex',backgroundColor:'white',justifyContent: 'center'}}>
                            <FunnelDiagram isProjectManager={true} Style={{borderRight:0}} />
                        </Col>
                    </Row>
                    <List
                        {...props}
                        url={allUrl.StoreManage.getProjectManagerStoreList}
                        pageType='projectManage'
                    />
                </> : null
        }
        {
            !roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROJECT_MANAGER_LIST') && roleJudgment(userInfo, 'PERM_STORE_PROJECT_NETWORK_DEPARTMENT_LIST') ?
                <>
                    <Row className='storeProjectManage_graphic'>
                        <Col span={14}>
                            <FunnelDiagram isProjectManager={false} wrapperStyle={{width:'100%'}}/>
                        </Col>
                        <Col span={10}>
                            <PieChart />
                        </Col>
                    </Row>
                    <List
                        {...props}
                        url={allUrl.StoreManage.getStoreProjectList}
                        pageType='networkDepartment'
                    />
                </> : null
        }

        {
            roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROJECT_MANAGER_LIST') && roleJudgment(userInfo, 'PERM_STORE_PROJECT_NETWORK_DEPARTMENT_LIST') ?
                <Tabs defaultActiveKey="1" onChange={tabsCallback}>
                    <TabPane tab='项目经理' key='1'>
                        {
                            tabsKey === '1' ?
                                <>
                                    <Row className='storeProjectManage_graphic'>
                                        <Col span={24}>
                                            <FunnelDiagram isProjectManager={true} Style={{borderRight:0}} />
                                        </Col>
                                    </Row>
                                    <List
                                        {...props}
                                        url={allUrl.StoreManage.getProjectManagerStoreList}
                                        pageType='projectManage'
                                    />
                                </>
                                : null
                        }
                    </TabPane>
                    <TabPane tab='网络部管理员' key='2'>
                        {
                            tabsKey === '2' ?
                                <>
                                    <Row className='storeProjectManage_graphic'>
                                        <Col span={14}>
                                            <FunnelDiagram isProjectManager={false} wrapperStyle={{width:'100%'}} />
                                        </Col>
                                        <Col span={10}>
                                            <PieChart />
                                        </Col>
                                    </Row>
                                    <List
                                        {...props}
                                        url={allUrl.StoreManage.getStoreProjectList}
                                        pageType='networkDepartment'
                                    />
                                </>
                                : null
                        }
                    </TabPane>
                </Tabs>
                : null
        }
    </div>
}

export default StoreProjectManage