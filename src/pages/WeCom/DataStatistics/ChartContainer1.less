.chartContainer-card1{
    .ant-tabs .ant-tabs-nav{
        margin-left: 0px;
    }
    .ant-row{
        align-items: unset;
    }
    .ant-select-selection-overflow{
        // flex-wrap: nowrap;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border: 1px dashed #d9d9d9;
    }
    .align-center {
        display: flex;
        align-items: center;
    }
    .chartContainer-card-header{
        margin-bottom: 24px;
        .title-row{
            font-size: 18px;
            color: rgba(0, 0, 0, 0.85);
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            display: flex;
            // justify-content: space-between;
            .title-con{
                display: flex;
                align-items: center;
                flex: 1;
                .title-con-text{
                    // margin-right: 20px;
                }
                .ant-tabs{
                    .ant-tabs-nav{
                        margin-bottom: 0;
                    }
                }
            }
            .title-warp{
                display: flex;
                align-items: center;
                flex: 1;
                .theme{
                    width: 12px;
                    height: 12px;
                    background: #C04EFF;
                    border-radius: 3px;
                    margin-right: 8px;
                }
                .title {
                    font-weight: 500;
                    text-align: center;
                    margin-right: 20px;

                }
                .subtitle{
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 0.45);
                    display: flex;
                    justify-content: space-between;
                    margin-right: 20px;

                }
                .subtitle-statisticalTime{
                    font-size: 13px;
                    color: rgba(0, 0, 0, 0.45);
                    margin-right: 8px;

                }
                .tab {
                   
                }
                .filters{
                }
            }
            .extended{
                font-size: 14px;
                
            }
        }
    }
    .chartContainer-card-content{
        .content-con{
            .content-left{
                .content-left-item{
                    // height: 130px;
                    // width: 90%;
                    // border: solid 1px #bbbbbb;
                    // display: flex;
                    // flex-direction: column;
                    // justify-content: space-around;
                    // align-items: center;
                    margin-bottom: 20px;
                   
                    .info {
                        z-index: 201;
                        height: 110px;
                        border-radius: 10px;
                        border: 0.5px solid rgba(193, 201, 209, 1);
                        overflow: hidden;
                    }
                    .info-head{
                        background-color: #F6F9FC;
                        height: 46px;
                        display: flex;
                        justify-content: space-between;
                        padding: 14px 18px;
                    }
                    .title1 {
                        color: rgba(0, 0, 0, 0.72);
                        font-size: 13px;
                    }
                    .title2 {
                        color: rgba(137, 148, 160, 1);
                        font-size: 13px;
                    }
                    .info-body {
                        padding: 16px 18px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }
                    .value {
                        font-size: 24px;
                        color: rgba(4, 180, 85, 1);
                    }
                    .unit {
                        color: rgba(0, 0, 0, 0.48);
                        font-size: 18px;
                    }
                    .pre {
                        color: rgba(4, 180, 85, 1);
                        font-size: 14px;
                    }
                    .green {
                        color: rgba(4, 180, 85, 1);
                    }
                    .red {
                        color: rgba(235, 89, 25, 1);
                    }
                    
                }
            }
        }
    }
    .content-right {
        height: 100%;
        width: 100%;
    }
 
}