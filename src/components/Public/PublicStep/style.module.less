.steps {
  height: 70px;
  background: #EEF8FA;
  border-radius: 2px;
  border: 1px solid #61CBE0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  :global {
    .content {
      width: 784px;
      display: flex;
      justify-content: space-between;
      position: relative;

      &::after {
        width: 100%;
        height: 1px;
        background: #AFBECC;
        content: '';
        position: absolute;
        z-index: 0;
        margin: auto;
        top: 0;
        bottom: 0;
      }
    }

    .step {
      display: flex;
      align-items: center;
      z-index: 1;
      background: #EEF8FA;
      position: relative;
      padding: 0 16px;

      &.done {
        span {
          border: 1px solid #7872F9;
          background: #7872F9;
          color: #fff;
        }

      }

      &:nth-child(2) {
        &.done {
          span {
            border-color: #E8A507;
            background: #E8A507;
          }
        }
      }

      &:nth-child(3) {
        &.done {
          span {
            border-color: #09AFE4;
            background: #09AFE4;
          }
        }
      }

      span {
        width: 32px;
        height: 32px;
        border: 2px solid rgba(0, 0, 0, 0.15);
        border-radius: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.15);
        font-size: 16px;
        font-weight: bold;
      }

      b {
        color: rgba(0, 0, 0, 0.65);
        font-size: 18px;
        padding-left: 8px;
      }
    }
  }

}