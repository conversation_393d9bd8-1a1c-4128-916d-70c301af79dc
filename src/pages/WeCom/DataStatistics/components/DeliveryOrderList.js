import React ,{useEffect, useRef, useState} from 'react'
import { Modal,Table, message, Select } from 'antd'
import moment from 'moment'
import { post,get } from '@/utils/request'
import allUrl from '@/utils/url'
import PublicTable from '@/components/Public/PublicTable'
import Query from './Query'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
const {Option} = Select

export default (props) =>{
    const {visible,title,onCancel,type,recordObj} = props
    const url = allUrl.WeCom.pageDeliverOrder
    const [defaultQuery, setDefaultQuery] = useState({
        dealerCode: recordObj.dealerCode,
        deliverOrderNo: '',
        // delivererName: recordObj.dealerName,
        pageIndex: 1,
        pageSize: 10,
    })
    const [query, setQuery] = useState(recordObj)
    const [dictData, setDictData] = useState([])
    const tableRef = useRef()
    const [loading,setLoading] = useState(false)
    const [visible1,setVisible1] = useState(false)
    const [dataSource,setDataSource] = useState([])

    const columns = type==1 ?[
        { title: '序号', dataIndex: 'index',width:100, render: (text, record, index) => index + 1 },
        { title: '交付专员', dataIndex: 'delivererName' },
        { title: '车主姓名', dataIndex: 'ownerName' },
        { title: '交付状态', dataIndex: 'deliverStatusName' },
        { title: '交付完成日期', dataIndex: 'deliveredTime',render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss') :'' },
        
    ]:[
        { title: '序号', dataIndex: 'index',width:100, render: (text, record, index) => index + 1 },
        { title: '交付单号', dataIndex: 'deliverOrderNo' },
        { title: '车主姓名', dataIndex: 'ownerName' },
        { title: '交付状态', dataIndex: 'deliverStatusName' },
        { title: '交付完成日期', dataIndex: 'deliveredTime',render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss') :'' },
        { title: '操作日志', dataIndex: 'deliverStatusName',fixed:'right', width: 100,render:(text,record)=><a onClick={()=>lookLog(record)}>查看</a> },
        { title: '是否需要建群', dataIndex: 'statisticsStatus', fixed:'right', editable: true, render:(text, record) => 
        <Select defaultValue={text} placeholder='请选择' onChange={v => changeSalesOrderStatus(v, record)}>
            <Option value={1}>是</Option>
            <Option value={2}>否</Option>
        </Select>
        },
        {title: '备注原因', dataIndex: 'statisticsStatusComment', fixed:'right', editable: true, render:(text, record) => 
        <Select  style={{width: 100}} defaultValue={text} placeholder='请选择' onChange={(v) => changeSalesOrderStatusComment(v, record)}>
            {
                dictData.map((item,index)=><Option key={index} value={String(item.value)}>{item.name}</Option>)
            }
        </Select>
        },
       
    ]
    const columns2 = [
        { title: '时间', dataIndex: 'createTime', width: 120,render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss'):'' },
        { title: '修改人账号ID', dataIndex: 'userId'},
        { title: '修改人姓名', dataIndex: 'userName', width: 100 ,render: ((text, record) => record.log.userName)},
        { title: '修改人岗位', dataIndex: 'positionName',render: ((text, record) => record.log.positionName)},
        { title: '修改人所属组织', dataIndex: 'orgName', width: 160 ,render: ((text, record) => record.log.orgName)},
        { title: '修改人所属用户中心', dataIndex: 'dealerFullName', width: 160, render: ((text, record) => record.log.dealerFullName)},
        { title: '修改前内容', dataIndex: 'oldName', width: 140,render:((text, record) => record.log.oldName)},
        { title: '修改后内容', dataIndex: 'newName',width: 140,render: ((text, record) => record.log.newName)},
    ]

    const lookLog = (record) =>{
        post(allUrl.WeCom.queryNotIncludeLog,{objectKey:record.id}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setDataSource(Dt)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        }).catch(()=>{
            setLoading(false)
        })
        // setRecordObj(record)
        setVisible1(true)
    }

    const onSearch = (values) => {
        const obj = values && values.deliverOrderNo ?  Object.assign(recordObj, values) : defaultQuery
        post(url, obj).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt.list.map((item, index) => item.key = index + 1)
                tableRef.current.setDataSource(Dt.list)
                tableRef.current.setTotal(Dt.total)
            } else {
                // message.error(res.msg)
            }
        })
    }
    useEffect(()=> {
        get(allUrl.common.entryLists, {codes:'statistics_status_comment'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt['statistics_status_comment'].forEach(item=>{
                    item.name = item.entryMeaning
                    item.value = item.entryValue
                })
                setDictData(Dt['statistics_status_comment'])
            } 
        })
    }, [])
    const changeSalesOrderStatus = (v, record, key) => {
        post(allUrl.WeCom.updateSalesOrder, {id: record.id, statisticsStatus: v,statisticsStatusComment: record.statisticsStatusComment}).then(res => {
            if (res.success) {
                message.success('操作成功')
                tableRef.current.getTableData()
            } 
        })
    }

    const changeSalesOrderStatusComment = (v, record, key) => {
        post(allUrl.WeCom.updateSalesOrder, {id: record.id, statisticsStatus: record.statisticsStatus, statisticsStatusComment: v}).then(res => {
            if (res.success) {
                message.success('操作成功')
                tableRef.current.getTableData()
            } 
        })
    }
    let searchList = [
        { label: '交付单号：', name: 'deliverOrderNo', type: 'Input', placeholder: '请输入', colSpan: 16 },
    ]
    return <Modal  title={title} visible={visible} onCancel={onCancel} maskClosable={false} footer={null} width={'90%'}>
            {
                type == 2 ?
                <Query onSearch={onSearch} searchList={searchList} defaultQuery={recordObj} /> : null 
            }
            <Modal title='操作日志' width={'90%'} visible={visible1} onCancel={()=>setVisible1(false)} footer={null} maskClosable={false}>
                <Table rowKey='id' bordered loading={loading} columns={columns2} dataSource={dataSource} scroll={{y:'max-content'}} pagination={false} />
            </Modal>
            <PublicTable type={5} sticky={true} ref={tableRef} rowSelection={false} columns={columns} defaultQuery={recordObj} url={url} />
    </Modal>
}