import React, { useState, memo, useMemo, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Modal, Form, Row, Col, Select, Input, message } from 'antd'
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
const { Option } = Select;
const EditConfigInfo = (props) => {
    const { handleCancel, visible, addDisabled, reloadList, powerModelList } = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const dataSourceForm = useMemo(() => props.codeInfoSource, [props.codeInfoSource])
    const formRef = useRef(null)
    const [DictCarData, setDictCarData] = useState({}) // 车型名称接口
    const handleOk = () => {

        form.validateFields().then(values => {
            if (!values.carType) {
                return message.warn('车型名称不能为空')
            }
            if (!values.baseMaterialNo) {
                return message.warn('车型配置代码不能为空')
            }
            if (!values.config) {
                return message.warn('车型配置名称不能为空')
            }
            if (!values.carMarketName) {
                return message.warn('车型市场名称不能为空')
            }
            if (!values.carOfYear) {
                return message.warn('车型年款不能为空')
            }
            if (!values.powerModelObj) {
                return message.warn('动力方式不能为空')
            }
            if (!values.brand) {
                return message.warn('品牌不能为空')
            }
            const params = {
                id: dataSourceForm.id,
                carType: formRef.current.getFieldValue(['carType']),
                baseMaterialNo: formRef.current.getFieldValue(['baseMaterialNo']),
                config: formRef.current.getFieldValue(['config']),
                carMarketName: formRef.current.getFieldValue(['carMarketName']),
                powerModelObj: formRef.current.getFieldValue(['powerModelObj']),
                brand: formRef.current.getFieldValue(['brand']),
                manufacturer: formRef.current.getFieldValue(['manufacturer']),
                carOfYear: formRef.current.getFieldValue(['carOfYear']),

                powerModel: null,
                powerModelCode: null,
            }
            params.powerModel = params.powerModelObj.label
            params.powerModelCode = params.powerModelObj.value
            console.log('edit', params)
            post(allUrl.CarConfigMaintain.updateCarConfig, { ...params }).then(res => {
                if (res.success) {
                    handleCancel()
                    reloadList()
                    return message.success('更新成功！')

                } else {
                    // message.error('更新失败！')
                }
                setLoading(false)
            })

        })
    }
    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 15 },
    };
    const onBusinessCode = (value) => {
        console.log(value);
    }
    useEffect(() => {
        console.log(dataSourceForm, '888888');
        if (dataSourceForm) {
            dataSourceForm.powerModelObj = {
                label: dataSourceForm.powerModel,
                value: dataSourceForm.powerModelCode,
            }
            formRef.current && form.setFieldsValue({
                ...dataSourceForm,

            })
        }
    }, [dataSourceForm, form])
    // 车型名称字段接口
    useEffect(() => {
        get(allUrl.common.entryLists, { codes: 'equity_car_type' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setDictCarData(Dt || {})
            } else {
                // message.error(res.message)
            }
        })
    }, [])
    return (
        <Modal visible={visible} onOk={handleOk} okText={'保存'} onCancel={handleCancel} maskClosable={false} width='650px' title='车型配置信息'>
            <Form name='change-codeInfo' ref={formRef} form={form} {...layout} >
                <Row style={{ color: '#000000' }}>
                    <Col span={24}>
                        {/* <Form.Item label='车型名称' name='carType'>
                        <Input allowClear placeholder='请输入车型名称' />
                        </Form.Item> */}
                        <Form.Item name='carType' label="车型名称" rules={[{ required: true, message: '请选择车型名称!' }]}
                            style={{ marginBottom: 0 }}
                            help={<div style={{ color: '#F5222D', fontSize: '12px', marginTop: '5px', marginBottom: '15px' }}>注意：车型名称与DMS一致</div>}>
                            <Select placeholder='请选择车型名称' onChange={onBusinessCode} allowClear>
                                {
                                    DictCarData['equity_car_type'] && DictCarData['equity_car_type'].length ?
                                        DictCarData['equity_car_type'].map((item, index) => <Option key={index} value={Number(item.value)}>{item.name}</Option>)
                                        : null
                                }
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='车型配置代码' name='baseMaterialNo' rules={[{ required: true, message: '请输入车型配置代码!' }]}>
                            <Input allowClear placeholder='13位车型配置代码' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='车型配置名称' name='config' rules={[{ required: true, message: '请输入车型配置名称!' }]}>
                            <Input allowClear placeholder='示例：两驱标准版' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='车型市场名称' name='carMarketName' rules={[{ required: true, message: '请输入车型市场名称!' }]}>
                            <Input allowClear placeholder='示例：问界M5' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='车型年款' name='carOfYear' rules={[{ required: true, message: '请输入车型年款!' }]}>
                            <Input allowClear placeholder='示例：2023' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='动力方式' name='powerModelObj' rules={[{ required: true, message: '请输入动力方式!' }]}>
                            <Select placeholder='示例：增程式电动车' allowClear getPopupContainer={(triggerNode) => triggerNode.parentNode} labelInValue>
                                {powerModelList && powerModelList.length ?
                                    powerModelList.map((item, index) => <Option key={index} value={item.selectKey} label={item.selectValue}>{item.selectValue}</Option>)
                                    : null
                                }
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='品牌' name='brand' rules={[{ required: true, message: '请输入品牌!' }]}>
                            <Input allowClear placeholder='示例：问界' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='汽车厂商' name='manufacturer'>
                            <Input allowClear placeholder='示例：重庆金康汽车科技有限公司' />
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(EditConfigInfo)