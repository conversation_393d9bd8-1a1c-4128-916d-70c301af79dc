import React, { lazy, Suspense } from 'react';
import { <PERSON>h<PERSON>outer, Switch, Route, Redirect } from 'react-router-dom';
import { CacheRoute, CacheSwitch } from 'react-router-cache-route';
import { Spin } from 'antd';
import MainLayout from '@/components/Layout';
import Login from '@/pages/login';
import Transfer from '@/pages/Transfer';
import AuthorizedRoute from './AuthorizedRoute';
import { routeMap } from './routeMap';

// let versionScript = document.createElement('script');
// versionScript.src = window.location.origin + '/version.js?v=' + new Date().getTime();
// let s = document.getElementsByTagName('script')[0];
// s.parentNode.insertBefore(versionScript, s)

const RouterConfig = () => {
  const renderRoute = (data) => {
    return data.map((item, index) => {
      if (item.children && item.children.length) {
        return (
          <AuthorizedRoute item={item} exact key={index + 1 * 1000}>
            {renderRoute(item.children)}
          </AuthorizedRoute>
        );
      } else {
        return (
          <AuthorizedRoute
            item={item}
            exact
            key={index + 1}
            path={item.path}
            component={item.component ? WaitingCompontent(lazy(() => import(`@/${item.component}`))) : null}
          />
        );
      }
    });
  };
  const WaitingCompontent = (WarpComponent) => {
    return (props) => {
      return (
        <Suspense
          fallback={
            <div style={{ marginLeft: '24px' }}>
              <Spin spinning={true} />
            </div>
          }
        >
          <WarpComponent {...props} />
        </Suspense>
      );
    };
  };
  return (
    <HashRouter>
      <CacheSwitch>
        <Route path={'/'} exact component={Login} />
        <Route path={'/login'} exact component={Login} />
        <Route path={'/Transfer'} exact component={Transfer} />
        <MainLayout>{renderRoute(routeMap)}</MainLayout>
        <Redirect exact to="/" />
      </CacheSwitch>
    </HashRouter>
  );
};
export default RouterConfig;
