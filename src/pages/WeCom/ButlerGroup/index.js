import './index.less'
import React, { useEffect, useState, useRef } from 'react'
import { useSelector,connect } from 'react-redux'
import { Row, Col, Button, Modal, Descriptions, Popover,Tooltip,} from 'antd'
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import PublicTable from '@/components/Public/PublicTable'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
import PublicTooltip from '@/components/Public/PublicTooltip'
import _ from 'lodash'
import moment from 'moment';
import { roleJudgment } from '@/utils/authority'
import ExtendedColumn from '../../../components/Public/ExtendedColumn'
import TimeoutReminder from './components/TimeoutReminder'
import { QuestionCircleOutlined } from '@ant-design/icons'
import SpectialTimeoutReminder from './components/SpecialTimeoutReminder'
import {fileDown} from '@/utils'

const ButlerGroupList = (props) => {
    const tableRef = useRef()
    
    const { userInfo} = useSelector(state => state.common)

    const [tableHeight, setTableHeight] = useState(0)
    const [timeoutReminderVisible,setTimeoutReminderVisible] = useState(false)
    const [specialTimeoutReminderVisible,setSpecialTimeoutReminderVisible] = useState(false)
    const [defaultQuery, setDefaultQuery] = useState({
    
    })
    const yeasterDay =  moment().subtract(1, 'days').format('YYYY-MM-DD');
    const [visible, setVisible] = useState(false)
    const [rowGroupStatusName,setRowGroupStatusName] = useState('')
    const [qualifiedList, setQualifiedList] = useState([])
    const [columns, setColums] = useState([
        { title: '群名', dataIndex: 'name', width: 200, fixed: 'left',render:text=><PublicTooltip title={text}>{text}</PublicTooltip>  },
        { title: '群人数', dataIndex: 'memberCount', width: 80, fixed: 'left' },
        { title: '群主', dataIndex: 'ownerName', width: 140 },
        { title: '群主岗位', dataIndex: 'positionName', width: 140 },
        { title: '群主所在组织', dataIndex: 'organization', width: 180,render:text=><PublicTooltip title={text}>{text}</PublicTooltip>  },
        { title: '群主所属用户中心', dataIndex: 'storeFullNames',isExtend: true, width: 180,render:(text)=>text && text.length?<PublicTooltip title={text.join(',')}>{text.join(',')}</PublicTooltip>:'' },
        { title: '群公告', dataIndex: 'notice', width: 180,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '创建时间', dataIndex: 'groupCreateTime', width: 180 },
        { title: '是否有认定客户', dataIndex: 'hasAffirmCustomer',isExtend: true, width: 140 },
        { title: 
            <div>
                群状态&nbsp;
                <Tooltip title={`统计截止：${yeasterDay} 23:59:59`}key='1'>
                <QuestionCircleOutlined />
                </Tooltip>
            </div>, 
            isExtend: true,
            dataIndex: 'deliverStatusName',width: 100},
        { title:
            <div>
                建群是否合格&nbsp;
                <Tooltip title={`统计截止：${yeasterDay} 23:59:59`}key='2'>
                <QuestionCircleOutlined />
                </Tooltip>

            </div>,isExtend: true, dataIndex: 'qualifiedStatusName', width: 140},
        { title: '操作', width: 120, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
    ])
    const onSearch = (values) => {
        if (values.groupCreateTime && values.groupCreateTime.length) {
            values.groupCreateTimeStart = moment(values.groupCreateTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.groupCreateTimeEnd = moment(values.groupCreateTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.groupCreateTime
        }
        setDefaultQuery(values)
    }
    const RowDetail = (record) => {
        let data = {
            groupId: record.groupId,
            rowData:record,
            title: '管家群成员'
        }
        console.log(record)
        UniversalOpenWindow({
            JumpUrl: '/wecom/groupMembers', data, history: props.history
        })
    }
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff',textAlign:'center' }}>
            {roleJudgment(userInfo, "WECOM_BUTLER_GROUP_GROUP_MEMBERS_VIEW")
          ? [
              <p style={{ cursor: "pointer" }} onClick={() => RowDetail(record)}key={1}>
                查看群成员
              </p>,
            ]
          : null}
        {roleJudgment(userInfo, "WECOM_BUTLER_GROUP_QUALIFIED_DETAIL")
          ? [
              record.qualifiedDetailList && record.qualifiedDetailList.length ? (
                <p style={{ cursor: "pointer" }} onClick={() => qualifiedDetail(record)}key={2}>
                  不合格详情
                </p>
              ) : null,
            ]
          : null}
        </div>
    }
    const qualifiedDetail = (record) => {
        console.log(record.qualifiedDetailList)
        setRowGroupStatusName(record.deliverStatusName)
        setQualifiedList(record.qualifiedDetailList)
        setVisible(true)
    }
 
    const initPage = () => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let FormQueryH = document.getElementsByClassName('PublicList_FormQuery')[0].clientHeight
        let h = winH - FormQueryH - 47 - 54 - 305
        setTableHeight(h)
    }
    const qualifiedTitleStr = () => {
        const str = `不合格详情(${rowGroupStatusName})`
        return  <Tooltip title={'悬浮查看不合格详情'}>
                {str}&nbsp;<QuestionCircleOutlined />
                </Tooltip>
    }

    const exportPersonnel = () => {
      post(allUrl.WeCom.exportArchiveStaff,{}, {responseType: "blob"}).then(res=>{
        fileDown(res,res.fileName)
      })
    }
    
    useEffect(() => {
        initPage()
    }, [])

  let searchList = [
    { label: "群名", name: "name", type: "Input", colSpan: 6 },
    { label: "群主", name: "ownerName", type: "Input", colSpan: 6 },
    { label: "群成员", name: "memberName", type: "Input", colSpan: 6 },
    { label: "创建时间", name: "groupCreateTime", type: "RangePicker", colSpan: 6, showTime: true },
  ];

    let newColumns = _.cloneDeep({ columns }).columns
    for (let i = 0; i < newColumns.length; i++) {
        if (newColumns[i].isExtend && newColumns[i].checked == false) { // 有拓展属性并且未被选中的拓展项移出列表展示区
            newColumns.splice(i, 1)
            i--
        }
    }
  
  return (
    <div className="ButlerGroupList">
      <PublicTableQuery
        isCatch={true}
        isFormDown={false}
        onSearch={onSearch}
        searchList={searchList}
      />
      <div className="tableData">
        <Row className="tableTitle">
          <Col className="text">管家群列表</Col>
          <Col className="bts">
          {roleJudgment(userInfo, "WECOM_BUTLER_GROUP_EXPORT_PERSONNEL") ? (
              <Button type="primary" onClick={() => exportPersonnel()}>
                存档人员导出
              </Button>
            ) : null}
          {roleJudgment(userInfo, "WECOM_BUTLER_GROUP_SPECIAL_TIMEOUT_SETTING") ? (
              <Button type="primary" onClick={() => setSpecialTimeoutReminderVisible(true)}>
                特殊超时提醒设置
              </Button>
            ) : null}
            {roleJudgment(userInfo, "WECOM_BUTLER_GROUP_TIMEOUT_REMINDER_SETTINGS") ? (
              <Button type="primary" onClick={() => setTimeoutReminderVisible(true)}>
                超时提醒设置
              </Button>
            ) : null}
            <ExtendedColumn setColums={setColums} columns={columns} />
          </Col>
        </Row>
        <PublicTable
          isCatch={true}
          scroll={{ x: "max-content", y: tableHeight }}
          sticky={true}
          type={5}
          ref={tableRef}
          rowSelection={false}
          columns={newColumns}
          defaultQuery={defaultQuery}
          url={allUrl.WeCom.qwGroupList}
        />
      </div>
      {timeoutReminderVisible && (
        <TimeoutReminder
          visible={timeoutReminderVisible}
          onCancel={() => setTimeoutReminderVisible(false)}
        />
      )}

       {specialTimeoutReminderVisible && (
        <SpectialTimeoutReminder
          visible={specialTimeoutReminderVisible}
          onCancel={() => setSpecialTimeoutReminderVisible(false)}
        />
      )}
      <Modal
        width={800}
        visible={visible}
        title={qualifiedTitleStr()}
        onOk={() => setVisible(false)}
        onCancel={() => setVisible(false)}
        maskClosable={false}
        footer={false}
      >
        <Descriptions
          bordered
          layout="vertical"
          size="small"
          labelStyle={{ fontWeight: "bold" }}
          column={{ xxl: 6, xl: 4, lg: 4, md: 4, sm: 4, xs: 4 }}
        >
          {qualifiedList.length
            ? qualifiedList.map((item, index) => {
                return (
                  <Descriptions.Item key={index} label={item.name} style={{ cursor: "pointer" }}>
                    <Popover content={item.description}>
                      {item.qualified ? "合格" : "不合格"}
                    </Popover>
                  </Descriptions.Item>
                );
              })
            : null}
        </Descriptions>
      </Modal>
    </div>
  );
};
export default ButlerGroupList;
