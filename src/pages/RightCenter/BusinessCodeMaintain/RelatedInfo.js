import React, { useState,memo,useMemo,useEffect,useRef} from 'react'
import { useSelector } from 'react-redux'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Modal,Form,Row,Col,Select,Input,message,Space, Button} from 'antd'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
const RelatedInfo = (props) => {
    const {handleCancel,visible,addDisabled,onSearch,reloadList} = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const dataSourceForm = useMemo(() => props.codeInfoSource, [props.codeInfoSource])
    const formRef = useRef(null)
    const [DataDetailNameObj,setDataDetailNameObj]= useState({})
    const handleOk = (values) =>{
        // 暂时不需要 编辑时 表单验证  
    //     form.validateFields().then(values=>{
    //         if (!values.businessCode) {
    //             return message.warn('业务编码编号不能为空')
    //         }
    //         if (!values.businessCodeName) {
    //             return message.warn('业务编码名称不能为空')
    //         }
    //         if (!values.detailNo) {
    //             return message.warn('权益明细编码不能为空')
    //         }
    //         if (!values.detailName) {
    //             return message.warn('权益明细名称不能为空')
    //         }
    // const params={ 
    //                 businessCode:formRef.current.getFieldValue(['businessCode']),
    //                 businessCodeName:formRef.current.getFieldValue(['businessCodeName']),
    //                 detailNo:formRef.current.getFieldValue(['detailNo']),
    //                 detailName:formRef.current.getFieldValue(['detailName'])
    //             }
    //             console.log('edit',params)
    //             post(allUrl.BusinessCodeMaintain.updateBusinessCode, { ...params}).then(res => {
    //                 if (res.success) {
    //                     handleCancel()
    //                     window.location.reload(); 
    //                     // onSearch();
    //                     return  message.success('更新成功！')
                      

    //                 } else {
    //                     // message.error('更新失败！')
    //                 }
    //                 setLoading(false)
    //             })
    //     })


        handleCancel()
                const params={
                                    id:dataSourceForm.id,
                                detailId:dataSourceForm.detailId,
                            businessCode:formRef.current.getFieldValue(['businessCode']),
                            businessCodeName:formRef.current.getFieldValue(['businessCodeName']),
                            detailNo:formRef.current.getFieldValue(['detailNo']),
                            detailName:formRef.current.getFieldValue(['detailName'])
                        }
                        console.log('edit',params)
                    post(allUrl.BusinessCodeMaintain.updateBusinessCode, { ...params}).then(res => {
                    if (res.success) {
                        message.success('更新成功！')
                        reloadList()

                    } else {
                        // message.error('更新失败！')
                    }
                    setLoading(false)
                })
    }
    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 15 },
    };
    useEffect(() => {
        console.log(dataSourceForm,'888888');
        if (dataSourceForm) {
            formRef.current && form.setFieldsValue({
              ...dataSourceForm,
             
            })
          }
	}, [dataSourceForm,form])
    // const onBlurDetailNo = () =>{
    //     console.log('###########',formRef.current.getFieldValue(['detailNo']))
    //     const params={
    //         detailNo:formRef.current.getFieldValue(['detailNo']), 
    //     }

    //     post(allUrl.EquityPackageManage.queryDetailRelation,{...params}).then(res=>{
    //         if(res.success){
    //             let Dt = res.resp[0]
    //             setDataDetailNameList(Dt);
    //             console.log(DataDetailNameList.name,'00000',Dt)
    //             form.setFieldsValue({
    //                 detailName: Dt.name,
    //               });
              
    //         }else{
    //             // message.error(res.msg)
    //         }
    //         setLoading(false)
    //     })
    // }
    const onBlurDetailNo = () =>{
        console.log('###########',formRef.current.getFieldValue(['detailNo']))
        
        const params={
            detailNo:formRef.current.getFieldValue(['detailNo']), 
        }

        if(params.detailNo){
            post(allUrl.EquityPackageManage.queryDetailRelation,{...params}).then(res=>{
                if(res.success){
                    if(res.resp && res.resp.length>0 ){
                        let Dt = res.resp[0]
                    
                        setDataDetailNameObj(Dt);
                        // console.log(DataDetailNameObj.name,'00000',Dt)
                        form.setFieldsValue({
                            detailName: Dt.name,
                          });
                    }  else{
                        form.setFieldsValue({
                            detailName: ''
                          });
                    }            
                }
                setLoading(false)
            })
        } 
       
    }
    const onFinish = (values) => {
        console.log('Received values of form:', values);
    };
    return (
        <Modal visible={visible} onOk={handleOk} okText={'保存'} onCancel={handleCancel} maskClosable={false} width='650px' title='业务编码关联'>
            <Form name='change-codeInfo' ref={formRef} form={form} {...layout} onFinish={onFinish}>
                <Row>
                    <Col span={23}>
                        <Form.Item label='业务编码编号' name='businessCode' >
                            <Input allowClear placeholder='请输入业务编码编号' />
                        </Form.Item>
                    </Col>
                    <Col span={23}>
                        <Form.Item label='业务编码名称' name='businessCodeName' >
                            <Input allowClear placeholder='请输入业务编码名称' />
                        </Form.Item>
                    </Col>
                    <Col span={23}>
                        <Form.Item label='权益明细编码' name='detailNo' >
                            <Input allowClear onBlur={onBlurDetailNo}  placeholder='请输入权益明细编码' />
                        </Form.Item>
                    </Col>
                    <Col span={23}>
                        <Form.Item label='权益明细名称' name='detailName' >
                            <Input allowClear disabled placeholder='请输入权益明细名称' />
                        </Form.Item>
                    </Col>
                </Row>
                <Form.List name="users">
                    {(fields, { add, remove }) => (
                    <>
                        <Button style={{position: 'absolute', left: '535px', top: '190px'}} type="dashed" onClick={() => add()} icon={<PlusOutlined />}></Button>
                        {fields.map(({ key, name, ...restField }) => (
                        <Row>
                            <Col span={23}>
                                <Form.Item {...restField} name={[name, 'detailNo']} label='权益明细编码'>
                                    <Input allowClear onBlur={onBlurDetailNo}  placeholder='请输入权益明细编码' />
                                </Form.Item>
                            </Col>
                            <Col span={1} style={{ marginLeft: '-67px'}}>
                                <Button type="dashed" onClick={() => remove(name)} icon={<MinusCircleOutlined />}></Button>
                            </Col>
                            <Col span={23}>
                                <Form.Item {...restField} name={[name, 'detailName']} label='权益明细名称'>
                                    <Input allowClear disabled placeholder='请输入权益明细名称' />
                                </Form.Item>
                            </Col>
                        </Row>
                        ))}
                    </>
                    )}
                </Form.List>
                <Form.Item>
                    <Button type="primary" htmlType="submit">
                        Submit
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    )
}
export default memo(RelatedInfo)