/**
 * 车型配置需求：
 * 1.需求描述详见V5.0.4版本：http://wiki.seres.cn/pages/viewpage.action?pageId=29755229&preview=/29755229/29755786/image2023-10-17_17-45-29.png
 * 2.选中年款的全部按钮，所有车型配置按钮全部选中；
 * 3.选中年款checkbox，该年下的动力方式和配置都勾选上；
 * 4.选中动力方式checkbox，该动力方式的配置都勾选上；
 * 5.取消某动力方式下的所有配置，则该动力方式下的所有配置checkbox设为disabled，该动力方式checkbox取消选择；
 */
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Modal, Checkbox, Row, Col, Divider } from 'antd';
import { getWidth, offsetLeft } from '@/utils/index';
import './carConfigModal.less';

const CarConfigModal = forwardRef((props, ref) => {
  const { carConfigData, carConfigInfo, carType, onDataChange } = props;

  const [indeterminateYear, setIndeterminateYear] = useState(false); //车型年款全选
  const [checkAllYear, setCheckAllYear] = useState(false); //车型年款
  const [checkedListYear, setCheckedListYear] = useState([]); //年款选中的值
  const [yearOptions, setYearOptions] = useState([]); //年款所有的选项值

  const [indeterminatePower, setIndeterminatePower] = useState(false); //动力方式
  const [checkAllPower, setCheckAllPower] = useState(false); //动力方式
  const [checkedListPower, setCheckedListPower] = useState([]); //动力方式选中的值
  const [powerOptions, setPowerOptions] = useState([]); //动力方式所有的选项值
  const [powerAllDisabled, setPowerAllDisabled] = useState(true); //动力方式全选disabled
  const [everyPowerOptions, setEveryPowerOptions] = useState({}); //分年款下的动力方式所有选项值，如{'2023':[年款下所有动力下],'2022':[年款下所有动力下]}

  const [indeterminateConfig, setIndeterminateConfig] = useState(false); //具体配置
  const [checkAllConfig, setCheckAllConfig] = useState(false); //具体配置
  const [checkedListConfig, setCheckedListConfig] = useState([]); //具体配置选中的值
  const [configOptions, setConfigOptions] = useState([]); //具体配置所有的选项值
  const [everyConfigOptions, setEveryConfigOptions] = useState({}); //分动力方式下的具体配置所有选项值，如{'2023/evr':[动力下所有配置值],'2022/ev':[动力下所有配置值]}
  const [configAllDisabled, setConfigAllDisabled] = useState(true); //具体配置全选disabled

  const [powerTypeOptions, setPowerTypeOptions] = useState([]); // 动力方式枚举
  // const [çonfigTypeOptions, setConfigTypeOptions] = useState([])  // 具体配置枚举

  const [isEdit, setIsEdit] = useState(false); //是否编辑

  useImperativeHandle(ref, () => ({
    checkedListConfig,
    handleChangeCheckAllConfig,
  }));

  //控制所有checkbox 的disabled属性
  // const addDisabled = (data, disabled) => {
  //   data.forEach(i => {
  //     i.disabled = disabled;
  //     !!i.children && addDisabled(i.children, disabled);
  //   });
  // };

  //控制选中的checkbox的disabled属性
  // const addcheckedDisabled = (data, disabled) => {
  //   data.forEach(j => {
  //     j.disabled = true;
  //     if (!disabled && checkedListPower.includes(j.parent)) {
  //       j.disabled = disabled;
  //     }
  //   });
  // };
  //操作全部年款
  const handleChangeCheckAllYear = e => {
    setIsEdit(false);
    const { checked } = e.target;
    setCheckAllYear(checked);
    setCheckAllPower(checked);
    setCheckAllConfig(checked);
    setIndeterminateYear(false);
    setIndeterminatePower(false);
    setIndeterminateConfig(false);
    if (checked) {
      setCheckedListYear(yearOptions);
      setPowerAllDisabled(false);
      setConfigAllDisabled(false);
      // addDisabled(carConfigData, false);
    } else {
      setCheckedListYear([]);
      setConfigAllDisabled(true);
      setPowerAllDisabled(true);
      // addDisabled(carConfigData, true);
    }
  };
  //操作单个年款
  const handleChangeCheckYear = list => {
    setIsEdit(false);
    setCheckedListYear(list);
  };

  //动力方式全选
  const handleChangeCheckAllPower = e => {
    setIsEdit(false);
    const { checked } = e.target;
    setCheckAllPower(checked);
    setCheckAllConfig(checked);
    if (checked) {
      setCheckedListPower(powerOptions);
    } else {
      setCheckedListPower([]);
    }
  };
  //操作单个动力方式
  const handleChangeCheckPower = list => {
    setIsEdit(false);
    setCheckedListPower(list);
  };

  //具体配置全选
  const handleChangeCheckAllConfig = e => {
    setIsEdit(false);
    const { checked } = e.target;
    setCheckAllConfig(checked);
    if (checked) {
      setCheckedListConfig(configOptions);
    } else {
      setCheckedListConfig([]);
    }
  };

  //操作单个具体配置
  const handleChangeCheckConfig = list => {
    setIsEdit(false);
    setCheckedListConfig(list);
  };

  //处理枚举数据
  const getOptions = data => {
    const arr = [];
    data.forEach(element => {
      arr.push(element.children);
    });
    return arr;
  };

  //获取多选框组所有options值
  const getOptionsArr = data => {
    return data.map(item => item.value);
  };

  //具体配置是否有border
  const isBorder = (powerTypeOptions, index) => {
    const currentParent = powerTypeOptions[index].parent;
    const prevParent = powerTypeOptions[index - 1]?.parent;
    const tmpl = carConfigData.filter(item => item.value === currentParent);
    if (currentParent !== prevParent && tmpl[0].children?.length > 1) {
      return true;
    } else {
      return false;
    }
  };

  //观察选项下的子集是否有选中的值
  /**
   *
   * @param {*} data 操作的check下级已选中的值
   * @param {*} option 操作的checkBox选中的值
   * @param {*} initOptions 默认所有选项的值
   * @returns {flag->之前是否有选中的选项， options:之前选中的选项值}
   */
  const isPartChecked = (data, option, initOptions) => {
    //筛选出之前选中的值
    const tmpl = data.filter(item => {
      const lastIndex = item.lastIndexOf('/');
      const s = item.substring(0, lastIndex); //获取具体配置中的 “年/动力”
      if (s === option) return item;
    });
    if (!!tmpl.length && tmpl.length < initOptions[option]?.length) {
      return {
        flag: true,
        options: tmpl,
      };
    } else {
      return { flag: false };
    }
  };

  /**
   * 控制全选按钮
   * @param {*} checkedList checkbox选中的值
   * @param {*} options 默认全选的值
   * @param {*} setcheckFn 控制全选按钮是否选中的方法
   * @param {*} setDisabledFn 控制全选按钮disabled的方法
   */
  const handleCheckAll = (checkedList, options, setcheckFn, setDisabledFn) => {
    if (!!checkedList.length && checkedList.length === options.length) {
      setcheckFn(true);
      setDisabledFn && setDisabledFn(false);
    } else {
      setcheckFn(false);
      setDisabledFn && setDisabledFn(true);
    }
  };

  /**
   * 处理需要选中的option
   * @param {*} checkedList
   * @param {*} subCheckedList
   * @param {*} subAllOptions
   * @returns [] 需要选中的option list
   */
  const handleSubCheckedList = (checkedList, subCheckedList, subAllOptions) => {
    let arr = [];
    checkedList.forEach(i => {
      const { flag, options } = isPartChecked(subCheckedList, i, subAllOptions);
      if (!flag) {
        arr.push(subAllOptions[i]);
      } else {
        arr.push(options);
      }
    });
    return arr;
  };

  /**
   * 取消checkbox的勾选，设置上一级的值
   * @param {*} checkedList 选中的值
   * @param {*} parentCheckedList 上一级选中的值
   * @param {*} handleParentFn 设置上一级选中值的方法
   */
  const cancelCheckFn = (checkedList, parentCheckedList, handleParentFn) => {
    const tmpl = [];
    checkedList.forEach(i => {
      const lastIndex = i.lastIndexOf('/');
      const s = i.substring(0, lastIndex);
      tmpl.push(s);
    });
    const newTmpl = Array.from(new Set(tmpl));
    if (JSON.stringify(newTmpl) != JSON.stringify(parentCheckedList)) {
      handleParentFn(newTmpl);
    }
  };

  useEffect(() => {
    setIndeterminateYear(!!checkedListYear.length && checkedListYear.length < yearOptions.length);
    if (!!checkedListYear.length) {
      if (checkAllYear) {
        let arr = [];
        checkedListYear.forEach(i => {
          arr.push(everyPowerOptions[i]);
        });
        !isEdit && setCheckedListPower(arr.flat());
      } else {
        const arr = handleSubCheckedList(checkedListYear, checkedListPower, everyPowerOptions);
        !isEdit && setCheckedListPower(arr.flat());
      }
      //设置选中动力方式的disabeld
      // if (!!arr.length) {
      //   // addcheckedDisabled(powerTypeOptions, false)
      //   powerTypeOptions.forEach(i => {
      //     i.disabled = true;
      //     if (checkedListYear.includes(i.parent)) {
      //       i.disabled = false;
      //     }
      //   });
      // } else {
      //   addDisabled(carConfigData, true);
      // }
    } else {
      setCheckedListPower([]);
    }
    //控制全选按钮
    handleCheckAll(checkedListYear, yearOptions, setCheckAllYear);
  }, [checkedListYear]);

  useEffect(() => {
    setIndeterminatePower(!!checkedListPower.length && checkedListPower.length < powerOptions.length);
    if (!!checkedListPower.length) {
      if (checkAllYear) {
        let arr = [];
        checkedListPower.forEach(i => {
          arr.push(everyConfigOptions[i]);
        });
        !isEdit && setCheckedListConfig(arr.flat());
      } else {
        const arr = handleSubCheckedList(checkedListPower, checkedListConfig, everyConfigOptions);
        !isEdit && setCheckedListConfig(arr.flat());
      }
      //处理disabled
      // if (!!arr.length) {
      //   powerTypeOptions.forEach(i => {
      //     addcheckedDisabled(i.children, false);
      //   });
      // } else {
      //   addDisabled(carConfigData, true);
      // }
    } else {
      // addDisabled(carConfigData, true);
      setCheckedListConfig([]);
    }
    //控制全选按钮
    handleCheckAll(checkedListPower, powerOptions, setCheckAllPower, setPowerAllDisabled);

    //动力方式取消选择
    cancelCheckFn(checkedListPower, checkedListYear, setCheckedListYear);
  }, [checkedListPower]);

  useEffect(() => {
    setIndeterminateConfig(!!checkedListConfig.length && checkedListConfig.length < configOptions.length);

    if (!checkedListConfig.length) {
      setPowerAllDisabled(true);
      // addDisabled(carConfigData, true);
    }

    //控制全选按钮
    handleCheckAll(checkedListConfig, configOptions, setCheckAllConfig, setConfigAllDisabled);

    //具体配置取消选择
    if (!isEdit) {
      cancelCheckFn(checkedListConfig, checkedListPower, setCheckedListPower);
    }
    //向父组件传递checkedListConfig V23版本添加
    onDataChange({ [carType]: checkedListConfig });
  }, [checkedListConfig]);

  /**
   * 组装数据
   * @param {*} powerTypeOptions
   * setPowerOptions:所有动力选项的值 []
   * setConfigOptions:所有配置选项的值 []
   * setEveryConfigOptions:分动力方式下的具体配置所有选项值 {"2023/EVR":[],"2023/EV":[]}
   * setEveryPowerOptions:分年款下的动力方式所有选项值 {"2023":[],"2022":[]}
   */
  const handleData = powerTypeOptions => {
    if (!!powerTypeOptions.length) {
      const fn = data => {
        const obj = {};
        data.forEach(element => {
          obj[element.value] = getOptionsArr(element.children);
        });
        return obj;
      };

      powerTypeOptions.forEach(element => {
        setConfigOptions(prev => {
          return [...prev, ...getOptionsArr(element.children)];
        });
      });
      setPowerOptions(getOptionsArr(powerTypeOptions));
      setEveryConfigOptions(fn(powerTypeOptions));
      setEveryPowerOptions(fn(carConfigData));
    }
  };

  useEffect(() => {
    if (!!carConfigData.length) {
      // addDisabled(carConfigData, true);
      const pTypeOptions = getOptions(carConfigData).flat();
      setPowerTypeOptions(pTypeOptions);
      handleData(pTypeOptions);
      setYearOptions(getOptionsArr(carConfigData));
    }
  }, []);

  useEffect(() => {
    if (!!carConfigInfo?.length) {
      setIsEdit(true);
      const yearTmpl = [];
      const powerTmpl = [];
      carConfigInfo.forEach(item => {
        const tmpl = item.split('/');
        const lastIndex = item.lastIndexOf('/');
        yearTmpl.push(tmpl[0] + '/' + tmpl[1]);
        powerTmpl.push(item.substring(0, lastIndex));
      });
      setCheckedListYear(Array.from(new Set(yearTmpl)));
      setCheckedListPower(Array.from(new Set(powerTmpl)));
      setCheckedListConfig(carConfigInfo);
    }
  }, [carConfigInfo]);

  return (
    <>
      {/* 车型年款 */}
      <div className="car-config-content">
        <div className="top">
          <span style={{ display: 'inline-block', marginRight: '8px' }}>车型年款</span>
          <Checkbox
            indeterminate={indeterminateYear}
            checked={checkAllYear}
            onChange={handleChangeCheckAllYear}
          ></Checkbox>
        </div>
        <div className="bottom">
          <Checkbox.Group style={{ width: '100%' }} value={checkedListYear} onChange={handleChangeCheckYear}>
            <Row>
              {carConfigData.map(item => {
                return (
                  <Col span={4} style={{ marginBottom: '20px' }} key={item.value}>
                    <Checkbox value={item.value}>{item.label}</Checkbox>
                  </Col>
                );
              })}
            </Row>
          </Checkbox.Group>
        </div>
      </div>
      {/* 动力方式 */}
      <div className="car-config-content">
        <div className="top">
          <span style={{ display: 'inline-block', marginRight: '8px' }}>动力方式</span>
          <Checkbox
            indeterminate={indeterminatePower}
            checked={checkAllPower}
            onChange={handleChangeCheckAllPower}
            disabled={powerAllDisabled}
          ></Checkbox>
        </div>
        <div className="bottom">
          <Checkbox.Group style={{ width: '100%' }} value={checkedListPower} onChange={handleChangeCheckPower}>
            <Row>
              {powerTypeOptions.map((item, index) => {
                return (
                  <>
                    <Col span={4} style={{ marginBottom: '20px' }} key={item.value}>
                      <Checkbox value={item.value} disabled={!checkedListYear.includes(item.parent)}>
                        {item.label}
                      </Checkbox>
                    </Col>
                    {powerTypeOptions[index + 1]?.parent === powerTypeOptions[index].parent || (
                      <Divider type="vertical" className="custom-vertical-divider" />
                    )}
                  </>
                );
              })}
            </Row>
          </Checkbox.Group>
        </div>
      </div>
      {/* 具体配置 */}
      <div className="car-config-content">
        <div className="top">
          <span style={{ display: 'inline-block', marginRight: '8px' }}>具体配置</span>
          <Checkbox
            indeterminate={indeterminateConfig}
            checked={checkAllConfig}
            onChange={handleChangeCheckAllConfig}
            disabled={configAllDisabled}
          ></Checkbox>
        </div>
        <div className="bottom">
          <Checkbox.Group style={{ width: '100%' }} value={checkedListConfig} onChange={handleChangeCheckConfig}>
            {powerTypeOptions.map((powerOption, index) => (
              <>
                {/* 分割线 */}
                {powerTypeOptions[index].parent === powerTypeOptions[index - 1]?.parent || (
                  <Divider
                    className={`custom-divider ${checkedListYear.includes(powerOption.parent) ? 'divider-blue' : ''}`}
                    orientation="center"
                  >
                    {powerOption.parentLabel}
                  </Divider>
                )}
                <Row
                  key={powerOption.value}
                  className={isBorder(powerTypeOptions, index) ? 'border' : ''}
                  style={{
                    marginBottom: 10,
                    paddingLeft: '8px',
                  }}
                >
                  {/* checkbox配置选项 */}
                  {powerOption.children.map(item => {
                    return (
                      <Col span={6} style={{ marginBottom: '10px' }} key={item.value}>
                        <Checkbox value={item.value} disabled={!checkedListPower.includes(item.parent)}>
                          {item.label}
                        </Checkbox>
                      </Col>
                    );
                  })}
                </Row>
              </>
            ))}
          </Checkbox.Group>
        </div>
      </div>
    </>
  );
});
export default CarConfigModal;
