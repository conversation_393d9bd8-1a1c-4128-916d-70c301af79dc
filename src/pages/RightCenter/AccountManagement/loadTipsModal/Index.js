import React, { useState, useEffect } from 'react'

import { Modal, Button } from 'antd'
import { CheckCircleFilled } from '@ant-design/icons';
import styles from './LoadTipsModal.module.less'

const LoadTipsModal = (props) => {
    const { isLoadTipsModalOpen, setIsLoadTipsModalOpen } = props
    const [timer, setTimer] = useState(3)

    const handleCancel = () => {
        setIsLoadTipsModalOpen(false)
    }

    useEffect(() => {
        setInterval(() => {
            setTimer(timer => timer - 1)
        }, 1000)

        setTimeout(() => {
            setIsLoadTipsModalOpen(false)
        }, 3000)

        return () => {
            clearInterval()
        }
    }, [])

    return (
        <Modal title="提示"
            className={styles.modal}
            open={isLoadTipsModalOpen}
            onOk={handleCancel}
            onCancel={handleCancel}
            footer={
                [<Button type="primary" onClick={handleCancel}>我知道了（{timer}s）</Button>]
            }>
            <p style={{ position: 'relative', paddingLeft: 30 }}><CheckCircleFilled className={styles.icon} />附件已进入浏览器下载，请进入浏览器下载器中查看～</p>
        </Modal>
    )
}
export default LoadTipsModal