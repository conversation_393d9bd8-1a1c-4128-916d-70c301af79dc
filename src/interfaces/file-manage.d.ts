declare namespace FileManage {
  /**
   * Attachment
   */
  export interface Attachment {
    fileId?: number; // int64
    fileName?: string;
    id?: number; // int64
    linkFileId?: number; // int64
    ossObjectName?: string;
    size?: number; // int64
  }
  /**
   * AttachmentVO
   */
  export interface AttachmentVO {
    fileName?: string;
    linkFileId?: number; // int64
    url?: string;
  }
  /**
   * DirectoryDTO
   */
  export interface DirectoryDTO {
    bizType?: number; // int32
    id?: number; // int64
    name?: string;
    parentDirId?: number; // int64
    parentDirPath?: string;
    sortNo?: number; // int32
  }
  /**
   * DirectoryQuery
   */
  export interface DirectoryQuery {
    bizType?: number; // int32
    id?: number; // int64
  }
  /**
   * FileItemAccessConfigDTO
   */
  export interface FileItemAccessConfigDTO {
    deliverScope?: /* Organization */ Organization[];
    deliverType?: number; // int32
    id?: number; // int64
    postType?: number; // int32
    specificPost?: /* Post */ Post[];
    specificUserList?: string[];
  }
  /**
   * FileItemAccessConfigVO
   */
  export interface FileItemAccessConfigVO {
    deliverScope?: /* Organization */ Organization[];
    deliverType?: number; // int32
    id?: number; // int64
    postType?: number; // int32
    specificPost?: /* PostVO */ PostVO[];
    specificUserList?: /* UserVO */ UserVO[];
  }
  /**
   * FileItemDetailVO
   */
  export interface FileItemDetailVO {
    attachmentList?: /* AttachmentVO */ AttachmentVO[];
    bizType?: number; // int32
    deliverType?: number; // int32
    expireTime?: string; // date-time
    id?: number; // int64
    name?: string;
    ossObjectName?: string;
    parentDirId?: number; // int64
    parentDirPath?: string;
    permission?: number; // int32
    postType?: number; // int32
    sendBy?: string;
    sendTime?: string; // date-time
    seresUser?: string;
    specificUserList?: /* UserVO */ UserVO[];
    status?: number; // int32
    updateBy?: string;
    updateTime?: string; // date-time
  }
  /**
   * FileItemEditDTO
   */
  export interface FileItemEditDTO {
    attachmentList?: /* Attachment */ Attachment[];
    bizType?: number; // int32
    deliverScope?: /* Organization */ Organization[];
    deliverType?: number; // int32
    expireTime?: string; // date-time
    id?: number; // int64
    isSendMsg?: number; // int32
    linkFileIdList?: number /* int64 */[];
    name?: string;
    ossObjectName?: string;
    parentDirId?: number; // int64
    parentDirPath?: string;
    permission?: number; // int32
    postType?: number; // int32
    seresOrgIdList?: number /* int64 */[];
    seresUserOneIdList?: string[];
    size?: number; // int64
    specificPost?: /* Post */ Post[];
    specificUserList?: string[];
  }
  /**
   * FileItemEditVO
   */
  export interface FileItemEditVO {
    attachmentList?: /* Attachment */ Attachment[];
    bizType?: number; // int32
    deliverScope?: /* Organization */ Organization[];
    deliverType?: number; // int32
    expireTime?: string; // date-time
    id?: number; // int64
    isSendMsg?: number; // int32
    linkFileList?: /* LinkFileVO */ LinkFileVO[];
    name?: string;
    ossObjectName?: string;
    parentDirId?: number; // int64
    parentDirPath?: string;
    permission?: number; // int32
    postType?: number; // int32
    seresOrgIdList?: number /* int64 */[];
    seresUserList?: /* SeresUserVO */ SeresUserVO[];
    size?: number; // int64
    specificPost?: /* PostVO */ PostVO[];
    specificUserList?: /* UserVO */ UserVO[];
  }
  /**
   * FileItemListVO
   */
  export interface FileItemListVO {
    bizType?: number; // int32
    deliverType?: number; // int32
    expireTime?: string; // date-time
    id?: number; // int64
    isSendMsg?: number; // int32
    name?: string;
    organization?: string;
    parentDirId?: number; // int64
    parentDirPath?: string;
    permission?: number; // int32
    position?: string;
    postType?: number; // int32
    sendBy?: string;
    sendTime?: string; // date-time
    seresUser?: string;
    specificUser?: string;
    status?: number; // int32
    updateBy?: string;
    updateTime?: string; // date-time
  }
  /**
   * FileItemQueryDTO
   */
  export interface FileItemQueryDTO {
    bizType?: number; // int32
    dirId?: number; // int64
    endTime?: string; // date-time
    name?: string;
    pageNum?: number; // int32
    pageSize?: number; // int32
    startTime?: string; // date-time
    statusList?: number /* int32 */[];
  }
  /**
   * FileOptSummary
   */
  export interface FileOptSummary {
    createTime?: string; // date-time
    createUser?: string;
    downloadAvgCount?: number;
    downloadCount?: number; // int32
    downloadRate?: number;
    fileId?: number; // int64
    id?: number; // int64
    innerUserOpt?: number; // int32
    notDownloadCount?: number; // int32
    notPreviewCount?: number; // int32
    organizeCode?: string;
    organizeName?: string;
    previewAvgCount?: number;
    previewAvgDuration?: number;
    previewCount?: number; // int32
    previewRate?: number;
    sendCount?: number; // int32
    sendType?: number; // int32
    totalDownloadCount?: number; // int32
    totalPreviewCount?: number; // int32
    totalPreviewDuration?: number;
    updateTime?: string; // date-time
    updateUser?: string;
  }
  /**
   * FileOrganizationRelationQuery
   */
  export interface FileOrganizationRelationQuery {
    fileId?: number; // int64
    pageNum?: number; // int32
    pageSize?: number; // int32
  }
  /**
   * FilePostRelationQuery
   */
  export interface FilePostRelationQuery {
    fileId?: number; // int64
    pageNum?: number; // int32
    pageSize?: number; // int32
  }
  /**
   * FileRecognitionDTO
   */
  export interface FileRecognitionDTO {
    bizCategory?: string;
    createTime?: number; // int64
    fileName?: string;
    fromOrg?: string;
    fromUser?: string;
    fromUserNo?: string;
    sendTime?: number; // int64
    sendUser?: string;
  }
  /**
   * FileSeresUserDTO
   */
  export interface FileSeresUserDTO {
    fileId?: number; // int64
    seresOrgIdList?: number /* int64 */[];
    seresUserOneIdList?: string[];
  }
  /**
   * FileSeresUserVO
   */
  export interface FileSeresUserVO {
    seresOrgIdList?: number /* int64 */[];
    seresUserList?: /* SeresUserVO */ SeresUserVO[];
  }
  /**
   * FileSummaryDTO
   */
  export interface FileSummaryDTO {
    downloadAvgCount?: number;
    downloadCount?: number; // int32
    downloadRate?: number;
    notDownloadCount?: number; // int32
    notPreviewCount?: number; // int32
    previewAvgCount?: number;
    previewAvgDuration?: number;
    previewCount?: number; // int32
    previewRate?: number;
    sendPeopleCount?: number; // int32
    sendStoreCount?: number; // int32
    sendType?: number; // int32
    totalDownloadCount?: number; // int32
    totalPreviewCount?: number; // int32
    totalPreviewDuration?: number;
  }
  /**
   * FrequentPost
   */
  export interface FrequentPost {
    codeList?: string;
    id?: number; // int64
    name?: string;
    nameList?: string;
    status?: number; // int32
    type?: number; // int32
    updateTime?: string; // date-time
  }
  /**
   * FrequentPostDTO
   */
  export interface FrequentPostDTO {
    id?: number; // int64
    list?: /* PostVO */ PostVO[];
    name?: string;
    type?: number; // int32
  }
  /**
   * FrequentPostVO
   */
  export interface FrequentPostVO {
    id?: number; // int64
    list?: /* PostVO */ PostVO[];
    name?: string;
    type?: number; // int32
  }
  /**
   * LinkFileVO
   */
  export interface LinkFileVO {
    bizType?: number; // int32
    createBy?: string;
    fileName?: string;
    id?: number; // int64
  }
  /**
   * MoveDIrCommand
   */
  export interface MoveDIrCommand {
    dirId?: number; // int64
    fileIdList?: number /* int64 */[];
  }
  /**
   * MovingDirectoryDTO
   */
  export interface MovingDirectoryDTO {
    sortedDirIds?: number /* int64 */[];
  }
  /**
   * NodeListVO
   */
  export interface NodeListVO {
    children?: /* DirectoryDTO */ DirectoryDTO[];
  }
  /**
   * OSSRecordVO
   */
  export interface OSSRecordVO {
    fileName?: string;
    ossObjectName?: string;
  }
  /**
   * Organization
   */
  export interface Organization {
    code?: string;
    name?: string;
  }
  /**
   * PageQuery
   */
  export interface PageQuery {
    pageNum?: number; // int32
    pageSize?: number; // int32
  }
  /**
   * PageRespDTO«FileItemListVO»
   */
  export interface PageRespDTOFileItemListVO {
    list?: /* FileItemListVO */ FileItemListVO[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«FileOptSummary»
   */
  export interface PageRespDTOFileOptSummary {
    list?: /* FileOptSummary */ FileOptSummary[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«FrequentPost»
   */
  export interface PageRespDTOFrequentPost {
    list?: /* FrequentPost */ FrequentPost[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«Organization»
   */
  export interface PageRespDTOOrganization {
    list?: /* Organization */ Organization[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«Post»
   */
  export interface PageRespDTOPost {
    list?: /* Post */ Post[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«PreviewDownloadDTO»
   */
  export interface PageRespDTOPreviewDownloadDTO {
    list?: /* PreviewDownloadDTO */ PreviewDownloadDTO[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«SeresDetailPageRespDTO»
   */
  export interface PageRespDTOSeresDetailPageRespDTO {
    list?: /* SeresDetailPageRespDTO */ SeresDetailPageRespDTO[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«SeresOrganization»
   */
  export interface PageRespDTOSeresOrganization {
    list?: /* SeresOrganization */ SeresOrganization[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«SeresPost»
   */
  export interface PageRespDTOSeresPost {
    list?: /* SeresPost */ SeresPost[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«SeresUserDTO»
   */
  export interface PageRespDTOSeresUserDTO {
    list?: /* SeresUserDTO */ SeresUserDTO[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«StoreDetailPageRespDTO»
   */
  export interface PageRespDTOStoreDetailPageRespDTO {
    list?: /* StoreDetailPageRespDTO */ StoreDetailPageRespDTO[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«SupplierDetailPageRespDTO»
   */
  export interface PageRespDTOSupplierDetailPageRespDTO {
    list?: /* SupplierDetailPageRespDTO */ SupplierDetailPageRespDTO[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * PageRespDTO«SupplierUserVO»
   */
  export interface PageRespDTOSupplierUserVO {
    list?: /* SupplierUserVO */ SupplierUserVO[];
    pagination?: /* Pagination */ Pagination;
  }
  /**
   * Pagination
   */
  export interface Pagination {
    current?: number; // int32
    pageSize?: number; // int32
    total?: number; // int64
  }
  /**
   * Post
   */
  export interface Post {
    code?: string;
    name?: string;
  }
  /**
   * PostVO
   */
  export interface PostVO {
    code?: string;
    deleted?: boolean;
    name?: string;
  }
  /**
   * PreviewDownloadDTO
   */
  export interface PreviewDownloadDTO {
    exportTime?: string; // date-time
    fileId?: number; // int64
    fileName?: string;
    operateNum?: number; // int32
    organizeCode?: string;
    organizeName?: string;
    previewDuration?: number;
    sequenceNo?: number; // int32
    userName?: string;
    userOneId?: string;
  }
  /**
   * PreviewDownloadQryDTO
   */
  export interface PreviewDownloadQryDTO {
    fileId?: number; // int64
    organizeCode?: string;
    pageNum?: number; // int32
    pageSize?: number; // int32
    queryType?: number; // int32
  }
  /**
   * RemoveNodeCommand
   */
  export interface RemoveNodeCommand {
    id?: number; // int64
    name?: string;
  }
  /**
   * ResponseData
   */
  export interface ResponseData {
    msg?: string;
    msgCode?: string;
    resp?: {
      [key: string]: any;
    }[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«DirectoryDTO»
   */
  export interface ResponseDataDirectoryDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* DirectoryDTO */ DirectoryDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«FileItemAccessConfigVO»
   */
  export interface ResponseDataFileItemAccessConfigVO {
    msg?: string;
    msgCode?: string;
    resp?: /* FileItemAccessConfigVO */ FileItemAccessConfigVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«FileItemDetailVO»
   */
  export interface ResponseDataFileItemDetailVO {
    msg?: string;
    msgCode?: string;
    resp?: /* FileItemDetailVO */ FileItemDetailVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«FileItemEditVO»
   */
  export interface ResponseDataFileItemEditVO {
    msg?: string;
    msgCode?: string;
    resp?: /* FileItemEditVO */ FileItemEditVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«FileRecognitionDTO»
   */
  export interface ResponseDataFileRecognitionDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* FileRecognitionDTO */ FileRecognitionDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«FileSeresUserVO»
   */
  export interface ResponseDataFileSeresUserVO {
    msg?: string;
    msgCode?: string;
    resp?: /* FileSeresUserVO */ FileSeresUserVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«FileSummaryDTO»
   */
  export interface ResponseDataFileSummaryDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* FileSummaryDTO */ FileSummaryDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«FrequentPostVO»
   */
  export interface ResponseDataFrequentPostVO {
    msg?: string;
    msgCode?: string;
    resp?: /* FrequentPostVO */ FrequentPostVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«int»
   */
  export interface ResponseDataInt {
    msg?: string;
    msgCode?: string;
    resp?: number /* int32 */[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«LinkFileVO»
   */
  export interface ResponseDataLinkFileVO {
    msg?: string;
    msgCode?: string;
    resp?: /* LinkFileVO */ LinkFileVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«long»
   */
  export interface ResponseDataLong {
    msg?: string;
    msgCode?: string;
    resp?: number /* int64 */[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«NodeListVO»
   */
  export interface ResponseDataNodeListVO {
    msg?: string;
    msgCode?: string;
    resp?: /* NodeListVO */ NodeListVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«OSSRecordVO»
   */
  export interface ResponseDataOSSRecordVO {
    msg?: string;
    msgCode?: string;
    resp?: /* OSSRecordVO */ OSSRecordVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«object»
   */
  export interface ResponseDataObject {
    msg?: string;
    msgCode?: string;
    resp?: {
      [key: string]: any;
    }[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«FileItemListVO»»
   */
  export interface ResponseDataPageRespDTOFileItemListVO {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«FileItemListVO» */ PageRespDTOFileItemListVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«FileOptSummary»»
   */
  export interface ResponseDataPageRespDTOFileOptSummary {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«FileOptSummary» */ PageRespDTOFileOptSummary[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«FrequentPost»»
   */
  export interface ResponseDataPageRespDTOFrequentPost {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«FrequentPost» */ PageRespDTOFrequentPost[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«Organization»»
   */
  export interface ResponseDataPageRespDTOOrganization {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«Organization» */ PageRespDTOOrganization[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«Post»»
   */
  export interface ResponseDataPageRespDTOPost {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«Post» */ PageRespDTOPost[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«PreviewDownloadDTO»»
   */
  export interface ResponseDataPageRespDTOPreviewDownloadDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«PreviewDownloadDTO» */ PageRespDTOPreviewDownloadDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«SeresDetailPageRespDTO»»
   */
  export interface ResponseDataPageRespDTOSeresDetailPageRespDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«SeresDetailPageRespDTO» */ PageRespDTOSeresDetailPageRespDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«SeresOrganization»»
   */
  export interface ResponseDataPageRespDTOSeresOrganization {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«SeresOrganization» */ PageRespDTOSeresOrganization[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«SeresPost»»
   */
  export interface ResponseDataPageRespDTOSeresPost {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«SeresPost» */ PageRespDTOSeresPost[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«SeresUserDTO»»
   */
  export interface ResponseDataPageRespDTOSeresUserDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«SeresUserDTO» */ PageRespDTOSeresUserDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«StoreDetailPageRespDTO»»
   */
  export interface ResponseDataPageRespDTOStoreDetailPageRespDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«StoreDetailPageRespDTO» */ PageRespDTOStoreDetailPageRespDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«SupplierDetailPageRespDTO»»
   */
  export interface ResponseDataPageRespDTOSupplierDetailPageRespDTO {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«SupplierDetailPageRespDTO» */ PageRespDTOSupplierDetailPageRespDTO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«PageRespDTO«SupplierUserVO»»
   */
  export interface ResponseDataPageRespDTOSupplierUserVO {
    msg?: string;
    msgCode?: string;
    resp?: /* PageRespDTO«SupplierUserVO» */ PageRespDTOSupplierUserVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«SeresOrganization»
   */
  export interface ResponseDataSeresOrganization {
    msg?: string;
    msgCode?: string;
    resp?: /* SeresOrganization */ SeresOrganization[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«SeresPost»
   */
  export interface ResponseDataSeresPost {
    msg?: string;
    msgCode?: string;
    resp?: /* SeresPost */ SeresPost[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«StoreOrganizationVO»
   */
  export interface ResponseDataStoreOrganizationVO {
    msg?: string;
    msgCode?: string;
    resp?: /* StoreOrganizationVO */ StoreOrganizationVO[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«StorePost»
   */
  export interface ResponseDataStorePost {
    msg?: string;
    msgCode?: string;
    resp?: /* StorePost */ StorePost[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«string»
   */
  export interface ResponseDataString {
    msg?: string;
    msgCode?: string;
    resp?: string[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«SupplierOrganization»
   */
  export interface ResponseDataSupplierOrganization {
    msg?: string;
    msgCode?: string;
    resp?: /* SupplierOrganization */ SupplierOrganization[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * ResponseData«SupplierPost»
   */
  export interface ResponseDataSupplierPost {
    msg?: string;
    msgCode?: string;
    resp?: /* SupplierPost */ SupplierPost[];
    success?: boolean;
    total?: number; // int32
  }
  /**
   * SeresDetailPageQryDTO
   */
  export interface SeresDetailPageQryDTO {
    fileId?: number; // int64
    pageNum?: number; // int32
    pageSize?: number; // int32
    queryType?: number; // int32
  }
  /**
   * SeresDetailPageRespDTO
   */
  export interface SeresDetailPageRespDTO {
    exportTime?: string; // date-time
    fileName?: string;
    sendTime?: string; // date-time
    sequenceNo?: number; // int64
    userName?: string;
    userNo?: string;
  }
  /**
   * SeresOrganization
   */
  export interface SeresOrganization {
    id?: number; // int64
    name?: string;
  }
  /**
   * SeresPost
   */
  export interface SeresPost {
    id?: number; // int64
    name?: string;
  }
  /**
   * SeresUserDTO
   */
  export interface SeresUserDTO {
    id?: number; // int64
    seresOrgId?: number; // int64
    seresOrgName?: string;
    seresPostId?: number; // int64
    seresPostName?: string;
    userName?: string;
    userOneId?: string;
  }
  /**
   * SeresUserVO
   */
  export interface SeresUserVO {
    userName?: string;
    userOneId?: string;
  }
  /**
   * StoreDetailPageQryDTO
   */
  export interface StoreDetailPageQryDTO {
    fileId?: number; // int64
    organizeCode?: string;
    pageNum?: number; // int32
    pageSize?: number; // int32
    queryType?: number; // int32
  }
  /**
   * StoreDetailPageRespDTO
   */
  export interface StoreDetailPageRespDTO {
    exportTime?: string; // date-time
    fileId?: number; // int64
    fileName?: string;
    positionCode?: string;
    positionName?: string;
    sendTime?: string; // date-time
    serialNo?: number; // int32
    storeCode?: string;
    storeName?: string;
    userName?: string;
    userNo?: string;
  }
  /**
   * StoreOrganizationVO
   */
  export interface StoreOrganizationVO {
    area?: boolean;
    children?: /* StoreOrganizationVO */ StoreOrganizationVO[];
    code?: string;
    name?: string;
  }
  /**
   * StorePost
   */
  export interface StorePost {
    code?: string;
    id?: number; // int64
    name?: string;
    status?: number; // int32
  }
  /**
   * SummaryExportQueryDTO
   */
  export interface SummaryExportQueryDTO {
    downloadAvgCount?: boolean;
    downloadCount?: boolean;
    downloadRate?: boolean;
    fileId?: number; // int64
    notDownloadCount?: boolean;
    notPreviewCount?: boolean;
    organizeCodes?: string[];
    previewAvgCount?: boolean;
    previewAvgDuration?: boolean;
    previewCount?: boolean;
    previewRate?: boolean;
    sendCount?: boolean;
    totalDownloadCount?: boolean;
    totalPreviewCount?: boolean;
    totalPreviewDuration?: boolean;
  }
  /**
   * SummaryQueryDTO
   */
  export interface SummaryQueryDTO {
    downloadAvgCount?: boolean;
    downloadCount?: boolean;
    downloadRate?: boolean;
    fileId?: number; // int64
    notDownloadCount?: boolean;
    notPreviewCount?: boolean;
    organizeName?: string;
    pageNum?: number; // int32
    pageSize?: number; // int32
    previewAvgCount?: boolean;
    previewAvgDuration?: boolean;
    previewCount?: boolean;
    previewRate?: boolean;
    sendCount?: boolean;
    totalDownloadCount?: boolean;
    totalPreviewCount?: boolean;
    totalPreviewDuration?: boolean;
  }
  /**
   * SupplierDetailPageQryDTO
   */
  export interface SupplierDetailPageQryDTO {
    fileId?: number; // int64
    organizeCode?: string;
    pageNum?: number; // int32
    pageSize?: number; // int32
    queryType?: number; // int32
  }
  /**
   * SupplierDetailPageRespDTO
   */
  export interface SupplierDetailPageRespDTO {
    exportTime?: string; // date-time
    fileId?: number; // int64
    fileName?: string;
    positionCode?: string;
    positionName?: string;
    sendTime?: string; // date-time
    serialNo?: number; // int32
    supplierCode?: string;
    supplierName?: string;
    userName?: string;
    userNo?: string;
  }
  /**
   * SupplierOrganization
   */
  export interface SupplierOrganization {
    code?: string;
    id?: number; // int64
    name?: string;
    status?: number; // int32
  }
  /**
   * SupplierPost
   */
  export interface SupplierPost {
    code?: string;
    id?: number; // int64
    name?: string;
    status?: number; // int32
  }
  /**
   * SupplierUserVO
   */
  export interface SupplierUserVO {
    organization?: string;
    position?: string;
    userName?: string;
    userOneId?: string;
  }
  /**
   * SwitchCommand
   */
  export interface SwitchCommand {
    id?: number; // int64
    result?: boolean;
  }
  /**
   * UpdateExpireTimeCommand
   */
  export interface UpdateExpireTimeCommand {
    expireTime?: string; // date-time
    id?: number; // int64
  }
  /**
   * UpdateFileItemStateCommand
   */
  export interface UpdateFileItemStateCommand {
    id?: number; // int64
    status?: number; // int32
  }
  /**
   * UpdateNodeCommand
   */
  export interface UpdateNodeCommand {
    id?: number; // int64
    newName?: string;
    oldName?: string;
  }
  /**
   * UpdatePermissionCommand
   */
  export interface UpdatePermissionCommand {
    download?: boolean;
    id?: number; // int64
  }
  /**
   * UserPageQuery
   */
  export interface UserPageQuery {
    orgCodeList?: string[];
    pageNum?: number; // int32
    pageSize?: number; // int32
    searchText?: string;
  }
  /**
   * UserVO
   */
  export interface UserVO {
    position?: string;
    userName?: string;
    userOneId?: string;
  }
}
declare namespace FileManage {
  namespace AccessConfigUsingPOST {
    export interface BodyParameters {
      fileItemAccessConfigDTO: Parameters.FileItemAccessConfigDTO;
    }
    namespace Parameters {
      export type FileItemAccessConfigDTO = /* FileItemAccessConfigDTO */ FileManage.FileItemAccessConfigDTO;
    }
    namespace Responses {
      export type $200 = /* ResponseData«long» */ FileManage.ResponseDataLong;
    }
  }
  namespace CancelUsingPOST {
    export interface BodyParameters {
      updateFileItemStateCommand: Parameters.UpdateFileItemStateCommand;
    }
    namespace Parameters {
      export type UpdateFileItemStateCommand = /* UpdateFileItemStateCommand */ FileManage.UpdateFileItemStateCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace CountSeresOrganizationRelationUsingGET {
    namespace Parameters {
      /**
       * orgId
       */
      export type OrgId = number; // int64
    }
    export interface QueryParameters {
      orgId: /* orgId */ Parameters.OrgId /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«int» */ FileManage.ResponseDataInt;
    }
  }
  namespace CountSeresPostRelationUsingGET {
    namespace Parameters {
      /**
       * orgId
       */
      export type OrgId = number; // int64
    }
    export interface QueryParameters {
      orgId: /* orgId */ Parameters.OrgId /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«int» */ FileManage.ResponseDataInt;
    }
  }
  namespace CreateDirectoryUsingPOST {
    export interface BodyParameters {
      directoryDTO: Parameters.DirectoryDTO;
    }
    namespace Parameters {
      export type DirectoryDTO = /* DirectoryDTO */ FileManage.DirectoryDTO;
    }
    namespace Responses {
      export type $200 = /* ResponseData«DirectoryDTO» */ FileManage.ResponseDataDirectoryDTO;
    }
  }
  namespace DeleteFrequentPostUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace DeleteSeresOrganizationUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace DeleteSeresPostUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace DeleteSeresUserUsingPOST {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace DeliverUsingPOST {
    export interface BodyParameters {
      updateFileItemStateCommand: Parameters.UpdateFileItemStateCommand;
    }
    namespace Parameters {
      export type UpdateFileItemStateCommand = /* UpdateFileItemStateCommand */ FileManage.UpdateFileItemStateCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace DeregisterInstanceUsingGET {
    namespace Responses {
      export type $200 = string;
    }
  }
  namespace DetailFileItemUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«FileItemDetailVO» */ FileManage.ResponseDataFileItemDetailVO;
    }
  }
  namespace ExpireUsingPOST {
    export interface BodyParameters {
      updateExpireTimeCommand: Parameters.UpdateExpireTimeCommand;
    }
    namespace Parameters {
      export type UpdateExpireTimeCommand = /* UpdateExpireTimeCommand */ FileManage.UpdateExpireTimeCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace ExportInCompleteListUsingPOST {
    export interface BodyParameters {
      storeDetail: Parameters.StoreDetail;
    }
    namespace Parameters {
      export type StoreDetail = /* StoreDetailPageQryDTO */ FileManage.StoreDetailPageQryDTO;
    }
  }
  namespace ExportStatisticDetailUsingPOST {
    export interface BodyParameters {
      detailExport: Parameters.DetailExport;
    }
    namespace Parameters {
      export type DetailExport = /* SummaryExportQueryDTO */ FileManage.SummaryExportQueryDTO;
    }
  }
  namespace ExportStatisticUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
  }
  namespace GenerateLinkUsingGET {
    namespace Parameters {
      /**
       * objectName
       */
      export type ObjectName = string;
    }
    export interface QueryParameters {
      objectName: /* objectName */ Parameters.ObjectName;
    }
    namespace Responses {
      export type $200 = /* ResponseData«string» */ FileManage.ResponseDataString;
    }
  }
  namespace GetFileItemEditDetailUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«FileItemEditVO» */ FileManage.ResponseDataFileItemEditVO;
    }
  }
  namespace ImportSeresUserUsingPOST {
    export interface FormDataParameters {
      file: /* file */ Parameters.File;
    }
    namespace Parameters {
      /**
       * file
       */
      export type File = unknown;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace InnerOptDetailQueryUsingPOST {
    export interface BodyParameters {
      detailPageQry: Parameters.DetailPageQry;
    }
    namespace Parameters {
      export type DetailPageQry = /* SeresDetailPageQryDTO */ FileManage.SeresDetailPageQryDTO;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«SeresDetailPageRespDTO»» */ FileManage.ResponseDataPageRespDTOSeresDetailPageRespDTO;
    }
  }
  namespace InnerUserSummaryUsingGET {
    namespace Parameters {
      /**
       * fileId
       */
      export type FileId = number; // int64
    }
    export interface QueryParameters {
      fileId: /* fileId */ Parameters.FileId /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«FileSummaryDTO» */ FileManage.ResponseDataFileSummaryDTO;
    }
  }
  namespace ListFileItemsUsingPOST {
    export interface BodyParameters {
      fileItemQueryDTO: Parameters.FileItemQueryDTO;
    }
    namespace Parameters {
      export type FileItemQueryDTO = /* FileItemQueryDTO */ FileManage.FileItemQueryDTO;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«FileItemListVO»» */ FileManage.ResponseDataPageRespDTOFileItemListVO;
    }
  }
  namespace ListFrequentPostsUsingGET {
    namespace Responses {
      export type $200 = /* ResponseData«FrequentPostVO» */ FileManage.ResponseDataFrequentPostVO;
    }
  }
  namespace ListFrequentPostsUsingPOST {
    export interface BodyParameters {
      pageQuery: Parameters.PageQuery;
    }
    namespace Parameters {
      export type PageQuery = /* PageQuery */ FileManage.PageQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«FrequentPost»» */ FileManage.ResponseDataPageRespDTOFrequentPost;
    }
  }
  namespace ListNodesUsingPOST {
    export interface BodyParameters {
      directoryQuery: Parameters.DirectoryQuery;
    }
    namespace Parameters {
      export type DirectoryQuery = /* DirectoryQuery */ FileManage.DirectoryQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«NodeListVO» */ FileManage.ResponseDataNodeListVO;
    }
  }
  namespace ListOrganizationUsingPOST {
    export interface BodyParameters {
      fileOrganizationRelationQuery: Parameters.FileOrganizationRelationQuery;
    }
    namespace Parameters {
      export type FileOrganizationRelationQuery =
        /* FileOrganizationRelationQuery */ FileManage.FileOrganizationRelationQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«Organization»» */ FileManage.ResponseDataPageRespDTOOrganization;
    }
  }
  namespace ListPartnerUserByPageUsingPOST {
    export interface BodyParameters {
      userPageQuery: Parameters.UserPageQuery;
    }
    namespace Parameters {
      export type UserPageQuery = /* UserPageQuery */ FileManage.UserPageQuery;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«SupplierUserVO»» */ FileManage.ResponseDataPageRespDTOSupplierUserVO;
    }
  }
  namespace ListPostUsingPOST {
    export interface BodyParameters {
      filePostRelationQuery: Parameters.FilePostRelationQuery;
    }
    namespace Parameters {
      export type FilePostRelationQuery = /* FilePostRelationQuery */ FileManage.FilePostRelationQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«Post»» */ FileManage.ResponseDataPageRespDTOPost;
    }
  }
  namespace ListSeresOrganizationByPageUsingPOST {
    export interface BodyParameters {
      pageQuery: Parameters.PageQuery;
    }
    namespace Parameters {
      export type PageQuery = /* PageQuery */ FileManage.PageQuery;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«SeresOrganization»» */ FileManage.ResponseDataPageRespDTOSeresOrganization;
    }
  }
  namespace ListSeresOrganizationUsingGET {
    namespace Responses {
      export type $200 = /* ResponseData«SeresOrganization» */ FileManage.ResponseDataSeresOrganization;
    }
  }
  namespace ListSeresPostByPageUsingPOST {
    export interface BodyParameters {
      pageQuery: Parameters.PageQuery;
    }
    namespace Parameters {
      export type PageQuery = /* PageQuery */ FileManage.PageQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«SeresPost»» */ FileManage.ResponseDataPageRespDTOSeresPost;
    }
  }
  namespace ListSeresPostUsingGET {
    namespace Responses {
      export type $200 = /* ResponseData«SeresPost» */ FileManage.ResponseDataSeresPost;
    }
  }
  namespace ListSeresUserByPageUsingPOST {
    export interface BodyParameters {
      userPageQuery: Parameters.UserPageQuery;
    }
    namespace Parameters {
      export type UserPageQuery = /* UserPageQuery */ FileManage.UserPageQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«SeresUserDTO»» */ FileManage.ResponseDataPageRespDTOSeresUserDTO;
    }
  }
  namespace ListStoreOrganizationByPageUsingPOST {
    namespace Parameters {
      /**
       * pageQuery
       */
      export type PageQuery = string;
    }
    export interface QueryParameters {
      pageQuery: /* pageQuery */ Parameters.PageQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«Organization»» */ FileManage.ResponseDataPageRespDTOOrganization;
    }
  }
  namespace ListStoreOrganizationUsingGET {
    namespace Responses {
      export type $200 = /* ResponseData«StoreOrganizationVO» */ FileManage.ResponseDataStoreOrganizationVO;
    }
  }
  namespace ListStorePostByPageUsingPOST {
    export interface BodyParameters {
      pageQuery: Parameters.PageQuery;
    }
    namespace Parameters {
      export type PageQuery = /* PageQuery */ FileManage.PageQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«Post»» */ FileManage.ResponseDataPageRespDTOPost;
    }
  }
  namespace ListStorePostUsingGET {
    namespace Responses {
      export type $200 = /* ResponseData«StorePost» */ FileManage.ResponseDataStorePost;
    }
  }
  namespace ListSupplierOrganizationByPageUsingPOST {
    export interface BodyParameters {
      pageQuery: Parameters.PageQuery;
    }
    namespace Parameters {
      export type PageQuery = /* PageQuery */ FileManage.PageQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«Organization»» */ FileManage.ResponseDataPageRespDTOOrganization;
    }
  }
  namespace ListSupplierOrganizationUsingGET {
    namespace Responses {
      export type $200 = /* ResponseData«SupplierOrganization» */ FileManage.ResponseDataSupplierOrganization;
    }
  }
  namespace ListSupplierPostByPageUsingPOST {
    export interface BodyParameters {
      pageQuery: Parameters.PageQuery;
    }
    namespace Parameters {
      export type PageQuery = /* PageQuery */ FileManage.PageQuery;
    }
    namespace Responses {
      export type $200 = /* ResponseData«PageRespDTO«Post»» */ FileManage.ResponseDataPageRespDTOPost;
    }
  }
  namespace ListSupplierPostUsingGET {
    namespace Responses {
      export type $200 = /* ResponseData«SupplierPost» */ FileManage.ResponseDataSupplierPost;
    }
  }
  namespace MoveDirUsingPOST {
    export interface BodyParameters {
      moveDIrCommand: Parameters.MoveDIrCommand;
    }
    namespace Parameters {
      export type MoveDIrCommand = /* MoveDIrCommand */ FileManage.MoveDIrCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace QueryAccessConfigUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«FileItemAccessConfigVO» */ FileManage.ResponseDataFileItemAccessConfigVO;
    }
  }
  namespace QuerySeresUserUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«FileSeresUserVO» */ FileManage.ResponseDataFileSeresUserVO;
    }
  }
  namespace RecognitionUsingPOST {
    export interface FormDataParameters {
      file: /* file */ Parameters.File;
    }
    namespace Parameters {
      /**
       * file
       */
      export type File = unknown;
    }
    namespace Responses {
      export type $200 = /* ResponseData«FileRecognitionDTO» */ FileManage.ResponseDataFileRecognitionDTO;
    }
  }
  namespace RegisterInstanceUsingGET {
    namespace Responses {
      export type $200 = string;
    }
  }
  namespace RemoveDirectoryUsingPOST {
    export interface BodyParameters {
      removeNodeCommand: Parameters.RemoveNodeCommand;
    }
    namespace Parameters {
      export type RemoveNodeCommand = /* RemoveNodeCommand */ FileManage.RemoveNodeCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace SaveFileItemUsingPOST {
    export interface BodyParameters {
      fileItemEditDTO: Parameters.FileItemEditDTO;
    }
    namespace Parameters {
      export type FileItemEditDTO = /* FileItemEditDTO */ FileManage.FileItemEditDTO;
    }
    namespace Responses {
      export type $200 = /* ResponseData«long» */ FileManage.ResponseDataLong;
    }
  }
  namespace SaveFrequentPostUsingPOST {
    export interface BodyParameters {
      frequentPostDTO: Parameters.FrequentPostDTO;
    }
    namespace Parameters {
      export type FrequentPostDTO = /* FrequentPostDTO */ FileManage.FrequentPostDTO;
    }
    namespace Responses {
      export type $200 = /* ResponseData«long» */ FileManage.ResponseDataLong;
    }
  }
  namespace SaveSeresOrganizationUsingPOST {
    export interface BodyParameters {
      seresOrganization: Parameters.SeresOrganization;
    }
    namespace Parameters {
      export type SeresOrganization = /* SeresOrganization */ FileManage.SeresOrganization;
    }
    namespace Responses {
      export type $200 = /* ResponseData«SeresOrganization» */ FileManage.ResponseDataSeresOrganization;
    }
  }
  namespace SaveSeresPostUsingPOST {
    export interface BodyParameters {
      SeresPost: Parameters.SeresPost;
    }
    namespace Parameters {
      export type SeresPost = /* SeresPost */ FileManage.SeresPost;
    }
    namespace Responses {
      export type $200 = /* ResponseData«SeresPost» */ FileManage.ResponseDataSeresPost;
    }
  }
  namespace SaveSeresUserUsingPOST {
    export interface BodyParameters {
      fileSeresUserDTO: Parameters.FileSeresUserDTO;
    }
    namespace Parameters {
      export type FileSeresUserDTO = /* FileSeresUserDTO */ FileManage.FileSeresUserDTO;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace SaveSeresUserUsingPOST1 {
    export interface BodyParameters {
      seresUser: Parameters.SeresUser;
    }
    namespace Parameters {
      export type SeresUser = /* SeresUserDTO */ FileManage.SeresUserDTO;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace SearchOnlineFileItemsUsingGET {
    namespace Parameters {
      /**
       * bizType
       */
      export type BizType = number; // int32
      /**
       * name
       */
      export type Name = string;
    }
    export interface QueryParameters {
      bizType?: /* bizType */ Parameters.BizType /* int32 */;
      name?: /* name */ Parameters.Name;
    }
    namespace Responses {
      export type $200 = /* ResponseData«LinkFileVO» */ FileManage.ResponseDataLinkFileVO;
    }
  }
  namespace SeresSummaryExportUsingGET {
    namespace Parameters {
      /**
       * fileId
       */
      export type FileId = number; // int64
    }
    export interface QueryParameters {
      fileId: /* fileId */ Parameters.FileId /* int64 */;
    }
  }
  namespace SeresUserDetailExportUsingGET {
    namespace Parameters {
      /**
       * downloadType
       */
      export type DownloadType = number; // int32
      /**
       * fileId
       */
      export type FileId = number; // int64
    }
    export interface QueryParameters {
      downloadType: /* downloadType */ Parameters.DownloadType /* int32 */;
      fileId: /* fileId */ Parameters.FileId /* int64 */;
    }
  }
  namespace SetMsgSendUsingPOST {
    export interface BodyParameters {
      switchCommand: Parameters.SwitchCommand;
    }
    namespace Parameters {
      export type SwitchCommand = /* SwitchCommand */ FileManage.SwitchCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace SetPermissionUsingPOST {
    export interface BodyParameters {
      updatePermissionCommand: Parameters.UpdatePermissionCommand;
    }
    namespace Parameters {
      export type UpdatePermissionCommand = /* UpdatePermissionCommand */ FileManage.UpdatePermissionCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace SortUsingPOST {
    export interface BodyParameters {
      movingDirectoryDTO: Parameters.MovingDirectoryDTO;
    }
    namespace Parameters {
      export type MovingDirectoryDTO = /* MovingDirectoryDTO */ FileManage.MovingDirectoryDTO;
    }
    namespace Responses {
      export type $200 = /* ResponseData«DirectoryDTO» */ FileManage.ResponseDataDirectoryDTO;
    }
  }
  namespace StatisticDetailUsingPOST {
    export interface BodyParameters {
      queryDTO: Parameters.QueryDTO;
    }
    namespace Parameters {
      export type QueryDTO = /* SummaryQueryDTO */ FileManage.SummaryQueryDTO;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«FileOptSummary»» */ FileManage.ResponseDataPageRespDTOFileOptSummary;
    }
  }
  namespace StatisticSummaryUsingGET {
    namespace Parameters {
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«FileSummaryDTO» */ FileManage.ResponseDataFileSummaryDTO;
    }
  }
  namespace StoreInCompleteListUsingPOST {
    export interface BodyParameters {
      storeDetail: Parameters.StoreDetail;
    }
    namespace Parameters {
      export type StoreDetail = /* StoreDetailPageQryDTO */ FileManage.StoreDetailPageQryDTO;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«StoreDetailPageRespDTO»» */ FileManage.ResponseDataPageRespDTOStoreDetailPageRespDTO;
    }
  }
  namespace StorePreviewDownLoadQueryUsingPOST {
    export interface BodyParameters {
      query: Parameters.Query;
    }
    namespace Parameters {
      export type Query = /* PreviewDownloadQryDTO */ FileManage.PreviewDownloadQryDTO;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«PreviewDownloadDTO»» */ FileManage.ResponseDataPageRespDTOPreviewDownloadDTO;
    }
  }
  namespace StorePreviewDownloadExportUsingPOST {
    export interface BodyParameters {
      query: Parameters.Query;
    }
    namespace Parameters {
      export type Query = /* PreviewDownloadQryDTO */ FileManage.PreviewDownloadQryDTO;
    }
  }
  namespace SupplierInCompleteListUsingPOST {
    export interface BodyParameters {
      supplierDetail: Parameters.SupplierDetail;
    }
    namespace Parameters {
      export type SupplierDetail = /* SupplierDetailPageQryDTO */ FileManage.SupplierDetailPageQryDTO;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«SupplierDetailPageRespDTO»» */ FileManage.ResponseDataPageRespDTOSupplierDetailPageRespDTO;
    }
  }
  namespace SupplierPreviewDownLoadQueryUsingPOST {
    export interface BodyParameters {
      query: Parameters.Query;
    }
    namespace Parameters {
      export type Query = /* PreviewDownloadQryDTO */ FileManage.PreviewDownloadQryDTO;
    }
    namespace Responses {
      export type $200 =
        /* ResponseData«PageRespDTO«PreviewDownloadDTO»» */ FileManage.ResponseDataPageRespDTOPreviewDownloadDTO;
    }
  }
  namespace SupplierPreviewDownloadExportUsingPOST {
    export interface BodyParameters {
      query: Parameters.Query;
    }
    namespace Parameters {
      export type Query = /* PreviewDownloadQryDTO */ FileManage.PreviewDownloadQryDTO;
    }
  }
  namespace UpdateDirectoryNameUsingPOST {
    export interface BodyParameters {
      updateNodeCommand: Parameters.UpdateNodeCommand;
    }
    namespace Parameters {
      export type UpdateNodeCommand = /* UpdateNodeCommand */ FileManage.UpdateNodeCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData */ FileManage.ResponseData;
    }
  }
  namespace UpdateFileStatusUsingPOST {
    export interface BodyParameters {
      updateFileItemStateCommand: Parameters.UpdateFileItemStateCommand;
    }
    namespace Parameters {
      export type UpdateFileItemStateCommand = /* UpdateFileItemStateCommand */ FileManage.UpdateFileItemStateCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
  namespace UploadFilesUsingPOST {
    export interface FormDataParameters {
      files: /* files */ Parameters.Files;
    }
    namespace Parameters {
      /**
       * files
       */
      export type Files = unknown[];
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«OSSRecordVO» */ FileManage.ResponseDataOSSRecordVO;
    }
  }
  namespace UploadUsingPOST {
    export interface FormDataParameters {
      file: /* file */ Parameters.File;
    }
    namespace Parameters {
      /**
       * file
       */
      export type File = unknown;
      /**
       * id
       */
      export type Id = number; // int64
    }
    export interface QueryParameters {
      id: /* id */ Parameters.Id /* int64 */;
    }
    namespace Responses {
      export type $200 = /* ResponseData«OSSRecordVO» */ FileManage.ResponseDataOSSRecordVO;
    }
  }
  namespace WithdrawUsingPOST {
    export interface BodyParameters {
      updateFileItemStateCommand: Parameters.UpdateFileItemStateCommand;
    }
    namespace Parameters {
      export type UpdateFileItemStateCommand = /* UpdateFileItemStateCommand */ FileManage.UpdateFileItemStateCommand;
    }
    namespace Responses {
      export type $200 = /* ResponseData«object» */ FileManage.ResponseDataObject;
    }
  }
}
