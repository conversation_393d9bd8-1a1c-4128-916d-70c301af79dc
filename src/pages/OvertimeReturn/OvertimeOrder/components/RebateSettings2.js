import React from 'react'
import { Modal, Form, InputNumber, message ,Spin} from 'antd'
import moment from 'moment'
import allUrl from '@/utils/url'
import { post } from '@/utils/request'
const RebateSettings = (props) => {
    const { visible, title, onCancel, cb, selectedRows } = props
    const [form] = Form.useForm();
    const [loading,setLoading] = React.useState(false)
    const handleOK = () => {
        form.validateFields().then(values => {
            let list = []
            selectedRows.forEach(item => {
                list.push({
                    orderNumber: item.orderNumber,
                    orderState: item.orderState,
                    rebateAmount: item.rebateAmount,
                    addMoney: values.addMoney
                })
            })
            console.log(list)
            setLoading(true)
            post(allUrl.orderTimeOut.OvertimeOrderAdds,{list}).then(res=>{
                if(res && res.success){
                    onCancel()
                    cb()
                    message.success(res.msg)
                }else{
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        })
    }
    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };
    return (
        <>
            <Modal
                visible={visible}
                title={title}
                onOk={() => handleOK()}
                onCancel={onCancel}
                maskClosable={false}
                centered={true}
                keyboard={false}
                confirmLoading={loading}
            >
                <Spin spinning={loading}>
                    <Form
                        {...layout}
                        form={form}
                    >
                        <Form.Item
                            label='金额'
                            name="addMoney"
                            rules={[{ required: true, message: '请填写金额！' }]}
                            initialValue={200}
                        >
                            <InputNumber autoComplete="off" min={1} style={{ width: '100%' }} />
                        </Form.Item>
                        <Form.Item
                            label='类型'
                            rules={[{ required: false, message: '请填写类型！' }]}
                            initialValue={'补贴'}
                        >
                            补贴
                        </Form.Item>
                        <Form.Item
                            label='返利时间'
                            rules={[{ required: false, message: '请填写返利时间！' }]}
                            initialValue={moment(new Date())}
                        >
                            {
                                moment(new Date()).format('YYYY-MM-DD hh:mm:ss')
                            }
                        </Form.Item>
                    </Form>
                </Spin>
            </Modal>
        </>
    )
}
export default RebateSettings