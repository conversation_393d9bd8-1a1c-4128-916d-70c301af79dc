import React, { useState,memo, useEffect} from 'react'
import { Form, Button, Input, Select, Row, Col ,DatePicker} from 'antd'
import {UpOutlined,DownOutlined} from '@ant-design/icons';
const { Option } = Select
const { RangePicker } = DatePicker;

const FormQuery = (props) => {
    const {onSearch,searchList,isFormDown} = props
    const [from] = Form.useForm()
    const [formDown,ChangeFormDown] = useState(isFormDown?isFormDown:false)
    const [searchFormList,changeSearchList] = useState([])
    const [isPutItAway,changeIsPutItAway] = useState(true)
    const handleSearch = () =>{
        from.validateFields().then(values => {
            for(let i in values){
                if(!values[i]){
                    values[i] = ''
                }
                console.log(i)
                if(!i){
                    delete values[i]
                }
            }
            onSearch(values)
        })
    }
    const onReset = () =>{
        from.resetFields()
        onSearch()
    }
    const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
    };
    useEffect(()=>{
        let newSearchList = [...searchList]
        newSearchList.forEach(item=>{
            item.onPressEnter = handleSearch
        })
        let colSpanNum=newSearchList.reduce((num,item)=>num+item.colSpan,0);
        let ColNum = 24 * Math.ceil(colSpanNum/24) - colSpanNum -  6
        if(colSpanNum <= 18){
            changeIsPutItAway(false)
            if(ColNum  === 6){
                newSearchList.push({colSpan:6})
            }else if(ColNum  === 12){
                newSearchList.push({colSpan:12})
            }
        }else{
            if(ColNum <0){
                newSearchList.push({colSpan:18})
            }else if(ColNum  === 12){
                newSearchList.push({colSpan:12})
            }else if(ColNum  === 6){
                newSearchList.push({colSpan:6})
            }
        }
        changeSearchList(newSearchList)
    },[searchList])
    return (
        <>
            <Form className='PublicList_FormQuery' form={from} {...layout}>
                <Row style={{paddingRight:'20px'}}>
                    {
                        searchFormList.map((item,index)=>{
                            return ( index <=2 &&
                            <Col span={item.colSpan} key={index}>
                                <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []} initialValue={item.initialValue || null}>
                                    {
                                        item.type === 'Input'?
                                        <Input placeholder={item.placeholder || '请输入...'} allowClear={item?.allowClear === false ?false :true} onPressEnter={item.onPressEnter} />
                                        :item.type === 'Select' ?
                                        <Select placeholder={item.placeholder || '请选择...'} allowClear={item?.allowClear === false ?false :true} mode={item.mode ?item.mode: ''} showSearch={item.showSearch?item.showSearch:false} optionFilterProp='children' filterOption={(input, option) =>
                                            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                          }>
                                            {
                                                item.data.map((ele,i)=>{
                                                    return <Option key={i} value={ele.value}>{ele.name}</Option>
                                                })
                                            }
                                        </Select>
                                        :item.type === 'RangePicker' ?
                                        <RangePicker placeholder={item.placeholder} />
                                        :null
                                    }
                                </Form.Item>
                            </Col>)
                        })
                    }
                    {
                        searchFormList.map((item,index)=>{
                            return ( index >2 && formDown &&
                            <Col span={item.colSpan} key={index}>
                                <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []} initialValue={item.initialValue || null}>
                                    {
                                        item.type === 'Input'?
                                        <Input placeholder={item.placeholder || '请输入...'} allowClear={item?.allowClear === false ?false :true} onPressEnter={item.onPressEnter} />
                                        :item.type === 'Select' ?
                                        <Select placeholder={item.placeholder || '请选择...'} allowClear={item?.allowClear === false ?false :true}>
                                            {
                                                item.data.map((ele,i)=>{
                                                    return <Option key={i} value={ele.value}>{ele.name}</Option>
                                                })
                                            }
                                        </Select>
                                        :item.type === 'RangePicker' ?
                                        <RangePicker placeholder={item.placeholder} />
                                        :null
                                    }
                                </Form.Item>
                            </Col>)
                        })
                    }
                    <Col span={6} className='FormQuerySubmit'>
                        <Col span={5} className='Reset'>
                            <Button onClick={onReset}>重置</Button>
                        </Col>
                        <Col span={6} className='Search'>
                            <Button type='primary' onClick={handleSearch}>查询</Button>
                        </Col>
                        {
                            isPutItAway?
                            <Col span={6} className='operationButtons'>
                                <Form.Item>
                                    {
                                        formDown ?
                                        <span onClick={()=>{
                                            ChangeFormDown(false)
                                            }}>
                                            <span>收起</span><UpOutlined />
                                        </span>
                                        :<span onClick={()=>{
                                            ChangeFormDown(true)
                                        }}>
                                            <span>展开</span><DownOutlined />
                                        </span>
                                    }
                                </Form.Item>
                            </Col>:null
                        }
                    </Col>
                </Row>
            </Form>
        </>
    )
}
export default memo(FormQuery)