.CustomerKPI {
    
    .tableData{
        background-color: white;
        // padding: 24px 24px 72px 24px;

        border-top: solid 24px #f0f2f5;
        border-right: solid 24px #f0f2f5;
        border-bottom: solid 24px #f0f2f5;
        border-left: solid 24px #f0f2f5;
        .ant-table-wrapper{
            // background-color: white;
            .ant-table{
                padding: 24px;
                .ant-table-container{
                    border: 0;
                    .ant-table-content{
                        // overflow: auto hidden;
                        .ant-table-thead{
                            tr>th,tr>td{
                                // border: 0;
                            }
                        }
                        .ant-table-tbody{
                            .ant-table-cell{
                                .isReceivedWrapper{
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                    .ant-table-pagination{
                        margin: 16px 24px;
                    }
                }
            }
        }
        .tableTitle{
            padding: 24px 24px 0px 32px;
            justify-content: space-between;
            .text{
                font-size: 20px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.85);
                line-height: 28px;
            }
            .bts{
                .ant-btn{
                    margin:0 7px;
                }
            }
        }
    }
    .ant-pagination{
        padding-right: 20px;
    }
    .PublicList_FormQuery{
        padding-top: 30px;
        padding-left: 24px;
        .ant-col-7{
            .ant-form-item{
                .ant-form-item-control-input{
                    width: 90%;
                    .ant-form-item-control-input-content{
                        .ant-picker{
                            width: 100%;
                        }
                    }
                }
            }
        }
        .FormQuerySubmit{
            display:flex;
            justify-content: flex-end;
            .operationButtons{
                span{
                    color: #1890ff;
                    cursor: pointer;
                    .anticon{
                        margin-left: 6px;
                    }
                }
            }
        }
    }
    .static-data {
        padding: 32px 0 24px 0;
        .item {
            display: flex;
            margin-bottom: 24px;
            .total {
                width: 20%;
                text-align: center;
                .data-text {
                    font-size: 16px;
                }
                .num {
                    font-size: 24px;
                    color: #333;
                    font-weight: 600;
                }
            }
            .session-num {
                width: 40%;
                padding-left: 80px;
                font-size: 14px;
                .s-bottom {
                    margin-top: 8px;
                }
            }
            
        }
    }
    
}