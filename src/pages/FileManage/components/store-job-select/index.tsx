import { Select, SelectProps } from 'antd';
import React, { FC } from 'react';
import { useSupplierJobs } from '../../hooks';

const SupplierJobSelect: FC<Omit<SelectProps<string, FileManage.SupplierPost>, 'options'>> = (props) => {
  const { data, loading } = useSupplierJobs();
  return <Select loading={loading} {...props} options={data} fieldNames={{ value: 'code', label: 'name' }} />;
};

export default SupplierJobSelect;
