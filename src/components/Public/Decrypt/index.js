import CryptoJS from 'crypto-js'
//解密
export const DecryptByAES = (word, key, iv) => {
    try{
        let encryptedHexStr = CryptoJS.enc.Hex.parse(word);
        let keyHex = CryptoJS.enc.Utf8.parse(key ? key :'5566778899ABCDEF');  
        let ivHex  = CryptoJS.enc.Utf8.parse(iv ? iv :'ABCDEF5566778899');  
        let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
        let decrypt = CryptoJS.AES.decrypt(srcs, keyHex, { iv: ivHex, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
        let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
        return decryptedStr.toString();
    }catch(error){
        return ''
    }
}