import React from 'react'
import { message, Button, Input, Row, Col, Form, Checkbox, Tree,Divider,Spin ,Select} from 'antd'
import { EditOutlined, PlusOutlined, CheckOutlined } from '@ant-design/icons';
import { connect } from 'react-redux'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import {GetUserInfo} from '@/actions/async'
import { get, post } from '../../../utils/request'
import allUrl from '../../../utils/url'
import OrgAdd from '../OrgManagement/Modal/OrgAdd'
import OrgEdit from '../OrgManagement/Modal/OrgEdit'
import '../index.less'
import './Detail.less'
import {roleJudgment} from '@/utils/authority'
import {utilsDict} from '@/utils/utilsDict'

const {Option} = Select
class Detail extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            loading: false,
            locationParmas: {},
            kjym: [],
            treeData: [],
            treeselectedKeys: [],
            treeSelectedRows: [],
            orgtreeselectedKeys: [],
            orgtreeSelectedRows: [],
            permissionIds: [],
            Type: '',
            formData: {},
            OrgtreeData:[],
            SelectedOrg:[],
            OrgAddVisible:false,
            OrgEditVisible:false,
            EditNodeData:[],
            checkedKeys:[],
            orgInputText:''
        }
        this.id = 0
    }
    formRef = React.createRef();
    Save = () => {
        const { handleDrawerCancel, handleDrawerCB } = this.props
        const { permissionIds,SelectedOrg,orgtreeselectedKeys } = this.state
        this.formRef.current.validateFields()
            .then(values => {
                console.log(values)
                let arr = []
                SelectedOrg.forEach(item => {
                    arr.push(item.key)
                })
                let data = {
                    id: this.id,
                    roleName: values.roleName,
                    roleCode: values.roleCode ? values.roleCode : '',
                    description: values.description,
                    permissionIds:[...new Set(permissionIds)],
                    organizationIds:orgtreeselectedKeys,
                    type:values.type || ''
                }
                console.log('参数为：', data)

                if (this.id) {
                    this.setState({loading:true},()=>{
                        post(allUrl.Authority.updateRole, data).then(res => {
                            if (res.success) {
                                message.success(res.msg || '修改成功！')
                                if (handleDrawerCancel) {
                                    handleDrawerCancel()
                                    handleDrawerCB()
                                    this.props.dispatch(GetUserInfo())
                                } else {
                                    history.goBack()
                                }
                            } else {
                                // message.error(res.msg || '修改失败！')
                            }
                            this.setState({loading:false})
                        })
                    })
                } else {
                    this.setState({loading:true},()=>{
                        post(allUrl.Authority.createRole, data).then(res => {
                            if (res.success) {
                                message.success(res.msg || '新增成功！')
                                if (handleDrawerCancel) {
                                    handleDrawerCancel()
                                    handleDrawerCB()
                                    this.props.dispatch(GetUserInfo())
                                } else {
                                    history.goBack()
                                }
                            } else {
                                // message.error(res.msg || '新增失败！')
                            }
                            this.setState({loading:false})
                        })
                    })
                }

            })
            .catch(error => {
                console.log(error)
            })
    }
    onCancel = () => {
        const { handleDrawerCancel } = this.props
        if (handleDrawerCancel) {
            handleDrawerCancel()
        } else {
            history.goBack()
        }
    }
    setMenuID = (arr) =>{
        const {treeData} = this.state
        // console.log(arr,treeData)
        let newArr = arr
        let fn = (newtreeData) =>{
            newtreeData.forEach(item=>{
                if(item.children && item.children.length){
                    fn(item.children)
                }else{
                    if(item.functionChildren && item.functionChildren.length){
                        let num = 0
                        item.functionChildren.forEach(ele=>{
                            // console.log(ele)
                            if(newArr.indexOf(ele.key)<=-1){
                                // console.log(newArr,item.key)
                                num+=1
                            }
                        })
                        if(num === item.functionChildren.length){
                            // console.log('删除唯一的父级ID')
                            let index = newArr.indexOf(item.key)
                            // console.log(index,'下标')
                            if(index>=0){
                                console.log(newArr,index)
                                newArr.splice(index,1)
                            }
                            num = 0
                        }
                    }
                }
            })
        }
        fn(treeData)
        return newArr
    }
    CheckboxChange = (e, row) => {
        let { permissionIds } = this.state
        if (e.target.checked) {
            if (permissionIds.indexOf(e.target.value) > -1) { //存在
                // permissionIds.remove(e.target.value)
            } else {  //不存在
                permissionIds.push(e.target.value)
                permissionIds.push(row.parentId)
            }
        } else {
            permissionIds = permissionIds.filter(item => item !== e.target.value)
            permissionIds = [...new Set(permissionIds)]
            permissionIds = this.setMenuID(permissionIds)

            // let temp = permissionIds.filter(item => item === row.parentId)
            // console.log(temp)
            // let index = permissionIds.indexOf(row.parentId)
            // console.log(index)
            // permissionIds.splice(index,1)

            // permissionIds = permissionIds.filter(item => item !== row.parentId)
        }
        this.setState({ permissionIds },()=>{
            console.log(this.state.permissionIds)
        })
    }

  

    treeSelect = (selectedKeys, selectedNodes, event) => {
        console.log('22222')
        // console.log(selectedKeys, selectedNodes)
        this.setState({
            treeselectedKeys: selectedKeys,
            treeSelectedRows: selectedNodes.selectedNodes,
            kjym: selectedNodes.node.functionChildren
        })
    }

    updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }

            if (node.children) {
                return { ...node, children: this.updateTreeData(node.children, key, children) };
            }

            return node;
        });
    }
    treeTitleClick = (nodeData) =>{
        // console.log(nodeData)
        const {orgtreeselectedKeys,orgtreeSelectedRows} = this.state
        if(orgtreeselectedKeys.indexOf(nodeData.key) >-1){  //取消
            let index = orgtreeselectedKeys.indexOf(nodeData.key)
            orgtreeSelectedRows.splice(index,1)
            orgtreeselectedKeys.splice(index,1)
        }else{  //勾选
            orgtreeselectedKeys.push(nodeData.key)
            orgtreeSelectedRows.push(nodeData)
        }
        this.setState({orgtreeselectedKeys,orgtreeSelectedRows})
    }

    treeTitleRender = (nodeData) => {
        const {orgtreeselectedKeys} = this.state
        let AddTreeData =  sessionStorage.getItem('AddOrgTreeData') ? JSON.parse(sessionStorage.getItem('AddOrgTreeData')):[]
        return <>
            <div style={{ marginRight: '40px', flex: 1 }} onClick={()=>this.treeTitleClick(nodeData)}><Checkbox checked={orgtreeselectedKeys.indexOf(nodeData.key)>-1?true:false}>{nodeData.title}</Checkbox></div>
            {
                AddTreeData.indexOf(nodeData.key) !== -1 &&
                <div style={{ marginRight: '10px' }} onClick={() => this.setState({ OrgEditVisible: true ,EditNodeData:[nodeData] })} title='编辑'><EditOutlined /></div>
            }
        </>
    }
    orgtreeSelect = (selectedKeys, selectedNodes, event) => {
        let selectRows = this.state.orgtreeSelectedRows
        selectRows = [...selectRows,...selectedNodes.selectedNodes]
        selectRows = this.deWeight(selectRows,'key')
        let arr = []
        selectRows.forEach(item=>{
            if(selectedKeys.indexOf(item.key)>-1){
                arr.push(item)
            }
        })
        console.log(selectRows)
        console.log(selectedKeys,selectedNodes)
        this.setState({ orgtreeselectedKeys: selectedKeys, orgtreeSelectedRows: arr })
    }
    selectOrg = () => {
        let { orgtreeSelectedRows, SelectedOrg } = this.state
        SelectedOrg.push(...orgtreeSelectedRows)
        SelectedOrg = this.deWeight(SelectedOrg,'key')
        this.setState({ SelectedOrg: SelectedOrg })
    }
    deWeight = (arr,field) => {
        for (var i = 0; i < arr.length - 1; i++) {
            for (var j = i + 1; j < arr.length; j++) {
                if (Number(arr[i][field]) === Number(arr[j][field])) {
                    arr.splice(j, 1);
                    //因为数组长度减小1，所以直接 j++ 会漏掉一个元素，所以要 j--
                    j--;
                }
            }
        }
        return arr;
    }
    SelectedOrgDel = (row) => {
        let { SelectedOrg } = this.state
        let arr = SelectedOrg.filter(item => item.key !== row.key)
        this.setState({ SelectedOrg: arr })
    }
    getOrg = (data) => {
        let AddTreeData =  sessionStorage.getItem('AddOrgTreeData') ? JSON.parse(sessionStorage.getItem('AddOrgTreeData')):[]
        if(data){
            let arr = []
            data.forEach(item=>{
                arr.push(item.id)
            })
            AddTreeData = [...AddTreeData, ...arr]
            sessionStorage.setItem('AddOrgTreeData',JSON.stringify(AddTreeData))
        }
    }

    orgSearch = () =>{
        this.GetOrgData()
    }
    orgReset = () =>{
        this.setState({orgInputText:''},()=>{
            this.GetOrgData()
        })
    }

    render() {
        const { locationParmas, kjym, loading,treeData, permissionIds, Type,orgtreeselectedKeys, formData,OrgtreeData ,orgtreeSelectedRows,SelectedOrg,OrgAddVisible,OrgEditVisible,EditNodeData,treeSelectedRows} = this.state
        const {userInfo} = this.props
        const formItemLayout = {
            labelCol: { span: 14 },
            wrapperCol: { span: 14 },
        }
        // console.log('111',orgtreeselectedKeys,orgtreeSelectedRows)
        // console.log('222',OrgtreeData)
        return (
            <Spin spinning={loading}>
                {
            Type === 'LookAt' ?
                <div className='PublicDetail'>
                    <div className='DetailBox'>
                        {
                            locationParmas.title &&
                            <div className='DetailTitle'>{locationParmas.title}</div>
                        }
                        <div className='DetailCon'>
                            <Form
                                {...formItemLayout}
                                layout={'vertical'}
                                ref={this.formRef}
                                initialValues={{ remember: true, }}
                            >
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>角色信息</p>
                                    <Row className='FormRowCon'>
                                        <Col span={8}>
                                            <Form.Item label="角色名称">
                                                {formData ? formData.roleName : ''}
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item label="角色分类">
                                                {utilsDict('roleType',formData?.type)}
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item label="角色描述" name="description">
                                                {formData ? formData.description : ''}
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </div>
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>所属组织</p>
                                    <Row className='FormRowCon'>
                                        <Col className='orgTreeSelected' style={{paddingLeft:0}}>
                                            {/* <p className='orgTreeSelectedTitle'>该账号所属组织</p> */}
                                            <div className='orgTreeSelectedCon'>
                                                {
                                                    SelectedOrg.map((item, index) => {
                                                        return <span key={index}>
                                                            {item.title}
                                                            {
                                                                index !== SelectedOrg.length-1 &&
                                                                <Divider type="vertical" style={{margin:'0 30px'}} />
                                                            }
                                                            {/* <span className='orgTreeSelectedConItemTitle'></span> */}
                                                            {/* <div className='orgTreeSelectedConItem_Clear' onClick={() => this.SelectedOrgDel(item)}>删除</div> */}
                                                        </span>
                                                    })
                                                }
                                            </div>
                                        </Col>
                                    </Row>
                                </div>
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>功能权限</p>
                                    <div className='KJYM'>
                                        <Row className='KJYM_Title'>
                                            <Col span={8}>可见页面</Col>
                                            <Col>功能权限</Col>
                                        </Row>

                                        <Row className='KJYM_Con'>
                                            <Col span={8} className='KJYM_ConLeft'>
                                                <Tree defaultExpandedKeys={['0']} treeData={treeData} onSelect={this.treeSelect} />
                                            </Col>
                                            <Col span={16} className='KJYM_ConRight'>
                                                <Row>
                                                    {
                                                        kjym && kjym.length ?
                                                            kjym.map((item, index) => {
                                                                return (
                                                                    <Col span={4} key={index} style={{display:permissionIds.indexOf(item.key) > -1?'block':'none'}}>
                                                                        {
                                                                            permissionIds.indexOf(item.key) > -1 ? item.title : ''
                                                                        }
                                                                        {/* <Checkbox value={item.key} checked={permissionIds.indexOf(item.key) > -1 ?true:false} onChange={(e) => this.CheckboxChange(e, item)}>{item.title}</Checkbox> */}
                                                                    </Col>
                                                                )
                                                            }) : '请先选择可见页面！'
                                                    }
                                                </Row>
                                            </Col>
                                        </Row>
                                    </div>
                                </div>
                            </Form>
                        </div>
                    </div>
                    <div className='DetailBtns'>
                        {/* <Button type="primary" onClick={this.Save} style={{ marginRight: '10px' }} onClick={() => this.Save()}>确定</Button> */}
                        <Button onClick={this.onCancel}>取消</Button>
                    </div>
                </div> :
                <div className='PublicDetail RoleManangMent'>
                    <div className='DetailBox'>
                        {
                            locationParmas.title &&
                            <div className='DetailTitle'>{locationParmas.title}</div>
                        }
                        <div className='DetailCon'>
                            <Form
                                {...formItemLayout}
                                layout={'vertical'}
                                ref={this.formRef}
                                initialValues={{ remember: true, }}
                            >
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>角色信息</p>
                                    <Row className='FormRowCon'>
                                        <Col span={8}>
                                            <Form.Item label="角色名称" name="roleName" rules={[{ required: true, message: '请输入角色名称！', }]}>
                                                <Input placeholder="请输入..." />
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                        <Form.Item label="角色分类" name="type" rules={[{ required: false, message: '请选择角色分类！', }]}>
                                            <Select allowClear placeholder='请选择'>
                                                <Option value={'1'}>门店</Option>
                                                <Option value={'3'}>厂端</Option>
                                                <Option value={'2'}>其他</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                        <Col span={8}>
                                            <Form.Item label="角色描述" name="description" rules={[{ required: false, message: '请输入角色描述！', }]}>
                                                <Input placeholder="请输入..." />
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </div>
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>所属组织</p>
                                    <Row style={{margin:'20px 0 0 30px'}}>
                                        <Col>
                                            <span>关键字：</span><Input placeholder='搜组织名称关键字！' style={{width:300}} allowClear onChange={e=>this.setState({orgInputText:e.target.value})} onPressEnter={this.orgSearch} />
                                        </Col>
                                        <Col style={{marginLeft:20}}>
                                            <Button type='primary' onClick={this.orgSearch}>查询</Button>
                                            <Button style={{marginLeft:20}} onClick={this.orgReset}>重置</Button>
                                        </Col>
                                    </Row>
                                    <Row className='FormRowCon'>
                                        <Col span={10} className='orgTreeBox'>
                                            {
                                                OrgtreeData && OrgtreeData.length ?
                                                <Tree multiple={true} defaultSelectedKeys={orgtreeselectedKeys} defaultExpandedKeys={[1]} treeData={OrgtreeData} titleRender={this.treeTitleRender} /> 
                                                :'暂无数据'
                                            }
                                            <div className='orgTreeBoxBtns'>
                                                {/* <Button icon={<CheckOutlined />} disabled={!orgtreeSelectedRows.length} onClick={() => this.selectOrg()}>选择组织</Button> */}
                                                {/* {
                                                    roleJudgment(userInfo,'PERM_ORGANIZATION_ADD') ?
                                                    <Button icon={<PlusOutlined />} type="primary" onClick={() => this.setState({ OrgAddVisible: true })}>添加组织</Button>
                                                    :null
                                                } */}
                                            </div>
                                        </Col>
                                        <Col span={14} className='orgTreeSelected'>
                                            <p className='orgTreeSelectedTitle'>该角色所属组织</p>
                                            <div className='orgTreeSelectedCon' style={{height:375,overflowY:'auto'}}>
                                                {
                                                    orgtreeSelectedRows.map((item, index) => {
                                                        return <div className='orgTreeSelectedConItem' key={index}>
                                                            <div className='orgTreeSelectedConItemTitle'>{item.title}</div>
                                                            {/* <div className='orgTreeSelectedConItem_Clear' onClick={() => this.SelectedOrgDel(item)}>删除</div> */}
                                                        </div>
                                                    })
                                                }
                                            </div>
                                        </Col>
                                    </Row>
                                </div>
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>功能权限</p>
                                    <div className='KJYM'>
                                        <Row className='KJYM_Title'>
                                            <Col span={8}>可见页面</Col>
                                            <Col>功能权限</Col>
                                        </Row>
                                        <Row className='KJYM_Con'>
                                            <Col span={8} className='KJYM_ConLeft'>
                                                <Tree defaultExpandedKeys={['0']} treeData={treeData} onSelect={this.treeSelect} />
                                            </Col>
                                            <Col span={16} className='KJYM_ConRight'>
                                                <Row>
                                                    {
                                                        treeSelectedRows.length && treeSelectedRows[0].remark ? 
                                                        <Col span={24} style={{marginBottom:'20px'}}>
                                                            <span style={{color:'red'}}>备注：</span>{treeSelectedRows[0].remark}
                                                        </Col>:null
                                                    }
                                                    {
                                                        kjym && kjym.length ?
                                                            kjym.map((item, index) => {
                                                                return (
                                                                    <Col span={6} key={index}>
                                                                        <Checkbox value={item.key} checked={permissionIds.indexOf(item.key) > -1 ? true : false} onChange={(e) => this.CheckboxChange(e, item)}>{item.title}</Checkbox>
                                                                    </Col>
                                                                )
                                                            }) : '请先选择可见页面！'
                                                    }
                                                </Row>
                                            </Col>
                                        </Row>
                                    </div>
                                </div>
                            </Form>
                        </div>
                    </div>
                    <div className='DetailBtns'>
                        <Button loading={loading} type="primary" onClick={this.Save} style={{ marginRight: '10px' }}>确定</Button>
                        <Button loading={loading} onClick={this.onCancel}>取消</Button>
                    </div>
                    {
                        OrgAddVisible &&
                        <OrgAdd visible={OrgAddVisible} title='添加组织' GetData={this.GetOrgData} cb={this.getOrg} onCancel={() => this.setState({ OrgAddVisible: false })} onOk={() => this.setState({ OrgAddVisible: false })} />
                    }
                    {
                        OrgEditVisible &&
                        <OrgEdit title={'编辑组织'} visible={OrgEditVisible} treeSelectedRows={EditNodeData} GetData={this.GetOrgData} onCancel={() => this.setState({ OrgEditVisible: false })}
                        />
                    }
                </div>
            }
            </Spin>
        )
    }
    componentDidMount() {
        if (!this.props.isDrawer) {
            let locationParmas = this.props.match.params.data ? JSON.parse(DecryptByAES(this.props.match.params.data)) : {}
            this.id = locationParmas.id
            // console.log(locationParmas)
            this.setState({ locationParmas, Type: locationParmas.type }, () => {
                this.getMenuList()
                this.GetOrgData()
                if (locationParmas.id) {
                    this.getData(locationParmas.id)
                }
            })
        } else {
            this.getMenuList()
            this.GetOrgData()
        }
    }

    getMenuList = () => {
        get(allUrl.Authority.MenuTreeList).then(res => {
            if (res.success) {
                this.setState({
                    treeData: res.resp,
                    treeselectedKeys: [],
                    treeSelectedRows: [],
                })
            } else {
                // message.error(res.msg || '获取菜单列表失败！')
            }
        })
    }
    getData = (id) => {
        this.setState({loading:true},()=>{
            get(allUrl.Authority.detailRole + '/' + id).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    let orgtreeselectedKeys = []
                    if(Dt.organizations && Dt.organizations.length){
                        Dt.organizations.forEach(item => {
                            item.title = item.name
                            item.key = item.id
                            orgtreeselectedKeys.push(item.id)
                        })
                    }
                    console.log( Dt.organizations,'111')
                    this.setState({
                        formData: Dt,
                        permissionIds: Dt.permissionIds ? Dt.permissionIds :[],
                        SelectedOrg:Dt.organizations ? Dt.organizations : [],
                        orgtreeSelectedRows:Dt.organizations ? Dt.organizations : [],
                        orgtreeselectedKeys
                    }, () => {
                        this.formRef.current.setFieldsValue({
                            roleName: Dt.roleName ? Dt.roleName :'',
                            type: Dt.type ? String(Dt.type) :null,
                            // roleCode: Dt.roleCode,
                            description: Dt.description ? Dt.description:''
                        })
                    })
                } else {
                    // message.error(res.msg || '获取用户详情失败！')
                }
                this.setState({loading:false})
            })
        })
    }
    GetOrgData = () => {
        const {orgInputText} = this.state
        post(allUrl.Authority.orgPageList,{name:orgInputText}).then(res => {
            if (res.success) {
                this.setState({ OrgtreeData: res.resp })
            } else {
                // message.error(res.msg)
            }
        })
    }
}
export default connect(({ common }) => {
    const {  userInfo } = common
    return {
         userInfo
    }
})(Detail)