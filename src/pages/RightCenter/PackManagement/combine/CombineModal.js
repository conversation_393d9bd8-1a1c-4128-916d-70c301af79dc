import React, { useState, memo, useEffect, useRef } from 'react';
import {
  Modal,
  Form,
  message,
  Row,
  Col,
  Descriptions,
  Spin,
  Input,
  Radio,
  InputNumber,
  Space,
  Select,
  DatePicker,
  Button,
} from 'antd';
import { getWidth, offsetLeft } from '@/utils/index';
import allUrl from '../../../../utils/url';
import { get } from '@/utils/request';
import moment from 'moment';
import CreateStrategyCom from '../../DetailManagement/CreateStrategyCom';
import style from './combine.module.less';
import styles from '../../antd.module.less';
import PublicTable from '../PublicTable';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { confirm } = Modal;
const CombineModal = props => {
  const { handleCancel, visible, tableList, combineRecord, setGroupDetails, groupDetails, allGroupDetailNos } = props;
  const [entryList, setEntryList] = useState({});
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [selectedRowKeys, setSelectedRowKey] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [limitYearRadioDisplay, setLimitYearRadioDisplay] = useState(''); // 权益限制年限
  const [timeRadioChangeDisplay, setTimeRadioChangeDisplay] = useState(1); // 权益生效时间
  const [frequencyRadioDisplay, setFrequencyRadioDisplay] = useState(''); // 享有权益频次
  const [currentTableList, setCurrentTableList] = useState([]); // 筛选过后的当前组件需要显示的tableList
  const [resList, setResList] = useState([]); //当前弹窗保存时最终结果list(给接口传的最终参数)
  const [editFlag, setEditFlag] = useState(false); // 当前弹窗是否是编辑状态
  const [formObj] = Form.useForm();
  const formRef = useRef(null);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 16 },
  };

  const getEntryLists = setPaint => {
    get(allUrl.common.entryLists, { codes: 'equity_relativeTime' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }

        setEntryList(Dt || {});
      } else {
        //   message.error(res.message)
      }
    });
  };
  const PageChange = (current, pageSize) => {
    setCurrent(current);
    setPageSize(pageSize);
  };
  // 权益限制年限
  const limitYearRadioChange = value => {
    setLimitYearRadioDisplay(value);
    if (value === 0) {
      formObj.setFieldValue('timeRadio', 1);
      timeRadioChange(1);
    } else {
      formObj.setFieldValue('timeRadio', 0);
      timeRadioChange(0);
    }
  };
  // 权益生效时间
  const timeRadioChange = value => {
    console.log('timeRadioChange', value);
    setTimeRadioChangeDisplay(value);
  };
  // 享受权益频次
  const frequencyRadioChange = value => {
    setFrequencyRadioDisplay(value);
  };
  // 享有权益频次
  const _frequencyNum = Form.useWatch(`frequencyNum-frequency`, formObj);
  if (_frequencyNum) {
    formObj.setFieldValue('outsideFrequency', _frequencyNum);
  }

  const filterTableList = (list, currentInGroupList) => {
    const _listTemp = list.map(item => item.originDetail);
    // console.log('_listTemp',_listTemp.length)
    const _list = _listTemp.filter(item => {
      // console.log(currentInGroupList && currentInGroupList.detailNoList);
      // 所在组织的所有选项
      if (currentInGroupList && currentInGroupList.detailNoList?.includes(item.detailNo)) {
        return item;
      }
      // console.log(allGroupDetailNos, item);
      // 没有组合过的
      if (!allGroupDetailNos?.includes(item.detailNo)) {
        return item;
      }
    });
    setCurrentTableList(_list);
  };

  const init = data => {
    const list = data.detailNoList;
    const dataSource = data.groupDetail;
    setSelectedRowKey([...new Set(list)]);
    if (dataSource['outsideRelativeTime']) {
      dataSource['timeRadio'] = 0;
    }
    if (dataSource.outsideFixedBeginTime && dataSource.outsideFixedEndTime) {
      dataSource['timeRadio'] = 1;
    }
    // 享有权益频次
    dataSource['frequencyRadio'] = dataSource['outsideFrequency'] > 0 ? 1 : dataSource['outsideFrequency'];
    dataSource['frequencyNum-frequency'] = dataSource['outsideFrequency'] < 1 ? null : dataSource['outsideFrequency']; //解决未选其他选项时，其他选项关联的题有值
    // 权益限制年限
    limitYearRadioChange(dataSource['outsideLimitTimeFrame']);
    // 享有权益频次
    frequencyRadioChange(dataSource['frequencyRadio']);
    // 权益生效时间
    timeRadioChange(dataSource['outsideRelativeTime'] ? 0 : 1);

    formObj.setFieldsValue({
      ...dataSource,
      name: dataSource['name'],
      statement: dataSource['statement'],
      outsideLimitTimeFrame: Number(dataSource['outsideLimitTimeFrame']),
      outsideLimitYear: Number(dataSource['outsideLimitYear'] || ''),
      outsideLimitMonth: Number(dataSource['outsideLimitMonth'] || ''),
      outsideLimitDay: Number(dataSource['outsideLimitDay'] || ''),
      timeRadio: dataSource['outsideRelativeTime'] ? 0 : 1,
      outsideRelativeTime: dataSource['outsideRelativeTime'],
      fixedRelativeTime:
        !dataSource['outsideRelativeTime'] && dataSource['outsideFixedBeginTime'] && dataSource['outsideFixedEndTime']
          ? [
              moment(dataSource['outsideFixedBeginTime'], 'YYYY-MM-DD HH:mm:ss'),
              moment(dataSource['outsideFixedEndTime'], 'YYYY-MM-DD HH:mm:ss'),
            ]
          : null,
      outsideFrequency: dataSource['outsideFrequency'],
      frequency: dataSource['outsideFrequency'],
      'frequencyNum-frequency': dataSource['frequencyNum-frequency'], // 权益频次数值
    });
  };

  // console.log('combineRecord', combineRecord);
  const isHas = (obj, name) => {
    return Object.prototype.hasOwnProperty.call(obj, name);
  };

  const footerRender = () => {
    if (editFlag) {
      return [
        <Button onClick={() => handleCancel()}>关闭</Button>,
        <Button danger onClick={() => handleUngroup()}>
          取消组合
        </Button>,
        <Button type="primary" onClick={() => handleOk()}>
          保存
        </Button>,
      ];
    } else {
      return [
        <Button onClick={() => handleCancel()}>关闭</Button>,
        <Button type="primary" onClick={() => handleOk()}>
          保存
        </Button>,
      ];
    }
  };

  const handleUngroup = () => {
    confirm({
      title: '请确认是否取消组合？',
      icon: <ExclamationCircleFilled />,
      content: '取消后，已填写的组合信息将不再被保留，明细与权益包关联关系不变。',
      okText: '确认取消',
      onOk() {
        const _groupDetails = groupDetails.filter((item, index) => {
          if (item?.detailNoList?.includes(combineRecord.detailNo)) {
            return false;
          }
          return true;
        });
        setGroupDetails(_groupDetails);
        // 最后关闭弹窗
        handleCancel();
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  const handleOk = () => {
    formObj
      .validateFields()
      .then(value => {
        console.log(selectedRowKeys);

        // 转换参数
        const values = formObj.getFieldsValue();
        if (isHas(values, 'outsideLimitTimeFrame') && values.outsideLimitTimeFrame == '1') {
          if (
            Number(values.outsideLimitYear) == 0 &&
            Number(values.outsideLimitMonth) == 0 &&
            Number(values.outsideLimitDay) == 0
          ) {
            message.warn('权益限制年限不能为0年0月0日');
            return false;
          }
          if (
            isHas(values, 'outsideLimitYear') &&
            values.outsideLimitYear == undefined &&
            values.outsideLimitMonth == undefined &&
            values.outsideLimitDay == undefined
          ) {
            message.warn('权益限制年限不能为0年0月0日');
            return false;
          }
        }
        // 权益生效时间
        if (isHas(values, 'timeRadio') && values.timeRadio == '1') {
          values.outsideRelativeTime = '';
          values.fixedRelativeTime = values.fixedRelativeTime || [];
          values.outsideFixedBeginTime = values.fixedRelativeTime.length
            ? moment(values.fixedRelativeTime[0]).format('YYYY-MM-DD HH:mm:ss')
            : '';
          values.outsideFixedEndTime = values.fixedRelativeTime.length
            ? moment(values.fixedRelativeTime[1]).format('YYYY-MM-DD HH:mm:ss')
            : '';
          delete values.fixedRelativeTime;
        }

        if (isHas(values, 'frequencyRadio') && (values.frequencyRadio === undefined || values.frequencyRadio == '0')) {
          values.outsideFrequency = 0;
        }
        if (isHas(values, 'frequencyRadio') && values.frequencyRadio == '-1') {
          values.outsideFrequency = -1;
        }
        // console.log(value, values);
        // 少于2条不允许保存
        if (selectedRowKeys.length < 2) {
          message.warn('组合权益至少选择两条明细');
          return false;
        }
        // 替换 or 新增 groupDetails对象
        let isInGroup = false;
        let _groupDetails = groupDetails.map(item => {
          // 这里判断目前数组与groupDetails是否有交集
          if (item?.detailNoList?.some(item => selectedRowKeys.includes(item))) {
            isInGroup = true;
            item.detailNoList = selectedRowKeys;
            item.groupDetail = {
              // id: item.groupDetail.id,
              // groupDetailNo: item.groupDetail.groupDetailNo,
              ...item.groupDetail, // 这里将已有的参数都带过来
              ...values,
            };
          }
          return item;
        });
        // 如果没有交集（则是将当前选项 以及 当前选项所在组合 全部取消，重新选择了其他，这时当新的组合重新保存）
        if (!isInGroup) {
          _groupDetails = [...groupDetails, { detailNoList: selectedRowKeys, groupDetail: values }];
        }
        // console.log(222, groupDetails, _groupDetails);
        setGroupDetails(_groupDetails);
        // 最后关闭弹窗
        handleCancel();
      })
      .catch(errorInfo => {});
    return;
  };

  useEffect(() => {
    getEntryLists();
  }, []);

  useEffect(() => {
    setTotal(currentTableList.length);
  }, [currentTableList]);

  useEffect(() => {
    if (groupDetails && groupDetails.length) {
      let isCombineFlag = true;
      groupDetails.map(item => {
        if (item?.detailNoList?.includes(combineRecord?.detailNo)) {
          isCombineFlag = false;
          console.log('tableList111', item);
          filterTableList(tableList, item);
          init(item);
          setEditFlag(true);
        }
      });
      if (isCombineFlag) {
        console.log('tableList222');
        filterTableList(tableList);
        setSelectedRowKey([...selectedRowKeys, combineRecord.detailNo]);
        // setEditFlag(true);
      }
    } else {
      console.log('tableList222');
      filterTableList(tableList);
      setSelectedRowKey([...selectedRowKeys, combineRecord.detailNo]);
    }
  }, [tableList]);
  return (
    <Modal
      open={visible}
      // onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      width={getWidth()}
      style={{ left: offsetLeft() }}
      title="组合明细信息"
      className="detail-create"
      footer={footerRender()}
    >
      <Form {...layout} name="combine" ref={formRef} form={formObj} className={style.combineModal}>
        <div className="title-wrapper">
          <h4>组合明细</h4>
          {selectedRowKeys.length ? <span>请选择与{selectedRowKeys.join('、')}进行组合的权益明细</span> : ''}
        </div>
        <PublicTable
          className={`${styles['gray-table']} 'TablePanel'`}
          dataSource={currentTableList}
          // loading={loading}
          rowKey={'detailNo'}
          pagination={{
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
          }}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            selectedRows,
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRowKey([...new Set(selectedRowKeys)]);
              setSelectedRows(selectedRows);
            },
          }}
        />
        <div className="title-wrapper">
          <h4>组合基本信息</h4>
        </div>
        {/* 名称 */}
        <Form.Item
          label="组合明细名称"
          className="prefix-red"
          name="name"
          rules={[{ required: true, max: 100, message: '请输入填写组合名称，最多100个字，建议50字以内，不影响展示' }]}
        >
          <Input placeholder="请输入填写组合名称，最多100个字，建议50字以内，不影响展示" allowClear maxLength={100} />
        </Form.Item>
        <Form.Item
          name="statement"
          label="组合明细声明"
          rules={[]}
          help={
            <div className="custom-item-help custom-item-help-mt6">
              如配置在声明里的权益策略，例如：一次补胎、两次补漆等，不作为权益策略，均无法进行核销
            </div>
          }
        >
          <TextArea
            rows={4}
            placeholder="此处需要对外展示，建议描述时，谨慎，例如：赛力斯为您所购买的赛力斯汽车提供整车及增程器包修期限为4年不限里程（以先到者为准，运营车辆除外），详见《保修及保养手册》质量担保明细"
            allowClear
          />
        </Form.Item>

        <div className="title-wrapper">
          <h4>组合策略</h4>
        </div>
        <Form.Item label="组合限制年限" style={{ marginBottom: 0 }}>
          <Form.Item name="outsideLimitTimeFrame" style={{ display: 'inline-block', width: '300px' }} initialValue={0}>
            <Radio.Group
              onChange={e => {
                limitYearRadioChange(e.target.value);
              }}
            >
              <Radio value={0}>无此策略</Radio>
              <Radio value={-1}>不限年数</Radio>
              <Radio value={1}>其他</Radio>
            </Radio.Group>
          </Form.Item>
          <>
            <Form.Item
              name="outsideLimitYear"
              style={{ display: 'inline-block', width: '90px', marginLeft: '-40px' }}
              initialValue={0}
            >
              <InputNumber min={0} style={{ width: 90 }} disabled={limitYearRadioDisplay !== 1} />
            </Form.Item>
            <span style={{ marginLeft: 10, marginRight: 10 }}>年</span>
            <Form.Item name="outsideLimitMonth" style={{ display: 'inline-block', width: '90px' }} initialValue={0}>
              <InputNumber min={0} style={{ width: 90, lineHeight: '30px' }} disabled={limitYearRadioDisplay !== 1} />
            </Form.Item>
            <span style={{ marginLeft: 10, marginRight: 10, lineHeight: '30px' }}>月</span>
            <Form.Item name="outsideLimitDay" style={{ display: 'inline-block', width: '90px' }} initialValue={0}>
              <InputNumber min={0} style={{ width: 90 }} disabled={limitYearRadioDisplay !== 1} />
            </Form.Item>
            <span style={{ marginLeft: 10, lineHeight: '30px' }}>日</span>
          </>
        </Form.Item>
        <Form.Item
          label="组合生效时间"
          rules={[{ required: false }]}
          style={{ marginBottom: 0 }}
          className="prefix-red"
        >
          <Row>
            {/* 权益生效时间 */}
            <Col span={3}>
              {/* V13.0.4 权益限制年限选择“无此策略”，权益生效时间只可选择“固定时间”，权益限制年限选择“不限年限”或者“其他”，权益生效时间只可选择“相对时间” */}
              <Form.Item
                name="timeRadio"
                style={{ display: 'inline-block', width: '200px', marginTop: '6px' }}
                initialValue={1}
              >
                <Radio.Group onChange={e => timeRadioChange(e.target.value)}>
                  <Space direction="vertical" align="center" size={20}>
                    <Radio value={0} disabled={limitYearRadioDisplay == 0}>
                      相对时间
                    </Radio>
                    <Radio value={1} style={{ marginTop: '6px' }} disabled={limitYearRadioDisplay != 0}>
                      固定时间
                    </Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col>
              <Space direction="vertical" align="center" size={16}>
                <div style={{ position: 'relative' }}>
                  <Form.Item
                    name="outsideRelativeTime"
                    style={{ display: 'inline-block', width: '400px', margin: '0 8px' }}
                    rules={[{ required: timeRadioChangeDisplay === 0, message: '请选择权益生效时间！' }]}
                  >
                    <Select
                      style={{ display: 'inline-block', width: '380px' }}
                      allowClear
                      placeholder="请选择权益生效时间"
                      disabled={timeRadioChangeDisplay !== 0}
                    >
                      {entryList['equity_relativeTime'] && entryList['equity_relativeTime'].length
                        ? entryList['equity_relativeTime']
                            .filter(i => i.extendField1 === '')
                            .map((item, index) => {
                              if ([3, 4].includes(Number(item.value))) {
                                return (
                                  <Option key={index} value={Number(item.value)}>
                                    {item.name}
                                  </Option>
                                );
                              }
                            })
                        : null}
                    </Select>
                  </Form.Item>
                  <span style={{ color: 'red', fontSize: 12, position: 'absolute', whiteSpace: 'nowrap', top: '8px' }}>
                    若选择相对时间，权益会基于车辆实际情况生效
                  </span>
                </div>
                <div style={{ position: 'relative' }}>
                  <Form.Item
                    name="fixedRelativeTime"
                    style={{ display: 'inline-block', width: '400px' }}
                    rules={[{ required: timeRadioChangeDisplay === 1, message: '请选择权益生效时间！' }]}
                  >
                    <RangePicker
                      showTime
                      style={{ width: '380px' }}
                      placeholder={['权益开始时间', '权益结束时间']}
                      disabled={timeRadioChangeDisplay !== 1}
                    />
                  </Form.Item>
                  <span
                    style={{
                      color: 'red',
                      fontSize: 12,
                      marginLeft: '10px',
                      position: 'absolute',
                      whiteSpace: 'nowrap',
                      top: '8px',
                    }}
                  >
                    若选择固定时间，权益只在固定时间范围内生效
                  </span>
                </div>
              </Space>
            </Col>
          </Row>
        </Form.Item>
        {/* 享有权益频次 */}
        <Form.Item label="享有权益频次" style={{ marginBottom: 0 }}>
          <Form.Item name="frequencyRadio" style={{ display: 'inline-block', width: '300px' }} initialValue={0}>
            <Radio.Group
              onChange={e => {
                frequencyRadioChange(e.target.value);
              }}
            >
              <Radio value={0}>无此策略</Radio>
              <Radio value={-1}>不限次数</Radio>
              <Radio value={1}>其他</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item style={{ display: 'inline-block', width: '270px' }}>
            <Space>
              <Form.Item name="outsideFrequency" style={{ display: 'none' }}></Form.Item>
              <Form.Item
                name="frequencyNum-frequency"
                noStyle
                rules={[{ required: frequencyRadioDisplay === 1, message: '请输入次数!' }]}
              >
                <InputNumber
                  min={1}
                  placeholder="只能输入数字，例如：16"
                  style={{ width: 180, marginLeft: -40 }}
                  disabled={frequencyRadioDisplay !== 1}
                />
              </Form.Item>
              <span style={{ marginLeft: 6 }}>次数</span>
              <span
                style={{
                  color: 'red',
                  fontSize: 12,
                  marginLeft: '10px',
                  position: 'absolute',
                  whiteSpace: 'nowrap',
                  top: '7px',
                }}
              >
                此处次数仅作为对外展示使用，核销次数以明细配置次数策略为准
              </span>
            </Space>
          </Form.Item>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default memo(CombineModal);
