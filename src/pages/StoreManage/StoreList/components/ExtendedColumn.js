import React, { useEffect, useState } from 'react'
import { Dropdown, Checkbox, Popconfirm, Button } from 'antd'
import _ from 'lodash'
import {
    DownOutlined,
    UpOutlined,
  } from '@ant-design/icons';
import './ExtendedColumn.less'

const ExtendedColumn = (props) => {
    const { columns, setColums } = props
    const [extendedColumn, setExtendedColumn] = useState([])
    const [refreshData, setRefreshData] = useState(0)
    const [visible, setVisible] = useState(false)
    const CheckboxChange = (e,row) =>{
        let arr = _.cloneDeep({extendedColumn})['extendedColumn']
        let target = arr.filter(item=>item.dataIndex === row.dataIndex)[0]
        target.checked = e.target.checked
        setExtendedColumn(arr)
    }
    const menu = (
        <div style={{ height: 300, overflowY: 'auto', paddingRight: '40px' }}>
            {
                extendedColumn.map((item, index) => {
                    return <p key={index}>
                        <Checkbox onChange={e=>CheckboxChange(e,item)} name={item.dataIndex} checked={item.checked || false}>{item.title}</Checkbox>
                    </p>
                })
            }
        </div>
    )
    const confirm = () => {
        let newColumns = _.cloneDeep({columns})['columns']
        newColumns.forEach(item=>{
            extendedColumn.forEach(ele=>{
                if(item.dataIndex === ele.dataIndex){
                    item.checked = ele.checked
                }
            })
        })
        setColums(newColumns)
    }
    const onVisibleChange = (visible) =>{
        console.log(visible)
        setVisible(visible)
    }
    useEffect(() => {
        let arr = _.cloneDeep({columns})['columns']
        arr = arr.filter(item => JSON.stringify(item['checked']) !== undefined)
        setExtendedColumn(arr)
    }, [columns,refreshData])
    return <Popconfirm onVisibleChange={onVisibleChange} overlayClassName='ExtendedColumn' placement="bottomRight" icon={<></>} style={{paddingLeft:0}} title={menu} okText='确定' cancelText='取消' onConfirm={confirm}>
        <a style={{marginLeft:'20px'}} onClick={()=>setRefreshData(refreshData+1)}>
            <span style={{marginRight:'4px'}}>扩展列</span>
        {
            visible? <UpOutlined />
            : <DownOutlined />
        }
       
        </a>
    </Popconfirm>
}
export default ExtendedColumn