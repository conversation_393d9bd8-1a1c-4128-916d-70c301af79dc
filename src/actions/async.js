import {PublicTabAddNormal,getMenuListNormal,getUserInfoNormal,getDictNormal,changeListExpand, staffchangeListExpand} from './normal'
import {get} from '../utils/request'
import allUrl from '../utils/url'
import { message } from 'antd';

//获取tab页list
export const PublicTabAdd = TabList => {
    return PublicTabAddNormal(() => {
        return TabList
    })
};
//获取用户菜单
export const GetMenuList  = data =>{
    return getMenuListNormal (()=>{
        return get(allUrl.Authority.MenuUserList).then(res=>{
            if(res && res.success){
                let data = res.resp
                for(let i=0;i< data.length ;i++){
                    if(!data[i].children){
                        data.splice(i,1)
                        i--
                    }
                    if(data[i].isHidden == 1) {
                        data.splice(i,1)
                        i--
                    }
                }
                let arr = [{
                    title:'根元素',
                    key:0,
                    children:data
                }]
                return arr
            }else{
                // message.error(res.msg)
                return false
            }
        })
    })
}
//获取用户信息
export const GetUserInfo  = (cb) =>{
    return getUserInfoNormal (()=>{
        // if(localStorage.getItem('userInfo')){
        //     return new Promise((resove,reject)=>{
        //         resove(localStorage.getItem('userInfo'))
        //     }).then(res=>{
        //         return JSON.parse(res)
        //     })
        // }else{
            return get(allUrl.Authority.userInfo).then(res=>{
                if(res && res.resp){
                    let data = res.resp ? res.resp[0] :{}
                    data.permissionCodeList = data.permissionCodeList ? data.permissionCodeList : []
                    data.organizations = data.organizations ? data.organizations : []
                    data.organizationIds = data.organizationIds ? data.organizationIds : []
                    data.position = data.position ? data.position : ''
                    localStorage.setItem('userInfo',JSON.stringify(data))
                    cb && cb()
                    return data
                }else{
                    // message.error(res.msg)
                    return false
                }
            })
        // }
    })
}

//获取公共字典
export const getDict  = codes =>{
    return getDictNormal (()=>{
        return get(allUrl.common.entryLists, codes).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                return Dt
            } else {
                // message.error(res.msg)
                return false
            }
        })
    })
};
//更改展开列表
export const ListExpandAdd = ListExpand => {
    return changeListExpand(() => {
        return ListExpand
    })
};
//更改门店员工列表展开列表
export const StaffListExpandAdd = ListExpand => {
    return staffchangeListExpand(() => {
        return ListExpand
    })
};