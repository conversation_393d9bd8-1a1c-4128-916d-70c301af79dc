import { getSeresFileItemDetail } from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import { useRequest } from '@/hooks/request';
import React, { FC } from 'react';
import { useParams } from 'react-router';
import { FileDeliverType, FilePostType } from '../constants';
import FileBaseInfo from './base';
import FileDeliverDetail from './detail';
import FileStatistic from './statistic';

const FileDetail: FC = () => {
  const { id } = useParams<{ id: string }>();

  const {
    data: [data] = [
      {
        deliverType: FileDeliverType.SPECIFIC_STORE,
        postType: FilePostType.SPECIFIC_POST,
      },
    ],
    loading,
  } = useRequest(() => getSeresFileItemDetail({ id: id as unknown as number }));

  return (
    <PageContainer ghost={false} loading={loading}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
        <FileBaseInfo file={data} loading={loading} />
        <FileStatistic file={data} />
        <FileDeliverDetail file={data} />
      </div>
    </PageContainer>
  );
};

export default FileDetail;
