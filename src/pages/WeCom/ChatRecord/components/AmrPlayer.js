import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { Button, Input } from 'antd'
import { DownloadOutlined } from '@ant-design/icons';
import './AmrPlayer.less'
var BenzAMRRecorder = require('benz-amr-recorder');

const AmrPlayer = forwardRef((props, ref) => {
    let timer = null
    const { url, id } = props
    const [amr, setAmr] = useState(undefined)    
    const [loading, setLoading] = useState(false)
    const [currentPosition, setCurrentPosition] = useState(undefined)
    const [isPlaying, setIsPlaying] = useState(false)
    const [isStop, setIsStop] = useState(true)
    const [isDragging, setIsDragging] = useState(false)

    const E = (selector) => {
        return document.getElementsByClassName(`${selector}-${id}`)[0]
    }
    useEffect(()=> {
        setAmr(new BenzAMRRecorder())
    }, [])
    useEffect(()=> {
        // console.log('时间', currentPosition)
    }, [currentPosition])
    useEffect(() => {
        if(!amr) return
        setLoading(true)
        var playBtn = E('amr-play')
        var stopBtn = E('amr-stop')
        var progressCtrl = E('amr-progress')
        var cur = E('amr-cur')
        var duration = E('amr-duration')

        amr.initWithUrl(url).then(function() {
            setLoading(false)
            playBtn.removeAttribute('disabled');
            stopBtn.removeAttribute('disabled');
            progressCtrl.removeAttribute('disabled');
            progressCtrl.setAttribute('max', amr.getDuration());
            duration.innerHTML = amr.getDuration().toFixed(2);
            // 绑定事件
           
        })
        amr.onPlay(function () {
            console.log('Event: play', amr.isPlaying());
            playBtn.innerHTML = '暂停';
            setIsPlaying(true)
            setIsStop(false)
        });
        amr.onStop(function () {
            console.log('Event: stop');
            playBtn.innerHTML = '播放';
            setIsPlaying(false)
            setIsStop(true)
        });
        amr.onPause(function () {
            console.log('Event: pause 暂停');
            playBtn.innerHTML = '继续';
            setIsPlaying(false)
            setIsStop(false)
        });
        amr.onResume(function () {
            console.log('Event: resume 恢复');
            playBtn.innerHTML = '暂停';
            setIsPlaying(true)
            setIsStop(false)
        });
        amr.onEnded(function () {
            console.log('Event: ended');
            playBtn.innerHTML = '播放';
            setIsPlaying(false)
            setIsStop(true)
        });
        amr.onAutoEnded(function () {
            console.log('Event: autoEnded');
        });
        amr.onStartRecord(function () {
            console.log('Event: startRecord');
        });
        amr.onFinishRecord(function () {
            console.log('Event: finishRecord');
        });
        amr.onCancelRecord(function () {
            console.log('Event: cancelRecord');
        });
        playBtn.onclick = function () {
            amr.playOrPauseOrResume();
        };
        stopBtn.onclick = function () {
            amr.stop();
        };

        progressCtrl.onmousedown = function (e) {
           setIsDragging(true)
        };
        progressCtrl.onmouseup = function (e) {
            amr.setPosition(e.target.value);
            setIsDragging(false)
            console.log('鼠标抬起', e.target.value)
        }
      
    }, [amr]);
  
    useEffect(() => {
        let timer1 = null
        console.log('isPlaying',isPlaying, isDragging, isStop )
        const cur = E('amr-cur')
        const progressCtrl = E('amr-progress')
       if(isPlaying && !isStop) {

            timer1 = setInterval(function () {
                cur.innerHTML = amr.getCurrentPosition().toFixed(2);
                if (!isDragging) {
                    progressCtrl.value = amr.getCurrentPosition().toFixed(2);

                }
                console.log('timer', cur.innerHTML,  amr.getCurrentPosition(), progressCtrl.value)
        }, 10);
       } 
       if(isStop) {
        progressCtrl.value = 0
        cur.innerHTML = '0.00';
       }
       return ()=> {
        clearInterval(timer1)
       }
    }, [isPlaying, isDragging, isStop]);
    const resetVoice = () => {
        amr.stop()
    }
    useImperativeHandle(ref, () => ({
        resetVoice,// 这里运用了es6的简写，（实际等于： resetSharePopup：resetSharePopup）
      }));
    return (
        <div className={`amr-player amr-player-${id}`}>
            <Button size="small" type="link" className={`amr-play-${id}`} loading={loading} >播放</Button>
            <Button size="small" type="link" className={`amr-stop-${id}`} disabled >停止</Button>
            <input type="range"  className={`amr-progress-${id}`} disabled step="any"/>
            <div className="amr-time">
                <span className={`amr-cur-${id}`}>0</span>
                <span>/</span>
                <span className={`amr-duration-${id}`}>0</span>
            </div>
            {/* 下载按钮隐藏 */}
            {/* <Button href={url} type="primary" shape="circle" icon={<DownloadOutlined />} size="small" /> */}
        </div>
    )
}
)
export default AmrPlayer