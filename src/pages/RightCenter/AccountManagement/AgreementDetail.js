import React, { memo, useContext } from 'react'

import { Table, Modal } from 'antd'
import { getWidth, offsetLeft } from '@/utils/index'

import moment from 'moment'
import styles from '../antd.module.less'
import { KeepTableInject } from '@/pages/RightCenter/AccountManagement/Detail'

const AgreementDetail = (props) => {
    const { visible, dataSource, handleCancel } = props
    const { type } = useContext(KeepTableInject) ?? {}
    return (
        <Modal visible={visible} footer={[]} onCancel={handleCancel} title='权益履约详情' width={getWidth()} style={{ left: offsetLeft() }} >

            <Table className={styles['blue-table']}
                dataSource={dataSource}
                rowKey={'id'}
                bordered={true}
                pagination={false}
                scroll={{ x: 'max-content' }}
            >
                {/* <Table.Column title='履约时间' key='useTime' dataIndex='useTime' width={140} render={text=>text?moment(text).format('YYYY年MM月DD日'):'-' }fixed={'left'}  /> */}
                {
                    type === 'boutique' ?
                        <>
                            <Table.Column title='批售订单号' key='purchaseOderNo' dataIndex='purchaseOderNo' width={140} fixed='left' render={text => text || '-'} />
                            <Table.Column title='服务订单号' key='serviceOrderNo' dataIndex='serviceOrderNo' width={140} fixed='left' render={text => text || '-'} />
                        </> :
                        <Table.Column title={dataSource[0] && dataSource[0].businessCode == '102' ? '工单编码' : '工单编码'} key='carCode' dataIndex='carCode' width={210} fixed='left' render={text => text || '-'} />
                }
                <Table.Column title='盛大订单号' key='sdOrderNo' dataIndex='sdOrderNo' width={120} fixed='left' render={text => text || '-'} />
                <Table.Column title='门店' key='salesStoreName' dataIndex='salesStoreName' width={100} fixed='left' render={text => text || '-'} />
                <Table.Column title='创建时间' key='createTime' dataIndex='createTime' width={210} fixed='left' render={text => text ? moment(text).format('YYYY年MM月DD日 HH:mm:ss') : '-'} />
                <Table.Column title='进场里程（公里）' key='mileage' dataIndex='mileage' width={210} render={text => text || '-'} />
                <Table.Column title='进场时智库里程（公里）' key='thinkMileage' dataIndex='thinkMileage' width={210} render={text => text || '-'} />
                <Table.Column title='客户姓名' key='userName' dataIndex='userName' width={210} render={text => text || '-'} />
                <Table.Column title='客户手机号' key='userPhone' dataIndex='userPhone' width={210} render={text => text || '-'} />
                <Table.Column title='维修内容' key='useContent' dataIndex='useContent' width={210} render={text => text || '-'} />
                <Table.Column title='更换内容' key='replaceContent' dataIndex='replaceContent' width={210} render={text => text || '-'} />
                <Table.Column title='维修人' key='maintainer' dataIndex='maintainer' width={210} render={text => text || '-'} />
                <Table.Column title='维修人手机号' key='maintainerPhone' dataIndex='maintainerPhone' width={210} render={text => text || '-'} />
                <Table.Column title='权益动作' key='equityExecuteName' dataIndex='equityExecuteName' width={210} render={text => text || '-'} />
            </Table>

        </Modal>
    )
}
export default memo(AgreementDetail)
