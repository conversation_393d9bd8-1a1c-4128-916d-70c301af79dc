import React,{useState,useEffect,useRef} from 'react'
import { Modal ,Input, Steps ,Button ,message ,Tree ,Divider,Form ,Row,Col ,Checkbox , Result ,Spin} from 'antd'
import {post,get} from '@/utils/request'
import allUrl from '@/utils/url';
import './SelectOrg.less'

const { Step } = Steps;
const { Search } = Input;

const SelectOrgModal = (props) =>{
    const {visible,onCancel,cb,parentSelectOrg,orgType,currentOrg,SelectedOrg} = props
    const treeRef = useRef();
    const [form] = Form.useForm()
    const [selectOrg,setSelectOrg] = useState({})
    const [loading,setLoading] = useState(false)
    const [title,setTiele] = useState('')
    const [current,setCurrent] = useState(1)
    const [treeData,setTreeData] = useState([])
    const [roleList,setRoleList] = useState([])
    const [treeselectedKeys,setTreeselectedKeys] = useState([])
    const [treeSelectedRows,setTreeSelectedRows] = useState([])
    const [defaultExpandedKeys,setDefaultExpandedKeys] = useState([])
    const [defaultSelectedKeys,setDefaultSelectedKeys] = useState(['1954'])
    const [saveData,setSaveData] = useState({})
    const [showRoleList, setShowRoleList] = useState([])
    const handleCancel = () =>{
        if(current === 2){
            cb(saveData, orgType, treeSelectedRows[0].type === 1)
        }
        onCancel()
    }

    const GetOrgData = () => {
        setLoading(true)
        return new Promise((resolve,reject)=>{
            GerRoleList()
            post(allUrl.Authority.orgPageList,{name:''}).then(res => {
                setLoading(false)
                if (res.success) {
                    setTreeData(res.resp)
                    resolve(true)
                } else {
                    // message.error(res.msg)
                    resolve(false)
                }
            })
        })
    }

    const GerRoleList = () =>{
        let ids = ''
        if(orgType === 'ADD'){
            ids = treeSelectedRows.length ? treeSelectedRows[0].key : parentSelectOrg[0].key
        }else{
            ids = treeSelectedRows.length ? treeSelectedRows[0].key : currentOrg.organizationId
        }
        // post(allUrl.Authority.rolesByOrganizationIds, { organizationIds:[ids] }).then(res => {
        get(allUrl.Authority.organizationRoleList).then(res => {
            if (res.success) {
                setRoleList(res.resp)
                setShowRoleList(res.resp)
            } else {
                // message.error(res.msg || '获取列表信息失败！')
            }
        })
    }

    const treeSelect = (selectedKeys, selectedNodes, event) =>{
        const data = {...saveData}

        data.organizationId = selectedNodes.selectedNodes.length ? selectedNodes.selectedNodes[0].key :''
        data.organizationName = selectedNodes.selectedNodes.length ? selectedNodes.selectedNodes[0].title :''
        setSaveData(data)
        setTreeselectedKeys(selectedKeys)
        setTreeSelectedRows(selectedNodes.selectedNodes)
        setTiele(selectedNodes.selectedNodes.length ? selectedNodes.selectedNodes[0].title :'')
        
    }

    const next = () => {
        const data = {...saveData}
        console.log(treeSelectedRows)
        let temp = SelectedOrg.filter(item=>item.organizationId === treeSelectedRows[0].key)
        if(temp.length) return message.error('所选组织不能重复！')

        // if(!data.dataAuthority && current === 0){
        //     message.error('请选择数据可见范围！')
        //     return
        // }
        if((!data.roleIds || !data.roleIds.length) && current === 1){
            message.error('请选择角色！')
            return 
        }
        setCurrent(current + 1);
        if(current === 2){
            handleCancel()
            return
        }
    };
    
    const prev = () => {
        setCurrent(current - 1);
        setTimeout(() => {
            treeRef.current.scrollTo({key:treeselectedKeys.length ? treeselectedKeys[0]:parentSelectOrg[0].key,align:'auto'})
        }, 100);
    };

    const handleChange = (value,field) =>{

        let data = {...saveData} || {}
        if(showRoleList.length < roleList.length) {
            data[field] = data[field] || [];
            showRoleList.forEach((s) => {
                console.log(s,data[field].indexOf(s.id))
                if(data[field].indexOf(s.id) > -1) {
                    data[field].splice(data[field].indexOf(s.id), 1)
                }
            })
        } else {
            data[field] = [];
        }
        data[field] = Array.from(new Set(data[field].concat(value)))
        
        if(field === 'roleIds'){
            data.roleArr = []
            data.roleNames = []
            roleList.forEach(item=>{
                data[field].forEach(ele=>{
                    if(item.id === ele){
                        data.roleArr.push({name:item.roleName,id:item.id})
                        data.roleNames.push(item.roleName)
                    }
                })
            })
        }
        data.type = treeSelectedRows[0].type

        setSaveData(data)
    }

    const onSearch = (v) => {
        let newList = []
        roleList.map(item => {
            if(item.roleName === v || item.roleName.startsWith(v) || item.roleName.indexOf(v) > -1){
                newList.push(item)
            }
        })
        setShowRoleList(newList)
    }
    const steps = [
        {
          title: '选择所属组织',
          content: <div>
            {
                treeData.length ?
                <Tree disabled={orgType === 'EDIT'} ref={treeRef} defaultSelectedKeys={defaultSelectedKeys} defaultExpandAll={true} treeData={treeData} height={500} onSelect={treeSelect} />:null
            }
            {/* <Form.Item label='数据可见范围'>
                <Radio.Group value={saveData.dataAuthority} onChange={(e)=>handleChange(e.target.value,'dataAuthority')}>
                <Radio value={1}>个人数据</Radio>
                <Radio value={2}>组织数据</Radio>
                </Radio.Group>
            </Form.Item> */}
          </div>
        },
        {
          title: '配置角色',
          content: <Row style={{marginTop:'20px'}}>
                <Search placeholder="角色快速搜索" onSearch={onSearch} allowClear style={{ width: 200,  marginTop: 10, marginBottom:20 }} />

              <Form.Item className='roleList' style={{width:'100%'}}>
                    <Checkbox.Group style={{ width: '100%' }} value={saveData.roleIds} onChange={(e)=>handleChange(e,'roleIds')}>
                        <Divider plain>店端配置角色</Divider>
                        <Row style={{ width: '100%' }}>
                            {
                                showRoleList.map((item, index) => {
                                    if (item.type == 1) {
                                        return (
                                            <Col span={4} key={item.id} style={{marginBottom:'5px'}}>
                                                <Checkbox value={item.id} name={item.name}>{item.roleName}</Checkbox>
                                            </Col>
                                        )    
                                    }
                                })
                            }
                        </Row>
                        <Divider plain>厂端配置角色</Divider>
                        <Row style={{ width: '100%' }}>
                            {
                                showRoleList.map((item, index) => {
                                    if (item.type == 3) {
                                        return (
                                            <Col span={4} key={item.id} style={{marginBottom:'5px'}}>
                                                <Checkbox value={item.id} name={item.name}>{item.roleName}</Checkbox>
                                            </Col>
                                        )    
                                    }
                                })
                            }
                        </Row>
                        <Divider plain>其他配置角色</Divider>
                        <Row style={{ width: '100%' }}>
                            {
                                showRoleList.map((item, index) => {
                                    if (item.type !== 1 && item.type !== 3) {
                                        return (
                                            <Col span={4} key={item.id} style={{marginBottom:'5px'}}>
                                                <Checkbox value={item.id} name={item.name}>{item.roleName}</Checkbox>
                                            </Col>
                                        )    
                                    }
                                })
                            }
                        </Row>
                    </Checkbox.Group>
                </Form.Item>
          </Row>,
        },
        {
          title: '完成',
          content: <Result
                status="success"
                title="操作成功"
                subTitle="您已完成组织添加"
                extra={[
                    <Button key='1' type="primary" onClick={() => handleCancel()}>
                        查看
                    </Button>,
                ]}
            />,
        },
    ];

    useEffect(()=>{
        const data = {...saveData}
        if(orgType === 'ADD'){
            data.organizationId = parentSelectOrg[0].key
            data.organizationName = parentSelectOrg[0].title
            GetOrgData().then(res=>{
                if(treeRef.current) treeRef.current.scrollTo({key:parentSelectOrg[0].key,align:'auto'})
            })
            setSaveData(data)
            setTiele(parentSelectOrg[0].title)
            setDefaultSelectedKeys([parentSelectOrg[0].key])
            setTreeSelectedRows(parentSelectOrg)
            setTreeselectedKeys([parentSelectOrg[0].key])
        }else{
            data.organizationId = currentOrg.organizationId
            data.organizationName = currentOrg.organizationName
            // data.dataAuthority = currentOrg.dataAuthority || 0
            data.roleArr = currentOrg.roleArr
            data.roleIds = currentOrg.roleIds
            data.roleNames = currentOrg.roleNames
            GetOrgData().then(res=>{
                if(treeRef.current) treeRef.current.scrollTo({key:currentOrg.organizationId,align:'auto'})
            })
            setSaveData(data)
            setTiele(currentOrg.organizationName)
            setDefaultSelectedKeys([currentOrg.organizationId])
            setTreeSelectedRows([currentOrg])
            setTreeselectedKeys([currentOrg.organizationId])
            setCurrent(1)
        }
    },[parentSelectOrg,orgType])
    return <Modal
            visible={visible}
            title={'选择组织：' + title}
            width={1000}
            onCancel={handleCancel}
            maskClosable={false}
            footer={null}
            wrapClassName='SelectOrgWarp'
        >
            <Spin spinning={loading}>
                <Steps current={current}>
                    {steps.map(item => (
                    <Step key={item.title} title={item.title} />
                    ))}
                </Steps>
                <div className="steps-content">{steps[current].content}</div>
                <div className="steps-action">
                    {current < 2 && 
                    <Button type="primary" loading={loading} onClick={() => next()}>
                        下一步
                    </Button>
                    }
                    {current > 0 && current < 2 &&
                    <Button style={{ margin: '0 8px' }} loading={loading} onClick={() => prev()}>
                        上一步
                    </Button>
                    }
                </div>
            </Spin>
    </Modal>
}
export default SelectOrgModal