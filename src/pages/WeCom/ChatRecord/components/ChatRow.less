
.chat-row {
    div {
        display: inline-block;
    }
    .pointer {
        cursor: pointer;
    }
    .text-align-right {
        text-align: right;
    }
    .content {
        overflow: hidden;
        display: inline-flex;
        padding: 15px;
        background-color: #fff;
        border-radius: 3px 8px 8px 8px;
        user-select: none;
    }
    .from-content {
        overflow: hidden;
        display: inline-flex;
        background-color: rgba(213, 231, 255, 1);
        padding: 15px;
        border-radius: 3px 8px 8px 8px;
        user-select: none;
        margin-right: 10px;
    }
    .from-name {
        max-width: 140px;
    }
    .chatrecord-title {
        margin-bottom: 20px;
    }
    .ant-avatar.ant-avatar-icon > .anticon {
        margin-top: 0;
    }
    .revoked-bg {
        border: 1px solid #ED6A0C;
        position: relative;
        padding: 20px 15px;
        min-width: 78px;
    }
    .revoked-tip {
        background-color: #ED6A0C;
        border-radius: 2px;
        color: #fff;
        font-size: 12px;
        position: absolute;
        left: 0;
        top: 0;
        line-height: 18px;
        padding: 0 2px;
    }
    .text-blue {
        color: #1890FF;
    }
    .text-green {
        color: #52C41A;
    }
    .ant-btn {
        height: 0;
        padding: 0;
    }
    
}
.content-video {
    display: flex;
    justify-content: center;
}
.spin-btn {
    z-index: 10000;
    position: fixed;
    right: 30px;
    top: 160px;
}
// .content-audio {
//     display: flex;
//     justify-content: center;
//     height: 100%;
//     width: 100%;
// }
// .voice-modal {
//     .ant-modal-content {
//         height: 100px;
//     }
//     .ant-spin-nested-loading {
//         display: contents;
//     }
// }
// .voice-spin {
//    width: 100%;
//    height: 100px;
// }
// .video-modal {
//     .ant-modal-content {
//         height: 600px;
//     }
//     .ant-spin-nested-loading {
//         display: contents;
//         height: 600px;
//     }

// }
// .image-modal {
//     text-align: center;
// }