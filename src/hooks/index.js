import { useEffect, useState, useRef, useCallback } from "react";

/**
 * 通过定义方法的回调函数获取state的值
 * @param {*} initState //useState的初始值
 * @returns []
 */

export const useCallbackState = (initState) => {
    // const cbRef = useRef();
    // const [data, setData] = useState(initState);

    // useEffect(() => {
    //     cbRef.current && cbRef.current(data);
    // }, [data]);

    // return [data, (d, callback) => {
    //     cbRef.current = callback;
    //     setData(d);
    // }];
    const [state, setState] = useState(initState)
    let cbRef = useRef()
    const setXState = (state, cb) => {
        setState(prev => {
            cbRef.current = cb
            return typeof state === 'function' ? state(prev) : state
        })
    }
    useEffect(() => {
        if (cbRef.current) {
            cbRef.current(state)
        }
    })
    return [state, setXState]
}

/**
 * 自定义 useState，返回最新值
 * @param state
 * @returns
 */
export const useSyncState = (initState) => {
    const cbRef = useRef();
    const [data, setData] = useState(initState);

    useEffect(() => {
        cbRef.current && cbRef.current(data);
    }, [data]);

    return [data, (d, callback) => {
        cbRef.current = callback;
        setData(d);
    }];
};
 