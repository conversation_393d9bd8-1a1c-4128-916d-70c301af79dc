.upgradeDetail {
  :global {

    .PublicList .tableData .ant-table-wrapper .ant-table {
      padding-top: 13px;
    }

    tr.ant-table-expanded-row>td {
      background-color: #fff;
    }

    .detail-table {
      &>.ant-table-wrapper .ant-table {
        margin: 20px 32px;
        padding: 0;
      }

      .detail-upgrade-table {
        &>.ant-table-wrapper .ant-table {
          margin: 0;
        }
      }

      h4 {
        padding-left: 11px;
        position: relative;
        z-index: 9;
        font-size: 15px;
        color: #2C73B4;
        font-weight: bold;
      }

      .tips-title {
        color: #F5222D;
        font-size: 12px;
        padding: 17px 32px 0;
      }

      .btn-wrapper {
        display: flex;
        align-items: center;
        // justify-content: center;
      }

      .show-form-wrapper {
        border: 1px solid #BADEFF;
        margin: 21px 8px 0;

        .method {
          background-color: #DBEEFF;
          height: 42px;
          padding-top: 5px;
          padding-left: 20px;

          .ant-form-item-label {
            width: 123px;

            &>label {
              color: #2C73B4;
              font-size: 15px;
              font-weight: bold;
            }
          }

        }

        .abreast {
          padding-top: 24px;
        }
      }

      .input {
        width: 776px;
      }

      .s-title {
        padding-left: 25px;
        line-height: 30px;
        border-bottom: 1px solid #E9E9E9;
        margin: 20px 0;
      }

      .ant-form-item {
        .ant-form-item-label {
          // width: 202px;
        }
      }
    }


  }

}

.upgradeWrapper {

  // width: calc(100% - 64px);

  .ant-table-expanded-row-fixed {
    background: #EEF7FF;
  }

  .detail-table {
    h4 {
      padding-top: 4px;
    }

    .ant-table {
      // padding: 0;
      background: #EEF7FF;
    }

    .ant-table-content {
      background-color: #fff;
    }
  }
}

.titleWrapper {
  border-bottom: 2px solid #5095D5;
  position: relative;
  margin-bottom: 32px;

  :global {
    h4 {
      width: 122px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      background: #5095D5;
      font-size: 15px;
      color: #fff;
    }

    span {
      color: #F5222D;
      font-size: 12px;
      padding-left: 15px;
      position: absolute;
      left: 130px;
      top: 5px;
    }
  }

}

.descriptions {
  padding-left: 8%;
}

.btn {
  padding-right: 8px;
  padding-left: 8px;
  line-height: 14px;
  height: 14px;
}

.upgradeBtn:extend(.btn) {
  color: #1890FF;
  border-left: 1px solid #E9E9E9;
}

.openBtn:extend(.btn) {
  color: #1890FF;
  border-left: 1px solid #E9E9E9;
}

.blueUpgradeTable {
  :global {
    .ant-table {
      padding: 8px 9px !important;

      .ant-table-thead>tr>th {
        background-color: #D6EBFF;
      }

      td {
        background-color: #F1F8FF;
      }
    }
  }
}