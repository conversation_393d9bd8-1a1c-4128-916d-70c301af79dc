import "./index.less";
import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Row, Col, Tabs, Spin, Button, Modal, message } from "antd";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
import PublicTable from "@/components/Public/PublicTable";
import _ from "lodash";
import moment from "moment";
import Staff from "./Staff";
import Timeout from "./Timeout";
import { roleJudgment } from "@/utils/authority";
import NightAndMorningTimeout from "./NightAndMorningTimeout";

const { TabPane } = Tabs;

const ChatRecord = (props) => {
  const [tabsKey, setTabsKey] = useState("1");
  const { userInfo } = useSelector((state) => state.common);
  const [defaultActiveKey, setDefaultActiveKey] = useState("1");
  const tabsCallback = (key) => {
    setTabsKey(key);
  };
  const getDefaultActiveKey=()=>{
    if(roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_STAFF")){
      //console.log('1')
      return '1'
    }else{
      if(roleJudgment(userInfo, "WECOM_CHAT_RECORDS_TIMEOUT")){
        //console.log('3')
        return '2'
      }else{
        if(roleJudgment(userInfo, "WECOM_CHAT_NIGHT_RECORDS_TIMEOUT") ){
          //console.log('3')
          return '3'
        }
      }
    }
    return '1'
  }
  // useEffect(() => {
  //   if(roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_STAFF")){
  //     console.log('1')
  //   }else{
  //     if(roleJudgment(userInfo, "WECOM_CHAT_RECORDS_TIMEOUT")){
  //       console.log('3')
  //       setDefaultActiveKey('2')
  //     }else{
  //       if(roleJudgment(userInfo, "WECOM_CHAT_NIGHT_RECORDS_TIMEOUT") ){
  //         console.log('3')
  //         setDefaultActiveKey('3')
  //       }
  //     }
  //   }
  // }, [defaultActiveKey]);
  return (
    <div className="ChatRecord">
      <>
        <Tabs onChange={tabsCallback} defaultActiveKey={getDefaultActiveKey()}>
          {roleJudgment(userInfo, "WECOM_CHAT_RECORDS_CHAT_STAFF") ? (
            <TabPane tab="聊天记录检索" key="1">
              <Staff />
            </TabPane>
          ) : null}{/*  */}
          {roleJudgment(userInfo, "WECOM_CHAT_RECORDS_TIMEOUT") ? (
            <TabPane tab="超时消息检索" key="2">
              <Timeout />
            </TabPane>
          ) : null}
          
          {roleJudgment(userInfo, "WECOM_CHAT_NIGHT_RECORDS_TIMEOUT") ? (
            <TabPane tab="夜间清晨超时消息监督" key="3">
              <NightAndMorningTimeout />
            </TabPane>
          ) : null}

        </Tabs>
      </>
    </div>
  );
};

export default ChatRecord;
