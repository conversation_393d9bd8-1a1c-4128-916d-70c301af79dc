import React, { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import { Row, Col, Button, Spin, Tooltip, Tag } from "antd";
import { post } from "@/utils/request";
import allUrl from "@/utils/url";
import PublicTable from "@/components/Public/PublicTable";
import PublicTableQuery from "@/components/Public/PublicTableQuery";
import _ from "lodash";
import moment from "moment";
import { roleJudgment } from "@/utils/authority";
import PublicTooltip from "@/components/Public/PublicTooltip";
import "./NightAndMorningTimeout.less";
import NightChatRow from "./components/NightChatRow";
import ExtendedColumn from "@/components/Public/ExtendedColumn";
import { useCallbackState } from "../../../hooks";

const NightAndMorningTimeout = (props) => {
  const { userInfo } = useSelector((state) => state.common);

  const queryRef = useRef();
  const tableRef = useRef();
  const [pageSpin, setPageSpin] = useCallbackState(false);
  const [tableHeight, setTableHeight] = useState(0);
  const [recordObj, setRecordObj] = useState({});
  const [treeOrgData, setTreeOrgData] = useState([]);
  const recordRef = React.createRef();
  const [chatList, setChatList] = useState([]);
  const chatListRef = useRef(chatList);
  const [chatListHasMore, setChatListHasMore] = useState(false);
  const dateFormat = "YYYY-MM-DD HH:mm:ss";

  const getstartTime = () => {
    //console.log("执行开始时间设置");
    const curentTime = moment();
    const yesterday21Clock = moment()
      .add(-1, "day")
      .startOf("day")
      .add(21, "hour");
    const today0Clock = moment().startOf("day");
    const today9Clock = moment().startOf("day").add(9, "hour");
    const today21Clock = moment().startOf("day").add(21, "hour");
    //console.log(yesterday21Clock,today0Clock,today9Clock)

    if (curentTime.isAfter(today0Clock) && curentTime.isBefore(today9Clock)) {
      return yesterday21Clock.format(dateFormat);
    } else if (
      curentTime.isAfter(today9Clock) &&
      curentTime.isBefore(today21Clock)
    ) {
      return yesterday21Clock.format(dateFormat);
    } else {
      return today21Clock.format(dateFormat);
    }
  };
  const getEndTime = () => {
    //console.log("执行结束时间设置");

    const curentTime = moment();
    const today0Clock = moment().startOf("day");
    const today9Clock = moment().startOf("day").add(9, "hour");
    const today21Clock = moment().startOf("day").add(21, "hour");

    if (curentTime.isAfter(today0Clock) && curentTime.isBefore(today9Clock)) {
      return curentTime.format(dateFormat);
    } else if (
      curentTime.isAfter(today9Clock) &&
      curentTime.isBefore(today21Clock)
    ) {
      return today9Clock.format(dateFormat);
    } else {
      return curentTime.format(dateFormat);
    }
  };
  const [defaultFormatStartTime, setDefaultFormatStartTime] = useState(
    getstartTime()
  );
  const [defaultFormatEndTime, setDefaultFormatEndTime] = useState(
    getEndTime()
  );

  const [defaultQuery, setDefaultQuery] = useState({
    startTime: defaultFormatStartTime,
    endTime: defaultFormatEndTime,
  });

  const [chatSpining, setChatSpining] = useState(false);
  const [hasScroll, setHasScroll] = useState(false);
  const [firstGetChatRecord, setFirstGetChatRecord] = useState(true);
  const [endId, setEndId] = useState(null);
  const [clickOlgMsgButton, setClickOlgMsgButton] = useState(false)
  const offsetDays = 31 * 24 * 60 * 60 * 1000; //最多选择范围31天ms
  const [toOldId, setToOldId] = useState(""); // 往上滑动时，查询过去会话传的lastId
  const [deliverStatusList, setDeliverStatusList] = useState([
    { name: "全部", value: 0 },
    { name: "待交付", value: 1 },
    { name: "交付完成", value: 2 },
  ]);
  const chatRowRef = useRef();

  useEffect(() => {
    chatListRef.current = chatList; // 把它写入 ref
  }, [chatList]); // 取得chatList最新值，方便赋值后直接使用

  // 初始化
  useEffect(() => {
    GetOrgData();
    initPage();
  }, []);

  const onSearch = (values) => {
    if (values.timeRange && values.timeRange.length) {
      values.startTime = moment(values.timeRange[0]).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      values.endTime = moment(values.timeRange[1]).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    } else {
      values.startTime = defaultFormatStartTime;
      values.endTime = defaultFormatEndTime;
    }
    delete values.timeRange;
    if(values.deliverStatus===0){
      delete values.deliverStatus;
    }
    setDefaultQuery(values);
    
  };
  // 获取组织列表
  const GetOrgData = (name = "") => {
    post(allUrl.Authority.orgPageList, { name }).then((res) => {
      if (res.success) {
        const arr = res.resp.slice(0);
        setTreeOrgData(arr);
      } else {
        // message.error(res.msg);
      }
    });
  };
  // 组织搜索
  const searchOrg = _.debounce((value) => {
    if (value) {
      GetOrgData(value);
    }
  }, 500);
  const renderOperation = (text, record) => {
    return (
      <div style={{ color: "#1890ff" }}>
        {roleJudgment(userInfo, "WECOM_CHAT_RECORDS_TIMEOUT_VIEW")
          ? [
              <div key={1}>
                <a onClick={() => lookChatRecord(record)}>查看超时消息</a>
              </div>,
            ]
          : null}
      </div>
    );
  };
  const lookChatRecord = (record) => {
    setRecordObj(record);
    setFirstGetChatRecord(true);
    const listDom = document.getElementsByClassName("record-list")[0];
    listDom.scrollTop = 0;
  };
  useEffect(() => {
    if (recordObj.chatCode) {
      setChatList([]);
      getRecord();
      setClickOlgMsgButton(false);
    }
  }, [recordObj]);
  const getRecord = (lastId, beforePosition, direction) => {
    if (chatSpining) return; // 节流，防止多次请求
    const listDom = document.getElementsByClassName("record-list")[0];
    const dataDirection = direction || 1; //数据拉取方向，1 为从节点向前拉取，2 为从节点向后拉取, 默认向后（当前时间）拉取
    setChatSpining(true);
    // 有lastid则是二次请求，首次请求标识置否
    lastId && setFirstGetChatRecord(false);
    // beforePosition && setBeforeScrollHeight(beforePosition);
    // 首次请求只传beginId, 再次向当前时间查询beginId传空，lastId传最近的id，再次向前查询beiginId也传空，lastId传最早的id，并且dataDirection传1
    post(allUrl.WeCom.NightSlideChatRoundChat, {
      chatCode: recordObj.chatCode,
      dataModel: dataDirection,
      // beginId: lastId ? "" : recordObj.beginId,
      // sign: recordObj.chatSign,
      // lastId,
      // dataDirection,
    }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        setChatListHasMore(Dt.hasMore);
        if (lastId) {
          // console.log("二次加载");
          // 再次加载
          // 向前查询
          if (dataDirection == 1 && Dt.list.length) {
            setToOldId(Dt.list[Dt.list.length - 1].id);
            setChatList((chatList) => Dt.list.reverse().concat(chatList));
          }
          // 向后查询
          if (dataDirection == 2 && Dt.list.length) {
            setEndId(Dt.list[0].id);
            setChatList((chatList) => Dt.list.concat(chatList));
          }
        } else {
          // 首次加载
          // console.log("首次加载");
          setChatList((chatList) => Dt.list);
        }
        listDom.scrollTop = lastId ? beforePosition : 0;
        setChatSpining(false);
      }
    });
  };
  // 聊天记录滚动条监听
  const handleOnScrollCapture = (event) => {
    if (recordRef) {
      if (
        recordRef.current.scrollHeight > recordRef.current.clientHeight &&
        recordRef.current.scrollTop === 0
      ) {
        // console.log("到顶部", toOldId, endId);
        // getRecord(chatListRef.current[0].id, recordRef.current.scrollHeight, 1);
      }
      if (
        recordRef.current.scrollHeight - recordRef.current.clientHeight >
        recordRef.current.scrollTop + 100
      ) {
      } else {
        // console.log("到底部");
        if (chatListHasMore) {
          // getRecord(
          //   chatListRef.current[chatListRef.current.length - 1].id,
          //   recordRef.current.scrollTop - 100,
          //   2
          // );
        }
      }
    }
  };
  // 滚动条设置
  useEffect(() => {
    setChatSpining(true);
    setTimeout(() => {
      setChatSpining(false);
    }, 500);
    setHasScroll(
      recordRef.current.scrollHeight > recordRef.current.clientHeight
    );
  }, [chatList, firstGetChatRecord]);

  let searchList = [
    {
      label: "时间段",
      name: "timeRange",
      type: "RangePicker",
      showTime: true,
      initialValue: [
        moment(defaultFormatStartTime, dateFormat),
        moment(defaultFormatEndTime, dateFormat),
      ],
      rules: [
        {
          required: true,
        },
      ],
    },
    { label: "群名", name: "groupName", type: "Input" ,colSpan: 6,},
    {
      label: "群状态",
      name: "deliverStatus",
      type: "Select",
      data: deliverStatusList,
      initialValue: 0,
    },
    {
      label: "客户认定姓名",
      name: "customerName",
      type: "Input",
    },
    { label: "群主", name: "groupOwnerName", type: "Input" },
    {
      label: "群主所在组织",
      name: "organizationId",
      type: "TreeSelect",
      data: treeOrgData,
      onSearch: searchOrg,
      fieldNames: {
        label: "title",
        value: "key",
        children: "children",
      },
      placeholder: "选择组织",
    },
  ];

  const [columns, setColums] = useState([
    {
      title: "会话开启时间",
      fixed: "left",
      dataIndex: "beginTime",
      width: 200,
    },
    {
      title: "群主所在组织",
      dataIndex: "ownerOrgName",
      width: 180,
      render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
    },
    {
      title: "群名",
      dataIndex: "groupName",
      width: 180,
      render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
    },
    { title: "群状态", dataIndex: "deliverStatusName", width: 100 },
    { title: "客户认定姓名", dataIndex: "customerName", width: 140 },
    { title: "客户类型", dataIndex: "customerTypeName", width: 140 },
    { title: "群主", dataIndex: "ownerName", width: 140 },
    { title: "群主岗位", dataIndex: "positionName", width: 140 },
    {
      title: "所属大区",
      dataIndex: "bigAreaName",
      isExtend: true,
      width: 180,
      //   render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>
    },
    {
      title: "所属省区",
      dataIndex: "middleAreaName",
      isExtend: true,
      width: 180,
      isExtend: true,
      width: 180,
      //   render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>
    },
    {
      width: 180,
      title: "所属用户中心",
      dataIndex: "dealerName",
      isExtend: false,
      width: 180,
      //   render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>
    },
    {
      title: "操作",
      width: 150,
      fixed: "right",
      dataIndex: "Operation",
      render: (text, record) => renderOperation(text, record),
    },
  ]);
  let newColumns = _.cloneDeep({ columns }).columns;
  for (let i = 0; i < newColumns.length; i++) {
    if (
      JSON.stringify(newColumns[i]["checked"]) !== undefined &&
      !newColumns[i].checked
    ) {
      newColumns.splice(i, 1);
      i--;
    }
  }
  const getOldMsg = () => {
    getRecord('', '', 2);
    setClickOlgMsgButton(true)
  };
  const initPage = () => {
    let winH =
      document.documentElement.clientHeight || document.body.clientHeight;
    let FormQueryH = document.getElementsByClassName("PublicList_FormQuery")[0]
      .clientHeight;
    let h = winH - FormQueryH - 47 - 54 - 305;
    setTableHeight(h);
  };
  const closeSpin = () => {
    chatRowRef.current.closeSpin();
  };
  return (
    <>
      {pageSpin ? (
        <Button type="primary" onClick={closeSpin} className="spin-btn">
          关闭
        </Button>
      ) : null}
      <Spin
        spinning={pageSpin} //是否开启加载中
        tip="资源加载中，请稍候..." //loading文本，这里也可以设置为动态的文本
      >
        <div className="timeoutSearch">
          <PublicTableQuery
            ref={queryRef}
            isCatch={true}
            isFormDown={false}
            onSearch={onSearch}
            searchList={searchList}
            defaultQuery={defaultQuery}
          />
          <div className="tableData">
            <div className="table">
              <Row className="tableTitle">
                <Col className="text">夜间清晨超时消息列表</Col>
                <Col className="bts">
                  <ExtendedColumn setColums={setColums} columns={columns} />
                </Col>
              </Row>

              <PublicTable
                isCatch={false}
                scroll={{ x: "max-content", y: tableHeight }}
                sticky={true}
                type={5}
                ref={tableRef}
                rowSelection={false}
                columns={newColumns}
                defaultQuery={defaultQuery}
                url={allUrl.WeCom.NightSearchOvertimeChatRound}
              />
            </div>
            <Spin wrapperClassName="record" spinning={chatSpining}>
              {chatList.length ? (
                <div className="record-info">
                  <div className="flex space-between">
                    <div className="name ellipsis">
                      <Tooltip title={recordObj.groupName}>
                        {recordObj.groupName}
                      </Tooltip>
                    </div>
                    {!clickOlgMsgButton ? (
                      <Button type="link" size="small" onClick={getOldMsg}>
                        查看更早消息
                      </Button>
                    ) : null}
                  </div>
                  <div className="tags">
                    {recordObj.tagNames && recordObj.tagNames.length
                      ? recordObj.tagNames.map((item, index) => {
                          return (
                            <Tag
                              key={index}
                              className="position ellipsis"
                              color="orange"
                            >
                              <Tooltip title={item}>{item}</Tooltip>
                            </Tag>
                          );
                        })
                      : null}
                  </div>
                </div>
              ) : null}
              <div
                className="record-list"
                ref={recordRef}
                onScrollCapture={handleOnScrollCapture}
              >
                {chatList.length ? (
                  <div>
                    <NightChatRow
                      ref={chatRowRef}
                      hasMore={chatListHasMore}
                      list={chatList}
                      beginId={recordObj.beginMsgId}
                      endId={recordObj.endMsgId}
                      qwUserId={recordObj.onwerUserId}
                      pageSpin={pageSpin}
                      setPageSpin={setPageSpin}
                    ></NightChatRow>
                  </div>
                ) : (
                  <div className="record-empty">
                    <div className="empty" style={{}}>
                      <img 
                        className="empty-img"
                        src={require("@/assets/img/empty1.png")}
                        style={{ width: 70, height: 70 }}
                      />
                      <div className="empty-content">暂无聊天数据</div>
                    </div>
                  </div>
                )}
              </div>
            </Spin>
          </div>
        </div>
      </Spin>
    </>
  );
};

export default NightAndMorningTimeout;
