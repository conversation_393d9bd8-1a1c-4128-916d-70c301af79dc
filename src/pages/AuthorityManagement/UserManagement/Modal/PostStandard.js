import React,{useEffect, useState} from 'react'
import {Modal,Table,message} from 'antd'
import {utilsDict} from '../../../../utils/utilsDict'
import { connect } from 'react-redux'
import { get } from '../../../../utils/request'
import allUrl from '../../../../utils/url'
import './PostStandard.less'


const dataSource =[
    {modular:'销售',postName:'销售合计',S:'16',A:'12',B:'12',C:'7',D:'7',remarks:''},
    {modular:'销售',postName:'总经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'销售',postName:'销售经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'销售',postName:'销售主管',S:'3',A:'2',B:'2',C:'1',D:'1',remarks:''},
    {modular:'销售',postName:'体验顾问',S:'9',A:'6',B:'6',C:'3',D:'3',remarks:''},
    {modular:'销售',postName:'大客户经理',S:'1',A:'1',B:'1',C:'1（可兼）',D:'1（可兼）',remarks:''},
    {modular:'销售',postName:'市场经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'销售',postName:'市场策划专员',S:'市场经理兼',A:'市场经理兼',B:'市场经理兼',C:'0',D:'0',remarks:''},
    {modular:'销售',postName:'市场分析专员',S:'市场经理兼',A:'0',B:'0',C:'0',D:'0',remarks:''},
    {modular:'交付',postName:'交付合计',S:'16',A:'11',B:'10',C:'7',D:'6',remarks:''},
    {modular:'交付',postName:'交付经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'交付',postName:'交付专员A',S:'3',A:'2',B:'2',C:'1',D:'1',remarks:''},
    {modular:'交付',postName:'交付专员B',S:'7',A:'5',B:'4',C:'3',D:'2',remarks:''},
    {modular:'交付',postName:'交付保障专员',S:'3',A:'2',B:'2',C:'1',D:'1',remarks:''},
    {modular:'交付',postName:'金融保险专员',S:'2',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'服务合计',S:'22',A:'19',B:'15',C:'15',D:'12',remarks:''},
    {modular:'服务',postName:'服务总监',S:'总经理无服务背景的建议设置1名', remarks:''}, 
    {modular:'服务',postName:'服务经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''}, 
    {modular:'服务',postName:'前台主管',S:'0',A:'0',B:'0',C:'0',D:'0',remarks:''},
    {modular:'服务',postName:'服务顾问',S:'3',A:'3',B:'2',C:'2',D:'2',remarks:''},
    {modular:'服务',postName:'保修专员',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'车间经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'调度员',S:'车间经理兼任',A:'车间经理兼任',B:'车间经理兼任',C:'车间经理兼任',D:'车间经理兼任',remarks:''},
    {modular:'服务',postName:'技术经理',S:'1',A:'0',B:'0',C:'0',D:'0',remarks:''},
    {modular:'服务',postName:'质检员',S:'技术经理兼任',A:'技术经理兼任',B:'技术经理兼任',C:'车间经理兼任',D:'车间经理兼任',remarks:''},
    {modular:'服务',postName:'机电技师',S:'5',A:'4',B:'3',C:'3',D:'2',remarks:''},
    {modular:'服务',postName:'钣金技师',S:'2',A:'2',B:'2',C:'2',D:'1',remarks:''},
    {modular:'服务',postName:'喷漆技师',S:'2',A:'2',B:'2',C:'2',D:'1',remarks:''},
    {modular:'服务',postName:'PDI专员',S:'2',A:'2',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'配件经理',S:'1',A:'0',B:'0',C:'0',D:'0',remarks:''},
    {modular:'服务',postName:'配件专员',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'用户关爱经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'服务',postName:'用户关爱专员',S:'交付用户数＜100时，关爱经理兼任；交付用户数＞200时，按用户数：关爱专员=200:1的比例设置'},
    {modular:'服务',postName:'用户体验专员',S:'1',A:'1',B:'战略城市、S/A级或交付数＞300的用户中心设置1名；其他用户中心可兼任。'},
    {modular:'服务',postName:'洗车工',S:'可兼职/外包',A:'可兼职/外包',B:'可兼职/外包',C:'可兼职/外包',D:'可兼职/外包',remarks:''},
    {modular:'精品附件',postName:'精品附件经理',S:'1',A:'1',B:'可兼',C:'可兼',D:'可兼',remarks:''},
    {modular:'精品附件',postName:'精品附件专员A（售前）',S:'可兼',A:'可兼',B:'可兼',C:'可兼',D:'可兼',remarks:''},
    {modular:'精品附件',postName:'精品附件专员B（售后）',S:'服务顾问兼任',A:'服务顾问兼任',B:'服务顾问兼任',C:'服务顾问兼任',D:'服务顾问兼任',remarks:''},
    {modular:'综合支持',postName:'财务经理',S:'1',A:'1',B:'1',C:'1',D:'1',remarks:''},
    {modular:'综合支持',postName:'财务专员',S:'1',A:'0',B:'0',C:'0',D:'0',remarks:''},
    {modular:'',postName:'总计',S:'57',A:'44',B:'38',C:'30',D:'26',remarks:''},
]
dataSource.map((item,index)=>item.key=index+1)

const PostStandard = (props) =>{
    const {visible,onCancel,userInfo} = props
    const [PostStandardInfo,setPostStandardInfo] = useState({})
    const [InforData4,setInforData4] = useState({
        rowKey: record => record.key,
        bordered: true,
        size:'small',
        dataSource,
        scroll: { x: 'max-content' },
        rowClassName:(record, index)=>{
            let arr = ['销售合计','服务合计','交付合计', '总计']
            return arr.indexOf(record.postName)>-1? 'rowBold':''
        },
        columns: [
            { title: '模块', dataIndex: 'modular',render:(text,record,index)=>{
                const obj = {
                    children: <div style={{textAlign:'center',fontWeight: 'bold'}}>{text}</div>,
                    props: {
                        rowSpan:0,
                    },
                };
               
                if(index ===0){
                    obj.props.rowSpan = 9
                }else if(index===9){
                    obj.props.rowSpan = 6
                }else if(index===15){
                    obj.props.rowSpan = 20
                }else if(index===35){
                    obj.props.rowSpan = 3
                }else if(index===38){
                    obj.props.rowSpan = 2
                }else if(index===40){
                    obj.props.rowSpan = 1
                }
                
                return obj
            },},
            { title: '岗位名称', dataIndex: 'postName',render:text=><div style={{textAlign:'center'}}>{text}</div> ,onCell(record, rowIndex) {
                // let filterText = ['销售合计','服务合计','交付合计']
                // return {
                //     className:filterText.indexOf(record.postName)<=-1?'td_blue':'',
                // };
                
            }},
            { 
                title: <div>
                <p>开业配置规则</p>
                <p>测算逻辑：保障开业前期3个月的业务量</p>
                <p>每人每月工作26天，有效工时70%,15%的事故台次占比</p>
            </div>, 
            colSpan: 5,
            dataIndex: 'configurationRules',children:[
                { title: 'S', dataIndex: 'S',
                render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(record.postName == '服务总监' || record.postName == '用户关爱专员'){
                        obj.children = <div style={{textAlign:'center', fontSize: 10}}>{text}</div>
                        obj.props.colSpan = 5
                    }
                    
                    return obj
                }},
                { title: 'A', dataIndex: 'A' , render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(record.postName == '服务总监' || record.postName == '用户关爱专员'){
                        obj.props.colSpan = 0
                    }
                    
                    return obj
                }},
                { title: 'B', dataIndex: 'B', render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(record.postName == '服务总监' || record.postName == '用户关爱专员'){
                        obj.props.colSpan = 0
                    }else if (record.postName == '用户体验专员') {
                        obj.children = <div style={{textAlign:'center', fontSize: 10}}>{text}</div>
                        obj.props.colSpan = 3
                    }
                    
                    return obj
                }},
                { title: 'C', dataIndex: 'C', render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(record.postName == '服务总监' || record.postName == '用户关爱专员'){
                        obj.props.colSpan = 0
                    }else if (record.postName == '用户体验专员') {
                        obj.props.colSpan = 0
                    }
                    
                    return obj
                }},
                { title: 'D', dataIndex: 'D' , render:(text,record,index)=>{
                    const obj = {
                        children: <div style={{textAlign:'center'}}>{text}</div>,
                        props: {
                            colSpan:1,
                        },
                    };
                   
                    if(record.postName == '服务总监' || record.postName == '用户关爱专员'){
                        obj.props.colSpan = 0
                    }else if (record.postName == '服务体验专员') {
                        obj.props.colSpan = 0
                    }
                    
                    return obj
                }},
                
            ]},
            { title: '备注', dataIndex: 'remarks',render:(text,record,index)=>{
                const obj = {
                    children: <div style={{textAlign:'center'}}>{text}</div>,
                    props: {}
                };
                // if(index ===2){
                //     obj.props.rowSpan = 3
                // }
                return obj
            }},
        ],
        pagination: false,
    })
    const handleOk = () =>{

    }

    const getlistByOrganizationIds = () =>{
        if(userInfo && userInfo.organizationIds && userInfo.organizationIds.length){
            get(allUrl.StoreManage.listByOrganizationIds,{organizationIds:userInfo?userInfo.organizationIds.join(','):''}).then(res=>{
                if(res && res.success){
                    let Dt = res.resp?res.resp[0]:{}
                    // Dt.dealerType = 1
                    // let newInforData1 = {...InforData2}
                    // let newInforData2 = {...InforData3}
                    // let target1 = newInforData1.dataSource.filter(item=>item.grade === Dt.level)
                    // let target2 = newInforData2.dataSource.filter(item=>item.grade === Dt.level)
                    // newInforData1.dataSource = target1
                    // newInforData2.dataSource = target2
                    // setInforData2(newInforData1)
                    // setInforData3(newInforData2)
                    setPostStandardInfo(Dt)
                }else{
                    // message.error(res.msg)
                }
            })
        }else{
            setPostStandardInfo({})
        }
    }

    useEffect(()=>{
        getlistByOrganizationIds()
    },[userInfo])

    return <Modal title={'岗位和人员标准'} wrapClassName='PostStandard' visible={visible} onOk={handleOk} onCancel={onCancel} width={1300} footer={false} maskClosable={false}>
        {
            <div>
                <Table {...InforData4} />
            </div>
        }
    </Modal>
}
export default connect(({ common }) => {
    const {userInfo } = common
    return {
        userInfo
    }
})(PostStandard)