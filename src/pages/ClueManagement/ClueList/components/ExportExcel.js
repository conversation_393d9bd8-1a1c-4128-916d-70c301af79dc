import React, { useState } from 'react'
import {Modal,Radio,Alert,Space,message} from 'antd'
import { post } from '../../../../utils/request'
import allUrl from '../../../../utils/url'
import moment from 'moment'
import {fileDown} from '@/utils'
const ExportExcel = (props) =>{
    const {visible,onCancel,total=0,defaultQuery} = props
    const [RadioValue,setRadioValue] = useState('1')
    const handleOk = () =>{
        console.log(RadioValue)
        let query = {}
        if(RadioValue === '2'){
            query = defaultQuery
            if(query.lastFollowTime && query.lastFollowTime.length){
                query.lastFollowTimeStart = moment(query.lastFollowTime[0]).format('YYYY-MM-DD')
                query.lastFollowTimeEnd = moment(query.lastFollowTime[1]).format('YYYY-MM-DD')
                delete query.lastFollowTime
            }
            if(query.createTime && query.createTime.length){
                query.createTimeStart = moment(query.createTime[0]).format('YYYY-MM-DD')
                query.createTimeEnd = moment(query.createTime[1]).format('YYYY-MM-DD')
                delete query.createTime
            }
        }
        post(allUrl.ClueManagement.exportClue,{...query},{responseType: "blob"}).then(res=>{
            fileDown(res,'线索列表')
            onCancel()
        })
    }
    const onChange = (e) =>{
        setRadioValue(e.target.value)
    }
    return <Modal title={'导出为EXCEL'} visible={visible} onOk={handleOk} onCancel={onCancel} maskClosable={false}>
        <Radio.Group onChange={onChange} value={RadioValue}>
            <Space direction="vertical">
                <Radio value='1'>全部数据</Radio>
                <Radio value='2'>满足当前筛选条件的数据</Radio>
            </Space>
        </Radio.Group>
        <Alert style={{marginTop:'26px'}} message="注意：当前导出的数据超过10000条，将分sheet导出。" type="info" showIcon />
    </Modal>
}
export default ExportExcel