import React, { useState,useEffect } from 'react'
import { Modal, message, Form, Select, DatePicker, Upload, Button } from 'antd'
import baseURL from '@/baseURL'
import Cookies from 'js-cookie'
import { get, post } from '@/utils/request'
import allUrl from '@/utils/url'
import moment from 'moment'

const { Item } = Form
const { Option } = Select

export default (props) => {
    const { title, onCancel, visible, type ,cb ,recordTableData ,delMechanismFile ,mechanismType } = props
    // const [deleteAttachmentIds, setDeleteAttachmentIds] = useState([])
    const [form] = Form.useForm()

    const handleOk = () => {
        console.log(form)
        form.validateFields().then(values=>{
            console.log(values)
            values.signContractTime = values.signContractTime ? moment(values.signContractTime).format('YYYY-MM-DD') :''
            values.institutionType = type
            if(recordTableData){
                values.id = recordTableData.id
                values.key = recordTableData.key
            }
            let fileList = values.attachments && values.attachments.length ? values.attachments.map(item=>{
                return {
                    attachmentName:item.attachmentName,
                    attachmentUrl:item.attachmentUrl,
                    id:item.id?item.id:''
                }
            }):[]
            values.attachments = fileList
            cb(values)
            onCancel()
        })
    }

    const normFile = (e, attachmentType) => {
        if (Array.isArray(e)) {
            e.forEach(item => {
                item.attachmentType = attachmentType
                if (item.response) {
                    item.attachmentName = item.name
                    item.attachmentUrl = item.response.resp[0].url
                }
            })
            return e;
        }
        e.fileList.forEach(item => {
            item.attachmentType = attachmentType
            if (item.response) {
                item.attachmentName = item.name
                item.attachmentUrl = item.response.resp[0].url
            }
        })
        return e && e.fileList;
    };

    useEffect(()=>{
        console.log(recordTableData)
        if(recordTableData){
            if(recordTableData.attachments && recordTableData.attachments.length){
                recordTableData.attachments.forEach((item, index) => {
                    item.name = item.attachmentName
                    item.url = item.attachmentUrl
                    item.uid = index + 1
                    item.status = 'done'
                })
            }
    
            form.setFieldsValue({
                institutionName:recordTableData.institutionName,
                signContractTime:recordTableData.signContractTime ? moment(recordTableData.signContractTime) :null,
                attachments:recordTableData.attachments || []
            })
        }
    },[recordTableData])

    const uploadProps = {
        name: 'file',
        action: baseURL.Host + allUrl.StoreManage.uploadFile,
        data: { code: 'storePorject' },
        headers: {
            appid: 1,
            authorization: Cookies.get('scrm_token')
        },
        onChange(info) {
            if (info.file.status !== 'uploading') {
                // console.log(info.file, info.fileList);
            }
            if (info.file.status === 'done') {
                message.success(`${info.file.name} 上传成功！`);
            } else if (info.file.status === 'error') {
                message.error(`${info.file.name} 上传失败！`);
            }
        },
        onRemove(info) {
            console.log(info)
            delMechanismFile(info)
            // let temp = [...deleteAttachmentIds]
            // if (!info.id) return
            // let obj = {
            //     id: info.id,
            //     attachmentType: info.attachmentType,
            //     attachmentName: info.attachmentName,
            //     attachmentUrl: info.attachmentUrl
            // }
            // temp.push(obj)
            // setDeleteAttachmentIds(temp)
        }
    }

    const fieldsArr = [
        { type: 'Input', label: '机构名称', name: 'institutionName' },
        { type: 'DatePicker', label: '签约时间', name: 'signContractTime' },
        { type: 'Upload', label: '机构附件', name: 'attachments' },
    ]
    const institutionNameOptoins = type == 1?
    [
        {name:'中国银行',value:'中国银行'},
        {name:'建设银行',value:'建设银行'},
        {name:'东风金融',value:'东风金融'},
        {name:'微众银行',value:'微众银行'},
        {name:'新网银行',value:'新网银行'},
        {name:'广发银行',value:'广发银行'},
    ]:[
        {name:'人保',value:'人保'},
        {name:'平安',value:'平安'},
        {name:'太平洋',value:'太平洋'},
    ]
    return <Modal title={title} visible={visible} onOk={handleOk} onCancel={onCancel} maskClosable={false}>
        <Form form={form}>
            {
                fieldsArr.map((item, i) => {
                    const {type, label, name } = item
                    if (type === 'Input') {
                        return <Item label={label} key={i} name={name} rules={[{ required: true }]}>
                            {/* <Input placeholder='请输入' allowClear /> */}
                            <Select allowClear placeholder='请选择' style={{ width: '100%' }}>
                                {
                                    institutionNameOptoins.map((item,index)=><Option key={index} value={item.value}>{item.name}</Option>)
                                }
                            </Select>
                        </Item>
                    } else if (type === 'DatePicker') {
                        return <Item label={label} key={i} name={name} rules={[{ required: false,message:'请选择签约时间' }]}>
                            <DatePicker placeholder='选择时间' style={{width:'100%'}} format="YYYY-MM-DD" />
                        </Item>
                    } else if (type === 'Upload') {
                        return <Item
                                name={name}
                                label={label}
                                key={i}
                                valuePropName="fileList"
                                getValueFromEvent={e => normFile(e, name)}
                                className='file'
                                rules={[{ required: false,message:'请上传机构附件' }]}
                            >
                                <Upload {...uploadProps}>
                                    <Button style={{ border: '1px solid #1890ff', color: '#1890ff', width: 78, padding: 0 }}>添加附件</Button>
                                    <span style={{color: 'rgba(0,0,0,0.65)',fontSize:'11px',marginLeft:'8px'}} onClick={e=>e.stopPropagation()}>请上传签约文件</span>
                                </Upload>
                            </Item>
                    }
                })
            }
        </Form>
    </Modal>
}