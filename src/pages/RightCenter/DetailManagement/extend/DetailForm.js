import React, { useEffect, useState, memo, useContext } from 'react'
import { Form, Input, Select, Button, Radio, Row, Col, message } from 'antd';
import { get } from '@/utils/request';
import allUrl from '@/utils/url';
import OriginalTable from './OriginalTable'
import { InjectContext } from '../CreateForm'
import CreateFormDetail from '../CreateFormDetail'

const DetailForm = (props) => {
    const { form, formData, setFormData, usable, setUsable, dataSource, formConfig,setOriginDetailVersion, ...rest } = useContext(InjectContext)
    const [currentDataSource, setCurrentDataSource] = useState({})
    const [currentFormDetailConfig, setCurrentFormDetailConfig] = useState({})
    const [validateStatus, setValidateStatus] = useState('')
    const [toast, setToast] = useState('')
    const [help, setHelp] = useState('')
    // console.log('123456789', formConfig, rest);
    const setConfig = (detail) => {
        if(!detail)return
        // 策略置灰选项
        const _config = {
            limitTimeFrame: {},
            limitLegendFrame: {},
            effectMileageFrame: {}
        }
        // 前权益选择无此策略：限制年限选择无此策略；前权益选择不限年数时，限制年限只能选择不限年数；前权益选择其他：延保限制年限默认选择其他；
        if (Number(detail.limitTimeFrame) === 0 || Number(detail.limitTimeFrame) === -1) {
            _config['limitTimeFrame']['disabled'] = true
        } else if (Number(detail.limitTimeFrame) === 1) {
            _config['limitTimeFrame']['childrenDisabled1'] = true
        }
        // 权益限制里程只显示可以修改的选项
        _config['limitLegendFrame']['currentShow'] = Number(detail['limitLegendFrame'])
        // 前权益配置了权益限制里程（基准）为行驶里程、增程器里程时，延保权益的延保生效里程无法选择「无此策略」（无此策略选项隐藏）
        if (Number(detail['limitLegendFrame'] !== 0)) {
            _config['effectMileageFrame']['childrenHidden1'] = true
        }
        return _config
    }
    const editDefault = (detail) => {
        let config
        // 如果当前是编辑状态
        if (sessionStorage.getItem('createType') === 'edit') {
            config = setConfig(dataSource)
        } else {
            config = setConfig(detail)
        }

        // console.log(_config);
        setFormData({
            strategyConfig: config,
            // formDataSource: _formDataSource,
            originDetail: detail
        })

    }
   

    const queryDetail = (value) => {
        const _value = value || form.getFieldValue("originDetailNo");
        if (!_value) return message.error("请输入前权益明细编码后再查询")
        formData.originDetail = {}
        reset()
        get(allUrl.DetailedManageInterests.getDetailInfo, { detailNo: _value }, { notificationConfig: true }).then(res => {
            if (res.success) {
                const detail = res.resp?.[0]

                if (detail) {
                    editDefault(detail)
                    // 默认带出前权益的业务编码,不可以编辑
                    const newDetailconfig = {
                        ...formConfig,
                        businessCode: { ...formConfig.businessCode, disabled: true } // 自动带出前权益明细的业务编码到延保明细的业务编码上，延保明细业务编码不可编辑；
                    }
                    setCurrentFormDetailConfig(newDetailconfig)
                    const newData = {
                        ...dataSource,
                        detailNo: '',
                        businessScene: 2,
                        businessCode: res.resp?.[0]?.businessCode,    // 默认带出前权益的业务编码
                        goodsCategory:res.resp?.[0]?.goodsCategory
                    }
                    // 延保权益创建时‘可否重复购买‘选项默认为：不可重复购买
                    if (rest.pageType === 'Add') {
                        newData.isRepeatablePurchase = 0
                    }
                    setCurrentDataSource(newData)

                    //1、 前权益若配置了：享有权益频次、全保养阶梯策略、补漆面部位、车联网流量、权益金额抵扣，正常查询，但无法延保 2、升级权益不可延保
                    if (!detail.allowExtend) {
                        setToast(detail.notAllowExtendDesc)
                        setUsable(false)

                    } else if (detail.businessScene == 2) {
                        setToast('该权益明细为延保明细，若选择此明细将会在延保明细上再次延保')
                        setUsable(true)
                    } else if (detail.businessScene == 1 && detail.extendDetailNoList.length > 0) {
                        const _detailStr = detail.extendDetailNoList.join('，')
                        setToast(`该权益明细已经由延保明细${_detailStr}延保，如需再次延保则延保明细支持同时生效～`)
                        setUsable(true)
                    } else {
                        // console.log(999)
                        setUsable(true)
                        setToast('')
                    }

                } else {
                    setValidateStatus('error')
                    setHelp('无法查询到该权益明细，请检查～')
                    setUsable(false)
                }
                // 编辑状态
                if (sessionStorage.getItem('createType') === 'edit') {
                    setValidateStatus('')
                    setHelp('')
                    setToast('')
                }

            } else {
                setValidateStatus('error')
                setHelp('无法查询到该权益明细，请检查～')
                setUsable(false)
            }
        })

    }
    const reset = () => {
        detailNoChange()
        setToast('')
        setFormData({
            strategyConfig: {},
            originDetail: {}
        })
    }
    const detailNoChange = () => {
        setValidateStatus('')
        setHelp('')
    }
    useEffect(() => {
        // 如果当前是编辑状态
        if (sessionStorage.getItem('createType') === 'edit') {
            if (dataSource.originDetailNo) {
                queryDetail(dataSource.originDetailNo)
            }
        }
    }, [sessionStorage.getItem('createType')])
    useEffect(() => {
        editDefault({})
    }, [])
    return (
        <>
            <Form.Item label="前权益明细编码" className='prefix-red' validateStatus={validateStatus} help={<span className='custom-item-help' style={{ display: validateStatus === '' ? 'none' : 'block' }}>{help}</span>}>
                <Row gutter={8}>
                    <Col span={12}>
                        <Form.Item
                            name="originDetailNo"
                            noStyle
                            rules={[{ require: true, message: '请输入' }]}>
                            <Input placeholder="请输入前权益明细编码后点击查询即可选择前权益明细" allowClear disabled={sessionStorage.getItem('createType') === 'edit'} onChange={detailNoChange} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Button type='primary' disabled={sessionStorage.getItem('createType') === 'edit'} onClick={e => queryDetail()}>查询</Button>
                        <span className='custom-item-help custom-item-help-mt6' style={{marginLeft:'10px'}}>请输入前权益明细编码后点击查询即可选择前权益明细</span>
                    </Col>
                </Row>
            </Form.Item>
            {/* 前权益明细内容 */}
            {(formData.originDetail && Object.keys(formData.originDetail).length) ? <OriginalTable dataSource={[formData.originDetail]} /> : ''}
            {toast ? <p className='custom-item-help' style={{ marginLeft: 32, marginBottom: 25 }}>{toast}</p> : ''}


            {usable ? <CreateFormDetail dataSource={currentDataSource} formConfig={currentFormDetailConfig} {...rest} /> : ''}
        </>

    )
}

export default memo(DetailForm)