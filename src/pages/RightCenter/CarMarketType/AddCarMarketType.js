import React, { useState,memo,useMemo,useEffect,useRef} from 'react'
import { useSelector } from 'react-redux'
import { Modal,Form,Row,Col,Select,Input,message} from 'antd'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
const AddCarMarketType = (props) => {
    const {handleCancel,visible,reloadList} = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const formRef = useRef(null)
    const handleOk = () =>{
        
        form.validateFields().then(values=>{
            if (!values.name) {
                return message.warn('车辆市场类型不能为空')
            }
            const params={
                name:formRef.current.getFieldValue(['name']),
         }
            console.log('add',params)
            post(allUrl.CarMarketType.saveCarMarketType, { ...params}).then(res => {
                if (res.success) {
                    handleCancel()
                    reloadList()
                 return  message.success('保存成功！')     
                } else {
                    // message.error('更新失败！')
                }
                setLoading(false)
            })

        })
    }
    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 15 },
    };
    return (
        <Modal visible={visible} onOk={handleOk} okText={'保存'}  onCancel={handleCancel} maskClosable={false} width='650px' title='车辆市场类型'>
            <Form name='change-codeInfo' ref={formRef} form={form} {...layout} >
                <Row>
                    <Col span={24}>
                        <Form.Item label='车辆市场类型' rules={[{ required: true,message: '请输入车辆市场类型!' }]} name='name'>
                        <Input allowClear placeholder='请输入车辆市场类型' />
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(AddCarMarketType)