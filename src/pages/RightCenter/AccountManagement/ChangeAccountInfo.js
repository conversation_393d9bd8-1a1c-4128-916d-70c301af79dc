import React, { useState, memo, useMemo, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Modal, Form, Row, Col, Select, Input, message } from 'antd'
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
const ChangeAccountInfo = (props) => {
    const { handleCancel, visible, reloadChangeInfo } = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const [reasonStatus, setReasonStatus] = useState('')
    const dataSourceForm = useMemo(() => props.dataSource, [props.dataSource])
    const formRef = useRef(null)
    const onChangeReason = () => {
        const { reason } = form.getFieldsValue()
        if (!reason) {
            setReasonStatus('error')
        } else {
            setReasonStatus('')
        }
    }
    const handleOk = () => {
        form.validateFields().then(values => {
            const reason = values.reason
            onChangeReason()
            if (reason) {
                handleCancel()
                const params = {
                    accountNo: dataSourceForm.accountNo,
                    carBuyerPhone: values.carBuyerPhone,
                    carBuyerName: values.carBuyerName,
                    carOwnerPhone: values.carOwnerPhone,
                    carOwnerName: values.carOwnerName,
                    certificationName: values.certificationName,
                    certificationPhone: values.certificationPhone,
                    updateReason: reason,
                }
                // 更改账户信息接口     730
                post(allUrl.AccountManagement.updateAccountObj, { ...params }).then(res => {
                    if (res.success) {
                        message.success('更新成功！')
                        reloadChangeInfo();
                    } else {
                        // message.error('更新失败！')
                    }
                    setLoading(false)
                })
            }

        })

    }

    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 15 },
    };
    useEffect(() => {
        if (dataSourceForm) {
            formRef.current && form.setFieldsValue({
                ...dataSourceForm,

            })
        }
    }, [dataSourceForm, form])
    return (
        <Modal visible={visible} onOk={handleOk} okText={'保存'} onCancel={handleCancel} maskClosable={false} width='650px' title='权益账户信息'>
            <Form name='change-accountInfo' ref={formRef} form={form} {...layout} >
                <Row>
                    <Col span={24}>
                        <Form.Item label='购车人手机号' name='carBuyerPhone' rules={[{ pattern: /^1\d{10}$/, message: '请输入正确的手机号' }]}>
                            <Input allowClear placeholder='请输入购车人手机号' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='购车人姓名' name='carBuyerName'>
                            <Input allowClear placeholder='请输入购车人姓名' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='车主手机号' name='carOwnerPhone' rules={[{ pattern: /^1\d{10}$/, message: '请输入正确的手机号' }]}>
                            <Input allowClear placeholder='请输入车主手机号' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='车主姓名' name='carOwnerName'>
                            <Input allowClear placeholder='请输入车主姓名' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='车联网手机号' name='certificationPhone' rules={[{ pattern: /^1\d{10}$/, message: '请输入正确的手机号' }]}>
                            <Input allowClear placeholder='请输入车联网手机号' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='实名认证姓名' name='certificationName'>
                            <Input allowClear placeholder='请输入实名认证姓名' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='更改原因' name='reason' validateStatus={reasonStatus} hasFeedback required={true}>
                            <Input allowClear placeholder='请输入更改原因' maxLength={50} onChange={onChangeReason} />
                        </Form.Item>
                        <p style={{ color: 'red', fontSize: '12px', position: 'absolute', top: '38px', left: '151px' }}>最多支持50个字，不支持其它表情和特殊符号，如配置则无法正常展示</p>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(ChangeAccountInfo)