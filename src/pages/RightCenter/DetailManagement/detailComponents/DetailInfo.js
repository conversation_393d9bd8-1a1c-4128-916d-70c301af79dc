import React from 'react'
import { Descriptions } from 'antd'

const performance_MAP = {
    1: '是',
    2: '否'
}
const refund_MAP = {
    1: '支持',
    2: '不支持'
}

const DetailInfo = (props) => {
    const { detail } = props
    return (
        <>
            < Descriptions title="" >
                {/* businessScene 权益业务场景（1-基本场景，2-延保场景，3-升级场景）*/}
                <Descriptions.Item
                    className='detail-sence'
                    label='权益业务场景'
                    span={detail.businessScene === 1 ? 3 : 1
                    }>{detail.businessSceneName}</Descriptions.Item>
                {
                    detail.businessScene === 2 ?
                        <>
                            <Descriptions.Item label='前权益明细编码'>{detail.originDetailNo || '-'}</Descriptions.Item>
                            <Descriptions.Item label='前权益明细名称' span={2}>{detail.originDetailName || '-'}</Descriptions.Item>
                        </> : null
                }
                <Descriptions.Item label='权益明细编码'>{detail.detailNo || '-'}</Descriptions.Item>
                <Descriptions.Item label='商品分类'>{detail.goodsCategoryName || '-'}</Descriptions.Item>
                <Descriptions.Item label='权益明细名称'>{detail.name || '-'}</Descriptions.Item>
                <Descriptions.Item label='权益履约方'>{detail.performingListDisplay || '-'}</Descriptions.Item>

                <Descriptions.Item label='业务编码'>{detail.businessCodeName || '-'}</Descriptions.Item>
                <Descriptions.Item label='业务分类'>{detail.businessCodeTypeName || '-'}</Descriptions.Item>
                <Descriptions.Item label='业务应用'>{detail.businessUseComment || '-'}</Descriptions.Item>

                <Descriptions.Item label='权益分类'>{detail.sortCodeName || '-'}</Descriptions.Item>
                <Descriptions.Item label='权益类别'>{detail.typeCodeName || '-'}</Descriptions.Item>
                <Descriptions.Item label='车辆属性'>{detail.carAttrName || '-'}</Descriptions.Item>

                <Descriptions.Item label='权益归属'>{detail.belongName || '-'}</Descriptions.Item>
                <Descriptions.Item label='是否支持退款'>{refund_MAP[detail.refund] || '-'}</Descriptions.Item>
                <Descriptions.Item label='是否三方履约'>{performance_MAP[detail.performance] || '-'}</Descriptions.Item>

                <Descriptions.Item label='权益明细版本编码' span={1}>{detail.detailVersion || '-'}</Descriptions.Item>
                <Descriptions.Item label='能否重复购买' span={2}>{detail.isRepeatablePurchaseName || '-'}</Descriptions.Item>

                <Descriptions.Item label='权益声明' span={3}>{detail.statement || '-'}</Descriptions.Item>
                <Descriptions.Item label='权益备注' span={3}>{detail.remark || '-'}</Descriptions.Item>
            </Descriptions >
        </>
    )
}

export default DetailInfo