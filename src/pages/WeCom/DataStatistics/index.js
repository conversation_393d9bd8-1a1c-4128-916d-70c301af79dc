/* eslint-disable jsx-a11y/anchor-is-valid */
import './index.less'
import React from 'react'
import { useSelector } from 'react-redux'
import { Tabs } from 'antd'
import { roleJudgment } from '@/utils/authority'
import GroupBuildingData from './GroupBuildingData'
import ChatData from './ChatData'
import PerforData from  './PerforData'
import CustomerK<PERSON> from './CustomerKPI'
const { TabPane } = Tabs

const DataStatistics = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    return (
        <div className='DataStatistics'>
            <Tabs defaultActiveKey='1'>
                {
                    roleJudgment(userInfo, 'WECOM_DATA_STATISTICS_GROUP_BUILDING_DATA') ? 
                        <TabPane tab='建群数据' key='1'>
                            <GroupBuildingData />
                        </TabPane> 
                    : null
                }
                {
                    roleJudgment(userInfo, 'WECOM_DATA_STATISTICS_CHAT_DATA') ? 
                        <TabPane tab='聊天数据' key='2'>
                            <ChatData />
                        </TabPane> 
                    : null 
                }
                {
                    roleJudgment(userInfo, 'WECOM_DATA_STATISTICS_OVERALL_RECEPTION_DATA') ? 
                        <TabPane tab='整体接待数据' key='3'>
                            <CustomerKPI />
                        </TabPane> 
                    : null
                }
                {
                    roleJudgment(userInfo, 'WECOM_DATA_STATISTICS_CUSTOMER_SERVICE_DATA') ? 
                        <TabPane tab='客服人员数据' key='4'>
                            <PerforData />
                        </TabPane> 
                    : null 
                }   
                </Tabs>
        </div>
    )
}
export default DataStatistics
