import React, { useState, memo, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';

import { Form, Button, Input, Select, Row, Col, DatePicker, TreeSelect } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import _, { add } from 'lodash';
import PropTypes from 'prop-types';
import { setStorage, getStorage, removeStorage } from '@/utils/Storage';
import moment from 'moment';
const { TreeNode } = TreeSelect;

const { Option } = Select;
const { RangePicker } = DatePicker;

const FormItem = props => {
  const { item, index, handleSearch, isItemShow } = props;
  const {
    type,
    data = [],
    label = '',
    name,
    labelCol = { span: 8 },
    wrapperCol = { span: 16 },
    rules = [],
    required = false,
    initialValue = null,
    placeholder,
    allowClear = true,
    mode = '',
    showSearch = false,
    isPressEnter = true,
    onChange,
    onSelect,
    colSpan = 6, // 一个筛选项默认占6个格，一共24格
    disabledDate = null,
    onCalendarChange = null,
    showTime = false,
    onSearch,
    help = null,
    labelAlign = 'right',
    fieldNames = null, // 自定义label，value，options字段
    extra = null,
    filterOption = false,
  } = item;
  return (
    <Col
      span={colSpan || 6}
      xs={24}
      sm={12}
      xl={colSpan || 8}
      xxl={colSpan || 6}
      key={index}
      style={{ display: isItemShow ? 'inline-block' : 'none' }}
    >
      <Form.Item
        label={label}
        name={name}
        labelCol={labelCol}
        wrapperCol={wrapperCol}
        rules={rules}
        initialValue={initialValue}
        required={required}
        help={help}
        labelAlign={labelAlign}
        extra={extra}
      >
        {type === 'Input' ? (
          <Input
            placeholder={placeholder || '请输入...'}
            allowClear={allowClear}
            onPressEnter={isPressEnter ? handleSearch : null}
          />
        ) : type === 'Select' ? (
          <Select
            placeholder={placeholder || '请选择...'}
            allowClear={allowClear}
            mode={mode}
            fieldNames={fieldNames || { value: 'value', label: 'name' }}
            onChange={onChange}
            onSearch={onSearch}
            onSelect={onSelect}
            showSearch={showSearch}
            optionFilterProp="children"
            options={data}
            filterOption={(input, option) => {
              return (option?.children || '').toLowerCase().includes(input.toLowerCase());
            }}
          >
            {/* {data && data.length
              ? data.map((ele, i) => (
                  <Option key={i} value={ele.value}>
                    {ele.name}
                  </Option>
                ))
              : null} */}
          </Select>
        ) : type === 'SearchSelect' ? (
          <Select
            placeholder={placeholder || '请选择...'}
            allowClear={allowClear}
            mode={mode}
            fieldNames={fieldNames || { value: 'value', label: 'name' }}
            onChange={onChange}
            onSearch={onSearch}
            onSelect={onSelect}
            showSearch={showSearch}
            optionFilterProp="children"
            options={data}
            filterOption={filterOption}
          >
            {/* {data && data.length
              ? data.map((ele, i) => (
                  <Option key={i} value={ele.value}>
                    {ele.name}
                  </Option>
                ))
              : null} */}
          </Select>
        ) : type === 'TreeSelect' ? (
          <TreeSelect
            showSearch
            showArrow={false}
            // style={{ width: "100%", marginBottom: "14px" }}
            value={name}
            dropdownStyle={{
              maxHeight: 800,
              overflow: 'auto',
            }}
            fieldNames={fieldNames}
            filterTreeNode={() => true}
            onSearch={onSearch}
            // labelInValue={true}
            dropdownMatchSelectWidth={false}
            treeData={data}
            placeholder={placeholder || '请选择'}
            allowClear
            onChange={onChange}
            onSelect={onSelect}
          />
        ) : type === 'RangePicker' ? (
          <RangePicker
            placeholder={placeholder || ['开始时间', '结束时间']}
            style={{ width: '100%' }}
            showTime={showTime}
            disabledDate={disabledDate}
            onCalendarChange={onCalendarChange}
          />
        ) : type === 'DatePicker' ? (
          <DatePicker placeholder={placeholder || '请选择...'} style={{ width: '100%' }} />
        ) : type === 'Text' ? (
          <>{typeof data === 'function' ? data() : data}</>
        ) : null}
      </Form.Item>
    </Col>
  );
};

FormItem.propTypes = {
  item: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  handleSearch: PropTypes.func.isRequired,
};

const PublicTableQuery = forwardRef((props, parentRef) => {
  // isFormDown 是否默认展开     resetCb 重置回调，表单的特殊处理
  const { onSearch, searchList, defaultQuery, initPage, isCatch, labelCol, wrapperCol, isFormDown, resetCb } = props;
  const [form] = Form.useForm();
  const [formDown, setFormDown] = useState(isFormDown ? true : false);
  const [searchFormList, setSearchList] = useState([]);
  const [isPutItAway, setIsPutItAway] = useState(true);
  const [colNum, setColNum] = useState(4);
  const handleSearch = () => {
    form.validateFields().then(values => {
      for (let i in values) {
        if (!values[i] && values[i] !== 0) {
          values[i] = '';
        }
        if (!i && i !== 0) {
          delete values[i];
        }
      }
      values._t = new Date().getTime();
      if (isCatch) {
        setStorage({
          Query: values,
          Type: window.location.hash,
        });
      }

      onSearch(values);
    });
  };
  // 对外暴露设置表单值方法
  const setFieldValue = (name, value) => {
    form.setFieldValue(name, value);
  };
  const onReset = () => {
    form.resetFields();
    if (isCatch) {
      removeStorage(window.location.hash);
    }
    resetCb && resetCb();
    onSearch({ _t: new Date().getTime() });
  };
  const defaultLabelCol = {
    span: 6,
  };
  const defaultWrapperCol = {
    span: 18,
  };
  const onResize = useCallback(() => {
    const width = document.documentElement.clientWidth;
    let num = 4;
    if (width >= 1200) {
      num = 4;
    } else if (width >= 560) {
      num = 2;
    } else {
      num = 1;
    }
    setColNum(num);
  }, [searchList]);
  useEffect(() => {
    const _searchList = searchList.filter(item => !item.vhide); //筛选出需要展示的searchList
    let newSearchList = _.cloneDeep({ _searchList })._searchList;
    if (newSearchList.length <= colNum - 1) {
      setIsPutItAway(false);
    } else {
      setIsPutItAway(true);
    }
    // let baseCols = 0;
    // newSearchList.map(item => {
    //   if(item.colSpan){
    //     baseCols += item.colSpan
    //   }
    // })
    // if (baseCols <= 20) {
    //   setIsPutItAway(true);
    //   setColNum(5)
    // } else {
    //   setIsPutItAway(true);
    // }
    let row = Math.ceil(_searchList.length / colNum); //查询总行数
    let sumCol = row * colNum; //全部Col的个数
    let diffCol = sumCol - _searchList.length; //差的Col的个数
    let addCol = 0; //添加Col的个数
    if (colNum > 1) {
      addCol = diffCol == 0 ? colNum - 1 : diffCol - 1;
    } else {
      setFormDown(true);
      setIsPutItAway(false);
    }
    // 如果设置默认展开，则不追加空栅格
    if (isFormDown) {
      addCol = 0;
    }
    for (let i = 0; i < addCol; i++) {
      newSearchList.push({});
    }
    setSearchList(newSearchList);
  }, [colNum, searchList]);
  useEffect(() => {
    window.addEventListener('resize', onResize);
    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []);
  useEffect(() => {
    onResize();
  }, [searchList]);
  // 时间控件在详情页面返回去时的回显需要单独处理
  const formatParams = res => {
    let _name;
    props.searchList.map(item => {
      _name = item.name;
      switch (item.type) {
        case 'DatePicker':
          const _value = res.Query[_name];
          if (_value) {
            res.Query[_name] = moment(res.Query[_name]);
          }
          break;
        case 'RangePicker':
          if (item.hasOwnProperty('paramProps')) {
            const { beginTimeKey, endTimeKey } = item.paramProps;
            if (res.Query[beginTimeKey] && res.Query[endTimeKey]) {
              res.Query[_name] = [moment(res.Query[beginTimeKey]), moment(res.Query[endTimeKey])];
            }
          }
          break;
      }
    });
  };
  useEffect(() => {
    if (JSON.stringify(defaultQuery) !== '{}') {
      getStorage(window.location.hash).then(res => {
        const _res = _.cloneDeep(res);
        if (_res && _res.Query) {
          formatParams(_res);
          delete _res.Query._t;
          form.setFieldsValue(_res.Query);
        } else {
          form.setFieldsValue(defaultQuery);
        }
      });
    }
  }, []);
  useImperativeHandle(parentRef, () => {
    // return返回的值或者方法就可以被父组件获取到
    const query = form.getFieldsValue();
    return {
      formDown,
      query,
      setFieldValue,
      handleSearch,
    };
  });

  //两个查询条件关联，关联item隐藏
  /**
   *
   * @param {*} item 查询条件item
   * @param {*} index 索引值
   * @param {*} isItemShow 查询item是否显示
   * @returns
   */
  const FormItemRender = (item, index, isItemShow) => {
    if (!item.vhide) {
      return <FormItem item={item} index={index} key={index} handleSearch={handleSearch} isItemShow={isItemShow} />;
    }
  };
  return (
    <>
      <Form
        className="PublicList_FormQuery"
        form={form}
        wrapperCol={wrapperCol || defaultWrapperCol}
        labelCol={labelCol || defaultLabelCol}
      >
        <Row style={{ paddingRight: '20px' }}>
          {searchFormList.map((item, index) => {
            if (index > colNum - 2 && !formDown) {
              return FormItemRender(item, index, false);
            }
            return FormItemRender(item, index, true);
          })}

          <Col xs={24} sm={12} xl={6} xxl={6} className="FormQuerySubmit" style={{ gap: 10 }}>
            <Col className="Reset">
              <Button onClick={onReset}>重置</Button>
            </Col>
            <Col className="Search">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
            </Col>
            {isPutItAway ? (
              <Col className="operationButtons">
                <div style={{ lineHeight: '30px' }}>
                  {formDown ? (
                    <span
                      style={{ color: '#1890ff', cursor: 'pointer' }}
                      onClick={() => {
                        setFormDown(false);
                        initPage && initPage(false);
                      }}
                    >
                      <span style={{ marginRight: 6 }}>收起</span>
                      <UpOutlined />
                    </span>
                  ) : (
                    <span
                      style={{ color: '#1890ff', cursor: 'pointer' }}
                      onClick={() => {
                        setFormDown(true);
                        initPage && initPage(true);
                      }}
                    >
                      <span style={{ marginRight: 6 }}>展开</span>
                      <DownOutlined />
                    </span>
                  )}
                </div>
              </Col>
            ) : null}
          </Col>
        </Row>
      </Form>
    </>
  );
});

PublicTableQuery.propTypes = {
  onSearch: PropTypes.func.isRequired,
  searchList: PropTypes.array.isRequired,
  initPage: PropTypes.func,
  defaultQuery: PropTypes.object,
};
export default memo(PublicTableQuery);
