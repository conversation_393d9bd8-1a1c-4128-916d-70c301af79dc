import { postSeresFilePostSeresSave } from '@/apis/file-manage';
import { Form, Input, message, Modal, ModalProps } from 'antd';
import React, { FC, useCallback, useEffect } from 'react';

export interface PositionModalProps extends ModalProps {
  open: boolean;
  data?: FileManage.SeresPost;
  onSave?: () => void;
}
const PositionModal: FC<PositionModalProps> = ({ data, onSave, ...resProps }) => {
  const [form] = Form.useForm();
  useEffect(() => {
    if (data) {
      form.setFieldsValue(data);
    }
  });

  const handleOk = useCallback(async () => {
    try {
      await form.validateFields();
      await postSeresFilePostSeresSave({ ...data, ...form.getFieldsValue() });

      message.success('保存成功');
      onSave?.();
    } catch (error) {}
  }, [data, form, onSave]);
  return (
    <Modal {...resProps} onOk={handleOk} afterClose={form.resetFields} title={data ? '编辑岗位' : '添加岗位'}>
      <Form form={form}>
        <Form.Item label="岗位名称" name="name" rules={[{ required: true, message: '请输入岗位名称' }]}>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PositionModal;
