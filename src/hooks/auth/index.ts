import { UserPermission } from '@/interfaces/permission';
import { checkUserPermission } from '@/utils/auth';
import { useCallback } from 'react';
import { useSelector } from 'react-redux';

export const useCheckPermission = () => {
  const { userInfo } = useSelector((state: any) => state.common);

  const checkPermission = useCallback(
    (permission: string | UserPermission) => {
      return checkUserPermission(userInfo, typeof permission === 'string' ? permission : permission.code);
    },
    [userInfo]
  );

  return checkPermission;
};
