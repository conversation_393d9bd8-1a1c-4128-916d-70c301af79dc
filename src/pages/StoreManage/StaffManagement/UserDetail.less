.user-detail{
    padding: 20px;
    background-color: #f0f2f5;
    height: calc(100vh - 48px - 54px - 56px - 48px);
    overflow-y:auto;
    .info-box{
        background-color: white;
        margin-top: 4px;
        margin-bottom: 24px;
        .bottom-box{
            padding: 24px 32px;
            .user-info{
                .name-span{
                    font-size: 16px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: rgba(0,0,0,0.85);
                }
                .middle-span{
                    margin-left: 5px;
                    margin-right: 5px;
                }
                .on-job{
                    display: inline-block;
                    width: 50px;
                    height: 22px;
                    background: #E6F7FF;
                    border-radius: 11px;
                    border: 1px solid #61C3FF;
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #1890FF;
                    line-height: 22px;
                    text-align: center;
                }
                .out-job{
                    display: inline-block;
                    width: 50px;
                    height: 22px;
                    background: #F1F1F1;
                    border-radius: 11px;
                    border: 1px solid #BBBBBB;
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(0,0,0,0.67);
                    line-height: 22px;
                    text-align: center;
                }
            }
        }
    }
    .user-container{
        background-color: white;
        .box{
            .top-title{
                height: 50px;
                line-height: 50px;
                background: #D6EBFF;
                font-size: 17px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: rgba(0,0,0,0.85);
                padding-left: 24px;
            }
            .bottom-box{
                padding: 24px 32px;

                .box-item{
                    .item-title{
                        .blue-dot{
                            display: inline-block;
                            width: 8px;
                            height: 8px;
                            background: #4F99DF;
                            margin-right: 10px;
                            border-radius: 8px;
                        }
                        .pass-result{
                            display: inline-block;
                            width: 100px;
                            height: 26px;
                            background: #F3F9FF;
                            border-radius: 12px;
                            border: 1px solid #C3E2FF;
                            text-align: center;
                            margin-left: 14px;
                            font-size: 14px;
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 500;
                            color: #1890FF;
                            line-height: 26px;
                        }
                        .unpass-result{
                            display: inline-block;
                            width: 100px;
                            height: 26px;
                            background: #FFF1F0;
                            border-radius: 12px;
                            border: 1px solid #FFA39E;
                            text-align: center;
                            margin-left: 14px;
                            font-size: 14px;
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 500;
                            color: #FF4D4F;
                            line-height: 26px;
                        }
                        .no-result{
                            display: inline-block;
                            width: 100px;
                            height: 26px;
                            background: rgba(0,0,0,0.04);
                            border-radius: 12px;
                            border: 1px solid rgba(0,0,0,0.15);
                            text-align: center;
                            margin-left: 14px;
                            font-size: 14px;
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 500;
                            color: rgba(0,0,0,0.6);
                            line-height: 26px;
                        }
                        font-size: 16px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: rgba(0,0,0,0.8);
                        line-height: 24px;
                    }
                    .interview{
                        margin-top: 16px;
                    }
                }
            }
            .account-info{
                width: 600px;
                .info-box{
                    height: 60px;
                    line-height: 60px;
                    .text-center{
                        text-align: center;
                    }
                }
            }
        }
        .ant-row{
            .ant-col{
                .ant-form-item{
                    margin-bottom: 12px;
                }
            }
        }
        .ellipsis {
            width: 200px;
            overflow: hidden;
            text-overflow:ellipsis;
        }
    }
}
.title-first{
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(0,0,0,0.85);
    padding-left: 32px;
    padding-bottom: 16px;
}
.user-operation{
    height: 56px;
    line-height: 56px;
    text-align: right;
    margin-right: 24px;
    border-top: solid 1px #e8e8e8;
    background-color: white;
    z-index: 1000;
}

