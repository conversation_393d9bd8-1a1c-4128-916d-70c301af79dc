/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useState } from "react";
import ReactEcharts from "echarts-for-react";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import { useSelector } from "react-redux";
import { Table, Modal, Button, DatePicker, Row, Popover } from "antd";
import { post, _post } from "@/utils/request";
import allUrl from "@/utils/url";
import moment from "moment";
import { roleJudgment } from "@/utils/authority";
import ChartContainer from "./ChartContainer";
import PageUserStatistical from "./components/PageUserStatistical";
import PageStoreStatistical from "./components/PageStoreStatistical";
import StoreGroupStatistic from "./components/StoreGroupStatistic";
import PublicTooltip from "@/components/Public/PublicTooltip";
import ExtendedColumn from "../../../components/Public/ExtendedColumn";
import _ from "lodash";
import { ArrowDownOutlined, ArrowUpOutlined } from "@ant-design/icons";
import { COLORS } from '@/constants/index'
import {fileDown} from '@/utils'
import './GroupBuildingData.less'

const { RangePicker } = DatePicker;

const unitFormat = ({ seriesName }) => {
  if (seriesName === "交付应建群量") {
    return "辆";
  } else if (seriesName === "建群交车单量") {
    return "个";
  } else if (seriesName === "建群率") {
    return "%";
  }
  return "";
};

/** 组件 ----------  昨日未建群用户中心排行榜、昨日未建群交付专员排行榜 */
const HorizontalHistogram = (props) => {
  const { data, type } = props;
  const colors = [
    "#D32F09",
    "#FF4D24",
    "#D6700C",
    "#FF7E00",
    "#FF9E00",
    "#E9BC04",
    "#FFCD00",
    "#E1D50A",
    "#FDE606",
    "#e1ef39",
  ].reverse();
  const getOption = () => {
    return {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
        },
        // formatter(params){
        //     return `
        //         ${params[0].axisValue}<br />
        //         ${params[0].marker} 未建群交车单 ${params[0].value} 个
        //     `
        // }
        formatter(params) {
          let relVal = params[0].name;
          for (var i = 0, l = params.length; i < l; i++) {
            relVal += `<div style="display:flex;justify-content: space-between;">
                                    <div>${params[i].marker}</div><div style="width:120px;text-align: left;">${params[i].seriesName}</div><div style="flex:1;text-align:right">${params[i].value} 个</div>
                            </div>`;
          }
          return relVal;
        },
      },
      grid: {
        top: "0%",
        left: "0%",
        right: "5%",
        bottom: "5%",
        containLabel: true,
      },
      xAxis: {
        type: "value",
        boundaryGap: [0, 0.01],
      },
      yAxis: {
        type: "category",
        data: data ? data.names : [],
        axisLabel: {
          width: 160,
          overflow: "truncate",
          formatter(value, index) {
            let str;
            if (type === 2) {
              str = `{a|${value}}\n{b|${data && data.dealerNames ? data.dealerNames[index] : ""}}`;
            } else {
              str = value;
            }
            return str;
          },
          rich: {
            a: {
              fontSize: 13,
              lineHeight: 22,
              color: "rgba(0,0,0,0.9)",
            },
            b: {
              fontSize: 12,
            },
          },
        },
      },
      series: [
        {
          name: "未建群交车单",
          type: "bar",
          data: data ? data.values : [],
          itemStyle: {
            normal: {
              //这里是重点
              color: function (params) {
                return colors[params.dataIndex];
              },
            },
          },
        },
      ],
    };
  };
  return (
    <div>
      <ReactEcharts style={{ minHeight: "400px" }} option={getOption()} />
    </div>
  );
};

/** 组件 ----------  交付建群趋势图 */
const GroupBuildingTrendChart = (props) => {
  const { colors, data } = props;
  let maxNum1 = data && data.values1 ? Math.max.apply(null, data.values1) + 100 : 0;
  let dataZoomEnd = 100;
  if (data && data.names && data.names.length) {
    if (data.names.length >= 25) {
      dataZoomEnd = 40;
    } else if (data.names.length >= 20) {
      dataZoomEnd = 50;
    } else if (data.names.length >= 15) {
      dataZoomEnd = 60;
    } else {
      dataZoomEnd = 80;
    }
  }
  const getOption = () => {
    return {
      color: colors,
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
        },
        formatter(params) {
          let relVal = params[0].name;
          for (var i = 0, l = params.length; i < l; i++) {
            relVal += `<div style="display:flex;justify-content: space-between;">
                                    <div>${
                                      params[i].marker
                                    }</div><div style="width:120px;text-align: left;">${
              params[i].seriesName
            }</div><div style="flex:1;text-align:right">${params[i].value} ${unitFormat(
              params[i]
            )}</div>
                            </div>`;
          }
          return relVal;
        },
      },
      grid: {
        right: "20%",
        bottom: "7%",
      },
      legend: {
        data: ["交付应建群量", "建群交车单量", "建群率"],
      },
      xAxis: [
        {
          type: "category",
          axisTick: {
            alignWithLabel: true,
          },
          data: data && data.names ? data.names : [],
          axisLabel: {
            margin: 13,
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "交付应建群量",
          position: "right",
          alignTicks: true,
          max: maxNum1,
          min: 0,
          // splitNumber:5,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[0],
            },
          },
          axisLabel: {
            formatter: (value) => {
              return Math.round(value) + "辆";
            },
          },
        },
        {
          type: "value",
          name: "建群交车单量",
          position: "right",
          alignTicks: true,
          max: maxNum1,
          min: 0,
          offset: 80,
          // splitNumber:5,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[1],
            },
          },
          axisLabel: {
            formatter: (value) => {
              return Math.round(value) + "个";
            },
          },
        },
        {
          type: "value",
          name: "建群率",
          position: "left",
          alignTicks: true,
          max: 100,
          min: 0,
          // minInterval:1,
          // maxInterval:100,
          splitNumber: 5,
          interval: 20,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[2],
            },
          },
          axisLabel: {
            formatter: (value) => {
              return Math.round(value) + "%";
            },
          },
        },
      ],
      series: [
        {
          name: "交付应建群量",
          type: "bar",
          data: data && data.values1 ? data.values1 : [],
        },
        {
          name: "建群交车单量",
          type: "bar",
          yAxisIndex: 1,
          data: data && data.values2 ? data.values2 : [],
        },
        {
          name: "建群率",
          type: "line",
          yAxisIndex: 2,
          data: data && data.values3 ? data.values3 : [],
        },
      ],
      dataZoom:
        data && data.names && data.names.length > 7
          ? [
              //给x轴设置滚动条
              {
                start: 0, //默认为0
                end: dataZoomEnd, //默认为100
                type: "slider",
                show: true,
                realtime: true,
                xAxisIndex: [0],
                handleSize: 0, //滑动条的 左右2个滑动条的大小
                height: 11, //组件高度
                bottom: 16, //下边的距离
                showDataShadow: false, //是否显示数据阴影 默认auto
                showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
                zoomOnMouseWheel: false, //鼠标移动能触发数据窗口缩放
                moveOnMouseMove: true, //鼠标移动能触发数据窗口平移
                brushSelect: false, //是否开启刷选功能
                zoomLock: true, //是否只平移不缩放
                fillerColor: "rgba(17, 100, 210, 0.32)", // 滚动条颜色
                borderColor: "rgba(17, 100, 210, 0.12)",
                dataBackground: {
                  lineStyle: {
                    width: 0,
                  },
                  areaStyle: {
                    color: "red",
                  },
                },
              },
            ]
          : [],
    };
  };
  return (
    <div>
      <ReactEcharts style={{ minHeight: "400px" }} option={getOption()} />
    </div>
  );
};

/** 组件 ----------  建群统计、昨日用户中心建群排行榜 */
const StatisticsTable = (props) => {
  const { dataSource = [], columns, type, ...reset } = props;
  // 处理columns
  let newColumns = _.cloneDeep({ columns }).columns;
  for (let i = 0; i < newColumns.length; i++) {
    if (JSON.stringify(newColumns[i]["checked"]) !== undefined && !newColumns[i].checked) {
      newColumns.splice(i, 1);
      i--;
    }
  }
  const InfoData = {
    rowKey: "key",
    size: "small",
    bordered: true,
    scroll: {x: 1200},
    columns: newColumns,
    dataSource,
    pagination: false,
    ...reset,
    // rowClassName:(record, index)=>{
    //     return record.deliverGroupPercentFlag == -1 ? 'table-red' :''
    // }
  };

  return (
    <>
      <Table {...InfoData} />
    </>
  );
};

const GroupBuildingData = (props) => {
  const { userInfo } = useSelector((state) => state.common);
  const [visible1, setVisible1] = useState(false);
  const [visible2, setVisible2] = useState(false);
  const [visible3, setVisible3] = useState(false);
  const [visible4, setVisible4] = useState(false);
  const [visible5, setVisible5] = useState(false);
  const [visible6, setVisible6] = useState(false);
  const [visible7, setVisible7] = useState(false);
  const [dates, setDates] = useState(null);
  const [hackValue, setHackValue] = useState(null);
  const [value, setValue] = useState([moment().add(-7, "d"), moment()]);
  const [Data1, setData1] = useState(null);
  const [Data2, setData2] = useState(null);
  const [Data3, setData3] = useState(null);
  const [Data4, setData4] = useState(null);
  const [Data5, setData5] = useState(null);
  const [Data6, setData6] = useState(null);
  const [Data7, setData7] = useState(null);
  const [currentRow, setCurrentRow] = useState({});

  // 交付建群趋势图
  const getGroupBuildingTrendChart = () => {
    let startTime = dates && dates.length === 2 ? dates[0] : value[0];
    let endTime = dates && dates.length === 2 ? dates[1] : value[1];
    let obj = {
      statisticsTimeStart: moment(startTime).format("YYYY-MM-DD 00:00:00"),
      statisticsTimeEnd: moment(endTime).format("YYYY-MM-DD 23:59:59"),
      pageSize: 30,
    };
    post(allUrl.WeCom.qwGroupStatisticPage, obj).then((res) => {
      if (res.success) {
        let Dt = res.resp[0].list;
        Dt.reverse();
        let obj = {
          names: [],
          values1: [],
          values2: [],
          values3: [],
        };
        Dt.forEach((item) => {
          obj.names.push(moment(item.statisticTime).format("MM-DD"));
          obj.values1.push(Number(item.deliverExpectGroupCount));
          obj.values2.push(Number(item.deliverGroupCount));
          obj.values3.push(Number(item.deliverGroupPercent));
        });
        setData5(obj);
      } else {
        // message.error(res.msg)
      }
    });
  };

  //建群统计
  const getGroupBuildingStatistics = () => {
    let startTime = dates && dates.length === 2 ? dates[0] : value[0];
    let endTime = dates && dates.length === 2 ? dates[1] : value[1];
    const statisticsTimeStart = moment(startTime).format("YYYY-MM-DD 00:00:00")
    const statisticsTimeEnd = moment(endTime).format("YYYY-MM-DD 23:59:59")
    post(allUrl.WeCom.qwGroupStatisticPage, 
      { statisticsTimeStart, statisticsTimeEnd }
      ).then((res) => {
      if (res.success) {
        res.resp[0].list.map((item, index) => (item.key = index + 1));
        setData3(res.resp[0].list);
      } else {
        // message.error(res.msg)
      }
    });
  };

  //昨日用户中心未建群排行榜
  const getStoreStatistical = () => {
    post(allUrl.WeCom.storeStatistical).then((res) => {
      if (res.success && res.resp.length) {
        res.resp[0].reverse();
        let obj = {
          names: [],
          values: [],
        };
        res.resp[0].forEach((item) => {
          obj.names.push(item.dealerName || "");
          obj.values.push(item.totalNum || "");
        });
        setData1(obj);
      } else {
        // message.error(res.msg)
      }
    });
  };

  //昨日用户中心建群排行榜
  const getQwStoreGroupStatistic = () => {
    post(allUrl.WeCom.qwStoreGroupStatisticPage, { pageIndex: 1, pageSize: 10000 }).then((res) => {
      if (res.success) {
        res.resp[0].list = res.resp[0].list.splice(0, 10)
        res.resp[0].list.map((item, index) => (item.key = index + 1));

        setData4(res.resp[0].list);
      } else {
        // message.error(res.msg)
      }
    });
  };

  //昨日交付专员未建群排行榜
  const getUserStatistical = () => {
    post(allUrl.WeCom.userStatistical).then((res) => {
      if (res.success && res.resp.length) {
        res.resp[0].reverse();
        let obj = {
          names: [],
          values: [],
          dealerNames: [],
        };
        res.resp[0].forEach((item) => {
          obj.names.push(item.delivererName || "");
          obj.values.push(item.totalNum || "");
          obj.dealerNames.push(item.dealerName || "");
        });
        setData2(obj);
      } else {
        // message.error(res.msg)
      }
    });
  };

  const disabledDate = (current) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], "days") > 30;
    const tooEarly = dates[1] && dates[1].diff(current, "days") > 30;
    return !!tooEarly || !!tooLate;
  };

  const onOpenChange = (open) => {
    if (open) {
      setHackValue([null, null]);
      setDates([null, null]);
    } else {
      setHackValue(null);
    }
  };
  // 建群率悬浮
  const renderPercent = (text, record, key1, key2) => {
    let isDown, hasValue, color, isNull = false;
    if (record[key2]) {
      isDown = !!record[key2].startsWith("-");
      hasValue = record[key2] !== "0.00";
      color = record[key2].startsWith("-") ? "#ff4d4f" : "#52c41a";
      isNull = record[key2] == "-";
    } else {
      isNull = true
    }

    const content = record.child.length ? record.child.map((item, index) => {
      return <div style={{display: 'flex', alignItems: 'center'}}key={index}>
              <i style={{marginRight: 5, borderRadius: '50%', backgroundColor:COLORS[index], width: 10, height: 10}}></i>
                <div>
                  {/* 名称 */}
                    {item.model}：{
                      item[key2] ? <div>
                        {/* 建群率展示 */}
                    {item[key1]}%
                    {/* 环比展示 */}
                    {(item[key2] !== '-' && item[key2] !== null) ? <span style={{ color: item[key2] !== '0.00' ? color : "#d9d9d9" }}>
                      （{item[key2]}%）
                      {item[key2] !== '0.00' ? <>{item[key2].startsWith('-') ? <ArrowDownOutlined /> : <ArrowUpOutlined />}</> : null}
                    </span> : null
                    }
                  </div> : `${item[key1]}%`
                }
                </div>
              </div>
          }) : null

    const renderContext = record[key2] && !isNull ? <div>
      {record[key1]}%
      <span style={{ color: hasValue ? color : "#d9d9d9" }}>
        （{record[key2]}%）
        {hasValue && !isNull ? <>{isDown ? <ArrowDownOutlined /> : <ArrowUpOutlined />}</> : null}
      </span>
    </div> : `${record[key1]}%`

      return record.child.length > 0 ? (
        <Popover
        content={content}
        title={
          <div>
            <p>{record.statisticTime}</p>
            {/* <p>已分配交车单-分配量</p> */}
          </div>
        }
      >
        {renderContext}
      </Popover>
    ) : renderContext
  };
  // 悬浮框展示
  const renderPopver = (text, record, key) => {
    const content = record.child.length ? record.child.map((item, index) => {
      return <div style={{display: 'flex', alignItems: 'center'}} key={index}>
        <i style={{marginRight: 5, borderRadius: '50%', backgroundColor:COLORS[index], width: 10, height: 10}}></i>
        <span>{item.model}：{item[key]}</span>
      </div>
    }) : null
      
    return record.child.length > 0 ? (
      <Popover
        content={content}
        title={
          <div>
            <p>{record.statisticTime}</p>
            {/* <p>已分配交车单-分配量</p> */}
          </div>
        }
      >
        {text}
      </Popover>
    ) : text
}
  // 昨日用户中心建群排行榜 导出建群统计
  const exportStoreCenter = () => {
    post(allUrl.WeCom.yesterDaystoreCenterExport,{}, {responseType: "blob"}).then(res=>{
      fileDown(res,res.fileName)
  })
  };
  // 交付单大区维度统计
  const getAreaDetail = (record) => {
    setCurrentRow(record);
    post(allUrl.WeCom.qwBigAreaStc, { statisticsTime: `${record.statisticTime} 00:00:00` }).then((res) => {
      if (res.success) {
        res.resp[0].list.map((item, index) => (item.key = index + 1));
        setData6(res.resp[0].list);
      } else {
        // message.error(res.msg)
      }
    });
    setVisible4(true);
  };
  // 交付单省区维度统计
  const getProvinceDetail = (record) => {
    setCurrentRow(record);
    post(allUrl.WeCom.qwProvinceStc, { statisticsTime: `${record.statisticTime} 00:00:00` }).then((res) => {
      if (res.success) {
        res.resp[0].list.map((item, index) => (item.key = index + 1));
        setData7(res.resp[0].list);
      } else {
        // message.error(res.msg)
      }
    });
    setVisible5(true);
  };
  // 建群统计导出
  const exportGroup = () => {
    post(allUrl.WeCom.qwGroupExport, {
      statisticsTimeStart: moment(value[0]).format("YYYY-MM-DD 00:00:00"),
      statisticsTimeEnd: moment(value[1]).format("YYYY-MM-DD 23:59:59"), pageSize: 10000, pageIndex: 1 }, {responseType: "blob"}).then(res=>{
        console.log('res.fileName',res.fileName)
     
        fileDown(res,res.fileName)
  })
  };
  // 建群统计省区
  const exportProvince = () => {
    post(allUrl.WeCom.exportPageProvince, { statisticsTime: `${currentRow.statisticTime} 00:00:00`, pageSize: 10000, pageIndex: 1 }, {responseType: "blob"}).then(res=>{
      fileDown(res,res.fileName)
  })
  };
  // 建群统计大区
  const exportBigArea = () => {
    post(allUrl.WeCom.exportPageBigArea, { statisticsTime: `${currentRow.statisticTime} 00:00:00`, pageSize: 10000, pageIndex: 1 }, {responseType: "blob"}).then(res=>{
      fileDown(res,res.fileName)
  })
  };
  
  const [columns1, setColumns1] = useState([
    { title: "统计时间", dataIndex: "statisticTime" ,width: 100, fixed: 'left',},
    {
      title: "已分配交车单",
      dataIndex: "",
      children: [
        {
          title: "分配量",
          width: 100,
          dataIndex: "distributionCount",
          render: (text, record) => renderPopver(text, record, 'distributionCount')
        },
        { title: "特殊备案量", width: 100,dataIndex: "distributionRemarkCount" ,render: (text, record) => renderPopver(text, record, 'distributionRemarkCount')},
        { title: "建群量", width: 100,dataIndex: "groupCount",render: (text, record) => renderPopver(text, record, 'groupCount') },
        {
          title: "建群率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "groupPercent", "distributionGrowthPercent"),
        },
        { title: "认定量", width: 100,dataIndex: "distributionAffirmCount", render: (text, record) => renderPopver(text, record, 'distributionAffirmCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "distributionAffirmPercent", "distributionAffirmGrowthPercent"),
        },
        { title: "建群合格量",  width: 100, dataIndex: "distributionQualifiedCount", render: (text, record) => renderPopver(text, record, 'distributionQualifiedCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "distributionQualifiedPercent", "distributionQualifiedGrowthPercent"),
        },
      ],
    },
    {
      title: "待交付交车单",
      dataIndex: "undelivered",
      // isExtend: true,
      children: [
        {
          title: "待交付量",
          width: 100,
          dataIndex: "undeliveredCount",
          render: (text, record) => renderPopver(text, record, 'undeliveredCount')
        },
        { title: "特殊备案量",width: 100, dataIndex: "undeliveredRemarkCount", render: (text, record) => renderPopver(text, record, 'undeliveredRemarkCount') },
        { title: "建群量", width: 100,dataIndex: "undeliveredGroupCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "建群率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "undeliveredGroupPercent", "undeliveredGrowthPercent"),
        },
        { title: "认定量",width: 100, dataIndex: "undeliveredAffirmCount", render: (text, record) => renderPopver(text, record, 'undeliveredAffirmCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "undeliveredAffirmPercent", "undeliveredAffirmGrowthPercent"),
        },
        { title: "建群合格量",width: 100, dataIndex: "undeliveredQualifiedCount", render: (text, record) => renderPopver(text, record, 'undeliveredQualifiedCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "undeliveredQualifiedPercent", "undeliveredQualifiedGrowthPercent"),
        },
      ],
    },
    {
      title: "交付完成交车单",
      children: [
        {
          title: "交付量",
          width: 100,
          dataIndex: "deliverCount",
          render: (text, record) => renderPopver(text, record, 'deliverCount')
        },
        { title: "特殊备案量",width: 100, dataIndex: "deliverRemarkCount", render: (text, record) => renderPopver(text, record, 'deliverRemarkCount')},
        { title: "建群量",width: 100, dataIndex: "deliverGroupCount" , render: (text, record) => renderPopver(text, record, 'deliverGroupCount')},
        {
          title: "建群率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "deliverGroupPercent", "deliverGrowthPercent"),
        },
        { title: "认定量",width: 100, dataIndex: "deliverAffirmCount", render: (text, record) => renderPopver(text, record, 'deliverAffirmCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "deliverAffirmPercent", "deliverAffirmGrowthPercent"),
        },
        { title: "建群合格量",width: 100, dataIndex: "deliverQualifiedCount", render: (text, record) => renderPopver(text, record, 'deliverQualifiedCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "deliverQualifiedPercent", "deliverQualifiedGrowthPercent"),
        },
      ],
    },
    {
      title: "大区排名明细",
      width: 120,
      dataIndex: "str2",
      // isExtend: true,
      render: (text, record) => (
        <>
          <Button type="link" size="small" onClick={() => getAreaDetail(record)}>
            详情
          </Button>
        </>
      ),
    },
    {
      title: "省区排名明细",
      dataIndex: "str3s",
      width: 120,
      // isExtend: true,
      render: (text, record) => (
        <>
          <Button type="link" size="small" onClick={() => getProvinceDetail(record)}>
            详情
          </Button>
        </>
      ),
    },
  ]);
  const [columns3, setColumns3] = useState([
    { title: "排名", dataIndex: "index",  fixed: 'left',width: 50,render: (text, record, index) => index + 1 },
    { title: "大区名称", dataIndex: "bigAreaName",  fixed: 'left',width: 120},
    {
      title: "已分配交车单",
      children: [
        {
          title: "分配量",
          width: 100,
          dataIndex: "distributionCount",
          render: (text, record) => renderPopver(text, record, 'distributionCount')
        },
        { title: "特殊备案量", width: 100,dataIndex: "distributionRemarkCount", render: (text, record) => renderPopver(text, record, 'distributionRemarkCount') },
        { title: "建群量", width: 100,dataIndex: "groupCount" , render: (text, record) => renderPopver(text, record, 'groupCount')},
        {
          title: "建群率（环比）",
          width: 180, 
          render: (text, record) =>
            renderPercent(text, record, "groupPercent", "distributionGrowthPercent"),
        },
        { title: "认定量", width: 100,dataIndex: "distributionAffirmCount", render: (text, record) => renderPopver(text, record, 'distributionAffirmCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "distributionAffirmPercent", "distributionAffirmGrowthPercent"),
        },
        { title: "建群合格量",width: 100, dataIndex: "distributionQualifiedCount", render: (text, record) => renderPopver(text, record, 'distributionQualifiedCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "distributionQualifiedPercent", "distributionQualifiedGrowthPercent"),
        },
      ],
    },
    {
      title: "待交付交车单",
      dataIndex: "str1",
      isExtend: true,
      children: [
        {
          title: "待交付量",
          width: 100,
          dataIndex: "undeliveredCount",
          render: (text, record) => renderPopver(text, record, 'undeliveredCount')
        },
        { title: "特殊备案量",width: 100, dataIndex: "undeliveredRemarkCount", render: (text, record) => renderPopver(text, record, 'undeliveredRemarkCount') },
        { title: "建群量",width: 100,dataIndex: "undeliveredGroupCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "建群率（环比）",
          width: 180, 
          dataIndex: "groupPercent",
          render: (text, record) =>
            renderPercent(text, record, "undeliveredGroupPercent", "undeliveredGrowthPercent"),
        },
        { title: "认定量", width: 100,dataIndex: "undeliveredAffirmCount", render: (text, record) => renderPopver(text, record, 'undeliveredAffirmCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "undeliveredAffirmPercent", "undeliveredAffirmGrowthPercent"),
        },
        { title: "建群合格量",width: 100, dataIndex: "undeliveredQualifiedCount", render: (text, record) => renderPopver(text, record, 'undeliveredQualifiedCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "undeliveredQualifiedPercent", "undeliveredQualifiedGrowthPercent"),
        },
      ],
    },
    {
      title: "交付完成交车单",
      children: [
        {
          title: "交付量",
          width: 100,
          dataIndex: "deliverCount",
          render: (text, record) => renderPopver(text, record, 'deliverCount')
        },
        { title: "特殊备案量",width: 100, dataIndex: "deliverRemarkCount", render: (text, record) => renderPopver(text, record, 'deliverRemarkCount') },
        { title: "建群量",width: 100, dataIndex: "deliverGroupCount", render: (text, record) => renderPopver(text, record, 'deliverGroupCount') },
        {
          title: "建群率（环比）",
          width: 180, 
          render: (text, record) =>
            renderPercent(text, record, "deliverGroupPercent", "deliverGrowthPercent"),
        },
        { title: "认定量", width: 100,dataIndex: "deliverAffirmCount", render: (text, record) => renderPopver(text, record, 'deliverAffirmCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "deliverAffirmPercent", "deliverAffirmGrowthPercent"),
        },
        { title: "建群合格量", width: 100,dataIndex: "deliverQualifiedCount", render: (text, record) => renderPopver(text, record, 'deliverQualifiedCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "deliverQualifiedPercent", "deliverQualifiedGrowthPercent"),
        },
      ],
    },
  ]);
  let columns4 = _.cloneDeep({ columns3 }).columns3;
  for (let i = 0; i < columns4.length; i++) {
    if (columns4[i]["title"] == "大区名称") {
      columns4[i]["title"] = "省区";
      columns4[i]["dataIndex"] = "provinceName";
    }
  }
  const [columns2, setColumns2] = useState([
    {
      title: "排名",
      dataIndex: "index",
      width: 40,
      fixed: 'left',
      render: (text, record, index) => <div>{index + 1}</div>,
    },
    {
      title: "用户中心",
      dataIndex: "storeFullName",
      width: 200,
      fixed: 'left',
      render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
    },
    {
      title: "已分配交车单",
      children: [
        {
          title: "分配量",
          width: 100,
          dataIndex: "distributionCount",
          render: (text, record) => renderPopver(text, record, 'distributionCount')
        },
        { title: "特殊备案量", width: 100,dataIndex: "distributionRemarkCount", render: (text, record) => renderPopver(text, record, 'distributionRemarkCount') },
        { title: "建群量",width: 100, dataIndex: "groupCount" , render: (text, record) => renderPopver(text, record, 'groupCount')},
        {
            title: "建群率（环比）",
            width: 180,
            render: (text, record) =>
              renderPercent(text, record, "groupPercent", "distributionGrowthPercent"),
        },
        { title: "认定量", width: 100,dataIndex: "distributionAffirmCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "distributionAffirmPercent", "distributionAffirmGrowthPercent"),
        },
        { title: "建群合格量", width: 100, dataIndex: "distributionQualifiedCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "distributionQualifiedPercent", "distributionQualifiedGrowthPercent"),
        },
      ],
    },
    {
      title: "待交付交车单",
      dataIndex: "1",
      // isExtend: true,
      children: [
        {
          title: "待交付量",
          width: 100,
          dataIndex: "undeliveredCount",
          render: (text, record) => renderPopver(text, record, 'undeliveredCount')
        },
        { title: "特殊备案量",width: 100, dataIndex: "undeliveredRemarkCount", render: (text, record) => renderPopver(text, record, 'undeliveredRemarkCount') },
        { title: "建群量", width: 100,dataIndex: "undeliveredGroupCount" , render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount')},
        {
            title: "建群率（环比）",
            width: 180,
            render: (text, record) =>
              renderPercent(text, record, "undeliveredGroupPercent", "undeliveredGrowthPercent"),
        },
        { title: "认定量",width: 100, dataIndex: "undeliveredAffirmCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "undeliveredAffirmPercent", "undeliveredAffirmGrowthPercent"),
        },
        { title: "建群合格量",width: 100, dataIndex: "undeliveredQualifiedCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "undeliveredQualifiedPercent", "undeliveredQualifiedGrowthPercent"),
        },
      ],
    },
    {
      title: "交付完成交车单",
      dataIndex: "222",
      children: [
        {
          title: "交付量",
          width: 100,
          dataIndex: "deliverCount",
          render: (text, record) => renderPopver(text, record, 'deliverCount')
        },
        { title: "特殊备案量", width: 100,dataIndex: "deliverRemarkCount", render: (text, record) => renderPopver(text, record, 'deliverRemarkCount') },
        { title: "建群量", width: 100,dataIndex: "deliverGroupCount" , render: (text, record) => renderPopver(text, record, 'deliverGroupCount')},
        {
            title: "建群率（环比）",
            width: 180,
            render: (text, record) =>
              renderPercent(text, record, "deliverGroupPercent", "deliverGrowthPercent"),
        },
        { title: "认定量",width: 100, dataIndex: "deliverAffirmCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "认定率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "deliverAffirmPercent", "deliverAffirmGrowthPercent"),
        },
        { title: "建群合格量", width: 100, dataIndex: "deliverQualifiedCount", render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount') },
        {
          title: "建群合格率（环比）",
          width: 180,
          render: (text, record) =>
            renderPercent(text, record, "deliverQualifiedPercent", "deliverQualifiedGrowthPercent"),
        },
      ],
    },
  ]);

  useEffect(() => {
    getGroupBuildingTrendChart();
  }, [value]);

  useEffect(() => {
    getGroupBuildingStatistics();
    getStoreStatistical();
    getUserStatistical();
    getQwStoreGroupStatistic();
  }, []);

  const renderDOM = [
    {
      title: "建群统计",
      subtitle: "数据源：已分配且未退订的交车单",
      themeColor: "#4577FF",
      authority: "GROUP_BUILDING_STATISTICS",
      loading: Data3,
      ColSpan: 24,
      content: <StatisticsTable className="groupBuildStc" type={1} columns={columns1} dataSource={Data3} />,
      extended: (
        <div>
          {
            roleJudgment(userInfo, 'GROUP_BUILDING_STATISTICS_EXPORT') ?
            <a style={{ marginRight: 0 }} onClick={() => exportGroup()}>
              导出
            </a> : null
          }
          {/* <a onClick={() => setVisible4(true)}>查看更多</a> */}
          <ExtendedColumn setColums={setColumns1} columns={columns1} />
        </div>
      ),
    },
    // {
    //   title: "交付建群趋势图",
    //   subtitle: "数据源：交车完成且已建群的交车单",
    //   themeColor: "#C04EFF",
    //   authority: "DELIVERY_GROUP_BUILDING_TREND_CHART",
    //   loading: Data5,
    //   ColSpan: 12,
    //   content: <GroupBuildingTrendChart colors={["#5470C6", "#91CC75", "#EE6666"]} data={Data5} />,
    //   extended: (
    //     <RangePicker
    //       value={hackValue || value}
    //       allowClear={false}
    //       disabledDate={disabledDate}
    //       onCalendarChange={(val) => setDates(val)}
    //       onChange={(val) => setValue(val)}
    //       onOpenChange={onOpenChange}
    //     />
    //   ),
    // },
    {
      title: "昨日用户中心未建群排行榜",
      subtitle: "数据源：已分配交付专员且未建群的交车单",
      themeColor: "#FF6539",
      authority: "USER_DOES_NOT_CREATE_GROUP",
      loading: Data1,
      ColSpan: 12,
      statisticalTime: `统计截止${moment(new Date())
        .subtract(1, "days")
        .format("YYYY-MM-DD 23:59:59")}`,
      content: <HorizontalHistogram data={Data1} type={1} />,
      extended: (
        <div>
          {/* <a style={{marginRight: 20}} onClick={() => exportGroupBuild()}>导出</a> */}
          <a onClick={() => setVisible3(true)}>查看更多</a>
          {/* <ExtendedColumn setColums={setColumns2} columns={columns2} /> */}
        </div>
      ),
    },
    {
      title: "昨日交付专员未建群排行榜",
      subtitle: "数据源：已分配交付专员且未建群的交车单",
      themeColor: "#FD3B6B",
      authority: "DELIVERY_SPECIALIST_DOES_NOT_BUILD_GROUP",
      loading: Data2,
      ColSpan: 12,
      statisticalTime: `统计截止${moment(new Date())
        .subtract(1, "days")
        .format("YYYY-MM-DD 23:59:59")}`,
      content: <HorizontalHistogram data={Data2} type={2} />,
      extended: <a onClick={() => setVisible2(true)}>查看更多</a>,
    },
    {
      title: "昨日用户中心建群排行榜",
      subtitle: "数据源：已分配且未退订的交车单",
      themeColor: "#05BDE3",
      authority: "USER_GROUP_BUILDING",
      loading: Data4,
      ColSpan: 24,
      statisticalTime: `统计截止${moment(new Date())
        .subtract(1, "days")
        .format("YYYY-MM-DD 23:59:59")}`,
      content: (
        <StatisticsTable className="yesterDayCenterGroupBuildRank" type={2} columns={columns2} dataSource={Data4} scroll={{ y: 330 }} />
      ),
      extended: (
        <div>
           {
                roleJudgment(userInfo, 'USER_GROUP_BUILDING_EXPORT') ?
              <a style={{ marginRight: 20 }} onClick={() => exportStoreCenter()}>
                导出
              </a> : null
          }
          <a onClick={() => setVisible1(true)}>查看更多</a>
          <ExtendedColumn setColums={setColumns2} columns={columns2} />
        </div>
      ),
    },
    
  ];

  return (
    <div className="GropuBuildingData">
      <div className="content">
        
        <Row gutter={[24, 24]}>
          {renderDOM.map((item, index) => {
            return roleJudgment(userInfo, item.authority) ? (
                <ChartContainer key={index} {...item}>
                  {item.content}
                </ChartContainer>
            ) : null;
          })}
          </Row>
      </div>
      {visible1 && (
        <StoreGroupStatistic
          title="昨日用户中心建群列表"
          visible={visible1}
          renderPopver={renderPopver}
          renderPercent={renderPercent}
          onCancel={() => setVisible1(false)}
        />
      )}
      {visible3 && (
        <PageStoreStatistical
          title="昨日用户中心未建群列表"
          visible={visible3}
          onCancel={() => setVisible3(false)}
        />
      )}
      {visible2 && (
        <PageUserStatistical
          title="昨日交付专员未建群列表"
          visible={visible2}
          onCancel={() => setVisible2(false)}
        />
      )}
      {
        <Modal
          title={
            <div>
              <div>{`${currentRow.statisticTime}建群数据统计（大区）`}</div>
              {
                roleJudgment(userInfo, 'GROUP_BUILDING_STATISTICS_BIGAREA_EXPORT') ?
                <Button style={{ marginTop: 10 }} type="primary" onClick={exportBigArea}>
                导出
                </Button>: null
              }
            </div>
          }
          visible={visible4}
          onCancel={() => setVisible4(false)}
          maskClosable={false}
          footer={null}
          width={'90%'}
          // size="small"
        >
          <Table className="groupBuildModal" size={'small'} pagination={false} scroll={{x: 1500, y: 600}} dataSource={Data6} columns={columns3} />
        </Modal>
      }
      {
        <Modal
          wrapClassName="groupBuildModal" 
          title={
            <div>
              <div>{`${currentRow.statisticTime}建群数据统计（省区）`}</div>
              {
                roleJudgment(userInfo, 'GROUP_BUILDING_STATISTICS_PROVINCE_EXPORT') ?
              <Button style={{ marginTop: 10 }} type="primary" onClick={exportProvince}>
                导出
              </Button> : null
              }
            </div>
          }
          visible={visible5}
          onCancel={() => setVisible5(false)}
          maskClosable={false}
          footer={null}
          width={'90%'}
        >
          <Table size={'small'} pagination={false} scroll={{x:900 ,y: 600}} dataSource={Data7} columns={columns4} />
        </Modal>
      }
    </div>
  );
};
export default GroupBuildingData;
