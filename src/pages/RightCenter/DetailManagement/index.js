import React, { useEffect, useState, useRef } from 'react'
// import styled from 'styled-components'
import { useSelector } from 'react-redux'

import { Table, message, Modal, Button, Row, Col, Divider, Spin } from 'antd'
import './index.less'
import { post, get, axiosDelete } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { roleJudgment } from '@/utils/authority'
import moment from 'moment'
import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import { setStorage, getStorage } from '@/utils/Storage'
import ExportModal from './ExportModal'
import { fileDown, calcPageNo } from '@/utils'
import styles from '../antd.module.less'
import publicQueryStyle from '../tableLayout.module.less'
// const { TabPane } = Tabs;

// const TablePanel = styled(Table)`
//   width: 100%;
//   .status {
//     min-width: 40px;
//     display: inline-block;
//     vertical-align: middle;
//     text-align: center;
//     padding: 0 8px;
//   }
// `
const getPageStorage = (key) => {
    let res = getStorage(window.location.hash, true)
    return res && res[key] ? res[key] : null
}

const { confirm } = Modal;
const DetailManagement = (props) => {
    const tableRef = useRef()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const [loading, setLoading] = useState(false)
    const [dataSource, changeDataSource] = useState([])
    const [isCatch, setisCatch] = useState(true)
    const [current, changeCurrent] = useState(isCatch && getPageStorage('pagination') ? getPageStorage('pagination').current : 1)
    const [pageSize, changePageSize] = useState(isCatch && getPageStorage('pagination') ? getPageStorage('pagination').pageSize : 10)
    const [total, changeTotal] = useState(0)
    const [tableHeight, setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({})
    const [sortCodeList, setSortCode] = useState([])
    const [ManageInterests, setManageInterests] = useState([])
    const [dictData, setDictData] = useState([])
    const [showExport, setShowExport] = useState(false)
    const [effectTimeFrame, setEffectTimeFrame] = useState(null)

    // todo 权益分类
    useEffect(() => {
        get(allUrl.DetailedManageInterests.getSortList).then(res => {
            if (res.success) {
                res.resp.forEach((item, index) => {
                    item.key = index + 1
                    item.value = item.code
                })
                setSortCode(res.resp)
            } else {
                // message.error(res.msg)
            }
        })

        initEffectTimeFrame()
    }, [])

    //设置搜索区“生效时间”的初始值
    const initEffectTimeFrame = () => {
        const Query = getPageStorage('Query') || {}
        const _val = Query.effectTimeFrame
        if (isCatch) {
            setEffectTimeFrame(_val)
        } else {
            setEffectTimeFrame(null)
        }
    }

    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
        if (isCatch) {
            setStorage({
                Type: window.location.hash,
                pagination: { current, pageSize }
            })
        }
    }
    const resetPage = () => {
        changeCurrent(1)
        changePageSize(10)
    }
    const onSearch = (values) => {
        if (values.beginTime && values.beginTime.length) {
            values.fixedBeginTime = moment(values.beginTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.fixedEndTime = moment(values.beginTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.beginTime
        }
        if (values.createTime) {
            values.createTime = moment(values.createTime).format('YYYY-MM-DD')
        }
        setDefaultQuery(values)
        resetPage()
    }
    const initPage = (flag) => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if (!flag) {
            h = winH - 47 - 54 - 345
        } else {
            h = winH - 47 - 54 - 400
        }
        setTableHeight(h)
    }
    useEffect(() => {
        initPage()
    }, [])
    // useEffect(() =>{
    //     let ldjType = sessionStorage.getItem('createType')
    //       console.log('ldjType',ldjType)
    //       if(ldjType =='Add'){
    //         console.log('addddddddddd')
    //         // window.location.reload()
    //       }
    // },[])

    const LookAt = (record) => {
        let data = {
            id: record.id,
            detailNo: record.detailNo,
            originDetailNo: record.originDetailNo,
            Type: 'LookAt',
            title: '权益明细详情',
        }
        UniversalOpenWindow({
            JumpUrl: '/RightCenter/DetailManagementDetail', data, history: props.history
        })
    }
    // 编辑跳转
    const EditAt = (record) => {
        let data = {
            id: record.id,
            Type: 'edit',
            title: '编辑权益明细',
        }
        UniversalOpenWindow({
            JumpUrl: '/RightCenter/DetailManagementCreate', data, history: props.history
        })
    }
    // 删除
    const DeteAt = (record) => {
        const { total, current, pageSize, setCurrent } = tableRef.current
        const _current = calcPageNo(total, current, pageSize, 1)

        get(allUrl.DetailedManageInterests.ableToDelete, { detailNo: record.detailNo }).then(res => {
            if (res.success) {
                confirm({
                    title: '确认删除该权益明细吗？删除后不可恢复！',
                    onOk() {
                        axiosDelete(`${allUrl.DetailedManageInterests.canDelete}${record.detailNo}`).then(res => {
                            if (res.success) {
                                message.success('删除成功！')
                                if (_current < current) {
                                    //删除最后1页最后1条数据，查询table上一页数据
                                    setCurrent(_current)
                                } else {
                                    tableRef.current.getTableData()
                                }
                            }
                        })
                    },
                    onCancel() {
                        console.log('Cancel');
                    },
                });
            } else {
                // message.error(res.msg)
            }
        })
    }

    //导出弹窗
    const onExportHandle = () => {
        setShowExport(true)
    }

    const handleCancel = () => {
        setShowExport(false)
    }

    //导出弹窗提交
    const submitHandle = (data) => {
        setLoading(true)
        post(allUrl.DetailedManageInterests.detailExcelExport, { ...defaultQuery, ...{ fields: data } }, { responseType: "blob" }).then(res => {
            if (res) {
                fileDown(res, '权益明细管理')
                setLoading(false)
            } else {
                setLoading(false)
            }
        }).catch(err => {
            setLoading(false)
        })
    }


    let searchList = [
        // { label: '权益名称', name: 'name', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '权益明细名称', name: 'name', type: 'Input', placeholder: '请输入权益明细名称', colSpan: 6 },
        { label: '权益明细编码', name: 'detailNo', type: 'Input', placeholder: '请输入权益明细编码', colSpan: 6 },
        {
            label: '生效时间', name: 'effectTimeFrame', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '固定时间', value: 1 },
                { name: '相对时间', value: 0 },
            ],
            onChange: (val) => setEffectTimeFrame(val),
        },
        {
            label: '具体时间', name: 'beginTime', type: 'RangePicker', placeholder: ['开始时间', '截止时间'], colSpan: 6,
            paramProps: { beginTimeKey: 'fixedBeginTime', endTimeKey: 'fixedEndTime' },
            vhide: effectTimeFrame !== 1,
        },
        { label: '具体时间', name: 'relativeTime', type: 'Select', placeholder: '请选择', colSpan: 6, data: dictData['equity_relativeTime'] || [], vhide: effectTimeFrame !== 0 },
        { label: '创建时间', name: 'createTime', type: 'DatePicker', placeholder: '请选择', colSpan: 6 },
        {
            label: '权益分类', name: 'sortCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: sortCodeList || []
        },
        {
            label: '权益类别', name: 'typeCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: ManageInterests
        },
        {
            label: '权益业务场景', name: 'businessScene', type: 'Select', placeholder: '请选择', colSpan: 6, data: dictData['equity_business_scene']?.filter(item => item.entryValue !== '3')
        },
        // {
        //     label: '权益车型', name: 'carAttr', type: 'Select', placeholder: '请选择', colSpan: 6, data: DictData || []
        // },
    ]
    const columns = [
        // {title: '序号', dataIndex: 'index', fixed: 'left', width: 70, render: (text, record, index) => <div style={{ textAlign: 'center' }}>{index + 1}</div> },
        { title: '权益明细编码', dataIndex: 'detailNo', width: 100, fixed: 'left' },
        {
            title: '权益明细名称', dataIndex: 'name', width: 260, render: (text) => {
                if (text) {
                    return <div>{text}</div>
                } else {
                    return <div>
                        {'—'}
                    </div>
                }
            }
        },
        {
            title: '权益业务场景', dataIndex: 'businessSceneName', width: 120, render: (text) => {
                if (text) {
                    return <div>{text}</div>
                } else {
                    return <div>
                        {'—'}
                    </div>
                }
            }
        },
        {
            title: '权益分类', dataIndex: 'sortCodeName', width: 100, render: (text) => {
                if (text) {
                    return <div style={{ whiteSpace: 'nowrap' }}>{text}</div>
                } else {
                    return <div>
                        {'—'}
                    </div>
                }
            }
        },
        {
            title: '权益类别', dataIndex: 'typeCodeName', width: 100, render: (text) => {
                if (text) {
                    return text
                } else {
                    return <div>
                        {'—'}
                    </div>
                }
            }
        },
        { title: '创建时间', dataIndex: 'createTime', width: 180, render: text => text ? <div style={{ whiteSpace: 'nowrap' }}>{moment(text).format('YYYY年MM月DD日 HH:mm:ss')}</div> : '' },
        {
            title: '生效时间', dataIndex: 'relativeTimeName', width: 120, render: (text, record) => {
                if (text) {
                    return text
                } else {
                    return <div style={{ whiteSpace: 'nowrap' }}>
                        {
                            record.fixedBeginTime && record.fixedEndTime ? <>{`${moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss')}~`}<br></br>{moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')}</> : '-'
                        }
                    </div>
                }
            }
        },
        {
            title: '车辆属性', dataIndex: 'carAttrName', width: 110, render: (text) => {
                if (text) {
                    return <div style={{ whiteSpace: 'nowrap' }}>{text}</div>
                } else {
                    return <div>
                        {'—'}
                    </div>
                }
            }
        },
        {
            title: '业务编码', dataIndex: 'businessCodeName', width: 110, render: (text) => {
                if (text) {
                    return <div style={{ whiteSpace: 'nowrap' }}>{text}</div>
                } else {
                    return <div>
                        {'—'}
                    </div>
                }
            }
        },
        {
            title: '操作', width: 120, fixed: 'right', dataIndex: 'Operation', render: (text, record) => (<div style={{ whiteSpace: 'nowrap' }}>
                {roleJudgment(userInfo, 'DETAIL_MANAGEMENT_DETAIL') ?
                    <Button type='link' size='small' onClick={() => LookAt(record)}>详情</Button> : null
                }
                {roleJudgment(userInfo, 'DETAIL_MANAGEMENT_DETAIL') && roleJudgment(userInfo, 'DETAIL_MANAGEMENT_EDIT') ? <Divider type='vertical' /> : null}
                {roleJudgment(userInfo, 'DETAIL_MANAGEMENT_EDIT') ?
                    <Button type='link' size='small' onClick={() => EditAt(record)} >编辑 </Button> : null
                }
                {roleJudgment(userInfo, 'DETAIL_MANAGEMENT_DETE') && roleJudgment(userInfo, 'DETAIL_MANAGEMENT_EDIT') ? <Divider type='vertical' /> : null}
                {roleJudgment(userInfo, 'DETAIL_MANAGEMENT_DETE') ?
                    <Button type='link' size='small' danger onClick={() => DeteAt(record)} >删除 </Button> : null
                }
            </div>)
        }
    ]

    //查询数据字典
    const getEntryLists = (codes) => {
        get(allUrl.common.entryLists, { codes }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setDictData(Dt || {})
            }
        })
    }

    useEffect(() => {
        get(allUrl.DetailedManageInterests.getTypeList).then(res => {
            if (res.success) {
                res.resp.forEach(item => {
                    // item.name = item.name
                    item.value = item.code
                })
                setManageInterests(res.resp)
            } else {
                // message.error(res.msg)
            }
        })

        getEntryLists('equity_relativeTime, equity_business_scene');

    }, [])

    return (
        <Spin spinning={loading}>
            <div className={`detail-PublicList ${publicQueryStyle.PublicList}`}>
                <PublicTableQuery
                    isCatch={true}
                    isFormDown={false}
                    onSearch={onSearch}
                    searchList={searchList}
                    resetCb={() => setEffectTimeFrame(null)}
                />
                {/* <FormQuery initPage={initPage} onSearch={onSearch} resetPage={resetPage} searchList={searchList} defaultQuery={defaultQuery} /> */}
                <div className="tableData">
                    <Row className='tableTitle'>
                        <Col className='text' style={{ color: 'black', fontSize: 20 }}>
                            <span style={{ marginRight: '20px' }}>权益明细列表</span>
                            {roleJudgment(userInfo, 'DETAIL_MANAGEMENT_EXPORT') ? <Button type="primary" onClick={onExportHandle}>导出</Button> : null}
                        </Col>
                        {roleJudgment(userInfo, 'DETAIL_MANAGEMENT_BUILD') ?
                            <Col className='bts'>
                                <Button type='primary' icon={<PlusOutlined />} onClick={() => {
                                    let data = {
                                        clueId: 0,
                                        Type: 'Add',
                                        title: '创建权益'
                                    }
                                    UniversalOpenWindow({
                                        JumpUrl: '/RightCenter/DetailManagementCreate',
                                        data,
                                        history: props.history
                                    })
                                }}>创建</Button></Col> : null
                        }
                    </Row>
                    <PublicTable
                        isCatch={true}
                        ref={tableRef}
                        type={6}
                        rowSelection={false}
                        defaultQuery={defaultQuery}
                        url={allUrl.DetailedManageInterests.queryDetailList}
                        columns={columns}
                        className={styles["blue-table"]}
                    />
                </div>
                <ExportModal
                    showExport={showExport}
                    handleCancel={handleCancel}
                    submitHandle={submitHandle}
                />
            </div>
        </Spin>
    )
}
export default DetailManagement
