import "./index.less";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Table, message, Tabs, Divider, Popconfirm, Alert } from "antd";
import moment from "moment";
import PublicTableQuery from "@/components/Public/PublicTableQuery";
import PublicTooltip from "@/components/Public/PublicTooltip";
import { post, get } from "../../../utils/request";
import allUrl from "../../../utils/url";
import SendMessage from "./components/SendMessage";
import { roleJudgment } from "@/utils/authority";
import { But<PERSON>, Modal, Tooltip } from "antd";
const { TabPane } = Tabs;

const ClueList = (props) => {
  const userInfo =
    useSelector((state) => state.common).userInfo ||
    JSON.parse(localStorage.getItem("userInfo")) ||
    {};
  const [loading, setLoading] = useState(false);
  const [loading2, setLoading2] = useState(false);
  const [entryList, setEntryList] = useState({});
  const [dataSource, changeDataSource] = useState([]);
  const [dataSource2, changeDataSource2] = useState([]);
  const [receiverDataSource, changeReceiverDataSource] = useState({});
  const [current, changeCurrent] = useState(1);
  const [pageSize, changePageSize] = useState(10);
  const [total, changeTotal] = useState(0);
  const [current2, changeCurrent2] = useState(1);
  const [pageSize2, changePageSize2] = useState(10);
  const [total2, changeTotal2] = useState(0);
  const [tableHeight, setTableHeight] = useState(0);
  const [tabsKey, setTabsKey] = useState(sessionStorage.getItem("CarrierListTabsKey") || "1");
  const [Type, setType] = useState("ADD");
  const [ID, setID] = useState("");
  const [defaultQuery, setDefaultQuery] = useState({
    // lastFollowTime:[moment().subtract(1,'months'),moment()],
    // createTime: [moment().subtract(1, 'months'), moment()],
  });
  const [defaultQuery2, setDefaultQuery2] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportVisible, setExportVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [isModalVisible2, setIsModalVisible2] = useState(false);
  const [exportLoading2, setExportLoading2] = useState(false);
  const [exportVisible2, setExportVisible2] = useState(false);
  const [confirmLoading2, setConfirmLoading2] = useState(false);
  const [fromTab, setFromTab] = useState(0)
  const [currentRow, setCurrentRow] = useState({})
  const [editorFlag, setEditorFlag] = useState(false);
  const showModal = () => {
    setIsModalVisible(true);
  };
  const showModal2 = () => {
    setIsModalVisible2(true);
  };
  const handleOk = () => {
    setIsModalVisible(false);
  };
  const handleCancel = () => {
    setID('')
    setIsModalVisible(false);
  };
  const handleCancel2 = () => {
    setID('')
    setIsModalVisible2(false);
  };
  // 导出回执名单
  const ExportExcel = () => {
    setConfirmLoading(true);
    setExportLoading(true);
    get(
      allUrl.MessageManage.exportReceipt + "/" + ID,
      {},
      {
        responseType: "blob",
      }
    ).then((res) => {
      if (res) {
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        let currentTime = moment(new Date()).format('YYYY-MM-DD')
                let name = currentRow.title.slice(0,20)
                name = name + '阅读数据' + currentTime
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容ie
          window.navigator.msSaveBlob(blob, name + ".xlsx");
        } else {
          let downloadElement = document.createElement("a");
          let href = window.URL.createObjectURL(blob); //创建下载的链接
          downloadElement.href = href;
          let date = new Date();
          downloadElement.download = name + ".xlsx"; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放掉blob对象
        }
        message.success("导出成功！");
      } else {
        // message.error(res.msg)
      }
      setExportLoading(false);
      setExportVisible(false);
      setConfirmLoading(false);
    });
  };
  // 导出回执名单
  const ExportExcel2 = () => {
    setConfirmLoading2(true);
    setExportLoading2(true);

    get(allUrl.MessageManage.exportReceipt2 + "/" + ID, {}).then((res) => {
      if (res.success) {
        window.location.href = res.resp[0].url;
        message.success("导出成功！");
      } else {
        // message.error(res.msg)
      }

      setExportLoading2(false);
      setExportVisible2(false);
      setConfirmLoading2(false);
    });
  };
  const PageChange = (current, pageSize) => {
    console.log("current", current, pageSize, tabsKey);
    tabsKey == "2" ? changeCurrent(current) : changeCurrent2(current);
    tabsKey == "2" ? changePageSize(pageSize) : changePageSize2(pageSize);
  };
  const onSearch = (values) => {
    // 处理更新时间格式
    if (values.groupCreateTime && values.groupCreateTime.length) {
      values.beginTime = moment(values.groupCreateTime[0]).format("YYYY-MM-DD HH:mm:ss");
      values.endTime = moment(values.groupCreateTime[1]).format("YYYY-MM-DD HH:mm:ss");
      delete values.groupCreateTime;
    }

    setDefaultQuery(values);
  };
  const onSearch2 = (values) => {
    // 处理更新时间格式
    if (values.groupCreateTime && values.groupCreateTime.length) {
      values.beginTime = moment(values.groupCreateTime[0]).format("YYYY-MM-DD HH:mm:ss");
      values.endTime = moment(values.groupCreateTime[1]).format("YYYY-MM-DD HH:mm:ss");
      delete values.groupCreateTime;
    }

    setDefaultQuery2(values);
  };

  // 获取回执名单数据
  const CheckReceiptDetail = (record) => {
    setLoading(true);
    setID(record.id);

    setCurrentRow(record)
    get(allUrl.MessageManage.getReceiptInfo + "/" + record.id).then((res) => {
      if (res.success) {
        res.resp.map((item, index) => (item.key = index + 1));
        changeReceiverDataSource(res.resp[0]);
      } else {
        message.error(res.msg);
      }

      showModal(true);
      setLoading(false);
    });
  };
  // 获取回执名单数据
  const CheckReceiptDetail2 = (record) => {
    setLoading(true);
    setID(record.id);
    setCurrentRow(record)
    get(allUrl.MessageManage.getReceiptInfo2 + "/" + record.id).then((res) => {
      if (res.success) {
        res.resp.map((item, index) => (item.key = index + 1));
        changeReceiverDataSource(res.resp[0]);
      } else {
        message.error(res.msg);
      }

      showModal2(true);
      setLoading2(false);
    });
  };
  const LookAt = (record) => {
    setID(record.id);
    setFromTab(tabsKey) // 记录从哪个tab跳转来的，便于区分新旧消息列表
    setTabsKey("1");
    setType("EDIT");
  };
  const CheckDetail = (record) => {
    setFromTab(tabsKey) // 记录从哪个tab跳转来的，便于区分新旧消息列表
    setID(record.id);
    setTabsKey("1");
    setType("CHECK");
  };
  const Del = (record) => {
    post(allUrl.MessageManage.delMessage + "/" + record.id).then((res) => {
      if (res.success) {
        message.success(res.msg);
        getData();
      } else {
        // message.error(res.msg)
      }
    });
  };
  const RecallMessage = (record) => {
    post(allUrl.MessageManage.withdrawMessage + "/" + record.id).then((res) => {
      if (res.success) {
        message.success(res.msg);
        getData();
      } else {
        // message.error(res.msg)
      }
    });
  };
  const calDiffHours = (StartDate, EndDate) => {
    let temp = moment(EndDate).diff(moment(StartDate), "hours");
    return temp;
  };
  const renderOperation = (text, record) => {
    return (
      <div style={{ color: "#1890ff" }}>
        {roleJudgment(userInfo, "WECOM_MESSAGE_PUSH_SENDOUT_EDIT") &&
        (record.status === 1 || record.status === 3 || record.status === 4) &&
        tabsKey == "2"
          ? [
              <span key={2} style={{ cursor: "pointer" }} onClick={() => LookAt(record)}>
                编辑
              </span>,
              <Divider type="vertical" key={3} />,
            ]
          : null}
        {roleJudgment(userInfo, "WECOM_MESSAGE_PUSH_SENDOUT_EDIT") && record.status === 2
          ? [
              <span key={2} style={{ cursor: "pointer" }} onClick={() => CheckDetail(record)}>
                查看
              </span>,
              <Divider type="vertical" key={3} />,
            ]
          : null}
        {roleJudgment(userInfo, "WECOM_MESSAGE_PUSH_SENDOUT_DEL") &&
        (record.status === 1 || record.status === 3 || record.status === 4) &&
        tabsKey == "2" ? (
          <Popconfirm title="确定删除此条消息吗？" onConfirm={() => Del(record)}>
            <span key={2} style={{ cursor: "pointer", color: "red" }}>
              删除
            </span>
          </Popconfirm>
        ) : null}
        {roleJudgment(userInfo, "WECOM_MESSAGE_PUSH_SENDOUT_WITHDRAW") && record.status === 2 ? (
          calDiffHours(record.sendTime, new Date()) <= 24 ? (
            <Popconfirm title="确定撤回此条消息吗？" onConfirm={() => RecallMessage(record)}>
              <span key={2} style={{ cursor: "pointer" }}>
                撤回消息
              </span>
            </Popconfirm>
          ) : null
        ) : null}
      </div>
    );
  };
  const getData = () => {
    setLoading(true);

    if (tabsKey === "1") {
    } else if (tabsKey === "2") {
      let query = { ...defaultQuery };
      let params = { pageNum: current, pageSize, ...query };
      get(allUrl.MessageManage.getMessageList, { ...params }).then((res) => {
        if (res.success) {
          // 处理回执情况字段，把两个字段包到一个字段中
          res.resp[0].list.map((item, index) => {
            return (res.resp[0].list[index].isReceived = [
              item.alreadyRead,
              item.unRead,
              item.noReceipt,
            ]);
          });

          res.resp[0].list.map((item, index) => (item.key = index + 1));
          changeDataSource(res.resp[0].list);
          changeTotal(res.resp[0].total);
        } else {
          // message.error(res.msg)
        }
        setLoading(false);
      });
    } else if (tabsKey === "3") {
      let query = { ...defaultQuery2 };
      let params = { pageNum: current2, pageSize: pageSize2, ...query };
      get(allUrl.MessageManage.getMessageList2, { ...params }).then((res) => {
        if (res.success) {
          // 处理回执情况字段，把两个字段包到一个字段中
          res.resp[0].list.map((item, index) => {
            return (res.resp[0].list[index].isReceived = [
              item.alreadyRead,
              item.unRead,
              item.noReceipt,
            ]);
          });

          res.resp[0].list.map((item, index) => (item.key = index + 1));
          changeDataSource2(res.resp[0].list);
          changeTotal2(res.resp[0].total);
        } else {
          // message.error(res.msg)
        }
        setLoading(false);
      });
    }
  };

  const tabsCallback = (key) => {
    setEditorFlag(true)
    console.log(key);
    setTabsKey(key);
    changeCurrent(1);
    changePageSize(10);
    sessionStorage.setItem("CarrierListTabsKey", key);
    setID("");
    setType("ADD");
  };
  const initPage = (flag) => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let h = 0;
    if (!flag) {
      h = winH - 47 - 54 - 345;
    } else {
      h = winH - 47 - 54 - 400;
    }
    setTableHeight(h);
  };
  const getSaveSuccess = () => {
    setEditorFlag(false)
    setType("ADD");
    setID("");
    setTabsKey(fromTab || '2');
  };
  useEffect(() => {
    get(allUrl.common.entryLists, { codes: "qw-send-status" }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach((item) => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }
        setEntryList(Dt || {});
      } else {
        // message.error(res.message)
      }
    });
  }, []);
  useEffect(() => {
    initPage();
  }, []);
  useEffect(() => {
    if (userInfo) {
      getData();
    }
  }, [defaultQuery, defaultQuery2, userInfo, current, current2, pageSize, pageSize2, tabsKey]);

  const renderStatusName = (record) => {
    const obj = {
      1: require("@/assets/img/message_draft.png"), //草稿
      2: require("@/assets/img/message_hasBeenSent.png"), //已发送
      3: require("@/assets/img/message_FailnSend.png"), //发送失败
      4: require("@/assets/img/message_Withdrawn.png"), //撤回
    };
    return (
      <div>
        <img src={obj[record.status]} style={{ width: "18px" }} />
        <span style={{ marginLeft: "6px" }}>{record.statusName}</span>
      </div>
    );
  };
  const renderIsReceipt = (status, record) => {
    return status == 2 ? (
      <a onClick={() => CheckReceiptDetail(record)}>查看</a>
    ) : (
      "暂无"
    );
  };
  const renderIsReceipt2 = (isReceived, record) => {
    return isReceived ? (
      isReceived[2] ? (
        <div>
          <span>{isReceived[2]}</span>
        </div>
      ) : (
        <div className="isReceivedWrapper" onClick={() => CheckReceiptDetail2(record)}>
          <Tooltip title="点击查看更多信息">
            <span>已读({isReceived ? isReceived[0] : ""})</span>&nbsp;&nbsp;
            <span style={{ color: "#1890FF" }}>未读({isReceived ? isReceived[1] : ""})</span>
          </Tooltip>
        </div>
      )
    ) : (
      ""
    );
  };
  const InforData = {
    rowKey: (record) => record.id,
    bordered: true,
    dataSource,
    loading,
    scroll: { x: "max-content" },
    columns: [
      {
        title: "状态",
        dataIndex: "statusName",
        width: 100,
        render: (text, record) => renderStatusName(record),
      },
      { title: "消息", dataIndex: "title", width: 100 },
      {
        title: "阅读情况",
        dataIndex: "status",
        width: 120,
        render: (text, record) => renderIsReceipt(text, record),
      },
      {
        title: "创建时间",
        dataIndex: "createTime",
        width: 160,
        render: (text) => (text ? moment(text).format("YYYY-MM-DD HH:mm:ss") : ""),
      },
      { title: "创建人", dataIndex: "createName", width: 100 },
      { title: "创建人所属组织", dataIndex: "organizationName", width: 140 },
      {
        title: "更新时间",
        dataIndex: "msgUpdateTime",
        width: 160,
        render: (text) => (text ? moment(text).format("YYYY-MM-DD HH:mm:ss") : ""),
      },
      { title: "最后更新人", dataIndex: "updateName", width: 90 },
      {
        title: "操作",
        width: 150,
        fixed: "right",
        dataIndex: "Operation",
        render: (text, record) => renderOperation(text, record),
      },
    ],
    pagination: {
      pageSize: pageSize,
      onChange: PageChange,
      current: current,
      total: total,
      showTotal: () => `共${total}条，${pageSize}条/页`,
      showSizeChanger: true,
      showQuickJumper: true,
      onShowSizeChange: PageChange,
    },
    rowSelection: null,
  };

  const InforData2 = {
    rowKey: (record) => record.id,
    bordered: true,
    dataSource: dataSource2,
    loading2,
    scroll: { x: "max-content" },
    columns: [
      {
        title: "状态",
        dataIndex: "statusName",
        width: 100,
        render: (text, record) => renderStatusName(record),
      },
      { title: "消息", dataIndex: "title", width: 100 },
      {
        title: "回执情况",
        dataIndex: "isReceived",
        width: 120,
        render: (isReceived, record) => renderIsReceipt2(isReceived, record),
      },
      {
        title: "创建时间",
        dataIndex: "createTime",
        width: 160,
        render: (text) => (text ? moment(text).format("YYYY-MM-DD HH:mm:ss") : ""),
      },
      { title: "创建人", dataIndex: "createName", width: 100 },
      { title: "创建人所属组织", dataIndex: "organizationName", width: 140 },
      {
        title: "更新时间",
        dataIndex: "updateTime",
        width: 160,
        render: (text) => (text ? moment(text).format("YYYY-MM-DD HH:mm:ss") : ""),
      },
      { title: "最后更新人", dataIndex: "updateName", width: 90 },
      {
        title: "操作",
        width: 150,
        fixed: "right",
        dataIndex: "Operation",
        render: (text, record) => renderOperation(text, record),
      },
    ],
    pagination: {
      pageSize: pageSize2,
      onChange: PageChange,
      current: current2,
      total: total2,
      showTotal: () => `共${total2}条，${pageSize2}条/页`,
      showSizeChanger: true,
      showQuickJumper: true,
      onShowSizeChange: PageChange,
    },
    rowSelection: null,
  };

  let searchList = [
    { label: "消息", name: "title", type: "Input", placeholder: "请输入", colSpan: 6 },
    {
      label: "状态",
      name: "status",
      type: "Select",
      placeholder: "请选择",
      colSpan: 6,
      data: entryList["qw-send-status"] || [],
    },
    { label: "创建人", name: "createName", type: "Input", placeholder: "请输入", colSpan: 6 },
    { label: "更新时间", name: "groupCreateTime", type: "RangePicker", colSpan: 6, showTime: true },
  ];
  let searchList2 = [
    { label: "消息", name: "title", type: "Input", placeholder: "请输入", colSpan: 6 },
    {
      label: "状态",
      name: "status",
      type: "Select",
      placeholder: "请选择",
      colSpan: 6,
      data: entryList["qw-send-status"] || [],
    },
    { label: "创建人", name: "createName", type: "Input", placeholder: "请输入", colSpan: 6 },
    { label: "更新时间", name: "groupCreateTime", type: "RangePicker", colSpan: 6, showTime: true },
  ];

  return (
    <div className="CarrierList">
      <Tabs onChange={tabsCallback} activeKey={tabsKey} defaultActiveKey={tabsKey}>
        <TabPane tab="发消息" key="1">
          <div className="tableData">
            <SendMessage
              Type={Type}
              ID={ID}
              fromTab={fromTab}
              setID={setID}
              setType={setType}
              editorFlag={editorFlag}
              setEditorFlag={setEditorFlag}
              getSaveSuccess={getSaveSuccess}
            />
          </div>
        </TabPane>
        <TabPane tab="已发送" key="2">
          <div className="tableData">
            <Alert
              message="仅发送时间在24小时内的消息支持撤回。"
              type="error"
              showIcon
              closable
              style={{ width: 500, marginLeft: "24px", marginTop: "24px" }}
            />
            <PublicTableQuery
              tabsKey={tabsKey}
              onSearch={onSearch}
              searchList={searchList}
              defaultQuery={defaultQuery}
            />
            <Table {...InforData} />
            <Modal
              title="阅读列表"
              visible={isModalVisible}
              onOk={ExportExcel}
              onCancel={handleCancel}
              okText="导出记录"
              confirmLoading={confirmLoading}
            >
              <Tabs>
                {/* tabsKey替换为已读名单.length */}
                <TabPane
                  tab={`已读人数（${
                    receiverDataSource.alreadyReadUserNames
                      ? receiverDataSource.alreadyReadUserNames.length
                      : 0
                  }）`}
                  key="1"
                >
                  {receiverDataSource.alreadyReadUserNames ? (
                    <div className="already-read-wrapper">
                      {receiverDataSource.alreadyReadUserNames.length ? (
                        receiverDataSource.alreadyReadUserNames.length > 20 ? (
                          <div className="already-read-list">
                            {receiverDataSource.alreadyReadUserNames.map((item, index) => {
                              return <div key={index + 1}>{item}</div>;
                            })}
                            <Divider />
                            <div className="more-info">更多已读/未读名单可导出查看</div>
                          </div>
                        ) : (
                          <div className="already-read-list">
                            {receiverDataSource.alreadyReadUserNames.map((item, index) => {
                              return <div key={index + 1}>{item}</div>;
                            })}
                          </div>
                        )
                      ) : null}
                    </div>
                  ) : null}
                </TabPane>
                {/* tabsKey替换为未读名单.length */}
                <TabPane
                  tab={`未读人数（${
                    receiverDataSource.unReadUserNames
                      ? receiverDataSource.unReadUserNames.length
                      : 0
                  }）`}
                  key="2"
                >
                  {receiverDataSource.unReadUserNames ? (
                    <div className="already-read-wrapper">
                      {receiverDataSource.unReadUserNames.length ? (
                        receiverDataSource.unReadUserNames.length > 20 ? (
                          <div className="already-read-list">
                            {receiverDataSource.unReadUserNames.slice(0, 20).map((item, index) => {
                              return (
                                <div className="already-read-item" key={index + 1}>
                                  {item}
                                </div>
                              );
                            })}
                            <Divider />
                            <div className="more-info">更多已读/未读名单可导出查看</div>
                          </div>
                        ) : (
                          <div className="already-read-list">
                            {receiverDataSource.unReadUserNames.map((item, index) => {
                              return (
                                <div className="already-read-item" key={index + 1}>
                                  {item}
                                </div>
                              );
                            })}
                          </div>
                        )
                      ) : null}
                    </div>
                  ) : null}
                </TabPane>
              </Tabs>
            </Modal>
          </div>
        </TabPane>
        <TabPane tab="已发送（历史数据）" key="3">
          <div className="tableData">
            <Alert
              message="仅发送时间在24小时内的消息支持撤回。"
              type="error"
              showIcon
              closable
              style={{ width: 500, marginLeft: "24px", marginTop: "24px" }}
            />
            <PublicTableQuery
              tabsKey={tabsKey}
              onSearch={onSearch2}
              searchList={searchList2}
              defaultQuery={defaultQuery2}
            />
            <Table {...InforData2} />
            <Modal
              title="阅读列表"
              visible={isModalVisible2}
              onOk={ExportExcel2}
              onCancel={handleCancel2}
              okText="导出记录"
              confirmLoading={confirmLoading2}
            >
              <Tabs>
                {/* tabsKey替换为已读名单.length */}
                <TabPane
                  tab={`已读人数（${
                    receiverDataSource.alreadyReadUserNames
                      ? receiverDataSource.alreadyReadUserNames.length
                      : 0
                  }）`}
                  key="1"
                >
                  {receiverDataSource.alreadyReadUserNames ? (
                    <div className="already-read-wrapper">
                      {receiverDataSource.alreadyReadUserNames.length ? (
                        receiverDataSource.alreadyReadUserNames.length > 20 ? (
                          <div className="already-read-list">
                            {receiverDataSource.alreadyReadUserNames.map((item, index) => {
                              return <div key={index + 1}>{item}</div>;
                            })}
                            <Divider />
                            <div className="more-info">更多已读/未读名单可导出查看</div>
                          </div>
                        ) : (
                          <div className="already-read-list">
                            {receiverDataSource.alreadyReadUserNames.map((item, index) => {
                              return <div key={index + 1}>{item}</div>;
                            })}
                          </div>
                        )
                      ) : null}
                    </div>
                  ) : null}
                </TabPane>
                {/* tabsKey替换为未读名单.length */}
                <TabPane
                  tab={`未读人数（${
                    receiverDataSource.unReadUserNames
                      ? receiverDataSource.unReadUserNames.length
                      : 0
                  }）`}
                  key="2"
                >
                  {receiverDataSource.unReadUserNames ? (
                    <div className="already-read-wrapper">
                      {receiverDataSource.unReadUserNames.length ? (
                        receiverDataSource.unReadUserNames.length > 20 ? (
                          <div className="already-read-list">
                            {receiverDataSource.unReadUserNames.slice(0, 20).map((item, index) => {
                              return (
                                <div className="already-read-item" key={index + 1}>
                                  {item}
                                </div>
                              );
                            })}
                            <Divider />
                            <div className="more-info">更多已读/未读名单可导出查看</div>
                          </div>
                        ) : (
                          <div className="already-read-list">
                            {receiverDataSource.unReadUserNames.map((item, index) => {
                              return (
                                <div className="already-read-item" key={index + 1}>
                                  {item}
                                </div>
                              );
                            })}
                          </div>
                        )
                      ) : null}
                    </div>
                  ) : null}
                </TabPane>
              </Tabs>
            </Modal>
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};
export default ClueList;
