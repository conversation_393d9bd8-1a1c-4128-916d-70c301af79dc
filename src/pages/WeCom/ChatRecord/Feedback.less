.Feedback {
  .ant-tabs {
    .ant-tabs-nav {
      margin-left: 24px;
    }
  }
  .ant-pagination {
    padding-right: 20px;
  }
  .tableData {
    background-color: white;
    // padding: 24px 24px 72px 24px;

    border-top: solid 24px #f0f2f5;
    border-right: solid 24px #f0f2f5;
    border-bottom: solid 24px #f0f2f5;
    border-left: solid 24px #f0f2f5;
    .ant-table-wrapper {
      // background-color: white;
      .ant-table {
        padding: 24px;
        .ant-table-container {
          border: 0;
          .ant-table-content {
            // overflow: auto hidden;
            .ant-table-thead {
              tr > th,
              tr > td {
                // border: 0;
              }
            }
          }
          .ant-table-pagination {
            margin: 16px 24px;
          }
        }
      }
    }
    .tableTitle {
      padding: 24px 24px 0px 32px;
      justify-content: space-between;
      .text {
        font-size: 20px;
        font-family: PingFangSC, PingFangSC-Medium, sans-serif;
        font-weight: 500;
        text-align: left;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
      }
      .bts {
        .ant-btn {
          margin: 0 7px;
        }
      }
    }
  }
  .PublicList_FormQuery {
    padding-top: 16px;
    padding-left: 24px;
    .ant-col-7 {
      .ant-form-item {
        .ant-form-item-control-input {
          width: 90%;
          .ant-form-item-control-input-content {
            .ant-picker {
              width: 100%;
            }
          }
        }
      }
    }
    .FormQuerySubmit {
      display: flex;
      justify-content: flex-end;
      .operationButtons {
        span {
          color: #1890ff;
          cursor: pointer;
          .anticon {
            margin-left: 6px;
          }
        }
      }
    }
  }
}
.Record {
  margin-left: 18px;
  overflow-y: auto;
  height: 600px;
  .ant-timeline {
    .ant-timeline-item {
      .ant-timeline-item-head-blue {
        background: #1890ff;
      }
      .ant-timeline-item-head-gray {
        background: #bcbcbc;
        color: #bcbcbc;
        border-color: #bcbcbc;
      }
      .ant-timeline-item-content {
        .TimelineItem_Con {
          font-family: PingFangSC, PingFangSC-Regular, sans-serif;
          font-weight: 400;
          margin-top: 10px;
          .time {
            font-size: 14px;
            color: #888888;
            margin-bottom: 0px;
          }
          .vin {
            font-size: 14px;
            color: #333333;
            margin-bottom: 3px;
          }
          .statusCn {
            font-size: 12px;
            color: #333333;
          }
          .bold {
            font-weight: bold;
          }
          .address {
            margin-top: 10px;
          }
        }
      }
    }
  }
  // .more{
  //     font-size: 12px;
  //     font-family: PingFangSC, PingFangSC-Regular, sans-serif;
  //     font-weight: 400;
  //     text-align: center;
  //     color: #b1b1b1;
  //     cursor: pointer;
  //     margin: 20px 0;
  // }
}

.reason-wrap {
  .ant-form-item {
    margin-bottom: 0;
  }
  .ant-input {
    height: 160px;
  }
}
