import React, { useState, memo, useEffect, useRef, createElement } from 'react';
import { useSelector } from 'react-redux';
import {
  Table,
  Modal,
  Form,
  message,
  Row,
  Col,
  Button,
  Select,
  Input,
  Descriptions,
  DatePicker,
  Popconfirm,
  Spin,
  Upload,
  Tag,
  Radio,
  Space,
} from 'antd';
import { post, get, postForm } from '../../../utils/request';
import allUrl from '../../../utils/url';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons';
import styles from '../antd.module.less';
import relevance from './relevance.module.less';
import { getWidth, offsetLeft } from '@/utils/index';
import _ from 'lodash';
import style from './reason.module.less';
import baseURL from '@/baseURL';
import Cookies from 'js-cookie';
import { utilsDict } from '@/utils/utilsDict';
const { Option } = Select;

const limitLegendMap = {
  0: {
    type: '',
    name: '',
    style: '',
  },
  1: {
    type: 'limitLegend',
    name: '行驶',
    style: 'custom-ant-tag-green',
  },
  2: {
    type: 'extenderMileage',
    name: '增程器',
    style: 'custom-ant-tag-yellow',
  },
  null: {
    type: '',
    name: '',
    style: '',
  },
};

const { Search } = Input;
const containExtendTips = '权益包内基础权益优先执行此处选择的生效策略，延保权益优先执行创建明细时选择的延保策略；';
const allExtendTips = '权益包内延保权益优先执行创建明细时选择的延保策略；';
const RelevancePayGiveTable = props => {
  const { handleCancel, visible, title, dataSourceDetail, payDetailContent, giveDetailContent, reloadChangeInfo } =
    props;
  const [dataSource, setDataSource] = useState([]);
  const [form] = Form.useForm();
  const [form1] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const formRef1 = useRef(null);
  const fileRef = useRef([]);
  const [detaillTop, setDetailTOp] = useState({});
  const [reasonStatus, setReasonStatus] = useState('');
  // const [packageErrTips, setPackageErrTips] = useState('')
  // const [carFalseGive, setCarFalseGive] = useState(false)
  const [hasRightButton, setHasRightButton] = useState(false);
  const [hasTime, setHasTime] = useState(false);
  const [buttonDisabled, setButtonDisabled] = useState(true);
  const [fileList, setFileList] = useState([]);
  const [type, setType] = useState('');
  const [packNo, setPackNo] = useState('');

  const [packEffectStrategyValue, setPackEffectStrategyValue] = useState(0);
  const [entryList, setEntryList] = useState({});
  const [packNoExtraTips, setPackNoExtraTips] = useState('');
  const [hasRelativeTime, setHasRelativeTime] = useState(false);
  const onChangeReason = () => {
    const { reason } = form1.getFieldsValue();
    if (reason) {
      setReasonStatus('');
      return true;
    } else {
      setReasonStatus('error');
      return false;
    }
  };
  // 保存权益包关联接口
  const confirm = () => {
    // console.log('点击保存按钮')
    // 赠送权益包关联 上传附件必传
    if (title.type === 'GiveRelation') {
      fileListFilter();
      if (fileList.length < 1) {
        return message.warn('上传附件不能为空');
      }
    }
    if (!onChangeReason()) return;
    const values = form.getFieldsValue();

    // form.validateFields().then(values => {
    // console.log(44444444444, values);

    if (!values.packNo) {
      return message.warn('权益包编码不能为空');
    }
    if (values.packEffectStrategy === 1 && !values.packRelativeTime) {
      return message.warn('相对时间不能为空');
    }
    if (values.packEffectStrategy === 2 && !values.effectTime) {
      return message.warn('开始时间不能为空');
    }
    Modal.confirm({
      title: '确认要保存吗？',
      icon: <ExclamationCircleOutlined />,
      content: '确认是否保存该权益包绑定，一旦保存将无法编辑和修改',
      okText: '确认',
      cancelText: '返回',
      onOk: () => {
        if (values.packEffectStrategy !== 1) {
          getAccountHaveDetailObj();
        } else {
          checkRelativeTime(values.packRelativeTime);
        }
      },
    });
    // })
  };

  //生效策略选择相对时间时，判断账户里的相对时间是否有值
  const checkRelativeTime = val => {
    const { name } = entryList['equity_relativeTime'].filter(i => i.value == val)[0];
    if (!dataSourceDetail.relativeTimeMap?.[val]) {
      Modal.warning({
        title: '提示',
        // icon: <ExclamationCircleOutlined />,
        content: (
          <div>
            关联失败，该账户暂无相对时间的<span style={{ color: 'red' }}>{name}</span>，请核实后再试试吧～
          </div>
        ),
        okText: '我知道了',
        // onOk: () => {
        //     getAccountHaveDetailObj()
        // },
      });
    } else {
      getAccountHaveDetailObj();
    }
  };
  // 保存之前判断是否有权益明细重复
  const getAccountHaveDetailObj = () => {
    const params = {
      type,
      packNo,
      accountNo: dataSourceDetail.accountNo,
    };
    post(allUrl.AccountManagement.getAccountHaveDetailObj, params).then(res => {
      const { success, resp, msg } = res;
      if (success) {
        if (resp && resp.length > 0) {
          // const vin = resp[0].vin
          const detailNoList = resp.map(i => i.detailNo) || [];
          const detailNos = detailNoList.join('、');
          Modal.confirm({
            // title: `该账户vin：<span style="color:#1890FF">${dataSourceDetail.vin}</span>已关联权益明细<span style="color:#1890FF">${detailNos}</span>和权益包内权益明细重复，是否继续关联`,
            title: createElement('div', {}, [
              '该账户vin：',
              createElement(
                'span',
                {
                  style: {
                    color: '#1890FF',
                  },
                },
                dataSourceDetail.vin
              ),
              ' 已关联权益明细 ',
              createElement(
                'span',
                {
                  style: {
                    color: '#1890FF',
                  },
                },
                detailNos
              ),
              ' 和权益包内权益明细重复，是否继续关联?',
            ]),
            icon: <ExclamationCircleOutlined />,
            // content: ,
            okText: '是',
            cancelText: '否',
            onOk: () => {
              onOKLast();
            },
          });
        } else {
          onOKLast();
        }
      }
    });
  };
  // 上传附件 数组过滤
  const fileListFilter = () => {
    let newArr = fileList.filter(item => item.status === undefined);
    if (newArr.length > 0) {
      fileRef.current = newArr;
    }
  };
  const onOKLast = () => {
    const { reason } = form1.getFieldsValue();
    const formValue = form.getFieldsValue();
    const urls = fileList.map(item => item?.response?.resp[0]?.url);
    const auditFileList = fileList.map(item => item?.response?.resp[0]);
    const params = {
      // equityPackageNo: formRef.current.getFieldValue(['packNo']),
      equityPackageNo: formValue.packNo,
      packEffectStrategy: formValue.packEffectStrategy,
      packRelativeTime: formValue.packEffectStrategy === 1 ? formValue.packRelativeTime : undefined,
      // effectTime: moment(formRef.current.getFieldValue(['effectTime'])).format('YYYY-MM-DD HH:mm:ss'),
      effectTime:
        formValue.packEffectStrategy === 2 ? moment(formValue.effectTime).format('YYYY-MM-DD HH:mm:ss') : undefined,
      // endTime: formRef.current.getFieldValue(['endTime']) === undefined ? '' : moment(formRef.current.getFieldValue(['endTime'])).format('YYYY-MM-DD HH:mm:ss'),
      endTime:
        formValue.packEffectStrategy === 2
          ? formValue.endTime === undefined
            ? ''
            : moment(formValue.endTime).format('YYYY-MM-DD HH:mm:ss')
          : undefined,
      vin: dataSourceDetail.vin,
      accountNo: dataSourceDetail.accountNo,
      matchCause: reason,
      packType: type,
      auditFileUrl: urls,
      auditFileList: auditFileList,
    };
    // console.log(params, '保存提交是数据');
    setLoading(true);

    const effectTime = new Date(params.effectTime);
    const endTime = new Date(params.endTime);
    if (endTime && effectTime.getTime() - endTime.getTime() > 0) {
      message.warn('结束时间不能在生效时间之前');
      setLoading(false);
      // post(allUrl.AccountManagement.relationEquityPack,{...params}).then(res=>{
      //     if(res.success){
      //         message.success('提交成功！')
      //         payDetailContent()
      //         giveDetailContent()
      //         console.log(params)
      //         handleCancel()
      //     }else{
      //         // message.error(res.msg)
      //     }
      //     setLoading(false)
      // })
    } else {
      // message.error('结束时间不能在生效时间之前')
      // setLoading(false)

      post(allUrl.AccountManagement.relationEquityPack, { ...params }).then(res => {
        if (res.success) {
          message.success('提交成功！');
          payDetailContent();
          giveDetailContent();
          console.log(params);
          handleCancel();
        } else {
          // message.error(res.msg)
        }
        setLoading(false);
        reloadChangeInfo();
      });
    }
  };
  const getYearUnitName = key => {
    return (
      {
        year: '年',
        month: '月',
        day: '日',
      }[key] || ''
    );
  };

  const layout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 15 },
  };
  const layout2 = {
    wrapperCol: { span: 15 },
  };
  // 文件上传处理
  const beforeUpload = file => {
    const isLt2M = file.size / 1024 / 1024 < 10;
    if (!isLt2M) {
      message.error('上传附件大小不能超过10M，请检查～');
      // return false
    }
    return isLt2M || Upload.LIST_IGNORE;
  };
  const uploadChange = ({ file, fileList, event }) => {
    if (file.status === 'done') {
      setFileList(fileList);
      // fileUploadHandler(file)
    }
  };
  const onRemove = file => {
    fileDeleteHandler(file);
  };
  const fileDeleteHandler = file => {
    const fileId = file.response?.resp[0]?.id;
    let data = new FormData();
    // data.append('fileName', file.name)
    // data.append('vin', dataSourceDetail.vin)
    // data.append('packNo', formRef.current.getFieldValue(['packNo']))
    data.append('id', fileId);

    if (!fileId) {
      setFileList(prev => {
        return prev.filter(item => item.uid !== file.uid);
      });
      return;
    }

    postForm(allUrl.AccountManagement.deleteUploadFile, data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(res => {
      if (res.success) {
        setFileList(prev => {
          return prev.filter(item => item.uid !== file.uid);
        });
        message.success('删除上传附件成功！');
      } else {
        message.warn(res.msg);
      }
    });
  };
  useEffect(() => {
    console.log(fileList);
  }, [fileList]);
  // const fileUploadHandler = (file) => {

  //     let data = new FormData()
  //     data.append('file', file)
  //     data.append('vin', dataSourceDetail.vin)
  //     data.append('packNo', formRef.current.getFieldValue(['packNo']))

  //     postForm(allUrl.AccountManagement.accountUploadFile, data, {
  //         headers: {
  //             'Content-Type': "multipart/form-data"
  //         }
  //     },

  //     ).then(res => {
  //         if (res.success) {
  //             message.success('上传附件成功！')
  //         } else {
  //             message.warn(res.msg)
  //         }
  //     })
  // }
  const abortPromise = promise1 => {
    let abort;
    const promise2 = new Promise((resolve, reject) => {
      abort = reject;
    });
    const p = Promise.race([promise1, promise2]);
    p.abort = abort;
    return p;
  };

  const debouncePromise = (successed, fail, time) => {
    let promise;
    return function (...rest) {
      if (promise && typeof promise.abort === 'function') {
        promise.abort();
      }
      const timeoutPromise = new Promise(resolve => {
        setTimeout(() => {
          resolve(undefined);
        }, time);
      });
      promise = abortPromise(timeoutPromise);
      return promise.then(
        () => {
          return successed(...rest);
        },
        () => {
          return fail(...rest);
        }
      );
    };
  };
  const fail = (...rest) => {
    console.log('由于防抖中断了第一次的请求', rest);
    return Promise.resolve(''); // 忽视它暂时是正确的
  };
  // 权益校验
  const getSnapshotPackObj = async (_type, _packNo) => {
    // 设置当前是 付费或赠送 弹窗的状态，在保存时用到
    setType(_type);
    setPackNo(_packNo);

    const params = {
      packNo: _packNo,
      carType: dataSourceDetail.carType || '',
      type: _type,
      carConfig: dataSourceDetail.carObj?.carConfig || '',
      carOfYear: String(dataSourceDetail.carObj?.carOfYear) || '',
      powerModelCode: dataSourceDetail.carObj?.powerModelCode || '',
    };
    const res = await get(allUrl.AccountManagement.getSnapshotPackObj, { ...params });
    if (res.success) {
      if (res.resp && res.resp.length > 0) {
        let Dtt = res.resp[0];
        setDataSource(Dtt.details);
        setDetailTOp(Dtt);
        setHasRightButton(true);
        const extendDetails = Dtt.details.filter(item => item.businessScene === 2);
        if (extendDetails.length === 0) {
          //包内都是基础明细
          setPackNoExtraTips('');
        } else if (extendDetails.length < Dtt.details.length) {
          //包内包含部分延保权益
          setPackNoExtraTips(containExtendTips);
        } else {
          //包内都是延保权益
          setPackNoExtraTips(allExtendTips);
        }
      }
      if (!res.resp || (res.resp && res.resp.length == 0)) {
        console.log('data  []');
        setHasRightButton(false);
        return Promise.reject(new Error(res.msg));
      }
    } else {
      return Promise.resolve();
    }
    setLoading(false);
  };
  // 付费权益校验
  const payValidator = async (...rest) => {
    console.log('请求后台接口', rest);
    setHasRightButton(false);
    const [rule, value, fn] = rest;
    if (value === '') return Promise.reject('请输入付费权益包编码!');
    return getSnapshotPackObj('2', value);
  };
  // 赠送权益校验
  const giveValidator = async (...rest) => {
    console.log('请求后台接口', rest);
    setHasRightButton(false);
    const [rule, value, fn] = rest;
    if (value === '') return Promise.reject('请输入赠送权益包编码!');
    return getSnapshotPackObj('4', value);
  };
  // 增值权益校验
  const gainValidator = async (...rest) => {
    // console.log('请求后台接口', rest)
    setHasRightButton(false);
    const [rule, value, fn] = rest;
    if (value === '') return Promise.reject('请输入增值权益包编码!');
    return getSnapshotPackObj('5', value);
  };

  // 驾行安心包校验
  const driverValidator = async (...rest) => {
    // console.log('请求后台接口', rest)
    setHasRightButton(false);
    const [rule, value, fn] = rest;
    if (value === '') return Promise.reject('请输入驾行安心包编码!');
    return getSnapshotPackObj('7', value);
  };

  const checkPay = debouncePromise(payValidator, fail, 500);
  const checkGive = debouncePromise(giveValidator, fail, 500);
  const checkGain = debouncePromise(gainValidator, fail, 500);
  const checkDriver = debouncePromise(driverValidator, fail, 500);
  const changeTime = val => {
    !!val ? setHasTime(true) : setHasTime(false);
  };

  const changeRelativeTime = val => {
    !!val ? setHasRelativeTime(true) : setHasRelativeTime(false);
  };

  useEffect(() => {
    console.log(hasRightButton, hasTime);
    // setButtonDisabled(hasRightButton && hasTime && hasRelativeTime)
    if (packEffectStrategyValue === 0) {
      setButtonDisabled(hasRightButton);
    } else if (packEffectStrategyValue === 1) {
      setButtonDisabled(hasRightButton && hasRelativeTime);
    } else if (packEffectStrategyValue === 2) {
      setButtonDisabled(hasRightButton && hasTime);
    }
  }, [hasRightButton, hasTime, hasRelativeTime, packEffectStrategyValue]);
  const getExtraData = file => ({
    vin: dataSourceDetail.vin,
    packNo: formRef.current.getFieldValue(['packNo']),
  });
  const getHeaders = {
    // 'Content-Type': "multipart/form-data",
    appid: 1,
    authorization: Cookies.get('scrm_token'),
  };

  //生效策略V17
  const packEffectStrategyChange = value => {
    setPackEffectStrategyValue(value);
  };
  //获取字典值
  const getEntryLists = () => {
    get(allUrl.common.entryLists, { codes: 'equity_relativeTime' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }

        setEntryList(Dt || {});
      }
    });
  };

  useEffect(() => {
    getEntryLists();
  }, []);
  return (
    <>
      <Spin spinning={loading}>
        <Modal
          open={visible}
          width={getWidth()}
          style={{ left: offsetLeft() }}
          okButtonProps={{ disabled: !buttonDisabled }}
          // cancelButtonProps={{ disabled: hasRightButton }}
          onOk={confirm}
          okText={'保存'}
          onCancel={handleCancel}
          maskClosable={false}
          title={title.title}
        >
          <Form name="add-detailNo" ref={formRef} form={form} className={relevance.relevance}>
            <Row>
              {title.type == 'PayRelation' ? (
                <>
                  <Col span={24}>
                    <Form.Item
                      label="付费权益包编码"
                      className={relevance.packNo}
                      rules={[{ required: true, validator: checkPay }]}
                      name="packNo"
                      extra={packNoExtraTips}
                    >
                      <Search
                        placeholder="请输入付费权益包编码"
                        style={{
                          width: 300,
                        }}
                      />
                    </Form.Item>
                  </Col>
                </>
              ) : title.type == 'GiveRelation' ? (
                <>
                  <Col span={24}>
                    <Form.Item
                      label="赠送权益包编码"
                      className={relevance.packNo}
                      rules={[{ required: true, validator: checkGive }]}
                      name="packNo"
                      extra={packNoExtraTips}
                    >
                      <Search
                        placeholder="请输入赠送权益包编码"
                        style={{
                          width: 300,
                        }}
                      />
                    </Form.Item>
                  </Col>
                </>
              ) : title.type == 'DriverRelation' ? (
                <>
                  <Col span={24}>
                    <Form.Item
                      label="驾行安心包编码"
                      className={relevance.packNo}
                      rules={[{ required: true, validator: checkDriver }]}
                      name="packNo"
                      extra={packNoExtraTips}
                    >
                      <Search
                        placeholder="请输入驾行安心包编码"
                        style={{
                          width: 300,
                        }}
                      />
                    </Form.Item>
                  </Col>
                </>
              ) : (
                <>
                  <Col span={24}>
                    <Form.Item
                      label="增值权益包编码"
                      className={relevance.packNo}
                      rules={[{ required: true, validator: checkGain }]}
                      name="packNo"
                      extra={packNoExtraTips}
                    >
                      <Search
                        placeholder="请输入增值权益包编码"
                        style={{
                          width: 300,
                        }}
                      />
                    </Form.Item>
                  </Col>
                </>
              )}

              {/* <Col span={8}>
                                <Form.Item label='开始时间' name='effectTime' rules={[{ required: true }]}>
                                    <DatePicker showTime onChange={changeTime} />
                                </Form.Item>
                                <p style={{ position: 'absolute', top: '32px', fontSize: '14px', color: 'red', whiteSpace: 'nowrap', left: '37.5%', background: '#fff', margin: '8px 0 16px' }}>权益开始时间以此处填写的权益生效时间为准</p>
                            </Col>
                            <Col span={8}>
                                <Form.Item label='结束时间' name='endTime'>
                                    <DatePicker showTime />
                                </Form.Item>
                            </Col> */}
            </Row>
            <Row style={{ marginBottom: 24, marginLeft: 15 }}>
              <Col style={{ width: 214 }}>
                <Form.Item label="生效策略" name="packEffectStrategy" className={relevance.mt} initialValue={0}>
                  <Radio.Group onChange={e => packEffectStrategyChange(e.target.value)}>
                    <Space direction="vertical" align="start" size={30}>
                      <Radio value={0}>根据明细策略生效</Radio>
                      <Radio value={1}>相对时间</Radio>
                      <Radio value={2}>固定时间</Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={18}>
                <Space direction="vertical" align="start" size={16}>
                  <div style={{ height: 30 }}>
                    <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap', marginLeft: 45 }}>
                      权益包内权益明细生效时间不一致，根据具体的权益明细策略生效；
                    </span>
                  </div>
                  <Space>
                    <Form.Item
                      name="packRelativeTime"
                      className={relevance.relativeTime}
                      style={{ display: 'inline-block', width: 468, marginBottom: 0 }}
                      // noStyle
                      // rules={[{ required: packEffectStrategyValue === 1, message: '请选择相对时间' }]}
                    >
                      <Select
                        style={{ display: 'inline-block' }}
                        allowClear
                        getPopupContainer={triggerNode => triggerNode.parentNode}
                        placeholder="请选择"
                        disabled={packEffectStrategyValue !== 1}
                        onChange={changeRelativeTime}
                      >
                        {entryList['equity_relativeTime'] && entryList['equity_relativeTime'].length
                          ? entryList['equity_relativeTime']
                              .filter(i => i.extendField3 === '1')
                              .map((item, index) => {
                                const NAME =
                                  item.name +
                                  '-' +
                                  (dataSourceDetail.relativeTimeMap?.[item.value] ?? '该账户暂无此时间');
                                return (
                                  <Option key={index} value={Number(item.value)} title={NAME}>
                                    {/* {item.name}-{dataSourceDetail.relativeTimeMap?.[item.value] ?? '该账户暂无此时间'} */}
                                    {NAME}
                                  </Option>
                                );
                              })
                          : null}
                      </Select>
                    </Form.Item>
                    <span style={{ color: 'red', fontSize: 12, whiteSpace: 'nowrap', marginLeft: 10 }}>
                      权益包内所有权益明细生效时间为同一个相对时间
                    </span>
                  </Space>
                  <Space>
                    <Form.Item
                      name="effectTime"
                      label="开始时间"
                      className={relevance.relativeTime}
                      style={{ display: 'inline-block', width: 290, marginBottom: 0 }}
                      // rules={[{ required: packEffectStrategyValue === 2, message: '请选择开始时间！' }]}
                    >
                      <DatePicker
                        showTime
                        placeholder="请选择"
                        disabled={packEffectStrategyValue !== 2}
                        getPopupContainer={triggerNode => triggerNode.parentNode}
                        onChange={changeTime}
                      />
                    </Form.Item>
                    <Form.Item
                      name="endTime"
                      label="结束时间"
                      style={{ display: 'inline-block', width: 290, marginBottom: 0 }}
                    >
                      <DatePicker
                        showTime
                        placeholder="可选择"
                        disabled={packEffectStrategyValue !== 2}
                        getPopupContainer={triggerNode => triggerNode.parentNode}
                      />
                    </Form.Item>
                    <span style={{ color: 'red', fontSize: 12 }}>
                      权益包内所有权益明细生效时间以此处选择的固定时间为准，结束时间未选择，则根据策略生成，选择结束时间，以选择的为准
                    </span>
                  </Space>
                </Space>
              </Col>
            </Row>
          </Form>
          <div style={{ backgroundColor: '#F6F8FB', padding: 20, border: '1px solid #EAECEF' }}>
            <div className="detailPanel">
              <Descriptions>
                <Descriptions.Item label="">
                  <span style={{ color: '#000000', fontSize: 15, fontWeight: 500 }}>权益包信息</span>
                </Descriptions.Item>
              </Descriptions>
              <Descriptions>
                <Descriptions.Item label="权益包名称">{detaillTop.name || '-'}</Descriptions.Item>
                <Descriptions.Item label="宣传时间">
                  {detaillTop.activityBeginTime
                    ? `${moment(detaillTop.activityBeginTime).format('YYYY年MM月DD日 HH:mm:ss')} ~ ${moment(detaillTop.activityEndTime).format('YYYY年MM月DD日 HH:mm:ss')}`
                    : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="是否在APP上展示">
                  {utilsDict('displayMap', detaillTop.display)}
                </Descriptions.Item>
              </Descriptions>

              <Descriptions>
                <Descriptions.Item label="权益描述">{detaillTop.description || '-'}</Descriptions.Item>
              </Descriptions>
              <Descriptions>
                <Descriptions.Item label="">
                  <span style={{ color: '#000000', fontSize: 15, fontWeight: 500 }}>权益明细信息</span>
                  <span style={{ color: 'red', fontSize: 12, marginLeft: 10 }}>
                    (如明细存在内外不一致，生效策略仅适用对内核销策略)
                  </span>
                </Descriptions.Item>
              </Descriptions>
            </div>

            <Table
              className={`${styles['gray-table']} TablePanel`}
              dataSource={dataSource}
              loading={loading}
              rowKey={'id'}
              size="small"
              pagination={false}
              scroll={{ x: 3140 }}
              bordered
            >
              <Table.Column
                title="权益明细编码"
                key="detailNo"
                dataIndex="detailNo"
                width={200}
                fixed="left"
                render={text => text || '-'}
              />
              <Table.Column
                title="权益明细分类"
                key="sortCodeName"
                dataIndex="sortCodeName"
                width={140}
                render={text => text || '-'}
                fixed={'left'}
              />
              <Table.Column
                title="权益明细名称"
                key="name"
                dataIndex="name"
                render={text => text || '-'}
                width={200}
                fixed="left"
              />
              <Table.Column
                title="权益业务场景"
                key="businessSceneName"
                dataIndex="businessSceneName"
                render={text => text || '-'}
                width={140}
              />
              <Table.Column
                title="权益类别"
                key="typeCodeName"
                dataIndex="typeCodeName"
                width={120}
                render={text => text || '-'}
              />
              <Table.Column
                title="车辆属性"
                key="carAttrName"
                dataIndex="carAttrName"
                width={120}
                render={text => text || '-'}
              />
              <Table.Column
                title="权益归属"
                key="belongName"
                dataIndex="belongName"
                width={120}
                render={text => text || '-'}
              />
              <Table.Column
                title="权益抵扣金额"
                key="deduction"
                dataIndex="deduction"
                width={200}
                render={(text, record) => {
                  return record.payAmount && record.deduction ? (
                    <span>
                      {record.payAmount} 元 ~ {record.deduction} 元
                    </span>
                  ) : (
                    '-'
                  );
                }}
              />
              <Table.Column
                title="权益限制里程(公里)"
                key="limitLegend"
                dataIndex="limitLegend"
                width={170}
                render={(text, record) => {
                  if (record.limitLegendFrame === null) {
                    return '-';
                  }
                  return (
                    <div className={limitLegendMap[record.limitLegendFrame]?.type}>
                      {record[limitLegendMap[record.limitLegendFrame]?.type] || '-'}{' '}
                      {record.limitLegendFrame ? (
                        <Tag className={styles[limitLegendMap[record.limitLegendFrame]?.style]}>
                          {limitLegendMap[record.limitLegendFrame]?.name}
                        </Tag>
                      ) : (
                        ''
                      )}
                    </div>
                  );
                }}
              />
              {/* <Table.Column title='权益包属性' key='sortCodeName' dataIndex='sortCodeName' render={text=>text || '-'} /> */}
              {/* <Table.Column title='权益车型' key='carAttrName' dataIndex='carAttrName' /> */}
              <Table.Column
                title="权益限制年限"
                key="limitYear"
                dataIndex="limitYear"
                width={120}
                render={(text, record) => {
                  let span = <span>-</span>;
                  if (record.limitTimeFrame == -1) {
                    span = <span>永久有效</span>;
                  } else if (record.limitTimeFrame == 1) {
                    span = (
                      <span>{`${Number(record.limitYear)}年${Number(record.limitMonth)}月${Number(record.limitDay)}日`}</span>
                    );
                  }
                  return span;

                  // return record.limitYear && record.yearUnit ? record.limitYear + getYearUnitName(record.yearUnit) :'不限年限'
                }}
              />
              <Table.Column
                title="权益属性"
                key="identityName"
                dataIndex="identityName"
                width={120}
                render={text => text || '-'}
              />
              <Table.Column
                title="享有车主身份"
                key="carIdentityName"
                dataIndex="carIdentityName"
                width={120}
                render={text => text || '-'}
              />
              <Table.Column
                title="享有权益频次"
                key="frequency"
                dataIndex="frequency"
                width={120}
                render={text => {
                  let a = '';
                  if (text) {
                    a = text < 0 ? '不限次数' : text + '次';
                  } else {
                    a = '-';
                  }
                  return a;
                }}
              />
              <Table.Column
                title="车联网流量"
                key="traffic"
                dataIndex="traffic"
                width={120}
                render={(text, record) => {
                  let a = '';
                  if (text) {
                    a = text < 0 ? '不限流量' : text + record.trafficUnit;
                  } else {
                    a = '-';
                  }
                  return a;

                  // return record.traffic  && record.trafficUnit ? record.traffic + ' ' + record.trafficUnit : '不限流量'
                }}
              />
              <Table.Column
                title="权益生效时间"
                key="relativeTime"
                dataIndex="relativeTime"
                width={210}
                render={(text, record) => {
                  return record.relativeTimeName ? (
                    record.relativeTimeName
                  ) : (
                    <>
                      {record.fixedBeginTime && record.fixedEndTime
                        ? moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') +
                          ' ~ ' +
                          moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')
                        : '-'}
                    </>
                  );
                }}
              />
              <Table.Column
                title="权益支持区域"
                key="provinceName"
                dataIndex="provinceName"
                width={200}
                ellipsis
                render={(text, record) => {
                  let str = '';
                  if (record.areas && record.areas.length) {
                    let obj = record.areas[0];
                    str = `${obj.provinceName || ''}  ${obj?.cityName || ''} ${obj?.areaName || ''}`;
                  } else {
                    str = '-';
                  }
                  return (
                    <div>
                      {record.allArea == '0' ? (
                        '全部区域'
                      ) : (
                        <PublicTooltip placement="topLeft" title={str}>
                          {str}
                        </PublicTooltip>
                      )}
                    </div>
                  );
                }}
              />
              <Table.Column
                title="权益支持门店"
                key="dealersArr"
                dataIndex="dealersArr"
                width={200}
                ellipsis={true}
                render={(text, record) => {
                  let temp = [];
                  if (record.dealers && record.dealers.length) {
                    record.dealers.forEach(item => {
                      temp.push(item.dealerCodeName);
                    });
                  } else {
                    temp = ['-'];
                  }
                  return (
                    <div>
                      {record.allDealer == '0' ? (
                        '全部门店'
                      ) : (
                        <PublicTooltip placement="topLeft" title={temp.join(',')}>
                          {temp.join(',')}
                        </PublicTooltip>
                      )}
                    </div>
                  );
                }}
              />
              <Table.Column
                title="是否支持退款"
                key="refundName"
                dataIndex="refundName"
                width={120}
                render={text => text || '-'}
              />
              <Table.Column
                title="是否三方履约"
                key="performanceName"
                dataIndex="performanceName"
                width={120}
                render={text => text || '-'}
              />
              <Table.Column
                title="能否重复购买"
                key="isRepeatablePurchaseName"
                dataIndex="isRepeatablePurchaseName"
                width={120}
                render={text => text || '-'}
              />
              {/* <Table.Column title='权益声明' key='statement' dataIndex='statement' width={300} ellipsis render={text=>text?<Tooltip title={text}><div style={{overflow: 'hidden',textOverflow: 'ellipsis',whiteSpace: 'nowrap'}}>{text}</div></Tooltip> : '-'} /> */}
              <Table.Column
                title="权益声明"
                key="statement"
                dataIndex="statement"
                width={300}
                ellipsis
                render={text =>
                  text ? (
                    <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                      {text}
                    </PublicTooltip>
                  ) : (
                    '-'
                  )
                }
              />
              <Table.Column
                title="权益备注"
                key="remark"
                dataIndex="remark"
                width={300}
                ellipsis
                render={text =>
                  text ? (
                    <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                      {text}
                    </PublicTooltip>
                  ) : (
                    '-'
                  )
                }
              />
            </Table>
          </div>
          <div
            className="upload-box"
            style={{
              backgroundColor: '#F6F8FB',
              padding: 20,
              marginTop: 18,
              marginBottom: 18,
              border: '1px solid #EAECEF',
            }}
          >
            <h4>
              上传附件<span style={{ color: 'red', marginLeft: 5, opacity: 0.5 }}>*</span>
            </h4>
            <div style={{ marginTop: 10 }}>
              {/* <span>附件：</span> */}
              <div style={{ position: 'relative' }}>
                <div style={{ marginTop: 25, marginBottom: 10 }}>
                  {' '}
                  <span>附件：</span>
                  <Upload
                    action={baseURL.Host + allUrl.AccountManagement.accountUploadFile}
                    data={getExtraData}
                    headers={getHeaders}
                    beforeUpload={beforeUpload}
                    accept=".xls,.xlsx,.pdf,.jpg,.png,.jpeg,.docx,.doc"
                    multiple={true}
                    maxCount={9}
                    onChange={uploadChange}
                    onRemove={onRemove}
                  >
                    {fileList.length < 9 ? <Button icon={<UploadOutlined />}>上传附件</Button> : null}
                  </Upload>
                </div>
                <div style={{ color: 'red', position: 'absolute', left: '180px', top: '5px' }}>
                  附件仅支持上传：图片、Excel、Word、PDF格式文件
                </div>
              </div>
            </div>
          </div>

          <div className={style.reason}>
            <Form name="unbind" ref={formRef1} form={form1} {...layout2}>
              <Row>
                <Col span={24}>
                  <Form.Item label="关联原因" name="reason" validateStatus={reasonStatus} hasFeedback required={true}>
                    <Input allowClear placeholder="请输入关联原因" maxLength={50} onChange={onChangeReason} />
                  </Form.Item>
                  <p style={{ color: 'red', fontSize: '12px', position: 'absolute', top: '38px', left: '84px' }}>
                    最多支持50个字，不支持其它表情和特殊符号，如配置则无法正常展示
                  </p>
                </Col>
              </Row>
            </Form>
          </div>
        </Modal>
      </Spin>
    </>
  );
};
export default memo(RelevancePayGiveTable);
