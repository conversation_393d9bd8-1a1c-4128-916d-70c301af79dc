import React, { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Timeline, message, Button, Tabs, Row, Col, Tag, Modal, Form, Input } from 'antd';
import moment from 'moment';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import PublicTableQuery from '@/components/Public/PublicTableQuery';
import PublicTable from '@/components/Public/PublicTable';
import { post, get } from '@/utils/request';
import allUrl from '@/utils/url';
import { roleJudgment } from '@/utils/authority';
import { trim } from '@/utils';
import _ from 'lodash';
import './Feedback.less';
const { TextArea } = Input;

const Feedback = props => {
  //创建ref节点
  const childRef = useRef(null);
  const tableRef = useRef(null);
  const dispatch = useDispatch();
  const { userInfo } = useSelector(state => state.common);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [current, setCurrent] = useState(1);
  const [tableHeight, setTableHeight] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [reasonOpen, setReasonOpen] = useState(false);
  const [allLableType, setAllLableType] = useState([]);
  const [allAffirmType, setAllAffirmType] = useState([]);
  const [ownerList, setOwnerList] = useState([]);
  const [currentRecord, setCurrentRecord] = useState([]);
  const [form] = Form.useForm();
  const formRefModal = useRef(null);

  const onSearch = values => {
    console.log('values', values);
    if (values.questionTime && values.questionTime.length) {
      values.questionTimeS = moment(values.questionTime[0]).format('YYYY-MM-DD HH:mm:ss');
      values.questionTimeE = moment(values.questionTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete values.questionTime;
    }
    setDefaultQuery({ ...values });
  };

  const addReason = record => {
    setReasonOpen(true);
    setCurrentRecord(record);
    //     post(allUrl.Partner.showPlaintextInfo, { id: record.id }).then(res => {
    //   if (res.success) {
    //     console.log('详情', res.resp[0], tableRef.current);
    //     //   let data = [...tableRef.current.dataSource]
    //     //   data.map((i) => {
    //     //     if(i.id == record.id) {
    //     //         i.key = record.key+new Date().getTime() // key值不更新，页面不更新！
    //     //         i.ownerPhone = res.resp[0].ownerPhone
    //     //     }

    //     //   })
    //     //   tableRef.current.updateDataSource(data)
    //     setShowPhone(res.resp[0].ownerPhone);
    //     setReasonOpen(true);
    //   } else {
    //     message.error(res.msg);
    //   }
    // });
  };
  useEffect(() => {
    getDictData();
    get(allUrl.WeCom.allLabel).then(res => {
      if (res.success) {
        let arr = res.resp[0].map(type => ({
          name: type.name,
          value: type.code,
          children: type.name,
        }));
        setAllLableType(arr);
      }
    });
  }, []);

  const renderOperation = (text, record) => {
    return (
      <div style={{ color: '#1890ff' }}>
        {roleJudgment(userInfo, 'WECOM_CHAT_FEEDBACK_SOLVED') && record.status !== 1
          ? [
              <span key={3} style={{ cursor: 'pointer' }} onClick={() => addReason(record)}>
                标记为“已解决”
              </span>,
            ]
          : null}
      </div>
    );
  };
  const initPage = () => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let FormQueryH = document.getElementsByClassName('PublicList_FormQuery')[0].clientHeight;
    let h = winH - FormQueryH - 47 - 54 - 305;
    setTableHeight(h);
  };
  useEffect(() => {
    initPage();
  }, []);
  /**
   * 根据字典和id返回状态名称
   * @param {Array} dict
   * @param {string} id
   * @returns
   */
  const renderStatusName = (dict, id) => {
    let str = '';
    dict.map(item => {
      if (item.value == id) {
        str = item.name;
      }
    });
    return str;
  };
  // 获取字典数据
  const getDictData = () => {
    get(allUrl.common.entryLists, { codes: 'qw_customer_type' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        Dt['qw_customer_type'].forEach(item => {
          item.name = item.entryMeaning;
          item.value = Number(item.entryValue);
        });
        setAllAffirmType(Dt['qw_customer_type']);
      }
    });
  };

  const searchOwnerList = _.debounce(value => {
    value = trim(value);
    post(allUrl.WeCom.ownerList, { keywords: value }).then(res => {
      if (res.success) {
        let Dt = res.resp[0].map((item, index) => {
          return { name: item.name, value: item.value, key: index + 1 };
        });
        setOwnerList([...Dt]);
      } else {
        // message.error(res.msg)
      }
    });
  }, 500);
  const columns = [
    { title: '问题总结', dataIndex: 'summarize', width: 200, fixed: 'left' },
    { title: '问题描述', dataIndex: 'question', width: 280 },
    { title: '问题类型', dataIndex: 'typeLabelName', width: 200 },
    { title: '提出时间', dataIndex: 'questionTime', width: 140 },
    { title: '提出人情绪', dataIndex: 'intent', width: 110 },
    { title: '是否解决', dataIndex: 'status', width: 100, render: text => (text == 1 ? '是' : '否') },
    { title: 'AI判断理由', dataIndex: 'reasoning', width: 280 },
    { title: '关键证据', dataIndex: 'keyEvidence', width: 260 },
    { title: '提出人', dataIndex: 'userNickname', width: 140 },
    {
      title: '是否认定',
      dataIndex: 'affirm',
      width: 140,
      render: text => {
        if (text === null || text === undefined) {
          return '';
        }
        return text ? '是' : '否';
      },
    },
    { title: '认定姓名', dataIndex: 'userAffirmName', width: 140 },
    { title: '认定类型', dataIndex: 'affirmTypeName', width: 140 },
    { title: '所属群', dataIndex: 'groupName', width: 140 },
    { title: '群主', dataIndex: 'ownerName', width: 140 },
    { title: '所属门店', dataIndex: 'dealerName', width: 140 },
    {
      title: '操作',
      fixed: 'right',
      width: 160,
      dataIndex: 'Operation',
      render: (text, record) => renderOperation(text, record),
    },
  ];

  let searchList = [
    {
      label: '问题类型',
      name: 'typeLabel',
      type: 'Select',
      mode: 'multiple',
      initialValue: [],
      placeholder: '请输入关键字',
      showSearch: true,
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
      data: allLableType,
    },
    {
      label: '提出人',
      name: 'userNickname',
      type: 'Input',
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
    },
    {
      label: '提出时间',
      name: 'questionTime',
      type: 'RangePicker',
      showTime: true,
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
    },
    // {
    //   label: '是否认定',
    //   name: 'affirm',
    //   type: 'Select',
    //   placeholder: '请选择',
    //   colSpan: 6,
    //   labelCol: { span: 8 },
    //   wrapperCol: { span: 20 },
    //   data: [
    //     {
    //       name: '是',
    //       value: 'true',
    //     },
    //     {
    //       name: '否',
    //       value: 'false',
    //     },
    //   ],
    // },
    {
      label: '认定姓名',
      name: 'userAffirmName',
      type: 'Input',
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
    },
    // {
    //   label: '认定类型',
    //   name: 'affirmType',
    //   type: 'Select',
    //   placeholder: '请选择',
    //   colSpan: 6,
    //   labelCol: { span: 8 },
    //   wrapperCol: { span: 20 },
    //   data: allAffirmType,
    // },
    {
      label: '提出人情绪',
      name: 'intent',
      type: 'Select',
      mode: 'multiple',
      initialValue: [],
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
      data: [
        { name: '无', value: '无' },
        { name: '潜在不满', value: '潜在不满' },
        { name: '明确不满', value: '明确不满' },
        { name: '投诉', value: '投诉' },
      ],
    },
    {
      label: '是否解决',
      name: 'status',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
      data: [
        { name: '否', value: 0 },
        { name: '是', value: 1 },
      ],
    },
    {
      label: '所属群',
      name: 'groupName',
      type: 'Input',
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
    },
    {
      label: '群主',
      name: 'ownerId',
      type: 'SearchSelect',
      mode: 'multiple',
      initialValue: [],
      placeholder: '请输入关键字',
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
      showSearch: true,
      onSearch: searchOwnerList,
      data: ownerList,
    },
    {
      label: '所属门店',
      name: 'dealerName',
      type: 'Input',
      colSpan: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 20 },
    },
  ];

  const handleOk = () => {
    let reason = formRefModal.current.getFieldValue(['reason']);
    if (!reason) return message.error('请简单介绍问题被解决的过程！');
    post(allUrl.WeCom.saveMark, { content: reason, id: currentRecord.id }).then(res => {
      if (res.success) {
        message.success('操作成功');
        setReasonOpen(false);
        form.resetFields();
        const oldData = _.cloneDeep(tableRef.current.dataSource || []);
        const index = oldData.findIndex(item => item.id === currentRecord.id);

        if (index !== -1) {
          oldData[index].status = 1;
          tableRef.current.updateDataSource(oldData);
        }
      }
    });
  };

  return (
    <div className="Feedback">
      <PublicTableQuery onSearch={onSearch} searchList={searchList} isCatch={false} isFormDown={false} ref={childRef} />
      <div className="tableData">
        <PublicTable
          url={allUrl.WeCom.saleQuestion}
          scroll={{ x: 'max-content', y: tableHeight }}
          isCatch={false}
          columns={columns}
          type={4}
          ref={tableRef}
          rowSelection={false}
          setCurrent={setCurrent}
          setPageSize={setPageSize}
          defaultQuery={{ ...defaultQuery }}
        />
      </div>
      {reasonOpen && (
        <Modal
          open={reasonOpen}
          centered
          onOk={() => handleOk()}
          onCancel={() => {
            form.resetFields();
            setReasonOpen(false);
          }}
          width={400}
          maskClosable={false}
          destroyOnClose={true}
          closable={false}
          wrapClassName="reason-wrap"
        >
          <Form name="reason-form" ref={formRefModal} form={form}>
            <Form.Item name="reason" rules={[]}>
              <TextArea rows={4} placeholder="请简单介绍问题被解决的过程" allowClear maxLength={500} />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </div>
  );
};
export default Feedback;
