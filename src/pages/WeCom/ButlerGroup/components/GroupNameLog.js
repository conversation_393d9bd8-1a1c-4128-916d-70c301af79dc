import React,{useState,useEffect} from 'react'
import { Modal,Timeline,Table,message } from 'antd'
import {get,post} from '@/utils/request'
import allUrl from '@/utils/url'
import moment from 'moment'

const GroupNameLog = props=>{
    const {visible,onCancel, objectKey} = props
    const [dataSource,setDataSource] = useState([])
    const [loading,setLoading] = useState(false)
    useEffect(() => {
        post(allUrl.WeCom.queryGroupNameChangeLog,{objectKey}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                let arr = []
                Dt.length && Dt.forEach((item) => {
                    arr.push(item.log || {})
                })
                setDataSource(arr)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        }).catch(()=>{
            setLoading(false)
        })
    }, []);
    const columns = [
        { title: '时间', dataIndex: 'createTime', width: 120},
        // { title: '修改人账号ID', dataIndex: 'userId', width: 120,},
        // { title: '修改人姓名', dataIndex: 'ownerName', width: 100 },
        // { title: '修改人岗位', dataIndex: 'positionName', width: 100 },
        // { title: '修改人所属组织', dataIndex: 'orgName', width: 160 },
        // { title: '修改人所属用户中心', dataIndex: 'dealerFullName', width: 180 },
        { title: '修改前内容', dataIndex: 'oldName', width: 160 },
        { title: '修改后内容', dataIndex: 'name', width: 180 },
    ]
    return <Modal title='群名修改日志' visible={visible} onCancel={onCancel} maskClosable={true} footer={null} width={800}>
        <Table columns={columns} loading={loading} dataSource={dataSource}  pagination={false}  bordered={true}/>  
    </Modal>
}
export default GroupNameLog