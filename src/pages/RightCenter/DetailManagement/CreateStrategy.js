import React from 'react'
import CreateStrategyCom from './CreateStrategyCom';
import { strategyConfig, outsideStrategyConfig } from './formConfig'

const CreateStrategy = (props) => {
	const { consumptionStrategy, formObj } = props

	return (
		<>
			{consumptionStrategy == '2' ? <>
				<div className='strategy-title'>
					<div className='left-title'>
						对外展示策略
					</div>
				</div>
				<CreateStrategyCom strategyConfig={outsideStrategyConfig} {...props} />

			</> : null}
			<div className='strategy-title'>
				<div className='left-title'>
					{consumptionStrategy == '2' ? '对内核销策略' : '对外展示策略'}
				</div>
			</div>
			<CreateStrategyCom strategyConfig={strategyConfig} {...props} />
		</>
	)
}
CreateStrategy.displayName = 'DatabaseForm'
export default CreateStrategy
