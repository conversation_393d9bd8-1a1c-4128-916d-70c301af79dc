.SendingRange{
    .SendingRangeLeft{
        .ant-table-wrapper{
            .ant-table-container{
                .ant-table-body{
                    overflow: auto auto !important;
                }
            }
        }
        .ant-tree{
            .ant-tree-list{
                height: 548px;
                overflow-y: auto;
                .ant-tree-node-selected{
                    background: white;
                }
            }
        }
    }
    .SendingRangeRight{
        .title{
            height:46px;
            padding:14px;
            background:#DFEFFF;
            color:rgba(0,0,0,0.65);
            font-size:14px;
        }
        .contentBox{
            background:#F5F9FD;
            // padding:0 20px;
            height:614px;
            overflow-y:auto;
            border-radius: 2px;
            .content_Item{
                // padding: 10px 0;
                // background-color: red;
                .name{
                    padding: 10px 0;
                    padding-left: 10px;
                }
                .del{
                    padding: 10px 0;
                    text-align: center;
                }
            }
            .content_Item:hover .name{
                background-color: #cee2f6;
            }
            .content_Item:hover .del{
                background-color: #cee2f6;
            }
        }
    }
}