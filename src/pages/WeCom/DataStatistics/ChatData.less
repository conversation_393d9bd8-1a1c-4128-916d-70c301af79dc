.DataStatistics {
  .chartContainer-card1 .chartContainer-card-header {
    margin-bottom: 0px !important;
  }
  .chartContainer-card {
    .chartContainer-card-header {
      .title {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        display: flex;
        // justify-content: space-between;
        .title-con {
          display: flex;
          align-items: center;
          flex: 1;
          .title-con-text {
            // margin-right: 20px;
          }
          .ant-tabs {
            .ant-tabs-nav {
              margin-bottom: 0 !important;
            }
          }
        }
        .title_warp {
          display: flex;
          align-items: center;
          flex: 1;
          .theme {
            width: 12px;
            height: 12px;
            background: #c04eff;
            border-radius: 3px;
            margin-right: 8px;
          }
        }
        .extended {
          font-size: 14px;
        }
      }
      .subtitle {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        display: flex;
        justify-content: space-between;
        .subtitle-statisticalTime {
          font-size: 13px;
        }
      }
    }
    .chartContainer-card-content {
      .content-con {
        .content-left {
          .content-left-item {
            padding: 10px;
            height: 130px;
            width: 90%;
            border: solid 1px #bbbbbb;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            margin-bottom: 20px;
            .title {
              color: rgba(16, 16, 16, 100);
              font-size: 26px;
              font-family: SourceHanSansSC-black;
              display: flex;
              justify-content: space-between;
              width: 100%;
              align-items: flex-end;
              .title-max {
                font-weight: 500;
                font-size: 18px;
              }
              .title-min {
                font-size: 14px;
                font-weight: 400;
              }
            }
            .con {
              color: rgba(162, 239, 77, 100);
              font-size: 36px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
              display: flex;
              justify-content: space-between;
              width: 100%;
              color: #000;
              sup {
                font-size: 16px;
                font-size: 14px;
                vertical-align: super;
              }
              sub {
                font-size: 16px;
              }
            }
            .color-green {
              color: #a2ef4d;
            }
          }
        }
      }
    }
  }
}
.buttonStyle {
  .ant-radio-button-wrapper:not(:first-child)::before {
    background-color: #e8f3ff;
  }
  .ant-radio-button-checked {
    background-color: #1890ff;
  }
  .ant-radio-button-wrapper {
    background: #e8f3ff;
    line-height: 22px;
    padding: 0 8px;
  }
}
.statusFont {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.64);
  height: 22px;
}
.titleColor {
  .ant-tabs-nav {
    margin-bottom: 0px !important;
  }
  .ant-tabs-tab-active {
    background: #e8f3ff !important;
    border-bottom-color: #e8f3ff !important;
  }
}
.exportContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 800px;
  height: 400px;
}
.PublicList_FormQuery {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
