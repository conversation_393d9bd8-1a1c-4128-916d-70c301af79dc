import { useCheckPermission } from '@/hooks/auth';
import { UserPermission } from '@/interfaces/permission';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import {
  Button,
  ButtonProps,
  Card,
  Col,
  ColProps,
  Form,
  FormItemProps,
  FormProps,
  PaginationProps,
  Row,
  Space,
  Table,
} from 'antd';
import { NamePath } from 'antd/lib/form/interface';
import { ColumnType, TableProps } from 'antd/lib/table';
import { FilterValue, SortOrder } from 'antd/lib/table/interface';
import classNames from 'classnames';
import { debounce } from 'lodash';
import omit from 'lodash/omit';
import React, {
  ComponentType,
  ReactNode,
  RefObject,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import isEqual from 'react-fast-compare';
import UserPermissionAuthorize from '../user-permission-authorize';
import './index.less';
import { nullableValueRender, ValueType, valueTypeRenders } from './value-types';

const defaultSearchTransform = (values: Record<string, any>) => values;

export interface ProTableRequestResponse<T> extends Record<string, any> {
  data: T[];
  success?: boolean;
  total?: number;
}

export interface ProTableSearchField<P extends Record<string, any> = Record<string, any>> extends FormItemProps {
  /**
   * 组件类型
   */
  type: ComponentType<P>;
  /**
   * 表单 label
   */
  label?: ReactNode;
  /**
   * 表单 name
   */
  name?: string | number | (string | number)[];
  /**
   * 组件 props
   */
  fieldProps?: P;
  /**
   * 表单项序号，越小越靠前
   */
  order?: number;
  /**
   * 表单项所占栅格
   * @default 6
   */
  span?: number;
  /**
   * col 配置
   */
  col?: ColProps;
}

export interface BaseTableColumn<T extends Record<string, any> = Record<string, any>> extends ColumnType<T> {
  /**
   * 是否隐藏搜索栏
   */
  hideInSearch?: boolean;
  /**
   * 是否隐藏表格列
   */
  hideInTable?: boolean;
  /**
   * 搜索配置
   */
  search?: ProTableSearchField<any>;
  /**
   * 值类型
   */
  valueType?: ValueType;
}

export interface EnumTableColumn<T extends Record<string, any> = Record<string, any>> extends BaseTableColumn<T> {
  /**
   * 枚举
   */
  valueMap: Record<string, string>;
  /**
   * 枚举类型
   */
  valueType: 'enum';
}

export interface IndexTableColumn<T extends Record<string, any> = Record<string, any>> extends BaseTableColumn<T> {
  /**
   * 序号类型
   * @enum 'page' | 'total'
   * @default 'total'
   * @example 'page' 单页序号
   * @example 'total' 全量序号
   */
  indexType?: 'page' | 'total';
  /**
   * 索引序号类型
   */
  valueType: 'index';
}

export type ProTableColumn<T extends Record<string, any> = Record<string, any>> =
  | BaseTableColumn<T>
  | EnumTableColumn<T>
  | IndexTableColumn<T>;

export interface SearchConfig<P extends Record<string, any> = Record<string, any>> {
  /**
   * 指定 label 宽度
   */
  labelWidth?: number;
  /**
   * 表单配置
   */
  form?: FormProps;
  /**
   * 每列默认 span
   *
   * @default 6
   */
  span?: number;

  /**
   * 表单字段转换
   * @param values 表单原始值
   * @returns 输出值
   */
  transform?: (values: P & Record<string, any>) => P;
}

export interface ProTableActions {
  /**
   * 刷新表格
   */
  reload: () => void;
  /**
   * 重置表格，包括分页、表单、以及查询参数
   */
  reset: () => void;
}

export interface PageParams {
  current: number;
  pageSize: number;
}

export interface ProTableRequestParams<P extends Record<string, any> = Record<string, any>> extends PageParams {
  params?: P;
  filters?: Record<string, FilterValue | null>;
  sorter?: Record<string, SortOrder>;
}

export interface ProTableRequest<
  T extends Record<string, any> = Record<string, any>,
  P extends Record<string, any> = Record<string, any>
> {
  (params: ProTableRequestParams<P>): Promise<ProTableRequestResponse<T>>;
}

export interface ProTableAction extends ButtonProps {
  /**
   * 按钮权限
   */
  permission?: UserPermission | string;
}

export interface ProTableProps<
  T extends Record<string, any> = Record<string, any>,
  P extends Record<string, any> = Record<string, any>
> extends Omit<TableProps<T>, 'title'> {
  ghost?: boolean;
  /**
   * 数据源请求方法
   */
  request?: ProTableRequest<T, P>;
  /**
   * 表格对外暴露的方法
   */
  actionRef?: RefObject<ProTableActions>;
  /**
   * 表格列配置
   */
  columns: ProTableColumn<T>[];
  /**
   * 搜索配置
   */
  search?: SearchConfig;
  /**
   * 表格默认参数
   * @description 会与搜索表单合并
   */
  params?: P;
  /**
   * 表格标题
   */
  title?: ReactNode | ((data: T[]) => ReactNode);
  /**
   * 表格顶部操作按钮
   */
  actions?: ProTableAction[];
  /**
   * 是否固定搜索表单
   */
  sticky?: boolean;
  /**
   * 自定义 action 渲染
   * @param actions
   * @returns
   */
  actionRender?: (actions?: ProTableAction[]) => ReactNode;
  /**
   * 数据变更回调
   * @param data
   */
  onDataChange?: (data: T[]) => void;
  /**
   * 自定义表格渲染
   * @param table 原始表格
   * @returns
   */
  tableRender?: (table: ReactNode) => ReactNode;
  /**
   * 表格操作区位置
   * @default 'table'
   * @enum 'toolbar' | 'table'
   * @description 'toolbar' 为表格上方，'table' 为表格右侧
   */
  actionPosition?: 'toolbar' | 'table';
  /**
   * 表单重置后回调
   */
  onReset?: () => void;
}

const DEFAULT_PAGE_CONFIG: PaginationProps = {
  defaultCurrent: 1,
  defaultPageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
};

const ProTable = <
  T extends Record<string, any> = Record<string, any>,
  P extends Record<string, any> = Record<string, any>
>({
  ghost = false,
  request,
  actionRef,
  columns,
  pagination: paginationProps,
  search,
  params: defaultParams,
  title,
  actions,
  sticky,
  actionRender,
  onDataChange,
  rowSelection,
  tableRender,
  onReset,
  actionPosition = 'table',
  ...tableProps
}: ProTableProps<T, P>) => {
  const [{ data, total }, setData] = useState<ProTableRequestResponse<T>>({
    data: [],
    total: 0,
  });

  const isFirstRender = useRef(true);

  const { labelWidth, form: formProps, span: defaultSpan = 6, transform = defaultSearchTransform } = search ?? {};

  const [form] = Form.useForm();

  const searchFields = useMemo<ProTableSearchField<any>[]>(() => {
    return columns
      .filter(({ hideInSearch = false, search }) => !hideInSearch && search?.type)
      .map(
        column =>
          ({
            order: 0,
            span: defaultSpan,
            label: column.title,
            name: column.dataIndex as NamePath,
            ...column.search,
          } as ProTableSearchField)
      )
      .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
  }, [columns, defaultSpan]);

  const isSearchFieldsOverflow = useMemo(() => {
    const span = searchFields.reduce((pre, cur) => pre + (cur.span ?? defaultSpan), 0);
    return span > 18;
  }, [defaultSpan, searchFields]);

  const collapseIndex = useMemo(() => {
    const index = searchFields.findIndex(
      (_, index) =>
        searchFields.slice(0, index + 1).reduce((pre, cur) => pre + (cur.span ?? defaultSpan), 0) > 24 - defaultSpan
    );
    return index > 0 ? index - 1 : searchFields.length;
  }, [defaultSpan, searchFields]);

  const [collapsed, setCollapsed] = useState<boolean>(isSearchFieldsOverflow);

  const remainSpan = useMemo(() => {
    const span =
      searchFields.reduce((pre, cur, index) => {
        if (collapsed && index > collapseIndex) {
          return pre;
        }
        return pre + (cur.span ?? defaultSpan);
      }, 0) % 24;
    return 24 - span;
  }, [searchFields, collapsed, collapseIndex, defaultSpan]);

  const onDataChangeRef = useRef(onDataChange);

  useEffect(() => {
    onDataChangeRef.current = onDataChange;
  }, [onDataChange]);

  const defaultPaginationRef = useRef<PageParams>({
    ...DEFAULT_PAGE_CONFIG,
    ...paginationProps,
    current: paginationProps === false ? 1 : paginationProps?.defaultCurrent ?? DEFAULT_PAGE_CONFIG.defaultCurrent ?? 1,
    pageSize:
      paginationProps === false ? 10 : paginationProps?.defaultPageSize ?? DEFAULT_PAGE_CONFIG.defaultPageSize ?? 10,
  });

  const [internalRequestParams, setInternalRequestParams] = useState<ProTableRequestParams<P>>({
    current: defaultPaginationRef.current.current,
    pageSize: defaultPaginationRef.current.pageSize,
    params: {
      ...defaultParams,
    } as P,
    filters: undefined,
    sorter: undefined,
  });

  const lastParamsRef = useRef(defaultParams);

  useEffect(() => {
    // 首次渲染不需要重置参数
    if (!isFirstRender.current && !isEqual(lastParamsRef.current, defaultParams)) {
      lastParamsRef.current = defaultParams;
      setInternalRequestParams(prevParams => ({
        ...prevParams,
        params: {
          ...prevParams.params,
          ...defaultParams,
        } as P,
      }));
    }
  }, [defaultParams]);

  const [loading, setLoading] = useState<boolean>(false);

  const transformRef = useRef(transform);

  useEffect(() => {
    transformRef.current = transform;
  }, [transform]);

  const fetchData = useCallback(async () => {
    isFirstRender.current = false;
    if (request) {
      try {
        setLoading(true);
        const res = await request?.({
          ...internalRequestParams,
          params: transformRef.current(omit(internalRequestParams.params, '_')) as P,
        });
        onDataChangeRef.current?.(res.data);
        setData(res);
      } catch (error) {
        console.error(error);
        onDataChangeRef.current?.([]);
        setData({
          data: [],
          total: 0,
        });
      } finally {
        setLoading(false);
      }
    }
  }, [internalRequestParams, request]);

  const handleSearch = useCallback(() => {
    const values = form.getFieldsValue();
    setInternalRequestParams(prev => ({
      current: 1,
      pageSize: prev.pageSize,
      params: {
        ...prev.params,
        ...values,
      },
    }));
  }, [form]);

  const handleReset = useCallback(() => {
    handleSearch();
    onReset?.();
  }, [handleSearch, onReset]);

  const debouncedFetchData = useCallback(debounce(fetchData, 100), [fetchData]);
  useEffect(() => {
    debouncedFetchData();
  }, [debouncedFetchData, internalRequestParams]);

  useEffect(() => {
    return () => {
      debouncedFetchData.cancel();
    };
  }, [debouncedFetchData]);

  useImperativeHandle(
    actionRef,
    () => ({
      reload: fetchData,
      reset: () => {
        form.resetFields();
        setInternalRequestParams(() => ({
          current: 1,
          pageSize: defaultPaginationRef.current.pageSize,
          params: defaultParams,
        }));
      },
    }),
    [defaultParams, fetchData, form]
  );

  const tableColumns = useMemo(() => {
    return columns
      .filter(({ hideInTable = false }) => !hideInTable)
      .map(column => {
        const { valueType = 'text', render } = column;
        if (!render) {
          if (valueType === 'index') {
            const { indexType = 'total' } = column as IndexTableColumn<T>;
            return {
              ...column,
              render: (text, record, index) =>
                indexType === 'total'
                  ? (internalRequestParams.current - 1) * internalRequestParams.pageSize + index + 1
                  : index + 1,
            };
          } else if ((column as EnumTableColumn<T>).valueMap) {
            const mappedValue = (column as EnumTableColumn<T>).valueMap;
            return {
              ...column,
              render: (text: any, record: T, index: number) =>
                nullableValueRender(() => mappedValue[text], mappedValue[text], record, index),
            };
          } else if (valueType && valueTypeRenders[valueType]) {
            return {
              ...column,
              render: (text: any, record: T, index: number) => valueTypeRenders[valueType]?.(text, record, index),
            };
          }
        }
        return column;
      });
  }, [columns, internalRequestParams]);

  const tableTitle = useMemo(() => {
    if (typeof title === 'function') {
      return title(data);
    }
    return title;
  }, [data, title]);

  const checkPermission = useCheckPermission();

  const authorizedActions = useMemo(() => {
    return actions?.filter(({ permission }) => {
      const mergedPermission = typeof permission === 'string' ? { code: permission } : permission;
      if (!mergedPermission?.code) {
        return true;
      }

      return checkPermission(mergedPermission?.code);
    });
  }, [actions, checkPermission]);

  const toolbarVisible = useMemo(
    () => actionPosition === 'toolbar' && authorizedActions?.length,
    [actionPosition, authorizedActions]
  );

  const actionsDom = useMemo(
    () =>
      actionRender ? (
        actionRender(actions)
      ) : (
        <Space>
          {actions?.map(({ permission, ...action }, index) => {
            const mergedPermission = typeof permission === 'string' ? { code: permission } : permission;
            return mergedPermission?.code ? (
              <UserPermissionAuthorize key={index} {...mergedPermission}>
                <Button key={index} {...action} />
              </UserPermissionAuthorize>
            ) : (
              <Button key={index} {...action} />
            );
          })}
        </Space>
      ),
    [actionRender, actions]
  );

  const handleTableChange = useCallback<Required<TableProps<T>>['onChange']>(
    (pagination, filters, sorter, { action }) => {
      const formattedSorter = Array.isArray(sorter) ? sorter : sorter ? [sorter] : [];
      const sorterParams = formattedSorter.reduce((prev, cur) => {
        if (cur.order && cur.field) {
          prev[cur.field.toString()] = cur.order;
        }
        return prev;
      }, {} as Record<string, SortOrder>);
      setInternalRequestParams(prev => {
        return {
          ...prev,
          // 如果 filters 或者 sorter 改变，需要重置分页
          current: action !== 'paginate' ? 1 : pagination.current ?? 1,
          pageSize: pagination.pageSize ?? 10,
          filters,
          sorter: sorterParams,
        };
      });
    },
    []
  );

  const tableDom = useMemo(
    () => (
      <Card bodyStyle={ghost ? { padding: 0 } : {}} bordered={!ghost}>
        {tableTitle || (actions?.length && actionPosition === 'table') ? (
          <Row className="pro-table-head" justify={title ? 'space-between' : 'end'} align="middle">
            <Col className="pro-table-title">{tableTitle}</Col>
            {actionPosition === 'table' ? <Col className="pro-table-actions">{actionsDom}</Col> : null}
          </Row>
        ) : null}
        <Table
          dataSource={data}
          bordered
          scroll={{ x: 'max-content' }}
          columns={tableColumns}
          pagination={
            paginationProps !== false
              ? {
                  ...defaultPaginationRef.current,
                  ...paginationProps,
                  current: internalRequestParams.current,
                  pageSize: internalRequestParams.pageSize,
                  total,
                  // showTotal: (total, range) => `共${total}条，当前 ${range[0]}-${range[1]} 条`,
                  showTotal: total => `共${total}条`,
                  showSizeChanger: true,
                  showQuickJumper: true,
                }
              : false
          }
          loading={loading}
          rowSelection={rowSelection ? { type: 'checkbox', fixed: 'left', ...rowSelection } : undefined}
          onChange={handleTableChange}
          {...tableProps}
        />
      </Card>
    ),
    [
      actionPosition,
      actions,
      actionsDom,
      data,
      ghost,
      handleTableChange,
      internalRequestParams,
      loading,
      paginationProps,
      rowSelection,
      tableColumns,
      tableProps,
      tableTitle,
      title,
      total,
    ]
  );

  return (
    <div
      className="pro-table"
      style={ghost ? { padding: 0 } : { paddingTop: searchFields.length > 0 ? undefined : 24 }}
    >
      {searchFields.length > 0 ? (
        <div
          className={classNames('pro-table-filter', {
            'pro-table-filter-sticky': sticky,
          })}
        >
          <Form {...formProps} initialValues={defaultParams} form={form} onFinish={handleSearch} onReset={handleReset}>
            <Row>
              {searchFields.map((field, index) => {
                const { type: Field, span, label, name, col, fieldProps, ...formItemProps } = field;
                return (
                  <Col
                    span={span}
                    key={[name, index].join('-')}
                    style={{
                      display: collapsed && index > collapseIndex ? 'none' : 'block',
                    }}
                    {...col}
                  >
                    <Form.Item
                      {...formItemProps}
                      label={label}
                      name={name}
                      labelCol={{
                        span: !labelWidth ? 6 : undefined,
                        style: {
                          width: labelWidth,
                        },
                      }}
                      wrapperCol={{
                        span: !labelWidth ? 18 : undefined,
                        style: {
                          width: labelWidth ? `calc(100% - ${labelWidth}px)` : undefined,
                        },
                      }}
                    >
                      <Field style={{ width: '100%' }} allowClear {...fieldProps} />
                    </Form.Item>
                  </Col>
                );
              })}
              <Col span={remainSpan}>
                <Form.Item>
                  <Row justify="end">
                    <Space>
                      <Button htmlType="submit" type="primary" loading={loading}>
                        查询
                      </Button>
                      <Button htmlType="reset">重置</Button>
                      {isSearchFieldsOverflow ? (
                        <Button type="link" onClick={() => setCollapsed(!collapsed)}>
                          <Space>
                            <span>{collapsed ? '展开' : '收起'}</span>
                            <span>{collapsed ? <DownOutlined /> : <UpOutlined />}</span>
                          </Space>
                        </Button>
                      ) : null}
                    </Space>
                  </Row>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      ) : null}
      {toolbarVisible ? (
        <div className="pro-table-toolbar" style={{ padding: ghost ? 0 : undefined }}>
          {actionPosition === 'toolbar' ? actionsDom : null}
        </div>
      ) : null}
      <div className="pro-table-content" style={ghost ? { padding: 0 } : {}}>
        {typeof tableRender === 'function' ? tableRender(tableDom) : tableDom}
      </div>
    </div>
  );
};

export default ProTable;
