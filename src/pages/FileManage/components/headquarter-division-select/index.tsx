import { getSeresFileOrganizationSeresList } from '@/apis/file-manage';
import { useRequest } from '@/hooks/request';
import { Checkbox, Select, SelectProps } from 'antd';
import React, { FC, useEffect, useMemo, useState } from 'react';

export interface HeadquarterDivisionSelectProps extends SelectProps {
  showAllSelect?: boolean;
}

const HeadquarterDivisionSelect: FC<HeadquarterDivisionSelectProps> = ({ showAllSelect, ...props }) => {
  const { data = [], loading } = useRequest(() => getSeresFileOrganizationSeresList());
  const [selectedValues, setSelectedValues] = useState<(string | number | undefined)[]>([]);

  // 处理全选的选中状态
  const allSelected = useMemo(() => selectedValues.length === data.length, [selectedValues, data]);
  const indeterminate = useMemo(
    () => selectedValues.length > 0 && selectedValues.length < data.length,
    [selectedValues, data]
  );
  const allOptions = useMemo(
    () =>
      data.map(item => ({
        label: item.name,
        value: item.id,
      })),
    [data]
  );

  useEffect(() => {
    if (props.value) {
      setSelectedValues(props.value);
    }
  }, [props.value]);

  const handleCheckAll = (e: { target: { checked: any } }) => {
    if (e.target.checked) {
      const allValues = data.map(item => item.id);
      setSelectedValues(allValues);
      if (props.onChange) {
        console.log('allOptions', allOptions);
        props.onChange(allValues, allOptions);
      }
    } else {
      setSelectedValues([]);
      if (props.onChange) {
        props.onChange([], []);
      }
    }
  };

  return (
    <Select
      placeholder="请选择总部部门"
      value={selectedValues}
      options={allOptions}
      loading={loading}
      dropdownRender={menu => (
        <>
          {showAllSelect && (
            <div style={{ padding: '5px 12px', borderBottom: '1px solid #e8e8e8' }}>
              <Checkbox indeterminate={indeterminate} onChange={handleCheckAll} checked={allSelected}>
                全选
              </Checkbox>
            </div>
          )}
          {menu}
        </>
      )}
      {...props}
    />
  );
};

export default HeadquarterDivisionSelect;
