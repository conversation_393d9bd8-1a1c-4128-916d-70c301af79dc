.pack-PublicList {
    

    .tableData {
        padding: 24px;
        background: #f0f2f5;

        .info-box {
            background: white;
            padding: 24px;
        }

        .detailPanel {
            padding: 24px 24px 0 24px;
            background-color: white;
        }

        .marg {
            margin-top: 24px
        }

       

        .pack-table-merge-column {
            padding-top: 0px;
            padding-bottom: 0px;
            height: 0px;

            .pack-table-merge-wrap {
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .pack-table-row {
                flex-grow: 1;
                position: relative;

                .detai {
                    height: 100%;
                    display: flex;
                    align-items: center;
                    min-height: 75px;
                }

                &:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    height: 1px;
                    margin-left: -16px;
                    margin-right: -16px;
                    background-color: #f0f0f0;
                }
            }
        }
    }

}

.export-content {
    background-color: #F1F8FF;

    .export-top {
        height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 36px;
        background-color: #DAEDFF;
        padding-left: 16px;
    }

    .export-bottom {
        padding-left: 16px;
        padding-right: 16px;
        padding-bottom: 24px;
        padding-top: 24px;
    }

    .export-add {
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 24px;
    }

    .export-last {
        padding: 0 16px 24px 16px;
    }
}