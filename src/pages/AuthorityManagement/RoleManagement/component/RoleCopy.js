import React,{useEffect, useState} from 'react'
import { Modal, Form, Row, Col, Input, message,Spin } from 'antd'
import { post } from '../../../../utils/request'
import allUrl from '../../../../utils/url'
const RoleCopy = (props) => {
    const { visible, onCancel ,rowData,GetData} = props
    const [form] = Form.useForm()
    const [loading,setLoading] = useState(false)
    const handleOk = () => {
        form.validateFields().then(values=>{
            let data = {
                copyId:rowData.id,
                roleName:values.roleName,
                description:values.description
            }
            setLoading(true)
            post(allUrl.Authority.copyRole,{...data}).then(res=>{
                if(res.success){
                    message.success(res.msg)
                    onCancel()
                    GetData()
                }else{
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        })
    }
    useEffect(()=>{
        form.setFieldsValue({
            oleRoleName:rowData.roleName
        })
    },[rowData])
    const layout = {
        labelCol: {
          span: 8,
        },
        wrapperCol: {
          span: 16,
        },
      };
    return <Modal title={'角色拷贝'} visible={visible} onOk={handleOk} onCancel={onCancel} maskClosable={false} width={1000} confirmLoading={loading}>
        <Spin spinning={loading}>
            <Form form={form} {...layout}>
                <Row>
                    <Col span={12}>
                        <Form.Item label='原角色名称' name='oleRoleName'>
                            <Input disabled allowClear/>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='新角色名称' name='roleName' rules={[
                            {
                                required: true,
                                message: '请输入新角色名称！'
                            },
                        ]}>
                            <Input allowClear placeholder='请输入...' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='描述' name='description' labelCol={{span:4}} wrapperCol={{span:20}}>
                            <Input allowClear placeholder='请输入...'/>
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Spin>
    </Modal>
}
export default RoleCopy