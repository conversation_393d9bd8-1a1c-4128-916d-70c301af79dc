export const detailFormConfig = {
    'name': {
        label: '升级权益明细名称',
        name: 'name',
        hide: true
    },
    'businessCode': {
        label: '业务编码',
        name: 'businessCode',
        hide: true
    },
    'businessCodeType': {
        label: '业务分类',
        name: 'businessCodeType',
        hide: true
    },
    'performingList': {
        label: '权益履约方',
        name: 'performingList',
        hide: true
    },
    'sortCode': {
        label: '权益分类',
        name: 'sortCode',
        hide: true
    },
    'typeCode': {
        label: '权益类别',
        name: 'typeCode',
        hide: true
    },
    'carAttr': {
        label: '车辆属性',
        name: 'carAttr',
        hide: true
    },
    'belong': {
        label: '权益归属',
        name: 'belong',
        hide: true
    },
    'statement': {
        label: '权益声明',
        name: 'statement',
    },
    'remark': {
        label: '权益备注',
        name: 'remark',
    },
    'refund': {
        label: '权益支持退款',
        name: 'refund',
        hide: true
    },
    'performance': {
        label: '是否三方履约',
        name: 'performance',
        hide: true
    },
}

export const strategyConfig = {
    limitTimeFrame: {
        l: '权益限制年限',
        c: {
            limitTimeFrame: {
                value: 'limitTimeFrame'
            },
            limitYear: {
                value: 'limitYear'
            },
            limitMonth: {
                value: 'limitMonth'
            },
            limitDay: {
                value: 'limitDay'
            }
        },
    },
    timeRadio: {
        l: '权益生效时间',
        c: {
            timeRadio: {
                value: 'timeRadio'
            },
            relativeTime: {
                value: 'relativeTime'
            },
            fixedRelativeTime: {
                value: 'fixedRelativeTime',
                fixedBeginTime: 'fixedBeginTime',
                fixedEndTime: 'fixedEndTime'
            }
        }
    },
    limitLegendFrame: {
        l: '权益限制里程（基准）',
        c: {
            limitLegendFrame: {
                value: 'limitLegendFrame'
            },
            limitLegend: {
                value: 'limitLegend'
            },
            extenderMileage: {
                value: 'extenderMileage'
            }
        }
    },
    'effectMileageFrame': {
        l: '权益生效里程',
        c: {
            effectMileageFrame: {
                value: 'effectMileageFrame'
            },
            relativeBeginMileageStrategy: {
                value: 'relativeBeginMileageStrategy'
            },
            relativeEndMileageStrategy: {
                value: 'relativeEndMileageStrategy'
            },
            fixedBeginMileage: {
                value: 'fixedBeginMileage'
            },
            fixedEndMileageStrategy: {
                value: 'fixedEndMileageStrategy'
            }
        }
    },
    'frequencyRadio': {
        l: '享有权益频次',
        c: {
            frequencyRadio: {
                value: 'frequencyRadio'
            },
            frequency: {
                value: 'frequency'
            }
        }

    },
    'identity': {
        l: '权益属性',
        c: {
            identity: {
                value: 'identity'
            },
        }
    },
    'carIdentity': {
        l: '享有车主身份',
        c: {
            carIdentity: {
                value: 'carIdentity'
            },
        }
    },
    'maintenanceLadderFrame': {
        l: '全保养阶梯策略',
        c: {
            maintenanceLadderFrame: {
                value: 'maintenanceLadderFrame'
            },
            maintenanceLadder: {
                value: 'maintenanceLadder'
            },
            maintenanceLadderLimitFrame: {
                value: 'maintenanceLadderLimitFrame'
            },
            maintenanceLadderDetail: {
                value: 'maintenanceLadderDetail'
            },
            maintenanceLadderDetailItem: {
                // ladderTimeFrame: {
                //     value: 'ladderTimeFrame',
                //     disabled: true
                // },
                ladderLimitYear: {
                    value: 'ladderLimitYear',
                    disabled: true
                },
                ladderLimitMonth: {
                    value: 'ladderLimitMonth',
                    disabled: true
                },
                // ladderLimitDay: {
                //     value: 'ladderLimitDay',
                //     disabled: true
                // },
                // ladderMileageFrame: {
                //     value: 'ladderMileageFrame',
                //     disabled: true
                // },
                ladderLimitMileage: {
                    value: 'ladderLimitMileage',
                    disabled: true
                },
                ladderNo: {
                    value: 'ladderNo',
                }
            }
        }
    },
    'paintFrame': {
        l: '补漆面部位',
        c: {
            paintFrame: {
                value: 'paintFrame'
            },
            paintList: {
                value: 'paintList'
            }
        }
    },
    'trafficRadio': {
        l: '车联网流量',
        c: {
            trafficRadio: {
                value: 'trafficRadio'
            },
            traffic: {
                value: 'traffic'
            },
            trafficUnit: {
                value: 'trafficUnit'
            }
        }
    },
    'deductionFrame': {
        l: '权益金额抵扣',
        c: {
            deductionFrame: {
                value: 'deductionFrame'
            },
            payAmount: {
                value: 'payAmount'
            },
            deduction: {
                value: 'deduction'
            }
        }
    },
}

export const outsideStrategyConfig = {
    limitTimeFrame: {
        l: '聚合权益限制年限',
        c: {
            limitTimeFrame: {
                value: 'limitTimeFrame'
            },
            limitYear: {
                value: 'limitYear'
            },
            limitMonth: {
                value: 'limitMonth'
            },
            limitDay: {
                value: 'limitDay'
            }
        }
    },
    timeRadio: {
        l: '聚合权益生效时间',
        c: {
            timeRadio: {
                value: 'timeRadio'
            },
            relativeTime: {
                value: 'relativeTime'
            },
            fixedRelativeTime: {
                value: 'fixedRelativeTime',
                fixedBeginTime: 'fixedBeginTime',
                fixedEndTime: 'fixedEndTime'
            }
        }
    },
    limitLegendFrame: {
        l: '聚合权益限制里程（基准）',
        c: {
            limitLegendFrame: {
                value: 'limitLegendFrame'
            },
            limitLegend: {
                value: 'limitLegend'
            },
            extenderMileage: {
                value: 'extenderMileage'
            }
        }
    },
    'effectMileageFrame': {
        l: '聚合权益生效里程',
        c: {
            effectMileageFrame: {
                value: 'effectMileageFrame'
            },
            relativeBeginMileageStrategy: {
                value: 'relativeBeginMileageStrategy'
            },
            relativeEndMileageStrategy: {
                value: 'relativeEndMileageStrategy'
            },
            fixedBeginMileage: {
                value: 'fixedBeginMileage'
            },
            fixedEndMileageStrategy: {
                value: 'fixedEndMileageStrategy'
            }
        }
    },
    'frequencyRadio': {
        l: '聚合权益频次',
        c: {
            frequencyRadio: {
                value: 'frequencyRadio'
            },
            frequency: {
                value: 'frequency'
            }
        }
    },
    'identity': {
        l: '聚合权益属性',
        c: {
            identity: {
                value: 'identity'
            },
        }
    },
    'carIdentity': {
        l: '聚合权益享有车主身份',
        c: {
            carIdentity: {
                value: 'carIdentity'
            },
        }
    },
    'maintenanceLadderFrame': {
        l: '聚合全保养阶梯策略',
        c: {
            maintenanceLadderFrame: {
                value: 'maintenanceLadderFrame'
            },
            maintenanceLadder: {
                value: 'maintenanceLadder'
            },
            maintenanceLadderLimitFrame: {
                value: 'maintenanceLadderLimitFrame'
            },
            maintenanceLadderDetail: {
                value: 'maintenanceLadderDetail'
            },
            maintenanceLadderDetailItem: {
                // ladderTimeFrame: {
                //     value: 'ladderTimeFrame',
                //     disabled: true
                // },
                ladderLimitYear: {
                    value: 'ladderLimitYear',
                    disabled: true
                },
                ladderLimitMonth: {
                    value: 'ladderLimitMonth',
                    disabled: true
                },
                // ladderLimitDay: {
                //     value: 'ladderLimitDay',
                //     disabled: true
                // },
                // ladderMileageFrame: {
                //     value: 'ladderMileageFrame',
                //     disabled: true
                // },
                ladderLimitMileage: {
                    value: 'ladderLimitMileage',
                    disabled: true
                },
                ladderNo: {
                    value: 'ladderNo',
                }
            }
        }
    },
    'paintFrame': {
        l: '聚合补漆面部位',
        c: {
            paintFrame: {
                value: 'paintFrame'
            },
            paintList: {
                value: 'paintList'
            }
        }
    },
    'trafficRadio': {
        l: '聚合车联网流量',
        c: {
            trafficRadio: {
                value: 'trafficRadio'
            },
            traffic: {
                value: 'traffic'
            },
            trafficUnit: {
                value: 'trafficUnit'
            }
        }
    },
    'deductionFrame': {
        l: '聚合权益金额抵扣',
        c: {
            deductionFrame: {
                value: 'deductionFrame'
            },
            payAmount: {
                value: 'payAmount'
            },
            deduction: {
                value: 'deduction'
            }
        }
    },
}

