import React, { useState, memo, useMemo, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Modal, Form, Row, Col, Select, Input, message } from 'antd'
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
const BusinessCodeDetail = (props) => {
    const { handleCancel, visible, detailInfoSource, getbusinessCodeTypeName } = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const dataSourceForm = useMemo(() => props.detailInfoSource, [props.detailInfoSource])
    const formRef = useRef(null)
    // const handleOk = () =>{
    //     handleCancel()
    //     const params={
    //                 businessCode:dataSourceForm.businessCode,
    //                 businessCodeName:dataSourceForm.businessCodeName,
    //                 detailNo:dataSourceForm.detailNo,
    //                 detailName:dataSourceForm.detailName
    //          }
    //             console.log('更改',params)
    //             // 更改账户信息接口     730
    //             // post(allUrl.DetailedManageInterests.updateEquityDetail, { ...params}).then(res => {
    //             //     if (res.success) {
    //             //         message.success('更新成功！')
    //             //     } else {
    //             //         // message.error('更新失败！')
    //             //     }
    //             //     setLoading(false)
    //             // })
    // }
    const layout = {
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
    };
    useEffect(() => {
        if (dataSourceForm) {
            formRef.current && form.setFieldsValue({
                ...dataSourceForm,

            })
        }
    }, [dataSourceForm, form])
    return (
        <Modal open={visible} footer={null} onCancel={handleCancel} maskClosable={false} width='650px' title='业务编码信息'>
            <Form name='change-codeInfo' ref={formRef} form={form} {...layout} >
                <Row style={{ marginLeft: 30 }}>
                    <Col span={24}>
                        <Form.Item label='业务编码编号'>{detailInfoSource.businessCode || '-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='业务编码名称'>{dataSourceForm ? dataSourceForm.businessCodeName || '-' : '-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='权益明细编码'>{dataSourceForm ? dataSourceForm.detailNo || '-' : '-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='权益明细名称' >{dataSourceForm ? dataSourceForm.detailName || '-' : '-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='业务编码分类' >{dataSourceForm ? getbusinessCodeTypeName(dataSourceForm.businessCodeType) || '-' : '-'}</Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='业务应用' >{dataSourceForm ? dataSourceForm.businessUseComment || '-' : '-'}</Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(BusinessCodeDetail)