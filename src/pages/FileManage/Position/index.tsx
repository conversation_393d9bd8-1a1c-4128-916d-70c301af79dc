import {
  getSeresFilePostSeresDelete,
  getSeresFileUserSeresCountPost,
  postSeresFilePostSeresPage,
} from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import ProTable, { ProTableActions } from '@/components/pro-table';
import UserPermissionAuthorize from '@/components/user-permission-authorize';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { positionPermissions } from '@/permissions/file';
import { PlusOutlined } from '@ant-design/icons';
import { message, Modal, Space, Typography } from 'antd';
import React, { FC, useCallback, useRef, useState } from 'react';
import PositionModal, { PositionModalProps } from './position-modal';

const Position: FC = () => {
  const request = usePaginationTableListStyleRequest(({ current: pageNum, pageSize, params }) => {
    return postSeresFilePostSeresPage({ ...params, pageNum, pageSize });
  });
  const [positionModal, setPositionModal] = useState<PositionModalProps>({ open: false });
  const actionRef = useRef<ProTableActions>(null);
  const handleSave = useCallback(() => {
    setPositionModal({ open: false });
    actionRef.current?.reload();
  }, []);
  const handleEdit = useCallback(record => {
    setPositionModal({ open: true, data: record });
  }, []);
  const handleDelete = useCallback(async (data: FileManage.SeresOrganization) => {
    try {
      const [num = 0] = await getSeresFileUserSeresCountPost({ orgId: data.id as unknown as number });
      Modal.confirm({
        title: '删除总部部门',
        content: `您正在删除【${data.name}】，已关联${num}名总部人员，删除后，该部分人员将缺失岗位信息`,
        onOk: async () => {
          try {
            await getSeresFilePostSeresDelete({ id: data.id as unknown as number });
            actionRef.current?.reload();
            message.success('删除成功');
          } catch (error) {}
        },
        okText: '确认删除',
        okButtonProps: {
          children: '确认删除',
          type: 'primary',
          danger: true,
        },
      });
    } catch (error) {}
  }, []);
  return (
    <PageContainer>
      <ProTable<FileManage.SeresPost>
        actions={[
          {
            children: '添加总部岗位',
            type: 'primary',
            icon: <PlusOutlined />,
            onClick: () => {
              setPositionModal({ open: true });
            },
            permission: positionPermissions.add,
          },
        ]}
        request={request}
        actionRef={actionRef}
        columns={[
          {
            title: '序号',
            dataIndex: 'index',
            valueType: 'index',
          },
          {
            title: '岗位',
            dataIndex: 'name',
          },
          {
            title: '操作',
            render: (_, record) => (
              <Space>
                <UserPermissionAuthorize {...positionPermissions.edit}>
                  <Typography.Link onClick={() => handleEdit(record)}>编辑</Typography.Link>
                </UserPermissionAuthorize>
                <UserPermissionAuthorize {...positionPermissions.delete}>
                  <Typography.Link type="danger" onClick={() => handleDelete(record)}>
                    删除
                  </Typography.Link>
                </UserPermissionAuthorize>
              </Space>
            ),
          },
        ]}
      />
      <PositionModal
        onSave={handleSave}
        onCancel={() => {
          setPositionModal({ open: false });
        }}
        {...positionModal}
      />
    </PageContainer>
  );
};

export default Position;
