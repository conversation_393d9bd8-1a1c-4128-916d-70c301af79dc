import React, { useEffect, useState } from 'react'
import { message, Button, Row, Col, Form, Select ,Modal ,Spin } from 'antd'
import { CloseOutlined } from '@ant-design/icons';
import history from '@/utils/history'
import { get, post } from '@/utils/request'
import allUrl from '@/utils/url'
import './detail.less'
import './projectManagerSettings.less'
const { Option } = Select
const { Item } = Form

let timeout;
let currentValue;

function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    currentValue = value;
  
    function fake() {
        get(allUrl.StoreManage.getBindableStoreList,{queryStr:value?value:''}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                callback(Dt);
            }else{
                // message.error(res.msg)
            }
        })
    }
  
    timeout = setTimeout(fake, 300);
  }

const Detail_projectManager = (props) => {
    const [form] = Form.useForm()
    const [userName, setUserName] = useState('')
    const [userId, setUserId] = useState('')
    const [dealerCodeObj,setdealerCodeObj] = useState({})
    const [projectManagerList, setProjectManagerList] = useState([])
    const [bindableStoreList,setBindableStoreList] = useState([])
    const [newDealerCode,setNewDealerCode] = useState([])
    const [deleteDealerCode,setDeleteDealerCode] = useState([])
    const [managerBindStoreList,setManagerBindStoreList] = useState([])
    const [fetching, setFetching] = useState(false);

    const handleSave = () => {
        let DealerCodeArr = newDealerCode.map(item=>item.dealerCode)
        let data = {
            userName,userId,
            newDealerCode:[...new Set(DealerCodeArr)],deleteDealerCode
        }
        console.log(data)
        post(allUrl.StoreManage.bindStoreManager,data).then(res=>{
            if(res.success){
                message.success(res.msg)
                setTimeout(()=>{
                    onCancel()
                },300)
            }else{
                // message.error(res.msg)
            }
        })
    }
    const onCancel = () => {
        history.goBack()
    }

    const userNameChange = (LabeledValue,option) =>{
        setUserName(option.children)
        setUserId(option.value)
        getManagerBindStoreList(option.value)
    }

    const dealerCodeChange = (LabeledValue,option) =>{
        if(!LabeledValue){
            getBindableStoreList()
            return
        }
        let obj = {
            dealerCode:option.value,
            storeName:option.children,
            managerUserId:option.userid,
            managerUserName:option.username
        }
        setdealerCodeObj(obj)
    }

    const dealerCodeSearch = value => {
        if (value) {
            setFetching(true)
            fetch(value, data => {
                setFetching(false)
                setBindableStoreList(data)
            });
        } else {
            setFetching(false)
            setBindableStoreList([])
        }
      };

    const bindDealerCode = () =>{
        let temp = [...newDealerCode]
        let delTemp = [...deleteDealerCode]
        let bindStore = [...managerBindStoreList]
        form.validateFields().then(values=>{
            let temp1 = bindStore.filter(item=>item.dealerCode === dealerCodeObj.dealerCode)
            if(temp1.length) return message.error('不能重复绑定同一家门店！')

            if(dealerCodeObj.managerUserId && dealerCodeObj.managerUserId!=userId){
                Modal.confirm({
                    title:'提示',
                    okText:'解除',
                    cancelText:'取消',
                    content:`${dealerCodeObj.storeName} 已经被项目经理 ${dealerCodeObj.managerUserName} 绑定，是否解除绑定关系并继续绑定！`,
                    onOk:()=>{
                        // form.resetFields(['dealerCode'])
                        temp.push(dealerCodeObj)
                        bindStore.push(dealerCodeObj)
                        setNewDealerCode(temp)
                        setManagerBindStoreList(bindStore)
                        delTemp = delTemp.filter(item=>item!=dealerCodeObj.dealerCode)
                        setDeleteDealerCode(delTemp)
                    },
                    onCancel:()=>{
                        return false
                    }
                })
            }else{
                temp.push(dealerCodeObj)
                bindStore.push(dealerCodeObj)
                setNewDealerCode(temp)
                setManagerBindStoreList(bindStore)
                delTemp = delTemp.filter(item=>item!=dealerCodeObj.dealerCode)
                setDeleteDealerCode(delTemp)
            }


        })
    }

    const delBindStore = (row) =>{
        let temp = [...managerBindStoreList]
        let delTemp = [...deleteDealerCode]
        let newTemp = [...newDealerCode]
        
        let bindArr = temp.filter(item=>item.dealerCode !== row.dealerCode)
        let newArr= newTemp.filter(item=>item.dealerCode !== row.dealerCode)
        if(row.id){ //接口返回的数据    
            delTemp.push(row.dealerCode)
        }else{  //从左侧移过来的数据    
        }
        console.log(bindArr,delTemp,newArr)
        setManagerBindStoreList(bindArr )
        setDeleteDealerCode(delTemp)
        setNewDealerCode(newArr)
    }

    const getProjectManagerList = (userName) =>{
        get(allUrl.StoreManage.getProjectManagerList,{userName:userName?userName:''}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setProjectManagerList(Dt)
            }else{
                // message.error(res.msg)
            }
        })
    }

    const getBindableStoreList = (queryStr) =>{
        get(allUrl.StoreManage.getBindableStoreList,{queryStr:queryStr?queryStr:''}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setBindableStoreList(Dt)
            }else{
                // message.error(res.msg)
            }
        })
    }

    const getManagerBindStoreList = (userId) =>{
        get(allUrl.StoreManage.getManagerBindStoreList,{userId}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setManagerBindStoreList(Dt)
                setDeleteDealerCode([])
                setNewDealerCode([])
            }else{
                // message.error(res.msg)
            }
        })
    }

    useEffect(()=>{
        getProjectManagerList()
        getBindableStoreList()
    },[])

    return <div className='detail projectManagerSettings'>
        <div className='detail_warp'>
            <div className='detail_title'>项目经理与门店绑定</div>
            <div className='detail_con projectManagerSettings_con'>
                <Form layout={'vertical'} form={form}>
                    <div className='projectManagerSettings_con_title'>
                        <div className='number'>1</div>
                        <div className='title'>选择项目经理</div>
                    </div>
                    <div className='projectManagerSettings_con_content'>
                        <Row>
                            <Col span={6}>
                                <Item name='userName' label='项目经理姓名' rules={[{ required: true, message: '请选择项目经理！'}]}>
                                    <Select
                                        showSearch
                                        placeholder="请输入项目经理姓名搜索"
                                        optionFilterProp="children"
                                        onChange={userNameChange}
                                        // onSearch={onSearch}
                                        filterOption={(input, option) =>
                                        option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                        }
                                    >
                                        {
                                            projectManagerList.map((item,index)=>{
                                                return <Option key={index} value={item.userId}>{item.userName}</Option>
                                            })
                                        }
                                    </Select>
                                </Item>
                            </Col>
                        </Row>
                    </div>
                    <div className='projectManagerSettings_con_title'>
                        <div className='number'>2</div>
                        <div className='title'>绑定门店</div>
                    </div>
                    <div className='remarks'>注：门店绑定组织后才可搜到</div>
                    <div className='projectManagerSettings_con_content' style={{paddingTop:26}}>
                        <Row>
                            <Col span={6}>
                                <Item name='dealerCode' label='门店简称' rules={[{ required: true, message: '请选择门店简称！'}]}>
                                    <Select
                                            showSearch
                                            allowClear
                                            placeholder="请输入门店简或编号搜索"
                                            optionFilterProp="children"
                                            onChange={dealerCodeChange}
                                            // onSearch={dealerCodeSearch}
                                            // notFoundContent={fetching ? <Spin size="small" /> : null}
                                            filterOption={(input, option) =>{
                                                return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.value.toLowerCase().indexOf(input.toLowerCase()) >=0
                                            }
                                            }
                                        >
                                            {
                                                bindableStoreList.map((item,index)=>{
                                                    return <Option key={index} value={item.dealerCode} userid={item.managerUserId} username={item.managerUserName}>{item.storeName}</Option>
                                                })
                                            }
                                    </Select>
                                </Item>
                                <Item>
                                    <Button type='primary' onClick={bindDealerCode}>绑定</Button>
                                </Item>
                            </Col>
                            <Col span={9} offset={3}>
                                <Item label='已绑定门店'>
                                    <div className='bindStore'> 
                                        {
                                            managerBindStoreList.map((item,index)=>{
                                                return <div className='bindStore_item' key={index}>
                                                            <span className='title'>{index+1}. {item.storeName}（{item.dealerCode}）</span>
                                                            <span className='icon' onClick={()=>delBindStore(item)}><CloseOutlined /></span>
                                                        </div>
                                            })
                                        }
                                    </div>
                                </Item>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </div>
        </div>
        <div className='detail_btns '>
            <Button type='primary' style={{ marginRight: '10px' }} onClick={handleSave}>确定</Button>
            <Button onClick={onCancel}>取消</Button>
        </div>

    </div>
}
export default Detail_projectManager