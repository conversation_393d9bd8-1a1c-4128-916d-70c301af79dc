import React, { useState, memo, useMemo, useEffect, useRef, useContext } from 'react'
import { useSelector } from 'react-redux'
import { Modal, Form, Row, Col, Select, Input, message, Descriptions, DatePicker, Button, Popconfirm, InputNumber } from 'antd'
import { put } from '../../../utils/request';
import allUrl from '../../../utils/url';
import moment from 'moment'
import { InjectMessage } from '@/pages/RightCenter/AccountManagement/Detail'

// import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
const limitLegendMap = {
    1: 'limitLegend',
    2: 'extenderMileage'
}
const ChangeRightDetail = (props) => {
    const { handleCancel, visible, dataSource, strategy } = props
    const injectObj = useContext(InjectMessage)
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const dataSourceForm = useMemo(() => dataSource, [dataSource])
    const [effectTime, setEffectTime] = useState(dataSourceForm.effectTime)
    dataSource.effectTime = dataSourceForm.effectTime ? moment(dataSourceForm.effectTime) : ''
    dataSource.endTime = dataSourceForm.endTime ? moment(dataSourceForm.endTime) : ''
    const { accountNo, reloadChangeRightInfo, type } = injectObj
    // const reloadChangeRightInfo = injectObj.reloadChangeRightInfo
    // console.log(accountNo);
    //保存编辑后的开始里程和结束里程，初始值为原开始里程和结束里程，
    const [effectLegend, setEffectLegend] = useState(dataSourceForm.fixedBeginMileage)
    const [endLegend, setEndLegend] = useState(dataSourceForm.fixedEndMileage)
    const formRef = useRef(null)
    const renderLimitYears = (obj) => {
        let span = <span>-</span>
        if (obj.limitTimeFrame == -1) {
            span = <span>永久有效</span>
        } else if (obj.limitTimeFrame == 1) {
            span = <span>{`${Number(obj.limitYear)}年${Number(obj.limitMonth)}月${Number(obj.limitDay)}日`}</span>
        }
        return span
    }
    const disabledDate = (current) => {
        let value = form.getFieldValue('effectTime')
        return value ? current && current < moment(value).endOf('day') : current
    }
    const handleOk = () => {
        const effectTime = form.getFieldValue('effectTime')
        const endTime = form.getFieldValue('endTime')

        const detailNo = dataSourceForm.detailNo
        const map = {
            'in': 1,
            'out': 2,
            'same': 0
        }
        const params = {
            effectTime: effectTime ? moment(effectTime).format('YYYY-MM-DD HH:mm:ss') : '',
            endTime: endTime ? moment(endTime).format('YYYY-MM-DD HH:mm:ss') : '',
            effectMileage: effectLegend,
            endMileage: endLegend,
            changeEffectType: dataSourceForm.consumptionStrategy === 1 ? 0 : map[strategy],
        }
        // console.log('leicj-------------dataSourceForm', dataSourceForm, params);
        // return
        const submit = () => {
            put(allUrl.AccountManagement.updateRightDetail({ accountNo, detailNo }), { ...params }).then(res => {
                if (res.success) {
                    message.success('更新成功！')
                    reloadChangeRightInfo(type)
                } else {
                    // message.error('更新失败！')
                }
                setLoading(false)
                handleCancel()
            })
        }

        // 编辑后，如果有结束里程，判断开始里程大于结束里程不让保存
        if (endLegend != undefined && effectLegend > endLegend) {
            Modal.warning({
                title: '提示',
                content: '权益结束里程不能小于权益开始里程，保存失败！',
            })
            return
        }

        let isTimeDiffer = false, isMileageDiffer = false
        //若该权益明细的权益开始里程是以「交付时里程」（relativeBeginMileageStrategy 为2）作为权益开始里程的，编辑后的权益开始里程与车辆交付时里程不一致时需要提示
        if (dataSourceForm.relativeBeginMileageStrategy == 2 && dataSourceForm.limitLegendType != 0) {
            const limitLegendType = {
                1: 'deliverMileage',  //行驶里程
                2: 'deliverExtenderMileage',  //增程器里程
                // 0 无此策略
            }
            let deliverLegend = dataSourceForm[limitLegendType[dataSourceForm.limitLegendType]]
            isMileageDiffer = (params.effectMileage != deliverLegend)
        }
        // 判断当前是否是相对时间并且值为3（交付时间）&& 修改时间与交付时间一致
        // console.log(params.effectTime, dataSource, moment(dataSource.effectTime).format('YYYY-MM-DD HH:mm:ss'));
        // if (dataSource.relativeTime === 3 && params.effectTime !== moment(dataSource.effectTime).format('YYYY-MM-DD HH:mm:ss')) {
        //     Modal.warning({
        //         // title: 'This is a warning message',
        //         content: '编辑后该权益明细生效时间与车辆交付时间不一致，请知悉！',
        //         okText: '我知道了',
        //         onOk: () => {
        //             submit()
        //         }
        //     })
        //     return
        // }
        if (dataSource.relativeTime === 3) {
            isTimeDiffer = (params.effectTime !== moment(dataSource.effectTime).format('YYYY-MM-DD HH:mm:ss'))
        }
        //弹窗提示
        let i = null
        if (isTimeDiffer && isMileageDiffer) {
            i = 3  //里程和时间都不一致
        }
        if (isTimeDiffer && !isMileageDiffer) {
            i = 1   //仅时间不一致
        }
        if (!isTimeDiffer && isMileageDiffer) {
            i = 2  //仅里程不一致
        }
        if (i) {
            const message = {
                1: '编辑后该权益明细生效时间与车辆交付时间不一致，请知悉！',
                2: '编辑后该权益明细开始里程和车辆交付时里程不一致，请知悉！',
                3: '编辑后该权益明细生效时间与车辆交付时间不一致，且开始里程和车辆交付时里程不一致，请知悉！'
            }
            Modal.warning({
                content: message[i],
                okText: '我知道了',
                width: 400,
                onOk: () => {
                    submit()
                }
            })
        } else {
            submit()
        }
    }

    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 15 },
    };
    const strategyMap = {
        in: {
            title: ' - 对内核销策略',

        },
        out: {
            title: ' - 对外展示策略',
        },
        same: {
            title: ''
        }
    }
    useEffect(() => {
        console.log('-----', dataSourceForm);

        if (dataSourceForm) {
            formRef.current && form.setFieldsValue({
                ...dataSourceForm,
                effectLegend: dataSourceForm.fixedBeginMileage,
                endLegend: dataSourceForm.fixedEndMileage,

            })
        }
    }, [dataSourceForm])
    // useEffect(() => {
    //     switch (strategy) {
    //         case 'in':

    //             break
    //         case 'out':
    //             break
    //         case 'same':
    //             break
    //     }
    // }, [strategy])
    return (
        <Modal open={visible} onOk={handleOk} okText={'保存'} onCancel={handleCancel} maskClosable={false} width='1000px' title={`权益信息编辑${strategyMap[strategy].title}`} footer={[
            <Button key="back" onClick={handleCancel} style={{ marginRight: '10px' }}>
                取消
            </Button>,
            <Popconfirm placement="topLeft" title='确认保存此次编辑？' disabled={!effectTime || (dataSourceForm.limitLegendType !== 0 && typeof effectLegend != 'number')} onConfirm={handleOk} okText="确认" cancelText="取消">
                <Button key="submit" type="primary" disabled={!effectTime || (dataSourceForm.limitLegendType !== 0 && typeof effectLegend != 'number')} loading={loading} >
                    保存
                </Button>
            </Popconfirm>,
        ]}>
            <Descriptions title="" >
                <Descriptions.Item label='权益明细编码' style={{ padding: '0 12px 16px' }}>{dataSourceForm.detailNo || '-'}</Descriptions.Item>
                <Descriptions.Item label='权益明细名称' style={{ padding: '0 12px 16px' }}>{dataSourceForm.name || '-'}</Descriptions.Item>
                <Descriptions.Item label='权益限制年限' style={{ padding: '0 12px 16px' }}>{renderLimitYears(dataSourceForm)}</Descriptions.Item>
                {/* 权益信息编辑增加权益限制里程和开始里程的展示 */}
                <Descriptions.Item label='权益限制里程（基准）' style={{ padding: '0 12px 16px' }}>
                    {(dataSourceForm[limitLegendMap[dataSourceForm.limitLegendType]] === null || dataSourceForm[limitLegendMap[dataSourceForm.limitLegendType]] === undefined) ? '-' : dataSourceForm[limitLegendMap[dataSourceForm.limitLegendType]] + '公里'}
                </Descriptions.Item>
                <Descriptions.Item label='车辆当前里程' style={{ padding: '0 12px 16px' }}>
                    {
                        dataSourceForm.limitLegendType == 1 & dataSourceForm['thinkMileage'] != undefined ?
                            dataSourceForm['thinkMileage'] + '公里' || '-' :
                            dataSourceForm.limitLegendType == 2 & dataSourceForm['rangeExtenderMileage'] != undefined ?
                                dataSourceForm['rangeExtenderMileage'] + '公里' || '-' :
                                '-'
                    }
                </Descriptions.Item>
            </Descriptions>
            <Form name='change-detailInfo' ref={formRef} form={form} {...layout} >
                <Row>
                    <Col span={12}>
                        <Form.Item label='权益开始时间：' name='effectTime' rules={[{ required: true, message: '请选择权益生效时间!' }]}>
                            <DatePicker style={{ width: '100%' }} disabled={dataSourceForm.effect == 1} showTime onChange={date => setEffectTime(date)} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='权益结束时间：' name='endTime' >
                            <DatePicker style={{ width: '100%' }} disabledDate={disabledDate} showTime disabled={dataSourceForm.effect == 1} />
                        </Form.Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        {/* 失去焦点校验开始里程是否与交付时里程一致，不一致提醒 */}
                        <Form.Item label='权益开始里程：' name='effectLegend' rules={[{ required: dataSourceForm.limitLegendType !== 0, message: '请输入权益开始里程!' }]}>
                            {/* 权益限制里程类型为无此策略是禁止编辑 */}
                            <InputNumber disabled={dataSourceForm.limitLegendType === 0 || dataSourceForm.effect == 1} min={0} placeholder='只能输入数字，例如：160000' style={{ width: '100%' }} onChange={value => setEffectLegend(value)} />
                            {/* <span style={{ marginLeft:6, whiteSpace:'nowrap' }}>公里</span> */}
                        </Form.Item>

                    </Col>
                    <Col span={12}>
                        <Form.Item label='权益结束里程：' name='endLegend'>
                            <InputNumber disabled={dataSourceForm.limitLegendType === 0 || dataSourceForm.effect == 1} min={0} placeholder='只能输入数字，例如：160000' style={{ width: '100%' }} onChange={value => setEndLegend(value)} />
                            {/* <span style={{ marginLeft:6, whiteSpace:'nowrap' }}>公里</span> */}
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(ChangeRightDetail)