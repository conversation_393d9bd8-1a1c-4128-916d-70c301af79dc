.progressEditing{
    .detail_con{
        background-color: #f0f2f5 !important;
        .ant-card{
            margin-bottom: 24px;
            .title{
                .projectManage{
                    font-size: 16px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    text-align: left;
                    color: rgba(0,0,0,0.85);
                }
                .name{
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: rgba(0,0,0,0.85);
                    line-height: 24px;
                }
                .update{
                    margin-left: 24px;
                }
                .remarks{
                    font-size: 12px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: rgba(0,0,0,0.65);
                    margin-top: 9px;
                }
            }
            .timeAxis{
                .ant-timeline{
                    margin-top: 24px;
                    .timeAxis_item{
                        .title{
                            font-size: 14px;
                            font-family: PingFangSC, PingFangSC-Semibold;
                            font-weight: 600;
                            text-align: left;
                            color: rgba(0,0,0,0.85);
                            .title_update{
                                font-weight: 400;
                                font-family: PingFangSC, PingFangSC-Regular;
                                color: #1890ff;
                                cursor: pointer;
                                margin-left: 24px;
                            }
                            .title_complete{
                                font-size: 14px;
                                font-family: PingFangSC, PingFangSC-Regular;
                                font-weight: 400;
                                text-align: left;
                                color: rgba(0,0,0,0.23);
                                margin-left: 24px;
                            }
                        }
                        .enterDetails{
                            font-family: PingFangSC, PingFangSC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: #1890ff;
                            font-size: 14px;
                            margin-top: 8px;
                            cursor: pointer;
                        }
                        .modifyInfo{
                            text-align: left;
                            margin-top: 8px;
                            margin-bottom: 2px;
                            font-size: 12px;
                            font-family: PingFangSC, PingFangSC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: rgba(0,0,0,0.65);
                            .modifyInfo_item{
                                width: 300px;
                            }
                        }
                        .fileView{
                            font-size: 14px;
                            font-family: PingFangSC, PingFangSC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: rgba(0,0,0,0.85);
                            margin-top: 8px;
                            // display: flex;
                            .fileView_title{
                                width: 70px;
                            }
                            .fileView_list{
                                font-size: 14px;
                                font-family: PingFangSC, PingFangSC-Regular;
                                font-weight: 400;
                                text-align: left;
                                color: #1890ff;
                                margin-right: 10px;
                                cursor: pointer;
                                flex: 1;
                                .fileView_list_item{
                                    overflow: hidden;
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                }
                            }
                        }
                    }
                }
            }

        }
    }
}