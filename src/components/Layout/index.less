.mainLayout{
    position: relative;
    overflow: hidden;
    height: 100%;
    .header{
        height: 48px;
        line-height: 48px;
        background: white;
        position: absolute;
        top: 0;
        right: 0;
        transition: left 0.2s;
        flex-direction: row;
        justify-content: space-between;
        display: flex;
        padding: 0 18px 0 0;
        .header_left{
            // text-align: center;
            font-size: 20px;
            color: skyblue;
            background: #001529;
            .logoMin{
                width: 40%;
                margin-top: -6px;
            }
            .logoMax{
                width: 15%;
                margin-right: 10px; 
                margin-left: 20px;
            }
        }
        .header_leftMin{
            text-align: center;
        }
        .header-con{
            flex: 1;
            text-align: right;
            .label {
                color: rgba(0,0,0,0.65);
            }
            .desc {
                margin-right: 30px;
                color: #1890FF;
            }
            .cusor {
                cursor: pointer;
            }
        }
        .header_right{
            flex: 1;
            display: flex;
            border-bottom: solid 1px #e8e8e8;
            box-shadow: 0px 2px 4px -4px #444 inset;
            .menuOperation{
                width: 76px;
                text-align: center;
                cursor: pointer;
            }
            .personalInfor{
                cursor: pointer;
                max-width: 270px;
                display: flex;
                // justify-content: center;
                align-items: center;
            }
        }
    }
    .ant-layout-sider{
        background: #001529;
        position: absolute;
        top: 48px;
        transition: width 0.2s;
        // position: fixed;
        // bottom: 0px;
        // top: 48px;
        // width: 208px;
        // min-width: 208px;
        .ant-layout-sider-children{
            .ant-menu-root{
            height: 100%;
            overflow-y: auto;
            }
        }
    }

        .Content_box{
            // padding: 0 31px;
            // padding: 0 10px 0 0;
            position:absolute;
            // transition: left 0.2s;
            top: 48px;
            bottom: 0;
            background-color: white;
            overflow: hidden;
            // width: 100%;
            .ant-breadcrumb{
                // margin: 16px 0px;
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                font-weight: 400;
                color: rgba(0,0,0,0.45);
                padding: 16px 0 16px 32px;
            }
            .Content_title{
                font-size: 20px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.85);
                padding: 0 0 16px 32px;
            }
            .Content-con{
                // padding: 24px;
                // min-height: 348;
                height:-webkit-calc(100vh - 102px);
                height:-moz-calc(100vh - 102px); 
                height:calc(100vh - 102px);
                overflow-y: auto;
                // height: 100%;
                // background: #f0f2f5;
            }
        }
        .footer{
            text-align: center;
            position: absolute;
            bottom: 0;
            right: 0;
            padding: 6px 50px;
            transition: left 0.2s;
        }
}