import React from 'react'
import { message, Button, Input, Row, Col, Form, Checkbox, Tree ,Select } from 'antd'
import { connect } from 'react-redux'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get, post } from '../../../utils/request'
import allUrl from '../../../utils/url'

import '../index.less'

const {Option} = Select
class Detail extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            loading: false,
            locationParmas: {},
            kjym: [

            ],
            treeData: [

            ],
            treeselectedKeys: [],
            treeSelectedRows: [],
            permissionIds: [],
            Type:''
        }
        this.id = 0
    }
    formRef = React.createRef();

    Save = () => {
        let { permissionIds } = this.state
        this.formRef.current.validateFields()
            .then(values => {
                console.log(values)
                if(permissionIds.length>1){
                    for(let i=0 ;i<permissionIds.length ;i++){
                        if(permissionIds[i] === '*'){
                            permissionIds.splice(i,1)
                        }
                    }
                }
                let data = {
                    id: this.id,
                    url: values.url,
                    requestMethod:values.requestMethod,
                    active:values.active,
                    role:permissionIds.length ? permissionIds.join(','):'*',
                    appId:1
                    
                }
                console.log('参数为：', data)
                if (this.id) {
                    post(allUrl.Authority.updateUrlPermission, data).then(res => {
                        if (res.success) {
                            message.success(res.msg || '修改成功！')
                            history.goBack()
                        } else {
                            // message.error(res.msg || '修改失败！')
                        }
                    })
                } else {
                    post(allUrl.Authority.addUrlPermission, data).then(res => {
                        if (res.success) {
                            message.success(res.msg || '新增成功！')
                            history.goBack()
                        } else {
                            // message.error(res.msg || '新增失败！')
                        }
                    })
                }

            })
            .catch(error => {
                console.log(error)
            })
    }
    onCancel = () => {
        const { handleDrawerCancel } = this.props
        if (handleDrawerCancel) {
            handleDrawerCancel()
        } else {
            history.goBack()
        }
    }
    CheckboxChange = (e, row) => {
        let {  permissionIds} = this.state
        if(e.target.checked){
            if(permissionIds.indexOf(e.target.value) > -1){ //存在
                // permissionIds.remove(e.target.value)
            }else{  //不存在
                permissionIds.push(e.target.value)
            }
        }else{
            permissionIds = permissionIds.filter(item=>item!==e.target.value)
        }
        this.setState({permissionIds})
    }

    treeSelect = (selectedKeys, selectedNodes, event) => {
        this.setState({
            treeselectedKeys: selectedKeys,
            treeSelectedRows: selectedNodes.selectedNodes,
            kjym: selectedNodes.node.functionChildren
        })
    }

    render() {
        const { locationParmas, kjym, treeData, permissionIds ,Type} = this.state
        console.log(Type)
        const formItemLayout = {
            labelCol: { span: 14 },
            wrapperCol: { span: 14 },
        }
        return (
            <div className='PublicDetail'>
                <div className='DetailBox'>
                    {
                        locationParmas.title &&
                        <div className='DetailTitle'>{locationParmas.title}</div>
                    }
                    <div className='DetailCon'>
                        <Form
                            {...formItemLayout}
                            layout={'vertical'}
                            ref={this.formRef}
                            initialValues={{ remember: true, }}
                        >
                            <div className='FormRow'>
                                <p className='FormRowTitle'>接口信息</p>
                                <Row className='FormRowCon'>
                                    <Col span={8}>
                                        <Form.Item label="Url" name="url" rules={[{ required: true, message: '请输入Url！', }]}>
                                            <Input placeholder="请输入..." />
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="请求方法" name="requestMethod" rules={[{ required: true, message: '请选择请求方法！', }]}>
                                            <Select allowClear>
                                                <Option value={'GET'}>GET</Option>
                                                <Option value={'POST'}>POST</Option>
                                                <Option value={'PUT'}>PUT</Option>
                                                <Option value={'DELETE'}>DELETE</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="是否生效" name="active" rules={[{ required: true, message: '请选择是否生效！', }]}>
                                            <Select allowClear>
                                                <Option value={true}>是</Option>
                                                <Option value={false}>否</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    {/* <Col span={8}>
                                        <Form.Item label="路由描述" name="description" rules={[{ required: false, message: '请输入角色描述！', }]}>
                                            <Input placeholder="请输入..." />
                                        </Form.Item>
                                    </Col> */}
                                </Row>
                            </div>
                            <div className='FormRow'>
                                <p className='FormRowTitle'>功能权限</p>
                                <div className='KJYM'>
                                    <Row className='KJYM_Title'>
                                        <Col span={8}>可见页面</Col>
                                        <Col>功能权限</Col>
                                    </Row>

                                    <Row className='KJYM_Con'>
                                        <Col span={8} className='KJYM_ConLeft'>
                                            {
                                                treeData.length ?
                                                <Tree defaultExpandedKeys={['0']} treeData={treeData} onSelect={this.treeSelect} />:null
                                            }
                                        </Col>
                                        <Col span={16} className='KJYM_ConRight'>
                                            <Row>
                                                {
                                                    kjym && kjym.length ?
                                                        kjym.map((item, index) => {
                                                            return (
                                                                <Col span={4} key={index}>
                                                                    <Checkbox value={item.code} checked={permissionIds.indexOf(item.code) > -1 ?true:false} onChange={(e) => this.CheckboxChange(e, item)}>{item.title}</Checkbox>
                                                                </Col>
                                                            )
                                                        }) : '请先选择可见页面！'
                                                }
                                            </Row>
                                        </Col>
                                    </Row>
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
                <div className='DetailBtns'>
                    <Button type="primary" style={{ marginRight: '10px' }} onClick={this.Save}>确定</Button>
                    <Button onClick={this.onCancel}>取消</Button>
                </div>
            </div>
        )
    }
    componentDidMount() {
        if (!this.props.isDrawer) {
            let locationParmas = this.props.match.params.data ? JSON.parse(DecryptByAES(this.props.match.params.data)) : {}
            this.id = locationParmas.id
            console.log(locationParmas)
            this.setState({ locationParmas, Type: locationParmas.type }, () => {
                this.getMenuList()
                if(locationParmas.id){
                    this.setState({
                        permissionIds:locationParmas.data.role ? locationParmas.data.role.split(','):[]
                    },()=>{
                        this.formRef.current.setFieldsValue({
                            url: locationParmas.data.url,
                            requestMethod: locationParmas.data.requestMethod,
                            active:locationParmas.data.active
                        })
                    })
                }
            })
        }else{
            this.getMenuList()
        }
    }

    getMenuList = () => {
        get(allUrl.Authority.MenuTreeList).then(res => {
            if (res.success) {
                this.setState({
                    treeData: res.resp,
                    treeselectedKeys: [],
                    treeSelectedRows: [],
                })
            } else {
                // message.error(res.msg || '获取菜单列表失败！')
            }
        })
    }
}
export default connect()(Detail)