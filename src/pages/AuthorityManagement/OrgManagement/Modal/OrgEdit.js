import React from 'react'
import { Modal, message, Form, Input, Button, Spin, Row, Col, Select, Checkbox, Divider } from 'antd'
import allUrl from '@/utils/url'
import { get, post } from '@/utils/request'
import { connect } from 'react-redux'
import './OrgEdit.less'
import { roleJudgment } from '@/utils/authority'
import DebounceSelect from '@/components/Public/DebounceSelect'

const { Option } = Select
class OrgEdit extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {
            MentData: {},
            loading: false,
            indeterminate: false,
            checkAll: false,
            plainOptions: [],
            orgDetail: {},
            typeList:[]
        }
        this.MenuID = ''
        this.ParentID = ''
    }
    formRef = React.createRef();
    handleOk = () => {
        const { onCancel, treeSelectedRows, clearTreeStates } = this.props
        this.formRef.current.validateFields().then(values => {
            if (values) {
                let data = {
                    id: treeSelectedRows[0].key,
                    name: values.name,
                    roleIds: values.roleIds,
                    parentId: treeSelectedRows[0].parentId
                }
                post(allUrl.Authority.isExistOrgName, { name: values.name, id: treeSelectedRows[0].key, parentId: treeSelectedRows[0].parentId }).then(result => {
                    if (result.success) {
                        if (result.resp[0]) {
                            return message.error('组织名称重复！')
                        }
                        post(allUrl.Authority.updateOrg, data).then(res => {
                            if (res.success) {
                                message.success(res.msg || '编辑成功！')
                                onCancel()
                                this.props.cb()
                                if (clearTreeStates) {
                                    clearTreeStates()
                                }
                            } else {
                                // message.error(res.msg || '编辑失败！')
                            }
                        })
                    } else {
                        // message.error(result.msg)
                    }
                })
            }
        })
    }
    Del = () => {
        const { onCancel, treeSelectedRows } = this.props
        post(allUrl.Authority.deleteOrg + '/' + treeSelectedRows[0].key).then(res => {
            if (res.success) {
                message.success(res.msg || '删除成功！')
                this.props.cb()
                onCancel()
            } else {
                // message.error(res.msg || '删除失败！')
            }
        })
    }

    ininForm = () => {
        const { orgDetail, plainOptions } = this.state
        this.setState({
            checkAll: orgDetail.roleIds.length === plainOptions.length,
            indeterminate: orgDetail.roleIds.length === plainOptions.length ? false : true
        })
        this.formRef.current.setFieldsValue({
            name: orgDetail.name,
            type: String(orgDetail.type),
            code: orgDetail.code,
            roleIds: orgDetail.roleIds,
        })
    }
    onCheckAllChange = (e) => {
        const { plainOptions } = this.state
        const checkedList = plainOptions.map(item => item.id)

        this.setState({
            indeterminate: false,
            checkAll: e.target.checked
        }, () => {
            this.formRef.current.setFieldsValue({
                roleIds: e.target.checked ? checkedList : []
            })
        })
    }

    CheckboxGroupChange = (list) => {
        const { plainOptions } = this.state
        const checkedList = plainOptions.map(item => item.id)
        this.setState({
            indeterminate: !!list.length && list.length < checkedList.length,
            checkAll: list.length === checkedList.length
        })
    }

    handleChange = (value) => {
        if (value) {
            this.formRef.current.setFieldsValue({
                code: value
            })
        } else {
            this.formRef.current.resetFields(['code'])
        }
    }

    render() {
        const { visible, onCancel, title, userInfo } = this.props
        const { loading, indeterminate, checkAll, plainOptions, orgDetail,typeList } = this.state
        const layout = {
            labelCol: { span: 8 },
            wrapperCol: { span: 18 },
        };
        return (
            <Modal
                visible={visible}
                title={title}
                onOk={this.handleOk}
                onCancel={onCancel}
                maskClosable={false}
                wrapClassName={'OrgEdit'}
                footer={false}
                width={900}
            >
                <Spin spinning={loading}>
                    <Form
                        {...layout}
                        name="vertical"
                        onFinish={this.onFinish}
                        ref={this.formRef}
                        className='MenuEdit'
                    >
                        <Row>
                            <Col span={12}>
                                <Form.Item
                                    label="组织类型"
                                    name="type"
                                    rules={[{ required: true, message: '请选择组织类型！' }]}
                                >
                                    <Select disabled placeholder='请选择' onChange={this.orgTypeChange}>
                                        {/* <Option value={'1'}>门店</Option>
                                        <Option value={'2'}>其他</Option> */}
                                        {
                                            typeList.map((item,index)=><Option key={index} value={String(item.type)}>{item.name}</Option>)
                                        }
                                    </Select>
                                </Form.Item>
                            </Col>
                            {
                                orgDetail?.type === 1 ?
                                    <Col span={12}>
                                        <Form.Item
                                            label="组织名称"
                                            name="name"
                                            rules={[{ required: true, message: '请填写组织名称！' }]}
                                            className='nameStyle'
                                        >
                                            <DebounceSelect
                                                allowClear
                                                placeholder={'请输入门店简称/门店编号后选择'}
                                                fetchOptions={this.getStoreList}
                                                onChange={this.handleChange}
                                                disabled
                                            />
                                        </Form.Item>
                                    </Col> :
                                    <Col span={12}>
                                        <Form.Item
                                            label="组织名称"
                                            name="name"
                                            rules={[{ required: true, message: '请填写组织名称！' }]}
                                        >
                                            <Input autoComplete="off" allowClear onChange={this.OrgNameChange} placeholder='请输入' />
                                        </Form.Item>
                                    </Col>
                                    

                            }
                            {
                                // 合作伙伴显示编号
                                orgDetail?.type == 4 ? 
                                <Col span={12}>
                                    <Form.Item
                                            label="合作伙伴编号"
                                            name="code"
                                        >
                                        {orgDetail.code || '-'}
                                    </Form.Item>
                                </Col> : null
                            }
                        </Row>
                        <Row>

                            {
                                orgDetail?.type === 1 ?
                                    <>
                                        <Col span={12}>
                                            <Form.Item
                                                label="门店编号"
                                                name="code"
                                                rules={[{ required: true, message: '请填写门店编号！' }]}
                                            >
                                                <Input autoComplete="off" allowClear placeholder='自动带出' disabled />
                                            </Form.Item>
                                        </Col>
                                        <Col span={24}>
                                            <Col span={24} style={{ position: 'absolute', left: 143 }}>
                                                <Checkbox indeterminate={indeterminate} onChange={this.onCheckAllChange} checked={checkAll}>
                                                    全选
                                                </Checkbox>
                                            </Col>
                                            <Form.Item
                                                label="配置角色"
                                                name="roleIds"
                                                rules={[{ required: true, message: '请选择角色名称！' }]}
                                                labelCol={{ span: 4 }}
                                                wrapperCol={{ span: 20 }}
                                                className='roles'
                                            >
                                                <Checkbox.Group style={{ width: '100%' }} onChange={this.CheckboxGroupChange}>
                                                <Divider plain>店端配置角色</Divider>
                                                <Row style={{ width: '100%' }}>
                                                    {
                                                        plainOptions.map((item, index) => {
                                                            if (item.type == 1) {
                                                                return (
                                                                    <Col span={4} key={item.id} style={{marginBottom:'5px'}}>
                                                                        <Checkbox value={item.id} name={item.name}>{item.roleName}</Checkbox>
                                                                    </Col>
                                                                )    
                                                            }
                                                        })
                                                    }
                                                </Row>
                                                <Divider plain>厂端配置角色</Divider>
                                                <Row style={{ width: '100%' }}>
                                                    {
                                                        plainOptions.map((item, index) => {
                                                            if (item.type == 3) {
                                                                return (
                                                                    <Col span={4} key={item.id} style={{marginBottom:'5px'}}>
                                                                        <Checkbox value={item.id} name={item.name}>{item.roleName}</Checkbox>
                                                                    </Col>
                                                                )    
                                                            }
                                                        })
                                                    }
                                                </Row>
                                                <Divider plain>其他配置角色</Divider>
                                                <Row style={{ width: '100%' }}>
                                                    {
                                                        plainOptions.map((item, index) => {
                                                            if (item.type !== 1 && item.type !== 3) {
                                                                return (
                                                                    <Col span={4} key={item.id} style={{marginBottom:'5px'}}>
                                                                        <Checkbox value={item.id} name={item.name}>{item.roleName}</Checkbox>
                                                                    </Col>
                                                                )    
                                                            }
                                                        })
                                                    }
                                                </Row>
                                                </Checkbox.Group>
                                            </Form.Item>
                                        </Col>
                                    </> : null
                            }
                            <Col span={24}>
                                <Form.Item
                                    label={<span>组织删除限制</span>}
                                    rules={[{ required: false }]}
                                    labelCol={{ span: 4 }}
                                    wrapperCol={{ span: 20 }}
                                    className='delText'
                                >
                                    <p>1、一级组织无法删除；</p>
                                    <p>2、有下级组织的组织无法删除；</p>
                                    <p>3、组织中还有成员的组织无法删除；</p>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form>
                    <div className='btns'>
                        {
                            roleJudgment(userInfo, 'PERM_ORGANIZATION_DELETE') ?
                                <Button onClick={this.Del}>删除</Button>
                                : null
                        }
                        <Button type="primary" style={{ marginLeft: '10px' }} onClick={this.handleOk}>保存</Button>
                    </div>
                </Spin>
            </Modal>
        )
    }
    componentDidMount() {
        this.getOrgType()
        this.getOrgData()
    }
    getOrgData = async () => {
        const { treeSelectedRows } = this.props
        this.setState({ loading: true })
        const orgReault = await get(allUrl.Authority.orgDetailByID + treeSelectedRows[0].key)
        const roleResult = await get(allUrl.Authority.typeRoles, { type: 1 })
        this.setState({
            orgDetail: orgReault.resp[0],
            plainOptions: roleResult.resp,
            loading: false
        }, () => {
            this.ininForm()
        })
    }
    getStoreList = (value) => {
        return get(allUrl.StoreManage.getStoreInfo + value).then(res => {
            if (res.success) {
                return res.resp
            } else {
                return []
            }
        })
    }
    getOrgType = () =>{
        get(allUrl.Authority.orgTypeList).then(res=>{
            if(res.success){
                let Dt = res.resp
                console.log(Dt)
                this.setState({typeList:Dt})
            }
        })
    }

}
export default connect(({ common }) => {
    const { userInfo } = common
    return {
        userInfo
    }
})(OrgEdit)