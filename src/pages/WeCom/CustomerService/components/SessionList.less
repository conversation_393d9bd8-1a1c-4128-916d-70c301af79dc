.SessionList {
    .ant-modal-body {
        padding: 0;
    }
    
    .r-d-item {
        padding: 10px 0;
    }
    .user-time {
        color: #1890ff;
        font-size: 12px;
    }
    .r-text {
        color: #000;
        font-size: 14px;
        margin-top: 4px;
        word-break:break-all;
    }
    
    .tip {
        display: inline-block;
        color: #1890ff;
        font-size: 12px;
        margin-top: 10px;
        cursor: pointer;
    }
    .voice-box {
        width: 70%;
        background: #f1f1f1;
        padding: 4px 16px;
        margin-top: 8px;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        position: relative;
    }
    .voice-icon-box {
        width: 16px;
        overflow: hidden;
        
    }
    .voice-icon-ani {
        animation: voice<PERSON>ni 1.5s infinite;
    }
    .voice-icon {
        width: 16px;
    }
    .amr-time {
        position: absolute;
        left: 36px;
    }
    .r-img {
        max-width: 200px;
        object-fit: cover;
    }
    
    @keyframes voiceAni
    {
        0% { width: 0px; }
        100% { width: 16px; }
    }
        
}