export const utilsDict = (key, val) => {
  switch (key) {
    /* 岗位 */
    case 'position':
      return (
        {
          POST_SHOP_MANAGER: '厅店店长',
          POST_SALES_MAN: '销售顾问',
          POST_DRIVE_SPECIALIST: '试驾专员',
          POST_NURTURING_MANAGER: '培育经理',
          POST_CULTIVATION_SPECIALIST: '培育专员',
          POST_LOGISTICS_MANAGER: '板车物流管理员',
          POST_DELIVERY_MANAGER: '交付经理',
          POST_DELIVERY_SPECIALIST: '交付专员',
          POST_DCC_SPECIALIST: 'DCC专员',
          POST_FINANCIALINSURANCE_SPECIALIST: '金融保险保险专员',
          POST_PDI_SPECIALIST: 'PDI专员',
          POST_INTEGRATEDSERVICES_SPECIALIST: '综合事务专员',
          POST_FINANCE_SPECIALIST: '财务专员',
          POST_USERCARE_MANAGER: '用户关爱经理',
          POST_SERVICESTATION_MASTER: '服务站长',
          POST_SERVICE_CONSULTANT: '服务顾问',
          POST_SANBAOCLAIMS_SPECIALIST: '三包索赔专员',
          POST_ACCESSORIES_SPECIALIST: '配件专员',
          POST_SHEETMETAL_TECHNICIAN: '钣金技师',
          POST_PAINTING_CONSULTANT: '喷漆技师',
          POST_ELECTROMECHANICALMAINTENANCE_ENGINEER: '机电维修师',
          厅店店长: 'POST_SHOP_MANAGER',
          销售顾问: 'POST_SALES_MAN',
          试驾专员: 'POST_DRIVE_SPECIALIST',
          培育经理: 'POST_NURTURING_MANAGER',
          培育专员: 'POST_CULTIVATION_SPECIALIST',
          板车物流管理员: 'POST_LOGISTICS_MANAGER',
          交付经理: 'POST_DELIVERY_MANAGER',
          交付专员: 'POST_DELIVERY_SPECIALIST',
          DCC专员: 'POST_DCC_SPECIALIST',
          金融保险保险专员: 'POST_FINANCIALINSURANCE_SPECIALIST',
          PDI专员: 'POST_PDI_SPECIALIST',
          综合事务专员: 'POST_INTEGRATEDSERVICES_SPECIALIST',
          财务专员: 'POST_FINANCE_SPECIALIST',
          用户关爱经理: 'POST_USERCARE_MANAGER',
          服务站长: 'POST_SERVICESTATION_MASTER',
          服务顾问: 'POST_SERVICE_CONSULTANT',
          三包索赔专员: 'POST_SANBAOCLAIMS_SPECIALIST',
          配件专员: 'POST_ACCESSORIES_SPECIALIST',
          钣金技师: 'POST_SHEETMETAL_TECHNICIAN',
          喷漆技师: 'POST_PAINTING_CONSULTANT',
          机电维修师: 'POST_ELECTROMECHANICALMAINTENANCE_ENGINEER',
        }[val] || ''
      );
    /**营业状态 */
    case 'businessStatus':
      return (
        {
          899030000: '营业中',
          899030001: '停业',
          899030002: '搬迁',
          899030003: '改建',
          102910000: '在建',
          102910001: '拟退网',
        }[val] || ''
      );
    /**门店类型 */
    case 'dealerType':
      return (
        {
          1: '体验中心',
          2: '用户中心',
          3: '兼营服务中心',
          4: '专营服务中心',
        }[val] || ''
      );
    /**门店状态 */
    case 'dealerStates':
      return (
        {
          0: '待审批',
          1: '入网',
          2: '兼营服退网务中心',
        }[val] || ''
      );
    /**金康/华为 */
    case 'channel':
      return (
        {
          0: '金康',
          1: '华为',
          2: '其他',
        }[val] || ''
      );
    /**直营/经销 */
    case 'dealerisDirect':
      return (
        {
          0: '直营',
          2: '经销',
        }[val] || ''
      );
    /**角色分类 */
    case 'roleType':
      return (
        {
          1: '门店',
          2: '其他',
          3: '厂端',
        }[val] || '-'
      );
    //性别
    case 'gender':
      return (
        {
          0: '其他',
          1: '男性',
          2: '女性',
        }[val] || '-'
      );
    // 标签类型
    case 'tagType':
      return (
        {
          1: '企业设置',
          2: '用户自定义',
          3: '规则组标签',
        }[val] || '-'
      );
    // 企业微信状态
    case 'qywxStatus':
      return (
        {
          1: '已激活',
          2: '已禁用',
          4: '未激活',
          5: '成员退出',
          6: '删除账号',
        }[val] || '-'
      );
    // 标签类型
    case 'issueType':
      return (
        {
          1: '直接推送',
          2: '扫码领券',
        }[val] || '-'
      );
    //权益中心-权益限制开始里程类型
    // case 'relativeMileageMap':
    //   return (
    //     {
    //       1: '车辆销售时里程',
    //       2: '车辆交付时里程',
    //       3: '车辆开票时里程',
    //       11: '前权益实际结束里程',
    //       12: '前权益相对结束里程'
    //     }[val] || '-'
    //   );
    //权益中心-权益限制结束里程类型
    case 'endMileageMap':
      return (
        {
          1: '计算到总里程和',
          2: '计算到里程策略',
        }[val] || '-'
      );
    //权益中心-行驶里程
    case 'limitLegendMap':
      return (
        {
          0: '-',
          1: '行驶里程',
          2: '增程器里程',
        }[val] || '-'
      );
    //权益中心-客户段展示策略
    case 'displayFrameMap':
      return (
        {
          1: '并列',
          2: '聚合',
        }[val] || '-'
      );
    //权益中心-展示不展示
    case 'displayMap':
      return (
        {
          0: '展示',
          1: '不展示',
        }[val] || '-'
      );
    default:
      return '-';
  }
};
