import React, { memo } from 'react';
import { Table } from 'antd';
import moment from 'moment';
import styles from '../../antd.module.less';

const PublicTable = props => {
  const { dataSource } = props;

  return (
    <>
      <Table
        className={`${styles['gray-table']} basePayTable`}
        dataSource={dataSource?.filter(item => Boolean(item))}
        rowKey={record => record.detailNo.toString()}
        size="small"
        bordered={true}
        pagination={false}
        scroll={{ x: 1840 }}
      >
        <Table.Column
          title="权益明细编码"
          key="detailNo"
          dataIndex="detailNo"
          width={200}
          fixed="left"
          render={(text, record) => {
            return (
              <div>
                <span>{text}</span>
              </div>
            );
          }}
        />
        <Table.Column
          title="权益分类"
          key="sortCodeName"
          dataIndex="sortCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细名称"
          key="name"
          dataIndex="name"
          render={text => text || '-'}
          width={200}
          fixed="left"
        />
        <Table.Column
          title="业务编码"
          key="businessCodeName"
          dataIndex="businessCodeName"
          width={130}
          render={text => text || '-'}
        />
        <Table.Column
          title="工单号"
          key="repairOrderNo"
          dataIndex="repairOrderNo"
          width={140}
          render={text => text || '-'}
        />
        <Table.Column
          title="物料名称"
          key="materialName"
          dataIndex="materialName"
          width={140}
          render={text => text || '-'}
        />
        <Table.Column
          title="溯源码编码"
          key="traceCode"
          dataIndex="traceCode"
          width={140}
          render={text => text || '-'}
        />
        <Table.Column
          title="质保开始日期"
          key="effectTime"
          dataIndex="effectTime"
          width={140}
          render={(text, record) => (text ? moment(text).format('YYYY-MM-DD') : '-')}
        />
        <Table.Column
          title="质保结束日期"
          key="endTime"
          dataIndex="endTime"
          width={140}
          render={(text, record) => (text ? moment(text).format('YYYY-MM-DD') : '-')}
        />
      </Table>
    </>
  );
};
export default memo(PublicTable);
