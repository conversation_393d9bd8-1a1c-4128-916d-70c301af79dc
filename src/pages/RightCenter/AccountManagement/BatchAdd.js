import React, { useEffect, useState, useRef, useReducer } from 'react'
import { Modal, Table, Tooltip } from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import allUrl from '@/utils/url'
import {columns}  from '@/utils/columns'
import './style.less'
import { post } from '@/utils/request'
import styles from '../antd.module.less'
import publicQueryStyle from '../tableLayout.module.less'

function reducer(state, action){
    switch(action.type){
        case 'add':{
            let rows = state.rows.slice()
            let vinChecked = state.vinChecked.slice()
            let checked = state.checked.slice()

            rows.push(action.payload.row)
            vinChecked.push(action.payload.row.vin)
            checked.push(action.payload.row.id)
            return {
                ...state,
                ...{
                    rows,
                    vinChecked,
                    checked
                }
            }
        }
        case 'remove': {
            let { row } = action.payload

            let rows = state.rows.slice()
            let vinChecked = state.vinChecked.slice()
            let checked = state.checked.slice()

            let deleteIndex = checked.indexOf(row.id)
            checked.splice(deleteIndex, 1)
            vinChecked.splice(deleteIndex, 1)
            rows.splice(deleteIndex, 1)
            
            return {
                ...state,
                ...{
                    rows,
                    vinChecked,
                    checked
                }
            }
        }
        case 'addAll': {
            let { row } = action.payload
            let rows = state.rows.slice()
            let vinChecked = state.vinChecked.slice()
            let checked = state.checked.slice()


            row.map(item => {
                if(checked.indexOf(item.id) === -1){
                    rows.push(item)
                    vinChecked.push(item.vin)
                    checked.push(item.id)
                }
            })
            return {
                ...state,
                ...{
                    rows,
                    vinChecked,
                    checked
                }
            }
        }
        case 'removeAll': {
            let { row } = action.payload
            let rows = state.rows.slice()
            let vinChecked = state.vinChecked.slice()
            let checked = state.checked.slice()

            row.map(item => {
                let deleteIndex = checked.indexOf(item.id)
                if(deleteIndex !== -1){
                    checked.splice(deleteIndex, 1)
                    vinChecked.splice(deleteIndex, 1)
                    rows.splice(deleteIndex, 1)
                }
            })

            return {
                ...state,
                ...{
                    rows,
                    vinChecked,
                    checked
                }
            }
        }
        case 'update': {
            let { rows, vinChecked, checked } = action.payload
            return {
                rows,
                vinChecked,
                checked
            }
        }
        default: 
                return state;
    }
}

const BatchAdd = (props) => {
    const tableRef = useRef()
    const [state, dispatch] = useReducer(reducer, { vinChecked: props.state.vinChecked, rows: props.state.rows, checked: props.state.checked})
    const [dataSource, setDataSource] = useState([])
    const [defaultQuery, setDefaultQuery] = useState({})
    const [pageSize, setPageSize]=useState(10)
    const [current, setCurrent]=useState(1)
    const [total, setTotal] = useState(0)
    const [loading, setLoading] = useState(false)


    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
    }

    const onCancel = () => {
        props.batchAddHandle(false)
    }

    const onSearch = (values) => {
        PageChange(1, 10)
        setDefaultQuery(values)
    }

    const onOk = () => {
        props.batchAddSubmit(state)
    }

    useEffect(() => {
        if(props.showBatchAdd){
            let { rows, vinChecked, checked } = props.state
            dispatch({type: 'update', payload: {rows, vinChecked, checked}})
        }
    }, [props.showBatchAdd])

    const searchList = [
        { label: '车辆VIN', name: 'vin', type: 'Input', placeholder: '请输入车辆vin', colSpan: 6, rules:[
            { pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入'}
        ]},
        { label: '权益账户', name: 'account', type: 'Input', placeholder: '请输入购车人手机号', colSpan: 6,rules:[
            { pattern: /^1\d{10}$/, message: '请正确输入手机号'}
        ] }
    ]

    const onSelect = (record, selected, selectedRows, nativeEvent) => {
        dispatch({type: selected ? 'add' : 'remove', payload: {row: record}})
    }
    const onSelectAll = (selected, selectedRows, changeRows) => {
        dispatch({type: selected ? 'addAll' : 'removeAll', payload: {row: changeRows}})
    }

    useEffect(()=>{
        if(props.showBatchAdd){
            setLoading(true)
            getTableData()
        }
    },[current,pageSize,defaultQuery, props.showBatchAdd])

    const getTableData = async () => {
        let params = {pageSize, pageNum: current, ...defaultQuery,...{
            carType: props.carType,
            packNo: props.packNo
        }}

        post(allUrl.AccountManagement.getAccountListForRel, params).then(res => {
            if (res.success) {
                if(res.resp){
                    res.resp.map((item, index) => item.key =  item.id)
                    setDataSource(res.resp)
                } else {
                    setDataSource([])
                }
                setTotal(res.total)
                setLoading(false)
            } else { 
            }
        })
    }

    const pagination = {
        pageSize: pageSize,
        onChange: PageChange,
        current: current,
        total: total,
        showTotal: () => `共${total}条，${pageSize}条/页`,
        showSizeChanger: true,
        showQuickJumper: true,
        onShowSizeChange: PageChange,
    }
    const rowSelection = {
        selectedRowKeys: state.checked,
        onSelect,
        onSelectAll,
        renderCell(checked, record, index, node) {
            if (state.checked.length >= 300) {
              return <Tooltip title="当前选择账户已到达300人上限">{node}</Tooltip>;
            }
            return node;
        },
        getCheckboxProps: (record) => {
            let checkboxProps = {};
            if (state.checked.length >= 300 && state.checked.indexOf(record.id) === -1) {
              checkboxProps.disabled = true;
            }
            return checkboxProps;
        },
    }
    return (
        <Modal
            width={1100}
            visible={props.showBatchAdd}
            onCancel={onCancel} 
            title={<>添加关联<span style={{ color: 'red', marginLeft: '10px', fontSize: '10px'}}>权益账户列表已根据填写的权益包进行：车型、权益包关联情况、车辆交付情况筛选</span></>}
            onOk={onOk}
        >
            <div className={`batch-add ${publicQueryStyle.PublicList}`}>
                <PublicTableQuery isCatch={true} defaultQuery={defaultQuery}  isFormDown={false} onSearch={onSearch} searchList={searchList} />
                
                <Table 
                    className={styles['blue-table']}
                    bordered
                    rowKey={'id'}
                    dataSource={dataSource}
                    loading={loading}
                    ref={tableRef} 
                    scroll={{x:3140}}
                    columns={columns}
                    rowSelection={rowSelection}
                    pagination={pagination}>
                </Table>
                    
            </div>
        </Modal>
    )
}
export default BatchAdd