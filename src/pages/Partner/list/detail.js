import React, { useState, useEffect } from "react";
import { Row, Col, Button, Modal, Form, Input, Select } from "antd";

const { Option } = Select;

const PartnerForm = ({
  visible,
  onCancel,
  onSave,
  initialValues,
  isViewMode,
  partnerStatus,
  partnerTypeOptions,
  loading,
  renderStatusName,
}) => {
  const [form] = Form.useForm();
  console.log("initialValues", initialValues);
  const onFinish = (values) => {
    let data;
    if (initialValues) {
      data = Object.assign(initialValues, values, {});
    } else {
      data = values;
    }
    onSave(data);
    form.resetFields();
  };
  
  return (
    <Modal open={visible} onCancel={onCancel} closable={true} footer={null} width={1200}>
      <Form
        form={form}
        onFinish={onFinish}
        initialValues={initialValues}
        labelCol={{ span: 14 }}
        wrapperCol={{ span: 10 }}
        layout="vertical"
      >
        {isViewMode ? (
          <h4 style={{ marginBottom: 20, fontSize: 18 }}>合作伙伴详情</h4>
        ) : (
          <h4 style={{ marginBottom: 20, fontSize: 18 }}>
            {initialValues.id ? "编辑" : "新增"}合作伙伴
          </h4>
        )}
        <Row>
          <Col span={8}>
            <Form.Item
              label="伙伴企业名称"
              name="partnerName" 
              rules={[{ required: true, message: "请输入伙伴企业名称" }]}
            >
              {isViewMode ? (
                <p>{initialValues.partnerName || "-"}</p>
              ) : (
                <Input placeholder="请输入伙伴名称"/>
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="伙伴编号"
              name="partnerCode"
              rules={[{ required: true, message: "请输入伙伴编号" }]}
            >
              {isViewMode || initialValues.partnerCode ? (
                <p>{initialValues.partnerCode || "-"}</p>
              ) : (
                <Input  placeholder="请输入伙伴编号"/>
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="负责人"
              name="owner"
              rules={[{ required: true, message: "请输入负责人" }]}
            >
              {isViewMode ? <p>{initialValues.owner || "-"}</p> : <Input placeholder="请输入负责人"/>}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <Form.Item
              label="负责人手机号"
              name="ownerPhone"
              rules={[
                { required: true, message: "请输入手机号！" },
                {
                  validator(_, value) {
                    if (value && !/^[1][0-9][0-9]{9}$/.test(value)) {
                      return Promise.reject(new Error("请输入正确的手机号！"));
                    } else {
                      return Promise.resolve();
                    }
                  },
                },
              ]}
            >
              {isViewMode ? (
                <p>{initialValues.ownerPhone || "-"}</p>
              ) : (
                <Input placeholder="请输入手机号"/>
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="伙伴类型"
              name="type"
              rules={[{ required: true, message: "请选择合作状态" }]}
            >
              {isViewMode ? (
                <p>{renderStatusName(partnerTypeOptions, initialValues.type)}</p>
              ) : (
                <Select placeholder="请选择合作状态"
                options={partnerTypeOptions}
                >
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="合作状态"
              name="cooperateStatus"
              rules={[{ required: true, message: "请选择合作状态" }]}
            >
              {isViewMode ? (
                <p>{renderStatusName(partnerStatus, initialValues.cooperateStatus)}</p>
              ) : (
                <Select placeholder="请选择合作状态">
                  <Option value={1}>合作中</Option>
                  <Option value={2}>已结束</Option>
                </Select>
              )}
              </Form.Item>
            </Col>
            </Row>
            <Row justify="end" align="middle">
          {!isViewMode && (
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存
              </Button>
            </Form.Item>
          )}
        </Row>
      </Form>
    </Modal>
  );
};

export default PartnerForm;
