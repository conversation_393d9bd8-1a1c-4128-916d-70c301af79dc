import React,{useState,useEffect} from 'react'
import { Modal,Timeline,Table,message } from 'antd'
import {get,post} from '@/utils/request'
import allUrl from '@/utils/url'
import moment from 'moment'

const GroupLeaderLog = props=>{
    const {visible,onCancel, objectKey} = props
    const [dataSource,setDataSource] = useState([])
    const [loading,setLoading] = useState(false)
    useEffect(() => {
        post(allUrl.WeCom.queryOwnerChangeLog,{objectKey}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                let arr = []
                Dt.length && Dt.forEach((item) => {
                    arr.push(item.log || {})
                })
                setDataSource(arr)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        }).catch(()=>{
            setLoading(false)
        })
    }, []);
    const columns = [
        { title: '时间', dataIndex: 'createTime', width: 120},
        { title: '群主账号ID', dataIndex: 'ownerId', width: 120,},
        { title: '群主姓名', dataIndex: 'ownerName', width: 100 },
        { title: '群主岗位', dataIndex: 'positionName', width: 100 },
        { title: '群主所属组织', dataIndex: 'orgName', width: 160 },
        { title: '群主所属用户中心', dataIndex: 'dealerFullName', width: 180 },
    ]
    return <Modal title='群主变更日志' visible={visible} onCancel={onCancel} maskClosable={true} footer={null} width={800}>
        <Table columns={columns} loading={loading} dataSource={dataSource}  pagination={false} scroll={{ y: 500 }}/>
        {/* {
            dataSource.length  ? <div style={{padding:'20px',height:500,overflowY:'auto'}}>
            <Timeline>
                   {
                       dataSource.map((item,index)=>{
                           return item && item.l1og ? <Timeline.Item key={index}>
                               <div style={{marginBottom:'10px'}}>{item.log.createTime}</div>
                               <div>群主账号ID:{item.log.ownerId}</div>
                               <div>群主:{item.log.ownerName}</div>
                               <div>群主岗位:{item.log.positionName}</div>
                               <div>群主所属组织:{item.log.orgName}</div>
                               <div>群主所属用户中心:{item.log.dealerFullName}</div>
                           </Timeline.Item> : ''
                       })
                   }
               </Timeline>
           </div> : <div style={{textAlign: 'center'}}>暂无数据</div> }
        {*/}
        
    </Modal>
}
export default GroupLeaderLog