import React, { useEffect, useState, forwardRef, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Row, Col, Button, message, Input, Tabs, Tree } from "antd";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
import { utilsDict } from "@/utils/utilsDict";
import _ from "lodash";
import moment from "moment";
import baseURL from "@/baseURL";
import { roleJudgment } from "@/utils/authority";
import { fileDown } from "@/utils";
import "./index.less";
import mapJS from "@/utils/map";
const { Search } = Input;

let map = null;
let infoWindow = null
let marker = null
let storeInfo = null
const StorePosition = () => {
  const [keyword, seTKeyword] = useState("");
  const [oneStoreInfo, setOneStoreInfo] = useState(null);
  const [oneInfoWindow, setOneInfoWindow] = useState(null);
  const [treeData, setTreeData] = useState([]);
  const [searchList, setSearchList] = useState([]);
  useEffect(() => {
    // addPulgin();
    initMap();
    getOrg();
  }, []);
  const addMarkerOnMap = (info) => {
    map.clearMap();
    const lng = Number(info.lng || info.sourceLng);
    const lat = Number(info.lat || info.sourceLat);
    console.log("坐标", lat, lng);
    const lnglat = new window.AMap.LngLat(lng, lat);
    marker = new window.AMap.Marker({
      position: lnglat,
      offset: new window.AMap.Pixel(-10, -10),
      icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
      title: info.name,
      draggable: true,
      cursor: 'move',
    });
    console.log("map", map, marker);
    map.add(marker);
    map.setCenter([lng, lat]);
    addInfoModel(info, lnglat, marker)
    // 添加拖拽停止监听事件
    marker.on ('dragend', function (e) {
      infoWindow.close()
      const newlnglat = new window.AMap.LngLat(e.lnglat.getLng (), e.lnglat.getLat ());
      storeInfo.lng =  e.lnglat.getLng ()
      storeInfo.lat =  e.lnglat.getLat ()
      addInfoModel(storeInfo, newlnglat, marker)
      console.log (' 当前位置：', e.lnglat.getLng (), e.lnglat.getLat ());
    });
  };
  const initMap = () => {
    // 新建地图
    map = new window.AMap.Map("container", {
      zoom: 10, //设置地图显示的缩放级别
      resizeEnable: true,
      city: "021",
      // center: [116.397428, 39.90923],  //设置地图中心点坐标
      viewMode: "2D", //设置地图模式
    });
     // 同时引入工具条插件，比例尺插件和鹰眼插件
     window.AMap.plugin(["AMap.ToolBar", "AMap.Scale", "AMap.ControlBar"], function () {
      // 在图面添加工具条控件，工具条控件集成了缩放、平移、定位等功能按钮在内的组合控件
      map.addControl(new window.AMap.ToolBar({ position: {
        top: '110px',
        right: '40px'
    }}));
      map.addControl(new window.AMap.ControlBar({
        position: {
            top: '10px',
            right: '10px',
        }
    }),)
      // 在图面添加比例尺控件，展示地图在当前层级和纬度下的比例尺
      map.addControl(new window.AMap.Scale());

      // 在图面添加定位控件，用来获取和展示用户主机所在的经纬度位置
      // map.addControl(new AMap.Geolocation());
    });
    
    // //输入提示
    var autoOptions = {
      input: "tipinput",
    };
    // // 加载搜索框
    window.AMap.plugin(["AMap.PlaceSearch", "AMap.AutoComplete"], function () {
      var auto = new window.AMap.AutoComplete(autoOptions);
      var placeSearch = new window.AMap.PlaceSearch({
        map: map,
      }); //构造地点查询类
      auto.on("select", select); //注册监听，当选中某条记录时会触发
      function select(e) {
        console.log(e);
        placeSearch.setCity(e.poi.adcode);
        placeSearch.search(e.poi.name); //关键字查询查询
      }
    });

   
  };
  const getOrg = (name) => {
    post(allUrl.LBS.orgTree, { type: "seres", name }).then((res) => {
      if (res.success) {
        console.log("组织树", res.resp[0]);
        setTreeData(res.resp[0].children);
      } else {
      }
    });
  };
  const searchStore = (name) => {
    post(allUrl.LBS.searchStoreByName, { name }).then((res) => {
      if (res.success) {
        console.log("查询门店", res.resp[0]);
        setTreeData(res.resp[0].children);
      } else {
      }
    });
  };
  const treeSelect = (key, e) => {
    const code = e.node.code;
    console.log(e);
    if(e.node.type !== 'dealer') {
      return false
    }
    post(allUrl.LBS.queryAddress, { code }).then((res) => {
      if (res.success) {
        console.log("门店位置信息", res.resp[0]);
        // setOneStoreInfo(res.resp[0])
        storeInfo = res.resp[0]
        addMarkerOnMap(res.resp[0]);
      } else {
      }
    });
  };
  // 需要绑定在window上否则AMap找不到
  window.updateLngLat = () => {
    post(allUrl.LBS.updateAddress, { id: storeInfo.id, lng: storeInfo.lng || storeInfo.sourceLng, lat: storeInfo.lat || storeInfo.sourceLat }).then((res) => {
      if (res.success) {
        message.success('修改成功！')
      }
    });
  }
  const selectStore = (i) => {
    console.log('selectStore', i)
  }
  const renderList = () => {
    let dom = null
    console.log('渲染列表', keyword)
    if(searchList.length){
        dom = (
            searchList.map(i => {
               return <div key={i.dealerCode} onClick={selectStore(i)}>{i.fullname}</div>
            })
        )
    } else {
        dom = (<Col span={10} className="orgTreeBox">
        {treeData && treeData.length ? (
          <Tree defaultExpandedKeys={[0]} treeData={treeData} onSelect={treeSelect} />
        ) : (
          "暂无数据"
        )}
      </Col>)
    }
    return dom;
   
  };
  
  const items = [
    { label: "seres", key: "seres", children: renderList() }, // 务必填写 key
    // { label: '项目 2', key: 'item-2', children: '内容 2' },
  ];
  
//   新增信息载体
  const addInfoModel = (info, lnglat, marker) => {
    console.log('lnglat', lnglat)
    var infoWindowContent =
    '<div id="infoWindowContent" className="custom-infowindow input-card">' +
        `<label style="color:grey">${info.name}</label>` +
        `<p class="input-item">名称：${info.name}</p>` +
        `<p class="input-item">地址：${info.address}</p>` +
        `<p class="input-item">经纬度：${info.lng || info.sourceLng}, ${info.lat || info.sourceLat}</p>` +
        // 为 infowindow 添加自定义事件
        `<input id="lnglat2container" type="button" class="btn" value="确认修改" onClick="updateLngLat()"/>` +
    '</div>';
    // 创建一个自定义内容的 infowindow 实例
    infoWindow = new window.AMap.InfoWindow({
        position: lnglat,
        // offset: new window.AMap.Pixel(0, -30),
        content: infoWindowContent,
        autoMove: false,
        isCustom: false,
        // anchor: 'center'
    });
    infoWindow.open(map);
   
  }
  
  //  左侧搜索门店
  const SearchStore = (name) => {
    console.log("搜索门店", name);
    if(name) {searchStore(name)} else {getOrg()}
  };
  
  return (
    <div className="storePosition">
      <Row>
        <Col span={8}>
          <div>
            <div className="searchStore">
            <Search placeholder="请输入门店名称" allowClear onSearch={SearchStore} style={{ width: 200 }} />
            </div>
            <Tabs items={items} />
          </div>
        </Col>
        <Col span={16}>
          {/* 地图 */}
          <div id="container"></div>
          {/* 搜索框 */}
          <div id="myPageTop">
            <table>
              <tr>
                <td>
                  <label>请输入关键字：</label>
                </td>
              </tr>
              <tr>
                <td>
                  <input id="tipinput" />
                </td>
              </tr>
            </table>
          </div>
          {/* 工具栏 */}
        </Col>
      </Row>
    </div>
  );
};

export default StorePosition;
