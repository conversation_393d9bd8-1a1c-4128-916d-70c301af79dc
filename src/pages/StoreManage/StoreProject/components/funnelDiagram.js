import React,{useEffect,useState} from 'react'
import {message} from 'antd'
import './legend.less'
import ReactEcharts from "echarts-for-react";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import {get} from '@/utils/request'
import allUrl from '@/utils/url';

const FunnelDiagram = (props) => {
    const {isProjectManager,Style,wrapperStyle} = props
    const colors = ["#6C89FF", "#24B9FF", "#49D3FF", "#71DDFF", "#A0E8FF"];
    const [dataSource,setDataSource] = useState({})
    const getOption = () => {
        return {
            grid: {
                top: 20,
                left: "2%",
                right: 20,
                bottom: 30,
                containLabel: true,
                borderWidth: 0.5,
            },
            // color:colors,
            series: [
                {
                    name: 'Funnel',
                    type: 'funnel',
                    left: '10%',
                    top: 30,
                    bottom: 30,
                    width: '80%',
                    max: 100,
                    min: 0,
                    minSize: '3%',
                    maxSize: '60%',
                    gap: 12,
                    label:{
                        color:'rgba(0,0,0,0.65)',
                        fontSize:'14px'
                    },
                    itemStyle:{
                        color:function(params){
                            return colors[params.dataIndex]
                        }
                    },
                    emphasis:{
                        disabled:true
                    },
                    data: [
                        { value: 100, name: `入网用户中心（待提交${dataSource.waitSubmitNumber}个）`},
                        { value: 80, name: `准入提交（一验中${dataSource.firstCheckingNumber}个；一验整改中${dataSource.firstCheckFailNumber}个）` },
                        { value: 60, name: `一验通过（二验中${dataSource.secendCheckingNumber}个；二验整改中${dataSource.secendCheckFailNumber}个）` },
                        { value: 40, name: `二验通过（综验中${dataSource.finalCheckingNumber}个；综验整改中${dataSource.finalCheckFailNumber}个）` },
                        { value: 20, name: `综验通过` },
                    ]
                  },
                  {
                    name: 'Actual',
                    type: 'funnel',
                    left: '10%',
                    top: 30,
                    bottom: 30,
                    width: '80%',
                    max: 100,
                    min: 0,
                    minSize: '3%',
                    maxSize: '60%',
                    gap: 12,
                    label: {
                      position: 'inside',
                      color: '#fff'
                    },
                    itemStyle:{
                        color:function(params){
                            return colors[params.dataIndex]
                        }
                    },
                    data: [
                        { value: 100, name: dataSource.onlineNumber },
                        { value: 80, name: dataSource.submitedNumber },
                        { value: 60, name: dataSource.firstCheckedNumber },
                        { value: 40, name: dataSource.secendCheckedNumber },
                        { value: 20, name: dataSource.finalCheckedNumber },
                    ],
                    
                  }
            ],
        };
    }

    const faultTolerant = (value) =>{
        return value ? value :'0%'
    }

    useEffect(()=>{
        get(allUrl.StoreManage.getFunnelFigure,{isProjectManager}).then(res=>{
            if(res.success){
                setDataSource(res.resp[0])
            }else{
                // message.error(res.msg)
            }
        })
    },[])

    const lineArr = [
        {name:'准入提交率',value:faultTolerant(dataSource.submitRatio),style:{left: '10%',top:'20%',width:'100%'},borderStyle:{width:'10%'}},
        {name:'一验通过率',value:faultTolerant(dataSource.firstCheckedRatio),style:{left: '10%',top:'36.5%',width:'100%'},borderStyle:{width:'14%'}},
        {name:'二验通过率',value:faultTolerant(dataSource.secendCheckedRatio),style:{left: '10%',top:'53%',width:'100%'},borderStyle:{width:'19%'}},
        {name:'综验通过率',value:faultTolerant(dataSource.finalCheckedRatio),style:{left: '10%',top:'70%',width:'100%'},borderStyle:{width:'23%'}}
    ]


    return (
        <div className='legend' style={Style?Style:{ borderRight: 'solid 12px #f0f2f5' }}>
            <div className='legend_title'>
                项目管理数据统计
            </div>
            <div className='legend_wrapper' style={{position: 'relative',width:'60%',margin:'auto',...wrapperStyle}}>
                <ReactEcharts
                    style={{ minHeight: "400px",zIndex:10000 }}
                    option={getOption()}
                />
                <div className='legend_name'>项目节点漏斗图</div>
                <div className='line_box'>
                    {
                        lineArr.map((item,index)=>{
                            return <div className='lint_item' key={index} style={{...item.style}}>
                                <div style={{width:'132px',color: 'rgba(0,0,0,0.65)',fontSize: '14px',cursor: 'pointer'}}>{item.name + item.value}</div>
                                <div style={{height:'1px',width:'11%',background:'red',cursor: 'pointer',opacity:0.8,backgroundColor:colors[index],...item.borderStyle}}></div>
                            </div>
                        })
                    }
                </div>
            </div>
        </div>
    )
}
export default FunnelDiagram
