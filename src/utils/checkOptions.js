export const detailOptions = [
  {
    label: '权益明细编码',
    value: 'detailNo',
  },
  {
    label: '权益明细名称',
    value: 'name',
  },
  {
    label: '权益业务场景',
    value: 'businessScene',
  },
  {
    label: '权益履约方',
    value: 'performingLists',
  },
  {
    label: '业务编码',
    value: 'businessCode',
  },
  {
    label: '业务分类',
    value: 'businessCodeType',
  },
  {
    label: '业务应用',
    value: 'businessUseComment',
  },
  {
    label: '权益分类',
    value: 'sortCode',
  },
  {
    label: '权益类别',
    value: 'typeCode',
  },
  {
    label: '车辆属性',
    value: 'carAttr',
  },
  {
    label: '权益归属',
    value: 'belong',
  },
  {
    label: '是否支持退款',
    value: 'refund',
  },
  {
    label: '是否支持第三方履约',
    value: 'performance',
  },
  {
    label: '权益声明',
    value: 'statement',
  },
  {
    label: '权益备注',
    value: 'remark',
  },
  {
    label: '创建时间',
    value: 'createTime',
  },
  {
    label: '权益限制年限',
    value: 'limitYear',
  },
  {
    label: '权益生效时间',
    value: 'relativeTime',
  },
  {
    label: '权益限制里程',
    value: 'limitLegend',
  },
  {
    label: '权益生效里程',
    value: 'effectMileage',
  },
  {
    label: '享有权益频次',
    value: 'frequency',
  },
  {
    label: '权益属性',
    value: 'identity',
  },
  {
    label: '享有车主身份',
    value: 'carIdentity',
  },
  {
    label: '补漆面部位',
    value: 'paintDesc',
  },
  {
    label: '车联网流量',
    value: 'traffic',
  },
  {
    label: '权益金额抵扣',
    value: 'deduction',
  },
  {
    label: '延保激活时间',
    value: 'extendWarrantyActiveTime',
  },
  // {
  //     label: '权益支持区域',
  //     value: 'allArea'
  // }, {
  //     label: '权益支持门店',
  //     value: 'allDealer'
  // },
];

export const packageInfoOptions = [
  {
    label: '权益包编码',
    value: 'packNo',
  },
  {
    label: '权益包名称',
    value: 'packName',
  },
  {
    label: '权益包属性',
    value: 'attribute',
  },
  {
    label: '权益包类型',
    value: 'type',
  },
  {
    label: '车辆市场类型',
    value: 'carMarketType',
  },
  {
    label: '宣传时间',
    value: 'activityBeginTime',
  },
  {
    label: '创建时间',
    value: 'createTime',
  },
  {
    label: '修改时间',
    value: 'updateTime',
  },
  {
    label: '权益车型',
    value: 'carType',
  },
  {
    label: '权益车型配置',
    value: 'carTypeConfig',
  },
  {
    label: '权益支持门店',
    value: 'allDealer',
  },
  {
    label: '权益支持区域',
    value: 'allArea',
  },
  {
    label: '权益包状态',
    value: 'status',
  },
  {
    label: '权益包描述',
    value: 'description',
  },
  {
    label: '权益请示编码',
    value: 'askingNo',
  },
  {
    label: '权益请示活动有效期',
    value: 'askingTime',
  },
  {
    label: '权益请示活动数量',
    value: 'askingCount',
  },
  {
    label: '活动名称',
    value: 'promotionName',
  },
  {
    label: '权益请示名称',
    value: 'askingName',
  },
  {
    label: '销售渠道',
    value: 'salesChannel',
  },
  {
    label: '权益来源',
    value: 'equitySource',
  },
  {
    label: '单车预算',
    value: 'budgetSingle',
  },
  {
    label: '合计预算',
    value: 'budgetAmount',
  },
  {
    label: '活动发起部门',
    value: 'promotionInitiator',
  },
  {
    label: '销售结算主体',
    value: 'salesSubject',
  },
  {
    label: '成本价格',
    value: 'costPrice',
  },
  {
    label: '销售价格',
    value: 'price',
  },
  {
    label: '采购价格',
    value: 'purchasePrice',
  },
  {
    label: '销售服务费结算比例',
    value: 'percentage',
  },
  {
    label: '销售收入',
    value: 'salesIncome',
  },
  {
    label: '权益包销售主体',
    value: 'equitySalesSubject',
  },
  {
    label: '是否应用于活动中',
    value: 'promotionExist',
  },
  {
    label: '活动名额',
    value: 'promotionQuota',
  },
  {
    label: '活动时间',
    value: 'promotionTimeFrame',
  },
  {
    label: '活动状态',
    value: 'promotionStatus',
  },
  {
    label: '权益包版本编码',
    value: 'packVersion',
  },
];

export const packageDetailOptions = [
  {
    label: '权益明细编码',
    value: 'detailNo',
  },
  {
    label: '权益明细名称',
    value: 'name',
  },
  {
    label: '权益业务场景',
    value: 'businessSceneDesc',
  },
  {
    label: '权益履约方',
    value: 'performingLists',
  },
  {
    label: '业务编码',
    value: 'businessCode',
  },
  {
    label: '业务分类',
    value: 'businessCodeType',
  },
  {
    label: '业务应用',
    value: 'businessUseComment',
  },
  {
    label: '权益分类',
    value: 'sortCode',
  },
  {
    label: '权益类别',
    value: 'typeCode',
  },
  {
    label: '车辆属性',
    value: 'carAttr',
  },
  {
    label: '权益归属',
    value: 'belong',
  },
  {
    label: '是否支持退款',
    value: 'refund',
  },
  {
    label: '是否支持三方履约',
    value: 'performance',
  },
  {
    label: '权益声明',
    value: 'statement',
  },
  {
    label: '权益备注',
    value: 'remark',
  },
  {
    label: '权益限制年限',
    value: 'limitYear',
  },
  {
    label: '权益生效时间',
    value: 'relativeTime',
  },
  {
    label: '权益限制里程（公里）',
    value: 'limitLegend',
  },
  {
    label: '权益生效里程',
    value: 'effectMileage',
  },
  {
    label: '享有权益频次',
    value: 'frequency',
  },
  {
    label: '权益属性',
    value: 'identity',
  },
  {
    label: '享有车主身份',
    value: 'carIdentity',
  },
  {
    label: '补漆面部位',
    value: 'paintDesc',
  },
  {
    label: '车联网流量',
    value: 'traffic',
  },
  {
    label: '权益抵扣金额',
    value: 'deduction',
  },
  {
    label: '延保激活时间',
    value: 'extendWarrantyActiveTime',
  },
  {
    label: '权益明细版本编码',
    value: 'detailVersion',
  },
];

export const accountInfoOptions = [
  {
    label: '权益账户编码',
    value: 'accountNo',
  },
  {
    label: '购车人手机号',
    value: 'carBuyerPhone',
  },
  {
    label: '购车人',
    value: 'carBuyerName',
  },
  {
    label: '车型',
    value: 'carType',
  },
  {
    label: '车辆VIN',
    value: 'vin',
  },
  {
    label: '基础配置',
    value: 'baseConfig',
  },
  {
    label: '车辆销售类型',
    value: 'transferType',
  },
  {
    label: '车主姓名',
    value: 'carOwnerName',
  },
  {
    label: '车主手机号',
    value: 'carOwnerPhone',
  },
];

export const accountOrderOptions = [
  {
    label: '意向订单号',
    value: 'bookOrderNo',
  },
  {
    label: '正式订单号',
    value: 'formalOrderNo',
  },
  {
    label: '华为订单号',
    value: 'hwOrderNo',
  },
  {
    label: '销售订单号',
    value: 'saleOrderNo',
  },
  {
    label: '交付订单号',
    value: 'deliverOrderNo',
  },
];

export const accountTypeOptions = [
  {
    label: '基础权益包',
    value: 'baseNo',
  },
  {
    label: '付费权益包',
    value: 'payNo',
  },
  {
    label: '赠送权益包',
    value: 'giveNo',
  },
];
export const accountRightOptions = [
  {
    label: '权益包编码',
    value: 'packNo',
  },
  {
    label: '权益包名称',
    value: 'packName',
  },
  {
    label: '权益包属性',
    value: 'attribute',
  },
  {
    label: '车辆销售类型',
    value: 'packSalesType',
  },
  {
    label: '权益明细编码',
    value: 'detailNo',
  },
  {
    label: '权益明细名称',
    value: 'name',
  },
  {
    label: '权益生效时间（对内核销）',
    value: 'effectTime',
  },
];
