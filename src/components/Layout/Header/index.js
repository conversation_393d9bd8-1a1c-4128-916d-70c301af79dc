import React ,{useState,useEffect} from 'react'
import Cookies from 'js-cookie'
import { Layout, message } from 'antd'
import { connect } from "react-redux";
import { Avatar, Dropdown, Menu } from 'antd';
import { UserOutlined ,MenuUnfoldOutlined ,MenuFoldOutlined,DownOutlined } from '@ant-design/icons';
import {setSiderNormal,changePsd} from '@/actions/normal'
import {get} from '@/utils/request'
import allUrl from '@/utils/url'
import ChangePsd from '../Common/ChangePsd'
import history from '@/utils/history'
import logo from '@/assets/img/logo.png'
import logo2x from '@/assets/img/logo2x.png'
import ChangeLoginOrg from '../Common/ChangeLoginOrg'
import baseURL from '@/baseURL'
const { Header } = Layout;

const HeaderComponent =  (props) => {
    let { SiderType ,dispatch ,userInfo,changePwdVisible} = props
    const [OrgVisible,setOrgVisible] = useState(false)
    const [orgList,setOrgList] = useState([])
    const [curOrgInfo,setCurOrgInfo] = useState({})
    const [currentOrgId, setCurrentOrgId] = useState('')
    userInfo =  JSON.parse(localStorage.getItem('userInfo'))
    const logout = () => {
        if(Cookies.get('isTransfer') === '1'){
            get(allUrl.Authority.sso_logout,null).then(res=>{
                if(res.success){
                    let Dt = res.resp[0]
                    console.log(baseURL)
                    Cookies.remove('scrm_token')
                    Cookies.remove('isTransfer')
                    localStorage.removeItem('userInfo')
                    sessionStorage.clear()
                    window.location.href = `${baseURL.SSO_Host}#/Transfer?authorization=${Dt.accessToken}`
                    
                }else{
                    // message.error(res.msg)
                }
            })
        }else{
            get(allUrl.Authority.logout,null).then(res=>{
                if(res.success){
                    Cookies.remove('scrm_token')
                    localStorage.removeItem('userInfo')
                    sessionStorage.clear()
                    // history.push('/')
                    window.location.href = `${window.location.origin}/#/login`
                }else{
                    // message.error(res.msg)
                }
            })
        }
    }
    const onCollapse = collapsed => {
        dispatch(setSiderNormal(collapsed))
    }
    const showChangeOrgModal = () => {
        if(orgList.length > 1) {
            setOrgVisible(true)
        }
    }
    const changeLoginOrg = () => {
        setOrgVisible(false)
    }
    const getOrgData = () => {
        get(allUrl.Authority.headerOrgList).then(res=>{
            if(res.success){
                const organizations = res.resp[0].organizations || []
                if(!userInfo.boolAdmin) {
                    setOrgList(organizations)
                    getCurrentLoginOrg(organizations, userInfo.loginOrganizationId ? +userInfo.loginOrganizationId.split(',')[0]:null)
                }
                
            }else{
                // message.error(res.msg || '密码修改失败！')
            }
        })
    }
    const getCurrentLoginOrg = (arr, id) => {
        const info = arr.filter(i => i.id === id)
        setCurOrgInfo(info[0])
    }

    const closeChangePsd = () =>{
        console.log(sessionStorage.getItem('changePwdStatus'))
        dispatch(changePsd(false))
        if(sessionStorage.getItem('changePwdStatus') == 'true'){
            localStorage.removeItem('remember')
            logout()
        }

    }
    const menu = (
        <Menu>
            <Menu.Item key='1' style={{padding: '4px 12px'}} onClick={()=>dispatch(changePsd(true))}>
                <span>修改密码</span>
            </Menu.Item>
            <Menu.Item key='2' style={{padding: '4px 12px'}} onClick={() => logout()}>
                <span>退出登录</span>
            </Menu.Item>
            
        </Menu>
    )

    useEffect(() => {
        if(userInfo && userInfo.id) {
            setCurrentOrgId(+userInfo.loginOrganizationId)
            getOrgData()
        }
    }, []);


    return (
        <Header style={{ zIndex: 1, left: 0 }} className='header'>
            <div className={`header_left ${SiderType?'header_leftMin':'header_leftMax'}`} style={{ width: SiderType ? '80px' : '208px',minWidth:SiderType ? '80px' : '208px' }}>
                {
                    SiderType ? 
                    <img className='logoMin' alt='logo' src={logo} />
                    : <div>
                        <img className='logoMax' alt='logo' src={logo} />
                        <span style={{fontSize: '16px', color: '#fff'}}>SERES协同系统</span>
                    </div>
                }
            </div>
            <div className='header_right'>
                <div className='menuOperation' onClick={()=>onCollapse(!SiderType)}>
                    {
                        SiderType ?
                        <MenuUnfoldOutlined style={{ fontSize: '16px'}} />
                        :<MenuFoldOutlined style={{ fontSize: '16px'}} />
                    }
                </div>
                <div className='header-con'>
                    <div className='org-info'>
                        {
                            userInfo && !userInfo.boolAdmin && curOrgInfo &&
                              <><span className='label'>当前组织：</span><span onClick={showChangeOrgModal} className={`desc ${orgList.length > 1 ? 'cusor' : ''}`}>{userInfo.organizationsDescription}</span></>
                        }
                    </div>
                </div>
                <Dropdown overlay={menu}>
                    <div className='personalInfor'>
                        <Avatar size="small" icon={<UserOutlined />} />
                            <div style={{ marginLeft: '5px' ,marginRight:'5px' }}>{userInfo ? userInfo.userName : ''}</div>
                            <DownOutlined />
                    </div>
                </Dropdown>
            </div>
            {
                OrgVisible && 
                <ChangeLoginOrg visible={OrgVisible} list={orgList} curOrgInfo={curOrgInfo} onOk={changeLoginOrg} setCurrentOrgId={setCurrentOrgId} setCurOrgInfo={setCurOrgInfo} onCancel={()=>setOrgVisible(false)} ></ChangeLoginOrg>
            }
            {
                changePwdVisible && 
                <ChangePsd visible={changePwdVisible} userInfo={userInfo} title={'密码重置'} onOk={()=>dispatch(changePsd(false))} onCancel={closeChangePsd} />
            }
        </Header>
    )
}

export default connect(({ common }) => {
    const { SiderType, userInfo, changePwdVisible } = common;
    return {
      SiderType,
      userInfo,
      changePwdVisible,
    };
  })(HeaderComponent);