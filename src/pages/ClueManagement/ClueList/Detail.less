.ClueDetail{
    .FormRow{
        .FormRowTitle{
            .FormRowTitleIndex{
                height:20px;
                width:20px;
                line-height:20px;
                border-radius:50%;
                background:#e6f7ff;
                font-size:13px;
                color:#1890ff;
                text-align:center;
                display:inline-block;
                margin-right:10px;
            }
            .del{
                width: 64px;
                height: 32px;
                line-height: 32px;
                text-align: center;
                border: solid 1px #E9E9E9;
                cursor: pointer;
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                font-weight: 400;
                color: rgba(0,0,0,0.65);
                border-radius: 2px;
            }
            .del:hover{
                border: solid 1px #1890ff;
                color: #1890ff;
            }
        }
        .FormRowCon{
            .ant-radio-group{
                .ant-radio-wrapper{
                    width: 100%;
                    line-height: 56px;
                    .yxys{
                        margin-right: 8px;
                    }
                }
            }
        }
        .Add{
            background: #ffffff;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            height: 32px;
            width: 100%;
            line-height: 32px;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
            font-weight: 400;
            // text-align: left;
            color: rgba(0,0,0,0.65);
            text-align: center;
            margin-bottom: 90px;
            cursor: pointer;
        }
        .AddDis{
            background: #f5f5f5;
            color: rgba(0, 0, 0, 0.35);
        }
    }
    .ClueDetailLookAt{
        .InitialInfo{
            .InitialInfo_Personal{
                margin-bottom: 14px;
                img{
                    width: 14px;
                }
                .InitialInfo_Name{
                    font-size: 16px;
                    font-family: PingFangSC, PingFangSC-Semibold, sans-serif; 
                    font-weight: 600;
                    text-align: left;
                    color: #333333;
                    margin-right: 10px;
                }
                .InitialInfo_Gender{
                    margin-right: 5px;
                    font-size: 12px;
                    font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                    font-weight: 400;
                    text-align: left;
                    color: #888888;
                }
                .InitialInfo_PhoneBox{
                    margin-top: 14px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .InitialInfo_Phone{
                        margin-left: 8px;
                        margin-right: 20px;
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: left;
                        color: #333333;
                    }
                    .InitialInfo_Status{
                        display: inline-block;
                        vertical-align: middle;
                        width: 60px;
                        height: 24px;
                        line-height: 24px;
                        // background: rgba(24,144,255,0.08);
                        border-radius: 12px;
                        font-size: 11px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: center;
                        // color: #1890ff;
                        // border: 0px solid rgba(24,144,255,0.49);
                    }
                    .InitialInfo_StatusBule{
                        background: rgba(255,170,0,0.06);
                        border: 0px solid #ffaa00;
                        color: #ffaa00;
                    }

                    .InitialInfo_CreateInfo{
                        font-size: 12px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: right;
                        color: #888888;
                        line-height: 17px;
                    }
                }
                .InitialInfo_Remarks{
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                    font-weight: 400;
                    text-align: left;
                    color: #666666;
                    margin-top: 16px;
                }
            }
        }
        .MaintenanceInfo{
            .MaintenanceInfo_title{
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.65);
                margin-bottom: 16px;
            }
            .InitialInfo{
                .InitialInfo_Personal{
                    margin-bottom: 14px;
                    img{
                        width: 14px;
                    }
                    .InitialInfo_NameBox{
                        display: flex;
                        justify-content:space-between;
                        align-items: center;
                        .InitialInfo_Name{
                            font-size: 16px;
                            font-family: PingFangSC, PingFangSC-Semibold, sans-serif; 
                            font-weight: 600;
                            text-align: left;
                            color: #333333;
                            margin-right: 10px;
                        }
                        .InitialInfo_Gender{
                            margin-right: 5px;
                            font-size: 12px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: left;
                            color: #888888;
                        }
                        .InitialInfo_CreateInfo{
                            font-size: 12px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: right;
                            color: #888888;
                            line-height: 17px;
                        }
                    }
                    .InitialInfo_PhoneBox{
                        margin-top: 14px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .InitialInfo_Phone{
                            margin-left: 8px;
                            margin-right: 20px;
                            font-size: 14px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: left;
                            color: #333333;
                        }
                        .InitialInfo_CreateInfo{
                            font-size: 12px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: right;
                            color: #888888;
                            line-height: 17px;
                        }
                    }
                    .InitialInfo_Remarks{
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: left;
                        color: #666666;
                        margin-top: 16px;
                    }
                }
            }
            .IntentionInfo{
                .IntentionInfoTitle{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 14px;
                    .IntentionInfoLeft{
                        font-size: 15px;
                        font-family: PingFangSC, PingFangSC-Semibold, sans-serif; 
                        font-weight: 600;
                        text-align: left;
                        color: #333333;
                        .IntentionInfoIndex{
                            display: inline-block;
                            width: 20px;
                            height: 20px;
                            line-height: 20px;
                            text-align: center;
                            margin-right: 9px;
                            background: #e6f7ff;
                            color: #1890ff;
                            border-radius: 50%;
                            font-size: 13px;
                        }
                    }
                    .IntentionInfoRight{
                        font-size: 12px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: right;
                        color: #888888;
                    }
                }
                .IntentionInfoCon{
                    margin-bottom: 14px;
                    .IntentionInfoConItem{
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: left;
                        color: #333333;
                        margin-bottom: 10px;
                        img{
                            width: 98px;
                            height: 72px;
                        }
                        .IntentionInfoConTitle{
                            font-family: PingFangSC, PingFangSC-Semibold, sans-serif; 
                            font-weight: 600;
                            text-align: left;
                            color: #333333;
                            font-size: 14px;
                        }
                    }
                    
                }
            }
            
        }
        .FollowUpRecord{
            .FollowUpRecordTitle{
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.65);
                margin-bottom: 16px;
            }
            .FollowUpRecordItem{
                margin-bottom: 32px;
                .FollowUpRecordTime{
                    font-size: 12px;
                    font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                    font-weight: 400;
                    text-align: left;
                    color: #888888;
                    margin-bottom: 8px;
                    span{
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        margin-right: 8px;
                        background: #BCBCBC;
                        border-radius: 50%;
                        vertical-align: middle;
                    }
                }
                .FollowUpRecordCon{
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                    font-weight: 400;
                    text-align: left;
                    color: #333333;
                    line-height: 24px;
                    padding-left: 16px;
                }
                .FollowUpRecordName{
                    font-size: 11px;
                    font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                    font-weight: 400;
                    color: #888888;
                    text-align: right;
                }
            }
        }
        .more{
            font-size: 12px;
            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
            font-weight: 400;
            // text-align: left;
            color: #888888;
            text-align: center;
        }
        .xiaoshouinfo{
            .MaintenanceInfo_title{
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.65);
                margin-bottom: 16px;
            }
            .InitialInfo{
                .InitialInfo_Personal{
                    margin-bottom: 14px;
                    img{
                        width: 14px;
                    }
                    .InitialInfo_NameBox{
                        display: flex;
                        justify-content:space-between;
                        align-items: center;
                        .InitialInfo_Name{
                            font-size: 16px;
                            font-family: PingFangSC, PingFangSC-Semibold, sans-serif; 
                            font-weight: 600;
                            text-align: left;
                            color: #333333;
                            margin-right: 10px;
                        }
                        .InitialInfo_Gender{
                            margin-right: 5px;
                            font-size: 12px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: left;
                            color: #888888;
                        }
                        .InitialInfo_CreateInfo{
                            font-size: 12px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: right;
                            color: #888888;
                            line-height: 17px;
                        }
                    }
                    .InitialInfo_PhoneBox{
                        margin-top: 14px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .InitialInfo_Phone{
                            margin-left: 8px;
                            margin-right: 20px;
                            font-size: 14px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: left;
                            color: #333333;
                        }
                        .InitialInfo_CreateInfo{
                            font-size: 12px;
                            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                            font-weight: 400;
                            text-align: right;
                            color: #888888;
                            line-height: 17px;
                        }
                    }
                    .InitialInfo_Remarks{
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: left;
                        color: #666666;
                        margin-top: 16px;
                    }
                }
            }
            .IntentionInfo{
                .IntentionInfoTitle{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 14px;
                    .IntentionInfoLeft{
                        font-size: 15px;
                        font-family: PingFangSC, PingFangSC-Semibold, sans-serif; 
                        font-weight: 600;
                        text-align: left;
                        color: #333333;
                        .IntentionInfoIndex{
                            display: inline-block;
                            width: 20px;
                            height: 20px;
                            line-height: 20px;
                            text-align: center;
                            margin-right: 9px;
                            background: #e6f7ff;
                            color: #1890ff;
                            border-radius: 50%;
                            font-size: 13px;
                        }
                    }
                    .IntentionInfoRight{
                        font-size: 12px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: right;
                        color: #888888;
                    }
                }
                .IntentionInfoCon{
                    margin-bottom: 14px;
                    .IntentionInfoConItem{
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                        font-weight: 400;
                        text-align: left;
                        color: #333333;
                        margin-bottom: 10px;
                        .IntentionInfoConTitle{
                            font-family: PingFangSC, PingFangSC-Semibold, sans-serif; 
                            font-weight: 600;
                            text-align: left;
                            color: #333333;
                            font-size: 14px;
                        }
                    }
                    
                }
            }
        }
    }
}