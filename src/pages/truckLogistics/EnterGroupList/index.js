import './index.less'
import React, { useEffect, useState,useRef,useCallback } from 'react'
import { useSelector,useDispatch } from 'react-redux'
import { Table,message, Tabs ,Row,Col,Button,Modal,Radio,Popconfirm,Form,Input,Select,Menu,Dropdown,Timeline,Tag,DatePicker} from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons';
import moment from 'moment'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import { get,post } from '@/utils/request';
import {trim} from '@/utils'
import _ from "lodash";
import allUrl from '@/utils/url';
import {roleJudgment} from '@/utils/authority'
import {setStorage,getStorage} from '../../../utils/Storage'
import {getDict} from '../../../actions/async'
import ExtendedColumn from '@/components/Public/ExtendedColumn'
import ExtendedColumnNew from '@/components/Public/ExtendedColumnNew'
import { renderStatusName, fileDown } from '@/utils'
const { confirm } = Modal;
const { TabPane } = Tabs;
const { Option } = Select;
const ENTER_STATUS = [ { name: '待核销',key: 1, value: 1 },{ name: '已取消',key: 2, value: 2 }, { name: '已核销',key: 3, value: 3 },] 
const BOARD_STATUS = [ { name: '待组板',key: 1, value: 1 },{ name: '已取消',key: 2, value: 2 }, { name: '组板完成',key: 3, value: 3 },] 
const BOARD_TYPE = [ { name: '物流组板',key: 1, value: 1 },{ name: '中转库出库',key: 2, value: 2 }] 
const LOG_TYPE = [ { name: '修改进场预约时间',key: 1, value: 1 },{ name: '修改进场时间限制',key: 2, value: 2 },{ name: '强制组板',key: 3, value: 3 },{ name: '物流管理员补偿送达',key: 4, value: 4 }] 
const ClueList = (props) => {
    //创建ref节点
    const tableRef1 = useRef()
    const tableRef2 = useRef()
    const childRef1 = useRef(null);
    const childRef2 = useRef(null);
    const [driverform] = Form.useForm();
    const driverformRef = useRef(null);
    const dispatch = useDispatch()
    const { userInfo } = useSelector(state => state.common)
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const [current, setCurrent] = useState(1)
    const [pageSize, setPageSize] = useState(10)
    const [total, setTotal] = useState(0)
    const [tableHeight,setTableHeight] = useState(0)
    const [title,setTitle]=useState({})
    const [isExpand, setIsExpand] = useState(false)
    const [operationVisible,setOperationVisible] = useState(false)
    const [operationVisible1,setOperationVisible1] = useState(false)
    const [operationVisible2,setOperationVisible2] = useState(false)
    const [operationVisible3,setOperationVisible3] = useState(false)
    const [dataSource3, setDataSource3] = useState([])
    const [driverOptions,setdriverOptions] = useState([])
    const [carOptions,setcarOptions] = useState([])
    const [transferClockList,settransferClockList] = useState([])
    const [recordObj,setRecordObj] = useState({})
    const [driverName,setDriveName] = useState('')
    const [driverPhone,setdriverPhone] = useState('')
    const [logisticsProviderNo,setlogisticsProviderNo] = useState('')
    const [logisticsProviderName,setlogisticsProviderName] = useState('')
    const [transferInput,settransferInput] = useState(false)
    const [transferDefult,settransferDefult] = useState(true)
    const [toTrailerLicensePlate,settoTrailerLicensePlate] = useState('')
    const [turnform] = Form.useForm();
    const turnformRef = useRef(null);
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('CarrierListTabsKey') || '1')
    const [defaultQuery, setDefaultQuery] = useState(
        // sessionStorage.getItem('CarrierListDefaultQuery') ? JSON.parse(sessionStorage.getItem('CarrierListDefaultQuery')):{carriageStatusList: [0, 1]}
        {
            enterTime:[moment().startOf('day'), moment().endOf('day')],
            beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            completeTime:[],
            completeTimeStart: '',
            completeTimeEnd: '',
            createTime: [moment().subtract(31, 'days').startOf('day'), moment().endOf('day')],
            createTimeStart: moment().subtract(31, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            createTimeEnd: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            dmsCancelTime:[],
            dmsCancelTimeStart:'',
            dmsCancelTimeEnd: '',
            
        // carriageStatusList: [0, 1]
        // enterTime:[moment('2015-06-06'),moment().endOf('day')],
        }
    )
    const [defaultQuery1, setDefaultQuery1] = useState(
        {
            enterTime:[moment().startOf('day'), moment().endOf('day')],
            beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            status: [1, 3]
        }
    )
    const [defaultQuery2, setDefaultQuery2] = useState(
        {
            createTime: [moment().subtract(31, 'days').startOf('day'), moment().endOf('day')],
            createTimeStart: moment().subtract(31, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            createTimeEnd: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        }
    )
    const [enterConfigVisible,setEnterConfigVisible] = useState(false);
    const [updateTimeVisible, setUpdateTimeVisible] = useState(false);
    const [enterBeginTime, setEnterBeginTime] = useState('');
    const [enterEndTime, setEnterEndTime] = useState('');
    const [orgList,setOrgList]= useState([]); // 所有组织字典
    const [warehouseList,setWarehouseList]= useState([]); // 仓库字典
    const [logisticsProviderList,setLogisticsProviderList]= useState([]); // 物流商字典
    const [enterConfigRadio, setEnterConfigRadio] = useState(1)
    const [oneEntryNoVinList, setOneEntryNoVinList] = useState([])
    const [phoneVisible, setPhoneVisible] = useState(false)
    const [pePhone, setPePhone] = useState('')
   
    // 获取组织列表
    const getLogisticsProviderList =(type)=>{
        post(allUrl.truckLogistics.logisticsProviderList, {type}).then(res => {
            if (res.success && res.resp[0]) {
                let arr1 = [], arr2 = []
                if(res.resp[0].length) {
                    res.resp[0].map((i) => {
                        if(i.type == 1) {
                            arr1.push(i)
                        } else {
                            arr2.push(i)
                        }
                    })
                }
                setOrgList(res.resp[0])
                setLogisticsProviderList(arr1)
                setWarehouseList(arr2)
            } else {
                message.error(res.msg || '获取物流服务商失败')
            }
        }) 
    }
    // 获得当前登录人对应的默认物流商
    const getDefaultLogisticsProvider =()=>{
        get(allUrl.truckLogistics.getDefaultLogisticsProvider).then(res => {
            if (res.success && res.resp[0]) {
                setlogisticsProviderNo(res.resp[0].value)
                setlogisticsProviderName(res.resp[0].name)
            } else {
                settransferDefult(false)
            }
        }) 
    }
 
    const onSearch = (values, key) => {
        console.log('onsearch', key ,tabsKey)
        key = key || tabsKey
        if (values.enterTime && values.enterTime.length) {
            const [start, end] = values.enterTime;
            const diffDays = end.diff(start, 'days');
            if (diffDays > 31) {
                message.error('选择的预约进场时间范围不能超过31天');
                return;
            }
            values.beginTime = moment(values.enterTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.endTime = moment(values.enterTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.enterTime
        }
        if (values.createTime && values.createTime.length) {
            const [start, end] = values.createTime;
            const diffDays = end.diff(start, 'days');
            if (diffDays > 31) {
                message.error('选择的组板创建时间范围不能超过31天');
                return;
            }
            values.createTimeStart = moment(values.createTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.createTimeEnd = moment(values.createTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.createTime
        }
        if (values.completeTime && values.completeTime.length) {
            const [start, end] = values.completeTime;
            const diffDays = end.diff(start, 'days');
            if (diffDays > 31) {
                message.error('选择的组板完成时间范围不能超过31天');
                return;
            }
            values.completeTimeStart = moment(values.completeTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.completeTimeEnd = moment(values.completeTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.completeTime
        }
        if (values.dmsCancelTime && values.dmsCancelTime.length) {
            const [start, end] = values.dmsCancelTime;
            const diffDays = end.diff(start, 'days');
            if (diffDays > 31) {
                message.error('选择的组板取消时间范围不能超过31天');
                return;
            }
            values.dmsCancelTimeStart = moment(values.dmsCancelTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.dmsCancelTimeEnd = moment(values.dmsCancelTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.dmsCancelTime
        }
        setDefaultQuery(values)
        if (key === '1') {
            setDefaultQuery1(values)
        }
        if (key === '2') {
            setDefaultQuery2(values)
        }
        setStorage({
            Query: {...values},
            Type: window.location.hash,
        });
        if((childRef1.current ? !childRef1.current.formDown : true) && sessionStorage.getItem('CarrierListTabsKey')=== '1'){
            // 待处理运单未展开时手动添加搜索条件【0，1】
            // let data ={...values,carriageStatusList: [0, 1]}
            // setDefaultQuery(data)
            setStorage({
                // Query: {...data},
                Type: window.location.hash,
            });
        }
        if((childRef2.current ? !childRef2.current.formDown : true) && sessionStorage.getItem('CarrierListTabsKey')=== '2'){
            // 历史运单未展开时手动添加搜索条件【2，3】
            // let data ={...values,carriageStatusList: [2, 3]}
            // setDefaultQuery(data)
            setStorage({
                // Query: {...data},
                Type: window.location.hash,
            });
        }
        
      
    }
    // 导出
    const exportData = useCallback(() => {
        console.log('current',tabsKey, childRef1.current, childRef2)
        if (tabsKey === '1') {
            let values = childRef1.current.query
            if (values.enterTime && values.enterTime.length) {
                const [start, end] = values.enterTime;
                const diffDays = end.diff(start, 'days');
                if (diffDays > 31) {
                    message.error('选择的预约进场时间范围不能超过31天');
                    return;
                }
                values.beginTime = moment(values.enterTime[0]).format('YYYY-MM-DD HH:mm:ss')
                values.endTime = moment(values.enterTime[1]).format('YYYY-MM-DD HH:mm:ss')
                delete values.enterTime
            }
            post(allUrl.truckLogistics.carriageEntryAdminExport, values, {responseType: "blob"}).then(res=>{
                console.log('res.fileName',res.fileName)
                fileDown(res,res.fileName)
            })
        } else {
            let values = childRef2.current.query
            if (values.createTime && values.createTime.length) {
                const [start, end] = values.createTime;
                const diffDays = end.diff(start, 'days');
                if (diffDays > 31) {
                    message.error('选择的组板创建时间范围不能超过31天');
                    return;
                }
                values.createTimeStart = moment(values.createTime[0]).format('YYYY-MM-DD HH:mm:ss')
                values.createTimeEnd = moment(values.createTime[1]).format('YYYY-MM-DD HH:mm:ss')
                delete values.createTime
            }
            if (values.completeTime && values.completeTime.length) {
                const [start, end] = values.completeTime;
                const diffDays = end.diff(start, 'days');
                if (diffDays > 31) {
                    message.error('选择的组板完成时间范围不能超过31天');
                    return;
                }
                values.completeTimeStart = moment(values.completeTime[0]).format('YYYY-MM-DD HH:mm:ss')
                values.completeTimeEnd = moment(values.completeTime[1]).format('YYYY-MM-DD HH:mm:ss')
                delete values.completeTime
            }
            if (values.dmsCancelTime && values.dmsCancelTime.length) {
                const [start, end] = values.dmsCancelTime;
                const diffDays = end.diff(start, 'days');
                if (diffDays > 31) {
                    message.error('选择的组板取消时间范围不能超过31天');
                    return;
                }
                values.dmsCancelTimeStart = moment(values.dmsCancelTime[0]).format('YYYY-MM-DD HH:mm:ss')
                values.dmsCancelTimeEnd = moment(values.dmsCancelTime[1]).format('YYYY-MM-DD HH:mm:ss')
                delete values.dmsCancelTime
            }
            post(allUrl.truckLogistics.carriageBoardAdminExport, values, {responseType: "blob"}).then(res=>{
                console.log('res.fileName',res.fileName)
                fileDown(res,res.fileName)
            })
        }
        
    })
    const getTableData = () => {
        setLoading(true)
        // let query = { ...defaultQuery }
        // let params = { pageNum: current, pageSize, ...query }
        if(tabsKey === '1'){
            tableRef1.current.getTableData()
        }else if(tabsKey === '2'){
            tableRef2.current.getTableData()
        }
    }
   
    const renderOperation = (text, record,key) => {
        const menu = (
            <Menu onClick={(e)=>onMenuClick(e,record, key)}>
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_ENTER_EDIT_TIME') && key == 1 && record.status == 1 ?
                    <Menu.Item key='1'>
                            <a style={{ cursor: 'pointer' } } >修改预约时间</a>
                    </Menu.Item>:null
                    
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_ENTER_ORDER_CONFIG_LOG') && key == 1 ?
                    <Menu.Item key='2'>
                            <a style={{ cursor: 'pointer' } }>修改日志</a>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_GROUP_FINISH') && key == 2 && record.status == 1 ?
                    <Menu.Item key='3'>
                            <a style={{ cursor: 'pointer' } }>组板完成确认</a>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_GROUP_FINISH_LOG')  && key == 2 ?
                    <Menu.Item key='4'>
                            <a style={{ cursor: 'pointer' } }>组板完成确认日志</a>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_HIDDEN_PHONE') ? <Menu.Item key='5'>
                        <a style={{ cursor: 'pointer' } }>查看手机号</a>
                    </Menu.Item> :null
                }
                
                
                
            </Menu>
          );
        return <Dropdown overlay={menu} placement="bottomCenter" trigger={['click']} >
                    <a>更多操作</a>
                </Dropdown>
    }
    const onMenuClick = ({key},record, tabKey) =>{
        setRecordObj(record)
        switch (key){
            case '1' :
            // 修改预约时间
                
                setUpdateTimeVisible(true)
            break;
            case '2' :
                setTitle('修改日志')
                getLog(1, record.logKey)
                setOperationVisible3(true)
            break;
            case '3' :
                setTitle('组板完成确认')
                post(allUrl.truckLogistics.carriageBoardList,{boardNo:record.boardNo}).then(res => {
                    if (res.success &&  res.resp[0]) {
                    let list = res.resp[0].list
                    let vinList = []
                    if(list.length) {
                        list.map((i) => {
                            vinList.push(i.vin)
                        })
                    }
                    setOneEntryNoVinList(vinList)
                    setOperationVisible1(true)
                    } 
                })
            break;
            case '4' :
                setTitle('组板完成确认日志')
                getLog(3, record.logKey)
                setOperationVisible3(true)
            break;
            case '5' :
                openPhoneModal(record, tabKey)
            break;
    }
}
    const getLog = (operationType, logKey) => {
        post(allUrl.truckLogistics.getLog,{operationType, key: logKey}).then(res => {
            if (res.success) {
                let Dt = []
                if(res.resp[0] && res.resp[0].list.length) {
                    Dt = res.resp[0].list
                } else {
                    Dt = []
                }
            setDataSource3(Dt)
            } 
        })
    }
    const onChange3 = (e) => {
        setEnterConfigRadio(e.target.value)
    }
    const showEnterConfigLog = () => {
        setTitle('进场修改日志')
        // logKey为固定值
        getLog(2, 'd76c0c770e0e5561bb5b36815ed1e053')
        setOperationVisible3(true)
    }
    const handleOk3 = () => {
        setOperationVisible3(false)
    }

    const tabsCallback = (key) => {
        setTabsKey(key)
        setCurrent(1)
        setPageSize(10)
        sessionStorage.setItem('CarrierListTabsKey',key)
        if(key === '1'){
              if(childRef1.current){
                console.log('查询1', childRef1.current)
                onSearch(childRef1.current.query, key)
              }else{
                setStorage({
                    Query: {...defaultQuery1},
                    Type: window.location.hash,
                  });
              }
              
        }else if(key === '2'){
            if(childRef2.current){
                onSearch(childRef2.current.query, key)
            }else{
                setStorage({
                    Query: {...defaultQuery2},
                    Type: window.location.hash,
                });
            }
        }
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }
    const handleForceBoardById = () => {
        confirm({
            title: '确认提示',
            icon: <ExclamationCircleFilled />,
            content: '是否要确认VIN所属组板单组板完成？确认后将不可修改',
            onOk() {
              console.log('OK');
              post(allUrl.truckLogistics.forceBoardById, {boardId: recordObj.boardId}).then(res => {
                if (res.success) {
                    message.success('操作成功！')
                }
                setOperationVisible1(false)
                getTableData()
            })
            },
          });
    }
    
    //   进场设置
    const handleSetEnterConfig = () => {
        const params = { status: enterConfigRadio}
        get(allUrl.truckLogistics.setLimitEntry,{...params}).then(res => {
            if (res.success) {
                setEnterConfigVisible(false)
                message.success(res.msg || '操作成功')
            } else {
                message.error(res.msg || '操作失败')
            }
        })
    }

    const getEnterConfigRadio = () => {
        get(allUrl.truckLogistics.getLimitEntry).then(res => {
            if (res.success) {
                console.log('进场设置回显', res.resp[0])
                setEnterConfigRadio(res.resp[0].status)
            }
        })
    }
    const updateEnterTime = () => {
        console.log(enterBeginTime.isBefore(enterEndTime))
        if(enterBeginTime.isBefore(enterEndTime)) {
            post(allUrl.truckLogistics.updateEntryTimeById, {
                id: recordObj.id,
                beginTime: moment(enterBeginTime).format('YYYY-MM-DD HH:mm:ss'),
                endTime: moment(enterEndTime).format('YYYY-MM-DD HH:mm:ss')
            }).then(res => {
                if (res.success) {
                    setUpdateTimeVisible(false)
                    message.success('修改成功！')
                    getTableData()
                    console.log('修改时间成功')
                }
            })
        } else {
            message.error('开始时间必须大于结束时间')
        }
        
    }
    const openPhoneModal = (record, tabKey) => {
        let url = ''
        if (tabKey === '1') {
            url = allUrl.truckLogistics.carriageEntryAdminPeShow
        } else {
            url = allUrl.truckLogistics.carriageBoardAdminPeShow
        }
        post(url, {pe:record.pe}).then(res => {
            if (res.success) {
                // settransferClockList(res.resp[0])
                setPePhone(res.resp[0])
                console.log(res.resp[0])
                setPhoneVisible(true)
            } else {
                message.error(res.msg || '获取手机号码失败')
            }
        }) 
    }
    // 格式化时间区间
    const formatDateTimeRange = (startDate, endDate) => {
        // const start = new Date(startDate);
        // const end = new Date(endDate);
      
        // const formattedStartDate = start.toISOString().replace('T', ' ');
        // const formattedEndDate = end.toISOString().replace('T', ' ');
      
        return `${startDate}至${endDate}`;
      }
    //   useEffect(()=>{
    //     if(driverformRef.current){
    //         driverform.setFieldsValue({
    //             driverName:recordObj.driverName,
    //             driverPhone:recordObj.driverPhone,
    //             logisticsProviderNo:recordObj.logisticsProviderNo
    //         })
    //     }
    //     if(turnformRef.current){
    //         if(!transferDefult){
    //             settransferInput(false)
    //             if(! recordObj.logisticsProviderNo){
    //                 settransferInput(true)
    //             }
    //             turnform.setFieldsValue({
    //                 logisticsProviderNo:recordObj.logisticsProviderNo,
    //             })
    //             setlogisticsProviderNo(recordObj.logisticsProviderNo)
    //             setlogisticsProviderName(recordObj.logisticsProviderNoDesc)
    //         }
    //         if(transferDefult){
    //             turnform.setFieldsValue({
    //                 logisticsProviderNo:logisticsProviderNo,
    //             })
    //         }
           
    //     }
    // },[recordObj])
    useEffect(()=>{
        // 服务物流商
        // dispatch(getDict({codes:'dray_logistics_provider_no'})).then(({payload})=>{
        //     setDictList(payload)
        // })
        console.log('childRef1', childRef1)
        getLogisticsProviderList()
        getDefaultLogisticsProvider()
        getEnterConfigRadio()
        childRef1.current && childRef1.current.setFieldValue('enterTime', [moment().startOf('day'), moment().endOf('day')])
        childRef2.current && childRef2.current.setFieldValue('completeTime', [])
        if (childRef1.current) {
            console.log('childRef1', defaultQuery1)
        }
        childRef2.current && childRef2.current.setFieldValue('dmsCancelTime', [])
        // childRef2.current && childRef2.current.setFieldValue('createTime', [moment().startOf('month'), moment().endOf('month')])
        childRef2.current && childRef2.current.setFieldValue('createTime', [moment().subtract(31, 'days').startOf('day'), moment().endOf('day')])
        if (childRef2.current) {
            console.log('childRef2', defaultQuery2)
        }
    },[])
    useEffect(()=>{
        initPage()
    },[])
    const [columns1, setColums1] = useState([
        { title: '进场单号', dataIndex: 'entryNo', width: 120 ,fixed: 'left' },
        { title: '司机姓名', dataIndex: 'driverName', width: 100 },
        { title: '司机手机号', dataIndex: 'driverPhone', width: 100 },
        { title: '承运板车', dataIndex: 'licensePlate', width: 120 },
        { title: '物流服务商', dataIndex: 'logisticsProviderName', width: 150, render:text=>text ? text : '-' },
        { title: '预约仓库', dataIndex: 'warehouseName', width: 300 },
        { title: '预约进场时间', dataIndex: 'beginTime', width: 200,  render:(text, record) => {
            return record.beginTime && record.endTime && formatDateTimeRange(record.beginTime, record.endTime)
        }}, 
        { title: '核销时间', dataIndex: 'entryTime', width: 200 },
        { title: '取消进场时间', dataIndex: 'cancelTime', width: 200 },
        { title: '核销人', dataIndex: 'verificationName', width: 100,render:text=>text ? text : '-'  },
        { title: '是否修改预约时间', dataIndex: 'isChangeTime', width: 100, render:text=>text == '0' ?  '否' : '是'  }, 
        { title: '进场状态', dataIndex: 'status', width: 120,  render: (text, record) => renderStatusName(ENTER_STATUS, text) }, 
        { title: '操作', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record,1) }
    ])
    const [columns2, setColums2] = useState([
        { title: '车辆VIN', dataIndex: 'vin', width: 100,fixed: 'left' },
        { title: '组板单号', dataIndex: 'boardNo', width: 100 },
        { title: '司机姓名', dataIndex: 'driverName', width: 100 },
        { title: '司机手机号', dataIndex: 'driverPhone', width: 120 },
        { title: '承运板车', dataIndex: 'licensePlate', width: 120 },
        { title: '物流服务商', dataIndex: 'logisticsProviderName', width: 150 , render:text=>text ? text : '-'},
        { title: '仓库', dataIndex: 'warehouseName', width: 200 , render:text=>text ? text : '-' },
        { title: '道次', dataIndex: 'gate', width: 200, render:text=>text ? text : '-'  }, 
        { title: '组板状态', dataIndex: 'status', width: 200,  render:(text,record)=> renderStatusName(BOARD_STATUS, text) },
        { title: '是否管理员确认组板', dataIndex: 'isForceComplete', width: 110, render:text=>text == 1 ? '是' : '否' },
        { title: '组板完成时间', dataIndex: 'completeTime', width: 140, render:text=>text ? text : '-' },
        { title: '组板取消时间', dataIndex: 'dmsCancelTime', width: 140 },
        { title: '组板创建时间', dataIndex: 'createTime', width: 140 },
        { title: '组板单类型', dataIndex: 'type', width: 120 ,render:text=> renderStatusName(BOARD_TYPE, text)  },
        { title: '操作', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record,2) }
    ])
    
    let searchList1 = [ 
        { label: '进场单号', name: 'entryNo', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '司机姓名', name: 'driverName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '司机手机号', name: 'driverPhone', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '预约仓库', name: 'warehouseCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: warehouseList || [] },
        { label: '核销人', name: 'verificationName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '预约进场时间', name: 'enterTime', type: 'RangePicker', showTime: true,  colSpan: 6 },
        { label: '进场状态', name: 'status', type: 'Select', mode: 'multiple',initialValue:[1,3], placeholder: '请选择',  colSpan: 6, data: ENTER_STATUS },
        
    ]
    let searchList2 = [
        { label: '车辆VIN', name: 'vin', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '组板单号', name: 'boardNo', type: 'Input', placeholder: '请输入', colSpan: 6 },
        
        { label: '司机姓名', name: 'driverName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '司机手机号', name: 'driverPhone', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '承运板车', name: 'licensePlate', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '仓库', name: 'warehouseCode', type: 'Select', placeholder: '请输入', colSpan: 6, data: warehouseList },
        {
            label: '物流服务商', name: 'logisticsProviderNo', type: 'Select', placeholder: '请选择', colSpan: 6, data:logisticsProviderList
        },
        { label: '组板状态', name: 'status',type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '待组板', value: 1 },
            { name: '已取消', value: 2 },
            { name: '组板完成', value: 3 },
        ] },
        { label: '是否管理员组板确认', name: 'isForceComplete', width: 100, type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '是', value: 1 },
            { name: '否', value: 0 },
        ]},
        { label: '组板创建时间', name: 'createTime', type: 'RangePicker',  showTime: true,placeholder: ['开始时间', '截止时间'], colSpan: 6 },
        { label: '组板完成时间', name: 'completeTime', type: 'RangePicker',  showTime: true,placeholder: ['开始时间', '截止时间'], colSpan: 6 },
        { label: '组板取消时间', name: 'dmsCancelTime', type: 'RangePicker', showTime: true, placeholder: ['开始时间', '截止时间'], colSpan: 6 },
        { label: '组板单类型', name: 'type', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '物流组板', value: 1 },
            { name: '中转库出库', value: 2 },
        ] },
        
    ]
    const InforData3 = {
        rowKey: record => record.id,
        bordered: true,
        dataSource:dataSource3,
        scroll: { x: 'max-content' },
        columns: [
            { title: '时间', dataIndex: 'createTime', width: 180},
            { title: '操作者', dataIndex: 'userName', width: 100},
            { title: '操作者ID', dataIndex: 'userId', width: 100 },
            { title: '操作者所属组织', dataIndex: 'orgNames', width: 180 },
            { title: '操作类型', dataIndex: 'operationType', width: 140, render: (text, record) => renderStatusName(LOG_TYPE, text)  },
            { title: '修改内容', dataIndex: 'content', width: 180 },
        ],
        pagination: false,
        rowSelection: null,
    };
   
    let newColumns1 = _.cloneDeep({ columns1 }).columns1
    for (let i = 0; i < newColumns1.length; i++) {
        if (JSON.stringify(newColumns1[i]['checked']) !== undefined && !newColumns1[i].checked) {
            newColumns1.splice(i, 1)
            i--
        }
    }
    let newColumns2 = _.cloneDeep({ columns2 }).columns2
    for (let i = 0; i < newColumns2.length; i++) {
        if (JSON.stringify(newColumns2[i]['checked']) !== undefined && !newColumns2[i].checked) {
            newColumns2.splice(i, 1)
            i--
        }
    }

    return (
        <div className='CarrierList'>
            <Tabs onChange={tabsCallback} type="card" activeKey={tabsKey} defaultActiveKey={tabsKey} >
                {
                      roleJudgment(userInfo,'TRUCK_LOGISTICS_ENTER_ORDER') ?
                      <TabPane tab="进场单" key="1">
                    <PublicTableQuery initPage={initPage} tabsKey={tabsKey} onSearch={onSearch} searchList={searchList1}  isCatch={true} isFormDown={true} ref={childRef1}/>
                    <div className='tableData'>
                        <Row className='tableTitle'>
                            <Col span={24} style={{textAlign:'right'}}>
                                {
                                    roleJudgment(userInfo,'TRUCK_LOGISTICS_ENTER_EXPORT') ? <Button type="primary" style={{ marginRight: '20px' }} onClick={() => exportData()}>
                                    导出
                                    </Button> : null
                                }
                                {
                                    roleJudgment(userInfo,'TRUCK_LOGISTICS_ENTER_CONFIG') ?
                                        <Button type='primary' onClick={()=>setEnterConfigVisible(true)} >进场设置</Button>
                                        : null
                                }                              
                                <ExtendedColumn setColums={setColums1} columns={columns1} />
                            </Col>
                        </Row>
                        {
                            tabsKey === '1' &&
                            <PublicTable
                                url={allUrl.truckLogistics.carriageEntryList}
                                isCatch={true}
                                columns={newColumns1}
                                type={2}
                                rowSelection={false}
                                ref={tableRef1}
                                setCurrent={setCurrent}
                                setPageSize={setPageSize}
                                defaultQuery={{...defaultQuery1}}
                            />
                        }
                    </div>
                      </TabPane> : null
                }
                  {
                      roleJudgment(userInfo,'TRUCK_LOGISTICS_GROUP_ORDER') ?
                      <TabPane tab="组板单" key="2">
                      <PublicTableQuery initPage={initPage} tabsKey={tabsKey} onSearch={onSearch} searchList={searchList2}  isCatch={true} isFormDown={false} ref={childRef2}/>
                      <div className='tableData'>
                      <Row className='tableTitle'>
                            <Col span={24} style={{textAlign:'right'}}>
                                {
                                    roleJudgment(userInfo,'TRUCK_LOGISTICS_GROUP_EXPORT') ? <Button type="primary" style={{ marginRight: '20px' }} onClick={() => exportData()}>
                                    导出
                                    </Button> : null
                                }
                            </Col>
                        </Row>
                          {
                              tabsKey === '2' &&
                              <PublicTable
                                  url={allUrl.truckLogistics.carriageBoardList}
                                  isCatch={true}
                                  columns={newColumns2}
                                  type={2}
                                  rowSelection={false}
                                  ref={tableRef2}
                                  setCurrent={setCurrent}
                                  setPageSize={setPageSize}
                                  defaultQuery={{...defaultQuery2}}
                              />
                          }
                      </div>
                      </TabPane> : null
                  }  
            </Tabs>
            {
              // 组板设置
              operationVisible1 && 
            <Modal  width={500} okText='确认组板完成' open={operationVisible1} title='组板完成确认' recordObj={recordObj} onOk={handleForceBoardById}  onCancel={()=>setOperationVisible1(false)}>
               <p>组板单号：{recordObj.boardNo}</p>
               <p>仓库：{recordObj.warehouseName} <span style={{width: 100}}></span>道次：{recordObj.gate}</p>
               {
                    oneEntryNoVinList.length && oneEntryNoVinList.map((item) => {
                       return( <p>{item}</p> )
                    })
               }
            </Modal>
            }
            {
                // 修改进场设置日志
            operationVisible3 && 
            <Modal  width={1200}  footer={null} open={operationVisible3} title={title} recordObj={recordObj} onOk={handleOk3}  onCancel={()=>setOperationVisible3(false)}>
                <Table {...InforData3} />
            </Modal>
            }
            {
              // 进场设置
            enterConfigVisible && 
            <Modal  width={500}  open={enterConfigVisible} title='进场设置' recordObj={recordObj} onOk={handleSetEnterConfig}  onCancel={()=>setEnterConfigVisible(false)}>
                {
                     roleJudgment(userInfo,'TRUCK_LOGISTICS_ENTER_CONFIG_LOG') ? 
                     <Button type='link' style={{marginLeft: -5}} onClick={showEnterConfigLog}>进场设置修改日志</Button>
                     : null
                }
                <Form>
                    <Form.Item label="进场是否限制时间？（默认是）" required>
                    <Radio.Group onChange={onChange3} value={enterConfigRadio}>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
                </Radio.Group>
                    </Form.Item>
                </Form>
            </Modal>
            }
            {
              // 修改预约时间
            updateTimeVisible && 
            <Modal  width={500}  open={updateTimeVisible} title='修改操作时间' recordObj={recordObj} onOk={updateEnterTime}  onCancel={()=>setUpdateTimeVisible(false)}>
                <Form>
                    <Form.Item label="预约进场开始时间" required>
                    <DatePicker showTime value={enterBeginTime} onChange={(v, str) => {setEnterBeginTime(v)}}/>
                    </Form.Item>
                    <Form.Item label="预约进场结束时间" required>
                    <DatePicker showTime value={enterEndTime} onChange={(v) => setEnterEndTime(v)}/>
                    </Form.Item>
                </Form>
            </Modal>
            }
            {
            phoneVisible && 
            <Modal  width={600}  open={phoneVisible} title='查看手机号' recordObj={recordObj} onOk={() => setPhoneVisible(false)}  onCancel={()=>setPhoneVisible(false)}>
                {pePhone}
            </Modal>
            }
        </div>
    )
}
export default ClueList
