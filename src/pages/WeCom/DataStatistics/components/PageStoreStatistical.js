import React ,{useEffect, useState} from 'react'
import { Modal ,Table ,message} from 'antd'
import {post} from '@/utils/request'
import allUrl from '@/utils/url'
import PublicTooltip from '@/components/Public/PublicTooltip'
import DeliveryOrderList from './DeliveryOrderList'

export default (props) =>{
    const {visible,title,onCancel} = props
    const [recordObj,setRecordObj] = useState({})
    const [visible2,setVisible2] = useState(false)
    const [current,setCurrent] = useState(1)
    const [pageSize,setPageSize] = useState(10)
    const [dataSource,setDataSource] = useState([])
    const [total,setTotal] = useState(0)
    const [loading,setLoading] = useState(false)

    const serialNumber =(pageIndex, pageSize, index)=>{
        return (current-1) * pageSize + index;
    }

    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
    }

    const getTableData = () =>{
        setLoading(true)
        post(allUrl.WeCom.pageStoreStatistical,{current,pageSize}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setDataSource(Dt.list)
                setTotal(Dt.total)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }
   
    useEffect(()=>{
        getTableData()
    },[current,pageSize])

    const columns = [
        // { title: '序号', dataIndex: 'index',width:100, render: (text, record, index) => index + 1 },
        { title: '排名', dataIndex: 'index',width:100, render: (text, record, index) => serialNumber(current,pageSize,index+1) },
        { title: '门店编码', dataIndex: 'dealerCode' ,width:200,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '用户中心', dataIndex: 'dealerName',render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        // { title: '交付专员', dataIndex: 'delivererName',render:(text,record)=>record.delivererName },
        // { title: '车主姓名', dataIndex: 'ownerName' },
        // { title: '交付状态', dataIndex: 'deliverStatusName' },
        // { title: '交付完成日期', dataIndex: 'deliveredTime' },
        {title:'交付单信息',dataIndex:'orderInfo',width:120,render:(text,record)=><div><a onClick={()=>{
            setRecordObj(record)
            setVisible2(true)
        }}>查看</a></div>},
        { title: '合计', dataIndex: 'totalNum',width:120 },
    ]

    const InforData = {
        columns,
        dataSource,
        bordered:true,
        rowKey:'id',
        loading,
        size: 'small',
        sticky:true,
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
    }
    return <Modal title={title} visible={visible} onCancel={onCancel} maskClosable={false} footer={null} width={1000}>
                {/* <PublicTable type={4} sticky={true} rowSelection={false} columns={columns} url={allUrl.WeCom.pageStoreStatistical} /> */}
                <Table {...InforData}key={1}/>
                {
                visible2 &&
                <DeliveryOrderList type={1} title={`${recordObj?.dealerName}：交付单信息`} recordObj={recordObj} data={recordObj.delivererInfo || []} visible={visible2}  onCancel={()=>setVisible2(false)}/>
            }
    </Modal>
}