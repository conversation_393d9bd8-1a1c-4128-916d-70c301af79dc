import React, { useEffect, useState, useRef } from "react";
import { Row, Col, Space, Spin, Modal, Radio, message, Tag } from "antd";
import { render } from "less";

const ExportModal = (props) => {
  const [value, setValue] = useState("yesterday");
  const { title, visible, exportExcel, onCancel } = props;

  const onChangeExportType = (e) => {
    console.log("radio checked", e.target.value);
    setValue(e.target.value);
  };
  return (
    <Modal
      title={title || "导出为Excel"}
      visible={visible}
      onOk={() => exportExcel(value)}
      onCancel={onCancel}
    >
      <Radio.Group onChange={onChangeExportType} value={value}>
        <Space direction="vertical">
          <Radio value={"yesterday"}>昨日数据</Radio>
          <Radio value={7}>近7天数据</Radio>
          <Radio value={31}>近31天数据</Radio>
          <Radio value={"condition"}>满足当前筛选条件的数据</Radio>
        </Space>
      </Radio.Group>
    </Modal>
  );
};

export default ExportModal;
