/**
 * @param {Object} style 样式
 * @param {Array} extension 上传文件扩展名限制 （['xls','xlsx']）
 * @param {String} action 上传地址 可选
 * @param {Function} customRequest  可选
 * @param {Object} headers 请求头
 * @param {boolean} showUploadList 是否展示上传文件列表
 * @param {Number} size 文件大小限制（单位：MB）
 * @param {Array} fileList 文件列表
 */
import React, { useEffect, useState } from 'react'
import { Upload, message } from 'antd'
import Cookies from 'js-cookie'
import { CloseOutlined, PlusOutlined, PaperClipOutlined, DeleteOutlined, DisconnectOutlined } from '@ant-design/icons';


const UploadFile = (props) => {
    const { children, style = {}, extension = [], action, headers = {}, iconRender, showUploadList = false, size, UploadChange, type, data = {}, fileList = [], UploadRemove, name,beforeUpload,customRequest } = props
    const [defaultFileList, setDefaultFileList] = useState([])
    const newHeaders = {
        ...headers,
        appid: 1,
        authorization: Cookies.get('scrm_token'),
    }

    const _beforeUpload = (file) => {
        const { name } = file
        const index = name.lastIndexOf(".");
        const suffix = name.substring(index + 1);
        if (extension.length) {
            if (extension.indexOf(suffix.toLowerCase()) <= -1) {
                message.error(`仅支持${extension}的文件类型！`)
                return false
            }
        }
        if (size) {
            const isLt2M = file.size / 1024 / 1024 < size;
            if (!isLt2M) {
                message.error(`文件大小不能超过${size}MB!`);
                return false
            }

        }
        beforeUpload&&beforeUpload(file)
        
        return true
    }
    const handleChange = ({ file, fileList }) => {
        setDefaultFileList(fileList)
        if (UploadChange) { UploadChange({ file, fileList }, type) }
    }
    const handleRemove = (file) => {
        // console.log(file)
        if (UploadRemove) { UploadRemove(file) }
    }
    useEffect(() => {
        // console.log(fileList)
    }, [fileList])
    const uploadProps = {
        // action,
        // customRequest,
        data,
        headers: newHeaders,
        showUploadList,
        fileList: showUploadList ? fileList : defaultFileList,
        iconRender: iconRender ? iconRender : <PaperClipOutlined style={{ color: 'rgba(0, 0, 0, 0.45)' }} />,
        beforeUpload: _beforeUpload,
        onChange: handleChange,
        onRemove: handleRemove
    }
    if (action) { uploadProps.action = action }
    if (customRequest) { uploadProps.customRequest = customRequest }
    if (name) { uploadProps.name = name }
    /**
     *  action={action}
            data={data}
            headers={newHeaders}
            showUploadList={showUploadList}
            fileList={showUploadList ? fileList : defaultFileList}
            iconRender={iconRender ? iconRender : <PaperClipOutlined style={{ color: 'rgba(0, 0, 0, 0.45)' }} />}
            beforeUpload={beforeUpload}
            name={name || 'file'}
            onChange={handleChange}
            onRemove={handleRemove}
     */
    return <span style={style}>
        <Upload
            {...uploadProps}
        >
            {children}
        </Upload>
    </span>
}
export default UploadFile