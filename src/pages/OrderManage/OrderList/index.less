.OrderList{
    .ant-tabs{
        .ant-tabs-nav{
            margin-left: 24px;
        }
    }
    .TurnoverInfo{
        margin-left: 32px;
        .Overall{
            width: 140px;
            height: 76px;
            opacity: 1;
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
            font-weight: 400;
            // text-align: left;
            color: rgba(0,0,0,0.65);
            text-align: center;
            padding-top: 12px;
            .num{
                font-size: 21px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                color: rgba(0,0,0,0.85);
            }
        }
        .unitData{
            width: 100px;
            height: 76px;
            opacity: 1;
            background: #fdfdfd;
            border: 1px solid #e8e8e8;
            text-align: center;
            padding-top: 14px;
            margin-left: 20px;
            .num{
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                color: #52c41a;
            }
        }
    }
    .ant-pagination{
        padding-right: 20px;
    }
    .tableData{
        background-color: white;
        // padding: 24px 24px 72px 24px;

        border-top: solid 24px #f0f2f5;
        border-right: solid 24px #f0f2f5;
        border-bottom: solid 24px #f0f2f5;
        border-left: solid 24px #f0f2f5;
        .ant-table-wrapper{
            // background-color: white;
            .ant-table{
                padding: 24px;
                .ant-table-container{
                    border: 0;
                    .ant-table-content{
                        // overflow: auto hidden;
                        .ant-table-thead{
                            tr>th,tr>td{
                                // border: 0;
                            }
                        }
                    }
                    .ant-table-pagination{
                        margin: 16px 24px;
                    }
                }
            }
        }
        .tableTitle{
            padding: 24px 24px 0px 32px;
            justify-content: space-between;
            .text{
                font-size: 20px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.85);
                line-height: 28px;
            }
            .bts{
                .ant-btn{
                    margin:0 7px;
                }
            }
        }
    }
    .PublicList_FormQuery{
        padding-top: 16px;
        padding-left: 24px;
        .ant-col-7{
            .ant-form-item{
                .ant-form-item-control-input{
                    width: 90%;
                    .ant-form-item-control-input-content{
                        .ant-picker{
                            width: 100%;
                        }
                    }
                }
            }
        }
        .FormQuerySubmit{
            display:flex;
            justify-content: flex-end;
            .operationButtons{
                span{
                    color: #1890ff;
                    cursor: pointer;
                    .anticon{
                        margin-left: 6px;
                    }
                }
            }
        }
    }
}