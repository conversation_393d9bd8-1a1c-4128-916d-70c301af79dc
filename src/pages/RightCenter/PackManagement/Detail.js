import React, { useEffect, useState } from 'react';

import { useSelector } from 'react-redux';
import { Table, message, Button, Descriptions, Row, Col, Spin, Tooltip, Tag } from 'antd';
import '../index.less';
import history from '@/utils/history';
import { get, post } from '../../../utils/request';
import allUrl from '../../../utils/url';
import moment from 'moment';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import { DecryptByAES } from '@/components/Public/Decrypt';
import PublicTooltip from '@/components/Public/PublicTooltip';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import styles from '../antd.module.less';
import detailCss from './detail.module.less';
import DetailPubliTable from './DetailPubliTable';
import GroupPublicTable from './GroupPublicTable';
import Logs from '../components/Logs';

const titleCss = {
  backgroundColor: '#D6EBFF',
  height: 50,
  paddingLeft: 24,
  paddingTop: 13,
  fontSize: 16,
  color: 'rgba(0,0,0,0.85)',
  fontWeight: 500,
};

const iconCss = {
  width: '16px',
  height: '16px',
  marginRight: '8px',
  cursor: 'pointer',
  marginTop: '-2px',
};

const subTitleCss = {
  color: '#000000',
  fontSize: 15,
  fontWeight: 500,
  paddingLeft: 24,
};

const display_MAP = {
  0: '展示',
  1: '不展示',
};

const status_MAP = {
  1: '草稿',
  2: '已生效',
  3: '未生效',
  4: '已失效',
  5: '已下架',
};

const equitySource_MAP = {
  1: '直销',
  2: '三方采购',
};

const subject_MAP = {
  1: '品牌策划部',
  2: '战区支持部',
  3: '用户服务部',
  4: '华为',
};

const equitySalesSubject_MAP = {
  '0000090375': '0000090375 重庆问界汽车销售有限公司（1900）',
  '0000009990': '0000009990 重庆赛力斯新电动汽车销售有限公司（1020）',
  '0000093235': '0000093235 重庆问界智选精品汽车备件有限公司（2300）',
};

const promotionExist_MAP = {
  1: '该权益包应用于活动中',
  0: '该权益包不应用于活动中',
};
const PackManagementDetail = props => {
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [id, setId] = useState(null);
  const [locationParmas, setLocationParmas] = useState({});
  const [Type, setType] = useState('');
  const [isItemLabel, setIsItemLabel] = useState(false);
  const [loading, setLoading] = useState(false);

  const [detail, setDetail] = useState({});
  const [salesIncome, setSalesIncome] = useState(0);

  const getData = params => {
    setLoading(true);
    get(allUrl.EquityPackageManage.getEquityPack, { id: params.id }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        /**
         * V22版本组装驾行安心包对外展示数据
         * 1.outsideDetailDisplayList里面是未组合的
         * 2.groupDetails=》detailDisplayList里面是组合的
         * 组装方法：
         * =》groupDetails=》detailNoList筛选出“最小的权益编码”，
         * =》groupDetails=》detailDisplayList里面找出“最小的权益编码”对应的明细数据detail与groupDetails=》groupDetail数据合并即为组合要展示的数据
         * =》特殊处理权益明细编码、业务编码、权益明细版本
         */
        if (Dt.groupDetails && Dt.groupDetails.length > 0) {
          Dt.groupDetails.map(item => {
            const minDetailNo = item.detailNoList.sort((a, b) => a.localeCompare(b))[0];
            const tmplDetail = item.detailDisplayList.find(item => item.detailNo === minDetailNo);
            //item.groupDetail带outside开头的字段赋值给不带outside开头的字段
            Object.keys(item.groupDetail).forEach(key => {
              if (key.startsWith('outside')) {
                const TMPL_KEY = key.replace('outside', '');
                const LOWER_KEY = TMPL_KEY.charAt(0).toLowerCase() + TMPL_KEY.slice(1);
                item.groupDetail[LOWER_KEY] = item.groupDetail[key];
              }
            });
            item.detail = {
              ...tmplDetail,
              ...item.groupDetail,
              detailNoList: item.detailDisplayList.map(item => item.detailNo),
              businessCodeDescList: item.detailDisplayList.map(item => item.businessCodeDesc),
              detailVersionList: item.detailDisplayList.map(item => item.detailVersion),
            };
          });
          Dt.outsideDetailDisplayList = [
            ...(Dt.outsideDetailDisplayList || []),
            ...Dt.groupDetails.map(item => item.detail),
          ];
        }
        setDetail(Dt);
      } else {
        // message.error(res.msg)
      }
      setLoading(false);
    });
  };
  useEffect(() => {
    const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {};
    setLocationParmas(locationParmas);
    setId(locationParmas.id);
    setType(locationParmas.Type);
    console.log(locationParmas);
    if (locationParmas.id && userInfo) {
      getData(locationParmas);
    } else {
    }
  }, [props.match.params.data, userInfo]);

  const onCancel = () => {
    history.push('/RightCenter/PackManagement');
  };
  const getYearUnitName = key => {
    return (
      {
        year: '年',
        month: '月',
        day: '日',
      }[key] || ''
    );
  };
  const onDetail = record => {
    let data = {
      id: record.detailId,
      Type: 'LookAt',
      title: '权益明细详情',
    };
    UniversalOpenWindow({
      JumpUrl: '/RightCenter/DetailManagementDetail',
      data,
      history: props.history,
    });
  };

  const handleData = (data, key) => {
    let object = {};
    data.forEach(item => {
      const _key = item[key];
      if (!object[_key]) {
        object[_key] = {
          [key]: item[key],
          children: [],
        };
      }
      object[_key].children.push(item);
    });
    return Object.values(object);
  };

  //车型配置
  const renderCarConfig = data => {
    const data_new = handleData(data, 'baseConfig');
    data_new.map(i => {
      i.configList = handleData(i.children, 'carOfYear');
      i.configList.map(j => {
        j.carOfYearConfig = handleData(j.children, 'powerModel');
      });
    });
    return data_new.map((i, index) => {
      return (
        <div className={detailCss.packCarConfigCss} key={i.baseConfig}>
          <div className={`carType ${'c' + index}`}>{i.baseConfig}</div>
          <div>
            {i.configList.map(j => {
              return (
                <div className="a2" key={j.carOfYear}>
                  <div className={`year ${'c' + index}`}>{j.carOfYear}</div>
                  <div>
                    {j.carOfYearConfig.map(k => {
                      return (
                        <div className="a4" key={k.powerModel}>
                          <div className={`power ${'c' + index}`}>{k.powerModel}</div>
                          <div className="a6">
                            {k.children.map(l => {
                              return (
                                <span key={l.config} className="config">
                                  {l.carOfYear + '/' + l.powerModel + '/' + l.config}
                                </span>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    });
  };

  return (
    <div className="PublicList">
      <Spin spinning={loading}>
        <div className="detail-overflow">
          <Row className="tableTitle" style={{ paddingBottom: 15, paddingTop: 0 }}>
            <Col className="text">权益包详情</Col>
            <Col className="bts">
              {/* 判断编辑按钮是否显示，草稿、下架状态显示 */}
              {(detail.status === 'draft' || detail.status === 'down') && (
                <Button
                  type="primary"
                  onClick={() => {
                    let data = {
                      id: detail.id,
                      Type: 'edit',
                      title: '编辑权益包',
                    };
                    UniversalOpenWindow({
                      JumpUrl: '/RightCenter/PackManagementCreate',
                      data,
                      history: props.history,
                    });
                  }}
                >
                  编辑
                </Button>
              )}
            </Col>
            {/* <Col className='bts'>
                            <Button onClick={onCancel}>返回</Button>
                        </Col> */}
          </Row>
          <div className="tableData">
            <div className="PublicList" style={{ backgroundColor: 'white' }}>
              <div className="marg" style={{ marginTop: 0 }}>
                <div style={titleCss}>权益包内容</div>
                <div className="tableData" style={{ backgroundColor: 'white' }}>
                  <Descriptions title="权益包基本信息" className={detailCss['ant-descriptions-custom']}>
                    <Descriptions.Item label="权益包类型">{detail.typeName || '-'}</Descriptions.Item>
                    <Descriptions.Item label="权益包编码">{detail.packNo || '-'}</Descriptions.Item>
                    <Descriptions.Item label="权益包版本编码">{detail.packVersion || '-'}</Descriptions.Item>

                    <Descriptions.Item label="权益包名称">{detail.name || '-'}</Descriptions.Item>
                    <Descriptions.Item label="权益车型">
                      {[...new Set(detail.packCarConfigs?.map(item => item.baseConfig))].join('、') || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="车辆市场类型">{detail.carMarketTypeName || '-'}</Descriptions.Item>

                    <Descriptions.Item
                      contentStyle={{ width: '60%', display: 'block' }}
                      label="权益包车型配置"
                      span={3}
                    >
                      {/* {!!detail.packCarConfigs?.length
                        ? detail.packCarConfigs.map(item => (
                            <Tag style={{ marginBottom: 10, marginRight: 10, padding: '2px 7px', borderRadius: 3 }}>
                              {item.carOfYear + '/' + item.powerModel + '/' + item.config}
                            </Tag>
                          ))
                        : '-'} */}
                      {!!detail.packCarConfigs?.length ? renderCarConfig(detail.packCarConfigs) : '-'}
                    </Descriptions.Item>

                    <Descriptions.Item label="权益包属性">{detail.attributeName || '-'}</Descriptions.Item>
                    <Descriptions.Item className={detailCss['item-red']} label="是否在APP上展示">
                      {display_MAP[detail.display] || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="车辆销售类型">{detail.packSalesTypeName || '-'}</Descriptions.Item>

                    <Descriptions.Item label="宣传时间" span={1}>
                      {detail.activityBeginTime && detail.activityEndTime ? (
                        <span>
                          {moment(detail.activityBeginTime).format('YYYY年MM月DD日 HH:mm:ss')}~
                          {moment(detail.activityEndTime).format('YYYY年MM月DD日 HH:mm:ss')}
                        </span>
                      ) : (
                        '-'
                      )}
                    </Descriptions.Item>
                    <Descriptions.Item label="宣传状态" span={2}>
                      {status_MAP[detail.status] || '-'}
                    </Descriptions.Item>

                    <Descriptions.Item label="权益包描述" span={3}>
                      {detail.description || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="权益支持区域" span={3}>
                      <span className="area-box">{detail.allAreaName || '-'}</span>
                    </Descriptions.Item>
                    <Descriptions.Item label="权益支持门店" span={3}>
                      <span className="area-box">{detail.allDealerName || '-'}</span>
                    </Descriptions.Item>
                  </Descriptions>

                  {[2, 4, 5].includes(detail.type) ? (
                    <Descriptions
                      title="权益包营销信息"
                      className={detailCss['ant-descriptions-custom']}
                      style={{ marginTop: 10 }}
                    >
                      <Descriptions.Item label="权益来源">
                        {equitySource_MAP[detail.equitySource] || '-'}
                      </Descriptions.Item>
                      <Descriptions.Item label="销售渠道">{detail.salesChannelName || '-'}</Descriptions.Item>
                      <Descriptions.Item label="活动名称">{detail.promotionName || '-'}</Descriptions.Item>

                      <Descriptions.Item label="权益请示名称">{detail.askingName || '-'}</Descriptions.Item>
                      <Descriptions.Item label="权益请示编码">{detail.askingNo || '-'}</Descriptions.Item>
                      <Descriptions.Item label="权益请示活动有效期">
                        {detail.askingStartTime
                          ? `
                                                            ${moment(detail.askingStartTime).format('YYYY年MM月DD日 HH:mm:ss')}
                                                            -
                                                            ${moment(detail.askingEndTime).format('YYYY年MM月DD日 HH:mm:ss')}
                                                       `
                          : '-'}
                      </Descriptions.Item>

                      <Descriptions.Item label="权益请示活动数量">{detail.askingCount || '-'}</Descriptions.Item>
                      <Descriptions.Item label="单车预算">{detail.budgetSingle || '-'}</Descriptions.Item>
                      <Descriptions.Item label="合计预算">{detail.budgetAmount || '-'}</Descriptions.Item>

                      <Descriptions.Item label="活动发起部门">
                        {subject_MAP[detail.promotionInitiator] || '-'}
                      </Descriptions.Item>
                      <Descriptions.Item label="销售结算主体">
                        {subject_MAP[detail.salesSubject] || '-'}
                      </Descriptions.Item>
                      <Descriptions.Item label="成本价格">{detail.costPrice || '-'}</Descriptions.Item>

                      <Descriptions.Item label="销售价格">{detail.price || '-'}</Descriptions.Item>
                      <Descriptions.Item label="采购价格">{detail.purchasePrice || '-'}</Descriptions.Item>
                      <Descriptions.Item label="销售服务费结算比例">{detail.percentage || '-'}</Descriptions.Item>

                      <Descriptions.Item label="销售收入">
                        {detail.price > 0 && detail.percentage > 0
                          ? `${detail.price * ((100 - Number(detail.percentage)) / 100)}元`
                          : '-'}
                      </Descriptions.Item>
                      <Descriptions.Item label="权益包销售主体">
                        {equitySalesSubject_MAP[detail.equitySalesSubject] || '-'}
                      </Descriptions.Item>
                      <Descriptions.Item label="是否应用于活动中">
                        {promotionExist_MAP[detail.promotionExist] || '-'}
                      </Descriptions.Item>

                      <Descriptions.Item label="活动名额">{detail.promotionQuota || '不限名额'}</Descriptions.Item>
                      {/* <Descriptions.Item label='剩余活动名额'>{detail.promotionQuota || '-'}</Descriptions.Item> */}
                      <Descriptions.Item label="活动时间">
                        {detail.promotionStartTime
                          ? `${moment(detail.promotionStartTime).format('YYYY年MM月DD日 HH:mm:ss')}-${moment(detail.promotionEndTime).format('YYYY年MM月DD日 HH:mm:ss')}`
                          : '不限时间'}
                      </Descriptions.Item>

                      <Descriptions.Item label="活动状态">{detail.promotionStatusName || '-'}</Descriptions.Item>
                    </Descriptions>
                  ) : null}
                </div>
              </div>
            </div>

            <div style={{ background: '#fff' }}>
              <div style={{ ...titleCss, marginTop: 24 }}>
                <span>权益包内权益明细关联</span>
              </div>
              <div>
                <span style={{ ...subTitleCss, paddingTop: 15, display: 'inline-block' }}>
                  {detail.outsideDetailDisplayList && detail.outsideDetailDisplayList.length
                    ? '对内核销策略'
                    : '内外一致'}
                </span>
                <DetailPubliTable
                  className={styles['gray-table']}
                  dataSource={detail.detailDisplayList}
                  rowKey={record => record.detailNo.toString()}
                  strategy={'in'}
                  pagination={false}
                  equityType={detail.type}
                />
              </div>
              {detail.outsideDetailDisplayList && detail.outsideDetailDisplayList.length ? (
                <div>
                  <span style={subTitleCss}>对外展示内容</span>
                  {detail.groupDetails && detail.groupDetails.length ? (
                    <GroupPublicTable
                      className={styles['gray-table']}
                      dataSource={detail.outsideDetailDisplayList}
                      rowKey={record => record.detailNo.toString()}
                      strategy={'out'}
                      pagination={false}
                      equityType={detail.type}
                    />
                  ) : (
                    <DetailPubliTable
                      className={styles['gray-table']}
                      dataSource={detail.outsideDetailDisplayList}
                      rowKey={record => record.detailNo.toString()}
                      strategy={'out'}
                      pagination={false}
                      equityType={detail.type}
                    />
                  )}
                </div>
              ) : null}
            </div>

            <div className="top-box" style={{ ...titleCss, marginTop: 24 }}>
              {isItemLabel ? (
                <img
                  style={{ ...iconCss }}
                  src={require('@/assets/img/up-icon.png')}
                  onClick={() => {
                    setIsItemLabel(false);
                  }}
                  alt=""
                />
              ) : (
                <img
                  style={{ ...iconCss }}
                  src={require('@/assets/img/down-icon.png')}
                  onClick={() => {
                    setIsItemLabel(true);
                  }}
                  alt=""
                />
              )}
              <span>操作信息</span>
            </div>

            <div className="detailPanel">
              <div className="log-wrap">
                <Logs type="pack" dataCode={locationParmas.packNo} showAll={isItemLabel}>
                  {item => (
                    <div>
                      {item.changeContent &&
                        typeof item.changeContent === 'object' &&
                        Object.entries(item.changeContent).map(k => (
                          <>
                            {k[0]}：{k[1]}
                          </>
                        ))}
                    </div>
                  )}
                </Logs>
              </div>
            </div>
          </div>
        </div>
        <Row
          className="tableTitle"
          style={{ paddingBottom: 24, boxShadow: '0px -2px 6px 0px rgba(0,0,0,0.08)', zIndex: 1000 }}
        >
          <Col className="text"></Col>
          <Col className="bts">
            <Button onClick={onCancel}>返回</Button>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};
export default PackManagementDetail;
