import {GET_SIDER_TYPE,USERINFO,COMMON_TABADD,GET_MENULIST,GET_DICT,CHANGE_PWD_STATUS,LIST_EXPAND, CHANGE_SHOWREDDOT, STAFF_LIST_EXPAND} from '../constants'

//sider展开折叠状态
export const setSiderNormal= data =>{
    return {
        type: GET_SIDER_TYPE,
        payload: data
    }
};

export const getUserInfoNormal = fn => {
    return {
        type: USERINFO,
        payload: fn()
    }
};

// 公共tab页面的添加
export const PublicTabAddNormal=fn=>{
    return{
        type:COMMON_TABADD,
        payload:fn()
    }
};

// 获取菜单列表
export const getMenuListNormal=fn=>{
    return{
        type:GET_MENULIST,
        payload:fn()
    }
};

// 获取公共字典
export const getDictNormal=fn=>{
    return{
        type:GET_DICT,
        payload:fn()
    }
};

export const changePsd = data=>{
    return {
        type:CHANGE_PWD_STATUS,
        payload:data
    }
};
// 扩展列状态
export const changeListExpand = fn=>{
    return {
        type:LIST_EXPAND,
        payload:fn()
    }
}
// 门店管理列表扩展列状态
export const staffchangeListExpand = fn=>{
    return {
        type:STAFF_LIST_EXPAND,
        payload:fn()
    }
}
export const changeShowRedDot = data => {
    return {
        type: CHANGE_SHOWREDDOT,
        payload: data
    }
}
