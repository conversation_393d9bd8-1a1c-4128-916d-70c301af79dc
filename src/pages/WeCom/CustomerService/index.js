import './index.less'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { Table, message, Tabs, Input} from 'antd'
import CustomerManage from  './CustomerManage'
import CustomerData from './CustomerData'
import SessionManage from './SessionManage'
import { roleJudgment } from '@/utils/authority'
const { TabPane } = Tabs;
const CustomerService = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('CustomerDataTabsKey') || '1')
    const tabsCallback = (key) => {
        setTabsKey(key)
        sessionStorage.setItem('CustomerDataTabsKey',key)
    }

    return (
        <div className='CustomerService'>
            <Tabs onChange={tabsCallback} activeKey={tabsKey} defaultActiveKey={tabsKey} >
            
            {
                roleJudgment(userInfo, 'CUSTOMER_MANAGE') ? 
                    <TabPane tab="客服管理" key="1">
                        <CustomerManage></CustomerManage>
                    </TabPane>
                : null
            }
            {
                roleJudgment(userInfo, 'SESSION_MANAGE') ? 
                    <TabPane tab="会话管理" key="2">
                        <SessionManage></SessionManage>
                    </TabPane>
                : null
            }
            {
                roleJudgment(userInfo, 'CUSTOMER_DATA') ? 
                    <TabPane tab="问题标注" key="3">
                        <CustomerData></CustomerData>
                    </TabPane>
                : null
            }  
            </Tabs>
        </div>
    )
}
export default CustomerService
