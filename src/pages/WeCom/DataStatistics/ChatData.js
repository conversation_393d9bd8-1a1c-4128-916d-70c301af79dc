/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useRef, useMemo, useState, forwardRef, useImperativeHandle, useCallback} from 'react'
import ReactEcharts from "echarts-for-react";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import { useSelector } from 'react-redux'
import { Row,Col,message, DatePicker, Select, Tabs, Radio, Modal, Form, Button} from 'antd'
import { post } from '@/utils/request'
import allUrl from '@/utils/url'
import moment from 'moment';
import './ChatData.less'
import { roleJudgment } from '@/utils/authority'
import ChartContainer from './ChartContainer'
import ChartContainer1 from './ChartContainer1'
import PublicTooltip from '@/components/Public/PublicTooltip'
import ResponseDataStatisticsList from './components/ResponseDataStatisticsList'
import UserCenterRankingList from './components/UserCenterRankingList'
import HousekeeperGroupRankingList from './components/HousekeeperGroupRankingList'
import { formatSeconds, fileDown } from '@/utils'
const { RangePicker } = DatePicker;
const { TabPane } = Tabs
const {Option} = Select


/** 组件 ----------  昨日用户中心服务响应排行榜、昨日管家群服务响应排行榜 */
const HorizontalHistogram = props => {
   
    const { data, type } = props
    const colors = ['#D32F09', '#FF4D24', '#D6700C', '#FF7E00', '#FF9E00', '#E9BC04', '#FFCD00', '#E1D50A', '#FDE606', '#e1ef39'].reverse()
    const getOption = () => {
        return {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                },
                // formatter(params){
                //     return `
                //         ${params[0].axisValue}<br />
                //         ${params[0].marker} 未建群交车单 ${params[0].value} 个
                //     `
                // }
                formatter(params) {
                    let relVal = params[0].name
                    for (var i = 0, l = params.length; i < l; i++) {
                        relVal += `<div style="display:flex;justify-content: space-between;">
                                    <div>${params[i].marker}</div><div style="width:120px;text-align: left;">
                                    ${params[i].seriesName}</div><div style="flex:1;text-align:right">
                                    ${type == 1 ? formatSeconds(params[i].value) : `${params[i].value}%`}</div>
                            </div>`
                    }
                    return relVal
                }
            },
            grid: {
                top: '0%',
                left: '0%',
                right: '5%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                // boundaryGap: [0, 0.01],
                name: type == 1 ? '秒' : '百分比',
                formatter(value, index) {
                    return type == 1 ? `${Math.floor(value / 60)}分` : `${Math.floor(value)}%`
                },
            },
            yAxis: {
                type: 'category',
                data: data ? data.names : [],
                axisLabel: {
                    width: 160,
                    overflow: 'truncate',
                   
                    rich: {
                        a: {
                            fontSize: 13,
                            lineHeight: 22,
                            color: 'rgba(0,0,0,0.9)'
                        },
                        b: {
                            fontSize: 12
                        },

                    }
                }
            },
            series: [
                {
                    name: type == 1 ? `响应时长` : `及时回复率`,
                    type: 'bar',
                    data: data ? data.values : [],
                    itemStyle: {
                        normal: {
                            //这里是重点
                            color: function (params) {
                                return colors[params.dataIndex]
                            }
                        }
                    }
                },
            ]
        }
    }
 
    return <div>
        <ReactEcharts
            style={{ minHeight: "400px", }}
            option={getOption()}
        />
    </div>
}

/** 组件 ----------  服务响应趋势图 */
const DynamicLineChart = forwardRef((props, ref)  =>{
    const { Data1, type } = props;
    const getOption2 = () => {
        const { xAxis, timelyReplyPercentLine, avgRoundTimeLine } = Data1
        let array = []
        if(type == 1) {
            avgRoundTimeLine.length && avgRoundTimeLine.map((i) => {
                i.smooth = true
                i.symbol = 'none'
                array.push(i.name)
            })
        } else {
            timelyReplyPercentLine.length && timelyReplyPercentLine.map((i) => {
                i.smooth = true
                i.symbol = 'none'
                array.push(i.name)
            })
        }
       

        return {
             title: {
            //    text: 'Stacked Line'
             },
             tooltip: {
               trigger: 'axis',
               formatter(params) {
                let relVal = params[0].name
                for (var i = 0, l = params.length; i < l; i++) {
                    relVal += `<div style="display:flex;justify-content: space-between;">
                                <div>${params[i].marker}</div><div style="width:120px;text-align: left;">
                                ${params[i].seriesName}</div><div style="flex:1;text-align:right">
                                ${type == 1 ? formatSeconds(params[i].value) : `${params[i].value}%`}</div>
                        </div>`
                }
                return relVal
            }
             },
             legend: {
               data: array
             },
             grid: {
               left: '12%',
               right: '2%',
               bottom: '2%',
               containLabel: true
             },
             toolbox: {
            //    feature: {
            //      saveAsImage: {}
            //    }
             },
             xAxis: {
               type: 'category',
               boundaryGap: false,
               data: xAxis,
               axisLabel: {
                //    rotate: 40
               }
             },
             yAxis: [{
               type: 'value',
               axisTick: {
                alignWithLabel: false,
                // length: 7,
                // interval: 5
              },
              splitNumber: 5,
            //    name: type == 1 ? '响应时长' : '及时回复率',
                axisLabel: {
                    formatter:function(value, index) {
                        if(value < 3600 && value >= 300) {
                            return type == 1 ? `${Math.floor(value / 60)}分` : `${Math.floor(value)}%`
                        } else if(value < 300) { // 小于五分钟用分
                            return type == 1 ? `${Math.floor(value)}秒` : `${Math.floor(value)}%`
                        } else {
                            return type == 1 ? `${Math.floor(value / 3600)}小时` : `${Math.floor(value)}%`
                        }
                    }
                }
             }],
             series:  type == 1 ? avgRoundTimeLine : timelyReplyPercentLine,
        }
    }
    useMemo(()=>{
        // getOption2()
    },[Data1])
     // 将方法暴露给父组件使用
    useImperativeHandle(ref, () => ({
        getOption2,
    }));
    return <div style={{height: '400px', width: '100%'}}>
        <ReactEcharts
            notMerge={true} // echarts-for-react提供的解决数据更新echat不更新问题
            style={{ minHeight: "100%", }}
            option={getOption2()}
            ref={ref}
            // key={Date.now()}  // 解决数据更新echat不更新问题，页面重新渲染就会调用，放弃
        />
    </div>
})

const ChatData = (props) => {
    const [form] = Form.useForm()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [visible1, setVisible1] = useState(false)
    const [visible2, setVisible2] = useState(false)
    const [visible3, setVisible3] = useState(false)
    const [visible4, setVisible4] = useState(false)
    const [dates, setDates] = useState(null);
    // const [hackValue, setHackValue] = useState(null);
    const [value, setValue] = useState([moment().add(-30, 'd'), moment(new Date()).subtract(1, 'days')]);
    const [Data1, setData1] = useState({
        xAxis: [],
        avgRoundTimeLine: [],
        timelyReplyPercentLine: [],
        tables: []
    })
    const echartsRef = useRef()

    const [Data2, setData2] = useState(null)
    const [Data3, setData3] = useState([])
    const [userCenterList, setUserCenterList] = useState([])
    const [areaList, setAreaList] = useState([])
    const [currentAreaCodes, setCurrentAreaCodes] = useState([])
    const [defaultQueryArea, setDefaultQueryArea] = useState([])
    const [selectAreas, setSelectAreas] = useState([])
    const [chatType, setChatType] = useState('1')
    const [groupStatus, setGroupStatus] = useState('')
    const [groupStatus2, setGroupStatus2] = useState('')
    const [timeStatus, setTimeStatus] = useState()
    const [timeStatus2, setTimeStatus2] = useState()
    const [userCenterType, setUserCenterType] = useState('1')
    const [nationTable, setNationTable] = useState({
        avgRoundTime: '',
        avgRoundTimeDay: '',
        avgRoundTimeHour: '',
        avgRoundTimeMinute: '',
        avgRoundTimeSecond: '',
        avgRoundTimeChainPercent: '',
        avgRoundTimeGrowType:0,
        timelyReplyPercent: '',
        timelyReplyChainPercent: '',
        timelyReplyPercentGrowType: 0
    })
   const [exportParams, setExportParams] = useState({})
    const GROUP_STATUS = [
        {
            name: '合计',
            value: ''
        },
        {
            name:'待交付',
            value: '1'  
        },
        {
            name: '交付完成',
            value: '2'
        }
    ]
    const [groupStatusName, setGroupStatusName] = useState('合计')
    const TIME_STATUS =[
        {
            name: '全天',
            value: ''
        },
        {
            name:'工作时间',
            value: '1'  
        },
        {
            name: '非工作时间',
            value: '2'
        }
    ]
    const [timeStatusName, setTimeStatusName] = useState('全天')
    const disabledDate = (current) => {
        if (!dates) {
            return false;
        }
        if(current > moment().add(-1, 'days')) {
            return true
        }
        const tooLate = dates[0] && current.diff(dates[0], 'days') >= 30;
        const tooEarly = dates[1] && dates[1].diff(current, 'days') >= 30;
        return !!tooEarly || !!tooLate;
    };

    const onOpenChange = (open) => {
        if (open) {
            // setHackValue([null, null]);
            // setDates([null, null]);
        } else {
            // setHackValue(null);
        }
    };

    //全国指标表
    const getNationTable = (queryType) => {
        post(allUrl.WeCom.nationTable, {deliverStatus: groupStatus,queryTimeType:timeStatus}).then(res => {
            if (res.success && res.resp.length) {
                setNationTable(res.resp[0])
                // timeStatus ? setNationTable(res.resp[0]):
                // setNationTable(res.resp[timeStatus])
                console.log(timeStatus);
            } else {
                // message.error(res.msg)
            }
        })
    }

    //昨日用户中心服务响应排行榜
    const getYesterdayDealerStatistic = (queryType, deliverStatus,timeStatus2) => {
        const type = queryType || userCenterType
        post(allUrl.WeCom.yesterdayDealerStatistic, 
            {
                queryDataType: type==1 ? 'ROUND_TIME' : 'REPLY_RATE',
                queryType: type,
                deliverStatus: deliverStatus || '',
                queryTimeType: timeStatus2 
            }).then(res => {
            if (res.success) {
                setUserCenterList(res.resp)
                if(res.resp.length){
                    let arr = res.resp.slice(0,10).concat([]) // 数组深拷贝
                    arr.reverse() // 响应时长需要倒序处理，这个echat图默认是数据小的放前面
                    let obj = {
                        names: [],
                        values: [],
                        valuesStr: []
                    }
                    arr.forEach((item,index) => {
                        if(index < 10) {
                            obj.names.push(item.fullName || '')
                            console.log(timeStatus2);
                            if(timeStatus2 == 'NORMAL' || !timeStatus2){
                                obj.values.push(type == 1 ? (item.intradayAvgTimeCost || 0) : item.avgResponseRate || 0)//全天时段秒 || 百分比
                            }else if(timeStatus2 == 'NONWORKING_TIME'){
                                obj.values.push(type == 1 ? (item.nonworkingTimeAvgRoundTimeNumber || 0) : item.nonworkingTimelyReplyPercentNumber || 0)//非工作时间时段秒 || 百分比
                            }else if(timeStatus2 == 'WORKING_TIME'){
                                obj.values.push(type == 1 ? (item.workingTimeAvgRoundTimeNumber || 0) : item.workingTimelyReplyPercentNumber || 0)//工作时间时段秒 || 百分比
                            }
                        }
                    })
                    setData2(obj)
                    console.log(obj,'date2数据');
                }
            } else {
                // message.error(res.msg)
            }
        })
    }
    // 服务响应趋势图查看更多数据
    const getMainChartTable = () => {
        let startTime = value[0]
        let endTime = value[1]
        setExportParams({
            startDate: moment(startTime).format('YYYY-MM-DD'),
            endDate: moment(endTime).format('YYYY-MM-DD'),
            areaCodes: selectAreas,
            deliverStatus: '',
            // queryTimeType: '',
        })
        post(allUrl.WeCom.mainChart, 
            {
                startDate: moment(startTime).format('YYYY-MM-DD'),
                endDate: moment(endTime).format('YYYY-MM-DD'),
                areaCodes: selectAreas,
                deliverStatus: '',
                // queryTimeType:'',
            }).then(res => {
            if (res.success && res.resp.length) {
                setData3(res.resp[0].tables || [])
            }
        })
    }
    const getMainChart = () => {
        let startTime = value[0]
        let endTime = value[1]
        setExportParams({
            startDate: moment(startTime).format('YYYY-MM-DD'),
            endDate: moment(endTime).format('YYYY-MM-DD'),
            areaCodes: selectAreas,
            deliverStatus: groupStatus,
            queryTimeType:timeStatus
        })
        post(allUrl.WeCom.mainChart, 
            {
                startDate: moment(startTime).format('YYYY-MM-DD'),
                endDate: moment(endTime).format('YYYY-MM-DD'),
                areaCodes: selectAreas,
                deliverStatus: groupStatus,
                queryTimeType:timeStatus
            }).then(res => {
            if (res.success && res.resp.length) {
                setData1(res.resp[0])
                // echartsRef.current.getOption2(res.resp[0])

            } else {
                // message.error(res.msg)
            }
        })
    }
    useEffect(() => {
        // echartsRef.current.getOption2()
    }, [Data1]);
    const handleChangeArea = (arr) => {
        if(arr.length > 7) {
            message.error('最多选择七个区域')
            return 
        }
        setCurrentAreaCodes(arr)
        let newArr = []
        if(arr.length) {
            arr.map((i) => {
                areaList.map(j => {
                    if(j.value === i){
                        newArr.push(j)
                    }
                })
            })
            setSelectAreas(newArr)
        } else {
            setSelectAreas([])
        }
        
    }

    useEffect(() => {
        if(selectAreas.length && value[0]._isValid && value[1]._isValid) {
            getMainChart() 
            getNationTable() 
        }
        if(Data1.xAxis.length && selectAreas.length === 0 && value[0]._isValid && value[1]._isValid){
            getMainChart()  
            getNationTable()

        }
    }, [selectAreas, value, groupStatus,timeStatus]);


    const getAllArea = () => {
        post(allUrl.WeCom.getAllMiddleArea).then(res => {
            if (res.success && res.resp.length) {
                setAreaList(res.resp[0])
                const areaList = res.resp[0]
                let arr = [], queryArr = []
                if(areaList.length) {
                    areaList.map((item,index) => {
                        if(index < 3) {
                            arr.push(item.value)
                            queryArr.push(item)
                        }
                      
                    })
                    setSelectAreas(queryArr)
                    setCurrentAreaCodes(arr)
                }
            }
        })
    }
    const changeDate = (val)  => {
        setValue(val)
    }

    useEffect(() => {
        getAllArea()
    }, [])

    const changeUserCenterType = (key) => {
        setUserCenterType(key)
        getYesterdayDealerStatistic(key, groupStatus2,timeStatus2)
    }

   const userRangeClick = () => {
        setVisible2(true)
   }

   const exportRangeClick = () => {
    form.resetFields()
     setVisible4(true)
    }
    
    // 导出
    const exportData = useCallback(() => {
        form.validateFields().then(values => {
            if (values.Time && values.Time.length) {
                const [start, end] = values.Time;
                const diffDays = end.diff(start, 'days');
                if (diffDays > 31) {
                    message.error('选择的日期范围不能超过31天');
                    return;
                }
                post(allUrl.WeCom.exportStoreChatData, {
                    startTime: moment(start).format("YYYY-MM-DD 00:00:00"),
                    endTime: moment(end).format("YYYY-MM-DD 23:59:59")}, {responseType: "blob"}).then(res=>{
                    console.log('res.fileName',res.fileName)
                    fileDown(res,res.fileName)
                })
            } else {
                message.error('请选择日期')
            }
        })
    })
      

    const changeChatType = (key) => {
        setChatType(key)
    }
    // 服务响应趋势图群状态选择
    const handleChangeGroupStatus = (itme) => {
        let key =itme.target.value
        setGroupStatus(key)
        setGroupStatusName(getGroupStatusName(key))
    } 
    // 
    const getGroupStatusName = (key) => {
        return GROUP_STATUS.map((i)=> {
            if(i.value === key){
                return i.name
            }
        })
    }
    
    //服务器响应趋势图时间段选择
    const handleChangeTimeStatus = (itme) => {
        let key =itme.target.value
        if(key == 0 || ''){
            setTimeStatus('NORMAL')
        }else if(key == 1){
            setTimeStatus('WORKING_TIME')
        }else if(key == 2){
            setTimeStatus('NONWORKING_TIME')
        }
        setTimeStatusName(getTimeStatusName(key))
    } 
    const getTimeStatusName = (key) => {
        return TIME_STATUS.map((i)=> {
            if(i.value === key){
                return i.name
            }
        })
    }

    // 昨日用户中心排行榜群状态选择
    const handleChangeGroupStatus2 = (itme) => {
        let key =itme.target.value
        setGroupStatus2(key)
    } 
    //昨日用户中心排行榜时间段选择
    const handleChangeTimeStatus2 = (itme) => {
        let key =itme.target.value
        if(key == 0 || ''){
            setTimeStatus2('NORMAL')
        }else if(key == 1){
            setTimeStatus2('WORKING_TIME')
        }else if(key == 2){
            setTimeStatus2('NONWORKING_TIME')
        }
    } 
    const lookMore1 = () => {
        getMainChartTable()
        setVisible1(true)
    }
    useEffect(() => {
        getYesterdayDealerStatistic('', groupStatus2,timeStatus2)
    }, [groupStatus2,timeStatus2])
    const renderDom1 =  {
        title: '服务响应趋势图',
        subtitle: '数据源：管家群聊天数据',
        statisticalTime: `统计截止${moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD 23:59:59')}`,
        extended: <a onClick={lookMore1}>查看更多</a>,
        filters:  
                <>
                </>,
        tab: <Tabs type="card" onChange={changeChatType} className='titleColor'>
                <TabPane tab='响应时长' key='1' />
                <TabPane tab='及时回复率' key='2' />
            </Tabs>,
        themeColor: '#C04EFF',
        authority: 'SERVICE_CORRESPONDING_TREND_CHART',
        loading: [{}],
        content: <div>
            <Row className='content-con'>
                    <Col span={7} className='content-left'style={{background:'#E8F3FF'}}>
                        <div className='content-left-item' style={{marginTop:'20px'}}>
                            <span style={{marginLeft:'20px',paddingRight:'10px'}}>日&nbsp;&nbsp;&nbsp;&nbsp;期：</span>
                            <RangePicker
                                style={{width:'267px'}}
                                value={value}
                                allowClear={false}
                                disabledDate={disabledDate}
                                // onCalendarChange={(val) => setDates(val)}
                                onChange={changeDate}
                                // onOpenChange={onOpenChange}
                            />
                        </div>
                    </Col>
                    <Col span={2}></Col>
                    <Col span={13} style={{marginTop:'20px'}}>
                        <Select maxLength={7} value={currentAreaCodes} allowClear showArrow defaultValue={currentAreaCodes} onChange={handleChangeArea} mode="multiple" placeholder="选择区域" style={{minWidth:'200px'}}>
                        {
                            areaList.length && areaList.map((item, index) => {
                                return <Option key={item.value} value={item.value}>{item.name}</Option>
                            })
                        } 
                        </Select>
                        
                    </Col>
                </Row>
            <Row  className='content-con'>
                <Col span={7} className='content-left'>
                    <div style={{background:'#E8F3FF',borderBottomRightRadius:'4px',borderBottomLeftRadius:'4px'}}>
                    <div style={{paddingBottom:'20px'}}>
                        <span style={{marginLeft:'20px',marginRight:'10px'}}>群状态：</span>
                        <Radio.Group defaultValue="" buttonStyle="solid"onChange={handleChangeGroupStatus} className='buttonStyle'>
                            <Radio.Button value="1"style={{border:'none',marginRight:'40px'}}className='statusFont'>待交付</Radio.Button>
                            <Radio.Button value="2"style={{border:'none',marginRight:'40px'}}className='statusFont'>交付完成</Radio.Button>
                            <Radio.Button value="" style={{border:'none'}}className='statusFont'>合计</Radio.Button>
                        </Radio.Group>
                    </div>
                    {
                      roleJudgment(userInfo, 'GROUP_RESPONSE_TIME_STATUS') ?
                    <div style={{paddingBottom:'20px'}}>
                        <span style={{marginLeft:'20px',marginRight:'20px'}}>时&nbsp;&nbsp;&nbsp;&nbsp;段:</span>
                        <Radio.Group defaultValue="" buttonStyle="solid"onChange={handleChangeTimeStatus} className='buttonStyle'>
                            <Radio.Button value="1"style={{border:'none',marginRight:'25px'}}className='statusFont'>工作时间</Radio.Button>
                            <Radio.Button value="2"style={{border:'none',marginRight:'26px'}}className='statusFont'>非工作时间</Radio.Button>
                            <Radio.Button value="" style={{border:'none'}}className='statusFont'>全天</Radio.Button>
                        </Radio.Group>
                    </div> : null
                    }
                    </div>
                    <div className='content-left-item'> </div>
                    <div className='content-left-item'>
                        <div className="info">
                            <div className="info-head">
                            <span className="title1">昨日平均响应时长({timeStatusName})</span>
                            <span className="title2">全国({groupStatusName})</span>
                            </div>
                            <div className="info-body flex-row">
                            <div className="flex align-center">
                                <span className={`value ${nationTable.avgRoundTimeGrowType == 0 ? '' : nationTable.avgRoundTimeGrowType > 0 ? 'red' : 'green'}`}>{nationTable.avgRoundTimeDay || '-'}</span>
                                <span className="unit">天</span>
                                <span className={`value ${nationTable.avgRoundTimeGrowType == 0 ? '' : nationTable.avgRoundTimeGrowType > 0 ? 'red' : 'green'}`}>{nationTable.avgRoundTimeHour || '-'}</span>
                                <span className="unit">小时</span>
                                <span className={`value ${nationTable.avgRoundTimeGrowType == 0 ? '' : nationTable.avgRoundTimeGrowType > 0 ? 'red' : 'green'}`}>{nationTable.avgRoundTimeMinute || '-'}</span>
                                <span className="unit">分</span>
                                <span className={`value ${nationTable.avgRoundTimeGrowType == 0 ? '' : nationTable.avgRoundTimeGrowType > 0 ? 'red' : 'green'}`}>{nationTable.avgRoundTimeSecond || '-'}</span>
                                <span className="unit">秒</span>
                            </div>
                            <span className={`pre ${nationTable.avgRoundTimeGrowType == 0 ? '' : nationTable.avgRoundTimeGrowType > 0 ? 'red' : 'green'}`}>{nationTable.avgRoundTimeChainPercent || '-'}</span>
                            </div>
                        </div>
                    </div>
                    <div className='content-left-item'>
                        <div className="info">
                            <div className="info-head">
                            <span className="title1">昨日平均及时回复率({timeStatusName})</span>
                            <span className="title2">全国({groupStatusName})</span>
                            </div>
                            <div className="info-body">
                            <span className={`value ${nationTable.timelyReplyPercentGrowType == 0 ? '' : nationTable.timelyReplyPercentGrowType > 0 ? 'green' : 'red'}`}>{nationTable.timelyReplyPercent || '-'}</span>
                            <span className={`pre ${nationTable.timelyReplyPercentGrowType == 0 ? '' : nationTable.timelyReplyPercentGrowType > 0 ? 'green' : 'red'}`}>{nationTable.timelyReplyChainPercent || '-'}</span>
                            </div>
                        </div>
                    </div>
                </Col>
                <Col span={17} className='content-right'>
                    <DynamicLineChart ref={echartsRef} type={chatType} Data1={Data1} style={{minHeight:900}}/>
                </Col>
            </Row>
        </div>,
        ColSpan:24,
    }
    const renderDOM = [
        {
            title: '昨日用户中心服务响应排行榜',
            tab: <Tabs type="card" onChange={changeUserCenterType}className='titleColor'>
                    <TabPane tab='响应时长' key='1' />
                    <TabPane tab='及时回复率' key='2' />
                </Tabs>,
            subtitle: '数据源：管家群聊天数据',
            themeColor: '#FF6539',
            authority: 'USER_CENTER_RESPONSE_RANKING',
            loading: [{}],
            statisticalTime: `统计截止${moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD 23:59:59')}`,
            content: <>
            
            
                    <HorizontalHistogram data={Data2} type={userCenterType} />
                    </>,
            ColSpan:24,
            extended: (
                <div>
                    {
                        roleJudgment(userInfo, 'EXPORT_STORE_CHAT_DATA') ? <a style={{marginRight: 20}} onClick={exportRangeClick}>导出门店响应数据</a> : null
                    }
                    <a onClick={userRangeClick}>查看更多</a>
                </div>
            ),
            filters: 
                   <>
                <Row >
                    <Col span={7} style={{background:'#E8F3FF'}}>
                        <div style={{padding:'20px 0 20px 0'}}>
                        <span style={{marginLeft:'20px',marginRight:'10px'}}>群状态：</span>
                        <Radio.Group defaultValue="" buttonStyle="solid"onChange={handleChangeGroupStatus2} className='buttonStyle'>
                            <Radio.Button value="1"style={{border:'none',marginRight:'40px'}}className='statusFont'>待交付</Radio.Button>
                            <Radio.Button value="2"style={{border:'none',marginRight:'40px'}}className='statusFont'>交付完成</Radio.Button>
                            <Radio.Button value="" style={{border:'none'}}className='statusFont'>合计</Radio.Button>
                        </Radio.Group>
                        </div>
                    </Col>
                </Row>
                {
                      roleJudgment(userInfo, 'USER_CENTER_TIME_STATUS') ?

                <Row style={{paddingBottom:'20px'}}>
                    <Col span={7} style={{background:'#E8F3FF',borderBottomRightRadius:'4px',borderBottomLeftRadius:'4px'}}>
                        <div style={{padding:'0 0 20px 0'}}>
                        <span style={{marginLeft:'20px',marginRight:'20px'}}>时&nbsp;&nbsp;&nbsp;&nbsp;段:</span>
                        <Radio.Group defaultValue="" buttonStyle="solid"onChange={handleChangeTimeStatus2} className='buttonStyle'>
                            <Radio.Button value="1"style={{border:'none',marginRight:'25px'}}className='statusFont'>工作时间</Radio.Button>
                            <Radio.Button value="2"style={{border:'none',marginRight:'26px'}}className='statusFont'>非工作时间</Radio.Button>
                            <Radio.Button value="" style={{border:'none'}}className='statusFont'>全天</Radio.Button>
                        </Radio.Group>
                        </div>
                    </Col>  
                </Row> : null
                }

                    </>
        },
        // {
        //     title: <Row className='title-con'>
        //         <Col span={10} className='title-con-text'>昨日管家群服务响应排行榜</Col>
        //         <Tabs type="card">
        //             <TabPane tab='响应时长' key='1' />
        //             <TabPane tab='及时回复率' key='2' />
        //         </Tabs>
        //     </Row>,
        //     subtitle: '数据源：管家群聊天数据',
        //     themeColor: '#FD3B6B',
        //     authority: 'HOUSEKEEPER_GROUP_RESPONSE_RANKING',
        //     loading: Data2,
        //     statisticalTime: `统计截止${moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD 23:59:59')}`,
        //     content: <HorizontalHistogram data={Data2} type={2} />,
        //     extended: <div className='extended-con'>

        //         <a onClick={() => setVisible3(true)}>查看更多</a>
        //     </div>
        // },
    ]
    return (
        <div className='DataStatistics'>
            <div className='content'>
            
            <Row gutter={[24, 24]}>
                <ChartContainer1 {...renderDom1}>{renderDom1.content}</ChartContainer1>
                {renderDOM.map((item, index) => {
                    return roleJudgment(userInfo, item.authority) ? <ChartContainer1 key={index} {...item}>{item.content}</ChartContainer1> : null
                })}
            </Row>
            </div>
            {
                visible1 &&
                <ResponseDataStatisticsList data={Data3} title='全国服务响应数据统计' exportParams={exportParams} visible={visible1} onCancel={() => setVisible1(false)} />
            }
            {
                visible2 &&
                <UserCenterRankingList data={userCenterList} groupStatus={groupStatus2} userCenterType={userCenterType} title={`昨日用户中心服务响应排行榜（${moment().subtract(1, 'day').format('YYYY-MM-DD')}）`} visible={visible2} onCancel={() => setVisible2(false)} />
            }
            {
                visible3 &&
                <HousekeeperGroupRankingList title={`昨日管家群服务响应排行榜（${moment().subtract(1, 'day').format('YYYY-MM-DD')}）`} visible={visible3} onCancel={() => setVisible3(false)} />
            }
            {visible4 && <Modal className="codeContainer" visible={visible4} footer={null} width={500} maskClosable={true} onCancel={()=>setVisible4(false)} >
            <Form className='PublicList_FormQuery' form={form}>
                <Row>
                    <Col>
                        <Form.Item label="日期" name='Time'>
                            <RangePicker placeholder={['开始日期', '结束日期']} />
                        </Form.Item>
                    </Col>
                    <Col>
                        <Form.Item>
                            {
                                <a style={{ marginLeft: '20px' }} onClick={() => exportData()}>
                                  导出
                                </a>
                            }
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
            </Modal>
            }
        </div>
    )
}
export default ChatData
