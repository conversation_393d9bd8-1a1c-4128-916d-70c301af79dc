import React, { useState ,useEffect} from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {message, Modal,Table,Menu, Dropdown ,Button} from 'antd'
import {get} from '../../../../utils/request'
import allUrl from '../../../../utils/url'
import PersonnelTrainingInfo  from './PersonnelTrainingInfo'
import moment from 'moment'
import {roleJudgment} from '@/utils/authority'
import {utilsDict} from '@/utils/utilsDict'


const Personnel = (props) =>{
    const { visible, onCancel,title ,rowData} = props
    const [dataSource,setDataSource] = useState([])
    const [total,setTotal] = useState(0)
    const [current,setCurrent] = useState(1)
    const [pageSize,setPageSize] = useState(10)
    const [loading,setLoading] = useState(false)
    const [recordObj,setRecordObj] = useState({})
    const [operationType,setOperationType] = useState(null)
    const [operationVisible,setOperationVisible] = useState(false)
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 

    const handleOk = () =>{

    }

    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
    }
    const openModal = (type,record) =>{
        setOperationType(type)
        setOperationVisible(true)
        setRecordObj(record)
    }

    const columns = [
        {title:'序号',dataIndex:'index',width:80,fixed:'left',render:(text,record,index)=>index+1},
        {title:'姓名',dataIndex:'userName',width:140,fixed:'left'},
        {title:'手机号',dataIndex:'mobilePhone',width:100},
        {title:'CEO账号ID',dataIndex:'ceoUserCode',width:135},
        {title:'岗位',dataIndex:'positionName',width:160},
        {title:'兼职岗位',dataIndex:'partTimePositionName',width:160},
        {title:'入职时间',dataIndex:'inductionTime',width:120,render:text=>text?moment(text).format('YYYY-MM-DD'):''},
        {title:'面试是否通过',dataIndex:'interviewResult',width:120},
        {title:'是否在职',dataIndex:'isOnJob',width:100,render:text=>text?'是':'否'},
        {title:'当前等级',dataIndex:'currentLevel',width:120},
        {title:'目标等级',dataIndex:'targetLevel',width:120},
        // {title:'证书',dataIndex:'12',width:160},
        {title:'身份证',dataIndex:'idNum',width:160},
        {title:'开户行',dataIndex:'bankName',width:160},
        {title:'银行卡',dataIndex:'bankAccount',width:160},
        {title:'企微是否绑定',dataIndex:'qywxUserId',width:150,render:text=>text?'绑定':'未绑定'},
        {title:'企微激活状态',dataIndex:'qywxStatus',width:120,render:text=>utilsDict('qywxStatus',text)},
        {title:'账号是否启用',dataIndex:'status',width:150,render:text=>text?'启用':'未启用'},
        {title:'操作',dataIndex:'active',fixed:'right',width:120,render:(text,record,index)=>{
            return <>
                {
                    roleJudgment(userInfo,'PERM_STORELIST_TRAINING_INFO_EDIT') &&
                    <a onClick={()=>openModal(1,record)}>编辑培训信息</a>
                }
                {
                    roleJudgment(userInfo,'PERM_STORELIST_TRAINING_INFO_VIEW') &&
                    <a onClick={()=>openModal(2,record)}>查看培训信息</a>
                }
            </>
        }},
    ]
    const InforData = {
        rowKey: record => record.key,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content' },
        columns,
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
    };
    const ModalProps = {
        width:1200,
        wrapClassName:'AddOrEditStore',
        title:'人员查看',
        visible,
        onOk:handleOk,
        onCancel,
        maskClosable:false,
        footer:null
    }
    const getTableData = () =>{
        setLoading(true)
        get(allUrl.StoreManage.getStoreUserList,{organizationId:rowData.organizationId || 0,pageNum:current,pageSize}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                Dt.list.map((item,index)=>item.key = index)
                setDataSource(Dt.list)
                setTotal(Dt.total)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }
    useEffect(()=>{
        getTableData()
    },[current,pageSize])
    return <Modal {...ModalProps}>
        <Table {...InforData}/>
        {
            operationVisible && 
            <PersonnelTrainingInfo type={operationType} visible={operationVisible} getTableData={getTableData} rowData={rowData} recordObj={recordObj} onCancel={()=>setOperationVisible(false)} />
        }
    </Modal>
}
export default Personnel