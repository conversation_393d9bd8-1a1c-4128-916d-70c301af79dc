import React,{useState,useEffect} from 'react'
import { Modal, Form ,Row,Col, message,Tooltip,Spin,Table} from 'antd'
import { get } from '@/utils/request'
import allUrl from '@/utils/url'
import './AddOrEditStore.less'

const AddOrEditStore = (props) => {
    const { visible, onCancel,title ,rowData} = props
    const [form] = Form.useForm();
    const [data,setData] = useState({})
    const[loading,setLoading] = useState(false)
    const handleOk = () => {
        form.validateFields().then(values=>{
            console.log(values)
        })
    }
    useEffect(()=>{
        setLoading(true)
        get(allUrl.OrderManage.getOrderDetail,{id:rowData.id}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                Dt.orderEntries.map((item,index)=>item.key = index + 1)
                setData(Dt)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        })
    },[])
    const formItemLayout = {
        labelCol: {
            span: 9,
        },
        wrapperCol: {
            span: 15,
        },
    }
    const columns = [
        {title:'行号',dataIndex:'entryNumber',width:80},
        {title:'商品编号',dataIndex:'productCode',width:120},
        {title:'商品名称',dataIndex:'productName',width:200,render:text=><Tooltip title={text}>{text}</Tooltip>},
        {title:'sku编码',dataIndex:'skuCode',width:160},
        {title:'sku名称',dataIndex:'skuName',width:200},
        {title:'数量',dataIndex:'orderQty',width:60},
        {title:'单价',dataIndex:'unitPrice',width:120},
        {title:'总价格',dataIndex:'totalPrice',width:120},
    ]
    return <Modal width={1200} wrapClassName='AddOrEditStore' title={title} visible={visible} onOk={handleOk} onCancel={onCancel} footer={false} maskClosable={false}>
            <Form {...formItemLayout}>
                <Spin spinning={loading}>
                    <Row>
                        <Col span={8}><Form.Item label='订单编号'>{data?data.orderCode:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='下单手机号'>{data?data.userMobile:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='推荐人姓名'>{data?data.recommendUserName:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='推荐人手机号'>{data?data.recommendUserPhone:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='购车人姓名'>{data?data.carBuyerName:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='购车人手机号'>{data?data.carBuyerPhone:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='销售厅店'>{data?data.dealerName:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='当前支付状态'>{data?data.paymentStatusName:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='交付时间'>{data?data.deliveryTime:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='销售厅店编号'>{data?data.dealerCode:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='订单总金额'>{data?data.cashTotal:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='已收金额'>{data?data.receivedAmount:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='提车点'>{data?data.carPickUpPlace:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='应收定金金额'>{data?data.receivedDepositAmount:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='已收订单金额'>{data?data.totalDepositAmount:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='折扣金额'>{data?data.deductionAmount:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='订单尾款'>{data?data.finalPayment:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='订单状态'>{data?data.orderStatusName:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='支付方式'>{data?data.mode:''}</Form.Item></Col>
                        <Col span={8}><Form.Item label='支付类型'>{data?data.tradeType:''}</Form.Item></Col>
                    </Row>
                    <Row style={{padding:'0 40px'}}>
                        <Table size='middle' rowKey='key' columns={columns} dataSource={data?data.orderEntries:[]} bordered pagination={false} scroll={{x:'max-content'}} />
                    </Row>
                </Spin>
            </Form>
    </Modal>


}
export default AddOrEditStore