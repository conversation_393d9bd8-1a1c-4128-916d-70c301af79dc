import { Button, Form, Input, message, InputNumber, Table } from 'antd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { get, post } from '@/utils/request'
import allUrl from '@/utils/url'
import './trainStandarSetting.less'
import history from '@/utils/history'

const EditableContext = React.createContext(null);

const EditableRow = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const EditableCell = ({
  title,
  editable,
  children,
  inputType,
  dataIndex,
  record,
  handleSave,
  ...restProps
}) => {

  const [editing, setEditing] = useState(false);
  const inputRef = useRef(null);
  const form = useContext(EditableContext);
  useEffect(() => {
    if (editing) {
      inputRef.current.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({
      [dataIndex]: record[dataIndex]
    });
  };

  const save = async () => {
    try {
      const values = await form.validateFields();
      toggleEdit();
      handleSave({ ...record, ...values });
    } catch (errInfo) {
      console.log("Save failed:", errInfo);
    }
  };

  let childNode = children;

  if (editable) {
    childNode = editing ? (
      <Form.Item
        style={{
          margin: 0
        }}
        name={dataIndex}
        rules={[
          {
            required: true,
            message: `${title} is required.`
          }
        ]}
      >
        {
          inputType === 'number'  ?  <InputNumber ref={inputRef} onPressEnter={save} onBlur={save} />
          :  <Input ref={inputRef} onPressEnter={save} onBlur={save} />
        }
      </Form.Item>
    ) : (
      <div
        className="editable-cell-value-wrap"
        style={{
          paddingRight: 24
        }}
        onClick={toggleEdit}
      >
        {children}
      </div>
    );
  }

  return <td {...restProps}>{childNode}</td>;
};

const DrainStandarSettings = () => {
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  useEffect(() => {
    post(allUrl.StoreManage.getTrainStandard).then(res => {
        if (res.success) {
            let Dt = res.resp[0]
            Dt.list.map((item, index) => item.key = index + 1)
            setDataSource(Dt.list)
        } else {
            // message.error(res.msg)
        }
    })
}, [])
  const setTable = () => {
    setLoading(true)
    post(allUrl.StoreManage.setTrainStandard, {
      list: dataSource
    }).then(res => {
      if (res.success) {
          message.success('设置成功')
      } 
      setLoading(false)
  })
  }
  const defaultColumns = [
   {
        title: '网点属性',
        children: [
            {
                title: <div style={{textAlign:'center'}}>模块</div>, 
                dataIndex: 'modelCodeName',
                render: (text, record, index) => {
                    const obj = {
                        children: <div style={{ textAlign: 'center', fontWeight: 'bold' }}>{text}</div>,
                        props: {
                            rowSpan:0
                        },
                    };
                    // 写死分组
                    if(index ===0){
                      obj.props.rowSpan = 8
                  }else if(index===8){
                      obj.props.rowSpan = 19
                  }
                  else if(index===27){
                      obj.props.rowSpan = 5
                  }
                  else if(index===32){
                      obj.props.rowSpan = 3
                  }
                  else if(index===35){
                      obj.props.rowSpan = 2
                  }
                  if(index ===37) {
                      obj.props.colSpan = 2
                      obj.props.rowSpan = 1
                  }
                  return obj
                },
            },
            {
                title: <div style={{textAlign:'center'}}>岗位信息</div>, 
                dataIndex: 'positionName',
            }
        ]
    },
    {
      title: "S",
      editable: true,
      children: [
        {
          title: "L1",
          dataIndex: "sl1",
          editable: true
        },
        {
          title: "L2",
          dataIndex: "sl2",
          editable: true
        },
        {
          title: "L3",
          dataIndex: "sl3",
          editable: true
        }
      ]
    },
    {
      title: "A",
      editable: true,
      children: [
        {
          title: "L1",
          dataIndex: "al1",
          editable: true
        },
        {
          title: "L2",
          dataIndex: "al2",
          editable: true
        },
        {
          title: "L3",
          dataIndex: "al3",
          editable: true
        }
      ]
    },
    {
      title: "B",
      editable: true,
      children: [
        {
          title: "L1",
          dataIndex: "bl1",
          editable: true
        },
        {
          title: "L2",
          dataIndex: "bl2",
          editable: true
        },
        {
          title: "L3",
          dataIndex: "bl3",
          editable: true
        }
      ]
    },
    {
      title: "C",
      editable: true,
      children: [
        {
          title: "L1",
          dataIndex: "cl1",
          editable: true
        },
        {
          title: "L2",
          dataIndex: "cl2",
          editable: true
        },
        {
          title: "L3",
          dataIndex: "cl3",
          editable: true
        }
      ]
    },
    {
      title: "D",
      editable: true,
      children: [
        {
          title: "L1",
          dataIndex: "dl1",
          editable: true
        },
        {
          title: "L2",
          dataIndex: "dl2",
          editable: true
        },
        {
          title: "L3",
          dataIndex: "dl3",
          editable: true
        }
      ]
    },
  ];

  const handleSave = (row) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataSource(newData);
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell
    }
  };

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    // 表头分组的可编辑单元行，需要对col.children进行操作
    if (col.children) {
      return {
        ...col,
        children:
          col.children.length &&
          col.children.map((subCol) => {
            return {
              ...subCol,
              onCell: (record) => {
                return {
                  record,
                  inputType: "number",
                  dataIndex: subCol.dataIndex,
                  title: subCol.title,
                  editable: subCol.editable,
                  handleSave
                };
              }
            };
          })
      };
    } else {
      return {
        ...col,
        onCell: (record) => ({
          record,
          editable: col.editable,
          dataIndex: col.dataIndex,
          title: col.title,
          handleSave
        })
      };
    }
  });
  return (
    <div className="trainStandarSetting">
      <Table
        components={components}
        rowClassName={() => "editable-row"}
        bordered
        dataSource={dataSource}
        columns={columns}
        scroll={{
          x: 2000,
          y: 600,
        }}
        pagination={false}
        // 动态计算合计
        summary={(pageData) => {
          let totalsl1 = 0;
          let totalsl2 = 0;
          let totalsl3 = 0;
          let totalal1 = 0;
          let totalal2 = 0;
          let totalal3 = 0;
          let totalbl1 = 0;
          let totalbl2 = 0;
          let totalbl3 = 0;
          let totalcl1 = 0;
          let totalcl2 = 0;
          let totalcl3 = 0;
          let totaldl1 = 0;
          let totaldl2 = 0;
          let totaldl3 = 0;
          pageData.forEach(({ sl1, sl2, sl3, al1, al2, al3, bl1, bl2, bl3, cl1, cl2, cl3, dl1, dl2, dl3 }) => {
            totalsl1 +=sl1
            totalsl2 +=sl2
            totalsl3 +=sl3
            totalal1 +=al1
            totalal2 +=al2
            totalal3 +=al3
            totalbl1 +=bl1
            totalbl2 +=bl2
            totalbl3 +=bl3
            totalcl1 +=cl1
            totalcl2 +=cl2
            totalcl3 +=cl3
            totaldl1 +=dl1
            totaldl2 +=dl2
            totaldl3 +=dl3
          });
          return (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell colSpan={2}><div style={{textAlign:'center'}}>合计</div></Table.Summary.Cell>
                <Table.Summary.Cell>{totalsl1}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalsl2}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalsl3}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalal1}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalal2}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalal3}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalbl1}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalbl2}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalbl3}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalcl1}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalcl2}</Table.Summary.Cell>
                <Table.Summary.Cell>{totalcl3}</Table.Summary.Cell>
                <Table.Summary.Cell>{totaldl1}</Table.Summary.Cell>
                <Table.Summary.Cell>{totaldl2}</Table.Summary.Cell>
                <Table.Summary.Cell>{totaldl3}</Table.Summary.Cell>
              </Table.Summary.Row>
            </>
          );
        }}
      >
      </Table>
      <div className='detail_btns'>
            <Button loading={loading} type="primary" onClick={setTable} style={{ marginRight: '10px' }}>保存</Button>
            <Button onClick={()=>history.goBack()}>返回</Button>
        </div>
    </div>
  );
};

export default DrainStandarSettings;
