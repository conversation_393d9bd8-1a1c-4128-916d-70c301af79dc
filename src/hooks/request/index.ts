import { useCallback, useEffect, useRef } from 'react';
import useSafeState from '../state';

const DEFAULT_CACHE_TIME = 5 * 60 * 1000;

const DEFAULT_STALE_TIME = 30 * 1000;

interface UseRequestOptions {
  /**
   * 是否默认 loading
   */
  defaultLoading?: boolean;
  /**
   * 是否手动触发请求
   */
  manual?: boolean;
  /**
   * 依赖项，当依赖项变化时，重新发起请求
   */
  deps?: any[];
  /**
   * 缓存 key
   */
  cacheKey?: string;
  /**
   * 缓存有效期，在缓存有效期内，会先返回缓存数据，并静默获取最新数据，单位：毫秒
   */
  cacheTime?: number;
  /**
   * 数据有效期，在有效期内不会重新发起请求，单位毫秒
   */
  staleTime?: number;
  /**
   * 是否允许相同请求并发，若为 false，则相同 cacheKey 的请求会被合并
   * @default true
   */
  concurrent?: boolean;
}

interface CacheData<T = any> {
  lastFetch: number;
  data: T;
}

/**
 * 全局数据缓存
 */
const globalCache = new Map<string, CacheData>();

/**
 * 全局请求并发控制
 */
const globalRequestPending = new Map<string, boolean>();

/**
 * 全局数据请求处理函数
 */
const handlers = new Map<string, ((res: any) => void)[]>();

/**
 * 带缓存和 loading 控制的请求
 * @param fn 请求方法
 * @param options 配置
 * @returns
 */
export function useRequest<T = any>(fn: () => Promise<T>, options: UseRequestOptions = {}) {
  const { deps = [] } = options;
  const optionsRef = useRef(options);
  const requestRef = useRef(fn);
  const [data, setData] = useSafeState<T>();
  const [loading, setLoading] = useSafeState(optionsRef.current.defaultLoading ?? !optionsRef.current.manual);
  const [error, setError] = useSafeState<Error>();
  const firstCalledRef = useRef(false);

  const request = useCallback(
    (force?: boolean) => {
      const {
        cacheKey,
        cacheTime = DEFAULT_CACHE_TIME,
        staleTime = DEFAULT_STALE_TIME,
        concurrent = true,
      } = optionsRef.current;
      firstCalledRef.current = true;
      setError(undefined);
      setLoading(true);
      if (cacheKey && !force) {
        const cacheData = globalCache.get(cacheKey);
        // 缓存有效，则直接返回缓存数据
        if (cacheData && cacheData.lastFetch + cacheTime > Date.now()) {
          setData(cacheData.data);
          setLoading(false);
          // 有效期内不再发起请求
          if (cacheData.lastFetch + staleTime > Date.now()) {
            return;
          }
        }
      }

      const success = (res: T) => {
        setData(res);
        if (cacheKey) {
          globalCache.set(cacheKey, {
            data: res,
            lastFetch: Date.now(),
          });
        }
      };
      if (concurrent || (cacheKey && !concurrent && !globalRequestPending.get(cacheKey))) {
        if (cacheKey && !concurrent) {
          globalRequestPending.set(cacheKey, true);
        }
        requestRef
          .current()
          .then((res) => {
            if (cacheKey && !concurrent) {
              const requestHandlers = handlers.get(cacheKey) ?? [];
              requestHandlers.forEach((handler) => {
                handler(res);
              });
            }
            success(res);
          })
          .catch((err: any) => {
            setData(undefined);
            setError(new Error(err.message || err.msg));
          })
          .finally(() => {
            if (cacheKey && !concurrent) {
              globalRequestPending.set(cacheKey, false);
            }
            setLoading(false);
          });
      } else if (cacheKey && !concurrent) {
        const requestHandlers = handlers.get(cacheKey) ?? [];

        requestHandlers.push(success);

        handlers.set(cacheKey, requestHandlers);
      }
    },
    [setData, setError, setLoading],
  );

  useEffect(() => {
    requestRef.current = fn;
  }, [fn]);

  useEffect(() => {
    optionsRef.current = options;
  });

  useEffect(() => {
    if (firstCalledRef.current || !optionsRef.current.manual) {
      request();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [request, ...deps]);

  return { data, loading, error, request };
}
