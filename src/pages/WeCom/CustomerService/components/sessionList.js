import "./SessionList.less"
import { Avatar, List, message, Button } from 'antd';
import React, { useEffect, useState } from 'react';
import Cookies from 'js-cookie'
import { post, get } from '@/utils/request'
import moment from 'moment'
import allUrl from '@/utils/url'
import BenzAMRRecorder from 'benz-amr-recorder';
const ContainerHeight = 400;
const SessionList = (props) => {
const [recordList, setRecordList] = useState([]);
const [total, setTotal] = useState(0);

const [sessionQuery, setSessionQuery] = useState({
    pageSize: 10,
    pageIndex: 1,
    sessionId: props.sessionItem.sessionId
})
const [sessionItem, setSessionItem] = useState(props.sessionItem)
const [initLoading, setInitLoading] = useState(true);
const [loading, setLoading] = useState(false);
const [voiceAni, setVoiceAni] = useState(false);
  

const queryMessageMedia = (item, index) => {
    let params = new URLSearchParams(); 
    params.append('msgId', item.msgId)
    post(allUrl.WeCom.queryMessageMedia, params, { isForm: true }).then(res => {
        if (res.success) {
                let ossUrl = res.resp[0]
                let request = new XMLHttpRequest();
                request.responseType = "blob";
                request.open("get", ossUrl, true);
                //这里带上请求头
                request.setRequestHeader("x-oss-meta-authorization", Cookies.get('scrm_token'));
                request.onreadystatechange = (e) => {
                    if (request.readyState == XMLHttpRequest.DONE && request.status === 200) {
                        if(item.msgType == 'voice'){
                            //amr对象不能共用，所以要为每条语音消息初始化一个amr对象
                            var amr = new BenzAMRRecorder();
                            let amrData = {}
                            amr.initWithUrl(URL.createObjectURL(request.response)).then(() => {
                                amrData.amrTime = Math.ceil(amr.getDuration());   //获取音频总时长
                                amrData.amr = amr;
                                let rList = JSON.parse(JSON.stringify(recordList)) 
                                rList[index].amrData = amrData
                                setRecordList(rList)
                            });
                        }else {
                            let rList = JSON.parse(JSON.stringify(recordList)) 
                            rList[index].ossUrl = URL.createObjectURL(request.response)
                            setRecordList(rList)
                        }
                    }
                }
                request.send(null);
                // if(item.msgType == 'video') {
                //     this.isPlay = true
                //     this.videoList.push(item)
                //     this.videoIndex = this.videoList.length - 1
                // }
        } else {
            message.error(res.msg)
        }
    })
}
const playVoice  = (item, index) => {
    if (item.amrData.amr.isPlaying()) {
        item.amrData.amr.pause();  //暂停
        setVoiceAni(false)
    } else {
        item.amrData.amr.play();  //播放
        setVoiceAni(true)
    }
    let timer = setInterval(() => {
        if(!recordList[index].amrData.amr.isPlaying()){
            setVoiceAni(false)
            clearInterval(timer)
        }
    }, 1000)
}
    const queryMessage = () => {
        post(allUrl.WeCom.queryMessage, { ...sessionQuery }).then(res => {
            if (res.success) {
                setRecordList(recordList.concat(res.resp[0].list));
                setTotal(res.resp[0].total)
            } else {
                message.error(res.msg)
            }
            setInitLoading(false);
        })
    };
    useEffect(() => {
        sessionQuery.sessionId = props.sessionItem.sessionId
        setSessionQuery(sessionQuery)
        queryMessage()
    }, [props.sessionItem.sessionId]);

    const onLoadMore = () => {
        if(total > recordList.length) {
            sessionQuery.pageIndex ++
            setSessionQuery(sessionQuery)
            setLoading(true);
            queryMessage()
        }
    }
  const loadMore =
    total > recordList.length  ? (
      <div style={{ textAlign: 'center', marginTop: 12,  height: 32, lineHeight: '32px' }} >
        <Button onClick={onLoadMore}>加载更多...</Button>
      </div>
    ) : null;
  return (
    <List
      style={{ maxHeight: ContainerHeight, overflowY: 'auto' }}
      loading={initLoading}
      itemLayout="horizontal"
      loadMore={loadMore}
      dataSource={recordList}
      renderItem={(item, index) => (
        <List.Item>
            <div className='SessionList'>
                <div className="r-d-item">
                    <div className="user-time">
                        <span>{ item.origin == 3 ? sessionItem.customerName : sessionItem.servicerName }</span>
                        <span style={{ color: '#333' }}>{ item.origin == 3 ? '@客户' : '@客服' }</span>
                        <span style={{ marginLeft: 16 }}>{ moment(item.msgTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                    </div>
                    {item.msgType == 'text' && <div className="r-text">{ item.dataDetail && item.dataDetail.content }</div>}
                    {item.msgType == 'image' && <div>
                        {!item.ossUrl ? <span className="tip" onClick={ () => queryMessageMedia(item, index) }>点击查看图片</span> :
                        <img style={{width: '75%', marginTop: '16px'}} src={item.ossUrl}></img>}
                    </div>}
                    {item.msgType == 'video' && <div>
                        {!item.ossUrl ? <span className="tip" onClick={ () => queryMessageMedia(item, index) }>点击查看视频</span> : 
                            <video style={{ width: '75%' }} src={ item.ossUrl } controls ></video>
                        }
                    </div>}
                    {item.msgType == 'voice' && <div>
                        {!item.amrData ? <span  className="tip" onClick={() => queryMessageMedia(item, index)}>点击获取语音</span> :
                        <div  className="voice-box" onClick={() => playVoice(item, index)} >
                            <div className={['voice-icon-box', voiceAni && 'voice-icon-ani'].join(' ')} >
                                <img className="voice-icon" src="https://adm-ms.icolor.com.cn/application/1/assets/voice.png" alt=""/>
                            </div>
                            <span className="amr-time">{item.amrData.amrTime}''</span>
                        </div>}
                    </div>}
                </div>
            </div>
          
        </List.Item>
      )}
    />
  );
};
export default SessionList;