import React,{useState,useEffect} from 'react'
import { Modal ,Table ,Select, message} from 'antd'
import {get,post} from '@/utils/request'
import allUrl from '@/utils/url'
import moment from 'moment'
import PublicTooltip from '@/components/Public/PublicTooltip'


const {Option} = Select
const DeliveryOrder = (props) =>{
    const {onCancel,visible,id} = props
    const [dataSource,setDataSource] = useState([])
    const [dataSource1,setDataSource1] = useState([])
    const [loading,setLoading] = useState(false)
    const [loading1,setLoading1] = useState(false)
    const [recordObj,setRecordObj] = useState({})
    const [visible1,setVisible1] = useState(false)
    const [dictData, setDictData] = useState([])

    const getTableData = () =>{
        setLoading(true)
        get(allUrl.WeCom.getCustomerSaleOrder,{id:id}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setDataSource(Dt)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        }).catch(()=>{
            setLoading(false)
        })
    }

    const lookLog = (record) =>{
        post(allUrl.WeCom.queryNotIncludeLog,{objectKey:record.id}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                setDataSource1(Dt)
            }else{
                // message.error(res.msg)
            }
            setLoading1(false)
        }).catch(()=>{
            setLoading1(false)
        })
        setRecordObj(record)
        setVisible1(true)
    }
    useEffect(()=> {
        get(allUrl.common.entryLists, {codes:'statistics_status_comment'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt['statistics_status_comment'].forEach(item=>{
                    item.name = item.entryMeaning
                    item.value = item.entryValue
                })
                setDictData(Dt['statistics_status_comment'])
            } 
        })
    }, [])
    const changeSalesOrderStatus = (v, record, key) => {
        post(allUrl.WeCom.updateSalesOrder, {id: record.id, statisticsStatus: v, statisticsStatusComment: record.statisticsStatusComment}).then(res => {
            if (res.success) {
                message.success('操作成功')
                getTableData()
            } 
        })
    }
    const changeSalesOrderStatusComment = (v, record, key) => {
        post(allUrl.WeCom.updateSalesOrder, {id: record.id, statisticsStatus: record. statisticsStatus, statisticsStatusComment: v}).then(res => {
            if (res.success) {
                message.success('操作成功')
                getTableData()
            } 
        })
    }
    const columns1 = [
        { title: '序号', dataIndex: 'index', width: 80,fixed:'left',render:(text,record,index)=>index+1 },
        { title: '交付单编号', dataIndex: 'deliverOrderNo',fixed:'left', width: 200},
        { title: '交付门店编号', dataIndex: 'deliverStoreCode', width: 120 },
        { title: '交付门店简称', dataIndex: 'deliverStoreName', width: 160,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '交付门店名称', dataIndex: 'delivererStoreFullName', width: 200,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '交付单门店所属大区', dataIndex: 'delivererStoreBigArea', width: 160 },
        { title: '交付单门店所属中区', dataIndex: 'delivererStoreMiddleArea', width: 160 },
        { title: '交付单门店所属小区', dataIndex: 'delivererStoreSmallArea', width: 160 },
        { title: '交付专员', dataIndex: 'delivererName', width: 120 },
        { title: '交付专员手机号', dataIndex: 'delivererPhone', width: 140 },
        { title: '交付专员DMS账号ID', dataIndex: 'delivererId', width: 180 },
        { title: '交付完成时间', dataIndex: 'deliveredTime', width: 180 ,render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss'):''},
        { title: '销售门店编码', dataIndex: 'saleStoreCode', width: 140 },
        { title: '销售门店简称', dataIndex: 'saleStoreName', width: 180,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '销售门店名称', dataIndex: 'saleStoreFullName', width: 200,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '购车人姓名', dataIndex: 'purchaserName', width: 200,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '购车人手机号', dataIndex: 'purchaserPhone', width: 200 },
        { title: '车主姓名', dataIndex: 'ownerName', width: 200,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '车主手机号', dataIndex: 'ownerPhone', width: 200},
        { title: '代理人姓名', dataIndex: 'agentName', width: 200,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '代理人手机号', dataIndex: 'agentPhone', width: 200 },
        { title: '物料代码', dataIndex: 'materielCode', width: 200 },
        { title: '车辆物料总称', dataIndex: 'skuName', width: 300 ,render:text=><PublicTooltip title={text}>{text}</PublicTooltip>},
        // { title: '金康正式订单编号', dataIndex: 'trueName', width: 120 },
        // { title: '华为正式订单编号', dataIndex: 'trueName', width: 120 },
        // { title: '正式订单交付时间', dataIndex: 'addTime', width: 180 ,render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss'):''},
        { title: '车辆VIN', dataIndex: 'carVin', width: 190 },
        { title: '车辆属性', dataIndex: 'carBaseConfig', width: 140 },
        { title: '车型', dataIndex: 'carModel', width: 140 },
        { title: '基础配置', dataIndex: 'carBaseConfig', width: 140 },
        { title: '外饰颜色', dataIndex: 'carOutColor', width: 120 },
        { title: '内饰颜色', dataIndex: 'carInColor', width: 120 },
        { title: '交付单状态', dataIndex: 'deliverStatusName',fixed:'right', width: 140 },
        { title: '操作日志', dataIndex: 'deliverStatusName',fixed:'right', width: 100,render:(text,record)=><a onClick={()=>lookLog(record)}>查看</a> },
        { title: '是否需要建群', dataIndex: 'statisticsStatus', fixed:'right', width: 120, editable: true, render:(text, record) => 
            <Select  defaultValue={text} placeholder='请选择' onChange={v => changeSalesOrderStatus(v, record)}>
                <Option value={1}>是</Option>
                <Option value={2}>否</Option>
            </Select>
        },
        {title: '备注原因', dataIndex: 'statisticsStatusComment', fixed:'right', width: 100, editable: true, render:(text, record) => 
        <Select style={{width: 100}} defaultValue={text} placeholder='请选择' onChange={(v) => changeSalesOrderStatusComment(v, record)}>
            {
                dictData.map((item,index)=><Option key={index} value={String(item.value)}>{item.name}</Option>)
            }
        </Select>
        },
    ]


    const columns2 = [
        { title: '时间', dataIndex: 'createTime', width: 120,render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss'):'' },
        { title: '修改人账号ID', dataIndex: 'userId'},
        { title: '修改人姓名', dataIndex: 'userName', width: 100 ,render: ((text, record) => record.log.userName)},
        { title: '修改人岗位', dataIndex: 'positionName',render: ((text, record) => record.log.positionName)},
        { title: '修改人所属组织', dataIndex: 'orgName', width: 160 ,render: ((text, record) => record.log.orgName)},
        { title: '修改人所属用户中心', dataIndex: 'dealerFullName',width: 160, render: ((text, record) => record.log.dealerFullName)},
        { title: '修改前内容', dataIndex: 'oldName', width: 160,render:((text, record) => record.log.oldName)},
        { title: '修改后内容', dataIndex: 'newName',width: 160,render: ((text, record) => record.log.newName)},
    ]



    useEffect(()=>{
        getTableData()
    },[id])
    return <div>
        <Modal title='交付单列表' width={1200} visible={visible} onCancel={onCancel} footer={null} maskClosable={false}>
            <Table rowKey='id' loading={loading} columns={columns1} dataSource={dataSource} scroll={{y:'max-content'}} pagination={false} />
        </Modal>
        <Modal title='操作日志' width={1200} visible={visible1} onCancel={()=>setVisible1(false)} footer={null} maskClosable={false}>
            <Table rowKey='id' bordered loading={loading1} columns={columns2} dataSource={dataSource1} scroll={{y:'max-content'}} pagination={false} />
        </Modal>
    </div>
}
export default DeliveryOrder