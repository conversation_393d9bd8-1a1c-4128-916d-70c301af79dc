import React, { useEffect, useState } from "react";
import { Modal, Select, Popconfirm, message, Spin, TimePicker } from "antd";

import moment from "moment";
import { nanoid } from "nanoid";
import { get, post } from "@/utils/request";
import allUrl from "@/utils/url";
import "./TimeoutReminder.less";

const { Option } = Select;
const SpecialTimeoutReminder = (props) => {
  const { visible, onCancel } = props;
  const [loading, setLoading] = useState(false);
  const [remindPeople, setRemindPeople] = useState([]);
  const [timeOption, setTimeOption] = useState([
    { name: "5", value: 5 },
    { name: "10", value: 10 },
    { name: "15", value: 15 },
    { name: "20", value: 20 },
    { name: "30", value: 30 },
    { name: "60", value: 60 },
  ]);
  const groupTypes = [
    {
      value: "1,2",
      label: "全部",
    },
    {
      value: "1",
      label: "待交付",
    },
    {
      value: "2",
      label: "交付完成",
    },
  ];
  const [ruleList, setRuleList] = useState([
    {
      dayStartTime: moment("00:00:00", "HH:mm:ss"),
      dayEndTime: moment("23:59:59", "HH:mm:ss"),
      timeout: null,
      deliverStatusList: "1,2",
      qwUserList: [],
      id: nanoid(),
      noticedPositionEntityList: "YJLCKF",
      isLocal: true,
      isModified: false,
    },
  ]);
  // 将列表数据转换为字符串，以','分隔
  const listToString = (list) => {
    let dataString = "";
    for (let i = 0; i < list.length; i++) {
      dataString = dataString + String(list[i]) + ",";
    }
    return dataString.slice(0, dataString.length - 1);
  };
  const changeRulesToSubmitType = (rules) => {
    return rules.map((item) => {
      return {
        dayStartTime: item.dayStartTime.format("HH:mm:ss"),
        dayEndTime: item.dayEndTime.format("HH:mm:ss"),
        timeout: item.timeout,
        deliverStatusList: item.deliverStatusList,
        isLocal: item.isLocal,
        isModified: item.isModified,
        userIds: listToString(item.qwUserList),
        id: item.id,
        noticedPositionCodes: item.noticedPositionEntityList+'',
      };
    });
  };
  const specialHandleOk = () => {
    for (let i = 0; i < ruleList.length; i++) {
      if (!ruleList[i].timeout) {
        message.error("请选择录入完整的提醒规则！");
        return;
      }
    }
    //console.log("ruleList数据为：", ruleList);
    let rules = changeRulesToSubmitType(ruleList);
    //console.log("rules数据为：", rules);
    setLoading(true);
    rules = rules.filter(
      (item) => item.isLocal || (item.isModified && !item.isLocal)
    );
    for (let i = 0; i < rules.length; i++) {
      // 新增的规则
      if (rules[i].isLocal) {
        delete rules[i].isLocal;
        delete rules[i].isModified;
        delete rules[i].id;
        post(allUrl.WeCom.addRule, { ...rules[i] }).then((res) => {
          if (!res.success) {
            message.error("保存失败！");
          } else {
          }
        });
      } else {
        delete rules[i].isLocal;
        delete rules[i].isModified;
        post(allUrl.WeCom.updateRule, { ...rules[i] }).then((res) => {
          if (!res.success) {
            message.error("保存失败！");
          } else {
          }
        });
      }
    }
    onCancel();
    setLoading(false);
    message.success("保存成功!");
  };

  const addRule = () => {
    let temp = [...ruleList];
    temp.push({
      dayStartTime: moment("00:00:00", "HH:mm:ss"),
      dayEndTime: moment("23:59:59", "HH:mm:ss"),
      timeout: null,
      deliverStatusList: "1,2",
      qwUserList: [],
      id: nanoid(),
      noticedPositionEntityList: "YJLCKF",
      isLocal: true,
      isModified: false,
    });
    setRuleList(temp);
  };

  const delRule = (row) => {
    var temp = [...ruleList];
    if (!row.isLocal) {
      get(allUrl.WeCom.deleteRule, { id: row.id }).then((res) => {
        if (res.success) {
        }
      });
    }
    temp = temp.filter((item) => item.id !== row.id);
    setRuleList([...temp]);
  };

  const fieldChange = (row, value, field) => {
    //console.log("字段更改了", value);
    let arr = [...ruleList];
    let temp = arr.filter((item) => item.id === row.id)[0];
    temp.isModified = true;
    if (field === "timeRange") {
      //console.log("时间改动", value);
      temp.dayStartTime = value[0];
      temp.dayEndTime = value[1];
    } else {
      temp[field] = value;
    }
    //console.log(arr);
    setRuleList(arr);
  };

  useEffect(() => {
    get(allUrl.common.entryLists, { codes: "qw_special_user_id" }).then(
      (res) => {
        //console.log(res);
        if (res.success) {
          let dt = res.resp[0].qw_special_user_id;
          setRemindPeople(
            dt.map((item) => {
              return {
                label: item.entryMeaning + "(" + item.extendField1 + ")",
                value: item.entryValue,
              };
            })
          );
        }
      }
    );
  }, []);

  useEffect(() => {
    setLoading(true);
    post(allUrl.WeCom.qwSpecialOvertimeNoticePage).then((res) => {
      setLoading(false);
      if (res.success) {
        let Dt = res.resp[0].list.map((item) => {
          return {
            id: item.id,
            dayStartTime: moment(item.dayStartTime, "HH:mm:ss"),
            dayEndTime: moment(item.dayEndTime, "HH:mm:ss"),
            timeout: item.timeout,
            deliverStatusList: item.deliverStatusList,
            isLocal: false,
            isModified: false,
            qwUserList:
              item.qwUserList == null
                ? []
                : item.qwUserList.map((subItem) => subItem.qwUserId),
            noticedPositionEntityList:
              item.noticedPositionEntityList == null
                ? 'YJLCKF'
                : item.noticedPositionEntityList.map(
                    (subItem) => subItem.noticedPositionCode
                  ),
          };
        });
        const ruleList = Dt;
        //console.log(ruleList);
        if (ruleList.length) {
          setRuleList(ruleList);
        }
      }
    });
  }, []);

  return (
    <Modal
      title="特殊超时提醒规则设置"
      visible={visible}
      onOk={specialHandleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      maskClosable={false}
      width={1300}
      wrapClassName="TimeoutReminder"
    >
      <div className="TimeoutReminder-wrap">
        <Spin spinning={loading}>
          <div className="title">
            <span>提醒规则：</span>
          </div>
          <div className="ruleList">
            {ruleList.map((item, index) => {
              return (
                <div className="ruleList-item" key={item.id}>
                  每日
                  <TimePicker.RangePicker
                    style={{ margin: "0 10px", width: "200px" }}
                    value={[item.dayStartTime, item.dayEndTime]}
                    onChange={(value) => fieldChange(item, value, "timeRange")}
                  />
                  超过
                  <Select
                    allowClear
                    style={{ margin: "0 10px", width: "60px" }}
                    value={item.timeout}
                    onChange={(value) => fieldChange(item, value, "timeout")}
                  >
                    {timeOption.map((item, index) => (
                      <Option key={index} value={item.value}>
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                  分钟未回复客户的
                  <Select
                    style={{
                      width: 100,
                      margin: "0 10px",
                    }}
                    value={item.deliverStatusList}
                    options={groupTypes}
                    onChange={(value) =>
                      fieldChange(item, value, "deliverStatusList")
                    }
                  />
                  群聊，提醒
                  <Select
                    style={{ width: 150, margin: "0 10px" }}
                    allowClear
                    placeholder="请选择岗位"
                    value={item.noticedPositionEntityList}
                    onChange={(e) =>
                      fieldChange(item, e, "noticedPositionEntityList")
                    }
                  >
                    <Option value={"YJLCKF"}>夜间凌晨客服</Option>
                    <Option value={"null"}>无</Option>
                  </Select>
                  岗位，提醒
                  <Select
                    mode="multiple"
                    style={{ width: 250, margin: "0 10px" }}
                    allowClear
                    placeholder="添加人员"
                    value={item.qwUserList}
                    onChange={(value) => fieldChange(item, value, "qwUserList")}
                  >
                    {remindPeople.map((item, index) => (
                      <Option key={index} value={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                  <Popconfirm
                    title="确定删除这条规则吗？"
                    onConfirm={() => delRule(item)}
                  >
                    <span
                      style={{
                        color: "red",
                        marginLeft: "13px",
                        cursor: "pointer",
                      }}
                    >
                      删除
                    </span>
                  </Popconfirm>
                </div>
              );
            })}
          </div>
          <div className="add-rule">
            <a onClick={addRule}>添加更多规则</a>
          </div>
        </Spin>
      </div>
    </Modal>
  );
};
export default SpecialTimeoutReminder;
