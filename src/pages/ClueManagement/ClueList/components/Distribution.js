import React from 'react'
import './Distribution.less'
import { Modal, message, Form, Input,Select,Row,Col,Radio, Space} from 'antd'
import { CloseOutlined} from '@ant-design/icons';
import allUrl from '../../../../utils/url'
import { post, get } from '../../../../utils/request'
import { connect } from 'react-redux'
import _ from "lodash";
import {trim} from '@/utils'
const {Option} = Select
class Distribution extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {
            loading:false,
            DealerOptions:[],
            DealerCheck:[],
            StorePersonOptions:[],
            StorePerson:'',
        }
    }
    formRef = React.createRef();
    handleOk = () => {
        const { onCancel ,getData,recordData,PersionType,tableSelectedRows} = this.props
        const { DealerCheck,StorePerson} = this.state
            if (PersionType == 1) {
                if(!DealerCheck.length){
                    message.error('请选择门店')
                    return
                }
                let onlyLeadIds =tableSelectedRows.map((item,index)=>{
                    return item.onlyLeadId
                })
                let dealerCodes = DealerCheck.map((item,index)=>{
                    return item.dealerCode
                })
                let data = {
                    onlyLeadIds:onlyLeadIds,
                    dealerCodes:dealerCodes
                }
                post(allUrl.ClueManagement.batchDistributionToDealer,{...data}).then(res=>{
                    if(res.success){
                        message.success(res.msg)
                        onCancel()
                        getData()
                    }else{
                        message.error(res.msg)
                    }
                })
                
            }else{
                if(!StorePerson){
                    message.error('请选择销售')
                    return
                }
                    let onlyLeadIds =tableSelectedRows.map((item,index)=>{
                        return item.onlyLeadId
                    })
                    let data = {
                        dealerCode:PersionType === 2 ? tableSelectedRows[0].dealerCode : recordData.dealerCode,
                        disChannel:3,
                        onlyLeadIds: PersionType === 2 ? onlyLeadIds : [recordData.onlyLeadId],
                        saleConsultantUserIds:[StorePerson]
                    }  
                
                post(allUrl.ClueManagement.batchDistributionLead,{...data}).then(res=>{
                    if(res.success){
                        message.success(res.msg)
                        onCancel()
                        getData()
                    }else{
                        message.error(res.msg)
                    }
                })
            }
            
    }
    onSearch =_.debounce((value)=>{
        value =trim(value)
        get(allUrl.ClueManagement.findDealersByKeywords, {keywords:value,}).then(res => {
            if (res.success) {
                let Dt =res.resp.map((item,index)=>{
                    return{value :item.name,
                        dealerCode :item.dealerCode,key:index+1}
                })
                this.setState( { DealerOptions:Dt })
            } else {
                // message.error(res.msg)
            }
        })

    }, 500);
    onChange = (value,index) =>{
        let newDt =[...this.state.DealerCheck]
        let newDtkey = this.state.DealerCheck.map((item,index)=>{
             return item.dealerCode
        })
        if(index){
                if (newDtkey.indexOf(index.dealerCode) == -1) {
                // 重复禁止添加
                newDt.push(index);
            }else{
                message.warn('已选择该门店')
            }
        }
        
        this.setState( { DealerCheck:newDt })
    }
    deDealerCheck = (key)=> {
        let newArr =[...this.state.DealerCheck]
        newArr.splice(key,1)
        this.setState( { DealerCheck:newArr })
    }
    StorePersonChange =(e)=>{
        this.setState({ StorePerson:e.target.value})
        console.log(e);
    }
    findSalesmanList =()=>{
        const {PersionType} = this.props
        if(PersionType !== 1){
            get(allUrl.ClueManagement.findSalesmanList).then(res => {
                if (res.success) {
                    let Dt =res.resp.map((item,index)=>{
                        return{value :item.userId,
                            name :item.userName,key:index+1}
                    })
                    this.setState( { StorePersonOptions:Dt })
                } else {
                    // message.error(res.msg)
                }
            })
        }
    }

    render() {
        const { visible, title,onCancel,PersionType } = this.props
        const { loading,DealerOptions,DealerCheck,StorePerson,StorePersonOptions } = this.state
        const layout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 20 },
        };
        
            return (
                <Modal
                    visible={visible}
                    title={title}
                    onOk={this.handleOk}
                    onCancel={onCancel}
                    maskClosable={false} 
                    width={500}

                >
                    <Form  {...layout} name="vertical" onFinish={this.onFinish} ref={this.formRef}>
                        
                            {
                                PersionType === 1 ?
                                <Row><Col span={16}>
                                    <Form.Item label="选择跟进的门店" name="customerName">
                                    <Select
                                        allowClear
                                        showSearch
                                        style={{
                                            width: '100%',
                                        }}
                                        placeholder="输入门店简称后选择"
                                        onSearch={this.onSearch}
                                        onChange={this.onChange}
                                        options={DealerOptions}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={24}>
                                    <Form.Item label="已选门店">
                                        {
                                            DealerCheck&&
                                            DealerCheck.map((item,index)=><Col key={index} value={item.value}>{item.value}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a style={{color:'rgba(0, 0, 0, 0.45)'}} onClick={()=>this.deDealerCheck(index)}><CloseOutlined /></a> </Col>)
                                        }
                                    </Form.Item>
                                </Col>
                                </Row>:null
                            }
                            {
                                PersionType === 2 ||PersionType === 3?
                                <Row><Col span={16}>
                                     <Radio.Group onChange={this.StorePersonChange} value={StorePerson}>
                                        <Space direction="vertical">
                                            {
                                                StorePersonOptions&&
                                                StorePersonOptions.map((item,index)=><Radio value={item.value}>{item.name}</Radio>)
                                            }

                                        </Space>
                                    </Radio.Group>
                                </Col>
                                </Row>:null
                            }
                    </Form>
                </Modal>
            )
        }
        componentDidMount(){
            this.findSalesmanList()
        }
    }
    export default connect()(Distribution)