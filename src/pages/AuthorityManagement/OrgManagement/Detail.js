import React from 'react'
import {  message, Button, Input, Row, Col, Form, Steps, Tree } from 'antd'
import { connect } from 'react-redux'
import history from '@/utils/history'
import { get } from '../../../utils/request'
import allUrl from '../../../utils/url'
import './Detail.less'
import '../index.less'
const { Step } = Steps
class Detail extends React.PureComponent {
    state = {
        loading: false,
        locationParmas:{},
        treeData: [],
        treeselectedKeys:[],
        treeSelectedRows:[],
        OrgAddVisible:false
    }
    formRef = React.createRef();
    onCancel = () => {
        history.goBack()
    }
    updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }

            if (node.children) {
                return { ...node, children: this.updateTreeData(node.children, key, children) };
            }

            return node;
        });
    }
    treeSelect = (selectedKeys,selectedNodes,event) =>{
        this.setState({treeselectedKeys:selectedKeys,treeSelectedRows:selectedNodes.selectedNodes})
    }

    render() {
        const {locationParmas,treeData,treeselectedKeys } = this.state

        const formItemLayout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 24 },
        }
        return (
            <div className='PublicDetail OrgManagementDetail'>
                <div className='DetailBox'>
                    <div className='DetailTitle'>{locationParmas.title}</div>
                    <div className='DetailCon'>
                        <Form
                            {...formItemLayout}
                            layout={'vertical'}
                            ref={this.formRef}
                            initialValues={{ remember: true, }}
                        >
                            <div className='progressBar'>
                                <Steps current={treeselectedKeys.length ? 1 :0}>
                                    {['选择上级组织', '添加下级组织','完成'].map((item, index) => (
                                        <Step key={index} title={item} />
                                    ))}
                                </Steps>
                            </div>
                            <div style={{background:'#f0f2f5',height:'24px'}}></div>
                            <Row className='OrgManagementCon'>
                                {/* <Row className='DetailInfo_Con'> */}
                                    <Col span={10} className='OrgManagementLeft'>
                                        <Tree defaultExpandedKeys={['0']} treeData={treeData} onSelect={this.treeSelect} />
                                    </Col>
                                    <Col span={14} className='OrgManagementRight'>
                                        <p style={{padding:'34px 0',fontSize:'16px',fontWeight:500}}>组织信息</p>
                                        <Form.Item style={{width:200}} label="组织名称" name="userName" rules={[{ required: true, message: '请输入组织名称！', }]}>
                                            <Input placeholder="请输入..." disabled={!treeselectedKeys.length} allowClear />
                                        </Form.Item>
                                    </Col>
                                {/* </Row> */}
                            </Row>
                            
                        </Form>
                    </div>
                </div>
                <div className='DetailBtns'>
                    <Button type="primary" onClick={this.Save} style={{marginRight:'10px'}}>确定</Button>
                    <Button onClick={this.onCancel}>取消</Button>
                </div>
            </div>
        )
    }
    componentDidMount(){
        console.log(this.props)
        let locationParmas = this.props.match.params.data ? JSON.parse(this.props.match.params.data):{}
        this.setState({
            locationParmas
        })
    }
}
export default connect()(Detail)