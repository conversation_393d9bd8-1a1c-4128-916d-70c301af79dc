.storeProjectManage{
    .ant-tabs{
        .ant-tabs-nav{
            margin-left: 32px;
            margin-bottom: 0;
        }
    }
    .storeProjectManage_graphic{
        background: #F0F2F5;
        padding: 24px 24px 0 24px;
    }
    .ant-pagination{
        padding-right: 20px;
    }
    .tableData{
        background-color: white;
        // padding: 24px 24px 72px 24px;

        border-top: solid 24px #f0f2f5;
        border-right: solid 24px #f0f2f5;
        border-bottom: solid 24px #f0f2f5;
        border-left: solid 24px #f0f2f5;
        .ant-table-wrapper{
            // background-color: white;
            .ant-table{
                padding: 24px;
                .ant-table-container{
                    border: 0;
                    .ant-table-content{
                        // overflow: auto hidden;
                        .ant-table-thead{
                            tr>th,tr>td{
                                // border: 0;
                            }
                        }
                    }
                    .ant-table-pagination{
                        margin: 16px 24px;
                    }
                }
            }
        }
        .title{
            font-size: 20px;
            font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
            font-weight: 500;
            text-align: left;
            color: rgba(0,0,0,0.85);
            line-height: 28px;
            margin: 24px 0 0 32px;
        }
        .bts{
            text-align: right;
            padding-right: 24px;
        }
        // .tableTitle{
        //     padding: 24px 24px 0px 32px;
        //     justify-content: space-between;
        //     .text{
        //         font-size: 20px;
        //         font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
        //         font-weight: 500;
        //         text-align: left;
        //         color: rgba(0,0,0,0.85);
        //         line-height: 28px;
        //     }
        //     .bts{
        //         .ant-btn{
        //             margin:0 7px;
        //         }
        //     }
        // }
        .ant-table-sticky-scroll{
            display: none;
        }
    }
    .PublicList_FormQuery{
        padding-top: 24px;
        padding-left: 24px;
        .ant-col-7{
            .ant-form-item{
                .ant-form-item-control-input{
                    width: 90%;
                    .ant-form-item-control-input-content{
                        .ant-picker{
                            width: 100%;
                        }
                    }
                }
            }
        }
        .FormQuerySubmit{
            display:flex;
            justify-content: flex-end;
            .operationButtons{
                span{
                    color: #1890ff;
                    cursor: pointer;
                    .anticon{
                        margin-left: 6px;
                    }
                }
            }
        }
    }
}