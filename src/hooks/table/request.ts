import { ProTableRequest } from '@/components/pro-table';
import { useCallback, useEffect, useRef } from 'react';

type ParamsType = Record<string, any>;

type RecordType = Record<string, any>;

/**
 * Pagination
 */
export interface Pagination {
  current?: number; // int32
  pageSize?: number; // int32
  total?: number; // int64
}

/**
 * PageData
 */
export interface PageData<T> {
  list?: T[];
  pagination?: Pagination;
}

export type RequestFn<T extends RecordType = RecordType, P extends ParamsType = ParamsType> = (
  ...args: Parameters<ProTableRequest<T, P>>
) => Promise<PageData<T>>;

export type PaginationRequestFn<T extends RecordType = RecordType, P extends ParamsType = ParamsType> = (
  ...args: Parameters<ProTableRequest<T, P>>
) => Promise<PageData<T>>;

export type PaginationListStyleRequestFn<T extends RecordType = RecordType, P extends ParamsType = ParamsType> = (
  ...args: Parameters<ProTableRequest<T, P>>
) => Promise<PageData<T>[]>;

export type ListRequestFn<T extends RecordType = RecordType, P extends ParamsType = ParamsType> = (
  ...args: Parameters<ProTableRequest<T, P>>
) => Promise<Array<T>>;

/**
 * 适配 ProTable 的请求方法
 * @param fn 请求方法
 * @returns
 */
export function useTableRequest<T extends RecordType = RecordType, P extends ParamsType = ParamsType>(
  fn: RequestFn<T, P>
) {
  const ref = useRef(fn);

  useEffect(() => {
    ref.current = fn;
  }, [fn]);
  const request = useCallback(async (...args: Parameters<ProTableRequest<T, P>>) => {
    try {
      const res = await ref.current?.(...args);

      return {
        data: res.list ?? [],
        success: true,
        total: res?.pagination?.total,
      };
    } catch (error) {
      return {
        data: [],
        total: 0,
        success: true,
      };
    }
  }, []);

  return request;
}

/**
 * 适配没有分页的 ProTable 的请求方法
 * @param fn 请求方法
 * @returns
 */
export function useListTableRequest<T extends RecordType = RecordType, P extends ParamsType = ParamsType>(
  fn: ListRequestFn<T, P>
) {
  const ref = useRef(fn);

  useEffect(() => {
    ref.current = fn;
  }, [fn]);
  const request = useCallback<ProTableRequest<T, P>>(async (...args) => {
    try {
      const res = await ref.current?.(...args);
      return {
        data: res ?? [],
        success: true,
      };
    } catch (error) {
      return {
        data: [],
        success: true,
      };
    }
  }, []);

  return request;
}

/**
 * 适配有分页的 ProTable 的请求方法
 * @param fn 请求方法
 * @returns
 */
export function usePaginationTableRequest<T extends RecordType = RecordType, P extends ParamsType = ParamsType>(
  fn: PaginationRequestFn<T, P>
) {
  const request = useTableRequest<T, P>((...args) => {
    return fn(...args);
  });

  return request;
}

/**
 * 适配有分页的 ProTable 的请求方法, 返回的数据是数组
 * @param fn 请求方法
 * @returns
 */
export function usePaginationTableListStyleRequest<
  T extends RecordType = RecordType,
  P extends ParamsType = ParamsType
>(fn: PaginationListStyleRequestFn<T, P>) {
  const request = useTableRequest<T, P>(async (...args) => {
    const res = await fn(...args);
    return res[0];
  });

  return request;
}
