.log-wrap {
    .log-item {
        display: flex;
        flex-direction: row;
        position: relative;
        z-index: 0;

        .log-time {
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            line-height: 20px;
            width: 165px;
        }

        .log-icon {
            width: 8px;
            height: 8px;
            background: #1890FF;
            border-radius: 50%;
            position: absolute;
            top: 7px;
            left: 162px;

            &::before {
                content: "";
                display: block;
                width: 20px;
                height: 20px;
                background: #fff;
                border-radius: 50%;
                border: 1px solid #1890FF;
                position: absolute;
                left: -6px;
                top: -6px;
                z-index: -1;
            }
        }

        .log-content {
            border-left: 2px dashed rgba(0, 0, 0, 0.08);
            border-radius: 2px;
            padding-left: 20px;
            padding-bottom: 26px;
            z-index: -2;
            line-height: 20px;
            flex: 1;

            div {
                margin-bottom: 14px;

                .ml6 {
                    margin-left: 6px;
                }
            }

            .changeContent {
                display: flex;
                flex-direction: row;

                .content {
                    flex: 1;
                }

                .log-block {
                    display: flex;

                    div {
                        margin-bottom: 0;
                    }

                    .log-word-break {
                        word-break: break-all;
                        overflow-wrap: break-word;
                    }

                    .log-flex-1 {
                        flex: 1;
                    }

                    .log-span {
                        .log-word-break();
                        display: block;
                        color: #1890ff;
                        cursor: pointer;
                        .log-flex-1();
                        margin-bottom: 10px;
                    }

                    .ant-image {
                        display: none;
                    }
                }
            }
        }
    }

    .log-item:last-child {
        .log-content {
            border-left: 2px dashed transparent;
        }
    }
}