import "./PerforKPI.less"
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import ReactEcharts from "echarts-for-react";
import "echarts/lib/chart/line";
import moment from 'moment'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import {fileDown} from '@/utils'
import PublicTableQuery from '@/components/Public/PublicTableQuery'

import {roleJudgment} from '@/utils/authority'
import { Row, Col, Button, DatePicker, Form, Card, message, Modal, Tooltip, Input,  Radio, Spin } from 'antd';
const { RangePicker } = DatePicker;
const { Search, TextArea } = Input;

const getOption = (echartsData) => {
    const { title, dataX, dataY } = echartsData
    return {
        title: {
          text: title
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['会话', '消息']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dataX
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '会话',
            type: 'line',
            symbolSize: 1,
            symbol: 'circle',
            smooth: true,
            stack: 'Total',
            data: dataY[0]
          },
          {
            name: '消息',
            type: 'line',
            symbolSize: 1,
            symbol: 'circle',
            smooth: true,
            stack: 'Total',
            data: dataY[1]
          },
        ]
    };
}
const CustomerKPI = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading, setLoading] = useState(false)
    const [exportLoading, setExportLoading] = useState(false)
    const [dataSource, changeDataSource] = useState([])
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)
    const [staticData, setStaticData] = useState({})
    const [tableHeight,setTableHeight] = useState(0)
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('CustomerDataTabsKey') || '1')
    const [echartsData24, setEchartsData24] = useState({
        title: '', dataX: [], dataY: [[], []]
    })
    const [echartsData, setEchartsData] = useState({
        title: '', dataX: [], dataY: [[], []]
    })
    const [defaultQuery, setDefaultQuery] = useState({

    })
    const [radioValue, setRadioValue] = useState(0);
    
    const onChangeRadio = (e) => {
        console.log('radio checked', e.target.value);
        let val = e.target.value
        let startTime = null, endTime = null
        if(val == 1) {
            startTime = moment().format('YYYY-MM-DD')
            endTime = moment().format('YYYY-MM-DD')
        }else if(val == 2) {
            startTime = moment().day(moment().day() - 6).format('YYYY-MM-DD')
            endTime = moment().format('YYYY-MM-DD')
        }else if(val == 3) {
            startTime = moment().subtract(1, 'months').format('YYYY-MM-DD') 
            endTime = moment().format('YYYY-MM-DD')
        }else if(val == 4) {
            startTime = moment().subtract(1, 'quarters').format('YYYY-MM-DD')
            endTime = moment().format('YYYY-MM-DD')
        }
        setDefaultQuery({
            startTime: startTime,
            endTime: endTime
        })
        setRadioValue(e.target.value);
    };
    
    const getData = () => {
        getSessionKpi()
        getSessionKpiHours()
        getSessionKpiDay()
    }
    const getSessionKpi = () => {
        setLoading(true)
        post(allUrl.WeCom.sessionKpi, { ...defaultQuery }).then(res => {
            if (res.success) {
                // 处理回执情况字段，把两个字段包到一个字段中
                setStaticData(res.resp[0])
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }
    const getSessionKpiHours = () => {
        setLoading(true)
        post(allUrl.WeCom.sessionKpiHours, { ...defaultQuery }).then(res => {
            if (res.success) {
                let dataX = [], dataY = [[], []]
                for (const item of res.resp[0]) {
                    dataX.push(item.groupData)
                    dataY[0].push(item.consultationNum)
                    dataY[1].push(item.msgNum)
                }
                setEchartsData24({
                    title: '24小时进线量趋势',
                    dataX: dataX,
                    dataY: dataY
                })
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }
    const getSessionKpiDay = () => {
        setLoading(true)
        post(allUrl.WeCom.sessionKpiDay, { ...defaultQuery }).then(res => {
            if (res.success) {
                let dataX = [], dataY = [[], []]
                for (const item of res.resp[0]) {
                    dataX.push(item.groupData)
                    dataY[0].push(item.consultationNum)
                    dataY[1].push(item.msgNum)
                }
                setEchartsData({
                    title: '进线量趋势',
                    dataX: dataX,
                    dataY: dataY
                })
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }
  
    useEffect(() => {
        
    }, [])
    useEffect(()=>{
        initPage()
    },[])
    useEffect(() => {
        if (userInfo) {
            getData()
        }
    }, [defaultQuery, userInfo, current, pageSize, tabsKey])

    const onChangePicker = (value, dateString) => {
        let startTime = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        let endTime = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
        setDefaultQuery({
            startTime: startTime,
            endTime: endTime
        })
        setRadioValue(0);
    }
    const handleSearch = () => {
        getData()
    }
    const onReset = () => {
        setRadioValue(0);
        setDefaultQuery({
            startTime: null,
            endTime: null
        })
        getData()
    } 
       
    return (
        <div className='content CustomerKPI'>
            <Card>
            <Form className='PublicList_FormQuery' >
                <Row style={{ paddingRight: '20px' }}>
                    <Col xs={24} sm={12} xl={8} xxl={8}>
                        <Radio.Group onChange={onChangeRadio} value={radioValue}>
                            <Radio value={1}>今天</Radio>
                            <Radio value={2}>近一周</Radio>
                            <Radio value={3}>近一月</Radio>
                            <Radio value={4}>近三月</Radio>
                        </Radio.Group>
                    </Col>
                    <Col xs={24} sm={12} xl={8} xxl={8}>
                        <RangePicker placeholder={['开始时间','结束时间']} onChange={onChangePicker} />
                    </Col>
                    <Col xs={24} sm={12} xl={8} xxl={8} className='FormQuerySubmit'>
                        <Col span={4} className='Reset'>
                            <Button onClick={onReset}>重置</Button>
                        </Col>
                        <Col span={4} className='Search'>
                            <Button type='primary' onClick={handleSearch}>查询</Button>
                        </Col>
                    </Col>
                </Row>
            </Form>
            <Spin spinning={loading}>
            <div className='static-data'>
                <div className='item'>
                    <div className='total'>
                        <div className='data-text'>咨询量</div>
                        <div className='num'>{ staticData.consultationNum || 0 }</div>
                    </div>
                    <div className='session-num'>
                        <div className="">有效会话数：{ staticData.sessionNumValid || 0 }</div>
                        <div className='s-bottom'>无效会话数：{ staticData.sessionNumInvalid || 0 }</div>
                    </div>
                    <div className='session-num'>
                        <div className="">接起会话数：{ staticData.sessionNum || 0 }</div>
                        {/* <div className='s-bottom'>排队中止量：{ staticData.queueAbortNum || 0 }</div> */}
                    </div>
                </div>
                <div className='item'>
                    <div className='total'>
                        <div className='data-text'>净满意度</div>
                        <div className='num'>{ staticData.satisfaTimesNet ? (staticData.satisfaTimesNet*100).toFixed(2) + '%' : 0 }</div>
                    </div>
                    <div className='session-num'>
                        <div className="">平均首次响应时长：{ staticData.firstResponseDurationAvg || 0 }</div>
                        <div className='s-bottom'>平均响应时长（秒）：{ staticData.responseDurationAvg || 0 }</div>
                    </div>
                    <div className='session-num'>
                        <div className=''>平均会话时长（秒）：{ staticData.sessionDurationAvg || 0 }</div>
                        {/* <div className='s-bottom'>接通率：{ staticData.connectionRate ? (staticData.connectionRate*100).toFixed(2) + '%' : 0 }</div> */}
                        
                    </div>
                </div>
            </div>
            
            <div className='echarts-box'>
                <div className='echarts' style={{padding: '24px'}}>
                    <ReactEcharts
                        style={{ minHeight: "400px", }}
                        option={getOption(echartsData24)}
                    />
                </div>
                <div className='echarts' style={{padding: '24px'}}>
                    <ReactEcharts
                        style={{ minHeight: "400px", }}
                        option={getOption(echartsData)}
                    />
                </div>
            </div>
            </Spin> 
            </Card>
        </div>
    )
}
export default CustomerKPI
