import React, { useState,useEffect } from 'react'
import { Modal ,Table ,Divider,message} from 'antd'
import {get} from '@/utils/request'
import allUrl from '@/utils/url'
import UploadFile from '@/components/Public/UploadFile'
import baseURL from '@/baseURL'

const ImportResultQuery = (props) =>{

    const {title,visible,onCancel} = props
    const [dataSource,setDataSource] = useState([])
    const [loading,setLoading] = useState(false)
    const [current,setCurrent] = useState(1)
    const [pageSize,setPageSize] = useState(10)
    const [total,setTotal] = useState(0)
    const handleOk = () =>{
        onCancel()
    }
    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
    }

    const getData = () =>{
        setLoading(true)
        get(allUrl.StoreManage.getFileList).then(res=>{
            if(res.success){
                let Dt = res.resp?res.resp :[]
                Dt.map((item,index)=>item.key = index+1)
                setDataSource(Dt)
                setTotal(Dt.length)
            }
            setLoading(false)
        })
    }
    
    const UploadChange = ({ file, fileList }, type) => {
        const { response } = file
        if (file.status === 'uploading') {
            setLoading(true)
        }
        if (file.status === 'done') {
            if(response.success){
                message.success(response.msg || response.msgCode)
                setLoading(false)
                getData()
            }else{
                message.error(response.msg || response.msgCode)
            }
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msgCode)
            setLoading(false)
        }
    }
    const getInportUrl = (record) =>{
        return baseURL.Host + allUrl.StoreManage.importExcel
    }
    const columns = [
        {title:'任务ID',dataIndex:'id',width:100},
        {title:'文件名称',dataIndex:'name'},
        {title:'导入时间',dataIndex:'time'},
        {title:'结果',dataIndex:'statusName'},
        {title:'操作',dataIndex:'operation',width:140,render:(text,record)=>
        !record.status &&<div>
            {
                record.url &&
                <span>
                    <a onClick={()=>{
                        window.open(record.url)
                    }}>{'下载'}</a>
                    <Divider type="vertical" />
                </span>
            }
            <UploadFile
                style={{ display: 'inline-block' }}
                extension={['xls', 'xlsx']}
                showUploadList={false}
                size={5}
                action={()=>getInportUrl(record)}
                UploadChange={(file) => UploadChange(file, 1)}
                data={{id:record.id}}
            >
                <a>重新导入</a>
            </UploadFile>
        </div>
        },
    ]
    const InforData = {
        rowKey: record => record.id,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content' },
        columns,
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
    };
    useEffect(()=>{
        getData()
    },[])
    return<Modal title={title} visible={visible} onOk={handleOk} onCancel={onCancel} width={800} maskClosable={false} footer={null}>
        <Table {...InforData} />
    </Modal>
}
export default ImportResultQuery
