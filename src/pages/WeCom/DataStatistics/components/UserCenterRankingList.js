import React ,{useEffect, useState} from 'react'
import { Modal ,Table ,Popover, message,But<PERSON>, Tooltip} from 'antd'
import allUrl from '@/utils/url'
import {post, get} from '@/utils/request'
import PublicTooltip from '@/components/Public/PublicTooltip'
import '../index.less'
import moment from 'moment'
import { roleJudgment } from '@/utils/authority'
import { useSelector } from 'react-redux'
import { QuestionCircleOutlined } from '@ant-design/icons'
import {fileDown} from '@/utils'
import { COLORS } from '@/constants/index'
import _ from 'lodash'
import ExtendedColumn from './ExtendedColumn'

export default (props) =>{
    const {visible,title,onCancel, userCenterType, groupStatus} = props
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [dataSource1,setDataSource1] = useState([])
    const [dataSource2,setDataSource2] = useState([])
    const [exportLoading,setExportLoading] = useState(false)
    const [total1,setTotal1] = useState(0)
    const [total2,setTotal2] = useState(0)
    const [pageSize1,setPageSize1] = useState(10)
    const [pageSize2,setPageSize2] = useState(10)
    const [current1,setCurrent1] = useState(1)
    const [current2,setCurrent2] = useState(1)
    const [visible1,setVisible1] = useState(false)
    const [recordInfo, setRecordInfo] = useState({
        fullName: ''
    })
        // 悬浮框展示
    const renderPopver = (text, record, key) => {
        const content = record.deliverChildren.length ? record.deliverChildren.map((item, index) => {
        return <div style={{display: 'flex', alignItems: 'center'}}>
            <i style={{marginRight: 5, borderRadius: '50%', backgroundColor:COLORS[index], width: 10, height: 10}}></i>
            <span>{item.deliverStatusName}：{item[key]}</span>
        </div>
        }) : null
        
        return record.deliverChildren.length > 0 ? (
        <Popover
            content={content}
            title={
            <div>
                <p>{record.belongDate}</p>
            </div>
            }
        >
            {text}
        </Popover>
        ) : text
    }
    const [columns1, setColums1] = useState([
        { title: '排名', dataIndex: 'index',fixed: 'left', width:100, render: (text, record, index) => serialNumber1(current1,pageSize1,index+1) },
        { title: '门店编码', dataIndex: 'dealerCode',fixed: 'left',width:140},
        { title: '用户中心', dataIndex: 'fullName',fixed: 'left',width:120,render:text=><PublicTooltip title={text}>{text}</PublicTooltip>},
        { title: '所属大区', dataIndex: 'bigAreaName',width:140},
        { title: '所属省区', dataIndex: 'middleAreaName',width:140},
        { title: '管家群量', dataIndex: 'groupCount',width:140,
        render: (text, record) => renderPopver(text, record, "groupCount"),
        },
        { title: 
            <div>
                已开启对话量&nbsp;
                <Tooltip title='指当天开启的但不一定当天被回复的对话轮'>
                <QuestionCircleOutlined key='1'/>
                </Tooltip>
            </div>,  dataIndex: 'roundCountSum',width:140,
        render: (text, record) => renderPopver(text, record, "roundCountSum"),
        },
        { title: 
            <div>
                响应量&nbsp;
                <Tooltip title='指被回复的对话轮数量'>
                <QuestionCircleOutlined key='2'/>
                </Tooltip>
            </div>, dataIndex: 'roundCountCompleteSum',width:140,
        render: (text, record) => renderPopver(text, record, "roundCountCompleteSum"),
        },
            { title: 
                <div>
                    当日响应量&nbsp;
                    <Tooltip title='24小时内被回复的会话轮数'>
                    <QuestionCircleOutlined key='3'/>
                    </Tooltip>
                </div>, 
             dataIndex: 'intradayCompleteCount',width:140,
        render: (text, record) => renderPopver(text, record, "intradayCompleteCount"),
        },
        { title: 
           <div>
               及时响应量&nbsp;
               <Tooltip title='10分钟内被回复的会话轮数'>
               <QuestionCircleOutlined key='4'/>
               </Tooltip>
           </div>, 
        dataIndex: 'timelyRoundCountSum',width:140,
        render: (text, record) => renderPopver(text, record, "timelyRoundCountSum"),
        },
        { title: 
            <div>
                超过10分钟响应量&nbsp;
                <Tooltip title='响应量-10分钟内被回复的会话轮数'>
                <QuestionCircleOutlined key='5'/>
                </Tooltip>
            </div>, 
        dataIndex: 'over10minRoundSum',width:170,
        render: (text, record) => renderPopver(text, record, "over10minRoundSum"),
        },
        { title: 
            <div>
                超过30分钟响应量&nbsp;
                <Tooltip title='响应量-30分钟内被回复的会话轮数'>
                <QuestionCircleOutlined key='6'/>
                </Tooltip>
            </div>, 
        dataIndex: 'over30minRoundSum',width:170,
        render: (text, record) => renderPopver(text, record, "over30minRoundSum"),
        },
        { title: 
            <div>
                超过72小时响应量&nbsp;
                <Tooltip title='响应量-72小时内被回复的会话轮数'>
                <QuestionCircleOutlined key='7'/>
                </Tooltip>
            </div>, 
        dataIndex: 'timeoutRoundCount',width:170,
        render: (text, record) => renderPopver(text, record, "timeoutRoundCount"),
        },
        { title:
            <div>
                72小时平均响应时长&nbsp;
                <Tooltip title='72小时平均响应时长=总响应时长/响应量，超过72小时的对话视为耗费72小时才响应'>
                <QuestionCircleOutlined key='8'/>
                </Tooltip>
            </div>,
         dataIndex: 'avgRoundTimeStr',width:170,
         render: (text, record) => renderPopver(text, record, "avgRoundTimeStr"),
        },
        { title:
            <div>
                当日平均响应时长&nbsp;
                <Tooltip title='当日平均响应时长=当日被回复的会话轮中总共耗费的时间/当日被回复的对话轮数'>
                <QuestionCircleOutlined key='9'/>
                </Tooltip>
            </div>,
         dataIndex: 'intradayAvgTimeCostStr',width:160,
         render: (text, record) => renderPopver(text, record, "intradayAvgTimeCostStr"),
        },
        { title: 
            <div>
                平均及时回复率&nbsp;
                <Tooltip title='当日及时回复率=当日10 分钟内被回复的会话轮数/当日开启的对话轮数'>
                <QuestionCircleOutlined key='10'/>
                </Tooltip>
            </div>,
        dataIndex: 'avgResponseRateStr',width:160,
        render: (text, record) => renderPopver(text, record, "avgResponseRateStr"),
        },
        {title: '当日平均响应时长（工作时间）',width:170, checked: false,dataIndex: 'workingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'workingTimeAvgRoundTime')},
        {title: '当日平均响应时长（非工作时间）', width:170,checked: false,dataIndex: 'nonworkingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'nonworkingTimeAvgRoundTime')},
        {title: '平均及时回复率（工作时间）',width:170, checked: false,dataIndex: 'workingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'workingTimelyReplyPercent')},
        {title: '平均及时回复率（非工作时间）',width:170,checked: false, dataIndex: 'nonworkingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'nonworkingTimelyReplyPercent')},
        { title: '历史数据', dataIndex: 'storeFullName1',width:100,fixed:'right',render:(text,record)=><a onClick={()=>{
            getDealerStatisticDetail(record)
            setRecordInfo(record)
            setVisible1(true)
        }}>详情</a>},
    ])

    const [columns2, setColums2] = useState([
        { title: '统计时间', dataIndex: 'belongDate',fixed: 'left', width:140},
        { title: '管家群量', dataIndex: 'groupCount',width:140,
        render: (text, record) => renderPopver(text, record, "groupCount"),
       
        },
        { title: '已开启对话量', dataIndex: 'roundCountSum',width:140,
        render: (text, record) => renderPopver(text, record, "roundCountSum"),
    },
        { title: '响应量', dataIndex: 'roundCountCompleteSum',width:140,
        render: (text, record) => renderPopver(text, record, "roundCountCompleteSum"),
    },
        { title: '当日响应量', dataIndex: 'intradayCompleteCount',width:140,
        render: (text, record) => renderPopver(text, record, "intradayCompleteCount"),
    },
        { title: '及时响应量', dataIndex: 'timelyRoundCountSum',width:140,
        render: (text, record) => renderPopver(text, record, "timelyRoundCountSum"),
    },
        { title: '超10分钟响应量', dataIndex: 'over10minRoundSum',width:140,
        render: (text, record) => renderPopver(text, record, "over10minRoundSum"),
    },
        { title: '超30分钟响应量', dataIndex: 'over30minRoundSum',width:140,
        render: (text, record) => renderPopver(text, record, "over30minRoundSum"),
    },
        { title: '超过72小时响应量', dataIndex: 'timeoutRoundCount',width:140,
        render: (text, record) => renderPopver(text, record, "timeoutRoundCount"),
    },
        { title: '72小时平均响应时长', dataIndex: 'avgRoundTimeStr',width:160,
        render: (text, record) => renderPopver(text, record, "avgRoundTimeStr"),
    },
        { title: '当日平均响应时长', dataIndex: 'intradayAvgTimeCostStr',width:140,
        render: (text, record) => renderPopver(text, record, "intradayAvgTimeCostStr"),
    },
        { title: '平均及时回复率', dataIndex: 'avgResponseRateStr',width:140,
        render: (text, record) => renderPopver(text, record, "avgResponseRateStr"),
    },
        { title: '当日平均响应时长', 
        children: [
            {title: '当日全国排名', dataIndex: 'rankAvgRoundTimeCountry',width:150,
        },
            {title: '当日大区排名', dataIndex: 'rankAvgRoundTimeBigArea',width:150,
        },
            {title: '当日省区排名', dataIndex: 'rankAvgRoundTimeArea',width:150,
        }
        ]},
        { title: '平均及时回复率', 
        children: [
            {title: '当日全国排名', dataIndex: 'rankResponseRateCountry',width:150,
        },
            {title: '当日大区排名', dataIndex: 'rankResponseRateBigArea',width:150,
        },
            {title: '当日省区排名', dataIndex: 'rankResponseRateArea',width:150,
        }
        ]},
        {title: '当日平均响应时长（工作时间）',width:170, checked: false,dataIndex: 'workingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'workingTimeAvgRoundTime')},
        {title: '当日平均响应时长（非工作时间）', width:170,checked: false,dataIndex: 'nonworkingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'nonworkingTimeAvgRoundTime')},
        {title: '平均及时回复率（工作时间）',width:170, checked: false,dataIndex: 'workingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'workingTimelyReplyPercent')},
        {title: '平均及时回复率（非工作时间）',width:170,checked: false, dataIndex: 'nonworkingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'nonworkingTimelyReplyPercent')},
    ])

    const serialNumber1 =(pageIndex, pageSize, index)=>{
        return (current1-1) * pageSize + index;
    }

    // 导出历史数据
    const ExportExcelDetail = () => {
        post(allUrl.WeCom.exportDealerStc, {queryType: userCenterType, dealerCode: recordInfo.dealerCode, deliverStatus: ''},{responseType: "blob"}).then(res=>{
            fileDown(res,res.fileName)
        })
    }
    const ExportExcel = () => {
        setExportLoading(true)
        post(allUrl.WeCom.exportYesterdayDealer, {deliverStatus: ''},{responseType: "blob"}).then(res=>{
            fileDown(res, res.fileName)
        })
        // post(allUrl.WeCom., {deliverStatus: ''}, {
        //     // headers:{
        //     //     'Content-Type': "application/x-www-form-urlencoded;charset=UTF-8"
        //     // },
        //     // responseType: "blob"
        // }).then(res => {
        //     if(res) {
        //         let blob = new Blob([res], {type: "application/vnd.ms-excel"});
        //         console.log('blob', blob)
        //         if (window.navigator.msSaveOrOpenBlob) {
        //           //兼容ie
        //           window.navigator.msSaveBlob(blob, '昨日用户中心响应排行榜.xlsx');
        //         } else {
        //           let downloadElement = document.createElement('a');
        //           let href = window.URL.createObjectURL(blob); //创建下载的链接
        //           downloadElement.href = href;
        //           downloadElement.download = `昨日用户中心响应排行榜(${moment().add(-1, 'days').format('YYYY-MM-DD')})` + '.xlsx'; //下载后文件名
        //           document.body.appendChild(downloadElement);
        //           downloadElement.click(); //点击下载
        //           document.body.removeChild(downloadElement); //下载完成移除元素
        //           window.URL.revokeObjectURL(href); //释放掉blob对象
        //         }
        //         message.success('下载成功！')
        //     }
            setExportLoading(false)
        // })
    }
    const getDealerStatisticDetail = (record) => {
        post(allUrl.WeCom.dealerStatisticDetail, 
            {
                queryType: userCenterType,
                dealerCode: record.dealerCode,
                deliverStatus: '',
            }).then(res => {
            if (res.success && res.resp.length) {
                setDataSource2(res.resp)
                // res.resp[0].reverse(s)
                // let obj = {
                //     names: [],
                //     values: [],
                // }
                // res.resp[0].forEach(item => {
                //     obj.names.push(item.dealerName || '')
                //     obj.values.push(item.totalNum || '')
                // })
                // setData1(obj)
            } else {
                // message.error(res.msg)
            }
        })
    }
    useEffect(()=>{
        post(allUrl.WeCom.yesterdayDealerStatistic,
            {
                queryType: 2, // 默认请求响应率数据
                deliverStatus: ''
            }).then(res=>{
            if(res.success){
                setDataSource1(res.resp)
                // setTotal1(res.resp[0].total)
            }else{
                // message.error(res.msg)
            }
        })
    },[])

    let newColumns1 = _.cloneDeep({ columns1 }).columns1
    for (let i = 0; i < newColumns1.length; i++) {
        if (JSON.stringify(newColumns1[i]['checked']) !== undefined && !newColumns1[i].checked) {
            newColumns1.splice(i, 1)
            i--
        }
    }
    const InforData1 = {
        columns:newColumns1,
        dataSource:dataSource1,
        bordered:true,
        rowKey:'id',
        size:'small',
        scroll:{y:600},
        pagination: false,
    }
    
    let newColumns2 = _.cloneDeep({ columns2 }).columns2
    for (let i = 0; i < newColumns2.length; i++) {
        if (JSON.stringify(newColumns2[i]['checked']) !== undefined && !newColumns2[i].checked) {
            newColumns2.splice(i, 1)
            i--
        }
    }
    const InforData2 = {
        columns:newColumns2,
        dataSource:dataSource2,
        bordered:true,
        rowKey:'id',
        size:'small',
        scroll:{y:600},
        pagination: false,
    }
    return <Modal title=
                {
                    <div>
                        <div>{title}<span style={{marginLeft:'20px',fontSize:'14px',color: 'rgba(0,0,0,0.45)',fontWeight: 400}}>{`统计截止${moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD 23:59:59')}`}</span>
                            <div>
                                {
                                    roleJudgment(userInfo, 'USER_CENTER_RESPONSE_RANKING_EXPORT') ?
                                    <Button type='primary' style={{marginTop: 10}} loading={exportLoading} onClick={ExportExcel}>导出</Button>
                                    :null
                                }
                                <ExtendedColumn setColums={setColums1} columns={columns1}/>
                            </div>
                        </div>
                    </div>
                } visible={visible} onCancel={onCancel} maskClosable={false} footer={null} width={1300}>
                <Table {...InforData1} />
                <Modal visible={visible1} title={`${recordInfo.fullName}`} footer={null} onCancel={()=>setVisible1(false)} width={1000}>
                    {
                        roleJudgment(userInfo, 'USER_CENTER_RESPONSE_RANKING_EXPORT') ?
                        <Button type='primary' style={{marginBottom:'20px'}} loading={exportLoading} onClick={ExportExcelDetail}>导出</Button>
                        :null
                    }
                    <Table {...InforData2} />
                </Modal>
                <Modal visible={visible1} 
                title=
                {
                    <div>
                        <div>{recordInfo.fullName}</div>
                            <div>
                                {
                                roleJudgment(userInfo, 'USER_CENTER_RESPONSE_RANKING_EXPORT') ?
                                <Button style={{marginTop: 10}} type="primary" loading={exportLoading} onClick={ExportExcelDetail}>导出</Button>: null
                                }
                                 <ExtendedColumn setColums={setColums2} columns={columns2}/>
                            </div>
                        </div>
                
                } 
               footer={null} onCancel={()=>setVisible1(false)} width={1000}>
                    <Table {...InforData2} />
                </Modal>
            </Modal>
}