// import './index.less'
import './OtherAccountManage.less'
import React, { useEffect, useState,useRef } from 'react'
import { useSelector,useDispatch } from 'react-redux'
import { Table, message, Tabs, Button, Row, Col, Popconfirm,Modal } from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import BindCEOAccount from './components/BindCEOAccount'
import BindQYWXAccount from './components/BindQYWXAccount'
import ImportResultQuery from './components/ImportResultQuery'
import UploadFile from '@/components/Public/UploadFile'
import ImportButton from './components/ImportButton'
import {getDict} from '../../../actions/async'
import baseURL from '@/baseURL'
import {roleJudgment} from '@/utils/authority'
import {utilsDict} from '@/utils/utilsDict'
const { TabPane } = Tabs;

const OtherAccountManage = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const dispatch = useDispatch()
    const tableRef1 = useRef()
    const tableRef2 = useRef()
    const [importLoading, setImportLoading] = useState({})
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const [CEOData, setCEOData] = useState([])
    const [current, setCurrent] = useState(1)
    const [pageSize, setPageSize] = useState(10)
    const [total, setTotal] = useState(0)
    const [BindCEOAccountVisible, setBindCEOAccountVisible] = useState(false)
    const [BindQYWXAccountVisible, setBindQYWXAccountVisible] = useState(false)
    const [rowData, setRowData] = useState({})
    const [tabsKey, setTabsKey] = useState('1')
    const [ImportResultQueryVisible, setImportResultQueryVisible] = useState(false)
    const [ImportIDVisible, setImportIDVisible] = useState(false)
    const [dictList,setDictList] = useState({})
    const [tableHeight,setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({
        // lastFollowTime:[moment().subtract(1,'months'),moment()],
        // createTime: [moment().subtract(1, 'months'), moment()],
    })
    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
    }
    const onSearch = (values) => {
        setDefaultQuery(values)
    }
    const bindAccount = (record) => {
        if (!record.isBind) {
            post(allUrl.Authority.unBindUser, { mobilePhone: record.mobilePhone }).then(res => {
                if (res.success) {
                    message.success(res.msg)
                    getData()
                } else {
                    // message.error(res.msg || res.msgCode)
                }
            })
        } else {
            setRowData(record)
            setBindCEOAccountVisible(true)
        }
    }
    const bindQYWXAccount = (record) => {
        if (record.qywxUserId) {
            Modal.confirm({
                title:'解绑后SERES协同账号与企微账号将不互通，是否解绑？',
                onOk:()=>{
                    post(allUrl.Authority.bindQywxUser,{userId:record.userId,qywxUserId:''}).then(res=>{
                        if(res.success){
                            message.success(res.msg || '成功')
                            getData()
                        }else{
                            // message.error(res.msg || '失败')
                        }
                    })
                },
                onCancel:()=>{
                    return
                }
            })
        } else {
            setRowData(record)
            setBindQYWXAccountVisible(true)
        }
    }
    const renderOperation1 = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                record.isBind ?
                <a style={{color:roleJudgment(userInfo,'PERM_OTHERACCOUNT_BIND_CEO_ACCOUNT') ?'#1890ff':'rgba(0,0,0,0.2)'}} onClick={() => {
                    if(roleJudgment(userInfo,'PERM_OTHERACCOUNT_BIND_CEO_ACCOUNT')) bindAccount(record)
                }}>绑定CEO账号</a>
                :roleJudgment(userInfo,'PERM_OTHERACCOUNT_UNBOUND_CEO_ACCOUNT')?
                <Popconfirm title={`${'解绑后将不再同步线索、跟进记录，是否解绑？'}`} onConfirm={() => bindAccount(record)}>
                    {
                        <span style={{ color: '#FF4D4F', cursor: 'pointer' }}>解绑CEO账号</span>
                    }
                </Popconfirm>
                :<span style={{ color:'rgba(0,0,0,0.2)', cursor: 'pointer' }}>解绑CEO账号</span>
                
            }
        </div>
    }
    const renderOperation2 = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                !record.qywxUserId ?
                <a style={{color:roleJudgment(userInfo,'PERM_OTHERACCOUNT_BIND_QYWX_ACCOUNT') ?'#1890ff':'rgba(0,0,0,0.2)'}} onClick={() => {
                    if(roleJudgment(userInfo,'PERM_OTHERACCOUNT_BIND_QYWX_ACCOUNT')) bindQYWXAccount(record)
                }}>绑定企微账号</a>
                :roleJudgment(userInfo,'PERM_OTHERACCOUNT_UNBOUND_QYWX_ACCOUNT')?
                // <Popconfirm title={`${'确定解绑企业微信账号吗？'}`} onConfirm={() => bindQYWXAccount(record)}>
                //     {
                        <span style={{ color: '#FF4D4F', cursor: 'pointer' }} onClick={()=>bindQYWXAccount(record)}>解绑企微账号</span>
                //     }
                // </Popconfirm>
                :<span style={{ color:'rgba(0,0,0,0.2)', cursor: 'pointer' }}>解绑企微账号</span>
                
            }
        </div>
    }
    const getData = () => {
        setLoading(true)
        let query = { ...defaultQuery }
        let params = { pageNum: current, pageSize, ...query }
        if(tabsKey === '1'){
            tableRef1.current.getTableData()
        }else if(tabsKey === '2'){
            tableRef2.current.getTableData()
        }
    }
    const getAllCeoCode = () => {
        get(allUrl.Authority.getAllCeoCode).then(res => {
            if (res.success) {
                if (res.resp) {
                    res.resp.map((item, index) => item.key = index + 1)
                    setCEOData(res.resp)
                }
            } else {
                // message.error(res.msg)
            }
        })
    }
    const tabsCallback = (key) => {
        console.log(key)
        setTabsKey(key)
        setCurrent(1)
        setPageSize(10)
        sessionStorage.setItem('CarrierListTabsKey', key)
    }

    const UploadChange = ({ file, fileList }, type) => {
        console.log(file, type)
        const { response } = file
        if (file.status === 'uploading') {
            setImportIDVisible(false)
            if (type === 1) {
                setImportLoading({ loading1: true })
            } else if (type === 2) {
                setImportLoading({ loading2: true })
            } else if (type === 3) {
                setImportLoading({ loading3: true })
            } else if (type === 4) {
                setImportLoading({ loading4: true })
            }
            setLoading(true)
        }
        if (file.status === 'done') {
            message.success(response.msg || response.msgCode)
            setImportIDVisible(false)
            setLoading(false)
            setImportLoading({});
            getData()
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msgCode)
            setLoading(false)
            setImportLoading({});
        }
    }
    const download = () => {
        let url = ''
        if (process.env.REACT_APP_ENV === 'dev') {
            url = 'https://scrm-dev-oss.oss-cn-shanghai.aliyuncs.com/templates/映射关系导入模板.xlsx'
        } else {
            url = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/seres-clue/templates/%E6%98%A0%E5%B0%84%E5%85%B3%E7%B3%BB%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
        }
        window.open(url)
    }
    const initPage = () =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        console.log(winH)
        let h = winH  - 47 - 54 - 468
        setTableHeight(h)
    }
    useEffect(()=>{
        initPage()
    },[tabsKey])
    useEffect(() => {
        getAllCeoCode()
    }, [])
    useEffect(() => {
        if (userInfo) {
            getData()
        }
    }, [tabsKey])
// }, [defaultQuery, userInfo, current, pageSize, tabsKey])

    useEffect(()=>{
        dispatch(getDict({codes:'scrm_position'})).then(({payload})=>{
            setDictList(payload)
        })
    },[])
    const columns1 = [
        { title: '账号ID', dataIndex: 'userId', width: 120 },
        { title: '手机号', dataIndex: 'mobilePhone', width: 100 },
        { title: '姓名', dataIndex: 'userName', width: 100 },
        { title: 'CEO账号ID', dataIndex: 'ceoUserCode', width: 140 },
        { title: '岗位', dataIndex: 'positionName', width: 160 },
        { title: '所属组织', dataIndex: 'organizationName', width: 200 },
        { title: '账号是否启用', dataIndex: 'statusName', width: 150 },
        { title: '操作', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation1(text, record) }
    ]
    const columns2 = [
        { title: '账号ID', dataIndex: 'userId', width: 120 },
        { title: '手机号', dataIndex: 'mobilePhone', width: 100 },
        { title: '姓名', dataIndex: 'userName', width: 100 },
        { title: '企微账号ID', dataIndex: 'qywxUserId', width: 140 },
        { title: '岗位', dataIndex: 'positionName', width: 200 },
        { title: '所属组织', dataIndex: 'orgName', width: 200,render:text=> text?text:''},
        { title: '账号是否启用', dataIndex: 'status', width: 150, render:text=>text?'已启用':'未启用' },
        { title: '企微激活状态', dataIndex: 'qywxStatus', width: 150,render:text=>utilsDict('qywxStatus',text) },
        { title: '操作', width: 110, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation2(text, record) }
    ]
    console.log(dictList)
    let searchList1 = [
        { label: '姓名', name: 'userName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '手机号', name: 'mobilePhone', type: 'Input', placeholder: '请输入', colSpan: 6 },
        {
            label: '账号是否启用', name: 'status', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '已启用', value: '1' },
                { name: '未启用', value: '0' },
            ]
        },
        {
            label: '岗位', name: 'position', type: 'Select', placeholder: '请选择', colSpan: 6,showSearch:true, data: dictList['scrm_position'] || []
        }
    ]
    let searchList2 = [
        { label: '姓名', name: 'userName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '手机号', name: 'mobilePhone', type: 'Input', placeholder: '请输入', colSpan: 6 },
        {
            label: '企微是否绑定', name: 'bind', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '已绑定', value: '1' },
                { name: '未绑定', value: '0' },
            ]
        },
        {
            label: '企微激活状态', name: 'qywxStatus', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '已激活', value: '1' },
                { name: '已禁用', value: '2' },
                { name: '未激活', value: '4' },
                { name: '成员退出', value: '5' },
                { name: '删除账号', value: '6' },
            ]
        },
        {
            label: '账号是否启用', name: 'status', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '已启用', value: '1' },
                { name: '未启用', value: '0' },
            ]
        }
    ]
    return (
        <div className='OtherAccountManage'>
            <Tabs onChange={tabsCallback} type="card" activeKey={tabsKey} defaultActiveKey={tabsKey} >
                <TabPane tab="CEO账号管理" key="1" forceRender>
                    <Row style={{ marginLeft: '24px' }}>
                        <Col span={24}>
                            {
                                roleJudgment(userInfo,'PERM_OTHERACCOUNT_RELATIONSHIP_IMPORT') ?
                                    <>
                                        <UploadFile
                                            style={{ display: 'inline-block' }}
                                            extension={['xls', 'xlsx']}
                                            showUploadList={false}
                                            size={5}
                                            action={baseURL.Host + allUrl.Authority.importUserRelation}
                                            UploadChange={(file) => UploadChange(file, 1)}
                                        >
                                            <Button style={{ marginRight: '20px' }} loading={importLoading?.loading1 ? true : false}>映射关系批量导入</Button>
                                        </UploadFile>
                                        <a style={{ marginRight: '30px', marginTop: '10px' }} onClick={() => download()}>模版下载</a>
                                    </>
                                    : null
                            }
                            {
                                roleJudgment(userInfo,'PERM_OTHERACCOUNT_CLUE_IMPORT') ?
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={5}
                                        action={baseURL.Host + allUrl.Authority.importClueExcel}
                                        UploadChange={(file) => UploadChange(file, 2)}
                                    >
                                        <Button loading={importLoading?.loading2 ? true : false} style={{ margin: '0 10px' }}>CEO线索批量导入</Button>
                                    </UploadFile>
                                    : null
                            }
                            {
                                roleJudgment(userInfo,'PERM_OTHERACCOUNT_PLAN_IMPORT') ?
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={5}
                                        action={baseURL.Host + allUrl.Authority.importPlanExcel}
                                        UploadChange={(file) => UploadChange(file, 4)}
                                    >
                                        <Button loading={importLoading?.loading4 ? true : false} style={{ margin: '0 10px' }}>CEO计划批量导入</Button>
                                    </UploadFile>
                                    : null
                            }
                            {
                                roleJudgment(userInfo,'PERM_OTHERACCOUNT_FOLLOWUPRECORD_IMPORT') ?
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={5}
                                        action={baseURL.Host + allUrl.Authority.importRecordExcel}
                                        UploadChange={(file) => UploadChange(file, 3)}
                                    >
                                        <Button loading={importLoading?.loading3 ? true : false} style={{ margin: '0 10px' }}>CEO跟进记录批量导入</Button>
                                    </UploadFile>
                                    : null
                            }
                        </Col>
                        {
                            roleJudgment(userInfo,'PERM_OTHERACCOUNT_IMPORT_RESULTS') ?
                                <Col span={23} style={{ marginTop: '10px' }} onClick={() => {
                                    setImportResultQueryVisible(true)
                                }}>
                                    <a>导入结果查看</a>
                                </Col> : null
                        }
                    </Row>
                    <PublicTableQuery onSearch={onSearch} searchList={searchList1} defaultQuery={defaultQuery} />
                    <div className='tableData'>
                        <PublicTable
                            url={allUrl.Authority.getCeoUserList}
                            rowSelection={false}
                            columns={columns1}
                            type={2}
                            ref={tableRef1}
                            defaultQuery={defaultQuery}
                        />
                    </div>
                </TabPane>
                <TabPane tab="企业微信账号管理" key="2" forceRender>
                    <PublicTableQuery onSearch={onSearch} searchList={searchList2} defaultQuery={defaultQuery} />
                    <div className='tableData'>
                        <PublicTable
                            url={allUrl.Authority.getQwUserList}
                            rowSelection={false}
                            columns={columns2}
                            method={'get'}
                            type={2}
                            ref={tableRef2}
                            defaultQuery={defaultQuery}
                        />
                    </div>
                </TabPane>
                {/* <TabPane tab="云课堂账号管理" key="3" forceRender>
                    
                </TabPane> */}
            </Tabs>
            {
                BindCEOAccountVisible &&
                <BindCEOAccount rowData={rowData} getData={getData} CEOData={CEOData} visible={BindCEOAccountVisible} onCancel={() => setBindCEOAccountVisible(false)} />
            }
            {
                BindQYWXAccountVisible &&
                <BindQYWXAccount rowData={rowData} getData={getData} CEOData={CEOData} visible={BindQYWXAccountVisible} onCancel={() => setBindQYWXAccountVisible(false)} />
            }
            {
                ImportResultQueryVisible &&
                <ImportResultQuery title='导入结果查看' rowData={rowData} visible={ImportResultQueryVisible} onCancel={() => setImportResultQueryVisible(false)} />
            }
        </div>
    )
}
export default OtherAccountManage