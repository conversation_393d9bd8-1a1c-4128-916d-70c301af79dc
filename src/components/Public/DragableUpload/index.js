/**
 * @param {Object} style 样式
 * @param {Array} extension 上传文件扩展名限制 （['xls','xlsx']）
 * @param {String} action 上传地址
 * @param {Object} headers 请求头
 * @param {boolean} showUploadList 是否展示上传文件列表
 * @param {Number} size 文件大小限制（单位：MB）
 * @param {Array} fileList 文件列表
 * @param {Function} setFileList 设置文件列表
 */
import React,{useEffect,useState,useCallback,useRef} from 'react'
import { Upload, Tooltip, Button, message } from 'antd'
import Cookies from 'js-cookie'
import update from 'immutability-helper';
import { CloseOutlined,PlusOutlined ,PaperClipOutlined,DeleteOutlined,DisconnectOutlined} from '@ant-design/icons';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import './index.less'
const type = 'DragableUploadList';

const DragableUploadListItem = ({ originNode, moveRow, file, fileList }) => {
    const ref = useRef(null);
    const index = fileList.indexOf(file);
    const [{ isOver, dropClassName }, drop] = useDrop({
        accept: type,
        collect: (monitor) => {
        const { index: dragIndex } = monitor.getItem() || {};
        if (dragIndex === index) {
            return {};
        }
        return {
            isOver: monitor.isOver(),
            dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
        };
        },
        drop: (item) => {
        moveRow(item.index, index);
        },
    });
    const [, drag] = useDrag({
        type,
        item: {
        index,
        },
        collect: (monitor) => ({
        isDragging: monitor.isDragging(),
        }),
    });
    drop(drag(ref));
    const errorNode = <Tooltip title="Upload Error">{originNode.props.children}</Tooltip>;
    return (
        <div
        ref={ref}
        className={`ant-upload-draggable-list-item ${isOver ? dropClassName : ''}`}
        style={{
            cursor: 'move',
        }}
        >
        {file.status === 'error' ? errorNode : originNode}
        </div>
    );
};
const UploadFile = (props) => {
    const { children, style = {}, extension = [], action, headers = {}, iconRender,showUploadList = false, size, UploadChange,type,data={} ,fileList =[], setFileList, UploadRemove} = props
    const [defaultFileList,setDefaultFileList] = useState([])
    const newHeaders = {
        ...headers,
        appid:1,
        authorization:Cookies.get('scrm_token'),
    }
    const beforeUpload = (file) => {
        const { name } = file
        const index = name.lastIndexOf(".");
        const suffix = name.substring(index + 1);
        if (extension.length) {
            if (extension.indexOf(suffix) <= -1) {
                message.error(`仅支持${extension}的文件类型！`)
                return false
            }
        }
        if (size) {
            const isLt2M = file.size / 1024 / 1024 < size;
            if (!isLt2M) {
                message.error(`文件大小不能超过${size}MB!`);
                return false
            }

        }
        return true
    }
    const handleChange = ({ file, fileList }) => {
        setDefaultFileList(fileList)
        if (UploadChange) { UploadChange({ file, fileList },type) }
    }
    const handleRemove = (file) =>{
        if (UploadRemove) { UploadRemove(file) }
    }
    const moveRow = useCallback(
        (dragIndex, hoverIndex) => {
          const dragRow = fileList[dragIndex];
          let data = update(fileList, {
            $splice: [
              [dragIndex, 1],
              [hoverIndex, 0, dragRow],
            ],
          })
          data.forEach((item,index)=>{
            if(item.originalName) item.name = `${index+1}: ${item.originalName}`
        })
          setFileList(data);
        },
        [fileList],
    )
    useEffect(()=>{
        // 每次附件发生变化重新排序，添加前端维护的首序号
         fileList.forEach((item,index)=>{
            if(item.originalName) item.name = `${index+1}: ${item.originalName}`
    })
    },[fileList])
    return <div className="dragable-upload" style={style}>
        <DndProvider backend={HTML5Backend}>
            <Upload
            action={action}
            data={data}
            headers={newHeaders}
            showUploadList={showUploadList}
            fileList={showUploadList ? fileList : defaultFileList}
            iconRender={iconRender?iconRender:<PaperClipOutlined style={{color:'rgba(0, 0, 0, 0.45)'}} />}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            onRemove={handleRemove}
            itemRender={(originNode, file, currFileList) => (
                <DragableUploadListItem
                  originNode={originNode}
                  file={file}
                  fileList={currFileList}
                  moveRow={moveRow}
                />
              )}
            >
                {children}
            </Upload>
        </DndProvider>
    </div>
}
export default UploadFile