import type { AxiosRequestConfig } from 'axios';

import fileManageService from '../services/file-manage';
/**
 * 创建目录
 */
export function postSeresFileDirectoryCreate(
  bodyParameters: FileManage.CreateDirectoryUsingPOST.BodyParameters['directoryDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataDirectoryDTO>['resp']>({
    url: '/seres/file/directory/create',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 展开目录
 */
export function postSeresFileDirectoryList(
  bodyParameters: FileManage.ListNodesUsingPOST.BodyParameters['directoryQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataNodeListVO>['resp']>({
    url: '/seres/file/directory/list',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 删除目录
 */
export function postSeresFileDirectoryRemove(
  bodyParameters: FileManage.RemoveDirectoryUsingPOST.BodyParameters['removeNodeCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/directory/remove',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 移动排序
 */
export function postSeresFileDirectorySort(
  bodyParameters: FileManage.SortUsingPOST.BodyParameters['movingDirectoryDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataDirectoryDTO>['resp']>({
    url: '/seres/file/directory/sort',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 修改目录名称
 */
export function postSeresFileDirectoryUpdate(
  bodyParameters: FileManage.UpdateDirectoryNameUsingPOST.BodyParameters['updateNodeCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/directory/update',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 查看访问许可配置
 */
export function getSeresFileItemAccessConfig(
  queryParameters: FileManage.QueryAccessConfigUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataFileItemAccessConfigVO>['resp']>({
    url: '/seres/file/item/access/config',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 编辑访问许可配置
 */
export function postSeresFileItemAccessConfig(
  bodyParameters: FileManage.AccessConfigUsingPOST.BodyParameters['fileItemAccessConfigDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataLong>['resp']>({
    url: '/seres/file/item/access/config',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 文件操作：作废
 */
export function postSeresFileItemCancel(
  bodyParameters: FileManage.CancelUsingPOST.BodyParameters['updateFileItemStateCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/cancel',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 文件操作：下发
 */
export function postSeresFileItemDeliver(
  bodyParameters: FileManage.DeliverUsingPOST.BodyParameters['updateFileItemStateCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/deliver',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 详情信息
 */
export function getSeresFileItemDetail(
  queryParameters: FileManage.DetailFileItemUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataFileItemDetailVO>['resp']>({
    url: '/seres/file/item/detail',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 编辑信息
 */
export function getSeresFileItemEditDetail(
  queryParameters: FileManage.GetFileItemEditDetailUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataFileItemEditVO>['resp']>({
    url: '/seres/file/item/editDetail',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 设置过期时间
 */
export function postSeresFileItemExpire(
  bodyParameters: FileManage.ExpireUsingPOST.BodyParameters['updateExpireTimeCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/expire',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 搜索查询
 */
export function postSeresFileItemList(
  bodyParameters: FileManage.ListFileItemsUsingPOST.BodyParameters['fileItemQueryDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOFileItemListVO>['resp']>({
    url: '/seres/file/item/list',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 移动文件
 */
export function postSeresFileItemMove(
  bodyParameters: FileManage.MoveDirUsingPOST.BodyParameters['moveDIrCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/move',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 设置下发消息推送
 */
export function postSeresFileItemNotice(
  bodyParameters: FileManage.SetMsgSendUsingPOST.BodyParameters['switchCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/notice',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 在线文件搜索
 */
export function getSeresFileItemOnline(
  queryParameters: FileManage.SearchOnlineFileItemsUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataLinkFileVO>['resp']>({
    url: '/seres/file/item/online',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 指定的下发范围
 */
export function postSeresFileItemOrganizationList(
  bodyParameters: FileManage.ListOrganizationUsingPOST.BodyParameters['fileOrganizationRelationQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOOrganization>['resp']>({
    url: '/seres/file/item/organization/list',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 设置文件下载权限
 */
export function postSeresFileItemPermission(
  bodyParameters: FileManage.SetPermissionUsingPOST.BodyParameters['updatePermissionCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/permission',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 指定的岗位
 */
export function postSeresFileItemPostList(
  bodyParameters: FileManage.ListPostUsingPOST.BodyParameters['filePostRelationQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOPost>['resp']>({
    url: '/seres/file/item/post/list',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 保存文件
 */
export function postSeresFileItemSave(
  bodyParameters: FileManage.SaveFileItemUsingPOST.BodyParameters['fileItemEditDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataLong>['resp']>({
    url: '/seres/file/item/save',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 获取总部人员下发范围
 */
export function getSeresFileItemSeresUserQuery(
  queryParameters: FileManage.QuerySeresUserUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataFileSeresUserVO>['resp']>({
    url: '/seres/file/item/seresUser/query',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 保存总部人员下发范围
 */
export function postSeresFileItemSeresUserSave(
  bodyParameters: FileManage.SaveSeresUserUsingPOST.BodyParameters['fileSeresUserDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/seresUser/save',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 变更文件状态（下发、作废、撤回）
 */
export function postSeresFileItemStatusUpdate(
  bodyParameters: FileManage.UpdateFileStatusUsingPOST.BodyParameters['updateFileItemStateCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/status/update',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 文件操作：撤回
 */
export function postSeresFileItemWithdraw(
  bodyParameters: FileManage.WithdrawUsingPOST.BodyParameters['updateFileItemStateCommand'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataObject>['resp']>({
    url: '/seres/file/item/withdraw',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 附件文件批量上传
 */
export function postSeresFileMultiUpload(
  queryParameters: FileManage.UploadFilesUsingPOST.QueryParameters,
  formDataParameters: FormData,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataOSSRecordVO>['resp']>({
    url: '/seres/file/multi/upload',
    method: 'post',
    data: formDataParameters,
    params: queryParameters,
    ...config,
  });
}
/**
 * 厂端预览的url
 */
export function getSeresFileOperateGenerateUrl(
  queryParameters: FileManage.GenerateLinkUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataString>['resp']>({
    url: '/seres/file/operate/generateURL',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 用户未下载、未预览人数分页查询
 */
export function postSeresFileOperateInnerOptDetailQuery(
  bodyParameters: FileManage.InnerOptDetailQueryUsingPOST.BodyParameters['detailPageQry'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOSeresDetailPageRespDTO>['resp']>({
    url: '/seres/file/operate/innerOptDetailQuery',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 内部用户数据汇总
 */
export function getSeresFileOperateInnerUserSummary(
  queryParameters: FileManage.InnerUserSummaryUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataFileSummaryDTO>['resp']>({
    url: '/seres/file/operate/innerUserSummary',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 文件识别
 */
export function postSeresFileOperateRecognition(formDataParameters: FormData, config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataFileRecognitionDTO>['resp']>({
    url: '/seres/file/operate/recognition',
    method: 'post',
    data: formDataParameters,
    ...config,
  });
}
/**
 * 汇总数据导出
 */
export function getSeresFileOperateSeresSummaryExport(
  queryParameters: FileManage.SeresSummaryExportUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<void>({
    url: '/seres/file/operate/seresSummaryExport',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 内部用户汇总数据详情导出
 */
export function getSeresFileOperateSeresUserDetailExport(
  queryParameters: FileManage.SeresUserDetailExportUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<void>({
    url: '/seres/file/operate/seresUserDetailExport',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 门店已预览、已下载导出接口
 */
export function postSeresFileOperateStorePreviewDownloadExport(
  bodyParameters: FileManage.StorePreviewDownloadExportUsingPOST.BodyParameters['query'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<void>({
    url: '/seres/file/operate/storePreviewDownloadExport',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 门店已预览、已下载查询接口
 */
export function postSeresFileOperateStorePreviewDownloadQuery(
  bodyParameters: FileManage.StorePreviewDownLoadQueryUsingPOST.BodyParameters['query'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOPreviewDownloadDTO>['resp']>({
    url: '/seres/file/operate/storePreviewDownloadQuery',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 供应商已预览、已下载导出接口
 */
export function postSeresFileOperateSupplierPreviewDownloadExport(
  bodyParameters: FileManage.SupplierPreviewDownloadExportUsingPOST.BodyParameters['query'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<void>({
    url: '/seres/file/operate/supplierPreviewDownloadExport',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 供应商已预览、已下载查询接口
 */
export function postSeresFileOperateSupplierPreviewDownloadQuery(
  bodyParameters: FileManage.SupplierPreviewDownLoadQueryUsingPOST.BodyParameters['query'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOPreviewDownloadDTO>['resp']>({
    url: '/seres/file/operate/supplierPreviewDownloadQuery',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 删除总部人员部门
 */
export function getSeresFileOrganizationSeresDelete(
  queryParameters: FileManage.DeleteSeresOrganizationUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/organization/seres/delete',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 获取总部人员部门列表-全量
 */
export function getSeresFileOrganizationSeresList(config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataSeresOrganization>['resp']>({
    url: '/seres/file/organization/seres/list',
    method: 'get',
    ...config,
  });
}
/**
 * 获取总部人员部门列表-分页
 */
export function postSeresFileOrganizationSeresPage(
  bodyParameters: FileManage.ListSeresOrganizationByPageUsingPOST.BodyParameters['pageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOSeresOrganization>['resp']>({
    url: '/seres/file/organization/seres/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 保存总部人员部门
 */
export function postSeresFileOrganizationSeresSave(
  bodyParameters: FileManage.SaveSeresOrganizationUsingPOST.BodyParameters['seresOrganization'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataSeresOrganization>['resp']>({
    url: '/seres/file/organization/seres/save',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 门店树
 */
export function getSeresFileOrganizationStoreList(config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataStoreOrganizationVO>['resp']>({
    url: '/seres/file/organization/store/list',
    method: 'get',
    ...config,
  });
}
/**
 * 门店列表-分页
 */
export function postSeresFileOrganizationStorePage(
  queryParameters: FileManage.ListStoreOrganizationByPageUsingPOST.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOOrganization>['resp']>({
    url: '/seres/file/organization/store/page',
    method: 'post',
    params: queryParameters,
    ...config,
  });
}
/**
 * 获取供应商列表-全量
 */
export function getSeresFileOrganizationSupplierList(config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataSupplierOrganization>['resp']>({
    url: '/seres/file/organization/supplier/list',
    method: 'get',
    ...config,
  });
}
/**
 * 获取供应商列表-分页
 */
export function postSeresFileOrganizationSupplierPage(
  bodyParameters: FileManage.ListSupplierOrganizationByPageUsingPOST.BodyParameters['pageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOOrganization>['resp']>({
    url: '/seres/file/organization/supplier/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 删除常用岗位
 */
export function getSeresFilePostFrequentDelete(
  queryParameters: FileManage.DeleteFrequentPostUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/post/frequent/delete',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 常用岗位列表-全量
 */
export function getSeresFilePostFrequentList(config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataFrequentPostVO>['resp']>({
    url: '/seres/file/post/frequent/list',
    method: 'get',
    ...config,
  });
}
/**
 * 常用岗位列表-分页
 */
export function postSeresFilePostFrequentPage(
  bodyParameters: FileManage.ListFrequentPostsUsingPOST.BodyParameters['pageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOFrequentPost>['resp']>({
    url: '/seres/file/post/frequent/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 保存常用岗位
 */
export function postSeresFilePostFrequentSave(
  bodyParameters: FileManage.SaveFrequentPostUsingPOST.BodyParameters['frequentPostDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataLong>['resp']>({
    url: '/seres/file/post/frequent/save',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 删除总部人员岗位
 */
export function getSeresFilePostSeresDelete(
  queryParameters: FileManage.DeleteSeresPostUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/post/seres/delete',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 总部人员岗位列表-全量
 */
export function getSeresFilePostSeresList(config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataSeresPost>['resp']>({
    url: '/seres/file/post/seres/list',
    method: 'get',
    ...config,
  });
}
/**
 * 总部人员岗位列表-分页
 */
export function postSeresFilePostSeresPage(
  bodyParameters: FileManage.ListSeresPostByPageUsingPOST.BodyParameters['pageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOSeresPost>['resp']>({
    url: '/seres/file/post/seres/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 保存总部人员岗位信息
 */
export function postSeresFilePostSeresSave(
  bodyParameters: FileManage.SaveSeresPostUsingPOST.BodyParameters['SeresPost'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataSeresPost>['resp']>({
    url: '/seres/file/post/seres/save',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 门店岗位列表-全量
 */
export function getSeresFilePostStoreList(config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataStorePost>['resp']>({
    url: '/seres/file/post/store/list',
    method: 'get',
    ...config,
  });
}
/**
 * 门店岗位列表-分页
 */
export function postSeresFilePostStorePage(
  bodyParameters: FileManage.ListStorePostByPageUsingPOST.BodyParameters['pageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOPost>['resp']>({
    url: '/seres/file/post/store/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 供应商岗位列表-全量
 */
export function getSeresFilePostSupplierList(config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseDataSupplierPost>['resp']>({
    url: '/seres/file/post/supplier/list',
    method: 'get',
    ...config,
  });
}
/**
 * 供应商岗位列表-分页
 */
export function postSeresFilePostSupplierPage(
  bodyParameters: FileManage.ListSupplierPostByPageUsingPOST.BodyParameters['pageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOPost>['resp']>({
    url: '/seres/file/post/supplier/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 未浏览、未下载人员明细-导出
 */
export function postSeresFileStatisticInCompleteListExport(
  bodyParameters: FileManage.ExportInCompleteListUsingPOST.BodyParameters['storeDetail'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<void>({
    url: '/seres/file/statistic/inComplete/list/export',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * (门店)未浏览、未下载人员明细-查看
 */
export function postSeresFileStatisticStoreInCompleteList(
  bodyParameters: FileManage.StoreInCompleteListUsingPOST.BodyParameters['storeDetail'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOStoreDetailPageRespDTO>['resp']>({
    url: '/seres/file/statistic/store/inComplete/list',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 汇总数据-查看
 */
export function getSeresFileStatisticSummary(
  queryParameters: FileManage.StatisticSummaryUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataFileSummaryDTO>['resp']>({
    url: '/seres/file/statistic/summary',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 明细数据-查看
 */
export function postSeresFileStatisticSummaryDetail(
  bodyParameters: FileManage.StatisticDetailUsingPOST.BodyParameters['queryDTO'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOFileOptSummary>['resp']>({
    url: '/seres/file/statistic/summary/detail',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 明细数据-导出
 */
export function postSeresFileStatisticSummaryDetailExport(
  bodyParameters: FileManage.ExportStatisticDetailUsingPOST.BodyParameters['detailExport'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<void>({
    url: '/seres/file/statistic/summary/detail/export',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 汇总数据-导出
 */
export function getSeresFileStatisticSummaryExport(
  queryParameters: FileManage.ExportStatisticUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<void>({
    url: '/seres/file/statistic/summary/export',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * (供应商)未浏览、未下载人员明细-查看
 */
export function postSeresFileStatisticSupplierInCompleteList(
  bodyParameters: FileManage.SupplierInCompleteListUsingPOST.BodyParameters['supplierDetail'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOSupplierDetailPageRespDTO>['resp']>({
    url: '/seres/file/statistic/supplier/inComplete/list',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 单个在线文件上传
 */
export function postSeresFileUpload(
  queryParameters: FileManage.UploadUsingPOST.QueryParameters,
  formDataParameters: FormData,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataOSSRecordVO>['resp']>({
    url: '/seres/file/upload',
    method: 'post',
    data: formDataParameters,
    params: queryParameters,
    ...config,
  });
}
/**
 * 合作员工信息列表-分页
 */
export function postSeresFileUserPartnerPage(
  bodyParameters: FileManage.ListPartnerUserByPageUsingPOST.BodyParameters['userPageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOSupplierUserVO>['resp']>({
    url: '/seres/file/user/partner/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 统计总部人员部门下的人数
 */
export function getSeresFileUserSeresCountOrg(
  queryParameters: FileManage.CountSeresOrganizationRelationUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataInt>['resp']>({
    url: '/seres/file/user/seres/count/org',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 统计总部人员岗位下的人数
 */
export function getSeresFileUserSeresCountPost(
  queryParameters: FileManage.CountSeresPostRelationUsingGET.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataInt>['resp']>({
    url: '/seres/file/user/seres/count/post',
    method: 'get',
    params: queryParameters,
    ...config,
  });
}
/**
 * 删除总部人员信息
 */
export function postSeresFileUserSeresDelete(
  queryParameters: FileManage.DeleteSeresUserUsingPOST.QueryParameters,
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/user/seres/delete',
    method: 'post',
    params: queryParameters,
    ...config,
  });
}
/**
 * 导入总部人员信息
 */
export function postSeresFileUserSeresImport(formDataParameters: FormData, config?: AxiosRequestConfig) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/user/seres/import',
    method: 'post',
    data: formDataParameters,
    ...config,
  });
}
/**
 * 总部人员信息列表-分页
 */
export function postSeresFileUserSeresPage(
  bodyParameters: FileManage.ListSeresUserByPageUsingPOST.BodyParameters['userPageQuery'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseDataPageRespDTOSeresUserDTO>['resp']>({
    url: '/seres/file/user/seres/page',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
/**
 * 保存总部人员信息
 */
export function postSeresFileUserSeresSave(
  bodyParameters: FileManage.SaveSeresUserUsingPOST1.BodyParameters['seresUser'],
  config?: AxiosRequestConfig
) {
  return fileManageService.request<Required<FileManage.ResponseData>['resp']>({
    url: '/seres/file/user/seres/save',
    method: 'post',
    data: bodyParameters,
    ...config,
  });
}
