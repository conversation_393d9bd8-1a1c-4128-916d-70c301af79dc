import React from 'react'
import { Tree, message, Table, Row, Col, But<PERSON>, Popconfirm, Input, Divider, Modal,Spin } from 'antd'
import { PlusOutlined, EditFilled, RetweetOutlined, SyncOutlined } from '@ant-design/icons';
import { connect } from 'react-redux'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import { get, post } from '../../../utils/request'
import allUrl from '../../../utils/url'
import OrgEdit from './Modal/OrgEdit'
import OrgAdd from './Modal/OrgAdd'
import OrgTransfer from './Modal/OrgTransfer'
import PublicTooltip from '@/components/Public/PublicTooltip'
import './index.less'
import '../index.less'
import {roleJudgment} from '@/utils/authority'
import {trim} from '@/utils'
import {utilsDict} from '@/utils/utilsDict'
import Detail from '../UserManagement/Modal/Detail'

const { Search } = Input
class OrgManagement extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            TabHeight: 0,
            dataSource: [],
            rowData: {},
            pageSize: 10,
            total: 0,
            selectedRowKeys: [],
            selectedRows: [],
            current: 1,
            EditVisible: false,
            OrgTransferVisible: false,
            treeData: [

            ],
            treeselectedKeys: [],
            treeSelectedRows: [],
            treeLoadedKeys:[],
            expandedKeys:[],
            orgLoading:false,
            userLoading:false,
            detailVisible:false
        }
        this.MenuID = ''
    }
    componentDidMount() {
        this.InitPage()
        this.GetData()
    }
    componentWillUnmount() {
        this.setState = () => {
            return;
        };
    }
    GetData = () => {
        this.setState({orgLoading:true},()=>{
            post(allUrl.Authority.orgPageList).then(res => {
                if (res.success) {
                    this.setState({ treeData: res.resp, treeselectedKeys: [], treeSelectedRows: [] })
                    this.getOrgUser(1)
                } else {
                    // message.error(res.msg)
                }
                this.setState({orgLoading:false})
            })
        })
    }
    getOrgUser = (organizationId) => {
        const { pageSize, current } = this.state
        const { userInfo } = this.props
        if (organizationId) {
            if (roleJudgment(userInfo,'PERM_USER_VIEW')) {
                this.setState({userLoading:true},()=>{
                    post(allUrl.Authority.userList, { params: { organizationId }, pageSize, current }).then(res => {
                        if (res.success) {
                            this.setState({
                                dataSource: res.resp.length ? res.resp[0].list : [],
                                total: res.resp.length ? res.resp[0].pagination.total : 0
                            }, () => {
                                let arr = []
                                res.resp[0].list.forEach(item => {
                                    arr.push(item.id)
                                })
                                this.getQYWXInfo(arr)
                            })
                        } else {
                            // message.error(res.msg)
                        }
                        this.setState({userLoading:false})
                    })
                })
            }
        }
    }
    getQYWXInfo = (arr) => {
        get(allUrl.Authority.getUserInfo, { userIds: arr.join(',') }).then(res => {
            if (res && res.success) {
                let dataSource = [...this.state.dataSource]
                let Dt = res.resp[0]
                dataSource.forEach(item => {
                    for (let i in Dt) {
                        if (item.id === Number(i)) {
                            item.qywxUserId = Dt[i]?.qywxUserId
                            item.partTimePosition = Dt[i]?.partTimePosition
                            item.partTimePositionName = Dt[i]?.partTimePositionName
                            item.qywxStatus = Dt[i]?.qywxStatus
                        }
                    }
                })
                this.setState({
                    dataSource
                })
            } else {
                // message.error(res.msg)
            }
        })
    }
    InitPage = () => {
        let WinHeight = document.documentElement.clientHeight;
        this.setState({ TabHeight: WinHeight - 200 });
    }
    PageChange = (current, pageSize) => {
        const { treeselectedKeys } = this.state
        this.setState({
            current: current, pageSize
        }, () => {
            this.getOrgUser(treeselectedKeys[0])
        });
    }

    clearTreeStates = () => {
        this.setState({ treeselectedKeys: [], treeSelectedRows: [] })
    }

    renderOperation = (text, record) => {
        const { userInfo } = this.props
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'PERM_ORGANIZATION_ACCOUNT_EDIT') ?
                    [
                        <span key={1} style={{ cursor: 'pointer' }} onClick={() => this.RowUserEdit(record)}>编辑</span>,
                        <Divider key={2} type="vertical" />
                    ] : null
            }
            {
                roleJudgment(userInfo,'PERM_ORGANIZATION_ACCOUNT_DETAIL') ?
                    [
                        <span key={3} style={{ cursor: 'pointer' }} onClick={() => this.RowUserDetail(record)}>详情</span>,
                        <Divider key={4} type="vertical" />
                    ] : null
            }
            {
                roleJudgment(userInfo,'PERM_ORGANIZATION_ACCOUNT_DEL') ?
                    <Popconfirm onConfirm={() => this.RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                        <span style={{ cursor: 'pointer' }}>删除</span>
                    </Popconfirm> : null
            }
        </div>
    }
    RowOrgAdd = () => {
        this.setState({ OrgAddVisible: true })
    }
    RowOrgEdit = () => {
        // const {treeSelectedRows} = this.state
        // get(allUrl.Authority.isStore,{organizationId:treeSelectedRows[0].key}).then(res=>{
        //     if(res.success){
        //         let Dt = res.resp[0]
        //         if(Dt.result){
        //             Modal.warning({
        //                 title:'提示！',
        //                 content:'你所选的组织为门店，暂不支持编辑操作！',
        //                 onOk:()=>{
                            
        //                 },
        //                 onCancel:()=>{
        //                     return false
        //                 }
        //             })
        //         }else{
        //         }
        //     }else{
        //         message.error(res.msg)
        //     }
        // })
        this.setState({ visible: true })
    }
    RowOrgTransfer = () => {
        // const {treeSelectedRows} = this.state
        // get(allUrl.Authority.isStore,{organizationId:treeSelectedRows[0].key}).then(res=>{
        //     if(res.success){
        //         let Dt = res.resp[0]
        //         if(Dt.result){
        //             Modal.warning({
        //                 title:'提示！',
        //                 content:'你所选的组织为门店，请在门店管理修改挂靠的组织。',
        //                 onOk:()=>{
                            
        //                 },
        //                 onCancel:()=>{
        //                     return false
        //                 }
        //             })
        //         }else{
        //         }
        //     }else{
        //         message.error(res.msg)
        //     }
        // })
        this.setState({ OrgTransferVisible: true })
    }
    RowUserEdit = (record) => {
        let data = {
            id: record.id,
            Type: 'Edit',
            title: '编辑账号'
        }
        UniversalOpenWindow({
            JumpUrl: '/AuthorityManagement/UserManagementDetail', data, history: this.props.history
        })
    }
    RowUserDetail = (record) => {
        this.setState({
            rowData:record,
            detailVisible:true
        })
    }
    RowDel = (record) => {
        const { treeselectedKeys } = this.state
        post(allUrl.Authority.userDelete + '/' + record.id).then(res => {
            if (res.success) {
                message.success(res.msg || '删除用户成功！')
                this.getOrgUser(treeselectedKeys[0])
            } else {
                // message.error(res.msg || '删除用户失败！')
            }
        })
    }
    handleModal = (visible) => {
        this.setState({ visible })
    }
    onSearch = (value) => {
        value = trim(value)
        post(allUrl.Authority.orgPageList, { name: value }).then(res => {
            if (res.success) {
                this.setState({
                    treeData: res.resp
                })
            } else {
                // message.error(res.msg)
            }
        })
    }
    updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }

            if (node.children) {
                return { ...node, children: this.updateTreeData(node.children, key, children) };
            }

            return node;
        });
    }
    treeSelect = (selectedKeys, selectedNodes, event) => {
        console.log(selectedKeys, selectedNodes)
        this.setState({ treeselectedKeys: selectedKeys, treeSelectedRows: selectedNodes.selectedNodes }, () => {
            this.getOrgUser(selectedKeys[0])
        })
    }
    onExpand = (expandedKeys, {expanded, node})=>{
        console.log(expandedKeys,expanded, node)
        this.setState({expandedKeys})
    }
    OrgTransferCB = () => {
        this.setState({ treeData: [], treeSelectedRows: [], treeselectedKeys: [] ,treeLoadedKeys:[1]}, () => {
            this.GetData()
        })
    }
    Synchronization = () => {
        Modal.confirm({
            title: '确定同步到企业微信吗？',
            onOk: () => {
                get(allUrl.Authority.syncOrg).then(res => {
                    if (res.success) {
                        message.success(res.msg)
                    } else {
                        // message.error(res.msg)
                    }
                })
            },
            onCancel: () => {
                return false
            }
        })
    }
    render() {
        const { treeselectedKeys,expandedKeys,detailVisible,rowData, OrgAddVisible,orgLoading,userLoading, treeSelectedRows, current, dataSource, TabHeight,treeLoadedKeys, pageSize, treeData, total, visible, OrgTransferVisible } = this.state
        const { userInfo } = this.props
        const InforData = {
            rowKey: record => record.id,
            rowExpandable: false,
            bordered: true,
            dataSource,
            loading:userLoading,
            scroll: { x: 970, y: TabHeight },
            columns: [
                { title: '账号ID', dataIndex: 'id', width: 80 },
                { title: '姓名', dataIndex: 'userName', width: 130 },
                { title: '手机号', dataIndex: 'mobilePhone', width: 130 },
                { title: '角色', dataIndex: 'rolesNameDescription', width: 160,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
                { title: '岗位', dataIndex: 'positionName', width: 120},
                { title: '兼职岗位', dataIndex: 'partTimePositionName', width: 120},
                { title: '是否在职', dataIndex: 'isOnJob', width: 100, render: text => text == 1 ? '是' : '否' },
                // { title: '所属组织', dataIndex: 'organizationsDescription', width: 200 },
                { title: '企微激活状态', dataIndex: 'qywxStatus', width: 120, render: text => utilsDict('qywxStatus',text) },
                { title: '是否启用', dataIndex: 'status', width: 100, render: text => text ? '是' : '否' },
                { title: '操作', width: 160, fixed: 'right', dataIndex: 'Operation', render: (text, record) => this.renderOperation(text, record) }
            ],
            // rowSelection : {
            //     selectedRowKeys,selectedRows,
            //     onChange: (selectedRowKeys,selectedRows)=>{this.setState({selectedRowKeys,selectedRows})},
            // },
            pagination: {
                pageSize: pageSize,
                onChange: this.PageChange,
                current: current,
                total: total,
                showTotal: () => `共${total}条，${pageSize}条/页`,
                showSizeChanger: true,
                showQuickJumper: true,
                onShowSizeChange: this.PageChange,
            }

        };
        return (
            <div className='OrgManagement PublicList'>
                <Row className='queryArea'>
                    {
                        roleJudgment(userInfo,'PERM_ORGANIZATION_ADD') ?
                            <Col style={{ marginRight: '16px' }}><Button onClick={() => this.RowOrgAdd()} icon={<PlusOutlined />}>添加组织</Button></Col>
                            : null
                    }
                    {
                        roleJudgment(userInfo,'PERM_ORGANIZATION_EDIT') ?
                            <Col style={{ marginRight: '16px' }}><Button onClick={() => this.RowOrgEdit()} icon={<EditFilled />} disabled={!treeselectedKeys.length}>编辑组织</Button></Col>
                            : null
                    }
                    {
                        roleJudgment(userInfo,'PERM_ORGANIZATION_TRANSFER') ?
                            <Col style={{ marginRight: '16px' }}><Button onClick={() => this.RowOrgTransfer()} icon={<RetweetOutlined />} disabled={!treeselectedKeys.length}>转移组织</Button></Col>
                            : null
                    }
                    <Col><Search placeholder="搜组织关键字" allowClear onSearch={this.onSearch} /></Col>
                </Row>
                <div className='OrgManagement_Con'>
                    <Row className='OrgManagement_ConBox'>
                                <Col span={8} className='OrgManagementLeft'>
                            <Spin spinning={orgLoading}>
                                    {
                                        treeData && treeData.length ?
                                            <Tree
                                                defaultExpandedKeys={[1]}
                                                defaultSelectedKeys={[1]}
                                                treeData={treeData}
                                                onSelect={this.treeSelect}
                                                selectedKeys={treeselectedKeys}
                                                expandedKeys={expandedKeys}
                                                onExpand={this.onExpand}
                                                loading={orgLoading}
                                            />
                                            : null
                                    }
                                    {
                                        roleJudgment(userInfo,'SYNCHRONOUS_SYNCHRONOUS_WECHAT') ?
                                            <div className='synchronization' onClick={() => this.Synchronization()}>
                                                <a><SyncOutlined /><span style={{ marginLeft: '4px' }}>同步到企微</span></a>
                                            </div> : null
                                    }
                            </Spin>
                                </Col>
                        <Col span={16} className='OrgManagementRight'>
                            <Row className='tableTiele' style={{ marginLeft: '32px' }}>组织成员</Row>
                            <Table {...InforData} />
                        </Col>
                    </Row>

                </div>
                {
                    visible &&
                    <OrgEdit
                        visible={visible}
                        treeSelectedRows={treeSelectedRows}
                        onCancel={() => this.handleModal(false)}
                        userInfo={userInfo}
                        title={'编辑组织'}
                        GetData={this.GetData}
                        clearTreeStates={this.clearTreeStates}
                        treeLoadedKeys={this.state.treeLoadedKeys}
                        cb={this.OrgTransferCB}
                    />
                }
                {
                    OrgAddVisible &&
                    <OrgAdd
                        visible={OrgAddVisible}
                        title='添加组织'
                        GetData={this.GetData}
                        onCancel={() => this.setState({ OrgAddVisible: false })}
                        onOk={() => this.setState({ OrgAddVisible: false })}
                        cb={this.OrgTransferCB}
                    />
                }
                {
                    OrgTransferVisible &&
                    <OrgTransfer
                        visible={OrgTransferVisible}
                        title='转移组织'
                        onCancel={() => this.setState({ OrgTransferVisible: false })}
                        cb={this.OrgTransferCB}
                        parentSelectRows={treeSelectedRows}
                        GetData={this.GetData}
                    />
                }
                {
                    detailVisible &&
                    <Detail visible={detailVisible} rowData={rowData} title='账号详情' onCancel={()=>this.setState({detailVisible:false})} />
                }
            </div>
        )
    }
}
export default connect(({ common }) => {
    const { MenuList, userInfo } = common
    return { MenuList, userInfo }
})(OrgManagement)