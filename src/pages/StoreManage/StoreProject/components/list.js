import '../index.less'
import React, { useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Row, Col, Button, Divider, Spin, Progress} from 'antd'
import PublicTable from '@/components/Public/PublicTable'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'

import { roleJudgment } from '@/utils/authority'
const List = (props) => {
    const { url ,pageType } = props
    const tableRef = useRef()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [defaultQuery, setDefaultQuery] = useState({
    })
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [selectedRows, setSelectedRows] = useState([])
    const [tableHeight, setTableHeight] = useState(0)
    const columns = [
        { title: '序号', dataIndex: 'index', width: 50,render:(text,record,index)=>index+1 },
        { title: '门店编号', dataIndex: 'dealerCode', width: 120 },
        // { title: '门店简称', dataIndex: 'name', width: 200, fixed: 'left' },
        { title: '门店名称', dataIndex: 'name', width: 260 },
        { title: '所属大区', dataIndex: 'bigAreaName', width: 140 },
        { title: '所属区域', dataIndex: 'middleAreaName', width: 200 },
        { title: '项目状态', dataIndex: 'statusName', width: 140 },
        { title: '装修进度', dataIndex: 'decorateProgress', width: 140, render:(text,record) => {
            return <Progress percent={record.decorateProgress || 0} size="small" />
        }},
        { title: '软硬件/人员配备进度', dataIndex: 'configProgress', width: 110, render:(text,record) => {
            return <Progress percent={record.configProgress || 0} size="small" />
        }},
        { title: '项目经理', dataIndex: 'managerUserName', width: 100 },
        { title: '操作', width:pageType === 'projectManage' ?110:80, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
    ]

    if( pageType === 'projectManage'){
        // columns.splice(6,1)
    }

    
    const onSearch = (values) => {
        setDefaultQuery(values)
        rowSelectionChange({ selectedRowKeys: [], selectedRows: [] })
    }
    const RowEdit = (record) => {
        let data = {
            dealerCode: record.dealerCode,
            id: record.id,
            Type: 'EDIT',
            title:`AITO授权用户中心-${record.name}(${record.dealerCode})`
        }
        UniversalOpenWindow({
            JumpUrl:'/StoreManage/StoreProject1/edit',data,history:props.history
        })
    }
    const RowView = (record) => {
        let data = {
            dealerCode: record.dealerCode,
            id: record.id,
            Type: 'LookAt',
            title:`AITO授权用户中心-${record.name}(${record.dealerCode})`
        }
        UniversalOpenWindow({
            JumpUrl:'/StoreManage/StoreProject1/edit',data,history:props.history
        })
    }
    const progressEditing = (record) => {
        let data = {
            dealerCode: record.dealerCode,
            storeName: record.name,
            Type: 'EDIT',
            title:`AITO授权用户中心-${record.name}(${record.dealerCode})`
        }
        UniversalOpenWindow({
            JumpUrl:'/StoreManage/StoreProject2/progressEdit',data,history:props.history
        })
    }
    const trainStandardSettings = () => {
        let data = {
            Type: 'EDIT',
            title:`人员培训情况-验收标准设置`
        }
        UniversalOpenWindow({
            JumpUrl:'/StoreManage/StoreProject2/trainStandardSettings',data,history:props.history
        })
    }
    const projectManagerSettings = () => {
        let data = {
            Type: 'EDIT',
            title:`项目经理与门店绑定`
        }
        UniversalOpenWindow({
            JumpUrl:'/StoreManage/StoreProject2/projectManagerSettings',data,history:props.history
        })
    }
    const RowDetail = (record) => {
        let data = {
            dealerCode: record.dealerCode,
            Type: 'LookAt',
            title:`AITO授权用户中心-${record.name}(${record.dealerCode})`
        }
        UniversalOpenWindow({
            JumpUrl:'/StoreManage/StoreProject2/progressEdit',data,history:props.history
        })
    }

    const rowSelectionChange = ({ selectedRowKeys, selectedRows }) => {
        setSelectedRowKeys(selectedRowKeys)
        setSelectedRows(selectedRows)
    }

    const renderOperation = (text, record) => {
        console.log(pageType)
        return pageType === 'projectManage' ? 
        <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo, 'PERM_STORE_PROJECT_STATUS_EDITING') ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowEdit(record)}>编辑</span>,
                        <Divider key={1} type="vertical" />,
                    ]
                    : null
            }
            {
                roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROGRESS_VIEW') ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowDetail(record)}>进度查看</span>,
                    ] : null
            }
        </div>
            : <>
            <div style={{ color: '#1890ff',textAlign:'center' }}>
                {
                    // roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROGRESS_EDITING') && record.statusName ?
                    roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROGRESS_EDITING')?
                        <div>
                            <span key={2} style={{ cursor: 'pointer' }} onClick={() => progressEditing(record)}>进度编辑</span>
                        </div>
                        : null
                }
                {
                    roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROGRESS_VIEW') ?
                        <div>
                            <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowDetail(record)}>进度查看</span>
                        </div> : null
                }
                {
                    roleJudgment(userInfo, 'PERM_STORE_PROJECT_STATUS_DETAIL') && record.managerUserName ?
                        <div>
                            <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowView(record)}>准入详情</span>
                        </div> : null
                }
            </div>
            </>
    }



    let searchList = [
        { label: '门店编号', name: 'dealerCode', type: 'Input', colSpan: 6 },
        { label: '门店名称', name: 'name', type: 'Input', colSpan: 6 },
    ]

    const initPage = () => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let FormQueryH = document.getElementsByClassName('PublicList_FormQuery')[0].clientHeight
        let h = winH - FormQueryH - 47 - 54 - 410
      setTableHeight(h)
    }

    useEffect(() => {
        initPage()
    }, [])


    const tableProps = pageType === 'projectManage'  ?{
        type:3,
        manualPaging:true
    }:{
        type:4
    }

    return (
        <div className='StoreList'>
            <Spin spinning={false}>
                <div className='tableData'>
                    <Col className='title'>项目管理门店列表</Col>
                    <PublicTableQuery isFormDown={false} onSearch={onSearch} searchList={searchList} />
                        {/* 按钮区域 */}
                        {
                            pageType === 'networkDepartment' ?
                            <div className='bts'>
                                {roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROJECT_TRAIN_STANDARD_SETTINGS') ? <Button type='primary' style={{marginRight: 10}} onClick={() => trainStandardSettings()}>培训标准设置</Button> : null}
                                {roleJudgment(userInfo, 'PERM_STORE_PROJECT_PROJECT_MANAGER_SETTINGS') ? <Button type='primary' onClick={() => projectManagerSettings()}>项目经理设置</Button> : null}

                            </div>
                                : null
                        }
                    <PublicTable
                        rowSelection={false}
                        sticky={true}
                        ref={tableRef}
                        columns={columns}
                        defaultQuery={defaultQuery}
                        url={url}
                        rowSelectionChange={rowSelectionChange}
                        {...tableProps}
                    />
                </div>
            </Spin>
        </div>
    )
}
export default List
