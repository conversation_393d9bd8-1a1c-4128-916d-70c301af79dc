import React, { useState, memo, useMemo, useEffect, useRef, useContext } from 'react'
import { Modal, Form, Row, Col, Button, Input, message } from 'antd'
import { put, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import UploadFile from '@/components/Public/UploadFile'
import { ExclamationCircleFilled, UploadOutlined, DownloadOutlined } from '@ant-design/icons'
import baseURL from '@/baseURL'
import style from './LO_REDModal.module.less'
import { getDataType } from '@/utils';
import { InjectMessage } from '@/pages/RightCenter/AccountManagement/Detail'

const LogoutModal = (props) => {
    const { handleCancel, visible, reloadChangeInfo, title, examine } = props
    const injectObj = useContext(InjectMessage)

    const tipsText = {
        default: title.type === 'LO' ? '注销前请确认所注销的账户信息，注销后不可恢复' : '还原至待交付状态前请确认所还原的账户信息，还原后不可恢复',
        error: title.type === 'LO' ? '该账户已关联付费、赠送权益包，如需要直接注销，请先联系用户服务业务老师-胡月，获得许可后并上传证明文件后，方可注销。' : '该账户已关联付费、赠送权益包，如需要还原至待交付状态，请先联系用户服务业务老师-胡月，获得许可后并上传证明文件后，方可还原。'
    }
    const label = title.type === 'LO' ? '注销' : '还原'
    const [form] = Form.useForm()
    const [loading1, setLoading1] = useState(false)
    const [loading2, setLoading2] = useState(false)
    const [btnLoading, setBtnLoading] = useState(false)
    const [filename1, setFilename1] = useState('')
    const [filename2, setFilename2] = useState('')
    const [tips, setTips] = useState(tipsText.default)
    // const [examine, setExamine] = useState(false) //是否需要证明文件
    const [btnDisabled, setBthDisabled] = useState(false)
    const [submittable, setSubmittable] = React.useState(false);

    const dataSourceForm = useMemo(() => props.dataSource, [props.dataSource])
    const formRef = useRef(null)

    // Watch all values
    const values = Form.useWatch([], form);
    // console.log(injectObj);


    useEffect(() => {
        console.log(values);

        setBthDisabled(false)
        if (getDataType(values) === 'Object') {
            Object.keys(values).map(i => {
                if (!values[i]) {
                    setBthDisabled(true)
                }
            })
        }

        // values
    }, [values]);
    const handleOk = () => {
        handleCancel()
        // const params = {
        //     accountNo: dataSourceForm.accountNo,
        //     account: formRef.current.getFieldValue(['account']),
        //     carBuyerName: formRef.current.getFieldValue(['carBuyerName']),
        //     carOwnerPhone: formRef.current.getFieldValue(['carOwnerPhone']),
        //     carOwnerName: formRef.current.getFieldValue(['carOwnerName'])
        // }
        // console.log('更改', params)
        // // 更改账户信息接口     730
        // post(allUrl.AccountManagement.updateAccountObj, { ...params }).then(res => {
        //     if (res.success) {
        //         message.success('更新成功！')
        //         reloadChangeInfo();
        //     } else {
        //         // message.error('更新失败！')
        //     }
        //     // setLoading(false)
        // })
    }
    const UploadChange = ({ file, fileList }, type) => {

        const { response } = file
        if (file.status === 'uploading') {
            eval(`setLoading${type}`)(true)
        }
        if (file.status === 'done') {
            message.success(response.msg || response.msgCode)
            eval(`setLoading${type}`)(false)
            eval(`setFilename${type}`)(file.name)
            switch (type) {
                case 1:
                    response.resp && form.setFieldValue('proofFileUrl', response.resp[0]?.url)
                    break
                case 2:
                    response.resp && form.setFieldValue('auditFileUrl', response.resp[0]?.url)
                    break
            }
            // console.log(form.getFieldsValue())
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msgCode)
            eval(`setLoading${type}`)(false)
        }
    }

    // 判断该账户内权益明细是否已经核销过或正在核销
    // const getBenefits = () => {
    //     // console.log(dataSourceForm);

    //     get(allUrl.AccountManagement.getBenefits({ accountNo: dataSourceForm.accountNo }), '').then(res => {
    //         const { success, resp } = res
    //         console.log(resp);

    //         if (success) {
    //             /**
    //              * hasUsed: 是否有已使用或者正在使用的权益
    //              * hasPaidOrGiftBenefits: 是否有付费或者赠送权益
    //              */
    //             const { hasUsed, hasPaidOrGiftBenefits } = resp[0]
    //             Modal.warning({
    //                 title: tips,
    //                 cancelText:'我知道了'
    //             })
    //             return
    //             if (hasUsed) {
    //                 setTips(tipsText.error)
    //                 Modal.warning({})
    //                 return
    //             }
    //             if (hasPaidOrGiftBenefits) {
    //                 // 已关联
    //                 setTips(tipsText.default)
    //                 setExamine(true)
    //             } 

    //         } else {
    //             message.warn(res.msg)
    //         }
    //     })
    // }
    const submit = (values) => {
        setBtnLoading(true)
        if (title.type === 'LO') {
            const { reason, auditFileUrl, proofFileUrl } = form.getFieldsValue()
            const data = {
                deactivateReason: reason,//注销原因
                auditFileUrl,//审核文件地址
            }
            if (examine) {
                data.proofFileUrl = proofFileUrl//证明文件地址
            }
            put(allUrl.AccountManagement.deactivation({ accountNo: dataSourceForm.accountNo }), data).then(res => {
                const { success, msg } = res
                if (success) {
                    message.success('注销成功')
                    handleCancel()
                    injectObj.getDetail()
                }
                setBtnLoading(false)

            }).catch(() => {
                setBtnLoading(false)
            })
        } else if (title.type === 'RED') {
            const { reason, auditFileUrl, proofFileUrl } = form.getFieldsValue()
            const data = {
                rollBackReason: reason,//还原原因
                auditFileUrl,//审核文件地址
            }
            if (examine) {
                data.proofFileUrl = proofFileUrl//证明文件地址
            }
            put(allUrl.AccountManagement.rollback({ accountNo: dataSourceForm.accountNo }), data).then(res => {
                const { success, msg } = res
                if (success) {
                    message.success('还原成功')
                    handleCancel()
                    injectObj.getDetail()
                }
                setBtnLoading(false)

            }).catch(() => {
                setBtnLoading(false)
            })
        }


    }


    // useEffect(() => {
    //     if (dataSourceForm) {
    //         formRef.current && form.setFieldsValue({
    //             ...dataSourceForm,

    //         })
    //     }
    // }, [dataSourceForm, form])

    useEffect(() => {
        if (examine) {
            setTips(tipsText.error)
        } else {
            setTips(tipsText.default)
        }
    }, [examine])
    return (
        <Modal open={visible} onOk={submit} formProps={{ form }} okText={`确认${label}`} okButtonProps={{ disabled: btnDisabled, loading: btnLoading }} onCancel={handleCancel} maskClosable={false} width='700px' title={title.title}>
            <div className={style.tips}>
                <ExclamationCircleFilled style={{ fontSize: '21px', color: '#FAAD14' }} />
                <p dangerouslySetInnerHTML={{ __html: tips }}></p>
            </div>
            <Form ref={formRef} form={form} className={style.changeAccountInfo}>
                <Row>
                    {examine ?
                        <Col span={24}>
                            <Form.Item label='证明文件' name='proofFileUrl' className={style.inputCon}>
                                <UploadFile
                                    style={{ display: 'inline-block' }}
                                    extension={['png', 'jpg', 'jpeg', 'pdf']}
                                    showUploadList={false}
                                    size={5}
                                    name='files'
                                    action={baseURL.Host + allUrl.common.uploadFile}
                                    UploadChange={(file) => UploadChange(file, 1)}
                                >
                                    <Button icon={<UploadOutlined />} type="primary" className={style.uploadBtn} loading={loading1}>{filename1 ? filename1 : '上传附件'}</Button>
                                    <p className={style.limitTips}> 请上传业务人员许可，支持图片、pdf上传，大小不超过5M</p>
                                </UploadFile>
                            </Form.Item>
                        </Col> :
                        null}

                    <Col span={24}>
                        <Form.Item label={`${label}原因`} name='reason'>
                            <Input allowClear placeholder='' maxLength={500} />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='审核文件' name='auditFileUrl' className={style.inputCon}>
                            {/* <UploadFile
                                style={{ display: 'inline-block' }}
                                extension={['png', 'jpg', 'jpeg', 'pdf']}
                                showUploadList={false}
                                size={5}
                                // action={baseURL.Host + allUrl.Authority.importUserRelation}
                                UploadChange={(file) => UploadChange(file, 2)}
                            >
                                <Button icon={<UploadOutlined />} type="primary" style={{ marginRight: '20px' }} loading={loading1}>{filename1 ? filename1 : '上传附件'}</Button>
                            </UploadFile> */}
                            <UploadFile
                                style={{ display: 'inline-block' }}
                                extension={['png', 'jpg', 'jpeg', 'pdf']}
                                showUploadList={false}
                                size={5}
                                name='files'
                                action={baseURL.Host + allUrl.common.uploadFile}
                                UploadChange={(file) => UploadChange(file, 2)}
                            >
                                <Button icon={<UploadOutlined />} type="primary" className={style.uploadBtn} loading={loading2}>{filename2 ? filename2 : '上传附件'}</Button>
                                <p className={style.limitTips}>请上传邮件截图、OA审批截图等审核文件，支持图片、pdf上传，大小不超过5M</p>
                            </UploadFile>

                        </Form.Item>

                    </Col>
                </Row>
            </Form>
            {/* <div className='footer'>
                <Button>取消</Button>
                <Button type='primary'>{`确认${label}`}</Button>
            </div> */}
        </Modal>
    )
}
export default memo(LogoutModal)