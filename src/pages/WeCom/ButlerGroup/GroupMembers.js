import './index.less'
import './GroupMembers.less'
import React, { useState, useRef,useEffect } from 'react'
import { useSelector ,useDispatch} from 'react-redux'
import {Typography ,Row,Col,Button,Tooltip} from 'antd'
import allUrl from '@/utils/url'
import PublicTable from '@/components/Public/PublicTable'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import _ from 'lodash'
import moment from 'moment';
import { roleJudgment } from '@/utils/authority'
import {DownOutlined,UpOutlined,QuestionCircleOutlined} from '@ant-design/icons';
import { DecryptByAES } from '@/components/Public/Decrypt'
import {getDict} from '../../../actions/async'
import history from '@/utils/history'
import GroupNameLog from './components/GroupNameLog'
import GroupLeaderLog from './components/GroupLeaderLog'
import {utilsDict} from '@/utils/utilsDict'
import DeliveryOrder from '../Customer/deliveryOrder'
const { Paragraph, Text } = Typography;

const GroupMembers = (props) => {
    const tableRef = useRef()
    const dispatch = useDispatch()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [defaultQuery, setDefaultQuery] = useState({})
    const [locationParmas, setLocationParmas] = useState({})
    const [dictList,setDictList] = useState({})
    const [ID, setID] = useState('')
    const [paragraphHide,setParagraphHide] = useState(false)
    const [groupNameLogVisible,setGroupNameLogVisible] = useState(false)
    const [groupLeaderLogVisible,setGroupLeaderLogVisible] = useState(false)
    const [key, setKey] = useState(0);
    const [deliveryOrderVisible, setDeliveryOrderVisible] = useState(false)
    const [recordObj, setRecordObj] = useState({})

    const onSearch = (values) => {
        if (values.joinTime && values.joinTime.length) {
            values.joinTimeStart = moment(values.joinTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.joinTimeEnd = moment(values.joinTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.joinTime
        }
        values.groupId = ID
        setDefaultQuery(values)
    }

    const RowDetail = (record) => {
        setRecordObj(record)
        setDeliveryOrderVisible(true)
    }

    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff',textAlign:'center' }}>
            {
                roleJudgment(userInfo, 'WECOM_BUTLER_GROUP_GROUP_MEMBERS_VIEW') ?
                    [
                        (record.customerId && record.customerType && [1,2,3,5].indexOf(+record.customerType) > -1) ? <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowDetail(record)}>查看交付单</span> : null,
                    ] : null
            }
        </div>
    }

    const columns = [
        { title: '序号', dataIndex: 'index', width: 80,render:(text,record,index)=>index+1 },
        { title: '成员姓名(群昵称)', dataIndex: 'name', width: 160 },
        { title: '成员类型', dataIndex: 'typeName', width: 140 },
        { title: '客户类型', dataIndex: 'customerTypeName', width: 140 },
        { title: 'UserID', dataIndex: 'userId', width: 140 },
        { title: '企微账号ID', dataIndex: 'enterpriseUserId', width: 140 },
        { title: '所属组织', dataIndex: 'organization', width: 200 },
        { title: '岗位名称', dataIndex: 'positionName', width: 160 },
        { title: '入群方式', dataIndex: 'joinSceneName', width: 180 },
        { title: '入群时间', dataIndex: 'joinTime', width: 180 },
        { title: '是否为群管理员', dataIndex: 'managerFlagName', width: 140 },
        { title:  <div>
            是否存档账号&nbsp;
            <Tooltip title={`统计截止：${moment().subtract(1, 'days').format('YYYY-MM-DD')} 23:59:59`} key='1'>
            <QuestionCircleOutlined />
            </Tooltip>
        </div>, dataIndex: 'qwChatArchive', width: 160, render: text => text == '1' ? '是' : '否' },
        { title: '是否在职', dataIndex: 'isOnJob', width: 100, render: text => text == 1 ? '是' : '否' },
        { title: '企微激活状态', dataIndex: 'qywxStatus', width: 120, render: text => utilsDict('qywxStatus',text)  },
        { title: '操作', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }

    ]

    let searchList = [
        { label: '成员姓名', name: 'name', type: 'Input', colSpan: 4 },
        { label: '成员类型', name: 'type', type: 'Select', colSpan: 4, data:dictList['qw_type'] || [] },
        // { label: '客户类型', name: 'customerType', type: 'Select', colSpan: 6, data:dictList['qw_customer_type'] || []},
        { label: '入群时间', name: 'joinTime', type: 'RangePicker', colSpan: 10, showTime: true},
    ]

    let newColumns = _.cloneDeep({ columns }).columns
    for (let i = 0; i < newColumns.length; i++) {
        if (JSON.stringify(newColumns[i]['checked']) !== undefined && !newColumns[i].checked) {
            newColumns.splice(i, 1)
            i--
        }
    }

    useEffect(() => {
        const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}
        setLocationParmas(locationParmas)
        setID(locationParmas.groupId)
        setDefaultQuery({groupId:locationParmas.groupId})
    }, [userInfo])

    useEffect(()=>{
        dispatch(getDict({codes:'qw_type,qw_customer_type'})).then(({payload})=>{
            setDictList(payload)
        })
    },[])

    const {rowData={}} = locationParmas
    return (
        <div className='ButlerGroupList'>
            <div className='customer-info'>
                <div style={{marginBottom:'20px'}}>
                    <Button onClick={()=>{
                        history.goBack()
                    }}>返回</Button>
                </div>
                <div className='customer-info-con'>
                    <div className='customer-info-con-left'>
                        <img src={require("../../../assets/img/wecom_avatar.png")} />
                    </div>
                    <div className='customer-info-con-right'>
                        <div className='title'>
                            <span className='name'>{rowData.name}</span>
                            {/* <span className='type'>VIP群</span> */}
                            <span className='num'>（{rowData.memberCount}人）</span>
                            <span style={{margin:'0 10px'}}><a onClick={()=>setGroupNameLogVisible(true)}>群名修改日志</a></span>
                        </div>
                        <div className='info'>
                            <div className='name'>
                                <Row>
                                    <Col span={6}>
                                        <span className='label'>群主：</span>{rowData.ownerName}
                                        <span style={{margin:'0 10px'}}><a onClick={()=>setGroupLeaderLogVisible(true)}>群主变更日志</a></span>
                                    </Col>
                                    <Col span={8}>
                                        <span className='label'>创建时间：</span>
                                        {rowData.groupCreateTime}
                                    </Col>
                                </Row>
                                <Row>
                                <Col span={24}>
                                        <div className='notice' key={key}>
                                            <div className='label'>群公告：</div>
                                            <Paragraph ellipsis={{ rows: 1, expandable: true, symbol: <span onClick={()=>setParagraphHide(!paragraphHide)}>展开 <DownOutlined /></span> }}>
                                            {rowData.notice}
                                                {
                                                    paragraphHide ?
                                                    <span onClick={()=>{
                                                        setParagraphHide(!paragraphHide)
                                                        setKey(key + 1)
                                                    }} style={{marginLeft:'4px'}}>
                                                        <a>收起<UpOutlined /></a>
                                                    </span>:null

                                                }
                                            </Paragraph>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className='tableData'>
                <PublicTableQuery isFormDown={false} onSearch={onSearch} searchList={searchList} />
                {
                    ID ? 
                    <PublicTable sticky={true} type={5} ref={tableRef} rowSelection={false} columns={newColumns} defaultQuery={defaultQuery} url={allUrl.WeCom.qwGroupMemberList} />
                    :null
                }
                {
                    groupNameLogVisible && 
                    <GroupNameLog visible={groupNameLogVisible} objectKey={ID} onCancel={()=>setGroupNameLogVisible(false)} />
                }
                {
                    groupLeaderLogVisible && 
                    <GroupLeaderLog visible={groupLeaderLogVisible} objectKey={ID} onCancel={()=>setGroupLeaderLogVisible(false)} />
                }
                {
                    deliveryOrderVisible && 
                    <DeliveryOrder id={recordObj.customerId} visible={deliveryOrderVisible} onCancel={() => setDeliveryOrderVisible(false)} />
                }

            </div>
        </div>
    )
}
export default GroupMembers
