import React, { useEffect, useState, useRef } from 'react';
// import styled from 'styled-components'
import { useSelector } from 'react-redux';
import { But<PERSON>, Row, Col, message } from 'antd';
import './style.less';
import tableModuleStyles from '../tableLayout.module.less';
import { get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { roleJudgment } from '@/utils/authority';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import PublicTableQuery from '@/components/Public/PublicTableQuery';
import PublicTable from './PublicTable';
import styles from '../antd.module.less';

const AccountManagementToClient = props => {
  const tableRef = useRef();
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [defaultQuery, setDefaultQuery] = useState({});

  const statusMapList = [
    { name: '已交付', value: '1' },
    { name: '未交付', value: '2' },
    { name: '已退定', value: '3' },
    { name: '异常关单', value: '4' },
    { name: '已注销', value: '5' },
  ];
  const orderCreateSourceMap = [
    { name: 'DMS(VMall)', value: '1' },
    { name: 'DMS(PMall)', value: '2' },
    { name: '营销云(JKSales)', value: '3' },
    { name: '人工创建权益账户', value: '4' },
    { name: '未知', value: '5' },
  ];

  useEffect(() => {
    // 后端预加载
    get(allUrl.AccountManagement.getSnapshotPackName);
    // 获取车辆市场类型接口参数 EquityPackageManage.getCarMarketTypeAll
    // post(allUrl.EquityPackageManage.getCarMarketTypeAll).then(res => {
    //     if (res.success) {
    //         setAllCarMarketType(res.resp[0])
    //     }
    // })
  }, []);

  const onSearch = values => {
    // const tmpl = Object.values(values)
    // //是否点击的重置按钮
    // const isReset = tmpl.length === 1
    // //点击查询按钮，查询条件是否都为空,因为默认有一个"_t"时间戳的查询参数
    // const isParamsEmpty = tmpl.filter(Boolean).length < 2
    // if (!isReset && isParamsEmpty) {
    //     message.warning('请至少选择一个查询条件')
    //     return
    // }
    setDefaultQuery(values);
  };

  const LookAt = record => {
    let data = {
      accountNo: record.accountNo,
      Type: 'LookAt',
      title: '权益账户信息',
    };
    UniversalOpenWindow({
      JumpUrl: '/RightCenter/AccountManagementDetail',
      data,
      history: props.history,
    });
  };

  const createMethods = [
    { name: '系统创建', value: 1 },
    { name: '手动创建', value: 2 },
    { name: '邮件创建', value: 3 },
  ];
  let searchList = [
    {
      label: '购车人手机号',
      name: 'carBuyerPhone',
      type: 'Input',
      placeholder: '请输入购车人手机号',
      colSpan: 6,
      rules: [{ pattern: /^[0-9]*$/, message: '只支持数字，请正确输入' }],
    },
    {
      label: '车辆VIN',
      name: 'vin',
      type: 'Input',
      placeholder: '请输入车辆vin',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    //交付状态
    // { label: '订单状态', name: 'accountStatus', type: 'Select', placeholder: '请选择', colSpan: 6, data: statusMapList },
    // { label: '订单来源', name: 'orderCreateSource', type: 'Select', placeholder: '请选择', colSpan: 6, data: orderCreateSourceMap },

    // { label: '车型', name: 'carAttr', type: 'Select', placeholder: '请选择', colSpan: 6, data: DictData || [] },
    // { label: '基础配置', name: 'carConfigName', type: 'Input', placeholder: '请输入基础配置（支持模糊搜索）', colSpan: 6 }, // 基础配置
    // {
    //     label: '车辆销售类型', name: 'transferType', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
    //         { name: '一手车', value: '1' },
    //         { name: '二手车', value: '2' },
    //     ]
    // },
    // { label: '车辆市场类型', name: 'carMarketType', type: 'Select', placeholder: '请选择', colSpan: 6, fieldNames: { value: 'code', label: 'name' }, data: allCarMarketType || [] },
    // { label: '权益包名称', name: 'packNoName', type: 'Input', placeholder: '请输入权益包名称', colSpan: 6 },
    // { label: '权益包编码', name: 'packNo2', type: 'Input', placeholder: '请输入权益包编码', colSpan: 6 },
    // { label: '权益账户编码', name: 'accountNo', type: 'Input', placeholder: '请输入权益账户编码', colSpan: 6 },
    // { label: '意向订单', name: 'bookOrderNo', type: 'Input', placeholder: '请输入意向订单号', colSpan: 6 },
    {
      label: '正式订单',
      name: 'formalOrderNo',
      type: 'Input',
      placeholder: '请输入XCO、CO开头正式订单号',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    {
      label: '华为订单',
      name: 'hwOrderNo',
      type: 'Input',
      placeholder: '请输入1H、1G、VF开头华为订单号',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    {
      label: '交付订单',
      name: 'deliverOrderNo',
      type: 'Input',
      placeholder: '请输入DO开头交付订单号',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    // { label: '销售订单', name: 'saleOrderNo', type: 'Input', placeholder: '请输入销售订单号', colSpan: 6 },
    // { label: '账户创建方式', name: 'orderSource', type: 'Select', placeholder: '请选择', colSpan: 6, data: createMethods },
    // { label: '账户创建时间', name: 'createTime', type: 'RangePicker', showTime: true, placeholder: ['起始时间', '结束时间'], colSpan: 6, extra: <div style={{ color: 'red', fontSize: '14px', marginTop: '7px' }}>创建时间可选择时间点、时间段进行查询</div> },
  ];
  const columns = [
    {
      title: '权益账户编码',
      dataIndex: 'accountNo',
      width: 120,
      fixed: 'left',
      render: (text, record) => {
        if (text) {
          // return text
          const res = statusMapList.filter(i => i.value == record.accountStatus)[0];
          return (
            <div>
              <div className={'mark bg' + res?.value}>
                <div className="mark-content">
                  <span>{res?.name}</span>
                </div>
              </div>
              <div>{text}</div>
            </div>
          );
        } else {
          return <div>{'——'}</div>;
        }
      },
    },

    {
      title: '购车人手机号',
      dataIndex: 'carBuyerPhone',
      width: 120,
      fixed: 'left',
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },

    {
      title: '购车人',
      dataIndex: 'carBuyerName',
      width: 120,
      fixed: 'left',
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },

    {
      title: '账户创建时间',
      dataIndex: 'createTime',
      width: 120,
      render: (text, record) => {
        if (text) {
          return text;
          // return moment(text).format('YYYY-MM-DD HH:MM:SS')
        } else {
          return <div>{'——'}</div>;
        }
      },
    },
    {
      title: '账户创建方式',
      dataIndex: 'orderSource',
      width: 120,
      render: (text, record) => {
        if (text) {
          return createMethods.filter(i => i.value == text)[0]['name'];
        } else {
          return <div>{'——'}</div>;
        }
      },
    },

    {
      title: '车型',
      dataIndex: 'carTypeName',
      width: 120,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆VIN',
      dataIndex: 'vin',
      width: 100,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆销售类型',
      dataIndex: 'transferTypeName',
      width: 120,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '基础配置',
      dataIndex: 'carConfigName',
      width: 190,
      render: (text, record) => {
        if (text) {
          return <div style={{ whiteSpace: 'nowrap' }}>{text}</div>;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆市场类型',
      dataIndex: 'carMarketTypeName',
      width: 120,
      render: (text, record) => {
        if (text) {
          return <div>{text}</div>;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '订单来源',
      dataIndex: 'orderCreateSource',
      width: 120,
      render: (text, record) => {
        if (text) {
          return orderCreateSourceMap.filter(i => i.value == text)[0]['name'];
        }
        return <div>{'—'}</div>;
      },
    },
    {
      title: '意向订单号',
      dataIndex: 'bookOrderNo',
      width: 110,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '正式订单号',
      dataIndex: 'formalOrderNo',
      width: 140,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '华为订单号',
      dataIndex: 'hwOrderNo',
      width: 110,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '销售订单号',
      dataIndex: 'saleOrderNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '交付订单号',
      dataIndex: 'deliverOrderNo',
      width: 200,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '基础权益包名称',
      dataIndex: 'baseName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '基础权益包编码',
      dataIndex: 'baseNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    //  630
    {
      title: '付费权益包名称',
      dataIndex: 'payName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '付费权益包编码',
      dataIndex: 'payNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '赠送权益包名称',
      dataIndex: 'giveName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '赠送权益包编码',
      dataIndex: 'giveNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '精品权益包名称',
      dataIndex: 'boutiqueName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return '—';
        }
      },
    },
    {
      title: '精品权益包编码',
      dataIndex: 'boutiqueNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return '—';
        }
      },
    },
    {
      title: '增值权益包名称',
      dataIndex: 'valueAddName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '增值权益包编码',
      dataIndex: 'valueAddNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '驾行权益包名称',
      dataIndex: 'safeName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '驾行权益包编码',
      dataIndex: 'safeNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '操作',
      width: 100,
      fixed: 'right',
      dataIndex: 'Operation',
      render: (text, record) => (
        <>
          {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_CLIENT_DETAIL') ? (
            <Button type="link" size="small" onClick={() => LookAt(record)}>
              详情
            </Button>
          ) : null}
        </>
      ),
    },
  ];

  return (
    <div className={`${tableModuleStyles.PublicList} account-client-PublicList`}>
      <PublicTableQuery isCatch={true} isFormDown={false} onSearch={onSearch} searchList={searchList} />
      <div className="tableData">
        <Row className="tableTitle">
          <Col className="text" span={6} style={{ color: 'black', fontSize: 20 }}>
            权益账户列表
          </Col>
        </Row>
        <PublicTable
          ref={tableRef}
          isCatch={true}
          rowSelection={false}
          defaultQuery={defaultQuery}
          url={allUrl.AccountManagement.getExactAccountList}
          columns={columns}
          className={styles['blue-table']}
        />
      </div>
    </div>
  );
};
export default AccountManagementToClient;
