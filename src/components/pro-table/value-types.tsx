import { isEmpty } from 'lodash';
import moment from 'moment';
import { ReactNode } from 'react';

export type ValueType =
  | 'text'
  | 'money'
  | 'percent'
  | 'date'
  | 'dateTime'
  | 'time'
  | 'dateRange'
  | 'dateTimeRange'
  | 'timeRange'
  | 'enum'
  | 'index';

type ValueRender = (value: any, record: Record<string, any>, index: number) => ReactNode;

export const nullableValueRender = (render: ValueRender, ...args: Parameters<ValueRender>) => {
  if (isEmpty(args[0]) && typeof args[0] !== 'number') {
    return '-';
  }
  return render(...args);
};

const dateRender: ValueRender = (value) => moment(value).format('YYYY-MM-DD');

const dateTimeRender: ValueRender = (value) => moment(value).format('YYYY-MM-DD HH:mm:ss');

const timeRender: ValueRender = (value) => moment(value).format('HH:mm:ss');

const dateRangeRender: ValueRender = (value: any[], record, index) =>
  value.map((i) => dateRender(i, record, index)).join(' - ');

const dateTimeRangeRender: ValueRender = (value: any[], record, index) =>
  value.map((i) => dateTimeRender(i, record, index)).join(' - ');

const timeRangeRender: ValueRender = (value: any[], record, index) =>
  value.map((i) => timeRender(i, record, index)).join(' - ');

export const valueTypeRenders: Partial<
  Record<ValueType, (value: any, record: Record<string, any>, index: number) => React.ReactNode>
> = {
  text: (...args) => nullableValueRender((value) => value, ...args),
  money: (...args) => nullableValueRender((value) => value, ...args),
  percent: (...args) => nullableValueRender((value) => value, ...args),
  date: (...args) => nullableValueRender(dateRender, ...args),
  dateTime: (...args) => nullableValueRender(dateTimeRender, ...args),
  time: (...args) => nullableValueRender(timeRender, ...args),
  dateRange: (...args) => nullableValueRender(dateRangeRender, ...args),
  dateTimeRange: (...args) => nullableValueRender(dateTimeRangeRender, ...args),
  timeRange: (...args) => nullableValueRender(timeRangeRender, ...args),
};
