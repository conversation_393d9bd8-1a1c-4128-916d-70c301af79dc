import './index.less'
import React,{useEffect, useState,useRef} from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {Row,Col,Table,Button,Divider,Popconfirm, message} from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment'
import { post ,get} from '@/utils/request';
import allUrl from '@/utils/url'
import _ from "lodash";
import {trim} from '@/utils'
import baseURL from '@/baseURL'
import {roleJudgment} from '@/utils/authority'
const PrimaryClue = (props) =>{
    const childRef = useRef(null);
    const {userInfo} = useSelector(state => state.common)
    const [loading,setLoading] = useState(false)
    const [dataSource,changeDataSource] = useState([])
    const [current,changeCurrent] = useState(1)
    const [pageSize,changePageSize] = useState(10)
    const [total,changeTotal] = useState(0)
    const [defaultQuery,setDefaultQuery] = useState({})
    const [scrm_clue_carType,setscrm_clue_carType] = useState([])
    const [clueOriginArr,setClueOriginArr] = useState([])
    const [DealerOptions,setDealerOptions] = useState([])
    const onSearch = (values) =>{
        console.log(values)
        if (values.createTime && values.createTime.length) {
            values.startTime = moment(values.createTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.endTime = moment(values.createTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.createTime
        }
        setDefaultQuery(values)

    }
    const dealersSearch =_.debounce((value)=>{
        value =trim(value)
        get(allUrl.ClueManagement.findDealersByKeywords, {keywords:value,}).then(res => {
            if (res.success) {
                let Dt =res.resp.map((item,index)=>{
                    return{name :item.name,
                        value :item.dealerCode,key:index+1}
                })
                setDealerOptions(Dt)
            } else {
                // message.error(res.msg)
            }
        })

    }, 500);
    const searchList=[
        {label:'姓名',name:'customerName',type:'Input',placeholder:'请输入姓名',colSpan:6},
        {label:'手机号',name:'mobilePhone',type:'Input',placeholder:'请输入手机号',colSpan:6},
        {label:'意向车型',name:'intentionCarType',type:'Select',placeholder:'请选择',colSpan:6,data:scrm_clue_carType},
        {label:'意向门店',name:'dealerCode',type:'SearchSelect',placeholder:'请选择',showSearch:true,onSearch:dealersSearch,colSpan:6,data:DealerOptions},
        {label:'线索渠道',name:'channel',type:'Select',placeholder:'请选择',colSpan:6,data:clueOriginArr},
        {label:'线索省份',name:'province',type:'Input',placeholder:'请输入省份',colSpan:6},
        {label:'线索城市',name:'city',type:'Input',placeholder:'请输入城市',colSpan:6},
        {label:'线索区',name:'district',type:'Input',placeholder:'请输入城市',colSpan:6},
        {label:'原始线索创建时间',name:'createTime',type:'RangePicker',placeholder:['开始时间', '截止时间'],colSpan:6},
        {label:'是否归集',name:'isCollected',type:'Select',colSpan:6,data:[
            {name:'是',value:'1'},
            {name:'否',value:'0'},
        ]},
    ]
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const getData = () =>{
        setLoading(true)
            let query = {...defaultQuery}
            let data = {current:current,pageSize,params:{...query}}
            post(allUrl.ClueManagement.queryLeadList,{...data}).then(res=>{
                if(res.success){
                    changeDataSource(res.resp[0].list)
                    changeTotal(res.resp[0].pagination.total)
                    setLoading(false)
                }else{
                    message.error(res.msg)
                }
                // setLoading(false)
            })
        
    }
    useEffect(()=>{
        get(allUrl.ClueManagement.carTypes).then(res=>{
            // 线索车辆
            console.log(res)
            if(res.success){
                res.resp.forEach(item=>{
                    item.name = item.value
                    item.value = item.key
                })
                setscrm_clue_carType(res.resp)
            }else{
                // message.error(res.msg)
            }
        })
        get(allUrl.ClueManagement.channels).then(res=>{
            // 线索渠道
            console.log(res)
            if(res.success){
                res.resp.forEach(item=>{
                    item.name = item.value
                    item.value = item.key
                })
                setClueOriginArr(res.resp)
            }else{
                // message.error(res.msg)
            }
        })
    },[])
    useEffect(()=>{
        if(userInfo){
            getData()
        }
    },[defaultQuery,userInfo,current,pageSize])
    const InforData = {
        rowKey: record => record.frontId,
        bordered: true,
        dataSource:dataSource,
        loading:loading,
        scroll: { x: 'max-content' },
        columns: 
        [
            { title: '原始线索ID', dataIndex: 'leadId',width:140,},
            { title: '姓名', dataIndex: 'customerName', width: 100 },
            { title: '手机号', dataIndex: 'mobilePhone', width: 140 },
            { title: '意向车型', dataIndex: 'intentionCarType', width: 140},
            { title: '意向门店', dataIndex: 'intentionDealer', width: 140},
            { title: '线索省份', dataIndex: 'province', width: 150},
            { title: '线索城市', dataIndex: 'city', width: 150},
            { title: '线索区', dataIndex: 'district', width: 150},
            { title: '线索渠道', dataIndex: 'channelName', width: 140 },
            { title: '原始线索创建时间', dataIndex: 'createTime', width: 180 },
            { title: '是否归集', dataIndex: 'isCollected', width: 100,render:text => text == 1 ? '是':'否'},
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
       
    };
    return(
            <div className='ClueList'>
                {
                roleJudgment(userInfo,'PERM_PRIMARY_LIST') ?
                <>
                 <PublicTableQuery onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery}/>
                <div className='tableData' key='2'>
                    <Row className='tableTitle' key='1'>
                        <Col className='text'>历史线索列表</Col>
                    </Row>
                    <Table {...InforData} />
                </div>
                </>:null
                }
            </div>
        
    )
}
export default PrimaryClue
