import React, { memo } from 'react'
import { Modal, Descriptions, Button } from 'antd'
import moment from 'moment'
const PartsDetail = (props) => {
    const { handleCancel, visible, detailInfoSource } = props
    return (
        <Modal
            visible={visible}
            footer={[<Button key="back" onClick={handleCancel}>返回</Button>]}
            onCancel={handleCancel}
            maskClosable={false}
            width='650px'
            title='配件详情'>
            <Descriptions title="" column={1} style={{ marginLeft: 40 }}>
                <Descriptions.Item label='车型'>{detailInfoSource.carType || '-'}</Descriptions.Item>
                <Descriptions.Item label='配件名称'>{detailInfoSource.partName || '-'}</Descriptions.Item>
                <Descriptions.Item label='配件编码'>{detailInfoSource.partNo || '-'}</Descriptions.Item>
                <Descriptions.Item label='配件包修类型'>{detailInfoSource.partTypeName || '-'}</Descriptions.Item>
                <Descriptions.Item label='原车保修期（月）'>{detailInfoSource.originalPartTime || '-'}</Descriptions.Item>
                <Descriptions.Item label='原车保修里程（公里）'>{detailInfoSource.originalPartMileage || '-'}</Descriptions.Item>
                <Descriptions.Item label='配件保修期（月）'>{detailInfoSource.partTime || '-'}</Descriptions.Item>
                <Descriptions.Item label='配件保修里程（公里）'>{detailInfoSource.partMileage || '-'}</Descriptions.Item>
                <Descriptions.Item label='状态'>{detailInfoSource.partStatusName || '-'}</Descriptions.Item>
                <Descriptions.Item label='同步时间'>{detailInfoSource.syncTime ? moment(detailInfoSource.syncTime).format('YYYY年MM月DD日 HH:mm:ss') : '-'}</Descriptions.Item>
                <Descriptions.Item label='更新时间'>{detailInfoSource.updateTime ? moment(detailInfoSource.updateTime).format('YYYY年MM月DD日 HH:mm:ss') : '-'}</Descriptions.Item>
                <Descriptions.Item label='更新批次'>{detailInfoSource.partVersion || '-'}</Descriptions.Item>
            </Descriptions>
        </Modal>
    )
}
export default memo(PartsDetail)