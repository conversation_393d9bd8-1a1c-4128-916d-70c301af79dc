import React from 'react'
import {<PERSON>,message,<PERSON><PERSON>,<PERSON>confirm,Input,Row,Col,Divider} from 'antd'
import {connect} from 'react-redux'
import allUrl from '../../../utils/url'
import {post} from '../../../utils/request'
import {PlusOutlined} from '@ant-design/icons';
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
import RoleCopy from './component/RoleCopy'
import {setStorage,getStorage} from '@/utils/Storage'
import {roleJudgment} from '@/utils/authority'
import {utilsDict} from '@/utils/utilsDict'
import './index.less'
import '../index.less'
const {Search} = Input
class RoleManagement extends React.Component{
    constructor(props){
        super(props)
        this.state = {
            dataSource:[],
            TabHeight:0,
            selectedRowKeys:[],
            selectedRows:[],
            pageSize:10,
            current:1,
            total:0,
            defaultQuery:{
                params:{}
            },
            rowData:{},
            loading:false,
            visible:false
        }
    }

    GetData = () =>{
        const {defaultQuery,pageSize,current} = this.state
        this.setState({loading:true},()=>{
            post(allUrl.Authority.roleList,{...defaultQuery,pageSize,current}).then(res=>{
                if(res.success){
                    this.setState({
                        dataSource:res.resp.length ? res.resp[0].list :[],
                        total:res.resp.length ? res.resp[0].pagination.total :0,
                    })
                    setStorage({Query:defaultQuery,pagination:{pageSize,current},Type:'RoleManage'})
                }else{
                    // message.error(res.msg || '获取列表信息失败！')
                }
                this.setState({loading:false})
            })
        })
    }
    PageChange=(current,pageSize)=>{ this.setState({current:current,pageSize},()=>{
        this.GetData()
    }); }

    InitPage = () =>{
        let WinHeight = document.documentElement.clientHeight;
        this.setState({TabHeight:WinHeight-410}); 
    }
    RowAdd = () =>{
        let data = {
            id:0,
            type:'Add',
            title:'添加角色'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/RoleManagementDetail',data,history:this.props.history
        })
    }
    RowEdit = (record) =>{
        let data = {
            id:record.id,
            type:'Edit',
            title:'编辑角色'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/RoleManagementDetail',data,history:this.props.history
        })
    }
    RowDetail = (record) =>{
        let data = {
            id:record.id,
            type:'LookAt',
            title:'角色详情'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/RoleManagementDetail',data,history:this.props.history
        })
    }
    RowDel = (record) =>{
        post(allUrl.Authority.deleteRole + '/' + record.id).then(res=>{
            if(res.success ){
                message.success(res.msg || '删除成功！')
                this.GetData()
            }else{
                // message.error(res.msg || '删除失败！')
            }
        })
    }
    RowCopy = (record) =>{
        this.setState({rowData:record,visible:true})
    }
    renderOperation = (text,record)  =>{
        const {userInfo} = this.props
        return <div style={{color:'#1890ff'}}>
            {
                        roleJudgment(userInfo,'PERM_ROLE_EDIT') ?
                        [
                            <span key={1} style={{cursor:'pointer'}} onClick={()=>this.RowEdit(record)}>编辑</span>,
                            <Divider key={2} type="vertical" />
                        ]:null

            }
            {
                        roleJudgment(userInfo,'PERM_ROLE_VIEW') ?
                        [
                            <span key={3} style={{cursor:'pointer'}} onClick={()=>this.RowDetail(record)}>详情</span>,
                            <Divider key={4} type="vertical" />
                        ]:null
            }
            {
                        roleJudgment(userInfo,'PERM_ROLE_DELETE') ?
                            [
                                <Popconfirm key={1} onConfirm={()=>this.RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                                    <span style={{cursor:'pointer'}}>删除</span>
                                </Popconfirm>,
                                <Divider key={2} type="vertical" />
                            ]
                        :null
            }
            {
                        roleJudgment(userInfo,'PERM_ROLE_COPY') ?
                        [
                            <span key={3} style={{cursor:'pointer'}} onClick={()=>this.RowCopy(record)}>角色拷贝</span>,
                        ]:null
            }
        </div>
    }

    onSearch = (value) =>{
        console.log(value)
        const {defaultQuery} = this.state
        defaultQuery.params.roleName = value
        this.setState({defaultQuery},()=>{
            this.GetData()
        })
    }
    onChange = (value) =>{
        console.log(value)
        const {defaultQuery} = this.state
        defaultQuery.params.roleName = value
        this.setState({defaultQuery})
    }
    
    render(){
        const {dataSource,defaultQuery,pageSize,total,current,loading,visible,rowData} = this.state
        const {userInfo} = this.props
        const InforData= {
            rowKey:record => record.id,
            bordered:true,
            dataSource,
            loading,
            scroll: { x:'max-content'},
            columns:[
                // { title: '序号', dataIndex: 'index',width:80,render:(text,record,index)=><div style={{textAlign:'center'}}>{(pageSize * (pageIndex+1))- pageSize +  index+1}</div>},
                { title: '角色ID',dataIndex: 'id',width:100},
                { title: '角色名称', dataIndex: 'roleName', width: 130},
                { title: '角色分类', dataIndex: 'type', width: 130,render:text=>utilsDict('roleType',text)},
                { title: '角色描述', dataIndex: 'description', width: 80},
                { title: '操作', width: 80,fixed:'right' ,dataIndex:'Operation',render:(text,record)=>this.renderOperation(text,record)}
            ],
            pagination:{
                pageSize:pageSize,
                onChange:this.PageChange,
                current:current,
                scroll:{x:'max-content'},
                total:total,
                showTotal:()=>`共${total}条，${pageSize}条/页`,
                showSizeChanger:true,
                showQuickJumper:true,
                onShowSizeChange:this.PageChange,
            }
        };
        return(
            <div className='UserManagement PublicList'>
                <Row className='queryArea'>
                    {
                        roleJudgment(userInfo,'PERM_ROLE_ADD') ?
                        <Col className='add' style={{marginRight:'16px'}}><Button onClick={()=>this.RowAdd()} icon={<PlusOutlined />}>添加角色</Button></Col>
                        :''
                    }
                    <Col className='search'><Search placeholder="搜角色关键字" value={defaultQuery.params.roleName || ''} allowClear onChange={(e)=>this.onChange(e.target.value)} onSearch={this.onSearch} /></Col>
                </Row>
                <Row className='tableTiele'>角色列表</Row>
                <div className='tableData'>
                <Table {...InforData} />
                </div>
                {
                    visible && 
                    <RoleCopy visible={visible} GetData={this.GetData} rowData={rowData} onCancel={()=>this.setState({visible:false})} />
                }
            </div>
        )
    }
    componentDidMount(){
        this.InitPage()
        getStorage('RoleManage').then(res=>{
            if(res){
                const {Query,pagination} = res
                this.setState({
                    defaultQuery:Query?Query:{params:{}},
                    current:pagination?pagination.current:1,
                    pageSize:pagination?pagination.pageSize:1,
                },()=>{
                    this.GetData()
                })
            }else{
                this.GetData()
            }
        })
    }
    componentWillUnmount() {
        this.setState = () => {
            return;
        };
    }
}
export default connect(({ common }) => {
    const { userInfo } = common
    return {
        userInfo
    }
})(RoleManagement)
