import CryptoJS from 'crypto-js'

// 加密
export function EncryptByAES(word, key, iv) {
    let keyHex = CryptoJS.enc.Utf8.parse(key ? key :'5566778899ABCDEF');  
    let ivHex  = CryptoJS.enc.Utf8.parse(iv ? iv : 'ABCDEF5566778899');  
    let srcs = CryptoJS.enc.Utf8.parse(word);
    let encrypted = CryptoJS.AES.encrypt(srcs, keyHex, { iv: ivHex,mode:CryptoJS.mode.CBC,padding: CryptoJS.pad.Pkcs7});
    return encrypted.ciphertext.toString().toUpperCase();
}