.relevance {
  :global {
    .ant-form-item {
      // margin-bottom: 24px;
      white-space: nowrap;
    }

    .ant-form-item-explain {
      margin: 8px 0 16px;
    }
  }
}

.packNo {
  position: relative;

  :global {
    .ant-form-item-extra {
      position: absolute;
      left: 310px;
      top: 8px;
      color: red;
      font-size: 12px;
    }
  }
}

.relativeTime {
  position: relative;

  &::before {
    content: '*';
    color: #ff4d4f;
    margin-right: 4px;
    position: absolute;
    left: -12px;
    top: 11px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
  }
}

.mt {
  :global {
    .ant-form-item-label {
      width: 108px;
      text-align: right;

      &>label {
        margin-top: -4px;

      }
    }
  }
}