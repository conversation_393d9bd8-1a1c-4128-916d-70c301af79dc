import React, { useEffect, useState, useRef } from 'react';
// import styled from 'styled-components'

import { useSelector } from 'react-redux';
import {
  Table,
  message,
  Button,
  Popconfirm,
  Input,
  Row,
  Col,
  Divider,
  Tooltip,
  Dropdown,
  Menu,
  Modal,
  Spin,
} from 'antd';
import _ from 'lodash';
import './index.less';
import { get, post, axiosDelete } from '../../../utils/request';
import allUrl from '../../../utils/url';
import ExportModal from './ExportModal';

import moment from 'moment';
import { PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import PublicTableQuery from '@/components/Public/PublicTableQuery';
import PublicTable from '@/components/Public/PublicTable';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import { roleJudgment } from '@/utils/authority';
import { setStorage, getStorage } from '@/utils/Storage';
import { fileDown, calcPageNo } from '@/utils';
import styles from '../antd.module.less';
import publicQueryStyle from '../tableLayout.module.less';
import { Tag } from 'antd';

const getPageStorage = key => {
  let res = getStorage(window.location.hash, true);
  return res && res[key] ? res[key] : null;
};
const PackManagement = props => {
  const tableRef = useRef();
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [loading, setLoading] = useState(false);
  const [dataSource, changeDataSource] = useState([]);
  const [isCatch, setisCatch] = useState(true);
  const [current, changeCurrent] = useState(
    isCatch && getPageStorage('pagination') ? getPageStorage('pagination').current : 1
  );
  const [pageSize, changePageSize] = useState(
    isCatch && getPageStorage('pagination') ? getPageStorage('pagination').pageSize : 10
  );
  const [total, changeTotal] = useState(0);
  const [tableHeight, setTableHeight] = useState(0);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [DictData, setDictData] = useState([]);
  const [DictData4, setDictData4] = useState([]);
  const [showExport, setShowExport] = useState(false);
  const [powerModelList, setPowerModelList] = useState([]); // 动力方式名称接口

  // const getData = () =>{
  //     if (userInfo) {
  //         setLoading(true)
  //         let query = { ...defaultQuery }
  //         if (query.beginTime && query.beginTime.length) {
  //             query.activityBeginTime = moment(query.beginTime[0]).format('YYYY-MM-DD HH:mm:ss')
  //             query.activityEndTime = moment(query.beginTime[1]).format('YYYY-MM-DD HH:mm:ss')
  //             delete query.beginTime
  //         }
  //         console.log(query)
  //         if (query.createTime) {
  //             query.createTime = moment(query.createTime).format('YYYY-MM-DD')
  //         }
  //         let params = { pageNum: current, pageSize, ...query }
  //         post(allUrl.EquityPackageManage.queryEquityPack, { ...params }).then(res => {
  //             if (res.success && res.resp!==null ) {
  //                 res.resp.map((item, index) => item.key = index + 1)
  //                 changeDataSource(res.resp)
  //                 changeTotal(res.total)
  //             } else {
  //                 changeDataSource([])
  //                 changeTotal(0)
  //                 // message.error(res.msg)
  //             }
  //             setLoading(false)
  //         })
  //     }
  // }

  const PageChange = (current, pageSize) => {
    changeCurrent(current);
    changePageSize(pageSize);
    if (isCatch) {
      setStorage({
        Type: window.location.hash,
        pagination: { current, pageSize },
      });
    }
  };
  const resetPage = () => {
    changeCurrent(1);
    changePageSize(10);
  };
  const onSearch = values => {
    // 宣传时间
    if (values.beginTime && values.beginTime.length) {
      values.activityBeginTime = moment(values.beginTime[0]).format('YYYY-MM-DD HH:mm:ss');
      values.activityEndTime = moment(values.beginTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete values.beginTime;
    }
    // 活动时间
    if (values.promotionTime && values.promotionTime.length) {
      values.promotionStartTime = moment(values.promotionTime[0]).format('YYYY-MM-DD HH:mm:ss');
      values.promotionEndTime = moment(values.promotionTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete values.promotionTime;
    }
    if (values.createTime) {
      values.createTime = moment(values.createTime).format('YYYY-MM-DD');
    }
    setDefaultQuery(values);
    resetPage();
  };
  const initPage = flag => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let h = 0;
    if (!flag) {
      h = winH - 47 - 54 - 345;
    } else {
      h = winH - 47 - 54 - 400;
    }
    setTableHeight(h);
  };
  useEffect(() => {
    initPage();
  }, []);
  const LookAt = record => {
    let data = {
      id: record.id,
      packNo: record.packNo,
      Type: 'LookAt',
      title: '权益包详情',
    };
    UniversalOpenWindow({
      JumpUrl: '/RightCenter/PackManagementDetail',
      data,
      history: props.history,
    });
  };
  // 编辑跳转
  const EditAt = record => {
    let data = {
      id: record.id,
      packNo: record.packNo,
      Type: 'edit',
      title: '编辑权益包',
    };
    UniversalOpenWindow({
      JumpUrl: '/RightCenter/PackManagementCreate',
      data,
      history: props.history,
    });
  };

  useEffect(() => {
    get(allUrl.common.entryLists, {
      codes:
        'equity_attribute,equity_pack_status,equity_car_type,equity_pay_type,pack_sales_type,equity_promotion_status',
    }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }
        setDictData(Dt || {});
      } else {
        // message.error(res.message)
      }
    });
  }, []);
  //车辆市场类型 搜索下拉接口
  useEffect(() => {
    get(allUrl.common.powerModelList).then(res => {
      if (res.success) {
        res.resp.forEach(item => {
          item.name = item.selectValue;
          item.value = item.selectKey;
        });
        setPowerModelList(res.resp);
      }
    });
    post(allUrl.EquityPackageManage.getCarMarketTypeAll).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        Dt.forEach((item, index) => {
          item.key = index + 1;
          item.value = item.code;
        });
        setDictData4(Dt);
      } else {
        // message.error(res.message)
      }
    });
  }, []);

  //保存或发布校验包内权益明细是否更新弹窗
  const checkDetailUpdate = record => {
    return new Promise(async (resolve, reject) => {
      const detailCheckList = record.relationDetails.map(item => item.originDetail);
      const { success, resp } = await post(allUrl.EquityPackageManage.detailVersionCheck, {
        packNo: record.packNo,
        detailCheckList,
      });
      if (success) {
        if (resp.length > 0) {
          const _updateDetailNo = resp.map(item => item.detailNo);
          //弹出有更新的弹窗
          Modal.confirm({
            title: (
              <div style={{ wordBreak: 'break-all' }}>
                包内明细{_updateDetailNo.join(',')}的版本有更新，是否更新为最新的版本？
              </div>
            ),
            cancelText: '不更新',
            okText: '更新',
            width: 500,
            icon: <ExclamationCircleFilled />,
            bodyStyle: {
              lineHeight: '30px',
              paddingTop: '45px',
            },
            content: (
              <>
                若选择不更新，则权益包直接保存该权益明细当前所选的版本；
                <br />
                若选择更新，则该明细更新为最新的版本。
              </>
            ),
            closable: true,
            onOk: () => {
              //更新关联的权益数据
              let data = {
                id: record.id,
                packNo: record.packNo,
                Type: 'edit',
                title: '编辑权益包',
                updateDetails: resp, //包内关联的权益明细
              };
              UniversalOpenWindow({
                JumpUrl: '/RightCenter/PackManagementCreate',
                data,
                history: props.history,
              });
              resolve(true);
            },
            onCancel: close => {
              if (typeof close.name !== 'undefined' && close.name !== '') {
                //点击了"不更新"按钮，直接保存
                close();
                resolve(false);
              }
            },
          });
        } else {
          //直接保存
          resolve(false);
        }
      }
    });
  };
  //V21版本：基础权益包=》保存或发布，校验同一车型配置、同一车辆市场类型的基础权益包是否存在，如存在，则提示用户
  const checkSameConfigPack = async record => {
    return new Promise(async (resolve, reject) => {
      const { success, resp } = await post(allUrl.EquityPackageManage.queryEffectBasePack, {
        carType: record.carType, //权益包车型
        carMarketType: record.carMarketType, //车辆市场类型
        carConfigList: record.packCarConfigs, //车型配置
      });
      if (success) {
        if (resp[0].alreadyExist) {
          //弹出有更新的弹窗
          Modal.confirm({
            title: '提示',
            cancelText: '取消',
            okText: '确认',
            width: 500,
            icon: <ExclamationCircleFilled />,
            bodyStyle: {
              lineHeight: '30px',
              paddingTop: '45px',
            },
            content: (
              <>
                当前发布的权益包，与已发布的
                <span style={{ color: 'red' }}>相同车型配置、“{resp[0].basePackageList?.[0].carMarketTypeName}”</span>
                的基础权益包存在重复。请确认是否继续发布？
                <br />
                若确认发布，系统将自动以本次新发布的基础权益包为准，进行基础权益关联。
              </>
            ),
            closable: true,
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              resolve(false);
            },
          });
        } else {
          resolve(true);
        }
      } else {
        resolve(false);
      }
    });
  };
  // 发布 todo
  const handleRelease = record => {
    const { packNo } = record;
    Modal.confirm({
      title: '确定发布吗？',
      onOk: async close => {
        close();
        //校验包内明细是否有更新
        const isNeedUpdate = await checkDetailUpdate(record);
        if (isNeedUpdate) return;
        // 校验是否已存在相同配置的基础包,若存在点击取消则不发布，若不存在or存在点击确定，直接继续发布
        if (record.type === 1) {
          const isPublic = await checkSameConfigPack(record);
          if (!isPublic) return;
        }

        setLoading(true);
        const params = { id: record.id, packNo };
        post(allUrl.EquityPackageManage.publishEquityPack, { ...params }).then(res => {
          if (res.success) {
            message.success('发布成功！');
            tableRef.current.getTableData();
          } else {
            // message.error(res.msg)
          }
          setLoading(false);
        });
      },
      onCancel: () => {
        return false;
      },
    });
  };
  // 重新发布 todo
  const handleRedistribution = record => {
    const { packNo } = record;
    //接口调用
    Modal.confirm({
      title: '确定重新发布吗？',
      onOk: async close => {
        close();
        //校验包内明细是否有更新
        const isNeedUpdate = await checkDetailUpdate(record);
        if (isNeedUpdate) return;
        // 校验是否已存在相同配置的基础包,若存在点击取消则不发布，若不存在or存在点击确定，直接继续发布
        if (record.type === 1) {
          const isPublic = await checkSameConfigPack(record);
          if (!isPublic) return;
        }

        setLoading(true);

        const params = { id: record.id, packNo };

        const status = record.status;
        post(allUrl.EquityPackageManage.publishEquityPack, { ...params }).then(res => {
          if (res.success) {
            message.success('发布成功！');
            tableRef.current.getTableData();
          }
          setLoading(false);
        });
        // if (status !== 4) {
        //     post(allUrl.EquityPackageManage.publishEquityPack, { ...params }).then(res => {
        //         if (res.success) {
        //             message.success('发布成功！')
        //             tableRef.current.getTableData()
        //         } else {
        //             // message.error(res.msg)
        //         }
        //         setLoading(false)
        //     })
        // } else {
        //     message.error('当前发布时间不在宣传时间范围内，无法发布～')
        // }
      },
      onCancel: () => {
        return false;
      },
    });
  };
  //下架 todo
  const handleShelves = record => {
    const { packNo } = record;
    const params = { id: record.id, packNo };
    Modal.confirm({
      title: '确定下架吗？',
      onOk: () => {
        post(allUrl.EquityPackageManage.downEquityPack, { ...params }).then(res => {
          if (res.success) {
            message.success('下架成功！');
            tableRef.current.getTableData();
          } else {
            // message.error(res.msg)
          }
          setLoading(false);
        });
      },
      onCancel: () => {
        return false;
      },
    });
  };

  //删除权益包
  const handleDelete = async record => {
    const { packNo } = record;
    const { total, current, pageSize, setCurrent } = tableRef.current;
    const _current = calcPageNo(total, current, pageSize, 1);

    const { success } = await get(allUrl.EquityPackageManage.ableToDelete, { packNo });
    if (success) {
      Modal.confirm({
        title: '确定删除该权益包吗？删除后不可恢复！',
        onOk: async () => {
          const { success } = await axiosDelete(`${allUrl.EquityPackageManage.delete}/${packNo}`, {}, { isForm: true });
          if (success) {
            message.success('删除成功');
            if (_current < current) {
              //删除最后1页最后1条数据，查询table上一页数据
              setCurrent(_current);
            } else {
              tableRef.current.getTableData();
            }
          }
        },
        onCancel: () => {
          return false;
        },
      });
    }
  };

  const renderOperation = (text, record) => {
    return (
      <div style={{ color: '#1890ff', whiteSpace: 'nowrap' }}>
        <>
          {' '}
          {roleJudgment(userInfo, 'PACK_MANAGEMENT_DETAIL') ? (
            <Button type="link" size="small" onClick={() => LookAt(record)}>
              详情
            </Button>
          ) : null}
          <>
            {(record.status === 1 || record.status === 5 || record.status === 4) && (
              <>
                <Divider type="vertical" />
                {roleJudgment(userInfo, 'PACK_MANAGEMENT_EDIT') ? (
                  <Button type="link" size="small" onClick={() => EditAt(record)}>
                    编辑
                  </Button>
                ) : null}
              </>
            )}
            {record.status === 1 && (
              <>
                <Divider type="vertical" />
                {roleJudgment(userInfo, 'PACK_MANAGEMENT_RELEASE') ? (
                  <Button type="link" size="small" onClick={() => handleRelease(record)}>
                    发布
                  </Button>
                ) : null}
              </>
            )}
            {(record.status === 5 || record.status === 4) && (
              <>
                <Divider type="vertical" />
                {roleJudgment(userInfo, 'PACK_MANAGEMENT_RELEASETION') ? (
                  <Button type="link" size="small" onClick={() => handleRedistribution(record)}>
                    重新发布
                  </Button>
                ) : null}
              </>
            )}
            {/* 630  二手车禁止下架 */}
            {(record.status === 3 || record.status === 2) && (
              <>
                <Divider type="vertical" />
                {roleJudgment(userInfo, 'PACK_MANAGEMENT_DOWN') ? (
                  <Button type="link" size="small" disabled={record.type === 3} onClick={() => handleShelves(record)}>
                    下架
                  </Button>
                ) : null}
              </>
            )}
            {roleJudgment(userInfo, 'PACK_MANAGEMENT_DELETE') ? (
              <>
                <Divider type="vertical" />
                <Button
                  type="link"
                  danger
                  disabled={record.status !== 1}
                  size="small"
                  onClick={() => handleDelete(record)}
                >
                  删除
                </Button>
              </>
            ) : null}
          </>
        </>
      </div>
    );
  };

  const tablePrefix = ({ className, text }) => {
    return (
      <div className={`${styles['custom-mark']} ${styles[className]}`} style={{ top: 0, left: -16 }}>
        <div className={styles['custom-mark-content']}>
          <span>{text}</span>
        </div>
      </div>
    );
  };

  let searchList = [
    { label: '权益包名称', name: 'name', type: 'Input', placeholder: '请输入权益包名称', colSpan: 6 },
    { label: '权益包编码', name: 'packNo', type: 'Input', placeholder: '请输入权益包编码', colSpan: 6 }, // 630
    {
      label: '权益车型',
      name: 'carType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData && DictData['equity_car_type'] ? DictData['equity_car_type'] : [],
    },
    {
      label: '车辆市场类型',
      name: 'carMarketType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData4 || [],
    },
    {
      label: '车辆销售类型',
      name: 'packSalesType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData && DictData['pack_sales_type'] ? DictData['pack_sales_type'] : [],
    },
    {
      label: '权益包类型',
      name: 'type',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData && DictData['equity_pay_type'] ? _.cloneDeep(DictData['equity_pay_type']) : [],
    },
    {
      label: '宣传状态',
      name: 'status',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData && DictData['equity_pack_status'] ? DictData['equity_pack_status'] : [],
      // data: [
      //     { name: '全部', value: '' },
      //     { name: '草稿', value: '1' },
      //     { name: '已生效', value: '2' },
      //     { name: '未生效', value: '3' },
      //     { name: '已失效', value: '4' },
      //     { name: '已下架', value: '5' },
      // ]
    },
    {
      label: '活动状态',
      name: 'promotionStatus',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData && DictData['equity_promotion_status'] ? _.cloneDeep(DictData['equity_promotion_status']) : [],
      // data: [
    },
    {
      label: '权益包属性',
      name: 'attribute',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData && DictData['equity_attribute'] ? DictData['equity_attribute'] : [],
      // data: [
      //     { name: '全部', value: '' },
      //     { name: '正式订单', value: '2' },
      //     { name: '意向订单', value: '1' },
      // ]
    },
    { label: '权益明细编码', name: 'detailNo', type: 'Input', placeholder: '请输入权益明细编码', colSpan: 6 },
    {
      label: '活动时间',
      name: 'promotionTime',
      type: 'RangePicker',
      placeholder: ['开始时间', '截止时间'],
      colSpan: 6,
      paramProps: { beginTimeKey: 'promotionStartTime', endTimeKey: 'promotionEndTime' },
    },
    {
      label: '宣传时间',
      name: 'beginTime',
      type: 'RangePicker',
      placeholder: ['开始时间', '截止时间'],
      colSpan: 6,
      paramProps: { beginTimeKey: 'activityBeginTime', endTimeKey: 'activityEndTime' },
    },
    { label: '创建时间', name: 'createTime', type: 'DatePicker', placeholder: '请选择', colSpan: 6 },
    { label: '车型年款', name: 'carOfYear', type: 'Input', placeholder: '请输入车型年款', colSpan: 6 },
    {
      label: '动力方式',
      name: 'powerModelCode',
      type: 'Select',
      placeholder: '请输入动力方式',
      colSpan: 6,
      data: powerModelList || [],
    },
  ];

  const columns = [
    { title: '权益包编码', dataIndex: 'packNo', width: 120 },
    {
      title: '权益包名称',
      dataIndex: 'name',
      width: 200,
      render: text => {
        if (text) {
          return <div style={{ whiteSpace: 'nowrap' }}>{text}</div>;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '权益包属性',
      dataIndex: 'attributeName',
      width: 110,
      render: text => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '权益包类型',
      dataIndex: 'typeName',
      width: 120,
      render: text => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆销售类型',
      dataIndex: 'packSalesTypeName',
      width: 180,
      render: text => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆市场类型',
      dataIndex: 'carMarketTypeName',
      width: 120,
      render: text => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '活动时间',
      dataIndex: '',
      width: 270,
      render: record =>
        record.promotionStartTime && record.promotionEndTime ? (
          <>
            <span>{moment(record.promotionStartTime).format('YYYY年MM月DD日')}</span>~
            <span>{moment(record.promotionEndTime).format('YYYY年MM月DD日')}</span>
          </>
        ) : (
          '-'
        ),
    },
    {
      title: '宣传时间',
      dataIndex: '',
      width: 270,
      render: record =>
        record.activityBeginTime && record.activityEndTime ? (
          <>
            <span>{moment(record.activityBeginTime).format('YYYY年MM月DD日')}</span>~
            <span>{moment(record.activityEndTime).format('YYYY年MM月DD日')}</span>
          </>
        ) : (
          '-'
        ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 220,
      render: text => (text ? moment(text).format('YYYY年MM月DD日 HH:mm:ss') : '-'),
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      width: 220,
      render: text => (text ? moment(text).format('YYYY年MM月DD日 HH:mm:ss') : '—'),
    },
    {
      title: '权益车型',
      dataIndex: '',
      width: 110,
      render: record => {
        if (!record.packCarConfigs?.length) {
          return '-';
        }
        return handleData(record.packCarConfigs, 'carType').map(item => {
          return (
            <>
              <Tag color={item.carType == defaultQuery.carType ? 'blue' : ''}>{item.baseConfig}</Tag>
              <br />
            </>
          );
        });
      },
    },
    {
      title: '车型年款',
      dataIndex: '',
      width: 110,
      render: record => {
        if (!record.packCarConfigs?.length) {
          return '-';
        }
        return handleData(record.packCarConfigs, 'year').map(item => {
          return (
            <>
              <Tag color={item.carOfYear === defaultQuery.carOfYear ? 'blue' : ''}>{item.carOfYear}</Tag>
            </>
          );
        });
      },
    },
    {
      title: '动力方式',
      dataIndex: '',
      width: 120,
      render: record => {
        if (!record.packCarConfigs?.length) {
          return '-';
        }
        return handleData(record.packCarConfigs, 'power').map(item => {
          //如果筛选条件没有年份，只需要动力方式匹配就高亮
          //如果筛选有年份，需要年份和动力方式都匹配
          let colorFlag =
            item.powerModelCode === defaultQuery.powerModelCode &&
            (defaultQuery.carOfYear === '' || item.carOfYear === defaultQuery.carOfYear);
          return (
            <>
              <Tag color={colorFlag ? 'blue' : ''}>{item.carOfYear + '/' + item.powerModel}</Tag>
            </>
          );
        });
      },
    },
    {
      title: '权益明细编码',
      dataIndex: 'relationDetails',
      width: 120,
      className: 'pack-table-merge-column',
      render: text => {
        if (text) {
          return (
            text && (
              <div className="pack-table-merge-wrap">
                {text.map((item, index) => {
                  return (
                    <>
                      {/* 明细信息 */}
                      <div className="pack-table-row">
                        {item.originDetail?.businessScene === 2
                          ? tablePrefix({ className: 'bg2', text: '延保' })
                          : null}
                        <div className="detai">{item.originDetail?.detailNo}</div>
                        {/* <div className='pack-table-line-bottom' style={{ marginLeft: -17, marginRight: -17, marginTop: 15 }}><Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} /></div> */}
                      </div>
                      {/* {index !== text.length - 1 && <div style={{ marginLeft: -17, marginRight: -17, marginTop: 15 }}><Divider style={{ marginTop: 0, backgroundColor: '#EFEFEF' }} /></div>} */}
                      {item.upgradeDetail ? (
                        // 升级明细信息
                        <div className="pack-table-row">
                          {tablePrefix({ className: 'bg1', text: '已升级' })}
                          <div className="detai">{item.upgradeDetail.detailNo}</div>
                          {/* <div className='pack-table-line-bottom' style={{ marginLeft: -17, marginRight: -17, marginTop: 15 }}><Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} /></div > */}
                        </div>
                      ) : null}
                    </>
                  );
                })}
              </div>
            )
          );
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '权益明细名称',
      dataIndex: 'relationDetails',
      width: 180,
      className: 'pack-table-merge-column',
      render: text => {
        if (text) {
          return (
            text && (
              <div className="pack-table-merge-wrap">
                {text.map((item, index) => {
                  return (
                    <>
                      {/* 明细信息 */}
                      <div className="pack-table-row">
                        <div style={{ whiteSpace: 'nowrap' }} className="detai">
                          {item.originDetail?.name}
                        </div>
                        {/* <div className='pack-table-line-bottom' style={{ marginLeft: -17, marginRight: -17, marginTop: 15 }}><Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} /></div> */}
                      </div>
                      {item.upgradeDetail ? (
                        // 升级明细信息
                        <div className="pack-table-row">
                          <div style={{ whiteSpace: 'nowrap' }} className="detai">
                            {item.upgradeDetail?.name}
                          </div>
                          {/* <div className='pack-table-line-bottom' style={{ marginLeft: -17, marginRight: -17, marginTop: 15 }}><Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} /></div> */}
                        </div>
                      ) : null}
                    </>
                  );
                })}
              </div>
            )
          );
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '宣传状态',
      dataIndex: 'statusName',
      width: 110,
      render: text => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '活动状态',
      dataIndex: 'promotionStatusName',
      width: 110,
      render: text => {
        return text || '-';
      },
    },
    {
      title: '操作',
      width: 230,
      fixed: 'right',
      dataIndex: 'Operation',
      render: (text, record) => renderOperation(text, record),
    },
  ];

  //导出弹窗
  const onExportHandle = () => {
    setShowExport(true);
  };

  const handleCancel = () => {
    setShowExport(false);
  };

  //导出弹窗提交
  const submitHandle = data => {
    setLoading(true);
    post(allUrl.EquityPackageManage.packageExport, { ...defaultQuery, ...{ fields: data } }, { responseType: 'blob' })
      .then(res => {
        if (res) {
          fileDown(res, '权益包管理');
          setLoading(false);
        } else {
          setLoading(false);
        }
      })
      .catch(err => {
        setLoading(false);
      });
  };

  //车型配置数据处理
  const handleData = (data, dataType) => {
    var temp = [];
    const tmplData = data || [];
    if (!!tmplData.length) {
      tmplData.forEach(item => {
        var check = temp.every(function (b) {
          if (dataType === 'carType') {
            return item.carType !== b.carType;
          }
          //如果是需要车型年款，只需要比对年份不同
          if (dataType === 'year') {
            return item.carOfYear !== b.carOfYear;
          }
          //如果需要动力方式，只要年份或者动力方式一项不同就是不同项
          return item.carOfYear !== b.carOfYear || item.powerModelCode !== b.powerModelCode;
        });
        check && temp.push(item);
      });
    }
    return temp;
  };

  return (
    <Spin spinning={loading}>
      <div className={`pack-PublicList ${publicQueryStyle.PublicList}`}>
        <PublicTableQuery isCatch={true} isFormDown={false} onSearch={onSearch} searchList={searchList} />
        <div className="tableData">
          <Row className="tableTitle">
            <Col className="text" style={{ color: '#000000', fontSize: 20 }}>
              <span style={{ marginRight: '20px' }}>权益包列表</span>
              {roleJudgment(userInfo, 'PACK_MANAGEMENT_EXPORT') ? (
                <Button type="primary" onClick={onExportHandle}>
                  导出
                </Button>
              ) : null}
            </Col>
            {roleJudgment(userInfo, 'PACK_MANAGEMENT_BUILD') ? (
              <Col className="bts">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    let data = {
                      clueId: 0,
                      Type: 'add',
                      title: '创建权益包',
                    };
                    UniversalOpenWindow({
                      JumpUrl: '/RightCenter/PackManagementCreate',
                      data,
                      history: props.history,
                    });
                  }}
                >
                  创建
                </Button>
              </Col>
            ) : null}
          </Row>
          <Row className="tableTitle" style={{ paddingTop: '14px' }}>
            <Col className="text"></Col>
            <Col className="bts" style={{ color: 'red', fontSize: 14, paddingRight: 8 }}>
              注意：如需对权益包内容编辑，需要先下架才可以编辑
            </Col>
          </Row>
          <PublicTable
            isCatch={true}
            ref={tableRef}
            type={6}
            rowSelection={false}
            defaultQuery={defaultQuery}
            url={allUrl.EquityPackageManage.queryEquityPack}
            columns={columns}
            className={styles['blue-table']}
          />
        </div>
        <ExportModal showExport={showExport} handleCancel={handleCancel} submitHandle={submitHandle} />
      </div>
    </Spin>
  );
};
export default PackManagement;
