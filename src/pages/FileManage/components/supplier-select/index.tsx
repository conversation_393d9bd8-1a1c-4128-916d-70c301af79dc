import { Select, SelectProps } from 'antd';
import React, { FC } from 'react';
import { useOrgSuppliers } from '../../hooks';

const SupplierSelect: FC<Omit<SelectProps<string, FileManage.SupplierOrganization>, 'options'>> = (props) => {
  const { data, loading } = useOrgSuppliers();
  return (
    <Select
      loading={loading}
      {...props}
      options={data}
      fieldNames={{ value: 'code', label: 'name' }}
      optionFilterProp="name"
    />
  );
};

export default SupplierSelect;
