.batch-PublicList{
  .detail-overflow{
      height: calc(100vh - 48px - 54px - 80px);
      overflow-y: auto;
  }
  .flex{
      display:flex;
      align-items: center;
  }
  .border-bottom{
      border-bottom: solid 1px #e8e8e8;
  }
  .tableTitle{
      background-color: white;
      padding: 16px 0px 16px 32px;
      justify-content: space-between;
     
      .text{
          font-size: 20px;
          font-family: PingFangSC, PingFangSC-Medium;
          font-weight: 500;
          text-align: left;
          color: rgba(0,0,0,0.85);
          line-height: 28px;
      }
      .bts{
          .ant-btn{
              margin:0 7px;
          }
      }
  }
  .box-shadow{
        box-shadow: 0px -2px 6px 0px rgba(0,0,0,0.08);
        z-index: 1000;
  }
    .tableData{
        padding: 24px;
        background: #f0f2f5;
        .table-top{
            background-color: #d6ebff;
            padding: 13px 0 0 22px;
            height: 50px;
            line-height: 50px;
            .top-text{
                font-size: 16px;
                font-weight: bold;
                line-height: 50px;
            }
            .red-text{
                color: red;
                margin-left: 8px;
            }
        }
        .table-bottom{
            background-color: #fff;
            padding: 32px 0 12px;
            .form-item{
                margin-bottom: 20px;
            }
            .padding-left{
                padding-left: 24px;
            }
        }
          .info-box{
              background: white;
              padding:24px;
          }
        .detailPanel{
          padding:24px 24px 0 24px;
          background-color: white;
        }
        .marg{
            margin-top:24px
        }
        .ant-table-wrapper{
            background-color: white;
            .ant-table{
                padding: 20px 24px;
            }
            .ant-table-pagination{
                margin: 16px 24px;
            }
        }
    }
      .PublicList_FormQuery{
          padding-top: 16px;
          padding-left: 24px;
          .ant-col-7{
              .ant-form-item{
                  .ant-form-item-control-input{
                      width: 90%;
                      .ant-form-item-control-input-content{
                          .ant-picker{
                              width: 100%;
                          }
                      }
                  }
              }
          }
          .FormQuerySubmit{
              display:flex;
              justify-content: flex-end;
              .operationButtons{
                  span{
                      color: #1890ff;
                      cursor: pointer;
                      .anticon{
                          margin-left: 6px;
                      }
                  }
              }
          }
      }
  }