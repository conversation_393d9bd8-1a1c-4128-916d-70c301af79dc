export const routeMap = [
  {
    title: '首页',
    component: 'pages/home',
    path: '/home',
    children: [],
  },
  {
    title: '登录测试',
    component: 'pages/thirdLogin',
    path: '/thirdLogin',
    children: [],
  },
  {
    title: '暂未开放',
    component: 'pages/NotOpenYet',
    path: '/NotOpenYet',
    children: [],
  },
  {
    title: '外部系统跳入本系统',
    component: 'pages/Transfer',
    path: '/Transfer',
    children: [],
  },
  {
    title: '404',
    component: 'pages/Error404',
    path: '/error404',
    children: [],
  },
  {
    title: '403',
    component: 'pages/Error403',
    path: '/error403',
    children: [],
  },
  {
    title: '500',
    component: 'pages/Error500',
    path: '/error500',
    children: [],
  },
  {
    title: '账号权限管理',
    component: '',
    path: '/AuthorityManagement',
    children: [
      {
        title: '账号管理',
        component: 'pages/AuthorityManagement/UserManagement',
        path: '/AuthorityManagement/UserManagement',
        children: [],
        catch: false,
      },
      {
        title: '账号管理详情',
        component: 'pages/AuthorityManagement/UserManagement/Detail',
        path: '/AuthorityManagement/UserManagementDetail/:data',
        children: [],
        catch: false,
      },
      {
        title: '菜单管理',
        component: 'pages/AuthorityManagement/MenuManagement',
        path: '/AuthorityManagement/MenuManagement',
        children: [],
        catch: false,
      },
      {
        title: '菜单管理详情',
        component: 'pages/AuthorityManagement/MenuManagement/Detail',
        path: '/AuthorityManagement/MenuManagementDetail/:data',
        children: [],
        catch: false,
      },
      {
        title: '组织管理',
        component: 'pages/AuthorityManagement/OrgManagement',
        path: '/AuthorityManagement/OrgManagement',
        children: [],
        catch: false,
      },
      {
        title: '组织管理详情',
        component: 'pages/AuthorityManagement/OrgManagement/Detail',
        path: '/AuthorityManagement/OrgManagementDetail/:data',
        children: [],
        catch: false,
      },
      {
        title: '角色管理',
        component: 'pages/AuthorityManagement/RoleManagement',
        path: '/AuthorityManagement/RoleManagement',
        children: [],
        catch: false,
      },
      {
        title: '角色管理详情',
        component: 'pages/AuthorityManagement/RoleManagement/Detail',
        path: '/AuthorityManagement/RoleManagementDetail/:data',
        children: [],
        catch: false,
      },
      {
        title: '接口权限管理',
        component: 'pages/AuthorityManagement/InterfaceAuthority',
        path: '/AuthorityManagement/InterfaceAuthority',
        children: [],
        catch: false,
      },
      {
        title: '接口权限管理详情',
        component: 'pages/AuthorityManagement/InterfaceAuthority/Detail',
        path: '/AuthorityManagement/InterfaceAuthority/:data',
        children: [],
        catch: false,
      },
      {
        title: '第三方账号管理',
        component: 'pages/AuthorityManagement/OtherAccountManage',
        path: '/AuthorityManagement/OtherAccountManage',
        children: [],
        catch: false,
      },
      // {
      //     title: "接口权限管理详情",
      //     component: "pages/AuthorityManagement/InterfaceAuthority/Detail",
      //     path: "/AuthorityManagement/InterfaceAuthority/:data",
      //     children: [],
      //     catch:false
      // }
    ],
  },
  {
    title: '补贴',
    component: '',
    path: '/OvertimeReturn',
    children: [
      {
        title: '超时订单',
        component: 'pages/OvertimeReturn/OvertimeOrder',
        path: '/OvertimeReturn/OvertimeOrder',
        children: [],
        catch: false,
      },
      {
        title: '超时订单详情',
        component: 'pages/OvertimeReturn/OvertimeOrder/Detail',
        path: '/OvertimeReturn/OvertimeOrder/:data',
        children: [],
        catch: false,
      },
    ],
  },
  {
    title: '线索管理',
    component: '',
    path: '/ClueManagement',
    children: [
      {
        title: '原始线索',
        component: 'pages/ClueManagement/PrimaryClue',
        path: '/ClueManagement/PrimaryClue',
        children: [],
        catch: false,
      },
      {
        title: '唯一线索',
        component: 'pages/ClueManagement/ClueList',
        path: '/ClueManagement/ClueList',
        children: [],
        catch: true,
      },
      {
        title: '唯一线索详情',
        component: 'pages/ClueManagement/ClueList/Detail',
        path: '/ClueManagement/ClueList/:data',
        children: [],
        catch: false,
      },
      {
        title: '异地线索',
        component: 'pages/ClueManagement/AllogeneicClue',
        path: '/ClueManagement/AllogeneicClue',
        children: [],
        catch: false,
      },
    ],
  },
  {
    title: '门店管理',
    component: '',
    path: '/StoreManage',
    children: [
      {
        title: '门店列表',
        component: 'pages/StoreManage/StoreList',
        path: '/StoreManage/StoreList',
        children: [],
        catch: false,
      },
      {
        title: '门店员工管理',
        component: 'pages/StoreManage/StaffManagement',
        path: '/StoreManage/StaffManagement',
        children: [],
        catch: false,
      },
      {
        title: '创建用户',
        component: 'pages/StoreManage/StaffManagement/CreateUser',
        path: '/StoreManage/StaffManagementCreate/:data',
        children: [],
        catch: false,
      },
      {
        title: '用户详情',
        component: 'pages/StoreManage/StaffManagement/UserDetail',
        path: '/StoreManage/StaffManagementDetail/:data',
        children: [],
        catch: false,
      },
      {
        title: '门店项目管理列表',
        component: 'pages/StoreManage/StoreProject',
        path: '/StoreManage/StoreProject',
        children: [],
        catch: false,
      },
      {
        title: '门店项目管理-状态编辑',
        component: 'pages/StoreManage/StoreProject/detail',
        path: '/StoreManage/StoreProject1/edit/:data',
        children: [],
        catch: false,
      },
      {
        title: '门店项目管理-进度编辑',
        component: 'pages/StoreManage/StoreProject/progressEditing',
        path: '/StoreManage/StoreProject2/progressEdit/:data',
        children: [],
        catch: false,
      },
      {
        title: '门店项目管理-项目经理设置',
        component: 'pages/StoreManage/StoreProject/projectManagerSettings',
        path: '/StoreManage/StoreProject2/projectManagerSettings/:data',
        children: [],
        catch: false,
      },
      {
        title: '培训标准设置',
        component: 'pages/StoreManage/StoreProject/trainStandardSettings',
        path: '/StoreManage/StoreProject2/trainStandardSettings/:data',
        children: [],
        catch: false,
      },
      {
        title: '门店位置管理',
        component: 'pages/StoreManage/StorePosition',
        path: '/StoreManage/StorePosition',
        children: [],
        catch: false,
      },
    ],
  },
  {
    title: '板车物流',
    path: '/truckLogistics',
    children: [
      {
        title: '进场组板列表',
        component: 'pages/truckLogistics/EnterGroupList',
        path: '/truckLogistics/EnterGroupList',
        catch: false,
      },
      {
        title: '承原单列表',
        component: 'pages/truckLogistics/CarrierList',
        path: '/truckLogistics/CarrierList',
        catch: false,
      },
      {
        title: '承运单详情',
        component: 'pages/truckLogistics/CarrierList/Detail',
        path: '/truckLogistics/CarrierListDetail/:data',
        catch: false,
      },
      {
        title: '司机与板车管理',
        component: 'pages/truckLogistics/DriverAndCarManage',
        path: '/truckLogistics/truckLogistics/DriverAndCarManage',
        catch: true,
      },
    ],
  },
  {
    title: '订单管理',
    path: '/OrderManage',
    children: [
      {
        title: '订单列表',
        component: 'pages/OrderManage/OrderList',
        path: '/OrderManage/OrderList',
        catch: false,
      },
    ],
  },
  // {
  //     title: "消息管理",
  //     path: "/messageManage",
  //     children: [
  //         {
  //             title: "企业微信消息推送",
  //             component: "pages/MessageManage/QWMessagePush",
  //             path: "/messageManage/QWMessagePush",
  //             catch:false
  //         },
  //     ]
  // },
  {
    title: '权益中心',
    path: '/RightCenter',
    children: [
      {
        title: '权益包管理',
        component: 'pages/RightCenter/PackManagement',
        path: '/RightCenter/PackManagement',
        catch: false,
      },
      {
        title: '权益包详情',
        component: 'pages/RightCenter/PackManagement/Detail',
        path: '/RightCenter/PackManagementDetail/:data',
        catch: false,
      },
      {
        title: '创建权益包',
        component: 'pages/RightCenter/PackManagement/Create',
        path: '/RightCenter/PackManagementCreate/:data',
        catch: false,
      },
      {
        title: '权益明细管理',
        component: 'pages/RightCenter/DetailManagement',
        path: '/RightCenter/DetailManagement',
        catch: false,
      },
      {
        title: '权益明细详情',
        component: 'pages/RightCenter/DetailManagement/Detail',
        path: '/RightCenter/DetailManagementDetail/:data',
        catch: false,
      },
      {
        title: '创建权益明细',
        component: 'pages/RightCenter/DetailManagement/Create',
        path: '/RightCenter/DetailManagementCreate/:data',
        catch: false,
      },
      {
        title: '权益账户管理',
        component: 'pages/RightCenter/AccountManagement',
        path: '/RightCenter/AccountManagement',
        catch: false,
      },
      //权益账户管理-400专用
      {
        title: '客服权益账户查看',
        component: 'pages/RightCenter/AccountManagementToClient',
        path: '/RightCenter/AccountManagement/client',
        catch: false,
      },
      {
        title: '权益账户信息',
        component: 'pages/RightCenter/AccountManagement/Detail',
        path: '/RightCenter/AccountManagementDetail/:data',
        catch: false,
      },
      {
        title: '批量关联',
        component: 'pages/RightCenter/AccountManagement/Batch',
        path: '/RightCenter/AccountManagementBatch',
        catch: false,
      },
      // 车型配置维护
      {
        title: '车型配置维护',
        component: 'pages/RightCenter/CarConfigMaintain',
        path: '/RightCenter/CarConfigMaintain',
        catch: false,
      },
      // 业务编码维护
      {
        title: '业务编码维护',
        component: 'pages/RightCenter/BusinessCodeMaintain',
        path: '/RightCenter/BusinessCodeMaintain',
        catch: false,
      },
      // 配件管理
      {
        title: '配件管理',
        component: 'pages/RightCenter/PartsManagement',
        path: '/RightCenter/PartsManagement',
        catch: false,
      },
      // 车辆市场类型
      {
        title: '车辆市场类型',
        component: 'pages/RightCenter/CarMarketType',
        path: '/RightCenter/CarMarketType',
        catch: false,
      },
      {
        title: '精品权益管理',
        component: 'pages/RightCenter/GoodRightManagement',
        path: '/RightCenter/GoodRightManagement',
        catch: false,
      },
      // 下载中心
      {
        title: '下载中心',
        component: 'pages/RightCenter/DownloadCenter',
        path: '/RightCenter/DownloadCenter',
        catch: false,
      },
      // 数据导入
      {
        title: '数据导入',
        component: 'pages/RightCenter/ExcelImport',
        path: '/RightCenter/ExcelImport',
        catch: false,
      },
    ],
  },
  {
    title: '企业微信',
    path: '/wecom',
    children: [
      {
        title: '消息推送',
        component: 'pages/WeCom/QWMessagePush',
        path: '/wecom/QWMessagePush',
        catch: false,
      },
      {
        title: '管家群',
        component: 'pages/WeCom/ButlerGroup',
        path: '/wecom/butlerGroup',
        catch: false,
      },
      {
        title: '管家群成员',
        component: 'pages/WeCom/ButlerGroup/GroupMembers',
        path: '/wecom/groupMembers/:data',
        catch: false,
      },
      {
        title: '客户',
        component: 'pages/WeCom/Customer',
        path: '/wecom/customer',
        catch: false,
      },
      {
        title: '数据统计',
        component: 'pages/WeCom/DataStatistics',
        path: '/wecom/datastatistics',
        catch: false,
      },
      {
        title: '服务监督',
        component: 'pages/WeCom/ChatRecord',
        path: '/wecom/chatRecord',
        catch: false,
      },
      {
        title: '企微客服',
        component: 'pages/WeCom/CustomerService',
        path: '/wecom/customerService',
        catch: false,
      },
    ],
  },
  {
    title: '车主活动',
    path: '/ownerActivity',
    children: [
      {
        title: '车主福利券',
        component: 'pages/ownerActivity/securit',
        path: '/ownerActivity/securit',
        catch: false,
      },
    ],
  },
  {
    title: '闪电交付',
    path: '/lightning',
    children: [
      {
        title: '锁车列表',
        component: 'pages/Lightning/Lock',
        path: '/Lightning/Lock',
        catch: false,
      },
    ],
  },
  {
    title: '合作伙伴',
    path: '/Partner',
    children: [
      {
        title: '合作伙伴管理',
        component: 'pages/Partner/list',
        path: '/Partner/list',
        catch: false,
      },
      {
        title: '合作伙伴员工管理',
        component: 'pages/Partner/staff',
        path: '/Partner/staff',
        catch: false,
      },
    ],
  },
  {
    title: '文件管理',
    path: '/FileManage',
    children: [
      {
        title: '文件管理',
        component: 'pages/FileManage/FileList',
        path: '/FileManage/FileList',
      },
      {
        title: '文件详情',
        component: 'pages/FileManage/FileDetail',
        path: '/FileManage/FileDetail/:id',
      },
      {
        title: '预览文件',
        component: 'pages/FileManage/FileView',
        path: '/FileManage/FileView/:id',
      },
      {
        title: '常用岗位设置',
        component: 'pages/FileManage/CommonJob',
        path: '/FileManage/CommonJob',
      },
      {
        title: '总部人员管理',
        component: 'pages/FileManage/Headquarters',
        path: '/FileManage/Headquarters',
      },
      {
        title: '总部部门设置',
        component: 'pages/FileManage/Division',
        path: '/FileManage/Division',
      },
      {
        title: '总部岗位设置',
        component: 'pages/FileManage/Position',
        path: '/FileManage/Position',
      },
    ],
  },
  {
    title: '水印管理',
    path: '/Watermark',
    children: [
      {
        title: '水印溯源',
        component: 'pages/Watermark/Trace',
        path: '/Watermark/Trace',
      },
    ],
  },
];
