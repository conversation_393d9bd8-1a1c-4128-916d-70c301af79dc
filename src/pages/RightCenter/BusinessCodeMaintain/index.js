import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Table, message, Button, Row, Col, Divider } from 'antd';
import PublicTableQuery from '@/components/Public/PublicTableQuery';
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { roleJudgment } from '@/utils/authority';
import moment from 'moment';
import { PlusOutlined } from '@ant-design/icons';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import BusinessCodeDetail from './BusinessCodeDetail';
import AddCodeInfo from './AddCodeInfo';
import RelatedInfo from './RelatedInfo';
import styles from '../antd.module.less';
import publicQueryStyle from '../tableLayout.module.less';

const BusinessCodeMaintain = props => {
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [loading, setLoading] = useState(false);
  const [dataSource, changeDataSource] = useState([]);
  const [current, changeCurrent] = useState(1);
  const [pageSize, changePageSize] = useState(10);
  const [total, changeTotal] = useState(0);
  const [tableHeight, setTableHeight] = useState(0);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [sortCodeList, setSortCode] = useState([]);
  const [ManageInterests, setManageInterests] = useState([]);
  const [DictData, setDictData] = useState([]);
  const [codeInfoSource, changeCodeInfo] = useState(null);
  const [isModalVisibleDetail, setIsModalVisibleDetail] = useState(false);
  const [detailInfoSource, setDetailInfoSource] = useState([]);
  const [isCodeModalVisible, setIsCodeModalVisible] = useState(false);
  const [isRelatedModalVisible, setIsRelatedModalVisible] = useState(false);
  const [flag, setFlag] = useState(null);

  const BUSINESS_CODE_TYPE = [
    {
      name: '工时',
      value: 1,
    },
    {
      name: '配件',
      value: 2,
    },
    {
      name: '精品安装',
      value: 3,
    },
  ];

  useEffect(() => {
    if (userInfo) {
      setLoading(true);
      let query = { ...defaultQuery };
      let params = { pageNum: current, pageSize, ...query };
      post(allUrl.BusinessCodeMaintain.queryBusinessCodeList, { ...params }).then(res => {
        if (res.success && res.resp !== null) {
          res.resp.map((item, index) => (item.key = index + 1));
          changeDataSource(res.resp);
          changeTotal(res.total);
        } else {
          changeDataSource([]);
          changeTotal(0);
          // message.error(res.msg)
        }
        setLoading(false);
      });
    }
  }, [defaultQuery, userInfo, current, pageSize]);

  const reloadList = () => {
    if (userInfo) {
      setLoading(true);
      let query = { ...defaultQuery };
      let params = { pageNum: current, pageSize, ...query };
      post(allUrl.BusinessCodeMaintain.queryBusinessCodeList, { ...params }).then(res => {
        if (res.success && res.resp !== null) {
          res.resp.map((item, index) => (item.key = index + 1));
          changeDataSource(res.resp);
          changeTotal(res.total);
        } else {
          changeDataSource([]);
          changeTotal(0);
          // message.error(res.msg)
        }
        setLoading(false);
      });
    }
  };

  const PageChange = (current, pageSize) => {
    changeCurrent(current);
    changePageSize(pageSize);
  };
  const resetPage = () => {
    changeCurrent(1);
    changePageSize(10);
  };
  const onSearch = values => {
    setDefaultQuery(values);
    resetPage();
  };
  const initPage = flag => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let h = 0;
    if (!flag) {
      h = winH - 47 - 54 - 345;
    } else {
      h = winH - 47 - 54 - 400;
    }
    setTableHeight(h);
  };
  useEffect(() => {
    initPage();
  }, []);
  //查看详情
  const LookAt = record => {
    setIsModalVisibleDetail(true);
    setDetailInfoSource(record); //跟文凯沟通查询详情取列表数据就行，优化后的逻辑导致查询逻辑有问题
  };
  // 关联弹窗
  // const RelateInfo = (record) => {
  //     setIsRelatedModalVisible(true)
  //     let params = {
  //         detailId: record.detailId
  //     }
  //     get(allUrl.BusinessCodeMaintain.getBusinessCodeDetail, { ...params }).then(res => {
  //         if (res.success) {
  //             res.resp.map((item, index) => item.key = index + 1)
  //             changeCodeInfo(res.resp[0])
  //         } else {
  //             // message.error(res.msg)
  //         }
  //         setLoading(false)
  //     })
  // }
  // 创建
  const AddBusinessCode = record => {
    setFlag('add');
    setIsCodeModalVisible(true);
    changeCodeInfo();
  };

  // 编辑
  const EditAt = record => {
    setFlag('edit');
    setIsCodeModalVisible(true);
    changeCodeInfo(record); //跟文凯沟通查询详情取列表数据就行，优化后的逻辑导致查询逻辑有问题
  };

  const handleCancel = () => {
    setIsCodeModalVisible(false);
    setIsModalVisibleDetail(false);
    // setIsRelatedModalVisible(false)
  };

  //查询业务分类编码枚举值
  const getbusinessCodeTypeName = value => {
    return BUSINESS_CODE_TYPE.filter(item => item.value === value)[0]?.name;
  };

  let searchList = [
    { label: '业务编码编号', name: 'businessCode', type: 'Input', placeholder: '请输入业务编码编号', colSpan: 6 },
    { label: '业务编码名称', name: 'businessCodeName', type: 'Input', placeholder: '请输入业务编码名称', colSpan: 6 },
    { label: '权益明细编码', name: 'detailNo', type: 'Input', placeholder: '请输入权益明细编码', colSpan: 6 },
    { label: '权益明细名称', name: 'detailName', type: 'Input', placeholder: '请输入权益明细名称', colSpan: 6 },
    {
      label: '业务编码分类',
      name: 'businessCodeType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: BUSINESS_CODE_TYPE || [],
    },
  ];
  const InforData = {
    rowKey: record => record.key,
    bordered: true,
    dataSource,
    loading,
    scroll: { x: 'max-content' },
    columns: [
      {
        title: '序号',
        dataIndex: 'index',
        fixed: 'left',
        width: 70,
        render: (text, record, index) => (
          <div style={{ textAlign: 'left' }}>{(current - 1) * pageSize + index + 1}</div>
        ),
      },
      {
        title: '业务编码编号',
        dataIndex: 'businessCode',
        width: 100,
        render: text => text || '—',
      },
      {
        title: '业务编码名称',
        dataIndex: 'businessCodeName',
        width: 260,
        render: text => text || '—',
      },

      {
        title: '权益明细编码',
        dataIndex: 'detailNo',
        width: 260,
        render: text => text || '—',
      },
      {
        title: '权益明细名称',
        dataIndex: 'detailName',
        width: 260,
        render: text => text || '—',
      },
      {
        title: '业务编码分类',
        dataIndex: 'businessCodeType',
        width: 120,
        render: text => {
          if (text) {
            return getbusinessCodeTypeName(text);
          } else {
            return '—';
          }
        },
      },
      {
        title: '操作',
        width: 120,
        fixed: 'right',
        dataIndex: 'Operation',
        render: (text, record) => (
          <div style={{ whiteSpace: 'nowrap' }}>
            {roleJudgment(userInfo, 'BUSINESS_CODE_MAINTAIN_DETAIL') ? (
              <Button type="link" size="small" onClick={() => LookAt(record)}>
                详情
              </Button>
            ) : null}
            {roleJudgment(userInfo, 'BUSINESS_CODE_MAINTAIN_DETAIL') &&
            roleJudgment(userInfo, 'BUSINESS_CODE_MAINTAIN_EDIT') ? (
              <Divider type="vertical" />
            ) : null}
            {roleJudgment(userInfo, 'BUSINESS_CODE_MAINTAIN_EDIT') ? (
              <Button type="link" size="small" onClick={() => EditAt(record)}>
                编辑{' '}
              </Button>
            ) : null}
          </div>
        ),
      },
    ],
    pagination: {
      pageSize: pageSize,
      onChange: PageChange,
      current: current,
      total: total,
      showTotal: () => `共${total}条，${pageSize}条/页`,
      showSizeChanger: true,
      showQuickJumper: true,
      onShowSizeChange: PageChange,
    },
    rowSelection: null,
  };
  return (
    <div className={publicQueryStyle.PublicList}>
      <PublicTableQuery isFormDown={false} onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery} />
      {/* <FormQuery
                initPage={initPage}
                onSearch={onSearch}
                resetPage={resetPage}
                searchList={searchList}
                defaultQuery={defaultQuery}
            /> */}
      <div className="tableData">
        <Row className="tableTitle">
          <Col className="text" style={{ color: 'black', fontSize: 20 }}>
            业务编码列表
          </Col>
          {roleJudgment(userInfo, 'BUSINESS_CODE_MAINTAIN_BUILD') ? (
            <Col className="bts">
              <Button type="primary" icon={<PlusOutlined />} onClick={AddBusinessCode}>
                创建
              </Button>
            </Col>
          ) : null}
        </Row>
        <div style={{ position: 'relative' }}>
          <Table {...InforData} className={styles['blue-table']} />
          <p style={{ position: 'absolute', bottom: 50, left: 32, color: '#ff4d4f' }}>代驾服务业务编码需要维护白名单</p>
        </div>
      </div>
      {isCodeModalVisible && (
        <AddCodeInfo
          handleCancel={handleCancel}
          visible={isCodeModalVisible}
          codeInfoSource={codeInfoSource}
          reloadList={reloadList}
          flag={flag}
          BUSINESS_CODE_TYPE={BUSINESS_CODE_TYPE}
        />
      )}
      {isModalVisibleDetail && (
        <BusinessCodeDetail
          handleCancel={handleCancel}
          visible={isModalVisibleDetail}
          detailInfoSource={detailInfoSource}
          getbusinessCodeTypeName={getbusinessCodeTypeName}
        />
      )}
      {/* {isRelatedModalVisible &&
                <RelatedInfo
                    handleCancel={handleCancel}
                    visible={isRelatedModalVisible}
                    codeInfoSource={codeInfoSource}
                    onSearch={onSearch}
                    reloadList={reloadList}
                />
            } */}
    </div>
  );
};
export default BusinessCodeMaintain;
