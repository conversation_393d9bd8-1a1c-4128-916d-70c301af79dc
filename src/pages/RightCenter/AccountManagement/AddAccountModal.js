import React, { useEffect, useState, useRef } from 'react'
import { Modal, Steps, Button, message, Table } from 'antd'
import { getWidth, offsetLeft } from '@/utils/index'
import { get, post, postForm } from '@/utils/request'
import UploadFile from '@/components/Public/UploadFile'
import PublicStep from '@/components/Public/PublicStep/index.js'
import baseURL from '@/baseURL'
import allUrl from '@/utils/url';
import style from './AddAccountModal.module.less'
import styles from '../antd.module.less'
import { CheckCircleFilled, UploadOutlined, DownloadOutlined } from '@ant-design/icons';

const steps = [
  {
    title: '上传文件'
  },
  {
    title: '校验数据',
  },
  {
    title: '上传成功',
  },
];
const AddAccountModal = (props) => {
  // console.log(props)
  const { open, changeShowAddAccount, onSearch } = props
  const [tableData, setTableData] = useState([])
  // 上传下载
  const [file1, setFile1] = useState(null)
  const [filename1, setFilename1] = useState('')
  const [filename2, setFilename2] = useState('')
  const [file2Url, setFile2Url] = useState('')
  const [loading, setLoading] = useState(false)
  const [nextLoading, setNextLoading] = useState(false)
  // 步骤条
  const [current, setCurrent] = useState(0);
  // 执行结果
  const [finalRes, setFinalRes] = useState({ succ: 0, err: 0 })

  // modal相关
  const closeModal = () => {
    changeShowAddAccount(false)
    reset()
  }
  const handleOK = () => {
    closeModal(false)
  }
  const handleCancel = () => {
    closeModal(false)
  }
  const reset = () => {
    setCurrent(0)
    setFilename1('')
    setFilename2('')
    setFile1(null)
    setFile2Url('')
    setTableData([])
    setLoading(false)
    setNextLoading(false)
  }


  // 上传下载
  const beforeUpload = (file,type) => {
    // 附件直接点击下一步时上传文件
    setFilename1(file.name)
    setFile1(file)
  }
  const UploadChange = ({ file, fileList }, type) => {
    // console.log(file, fileList);
    switch (type) {

      case 2:
        // 审核文件提前上传拿到文件地址
        const { response } = file
        if (file.status === 'uploading') {
          setLoading(true)
        }
        if (file.status === 'done') {
          const { msg, msgCode, resp, success } = response
          setLoading(false)
          if (success) {
            setFilename2(file.name)
            resp && setFile2Url(resp[0]?.url)
            message.success(msg || msgCode)

          } else {
            message.error(msg || msgCode)
          }

        }
        if (file.status === 'error') {
          message.error(response.msg || response.msgCode)
          setLoading(false)
        }
        break
    }


  }

  const download = () => {
    // 测试地址
    // window.open('https://scrm-equity-dev-oss.oss-cn-shanghai.aliyuncs.com/equity-file/20240522113018-权益中心_账户新增模板.xlsx')
    // 正式地址
    window.open('https://scrm-equity-prod-oss.oss-cn-shanghai.aliyuncs.com/equity-file/%E6%9D%83%E7%9B%8A%E4%B8%AD%E5%BF%83_%E6%9D%83%E7%9B%8A%E8%B4%A6%E6%88%B7%E6%96%B0%E5%A2%9E%E6%A8%A1%E6%9D%BF.xlsx')
  }


  // 步骤条

  const items = steps.map((item) => ({
    key: item.title,
    title: item.title,
  }));
  const next = () => {
    setCurrent(current + 1);
  };
  const step1 = () => {

    let formData = new FormData()
    formData.append('auditFileUrl', file2Url)
    formData.append('file', file1)
    setNextLoading(true)
    postForm(allUrl.AccountManagement.addByExcel, formData, {
      headers: {
        'Content-Type': "multipart/form-data"
      }
    }).then(res => {
      const { success, msg, resp } = res
      if (success) {
        setNextLoading(false)
        setTableData(resp)
        next()
      } else {
        // message.warn(msg)
        setNextLoading(false)
      }
    }).catch(() => {
      setNextLoading(false)
    })
  }
  const step2 = () => {

    // application/x-www-form-urlencoded
    setNextLoading(true)

    let formData = new FormData()
    formData.append('auditFileUrl', file2Url)
    post(allUrl.AccountManagement.addAccount, formData, { isForm: true }).then(res => {
      const { success, msg, resp } = res
      if (success) {
        const data = resp || []
        setNextLoading(false)
        next()
        const _successData = data.filter(i => !i.result)
        const _errorData = data.filter(i => i.result)
        setTableData(_errorData)
      
        setFinalRes({
          succ: _successData.length,
          err: _errorData.length
        })

      } else {
        // message.warn(msg)
        setNextLoading(false)
      }
    }).catch(() => {
      setNextLoading(false)
    })
  }

  const step3 = () => {
    onSearch({ _t: new Date().getTime() });
    handleCancel()
  }




  // 其他内容
  const result = {
    success: {
      color: '#52C41A',
      name: '成功'
    },
    error: {
      color: '#F5222D',
      name: '失败'
    }
  }
  const ball = {
    width: '8px', height: '8px', borderRadius: '8px', display: 'inline-block', marginRight: '8px'
  }
  const columns = [
    { title: '序号', dataIndex: 'idx', width: 60, fixed: 'left' },
    {
      title: '上传结果（格式）', dataIndex: 'result', width: 100, fixed: 'left', render: (text, record) => {
        if (text === 0) {
          return <><span style={{ ...ball, background: result['success']['color'] }}></span>{result['success']['name']}</>
        } else {
          return <><span style={{ ...ball, background: result['error']['color'] }}></span>{result['error']['name']}</>
        }
      }
    },
    {
      title: '失败原因', dataIndex: 'errMsg', width: 200, fixed: 'left', render: (text, record) => {
        if (record.result !== 0) {
          return <span>{record.errMsg}</span>
        } else {
          return <span>-</span>
        }
      }
    },
    { title: '商城订单编号', width: 220, dataIndex: 'hwOrderNo' },
    { title: '金康订单编号', width: 220, dataIndex: 'formalOrderNo' },
    { title: '交付订单编号', width: 220, dataIndex: 'deliverOrderNo' },
    { title: '大定支付时间', width: 200, dataIndex: 'formalOrderDate' },
    { title: '交付时间', width: 200, dataIndex: 'deliverDate' },
    { title: 'VIN', width: 200, dataIndex: 'vin' },
    { title: '基础车型', width: 120, dataIndex: 'baseConfig' },
    { title: '基础配置', width: 200, dataIndex: 'baseMaterialNo' },
    { title: '车主姓名', width: 120, dataIndex: 'carOwnerName' },
    { title: '车主手机号', width: 200, dataIndex: 'carOwnerPhone' },
    { title: '购车人姓名', width: 200, dataIndex: 'carBuyerName' },
    { title: '购车人手机号', width: 200, dataIndex: 'carBuyerPhone' },
    { title: () => { return <div><span style={{ color: 'red' }}>*</span>实名认证姓名</div>}, width: 150, dataIndex: 'certificationName' },
    // { title: '实名认证姓名', width: 150, dataIndex: 'certificationName' },
    { title: () => { return <div><span style={{ color: 'red' }}>*</span>车联网手机号</div>}, width: 200, dataIndex: 'certificationPhone' },
    // { title: '车联网手机号', width: 200, dataIndex: 'certificationPhone' },
    { title: '权益包编号', width: 200, dataIndex: 'basePackageNo' },
    { title: '权益包名称', width: 150, dataIndex: 'basePackageName' },
    { title: '权益开始时间', width: 200, dataIndex: 'effectTime' },
    { title: '权益开始里程（公里）', width: 180, dataIndex: 'effectMileage' },
    { title: '车辆市场类型', width: 180, dataIndex: 'carMarketTypeName' },
  ]


  return (
    <Modal title='权益账户新增' open={open} onOk={handleOK} onCancel={handleCancel} width={getWidth()} style={{ left: offsetLeft() }} footer={null} destroyOnClose={true} maskClosable={false}>
      <PublicStep current={current} items={items} />
      {/* 第一步 */}
      {current === 0 && (
        <div className={style.box} >
          <h2>新增数据上传</h2>
          <div className='con'>
            <label>下载数据导入模板：</label>
            <Button type="primary" ghost icon={<DownloadOutlined />} onClick={download}>模板下载</Button>
          </div>
          {/* 附件直接上传文件 */}
          <div className='con'>
            <label>附件：</label>
            <UploadFile
              style={{ display: 'inline-block' }}
              extension={['xls', 'xlsx']}
              showUploadList={false}
              size={5}
              // action={false}
              customRequest={() => { }}
              beforeUpload={(file) => beforeUpload(file, 1)}
            >
              <Button icon={<UploadOutlined />} type="primary" className={style.uploadBtn}  >{filename1 ? filename1 : '上传附件'}</Button>
            </UploadFile>
          </div>
          <div className='con'>
            <label></label>
            <div className='tips'>
              <p>请注意附件填写内容：</p>
              <p>1、交付时间应在大定支付时间之后；</p>
              <p>2、若附件中填写了权益开始时间，则以附件填写的权益开始时间为准，若未填写，则以权益包策略为准；</p>
              <p>3、表格内所有时间填写格式为：YYYY-MM-DD hh:mm:ss</p>
              <p>4、一次最多上传200条数据</p>
              <p>5、附件仅支持上传： Excel格式文件，最大支持5M</p>
              <p>6、请严格按照模版填写，不要修改模板表头内容，否则无法识别，如造成数据混乱，后果自负。</p>
            </div>
          </div>

          <hr></hr>
          {/* 审核文件上传后得到URL */}
          <h2>审核文件上传</h2>
          <div className='con'>
            <label>审核文件：</label>
            <UploadFile
              style={{ display: 'inline-block' }}
              extension={['png', 'jpg', 'jpeg', 'pdf']}
              showUploadList={false}
              size={5}
              name='files'
              action={baseURL.Host + allUrl.common.uploadFile}
              UploadChange={(file) => UploadChange(file, 2)}
            >
              <Button icon={<UploadOutlined />} type="primary" className={style.uploadBtn} loading={loading}>{filename2 ? filename2 : '上传文件'}</Button>
            </UploadFile>
          </div>
          <div className='con'>
            <label></label>
            <div className='tips'>
              <p>请上传邮件截图、OA截图、订单截图等证明文件，支持图片、pdf格式上传，大小不超过5M</p>
            </div>
          </div>
          <div className={style.footer}>
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" loading={nextLoading} onClick={step1} disabled={!(filename1 && filename2)}>
              下一步
            </Button>
          </div>

        </div>
      )}
      {/* 第二步 */}
      {current === 1 && (
        <div className={style.box}>
          <Table columns={columns} dataSource={tableData} scroll={{ y: 300, x: '100vw', }} pagination={false} className={styles['blue-table']} />

          <div className={style.footer}>
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" onClick={step2} loading={nextLoading}>
              执行
            </Button>
          </div>
        </div>
      )}
      {/* 第三步 */}
      {current === 2 && (
        <div className={style.box}>
          <div className={style.done}>
            <CheckCircleFilled className='icon' />
            <h3>账户新增完成</h3>
            <p>本次账户新增共成功 <span>{finalRes.succ}</span> 个，失败 <span className="red">{finalRes.err} 个</span> {finalRes.err > 0 ? '，失败数据如下：' : ''}</p>
          </div>
          {
            finalRes.err > 0 && <Table columns={columns} dataSource={tableData} scroll={{ y: 300, x: '100vw', }} pagination={false} className={styles['blue-table']} />
          }


          <div className={style.footer}>
            <Button type="primary" onClick={step3}>
              完成
            </Button>
          </div>
        </div>
      )}

    </Modal>
  )

}
export default AddAccountModal