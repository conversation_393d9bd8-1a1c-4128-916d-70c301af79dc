import {
  getSeresFileItemEditDetail,
  postSeresFileItemSave,
  postSeresFileMultiUpload,
  postSeresFileUpload,
} from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { useRequest } from '@/hooks/request';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  message,
  Modal,
  ModalProps,
  Radio,
  Row,
  Spin,
  TreeSelect,
  Upload,
} from 'antd';
import moment from 'moment';
import type { UploadRequestOption as RcCustomRequestOptions } from 'rc-upload/lib/interface';
import React, { FC, useCallback, useEffect, useState } from 'react';
import CommonJobSelect from '../components/common-job-select';
import SupplierJobSelect from '../components/store-job-select';
import StoreSelect from '../components/store-select';
import StoreJobSelect from '../components/supplier-job-select';
import SupplierSelect from '../components/supplier-select';

import { PlusOutlined } from '@ant-design/icons';
import HeadquarterDivisionSelect from '../components/headquarter-division-select';
import {
  FILE_BIZ_TYPE_MAP,
  FILE_DELIVER_TYPE_OPTIONS,
  FILE_DELIVER_TYPE_TO_COMMON_JOB_TYPE_MAP,
  FILE_PERMISSION_OPTIONS,
  FILE_POST_TYPE_OPTIONS,
  FileBizType,
  FileDeliverType,
  FilePermission,
  FilePostType,
  YES_OR_NO_OPTIONS,
  YesOrNo,
} from '../constants';
import FileSelector from './file-selector';
import HeadQuarterPersonSelector from './headquarter-person-selector';
import PersonSelector from './person-selector';

const { TreeNode } = TreeSelect;

export interface FileModalProps extends Omit<ModalProps, 'onOk'> {
  onSuccess: () => void;
  handleTreeNodeSelect: (value: any, node: any) => void;
  handleLoad: (params: any) => Promise<void>;
  data?: FileManage.FileItemListVO;
  bizType: FileBizType;
  parentDirId?: number;
  parentDirPath?: string;
  treeData?: any;
  loading?: boolean;
}

const FileModal: FC<FileModalProps> = ({
  onSuccess,
  data,
  bizType,
  parentDirId,
  parentDirPath,
  treeData,
  loading,
  handleLoad,
  handleTreeNodeSelect,
  ...props
}) => {
  const [form] = Form.useForm();
  const [pending, setPending] = useState(false);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [, setLoadingNodes] = useState<Record<string, boolean>>({}); // 用来追踪加载中的节点
  const [loadedNodes, setLoadedNodes] = useState<Record<string, boolean>>({}); // 用来追踪已经加载完成的节点

  const { data: [fileDetail] = [] } = useRequest(
    () => {
      if (data?.id) {
        return getSeresFileItemEditDetail({ id: data.id });
      } else {
        return Promise.resolve(undefined);
      }
    },
    {
      deps: [data?.id],
    }
  );

  const initEditForm = useCallback(async () => {
    if (fileDetail) {
      form.setFieldsValue({
        ...fileDetail,
        file: {
          uid: fileDetail.id,
          name: fileDetail.name,
          size: fileDetail.size,
          status: 'done',
          response: {
            ossObjectName: fileDetail.ossObjectName,
          },
        },
        onlineFilesList: fileDetail.linkFileList,
        expireTime: moment(fileDetail.expireTime),
        attachment: fileDetail.attachmentList?.map(item => ({
          uid: item.id,
          name: item.fileName,
          status: 'done',
          response: item,
          size: item.size,
        })),
        division: { enable: (fileDetail.seresOrgIdList?.length ?? 0) > 0, value: fileDetail.seresOrgIdList },
        person: {
          enable: (fileDetail.seresUserList ?? []).length > 0,
          value: fileDetail.seresUserList ?? [],
        },
        supplierPerson: fileDetail.specificUserList,
      });
      if ((fileDetail.linkFileList ?? []).length > 0) {
        setShowOnlineFile(true);
      }
      if ((fileDetail.seresUserList ?? []).length > 0) {
        setShowHeadquarterPerson(true);
      }
    }
  }, [fileDetail, form]);

  useEffect(() => {
    initEditForm();
  }, [initEditForm]);

  const handleModalOk = useCallback(async () => {
    try {
      const { file, person, division, supplierPerson, ...values } = await form.validateFields();
      const { specificPost = [], frequentPost = [] } = values;
      const frequentPostList = frequentPost.map(item => item.list).flat();
      const post = [...specificPost, ...frequentPostList];
      // 把post中的对象，根据code去重
      const postSet = new Set();
      const postArray = post.filter(item => {
        if (postSet.has(item.code)) {
          return false;
        }
        postSet.add(item.code);
        return true;
      });
      delete values.frequentPost;
      const params = {
        ...fileDetail,
        ...values,
        ...(postArray?.length ? { specificPost: postArray } : {}),
        expireTime: moment(values.expireTime).format('YYYY-MM-DD HH:mm:ss'),
        bizType,
        parentDirId: parentDirId || (fileDetail?.parentDirId as number),
        name: file.name,
        size: file.size,
        ossObjectName: file.response.ossObjectName,
        linkFileIdList: values.onlineFilesList?.map(item => item.id) || [],
        attachmentList: values.attachment?.map(item => ({ ...item.response, size: item.size })) || [],
        seresOrgIdList: division.enable ? division.value : [],
        seresUserOneIdList: person.enable ? person.value.map(item => item.userOneId) : [],
        specificUserList: supplierPerson?.map(item => item.userOneId) || [],
      };
      console.log('params', params);
      setPending(true);
      await postSeresFileItemSave(params);
      onSuccess();
    } catch (error) {
    } finally {
      setPending(false);
    }
  }, [bizType, fileDetail, form, onSuccess, parentDirId]);

  const uploadFile = useCallback(
    async ({ file, filename = 'file', onError, onProgress, onSuccess }: RcCustomRequestOptions) => {
      try {
        const formData = new FormData();
        formData.append(filename, file as File, (file as File).name);
        const [res] = await postSeresFileUpload({ id: parentDirId || (fileDetail?.parentDirId as number) }, formData, {
          onUploadProgress: event => {
            onProgress?.({
              ...event,
              percent: event.progress,
            } as any);
          },
        });
        onSuccess?.(res, new XMLHttpRequest());
      } catch (error) {
        onError?.(error as any);
      }
    },
    [fileDetail, parentDirId]
  );

  const uploadAttachment = useCallback(
    async ({ file, filename = 'files', onError, onSuccess }: RcCustomRequestOptions) => {
      console.log('filename', filename);
      try {
        const formData = new FormData();
        formData.append(filename, file as File, (file as File).name);
        const [res] = await postSeresFileMultiUpload(
          { id: parentDirId || (fileDetail?.parentDirId as number) },
          formData
        );
        onSuccess?.(res, new XMLHttpRequest());
      } catch (error) {
        onError?.(error as any);
      }
    },
    [fileDetail, parentDirId]
  );

  const handleTreeExpand = (newExpandedKeys: string[]) => {
    // 判断是否有新的节点被展开，如果有，触发加载
    const newKey = newExpandedKeys.find(key => !expandedKeys.includes(key));
    if (newKey && !loadedNodes[newKey]) {
      setLoadingNodes(prev => ({ ...prev, [newKey]: true }));
      handleLoad({ id: newKey }).then(() => {
        setLoadedNodes(prev => ({ ...prev, [newKey]: true }));
        setLoadingNodes(prev => ({ ...prev, [newKey]: false }));
      });
    }
    setExpandedKeys(newExpandedKeys);
  };

  const treeNodeSelect = (value, node, extra) => {
    console.log('node', node);
    if (node) {
      node.selected = false;
      handleTreeNodeSelect(value, { node }); // 传递选中的值和节点信息
    }
  };

  // 递归渲染 TreeNode，动态添加 loading 图标
  const renderTreeNode = useCallback(
    (item, index, parentPos = '0') => {
      // 累积每一层的 pos 值
      const currentPos = `${parentPos}-${index}`;
      // 判断是否为叶子节点
      const isLeaf = loadedNodes[item.id]
        ? loadedNodes[item.id] && (!item.children || item.children.length === 0)
        : false;

      return (
        <TreeNode
          value={item.id}
          title={item.name}
          key={item.id}
          isLeaf={item.parentDirPath === '/' ? false : isLeaf}
          bizType={item.bizType}
          name={item.name}
          id={item.id}
          parentDirId={item.parentDirId}
          parentDirPath={item.parentDirPath}
          pos={currentPos}
        >
          {item.children?.map((child, index) => renderTreeNode(child, index, currentPos))}
        </TreeNode>
      );
    },
    [loadedNodes]
  );

  const beforeUpload = useCallback((file: File) => {
    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
      message.error('文件大小超过50M，上传失败');
    }
    return isLt50M || Upload.LIST_IGNORE;
  }, []);

  const [showOnlineFile, setShowOnlineFile] = useState(false);
  const [showHeadquarterPerson, setShowHeadquarterPerson] = useState(false);
  return (
    <Modal
      maskClosable={false}
      forceRender
      title={data?.id ? '编辑文件' : '添加文件'}
      {...props}
      afterClose={() => {
        form.resetFields();
        setShowOnlineFile(false);
        setShowHeadquarterPerson(false);
      }}
      onOk={handleModalOk}
      confirmLoading={pending}
      width={800}
    >
      <Form
        form={form}
        {...modalFormLayout}
        initialValues={{
          bizType,
          parentDirId,
          permission: FilePermission.VIEW,
          deliverType: FileDeliverType.ALL_STORE,
          postType: FilePostType.ALL_POST,
          isSendMsg: YesOrNo.NO,
          division: { enable: true, value: [] },
          person: { enable: false, value: [] },
        }}
      >
        <Form.Item label="业务类型" required>
          <div>{FILE_BIZ_TYPE_MAP[bizType]}</div>
        </Form.Item>
        <Form.Item label="目录" required>
          {/* <Input disabled value={data ? data.parentDirPath : parentDirPath} /> */}
          {/* value={data ? data.parentDirPath : parentDirPath} */}
          {
            <Spin spinning={loading}>
              <TreeSelect
                value={parentDirPath ? parentDirPath : data?.parentDirPath}
                style={{ width: '100%' }}
                placeholder="Please select"
                onTreeExpand={handleTreeExpand}
                onSelect={treeNodeSelect}
              >
                {treeData.map((item, index) => renderTreeNode(item, index))}
              </TreeSelect>
            </Spin>
          }
        </Form.Item>
        <Form.Item
          label="添加正文"
          name="file"
          valuePropName="fileList"
          normalize={({ fileList }) => fileList[0]}
          getValueProps={value => {
            return { fileList: value ? [value] : [] };
          }}
          rules={[{ required: true, message: '请上传文件' }]}
          extra="说明：格式限PDF和图片（JPEG、PNG），大小50M以内，页数500页以内"
        >
          <Upload
            accept=".pdf,.jpeg,.jpg,.png"
            maxCount={1}
            showUploadList
            beforeUpload={beforeUpload}
            customRequest={uploadFile}
          >
            <Button>上传文件</Button>
          </Upload>
        </Form.Item>
        <Form.Item label="正文权限" name="permission" rules={[{ required: true, message: '请选择浏览下载权限' }]}>
          <Radio.Group options={FILE_PERMISSION_OPTIONS} />
        </Form.Item>
        <Form.Item label="附件" style={{ marginBottom: 0 }}>
          <Form.Item>
            <Button icon={<PlusOutlined />} onClick={() => setShowOnlineFile(true)}>
              添加水印文件
            </Button>
          </Form.Item>
          {showOnlineFile && (
            <Form.Item dependencies={['bizType']} noStyle>
              {({ getFieldValue }) => {
                const bizType = getFieldValue('bizType') as FileBizType;
                return (
                  <Form.Item name="onlineFilesList">
                    <FileSelector bizType={bizType} />
                  </Form.Item>
                );
              }}
            </Form.Item>
          )}
          <Form.Item
            name="attachment"
            valuePropName="fileList"
            normalize={({ fileList }) => fileList} // 保持为文件列表
            getValueProps={value => {
              return { fileList: value || [] }; // 确保 `fileList` 是数组
            }}
          >
            <Upload name="files" customRequest={uploadAttachment} maxCount={10} showUploadList multiple>
              <Button icon={<PlusOutlined />}>添加无水印可下载文件</Button>
            </Upload>
          </Form.Item>
        </Form.Item>

        <Form.Item label="失效日期" name="expireTime" rules={[{ required: true, message: '请选择失效日期' }]}>
          <DatePicker
            disabledDate={date => {
              return date.isBefore(moment().endOf('day'));
            }}
            showToday={false}
          />
        </Form.Item>

        <Form.Item label="下发范围" name="deliverType" rules={[{ required: true, message: '请选择下发范围' }]}>
          <Radio.Group
            options={FILE_DELIVER_TYPE_OPTIONS}
            onChange={() => form.resetFields(['deliverScope', 'specificPost', 'frequentPost'])}
          />
        </Form.Item>
        <Form.Item noStyle dependencies={['deliverType']}>
          {({ getFieldValue }) => {
            const deliverType = getFieldValue('deliverType') as FileDeliverType;
            if (deliverType === FileDeliverType.SPECIFIC_STORE) {
              return (
                <Form.Item
                  label="门店"
                  name="deliverScope"
                  rules={[{ required: true, message: '请选择门店' }]}
                  getValueFromEvent={(values, labels) => {
                    return values?.map((code, index) => ({
                      code,
                      name: labels[index],
                    }));
                  }}
                  getValueProps={values => {
                    return {
                      value: values?.map(item => item.code),
                    };
                  }}
                >
                  <StoreSelect multiple placeholder="请选择门店" />
                </Form.Item>
              );
            } else if (deliverType === FileDeliverType.SPECIFIC_SUPPLIER) {
              return (
                <Form.Item
                  label="供应商"
                  name="deliverScope"
                  rules={[{ required: true, message: '请选择供应商' }]}
                  getValueFromEvent={(_, options) => {
                    return options;
                  }}
                  getValueProps={value => {
                    return {
                      value: value?.map(item => item.code),
                    };
                  }}
                >
                  <SupplierSelect
                    onChange={() => {
                      form.resetFields(['supplierPerson']);
                    }}
                    mode="multiple"
                    placeholder="请选择下发供应商"
                  />
                </Form.Item>
              );
            }
            return null;
          }}
        </Form.Item>
        <Form.Item name="postType" label="岗位" rules={[{ required: true, message: '请选择岗位' }]}>
          <Radio.Group
            options={FILE_POST_TYPE_OPTIONS}
            onChange={() => form.resetFields(['specificPost', 'frequentPost', 'supplierPerson'])}
          />
        </Form.Item>
        <Form.Item noStyle dependencies={['postType', 'deliverType']}>
          {({ getFieldValue }) => {
            const deliverType = getFieldValue('deliverType') as FileDeliverType;
            const postType = getFieldValue('postType') as FilePostType;
            if (postType === FilePostType.SPECIFIC_POST) {
              const JobSelect =
                deliverType === FileDeliverType.SPECIFIC_STORE || deliverType === FileDeliverType.ALL_STORE
                  ? StoreJobSelect
                  : SupplierJobSelect;
              return (
                <>
                  <Form.Item
                    wrapperCol={{ ...modalFormLayout.wrapperCol, offset: modalFormLayout.labelCol.span }}
                    name="specificPost"
                    getValueFromEvent={(_, options) => {
                      return options;
                    }}
                    getValueProps={value => {
                      return {
                        value: value?.map(item => item.code),
                      };
                    }}
                  >
                    <JobSelect mode="multiple" maxTagCount={10} allowClear placeholder="请选择岗位" />
                  </Form.Item>
                  <Form.Item wrapperCol={{ ...modalFormLayout.wrapperCol }} name="frequentPost" label="常用岗位">
                    <CommonJobSelect type={FILE_DELIVER_TYPE_TO_COMMON_JOB_TYPE_MAP[deliverType]} />
                  </Form.Item>
                </>
              );
            }
            return null;
          }}
        </Form.Item>
        <Form.Item noStyle dependencies={['deliverType', 'postType', 'deliverScope']}>
          {({ getFieldValue }) => {
            const deliverType = getFieldValue('deliverType') as FileDeliverType;
            const postType = getFieldValue('postType') as FilePostType;
            const deliverScope = getFieldValue('deliverScope');

            if (deliverType === FileDeliverType.SPECIFIC_SUPPLIER && postType === FilePostType.SPECIFIC_POST) {
              return (
                <Form.Item label="指定人员" name="supplierPerson">
                  <PersonSelector rowKey="userOneId" orgCodeList={deliverScope?.map(item => item.code)} />
                </Form.Item>
              );
            }
            return null;
          }}
        </Form.Item>
        <Form.Item label="消息推送" name="isSendMsg" rules={[{ required: true, message: '请选择是否推送消息' }]}>
          <Radio.Group options={YES_OR_NO_OPTIONS} />
        </Form.Item>
        <Form.Item
          label="总部人员"
          required
          rules={[
            ({ getFieldValue }) => ({
              validator() {
                const departmentEnable = getFieldValue(['division', 'enable']);
                const personsEnable = getFieldValue(['person', 'enable']);
                if (!departmentEnable && !personsEnable) {
                  return Promise.reject('至少选中1个部门或者1个人员');
                }
                return Promise.resolve();
              },
            }),
          ]}
          validateStatus=""
        >
          <Row>
            <Form.Item valuePropName="checked" name={['division', 'enable']}>
              <Checkbox
                onChange={() => {
                  form.validateFields(['headquarter', ['division', 'value']]);
                }}
              >
                按部门
              </Checkbox>
            </Form.Item>
            <Col span={18}>
              <Form.Item>
                <Form.Item
                  name={['division', 'value']}
                  noStyle
                  rules={[
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        const departmentEnable = getFieldValue(['division', 'enable']);
                        if (departmentEnable && (!value || value.length === 0)) {
                          return Promise.reject('请选择部门');
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <HeadquarterDivisionSelect showAllSelect mode="multiple" />
                </Form.Item>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Form.Item valuePropName="checked" name={['person', 'enable']}>
              <Checkbox
                onChange={() => {
                  form.validateFields(['headquarter', ['person', 'value']]);
                }}
              >
                按人员
              </Checkbox>
            </Form.Item>

            <Form.Item
              name={['person', 'value']}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const departmentEnable = getFieldValue(['person', 'enable']);
                    if (departmentEnable && (!value || value.length === 0)) {
                      return Promise.reject('请选择人员');
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
              noStyle
            >
              <Col span={12}>
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setShowHeadquarterPerson(true);
                  }}
                >
                  添加人员
                </Button>
              </Col>
            </Form.Item>
          </Row>
          {showHeadquarterPerson && (
            <Form.Item
              name={['person', 'value']}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const departmentEnable = getFieldValue(['person', 'enable']);
                    if (departmentEnable && (!value || value.length === 0)) {
                      return Promise.reject('请选择人员');
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
              noStyle
            >
              <HeadQuarterPersonSelector rowKey="userOneId" />
            </Form.Item>
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default FileModal;
