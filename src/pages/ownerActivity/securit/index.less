.SecuritList{
    
    .ant-pagination{
        padding-right: 20px;
    }
    .tableData{
        background-color: white;
        // padding: 24px 24px 72px 24px;

        border-top: solid 24px #f0f2f5;
        border-right: solid 24px #f0f2f5;
        border-bottom: solid 24px #f0f2f5;
        border-left: solid 24px #f0f2f5;
        .ant-table-wrapper{
            // background-color: white;
            .ant-table{
                padding: 24px;
                .ant-table-container{
                    border: 0;
                    .ant-table-content{
                        // overflow: auto hidden;
                        .ant-table-thead{
                            tr>th,tr>td{
                                // border: 0;
                            }
                        }
                    }
                    .ant-table-pagination{
                        margin: 16px 24px;
                    }
                }
            }
        }
        .tableTitle{
            padding: 24px 24px 0px 32px;
            justify-content: space-between;
            .text{
                font-size: 20px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.85);
                line-height: 28px;
            }
            .bts{
                .ant-btn{
                    margin:0 7px;
                }
            }
        }
        .ant-table-sticky-scroll{
            display: none;
        }
    }
    .PublicList_FormQuery{
        padding-top: 16px;
        padding-left: 24px;
        .ant-col-7{
            .ant-form-item{
                .ant-form-item-control-input{
                    width: 90%;
                    .ant-form-item-control-input-content{
                        .ant-picker{
                            width: 100%;
                        }
                    }
                }
            }
        }
        .FormQuerySubmit{
            display:flex;
            justify-content: flex-end;
            .operationButtons{
                span{
                    color: #1890ff;
                    cursor: pointer;
                    .anticon{
                        margin-left: 6px;
                    }
                }
            }
        }
    }

    
}
.codeContainer {
    .ant-modal-body {
        padding: 0;
    }
    .aphoto {
        padding: 24px;
    }
    .codeTag {
        position: absolute;
        top: 0;
        left: 0;
        width: 120px;
        height: 40px;
        background-color: rgba(129, 211, 248, 1);
        text-align: center;
        line-height: 40px;
        font-weight: 500;
    }
    .codeTitle {
        text-align: center;
        font-size: 20px;
        font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
        font-weight: 600;
        margin-bottom: 24px;
        margin-top: 16px;
    
    }
    .codeBox {
        height: 400px;
        .codeImg {
            width: 100%;
            height: 100%;
        }
    }
    .downBtn {
        text-align: center;
        font-size: 30px;
        cursor: pointer;
        color: #505050;
        background-color: #cdcdcd;
        transition: opacity 0.2s;
        &:hover {
            opacity: 0.8;
        }
    }
}
