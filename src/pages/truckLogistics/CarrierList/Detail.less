.CarrierDetail{
    .CarrierDetail_Box{
        .CarrierDetail_Con{
            .CarrierDetail_Info_Title{
                font-size: 24px;
                font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.85);
                margin: 16px 0 ;
            }
            
            .CarrierDetail_Info_Con{
                .ant-row{
                    .ant-col{
                        .ant-form-item{
                            margin-bottom: 16px;
                        }
                    }
                }
                .CarrierDetail_Info_Con_title1{
                    font-size: 14px;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: rgba(0,0,0,0.85);
                    .ant-form-item-label > label{
                        font-size: 14px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: rgba(0,0,0,0.85);
                    }
                }
                .CarrierDetail_Info_Con_text1{
                    font-size: 16px;
                        color: rgba(0,0,0,0.85);  font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                        font-weight: 500;
                    .ant-form-item-label > label{
                        font-size: 16px;
                        color: rgba(0,0,0,0.85);  font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                        font-weight: 500;
                    }
                }
                .CarrierDetail_Info_Con_text2{
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(0,0,0,0.68);
                    .ant-form-item-label > label{
                        font-size: 14px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: rgba(0,0,0,0.68);
                    }
                }
                .ant-timeline-item-head-blue{
                    background: #1890FF;
                    margin: 4px 0px;
                }
                
            }
            
        }
        
    }
}
.PunchInRecord{
    margin-left: 18px;
    .ant-timeline{
        .ant-timeline-item{
            .ant-timeline-item-head-blue{
                background: #1890FF;
                margin: 4px 0px;
            }
            .ant-timeline-item-head-gray{
                background: #BCBCBC;
                color: #BCBCBC;
                border-color: #BCBCBC;
            }
            .ant-timeline-item-content{
                .TimelineItem_Con{
                    font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                    font-weight: 400;
                    .time{
                        font-size: 14px;
                        color: #888888;
                        margin-bottom: 10px;
                    }
                    .address{
                        font-size: 14px;
                        color: #333333;
                        margin-bottom: 3px;
                    }
                    .statusCn{
                        font-size: 12px;
                        color: #333333;
                    }
                }
            }
        }
    }
    .more{
        font-size: 12px;
        font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
        font-weight: 400;
        text-align: center;
        color: #b1b1b1;
        cursor: pointer;
        margin: 20px 0;
    }
}
.ant-popover-message{
    align-items: flex-start;
    .ant-popover-message-icon{
        padding-top: 2.3 px;
    }
}
.ant-timeline-item-tail {
    position: absolute;
    top: 14px;
    left: 4px;
    height: calc(100% - 10px);
    border-left: 2px dashed #f0f0f0;
}
