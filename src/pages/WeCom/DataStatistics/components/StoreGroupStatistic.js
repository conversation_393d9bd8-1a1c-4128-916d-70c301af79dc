import React ,{useEffect, useState} from 'react'
import { Modal ,Table ,message} from 'antd'
import allUrl from '@/utils/url'
import {post} from '@/utils/request'
import PublicTooltip from '@/components/Public/PublicTooltip'
import '../index.less'
import moment from 'moment'
import '../GroupBuildingData.less'

export default (props) =>{
    const {visible,title,onCancel, renderPercent, renderPopver} = props
    const [dataSource,setDataSource] = useState([])
    const [total,setTotal] = useState(0)
    const [pageSize,setPageSize] = useState(10000)
    const [current,setCurrent] = useState(1)
    const columns = [
        {
            title: "排名",
            dataIndex: "index",
            fixed:'left',
            width: 40,
            render: (text, record, index) => <div>{index + 1}</div>,
          },
          {
            title: "用户中心",
            dataIndex: "storeFullName",
            fixed:'left',
            width: 200,
            render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
          },
          {
            title: "已分配交车单",
            children: [
              {
                title: "分配量",
                width: 100,
                dataIndex: "distributionCount",
                render: (text, record) => renderPopver(text, record, 'distributionCount')
              },
              { title: "特殊备案量", width: 100,dataIndex: "distributionRemarkCount", render: (text, record) => renderPopver(text, record, 'distributionRemarkCount') },
              { title: "建群量", width: 100,dataIndex: "groupCount" , render: (text, record) => renderPopver(text, record, 'groupCount')},
              {
                  title: "建群率（环比）",
                  width: 180,
                  render: (text, record) =>
                    renderPercent(text, record, "groupPercent", "distributionGrowthPercent"),
              },
              { title: "认定量", width: 100,dataIndex: "distributionAffirmCount", render: (text, record) => renderPopver(text, record, 'distributionAffirmCount') },
              {
                title: "认定率（环比）",
                width: 180,
                render: (text, record) =>
                  renderPercent(text, record, "distributionAffirmPercent", "distributionAffirmGrowthPercent"),
              },
              { title: "建群合格量",  width: 100, dataIndex: "distributionQualifiedCount", render: (text, record) => renderPopver(text, record, 'distributionQualifiedCount') },
              {
                title: "建群合格率（环比）",
                width: 180,
                render: (text, record) =>
                  renderPercent(text, record, "distributionQualifiedPercent", "distributionQualifiedGrowthPercent"),
              },
            ],
          },
          {
            title: "待交付交车单",
            dataIndex: "",
            // isExtend: true,
            children: [
              {
                title: "待交付量",
                width: 100,
                dataIndex: "undeliveredCount",
                render: (text, record) => renderPopver(text, record, 'undeliveredCount')
              },
              { title: "特殊备案量", width: 100,dataIndex: "undeliveredRemarkCount", render: (text, record) => renderPopver(text, record, 'undeliveredRemarkCount') },
              { title: "建群量",width: 100, dataIndex: "undeliveredGroupCount" , render: (text, record) => renderPopver(text, record, 'undeliveredGroupCount')},
              {
                  title: "建群率（环比）",
                  width: 180,
                  render: (text, record) =>
                    renderPercent(text, record, "undeliveredGroupPercent", "undeliveredGrowthPercent"),
              },
              { title: "认定量",width: 100, dataIndex: "undeliveredAffirmCount", render: (text, record) => renderPopver(text, record, 'undeliveredAffirmCount') },
              {
                title: "认定率（环比）",
                width: 180,
                render: (text, record) =>
                  renderPercent(text, record, "undeliveredAffirmPercent", "undeliveredAffirmGrowthPercent"),
              },
              { title: "建群合格量", width: 100,dataIndex: "undeliveredQualifiedCount", render: (text, record) => renderPopver(text, record, 'undeliveredQualifiedCount') },
              {
                title: "建群合格率（环比）",
                width: 180,
                render: (text, record) =>
                  renderPercent(text, record, "undeliveredQualifiedPercent", "undeliveredQualifiedGrowthPercent"),
              },
            ],
          },
          {
            title: "交付完成交车单",
            dataIndex: "",
            children: [
              {
                title: "交付量",
                width: 100,
                dataIndex: "deliverCount",
                render: (text, record) => renderPopver(text, record, 'deliverCount')
              },
              { title: "特殊备案量", width: 100, dataIndex: "deliverRemarkCount", render: (text, record) => renderPopver(text, record, 'deliverRemarkCount') },
              { title: "建群量", width: 100, dataIndex: "deliverGroupCount" , render: (text, record) => renderPopver(text, record, 'deliverGroupCount')},
              {
                  title: "建群率（环比）",
                  width: 180,
                  render: (text, record) =>
                    renderPercent(text, record, "deliverGroupPercent", "deliverGrowthPercent"),
              },
              { title: "认定量",width: 100, dataIndex: "deliverAffirmCount", render: (text, record) => renderPopver(text, record, 'deliverAffirmCount') },
              {
                title: "认定率（环比）",
                width: 180,
                render: (text, record) =>
                  renderPercent(text, record, "deliverAffirmPercent", "deliverAffirmGrowthPercent"),
              },
              { title: "建群合格量", width: 100,dataIndex: "deliverQualifiedCount", render: (text, record) => renderPopver(text, record, 'deliverQualifiedCount') },
              {
                title: "建群合格率（环比）",
                width: 180,
                render: (text, record) =>
                  renderPercent(text, record, "deliverQualifiedPercent", "deliverQualifiedGrowthPercent"),
              },
            ],
          },
    ]

    const serialNumber =(pageIndex, pageSize, index)=>{
        return (current-1) * pageSize + index;
    }

    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
    }

    useEffect(()=>{
        post(allUrl.WeCom.qwStoreGroupStatisticPage,{pageIndex:current,pageSize}).then(res=>{
            if(res.success){
                setDataSource(res.resp[0].list)
                setTotal(res.resp[0].total)
            }else{
                // message.error(res.msg)
            }
        })
    },[current,pageSize])

    const InforData = {
        columns,
        dataSource,
        size:'small',
        bordered:true,
        rowKey:'id',
        scroll:{ y: 500 },
        pagination:false
    }
    return <Modal  title={<div>{title}<span style={{marginLeft:'20px',fontSize:'14px',color: 'rgba(0,0,0,0.45)',fontWeight: 400}}>{`统计截止${moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD 23:59:59')}`}</span></div>} visible={visible} onCancel={onCancel} maskClosable={false} footer={null} width={'90%'}>
                <Table className="groupBuildModal" {...InforData} />
            </Modal>
}