.box {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  text-align: center;

  :global {
    .con {
      margin: 0 auto;
      width: 694px;
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      label {
        width: 130px;
        text-align: right;
      }

      .tips {
        flex: 1;
        color: #F5222D;
        text-align: left;
        font-size: 12px;
      }
    }

    h2 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      padding-bottom: 22px;
    }

    .down-btn,
    .upload-btn {
      position: relative;
      text-indent: 8px;
      display: block;

      &.ant-btn-loading {
        text-indent: 0px;

        &::after {
          display: none;
        }
      }

      &::after {
        content: '';
        background: url('../../../assets/img/download-icon.png') no-repeat;
        background-size: 14px 14px;
        position: absolute;
        top: 50%;
        left: 12px;
        transform: translateY(-50%);
        width: 14px;
        height: 14px;
      }

    }

    .upload-btn {

      &::after {
        background-image: url('../../../assets/img/upload-icon.png');
      }

    }
  }
}

.done {
  :global {
    h3 {
      color: rgba(0, 0, 0, 0.85);
      line-height: 32px;
      font-size: 24px;
      padding-top: 22px;
    }

    p {
      text-align: center;
      font-size: 16px;
      padding-top: 13px;
      padding-bottom: 29px;
    }

    .red {
      color: #F5222D;
    }

    .icon {
      font-size: 72px;
      color: #74C041;
    }
  }

}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 25px;

  button:first-child {
    margin-right: 20px;
  }
}

.step {
  height: 70px;
  background: #EEF8FA;
  border-radius: 2px;
  border: 1px solid #61CBE0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploadBtn {
  width: 110px;
  margin-right: 20px;
  text-overflow: ellipsis;
  overflow: hidden;
  :global{
    span{
      display: inline;
    }
  }
}