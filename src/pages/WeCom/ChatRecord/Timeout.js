import React, { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import { Row, Col, Button, Spin, Table, Tooltip, message, Tag } from "antd";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
import PublicTable from "@/components/Public/PublicTable";
import PublicTableQuery from "@/components/Public/PublicTableQuery";
import _ from "lodash";
import moment from "moment";
import { roleJudgment } from "@/utils/authority";
import PublicTooltip from "@/components/Public/PublicTooltip";
import "./Timeout.less";
import ChatRow from "./components/ChatRow";
import ExtendedColumn from "@/components/Public/ExtendedColumn";
import { useCallbackState } from "../../../hooks";
import ExportModal from "./components/ExportModal";
import { fileDown } from "@/utils";

const Timeout = (props) => {
  const { userInfo } = useSelector((state) => state.common);

  const queryRef = useRef();
  const tableRef = useRef();
  const [pageSpin, setPageSpin] = useCallbackState(false);
  const [tableHeight, setTableHeight] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [recordObj, setRecordObj] = useState({});
  const [treeOrgData, setTreeOrgData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportVisible, setExportVisible] = useState(false);
  const recordRef = React.createRef();
  const [chatList, setChatList] = useState([]);
  const chatListRef = useRef(chatList);
  const [chatListHasMore, setChatListHasMore] = useState(false);
  const dateFormat = "YYYY-MM-DD HH:mm:ss";
  const defaultFormatStartTime = moment().add(-1, "days").format("YYYY-MM-DD 00:00:00");
  const defaultFormatEndTime = moment().add(-1, "days").format("YYYY-MM-DD 23:59:59");
  const defaultTimeRange = [moment(defaultFormatStartTime), moment(defaultFormatEndTime)];
  const [defaultQuery, setDefaultQuery] = useState({
    startTime: defaultFormatStartTime,
    endTime: defaultFormatEndTime,
  });
  const [chatSpining, setChatSpining] = useState(false);
  const [hasScroll, setHasScroll] = useState(false);
  const [firstGetChatRecord, setFirstGetChatRecord] = useState(true);
  const [endId, setEndId] = useState(null);
  const [beforeScrollHeight, setBeforeScrollHeight] = useCallbackState(0);
  const offsetDays = 31*24*60*60 * 1000; //最多选择范围31天ms
  const [selectStartTime, setSelectStartTime] = useState('');
  const [selectEndTime, setSelectEndTime] = useState('');
  const [selectedRange, setSelectedRange] = useState([]);
  const [toOldId, setToOldId] = useState(""); // 往上滑动时，查询过去会话传的lastId
  const chatRowRef = useRef();

  useEffect(() => {
    chatListRef.current = chatList; // 把它写入 ref
  }, [chatList]); // 取得chatList最新值，方便赋值后直接使用
  const PageChange = (current, pageSize) => {
    setCurrent(current);
    setPageSize(pageSize);
  };

  // 初始化
  useEffect(() => {
    GetOrgData();
    initPage();
  }, []);

  const onSearch = (values) => {
    if (values.groupCreateTime && values.groupCreateTime.length) {
      values.startTime = moment(values.groupCreateTime[0]).format("YYYY-MM-DD HH:mm:ss");
      values.endTime = moment(values.groupCreateTime[1]).format("YYYY-MM-DD HH:mm:ss");
    } else {
      values.startTime = defaultFormatStartTime;
      values.endTime = defaultFormatEndTime;
    }
    setDefaultQuery(values);
  };
  // 获取组织列表
  const GetOrgData = (name = "") => {
    post(allUrl.Authority.orgPageList, { name }).then((res) => {
      if (res.success) {
        const arr = res.resp.slice(0);
        setTreeOrgData(arr);
      } else {
        // message.error(res.msg);
      }
    });
  };
  // 组织搜索
  const searchOrg = _.debounce((value) => {
    if (value) {
      GetOrgData(value);
    }
  }, 500);
  const renderOperation = (text, record) => {
    return (
      <div style={{ color: "#1890ff" }}>
        {roleJudgment(userInfo, "WECOM_CHAT_RECORDS_TIMEOUT_VIEW")
          ? [
              <div key={1}>
                <a onClick={() => lookChatRecord(record)}>查看超时消息</a>
              </div>,
            ]
          : null}
      </div>
    );
  };
  const lookChatRecord = (record) => {
    setRecordObj(record);
    setFirstGetChatRecord(true);
    const listDom = document.getElementsByClassName("record-list")[0];
    listDom.scrollTop = 0;
  };
  useEffect(() => {
    if (recordObj.beginId) {
      setChatList([]);
      getRecord();
    }
  }, [recordObj]);
  const getRecord = (lastId, beforePosition, direction) => {
    if (chatSpining) return; // 节流，防止多次请求
    const listDom = document.getElementsByClassName("record-list")[0];
    const dataDirection = direction || 2; //数据拉取方向，1 为从节点向前拉取，2 为从节点向后拉取, 默认向后（当前时间）拉取
    setChatSpining(true);
    // 有lastid则是二次请求，首次请求标识置否
    lastId && setFirstGetChatRecord(false);
    // beforePosition && setBeforeScrollHeight(beforePosition);
    // 首次请求只传beginId, 再次向当前时间查询beginId传空，lastId传最近的id，再次向前查询beiginId也传空，lastId传最早的id，并且dataDirection传1
    post(allUrl.WeCom.slideChatRoundChat, {
      beginId: lastId ? "" : recordObj.beginId,
      sign: recordObj.chatSign,
      lastId,
      dataDirection,
    }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        setChatListHasMore(Dt.hasMore);
        if (lastId) {
          // console.log("二次加载");
          // 再次加载
          // 向前查询
          if (dataDirection == 1 && Dt.list.length) {
            setToOldId(Dt.list[Dt.list.length - 1].id);
            setChatList((chatList) => Dt.list.reverse().concat(chatList));
          }
          // 向后查询
          if (dataDirection == 2 && Dt.list.length) {
            setEndId(Dt.list[0].id);
            setChatList((chatList) => Dt.list.concat(chatList));
          }
        } else {
          // 首次加载
          // console.log("首次加载");
          setChatList((chatList) => Dt.list);
        }
        listDom.scrollTop = lastId ? beforePosition : 0;
        setChatSpining(false);
      }
    });
  };
  // 聊天记录滚动条监听
  const handleOnScrollCapture = (event) => {
    if (recordRef) {
      if (
        recordRef.current.scrollHeight > recordRef.current.clientHeight &&
        recordRef.current.scrollTop === 0
      ) {
        // console.log("到顶部", toOldId, endId);
        getRecord(chatListRef.current[0].id, recordRef.current.scrollHeight, 1);
      }
      if (
        recordRef.current.scrollHeight - recordRef.current.clientHeight >
        recordRef.current.scrollTop + 100
      ) {
      } else {
        // console.log("到底部");
        if (chatListHasMore) {
          getRecord(
            chatListRef.current[chatListRef.current.length - 1].id,
            recordRef.current.scrollTop - 100,
            2
          );
        }
      }
    }
  };
  // 滚动条设置
  useEffect(() => {
    setChatSpining(true);
    setTimeout(() => {
      setChatSpining(false);
    }, 500);
    setHasScroll(recordRef.current.scrollHeight > recordRef.current.clientHeight);
  }, [chatList, firstGetChatRecord]);

  const exportExcel = (type) => {
    let params = { ...queryRef.current.query };
    if (type === "condition") {
      if (params.groupCreateTime && params.groupCreateTime.length) {
        params.startTime = moment(params.groupCreateTime[0]).format("YYYY-MM-DD HH:mm:ss");
        params.endTime = moment(params.groupCreateTime[1]).format("YYYY-MM-DD HH:mm:ss");
      } else {
        message.error("请选择时间");
      }
    }
    setExportLoading(true);
    post(
      allUrl.WeCom.exportOvertimeDetail,
      { exportType: type, ...params },
      { responseType: "blob" }
    ).then((res) => {
      fileDown(res, res.fileName);
    });
    setExportVisible(false);
    setExportLoading(false);
  };

  const handleCalendar = (date, dateStrings, info) => {
    if (date) {
      if (date[0] && info.range === 'start') {
        setSelectStartTime(date[0]);
        setSelectEndTime('');
      } else if (date[1] && info.range === 'end') {
        setSelectStartTime('');
        setSelectEndTime(date[1]);
      }
    } else {
      setSelectStartTime('');
      setSelectEndTime('');
    }
  };

  const disabledDate = (current, selectedDate) => {
    if (selectStartTime) {
      const selectV = moment(selectStartTime, 'YYYY-MM-DD').valueOf();
      return current.valueOf() > selectV + offsetDays;
    } else if (selectEndTime) {
      const selectV = moment(selectEndTime, 'YYYY-MM-DD').valueOf();
      return current.valueOf() < selectV - offsetDays;
    }
  }
  let searchList = [
    {
      label: "会话开启时间",
      name: "groupCreateTime",
      type: "RangePicker",
      showTime: true,
      colSpan: 8,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      showTime: true,
      value:{selectedRange},
      onCalendarChange:handleCalendar,
      initialValue: defaultTimeRange,
      disabledDate,
      rules: [
        {
          required: true,
        },
      ],
    },
    {
      label: "群主所在组织",
      name: "organizationId",
      type: "TreeSelect",
      data: treeOrgData,
      onSearch: searchOrg,
      colSpan: 5,
      fieldNames: {
        label: "title",
        value: "key",
        children: "children",
      },
      //   onChange: selectOrg,
      placeholder: "选择组织",
    },
    {
      label: "客户认定姓名",
      name: "customerName",
      type: "Input",
      colSpan: 4,
      labelCol: { span: 10 },
      wrapperCol: { span: 12 },
    },
    { label: "群主", name: "groupOwnerName", type: "Input", colSpan: 5, labelCol: { span: 3 } },
  ];

  const [columns, setColums] = useState([
    {
      title: "会话开启时间",
      fixed: "left",
      dataIndex: "beginTime",
      width: 200,
    },
    {
      title: "会话结束时间",
      dataIndex: "endTime",
      width: 200,
    },
    {
      title: "响应时长",
      dataIndex: "timeCostStr",
      width: 100,
    },
    {
      title: "群主所在组织",
      dataIndex: "ownerOrgName",
      width: 180,
      render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
    },
    {
      title: "群名",
      dataIndex: "groupName",
      width: 180,
      render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>,
    },
    { title: "群状态", dataIndex: "deliverStatusName", width: 100 },
    { title: "客户认定姓名", dataIndex: "customerName", width: 140 },
    { title: "客户类型", dataIndex: "customerTypeName", width: 140 },
    { title: "群主", dataIndex: "ownerName", width: 140 },
    { title: "群主岗位", dataIndex: "positionName", width: 140 },
    {
      title: "所属大区",
      dataIndex: "bigAreaName",
      isExtend: true,
      width: 180,
      //   render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>
    },
    {
      title: "所属省区",
      dataIndex: "middleAreaName",
      isExtend: true,
      width: 180,
      isExtend: true,
      width: 180,
      //   render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>
    },
    {
      width: 180,
      title: "所属用户中心",
      dataIndex: "dealerName",
      isExtend: false,
      width: 180,
      //   render: (text) => <PublicTooltip title={text}>{text}</PublicTooltip>
    },
    {
      title: "操作",
      width: 150,
      fixed: "right",
      dataIndex: "Operation",
      render: (text, record) => renderOperation(text, record),
    },
  ]);
  let newColumns = _.cloneDeep({ columns }).columns;
  for (let i = 0; i < newColumns.length; i++) {
    if (JSON.stringify(newColumns[i]["checked"]) !== undefined && !newColumns[i].checked) {
      newColumns.splice(i, 1);
      i--;
    }
  }
  const getOldMsg = () => {
    getRecord(chatListRef.current[0].id, recordRef.current.scrollHeight, 1);
  };
  const initPage = () => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let FormQueryH = document.getElementsByClassName("PublicList_FormQuery")[0].clientHeight;
    let h = winH - FormQueryH - 47 - 54 - 305;
    setTableHeight(h);
  };
  const closeSpin = () => {
    chatRowRef.current.closeSpin();
  };
  return (
    <>
      {pageSpin ? (
        <Button type="primary" onClick={closeSpin} className="spin-btn">
          关闭
        </Button>
      ) : null}
      <Spin
        spinning={pageSpin} //是否开启加载中
        tip="资源加载中，请稍候..." //loading文本，这里也可以设置为动态的文本
      >
        <div className="timeoutSearch">
          <PublicTableQuery
            ref={queryRef}
            isCatch={true}
            isFormDown={false}
            onSearch={onSearch}
            searchList={searchList}
            defaultQuery={defaultQuery}
          />
          <div className="tableData">
            <div className="table">
              <Row className="tableTitle">
                <Col className="text">超时消息列表</Col>
                <Col className="bts">
                  {roleJudgment(userInfo, "WECOM_CUSTOMER_GROUP_EXPORT") ? (
                    <Button
                      type="primary"
                      loading={exportLoading}
                      onClick={() => setExportVisible(true)}
                    >
                      超时明细导出
                    </Button>
                  ) : null}
                  <ExtendedColumn setColums={setColums} columns={columns} />
                </Col>
              </Row>

              <PublicTable
                isCatch={false}
                scroll={{ x: "max-content", y: tableHeight }}
                sticky={true}
                type={5}
                ref={tableRef}
                rowSelection={false}
                columns={newColumns}
                defaultQuery={defaultQuery}
                url={allUrl.WeCom.searchOvertimeChatRound}
              />
            </div>
            <Spin wrapperClassName="record" spinning={chatSpining}>
              {chatList.length ? (
                <div className="record-info">
                  <div className="flex space-between">
                    <div className="name ellipsis">
                      <Tooltip title={recordObj.groupName}>{recordObj.groupName}</Tooltip>
                    </div>
                    {!hasScroll ? (
                      <Button type="link" size="small" onClick={getOldMsg}>
                        查看更早消息
                      </Button>
                    ) : null}
                  </div>
                  <div className="tags">
                    {recordObj.tagNames && recordObj.tagNames.length
                      ? recordObj.tagNames.map((item, index) => {
                          return (
                            <Tag key={index} className="position ellipsis" color="orange">
                              <Tooltip title={item}>{item}</Tooltip>
                            </Tag>
                          );
                        })
                      : null}
                  </div>
                </div>
              ) : null}
              <div className="record-list" ref={recordRef} onScrollCapture={handleOnScrollCapture}>
                {chatList.length ? (
                  <div>
                    <ChatRow
                      ref={chatRowRef}
                      hasMore={chatListHasMore}
                      list={chatList}
                      beginId={recordObj.beginMsgId}
                      endId={recordObj.endMsgId}
                      qwUserId={recordObj.onwerUserId}
                      pageSpin={pageSpin}
                      setPageSpin={setPageSpin}
                    ></ChatRow>
                  </div>
                ) : (
                  <div className="record-empty">
                    <div className="empty" style={{}}>
                      <img
                        className="empty-img"
                        src={require("@/assets/img/empty1.png")}
                        style={{ width: 70, height: 70 }}
                      />
                      <div className="empty-content">暂无聊天数据</div>
                    </div>
                  </div>
                )}
              </div>
            </Spin>
          </div>
          <ExportModal
            exportExcel={exportExcel}
            visible={exportVisible}
            onCancel={() => setExportVisible(false)}
          ></ExportModal>
        </div>
      </Spin>
    </>
  );
};

export default Timeout;
