import React, { useState ,useEffect} from 'react'
import { post,get } from '@/utils/request'
import allUrl from '@/utils/url'
import { useSelector, useDispatch } from 'react-redux'
import { Modal,Table,Button,Popconfirm,Divider,message} from 'antd'
import {roleJudgment} from '@/utils/authority'
import InterviewRecords from './InterviewRecords'
import moment from 'moment';



const Interview = (props) =>{
    const { visible, onCancel,rowData,GetData} = props
    const [dataSource,setDataSource] = useState([])
    const [interviewResult,setInterviewResult]=useState()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading,setLoading] = useState(false)
    const [recordObj,setRecordObj] = useState({})
    const [operationVisible,setOperationVisible] = useState(false)
    const [title,setTitle]=useState({})
    const handleOk = () =>{
        GetData()
        onCancel()

    }
    // 表格
    const InforData = {
        rowKey: record => record.id,
        bordered: true,
        loading,
        pagination:false,
        dataSource,
        columns: [
            { title: '面试时间', dataIndex: 'interviewTime', width: 100,render:text=> text?moment(text).format('YYYY-MM-DD'):'' },
            { title: '面试官账号ID', dataIndex: 'interviewerId', width: 120 },
            { title: '面试官', dataIndex: 'interviewer', width: 100 },
            { title: '面试官所属组织', dataIndex: 'interviewerOrgName', width: 150 },
            { title: '面试成绩', dataIndex: 'score', width: 100 },
            // { title: '面试结果', dataIndex: 'score', width: 100 ,render:text=>text>=60?'通过':'不通过'},
            { title: '操作', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
        ],
    };
     // 操作
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'PERM_USER_RESET_INTERVIEW') ?
                    [
                        <span key={1} style={{ cursor: 'pointer' }} onClick={() => openModal(1,record)}>编辑</span>,
                        <Divider key={2} type="vertical" />
                    ] : null
            }
            {
                roleJudgment(userInfo,'PERM_USER_RESET_INTERVIEW') ?
                    <Popconfirm onConfirm={() => RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                        <span key={3} style={{ cursor: 'pointer' }}>删除</span>
                    </Popconfirm> : null
            }
        </div>
    }
    const openModal = (type,record) => {
        setOperationVisible(true)
        
        if(type==1){
            // 编辑
            console.log(record);
            console.log(rowData);
            setRecordObj(record)
            setTitle('编辑面试记录')
        }
        if (type==2){
            // 新增
             console.log(rowData);
             setTitle('添加面试记录')
             setRecordObj({})
        }
      
    }
    // 删除
    const RowDel=(record)=>{
        
        get(allUrl.Authority.delStoreKeyPostInterview,{id:record.id}).then(res => {
            if (res.success) {
                message.success(res.msg || '删除面试信息成功！')
                getTableData()
            } else {
                message.error(res.msg || '删除面试信息失败！')
            }
           
        })
    }
    const getTableData = () =>{
        // 数据获取接口
        setLoading(true)
        get(allUrl.Authority.getStoreKeyPostInterviewDetail,{userId:rowData.id}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                Dt.list.map((item,index)=>item.key = index)
                setDataSource(Dt.list)
                setInterviewResult(Dt.interviewResult)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }
    useEffect(()=>{
        getTableData()
    },[])
    return <Modal width={780} visible={visible} title='面试信息' onOk={handleOk} onCancel={onCancel} maskClosable={false} footer={null}>
        <div style={{paddingBottom:'20px'}}>面试结果:
        {
            interviewResult ?
        [
            interviewResult =='通过' ? 
            <a style={{ color: '#1890ff',marginLeft:'20px' }}key={1}>通过</a> : <a style={{ color: 'red',marginLeft:'20px' }}key={2}>不通过</a> 
        ]
        :
        [<a style={{ color: '#1890ff',marginLeft:'20px' }}key={3}>无面试记录</a>]
       }
        </div>
        <Table {...InforData}/>
        <div style={{width:'100%',textAlign:'center',marginTop:'20px'}}><Button onClick={()=>openModal(2)}type="primary" style={{borderRadius:'4px',width:'160px'}}>添加面试记录</Button></div>
        {
            operationVisible && 
            <InterviewRecords  visible={operationVisible} title={title} getTableData={getTableData} rowData={rowData} recordObj={recordObj} onCancel={()=>setOperationVisible(false)} />
        }
    </Modal>
}
export default Interview