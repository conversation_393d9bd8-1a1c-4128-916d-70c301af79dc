import React, { useState, memo, useEffect, useContext } from 'react';
import { Table, Tag } from 'antd';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
import styles from '../../antd.module.less';

const PublicTable = props => {
  const { dataSource } = props;

  return (
    <>
      <Table
        className={`${styles['gray-table']} basePayTable`}
        dataSource={dataSource.filter(item => Boolean(item))}
        rowKey={record => record.benefitsNo.toString()}
        size="small"
        bordered={true}
        pagination={false}
        scroll={{ x: 1840 }}
      >
        <Table.Column
          title="权益明细编码"
          key="benefitsNo"
          dataIndex="benefitsNo"
          width={220}
          fixed="left"
          render={(text, record) => {
            return (
              <div>
                <span>{text}</span>
              </div>
            );
          }}
        />
        <Table.Column
          title="权益分类"
          key="benefitsTypeName"
          dataIndex="benefitsTypeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="业务编码"
          key="businessCodeName"
          dataIndex="businessCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细名称"
          key="benefitsName"
          dataIndex="benefitsName"
          render={text => text || '-'}
          width={200}
          fixed="left"
        />
        <Table.Column
          title="工单号"
          key="businessCodeName"
          dataIndex="businessCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="物料名称"
          key="businessCodeName"
          dataIndex="businessCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="溯源码编码"
          key="businessCodeName"
          dataIndex="businessCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="质保开始日期"
          key="activityStartTime"
          dataIndex="activityStartTime"
          width={140}
          render={(text, record) => (text ? moment(Number(text)).format('YYYY-MM-DD') : '-')}
        />
        <Table.Column
          title="质保结束日期"
          key="activityEndTime"
          dataIndex="activityEndTime"
          width={140}
          render={(text, record) => (text ? moment(Number(text)).format('YYYY-MM-DD') : '-')}
        />
      </Table>
    </>
  );
};
export default memo(PublicTable);
