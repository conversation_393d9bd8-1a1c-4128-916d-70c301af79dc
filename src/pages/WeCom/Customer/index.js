import "./index.less";
import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Row, Col, Tooltip, Spin, Button, Modal, message } from "antd";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
import PublicTable from "@/components/Public/PublicTable";
import ExtendedColumn from "./components/ExtendedColumn";
import PublicTableQuery from "@/components/Public/PublicTableQuery";
import _ from "lodash";
import moment from "moment";
import { roleJudgment } from "@/utils/authority";
import { utilsDict } from "@/utils/utilsDict";
import DeliveryOrder from "./deliveryOrder";
import CustomerIdentification from "./components/CustomerIdentification";
import LabelInformation from "./components/LabelInformation";
import PublicTooltip from "@/components/Public/PublicTooltip";
import { QuestionCircleOutlined } from "@ant-design/icons";
import UploadFile from "@/components/Public/UploadFile";
import baseURL from "@/baseURL";
import ImportResultQuery from "./components/ImportResultQuery";
import Cookies from "js-cookie";

const CustomerList = (props) => {
  const renderStr = (arr, field, cutOut) => {
    if (cutOut) arr = arr.slice(0, 3);
    return arr.map((item, index) => (
      <div
        key={index}
        style={
          cutOut
            ? { whiteSpace: "nowrap", textOverflow: "ellipsis", width: "100%", overflow: "hidden" }
            : {}
        }
      >
        {field === "addTime" && item[field]
          ? moment(item[field]).format("YYYY-MM-DD HH:mm:ss")
          : item[field]}
        ；
      </div>
    ));
  };
  const tableRef = useRef();
  const { userInfo } = useSelector((state) => state.common);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [tableHeight, setTableHeight] = useState(0);
  const [recordObj, setRecordObj] = useState({});
  const [deliveryOrderVisible, setDeliveryOrderVisible] = useState(false);
  const [customerIdentificationVisible, setCustomerIdentificationVisible] = useState(false);
  const [labelInformationVisible, setLabelInformationVisible] = useState(false);
  const [exportVisible, setExportVisible] = useState(false);
  const [importResultVisible, setImportResultVisible] = useState(false);
  const yeasterDay = moment().subtract(1, "days").format("YYYY-MM-DD");

  const [columns, setColums] = useState([
    { title: "ID", dataIndex: "id", width: 120, fixed: "left" },
    { title: "微信昵称", dataIndex: "name", width: 120, fixed: "left" },
    { title: "客户认定姓名", dataIndex: "trueName", width: 120 },
    { title: "UserID", dataIndex: "externalUserId", width: 120, checked: false },
    { title: "性别", dataIndex: "gender", width: 80, render: (text) => utilsDict("gender", text) },
    { title: "客户手机号", dataIndex: "mobilePhone", width: 120 },
    { title: "添加人UserID", dataIndex: "addUserId", width: 160 },
    {
      title: "添加人姓名",
      dataIndex: "addUserName",
      width: 110,
      render: (text, record) => {
        return (
          <div>
            {
              <PublicTooltip title={renderStr(record.addUsers, "addUserName")}>
                {renderStr(record.addUsers, "addUserName", true)}
              </PublicTooltip>
            }
          </div>
        );
      },
    },
    {
      title: "添加人所属组织",
      dataIndex: "orgNames",
      width: 220,
      render: (text, record) => {
        return (
          <div>
            {
              <PublicTooltip title={renderStr(record.addUsers, "orgNames")}>
                {renderStr(record.addUsers, "orgNames", true)}
              </PublicTooltip>
            }
          </div>
        );
      },
    },
    {
      title: "添加人岗位",
      dataIndex: "positionName",
      width: 160,
      render: (text, record) => {
        return (
          <div>
            {
              <PublicTooltip title={renderStr(record.addUsers, "positionName")}>
                {renderStr(record.addUsers, "positionName", true)}
              </PublicTooltip>
            }
          </div>
        );
      },
    },
    {
      title: "添加时间",
      dataIndex: "addTime",
      width: 200,
      render: (text, record) => {
        return (
          <div>
            {
              <PublicTooltip title={renderStr(record.addUsers, "addTime")}>
                {renderStr(record.addUsers, "addTime", true)}
              </PublicTooltip>
            }
          </div>
        );
      },
    },
    {
      title: "标签信息",
      dataIndex: "tagName",
      width: 100,
      checked: false,
      render: (text, record) => (
        <div style={{ textAlign: "center" }}>
          <a
            onClick={() => {
              setRecordObj(record);
              setLabelInformationVisible(true);
            }}
          >
            查看
          </a>
        </div>
      ),
    },
    { title: "是否认定", width: 100, dataIndex: "affirmStatusName", checked: false },
    { title: "客户类型", dataIndex: "customerTypeName", width: 120 },
    {
      title: (
        <div>
          是否建群&nbsp;
          <Tooltip title={`统计截止：${yeasterDay} 23:59:59`}>
            <QuestionCircleOutlined />
          </Tooltip>
        </div>
      ),
      dataIndex: "statisticsGroupStatus",
      checked: false,
      width: 100,
      render: (text, record) => renderGroupStatus(text, record),
    },
    {
      title: "建群时间",
      dataIndex: "groupTime",
      width: 180,
      render: (text) => (text ? moment(text).format("YYYY-MM-DD HH:mm:ss") : ""),
    },
    {
      title: (
        <div>
          建群是否合格&nbsp;
          <Tooltip title={`统计截止：${yeasterDay} 23:59:59`}>
            <QuestionCircleOutlined />
          </Tooltip>
        </div>
      ),
      dataIndex: "groupIsQualified",
      checked: false,
      width: 100,
    },
    {
      title: "操作",
      width: 120,
      fixed: "right",
      dataIndex: "Operation",
      render: (text, record) => renderOperation(text, record),
    },
  ]);

  const onSearch = (values) => {
    if (values.addTime && values.addTime.length) {
      values.startAddTime = moment(values.addTime[0]).format("YYYY-MM-DD HH:mm:ss");
      values.endAddTime = moment(values.addTime[1]).format("YYYY-MM-DD HH:mm:ss");
      delete values.addTime;
    }
    if (values.GroupTime && values.GroupTime.length) {
      values.startGroupTime = moment(values.GroupTime[0]).format("YYYY-MM-DD HH:mm:ss");
      values.endGroupTime = moment(values.GroupTime[1]).format("YYYY-MM-DD HH:mm:ss");
      delete values.GroupTime;
    }
    setDefaultQuery(values);
  };
  const UploadChange = ({ file, fileList }, type) => {
    console.log(file, type);
    const { response } = file;
    if (file.status === "uploading") {
      setImportLoading(true);
    }
    if (file.status === "done") {
      if (response.success) {
        message.success("文件上传完成，可点击“导入结果”查看任务详情");
        tableRef.current.getTableData();
      } else {
        message.error(response.msg || response.msgCode);
      }
      setImportLoading(false);
    }
    if (file.status === "error") {
      message.error(response.msg || response.msgCode);
      setImportLoading(false);
    }
  };
  const download = () => {
    let url =
      "https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/template/%E7%89%B9%E6%AE%8A%E5%A4%87%E6%A1%88%E4%BA%A4%E4%BB%98%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx";
    window.open(url);
  };

  const renderGroupStatus = (text, record) => {
    // 0:待更新,1:未建群,2:已建群
    let str = "";
    if (text == 0) {
      str = "待更新";
    } else if (text == 1) {
      str = "未建群";
    } else {
      str = "已建群";
    }
    return str;
  };
  const RowDetail = (record) => {
    setRecordObj(record);
    setDeliveryOrderVisible(true);
  };
  const CustomerIdentificationOperation = (record) => {
    setRecordObj(record);
    setCustomerIdentificationVisible(true);
  };
  const renderOperation = (text, record) => {
    return (
      <div style={{ color: "#1890ff" }}>
        {roleJudgment(userInfo, "WECOM_CUSTOMER_CUSTOMER_IDENTIFICATION")
          ? [
              <div key={2}>
                <a onClick={() => CustomerIdentificationOperation(record)}>客户认定</a>
              </div>,
            ]
          : null}
        {roleJudgment(userInfo, "WECOM_CUSTOMER_DELIVERY_ORDERS_VIEW")
          ? [
              <div key={2}>
                <a onClick={() => RowDetail(record)}>查看交付订单</a>
              </div>,
            ]
          : null}
      </div>
    );
  };

  /**
   *
   * @param {*} url 后台返回的请求地址
   * @param {*} fn 回调函数
   */
  const getOssBuffer = (url, fn) => {
    var xhr = new XMLHttpRequest();
    xhr.responseType = "blob";
    xhr.open("get", url, true);
    xhr.setRequestHeader("x-oss-meta-authorization", Cookies.get("scrm_token"));
    xhr.setRequestHeader("authorization", Cookies.get("scrm_token"));
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status == 200) {
        console.log("xhr", xhr);
        fn(xhr.response);
      } else {
        return false;
      }
    };
    xhr.send();
  };
  const ExportExcel = () => {
    setExportLoading(true);
    post(allUrl.WeCom.qwsalesOrderExportGroup, {}).then((res) => {
      if (res.success && res.resp[0]) {
        getOssBuffer(res.resp[0].url, (data) => {
          message.success("导出成功！");
          let blob = new Blob([data], { type: data.type });
          console.log("blob", moment(new Date()).format("YYYY-MM-DD HH:MM:SS"));
          if (window.navigator.msSaveOrOpenBlob) {
            //兼容ie
            window.navigator.msSaveBlob(
              blob,
              `建群明细${moment(new Date()).format("YYYY-MM-DD HH:MM:SS")}` || "未知文件"
            );
          } else {
            let downloadElement = document.createElement("a");
            let href = window.URL.createObjectURL(blob); //创建下载的链接
            downloadElement.href = href;
            downloadElement.download =
              `建群明细${moment(new Date()).format("YYYY-MM-DD HH:MM:SS")}` || "未知文件"; //下载后文件名
            document.body.appendChild(downloadElement);
            downloadElement.click(); //点击下载
            document.body.removeChild(downloadElement); //下载完成移除元素
            window.URL.revokeObjectURL(href); //释放掉blob对象
          }
        });
      } else {
        // message.error(res.msg);
      }
    });
    setExportLoading(false);
    setExportVisible(false);
  };

  let searchList = [
    { label: "微信昵称", name: "name", type: "Input", colSpan: 6 },
    { label: "客户认定姓名", name: "trueName", type: "Input", colSpan: 6 },
    { label: "添加人姓名", name: "addUserName", type: "Input", colSpan: 6 },
    { label: "手机号", name: "mobilePhone", type: "Input", colSpan: 6 },
    // { label: '交付专员名称', name: 'name', type: 'Input', colSpan: 6 },
    // { label: '交付专员所属门店', name: 'name', type: 'Input', colSpan: 6 },
    { label: "交付单号", name: "orderNo", type: "Input", colSpan: 6 },
    { label: "车辆VIN", name: "vin", type: "Input", colSpan: 6 },
    {
      label: "是否建群",
      name: "isGroup",
      type: "Select",
      colSpan: 6,
      data: [
        { name: "是", value: "1" },
        { name: "否", value: "0" },
      ],
    },
    { label: "添加时间", name: "addTime", type: "RangePicker", colSpan: 6, showTime: true },
    { label: "建群时间", name: "GroupTime", type: "RangePicker", colSpan: 6, showTime: true },
  ];

  const getCheckboxProps = (record) => {
    return { disabled: record.dealerCode ? false : true };
  };

  const initPage = () => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let FormQueryH = document.getElementsByClassName("PublicList_FormQuery")[0].clientHeight;
    let h = winH - FormQueryH - 47 - 54 - 345;
    setTableHeight(h);
  };

  let newColumns = _.cloneDeep({ columns }).columns;
  for (let i = 0; i < newColumns.length; i++) {
    if (JSON.stringify(newColumns[i]["checked"]) !== undefined && !newColumns[i].checked) {
      newColumns.splice(i, 1);
      i--;
    }
  }

  useEffect(() => {
    // onSearch()
    initPage();
  }, []);

  return (
    <div className="CustomerList">
      <Spin spinning={loading}>
        <PublicTableQuery
          isCatch={true}
          isFormDown={false}
          onSearch={onSearch}
          searchList={searchList}
        />
        <div className="tableData">
          <Row className="tableTitle">
            <Col className="text">客户列表</Col>
            <Col className="bts">
              {roleJudgment(userInfo, "WECOM_CUSTOMER_GROUP_EXPORT") ? (
                <Button
                  type="primary"
                  loading={exportLoading}
                  onClick={() => setExportVisible(true)}
                >
                  建群明细导出
                </Button>
              ) : null}
              {roleJudgment(userInfo, "WECOM_CUSTOMER_SPECIALORDER_IMPORT") ? (
                <>
                  <UploadFile
                    style={{ display: "inline-block" }}
                    extension={["xls", "xlsx"]}
                    showUploadList={false}
                    size={10}
                    action={baseURL.Host + allUrl.WeCom.importSpecialFilingExcel}
                    UploadChange={UploadChange}
                  >
                    <Button loading={importLoading}>导入</Button>
                  </UploadFile>
                  <a onClick={download}>模版下载</a>
                </>
              ) : null}
              {roleJudgment(userInfo, "WECOM_CUSTOMER_SPECIALORDER_IMPORT_RESULT") ? (
                <a style={{ marginLeft: 10 }} onClick={() => setImportResultVisible(true)}>
                  导入结果
                </a>
              ) : null}

              <ExtendedColumn setColums={setColums} columns={columns} />
            </Col>
          </Row>
          <PublicTable
            isCatch={true}
            scroll={{ x: "max-content", y: tableHeight }}
            type={4}
            sticky={true}
            ref={tableRef}
            scro
            rowSelection={false}
            getCheckboxProps={getCheckboxProps}
            columns={newColumns}
            defaultQuery={defaultQuery}
            url={allUrl.WeCom.getPageCustomer}
          />
        </div>
      </Spin>
      {deliveryOrderVisible && (
        <DeliveryOrder
          id={recordObj.id}
          visible={deliveryOrderVisible}
          onCancel={() => setDeliveryOrderVisible(false)}
        />
      )}
      {customerIdentificationVisible && (
        <CustomerIdentification
          recordObj={recordObj}
          visible={customerIdentificationVisible}
          onCancel={() => setCustomerIdentificationVisible(false)}
        />
      )}
      {labelInformationVisible && (
        <LabelInformation
          recordObj={recordObj}
          visible={labelInformationVisible}
          onCancel={() => setLabelInformationVisible(false)}
        />
      )}
      <Modal
        title="导出提示"
        visible={exportVisible}
        onCancel={() => setExportVisible(false)}
        onOk={() => ExportExcel()}
        confirmLoading={exportLoading}
      >
        <div>
          当前数据统计截至{moment(new Date()).subtract(1, "days").format("YYYY-MM-DD 23:59:59")}
          ，是否确认导出？
        </div>
      </Modal>
      {importResultVisible && (
        <ImportResultQuery
          visible={importResultVisible}
          title="导入结果查看"
          onCancel={() => setImportResultVisible(false)}
        />
      )}
    </div>
  );
};
export default CustomerList;
