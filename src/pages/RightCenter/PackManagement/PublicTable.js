import React, { memo, useState, useContext } from 'react';
import { Table, Tag, Button } from 'antd';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
import LadderPartDetail from '@/pages/RightCenter/DetailManagement/maintenanceLadder/LadderPartDetail';
import { get } from '@/utils/request';
import allUrl from '@/utils/url';
import { utilsDict } from '@/utils/utilsDict';
import { strategyConfig } from '@/pages/RightCenter/DetailManagement/formConfig';
import styles from '../antd.module.less';
import { extendFlag } from '../publicMethods';
import { InjectData } from '@/pages/RightCenter/PackManagement/Create';

const PublicTable = props => {
  const {
    className,
    dataSource,
    loading,
    rowKey,
    pagination,
    rowSelection,
    expandable,
    children,
    type,
    insideTable,
    rowClassName,
  } = props;
  const equityType = useContext(InjectData)?.equityType;
  const [detail, setDetail] = useState({});
  const [ladderPartDetailVisible, setLadderPartDetailVisible] = useState(false);
  let list = [];

  if (type) {
    dataSource.map(item => {
      if (item[type]) {
        list.push(item[type]);
      }
    });
  } else {
    list = dataSource;
  }

  //阶梯保养
  const handleLadderVisible = () => {
    setLadderPartDetailVisible(!ladderPartDetailVisible);
  };

  const getLadderData = record => {
    if (record.detailNo) {
      get(allUrl.EquityPackageManage.getDetailLadderInfo, {
        detailNo: record.detailNo,
        inside: 1, //对内对外标识  对内为1 对外为2
      })
        .then(res => {
          const { success, resp } = res;
          if (success) {
            if (!!resp.length) {
              setDetail({
                ...record,
                maintenanceLadderLimitFrame: record.maintenanceLadderLimitFrame,
                maintenanceLadderDetail: resp,
              });
            }
          }
        })
        .finally(() => {
          handleLadderVisible();
        });
    } else {
      //升级未保存的列表
      setDetail({ ...record });
      handleLadderVisible();
    }
  };

  return (
    <>
      <Table
        className={className}
        dataSource={list}
        loading={loading}
        rowKey={rowKey}
        size="small"
        scroll={{ x: 3140 }}
        bordered
        pagination={pagination}
        rowSelection={rowSelection}
        expandable={expandable}
        rowClassName={rowClassName}
      >
        <Table.Column
          title="权益明细编码"
          key="detailNo"
          dataIndex="detailNo"
          width={200}
          fixed="left"
          className={styles['table-pad-left']}
          render={(text, record) => {
            if (record.businessScene === 2) {
              return extendFlag({ text: text || '-' });
            } else {
              return text || '-';
            }
          }}
        />
        <Table.Column
          title="权益明细分类"
          key="sortCodeName"
          dataIndex="sortCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细名称"
          key="name"
          dataIndex="name"
          render={text => text || '-'}
          width={200}
          fixed="left"
        />
        <Table.Column
          title="权益业务场景"
          key="businessSceneName"
          dataIndex="businessSceneName"
          render={text => text || '-'}
          width={120}
        />
        <Table.Column
          title="权益类别"
          key="typeCodeName"
          dataIndex="typeCodeName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="车辆属性"
          key="carAttrName"
          dataIndex="carAttrName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益归属"
          key="belongName"
          dataIndex="belongName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="延保激活时间"
          key="extendWarrantyActiveTimeName"
          dataIndex="extendWarrantyActiveTimeName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益限制年限"
          key="limitYear"
          dataIndex="limitYear"
          width={120}
          className={styles['table-pad-left']}
          render={(text, record) => {
            let span = <span>-</span>;
            if (record.limitTimeFrame == -1) {
              span = <span>永久有效</span>;
            } else if (record.limitTimeFrame == 1) {
              span = (
                <span>{`${Number(record.limitYear)}年${Number(record.limitMonth)}月${Number(record.limitDay)}日`}</span>
              );
            }
            if (record.businessScene === 2) {
              return extendFlag({ text: span });
            } else {
              return span;
            }

            // return record.limitYear && record.yearUnit ? record.limitYear + getYearUnitName(record.yearUnit) :'不限年限'
          }}
        />
        <Table.Column
          title="权益生效时间"
          key="relativeTime"
          dataIndex="relativeTime"
          className={styles['table-pad-left']}
          width={230}
          render={(text, record) => {
            const res = record.relativeTimeName ? (
              record.relativeTimeName
            ) : (
              <>
                {record.fixedBeginTime && record.fixedEndTime
                  ? moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') +
                    ' ~ ' +
                    moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')
                  : '-'}
              </>
            );
            if (record.businessScene === 2) {
              return extendFlag({ text: res });
            } else {
              return res;
            }
          }}
        />
        <Table.Column
          title="权益限制里程(基准)"
          key="limitLegend"
          dataIndex="limitLegend"
          className={styles['table-pad-left']}
          width={180}
          render={(text, record) => {
            const limitLegendFrameMap = {
              0: () => '-', //无此策略
              1: () => (
                <span>
                  {record.limitLegend ?? '-'}
                  <Tag className={[styles['custom-ant-tag-green'], styles['ml5']]}>行驶</Tag>
                </span>
              ), //行驶里程
              2: () => (
                <span>
                  {record.extenderMileage ?? '-'}
                  <Tag className={[styles['custom-ant-tag-yellow'], styles['ml5']]}>增程器</Tag>
                </span>
              ), //增程器里程
            };
            let res;
            if (record.limitLegendFrame === null) {
              res = record.limitLegend || record.extenderMileage;
            } else {
              res = limitLegendFrameMap[record.limitLegendFrame]();
            }
            if (record.businessScene === 2) {
              return extendFlag({ text: res });
            } else {
              return res;
            }
          }}
        />

        {/* 权益生效里程 */}
        <Table.Column
          title="权益生效里程"
          key="effectMileageFrame"
          dataIndex="effectMileageFrame"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            const effectMileageFrameMap = {
              //无此策略
              0: '-',
              //固定里程
              1: record.fixedBeginMileage ?? '-',
              //相对里程
              2: record.relativeBeginMileageStrategyName,
            };
            if (record.businessScene === 2) {
              return extendFlag({ text: effectMileageFrameMap[record.effectMileageFrame] });
            } else {
              return effectMileageFrameMap[record.effectMileageFrame];
            }
          }}
        />

        {/* <Table.Column title='权益包属性' key='sortCodeName' dataIndex='sortCodeName' render={text=>text || '-'} /> */}
        {/* <Table.Column title='权益车型' key='carAttrName' dataIndex='carAttrName' /> */}
        <Table.Column
          title="享有权益频次"
          key="frequency"
          dataIndex="frequency"
          width={120}
          render={text => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限次数' : text + '次';
            } else {
              a = '-';
            }
            return a;
          }}
        />
        <Table.Column
          title="车辆权益变更属性"
          key="identityName"
          dataIndex="identityName"
          width={160}
          render={text => text || '-'}
        />
        <Table.Column
          title="享有车主身份"
          key="carIdentityName"
          dataIndex="carIdentityName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="商品分类"
          key="goodsCategoryName"
          dataIndex="goodsCategoryName"
          width={120}
          render={text => text || '-'}
        />
        {
          // 精品权益专属字段
          equityType === 6 && (
            <Table.Column
              title="权益所属"
              key="goodsTypeName"
              dataIndex="goodsTypeName"
              width={120}
              render={text => text || '-'}
            />
          )
        }
        <Table.Column
          title="全保养阶梯策略"
          key="maintenanceLadderDesc"
          dataIndex="maintenanceLadderDesc"
          width={120}
          render={(text, record) => {
            if (text) {
              if (text === '-') return '-';
              // return <>{text}<Button type='link' size='small' onClick={() => getLadderData(record)}>详情</Button></>
              return <>{text}</>;
            } else {
              switch (record.maintenanceLadderFrame) {
                case 0:
                  return '-';
                case 1:
                  // return <>{record.maintenanceLadder}次<Button type='link' size='small' onClick={() => getLadderData(record)}>详情</Button></>
                  return <>{record.maintenanceLadder}次</>;
              }
            }
          }}
        />
        <Table.Column
          title="补漆面部位"
          key="paintDesc"
          dataIndex="paintDesc"
          width={200}
          ellipsis
          render={(text, record) => {
            if (text) {
              return <PublicTooltip title={text}>{text}</PublicTooltip>;
            } else {
              switch (record.paintFrame) {
                case 0:
                  return '-';
                case -1:
                  return '不限面数';
                case 1:
                  const filter = record?.paintList?.filter(i => i.num > 0) || [];
                  let sum = 0;
                  const strArr =
                    filter.map(item => {
                      sum += item.num;
                      return `${item.name}*${item.num} `;
                    }) || [];
                  const str = strArr.join(',') + `, 共${sum}面`;
                  return <PublicTooltip title={str}>{str}</PublicTooltip>;
              }
            }
          }}
        />
        {/* <Table.Column title='权益包属性' key='sortCodeName' dataIndex='sortCodeName' render={text=>text || '-'} /> */}
        {/* <Table.Column title='权益车型' key='carAttrName' dataIndex='carAttrName' /> */}

        <Table.Column
          title="车联网流量"
          key="traffic"
          dataIndex="traffic"
          width={120}
          render={(text, record) => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限流量' : text + record.trafficUnit;
            } else {
              a = '-';
            }
            return a;

            // return record.traffic  && record.trafficUnit ? record.traffic + ' ' + record.trafficUnit : '不限流量'
          }}
        />
        <Table.Column
          title="权益抵扣金额"
          key="deduction"
          dataIndex="deduction"
          width={200}
          render={(text, record) => {
            return record.payAmount && record.deduction ? (
              <span>
                {record.payAmount} 元 ~ {record.deduction} 元
              </span>
            ) : (
              '-'
            );
          }}
        />

        {/* <Table.Column title='权益支持区域' key='provinceName' dataIndex='provinceName' width={200} ellipsis render={(text, record) => {
                        let str = ''
                        if (record.areas && record.areas.length) {
                            let obj = record.areas[0]
                            str = `${obj.provinceName || ''}  ${obj?.cityName || ''} ${obj?.areaName || ''}`
                        } else {
                            str = '-'
                        }
                        return <div>
                            {
                                record.allArea == '0' ? '全部区域' : <PublicTooltip placement="topLeft" title={str}>{str}</PublicTooltip>
                            }
                        </div>
                    }} />
                    <Table.Column title='权益支持门店' key='dealersArr' dataIndex='dealersArr' width={200} ellipsis={true} render={(text, record) => {
                        let temp = []
                        if (record.dealers && record.dealers.length) {
                            record.dealers.forEach(item => {
                                temp.push(item.dealerCodeName)
                            })
                        } else {
                            temp = ['-']
                        }
                        return <div>
                            {
                                record.allDealer == '0' ? '全部门店' : <PublicTooltip placement="topLeft" title={temp.join(',')}>{temp.join(',')}</PublicTooltip>
                            }
                        </div>
                    }} /> */}
        <Table.Column
          title="权益履约方"
          key="performingDesc"
          dataIndex="performingDesc"
          width={200}
          ellipsis
          render={(text, record) => {
            if (text) {
              return <PublicTooltip title={text}>{text}</PublicTooltip>;
            } else {
              let arr = [];
              record.performingList &&
                record.performingList.map(item => {
                  arr.push(item.label || item.name);
                });
              const str = arr.join(',');
              return <PublicTooltip title={str}>{str}</PublicTooltip>;
            }
          }}
        />
        <Table.Column
          title="履约结算价格"
          key="settlementPriceDesc"
          dataIndex="settlementPriceDesc"
          width={120}
          render={(text, record) => {
            if (text) {
              return text;
            } else {
              switch (record.settlementPriceFrame) {
                case 0:
                  return '-';
                case 1:
                  return '实际结算';
                case 2:
                  const price = record.settlementPrice;
                  return price.toFixed(2) + '元';
              }
            }
          }}
        />
        <Table.Column
          title="延保承保方"
          key="extendWarrantyAssurerName"
          dataIndex="extendWarrantyAssurerName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="业务编码"
          key="businessCodeDesc"
          dataIndex="businessCodeDesc"
          width={120}
          render={(text, record) => {
            return record.businessCodeDesc || record.businessCodeName || '-';
          }}
        />
        <Table.Column
          title="是否支持退款"
          key="refundName"
          dataIndex="refundName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="是否三方履约"
          key="performanceName"
          dataIndex="performanceName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="能否重复购买"
          key="isRepeatablePurchaseName"
          dataIndex="isRepeatablePurchaseName"
          width={120}
          render={text => text || '-'}
        />

        <Table.Column
          title="权益声明"
          key="statement"
          dataIndex="statement"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益备注"
          key="remark"
          dataIndex="remark"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益明细版本编码"
          key="detailVersion"
          dataIndex="detailVersion"
          width={200}
          ellipsis
          render={text => text || '-'}
        />

        {insideTable ? (
          <>
            <Table.Column
              title="客户端展示形式"
              key="displayFrame"
              dataIndex="displayFrame"
              width={120}
              ellipsis
              render={(text, record) => {
                const obj = {
                  1: '并列',
                  2: '聚合',
                  3: '组合',
                };
                return text ? obj[text] : '-';
              }}
            />
            <Table.Column
              title="是否在客户端展示"
              key="isDisplay"
              dataIndex="isDisplay"
              width={150}
              ellipsis
              render={(text, record) => {
                const obj = {
                  1: '在客户端展示',
                  2: '不在客户端展示',
                };
                return record.isDisplay ? obj[record.isDisplay] : '-';
              }}
            />
          </>
        ) : null}
        {children}
      </Table>
      {/* 阶梯保养详情 */}
      {ladderPartDetailVisible && (
        <LadderPartDetail
          handleLadderVisible={handleLadderVisible}
          ladderPartDetailVisible={ladderPartDetailVisible}
          data={{
            ...detail,
            rTimeName: detail.relativeTimeName,
            rBeginMileagegName: detail[strategyConfig['effectMileageFrame'].c.relativeBeginMileageStrategyName.value],
          }}
          strategyConfig={strategyConfig}
        />
      )}
    </>
  );
};
export default memo(PublicTable);
