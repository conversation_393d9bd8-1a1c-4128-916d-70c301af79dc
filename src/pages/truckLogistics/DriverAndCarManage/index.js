import './index.less'
import React, { useEffect, useState,useRef } from 'react'
import { useSelector,useDispatch } from 'react-redux'
import { Table,message, Tabs ,Row,Col,Button,Modal,Divider,Popconfirm,Form,Input,Select,TreeSelect} from 'antd'
import moment from 'moment'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import { post } from '@/utils/request';
import allUrl from '@/utils/url';
import {roleJudgment} from '@/utils/authority'
import baseURL from '@/baseURL'
import UploadFile from '@/components/Public/UploadFile'
import {getDict} from '../../../actions/async'
import { renderStatusName } from '@/utils'
import _ from "lodash";
const {Option} = Select
const { TabPane } = Tabs;
const USER_TYPE = [ { name: '全部',key: 0, value: '' },{ name: '司机',key: 1, value: 1 }, { name: '门卫',key: 2, value: 2 },]
const ORG_TYPE = [ { name: '物流商',key: 1, value: 1 }, { name: '工厂',key: 2, value: 2 },]
const DriverAndCarManage = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const dispatch = useDispatch()
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const [current, setCurrent] = useState(1)
    const [pageSize, setPageSize] = useState(10)
    const [total, setTotal] = useState(0)
    const [tableHeight,setTableHeight] = useState(0)
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('DriverTabsKey') || '1')
    const [dictList,setDictList] = useState({})
    const [importLoading, setImportLoading] = useState(false)
    const [operationVisible1,setOperationVisible1] = useState(false)
    const [operationVisible2,setOperationVisible2] = useState(false)
    const [operationVisible3,setOperationVisible3] = useState(false)
    const [operationVisible4,setOperationVisible4] = useState(false)
    const [operationVisible5,setOperationVisible5] = useState(false)
    const [recordObj,setRecordObj] = useState({})
    const [title,setTitle]=useState({})
    const [defaultQuery, setDefaultQuery] = useState({
        // lastFollowTime:[moment().subtract(1,'months'),moment()],
        // createTime: [moment().subtract(1, 'months'), moment()],
    })
    const [driverform] = Form.useForm();
    const driverformRef = useRef(null);
    const [orgform] = Form.useForm();
    const orgformRef = useRef(null);
    const [processform] = Form.useForm();
    const processformRef = useRef(null);
    const [carform] = Form.useForm();
    const carformRef = useRef(null);
    const [logisticsProviderform] = Form.useForm();
    const logisticsProvidermRef = useRef(null);
    const tableRef1 = useRef()
    const tableRef2 = useRef()
    const tableRef3 = useRef()
    const [logisticsProviderList,setLogisticsProviderList]= useState([]);
    const [orgDataList,setorgDataList]= useState([]);
    const [processStartScope,setprocessStartScope]= useState(0);
    const [processEndScope,setprocessEndScope]= useState(0);
    const [startScope,setStartScope]= useState(0);
    const [endScope,setEndScope]= useState(0);
    const [orgId,setorgId]= useState();
    const [identification,setidentification]= useState(true);
    const [dirverOrgs, setDirverOrgs] = useState([])
    const [guardOrgs, setGuardOrgs] = useState([])
    const [phoneVisible, setPhoneVisible] = useState(false)
    const [pePhone, setPePhone] = useState('')
    const orgOnChange =(value)=>{
        // 组织改变
        setorgId(value)
    }
    const orgOnSearch =_.debounce((value)=>{
        // 模糊搜索
        setorgId(value)
            getOrgData(value)
    },500)
    const ScopeChange =(value,key)=>{
        // 起运/送达范围改变
        if(key == 1){
            // 起运
            setStartScope(value)
        }
        if(key == 2){
            // 送达
            setEndScope(value)
        }

    }
//    获取组织列表
    const getLogisticsProviderList =(type)=>{
         // 获取组织列表
        post(allUrl.truckLogistics.logisticsProviderList, {type: type || ''}).then(res => {
            if (res.success) {
                setLogisticsProviderList(res.resp[0])
                type == 1 && setDirverOrgs(res.resp[0])
                type == 2 && setGuardOrgs(res.resp[0])
                // driverform.setFieldsValue({logisticsProviderNoList: res.resp[0]})
            } else {
                message.error(res.msg || '获取组织失败')
            }
        })
    }
    const getOrgData = (name = "") => {
        // 获取组织列表
        post(allUrl.Authority.messageSendOrgList, { name }).then((res) => {
        if (res.success) {
            const arr = res.resp.slice(0);
            setorgDataList(arr)

        } else {
            message.error(res.msg);
        }
        });
    };
    const onSearch = (values) => {
        setDefaultQuery(values)
    }
    const renderOperation1 = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
        {
            roleJudgment(userInfo,'TRUCK_DRIVER_PHONE') ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openPhoneModal(record, 1)}>查看手机号</span>,
                ] : null
        }
        {
            roleJudgment(userInfo,'TRUCK_DRIVER_UNLOCK') ?
                <Popconfirm onConfirm={() => RowUnlock(record)} title='确定解锁吗？' okText='确定' cancelText='取消'>
                    <Divider key={2} type="vertical" />
                    <span key={3} style={{ cursor: 'pointer' }}>登录解锁</span>
                </Popconfirm> : null
        }
        {
            roleJudgment(userInfo,'TRUCK_DRIVER_CHANGE_INFORMATION') ?
                <span>
                    <Divider key={2} type="vertical" />
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openModal(1,record)}>编辑</span>
                </span>
                : null
        }
        {
            roleJudgment(userInfo,'TRUCK_DRIVER_DELETE') ?
                <Popconfirm onConfirm={() => RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                    <Divider key={2} type="vertical" />
                    <span key={3} style={{ cursor: 'pointer' }}>删除</span>
                </Popconfirm> : null
        }
    </div>
    }
    const renderOperation2 = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
        {
            roleJudgment(userInfo,'TRUCK_CAR_CHANGE_INFORMATION') ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openModal(1,record)}>编辑</span>,
                ] : null
        }
        {
            roleJudgment(userInfo,'TRUCK_CAR_DELETE') ?
                <Popconfirm onConfirm={() => RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                <Divider key={2} type="vertical" />
                    <span key={3} style={{ cursor: 'pointer' }}>删除</span>
                </Popconfirm>
                : null
        }
    </div>
    }
    const renderOperation3 = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
        {
            roleJudgment(userInfo,'TRUCK_SERVICE_CHANGE_INFORMATION') ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openModal(1,record)}>编辑</span>,
                ] : null
        }
        {
            roleJudgment(userInfo,'TRUCK_SERVICE_UNBOUND_TISSUE') && record.orgId ?
            <Popconfirm onConfirm={() => unbindOrg(record)} title='确定解绑组织吗？' okText='确定' cancelText='取消'>
            <Divider key={2} type="vertical" />
                <span key={3} style={{ cursor: 'pointer' }}>解绑组织</span>
            </Popconfirm>
            : null
        }
         {
            roleJudgment(userInfo,'TRUCK_SERVICE_BOUND_TISSUE') && !record.orgId ?
            <span>
            <Divider key={2} type="vertical" />
                <span key={3} style={{ cursor: 'pointer' }}onClick={() => bindTissue(record)}>绑定组织</span>
            </span>
            : null
        }
        {
            roleJudgment(userInfo,'TRUCK_DRIVER_ORGANIZATION_PHONE') ?
                <span>
                    <Divider key={2} type="vertical" />
                    <span key={3} style={{ cursor: 'pointer' }}onClick={() => openPhoneModal(record, 2)}>查看手机号</span>
                </span>
            : null
        }
    </div>
    }
    const tabsCallback = (key) => {
        setTabsKey(key)
        setCurrent(1)
        setPageSize(10)
        sessionStorage.setItem('DriverTabsKey',key)
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }

    const downloadDataFailed=(response)=>{
        let url = response.resp[0].errorUrl
        window.open(url)
    }
    const UploadChange = ({ file, fileList }, type) => {
        console.log(file,type,'file的数据')
        const { response } = file
        if (file.status === 'uploading') {
            setImportLoading(true)
        }
        if (file.status === 'done') {
            if (response.success) {
                let key =2;
                message.loading({ content: '正在导入中，请稍后', key });
                setTimeout(() => {
                    message.success({ content: '导入成功!', key, duration: 2 });
                }, 1000);
            } else if(response.resp[0].errorUrl) {
                let key =2;
                message.loading({ content: '正在导入中，请稍后', key });
                setTimeout(() => {
                    message.error({content:<span>导入列表失败，点击"<a onClick={() =>downloadDataFailed(response)}>确定</a>"下载导入失败数据</span>, key, duration: 10 });
                }, 1000);
            } else {
                message.error(response.msg || response.msgCode)
            }
            setImportLoading(false)
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msgCode)
            setImportLoading(false)
        }
    }
    const download = () => {
        if(tabsKey=='1'){
            // 用户模版url
            let url1 = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/template/%E5%8F%B8%E6%9C%BA%E6%A8%A1%E7%89%88_%E6%96%B0v2.15.xlsx'
            window.open(url1)
        }else if(tabsKey=='2'){
            // 板车模版ur
            let url2 = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/template/%E6%9D%BF%E8%BD%A6%E6%A8%A1%E7%89%88_%E6%96%B0.xlsx'
            window.open(url2)
        }
    }
    const bindTissue =(record)=>{
        // 绑定组织
        setRecordObj(record)
        setOperationVisible5(true)
    }
    const openModal = (type,record) => {
        console.log('xxx', type, driverform.current, logisticsProviderform.getFieldValue('name'))
        // 编辑or新增
        if(tabsKey=='1'){
            setOperationVisible1(true)
        }
        if(tabsKey=='2'){
            setOperationVisible2(true)
        }
        if(tabsKey=='3'){
            setOperationVisible3(true)
        }
        if(type==1){
            // 编辑
            openPhoneModal(record, tabsKey == '1' ? 1 : 2, true)
            setidentification(true)
            if(dirverOrgs.length == 0){
                getLogisticsProviderList(record.type || 1)
            } else {
                if(record.type == 1) {
                    setLogisticsProviderList(dirverOrgs)
                }else {
                    setLogisticsProviderList(guardOrgs)

                }
            }

            setTitle('编辑信息')
            console.log('编辑按钮', record)
        }
        if (type==2){
            // 新增
            setidentification(false)
             setTitle('添加信息')
             dirverOrgs.length == 0 && getLogisticsProviderList(1)
             if(tabsKey == 1) {
                driverform.setFieldsValue({
                    type: 1,
                    logisticsProviderNo:[],
                    driverName: '',
                    driverPhone: ''
                })
             }
             if(tabsKey == 3) {
                logisticsProviderform.setFieldsValue({
                    type: 1,
                    logisticsProviderNo: '',
                    logisticsProviderNoName: '',
                    orgId: '',
                    logisticsProviderUserName: '',
                    logisticsProviderPhone: ''
                    // name:values.logisticsProviderNoName,
                    // no:values.logisticsProviderNo,
                    // orgId:values.orgId,
                    // userName:values.logisticsProviderUserName,
                    // telephone:values.logisticsProviderPhone,
                    // type:values.type
                })
             }
        }
    }

    const openPhoneModal = (record, code, editFlag) => {
        let url = ''
        if (code === 1) {
            url = allUrl.truckLogistics.carriageDriverPeShow
        } else {
            url = allUrl.truckLogistics.carriageLogisticsProviderPeShow
        }
        post(url,{pe:record.pe}).then(res => {
            if (res.success) {
                // settransferClockList(res.resp[0])
                if (editFlag) {
                    if (code === 1) {
                        record.driverPhone = res.resp[0]
                    } else if (code === 2) {
                        record.telephone = res.resp[0]
                    }
                    setRecordObj(record)
                    return
                }
                setPePhone(res.resp[0])
                console.log(res.resp[0])
                setPhoneVisible(true)
            } else {
                message.error(res.msg || '获取手机号码失败')
            }
        })
    }

    const RowDel=(record)=>{
        // 删除
        if(tabsKey=='1'){
            post(allUrl.truckLogistics.carriageDriverDelete,{id:record.id}).then(res => {
            if (res.success) {
                message.success(res.msg || '删除用户信息成功！')
                getData()
            } else {
                message.error(res.msg || '删除用户信息失败！')
            }

        })
        }else if(tabsKey=='2'){
           post(allUrl.truckLogistics.carriageTrailerDelete,{id:record.id}).then(res => {
            if (res.success) {
                message.success(res.msg || '删除板车信息成功！')
                getData()
            } else {
                message.error(res.msg || '删除板车信息失败！')
            }

        })
        }

    }
    const RowUnlock=(record)=>{
      post(allUrl.truckLogistics.unlock,{id:record.id}).then(res => {
        if (res.success) {
            message.success(res.msg || '解锁用户登录成功！')
            getData()
        } else {
            message.error(res.msg || '解锁用户登录失败！')
        }
      })
    }
    const handleOk = () => {
        if(tabsKey === '1'){
            // 用户信息
            driverform.validateFields().then(values=>{
                console.log('用户信息表单数据：', values);
                let params ={
                    id:recordObj.id,
                    driverName:values.driverName,
                    driverPhone:values.driverPhone,
                    logisticsProviderNoList:values.logisticsProviderNo,
                    type: values.type,
                }
                if (title=='添加信息'){
                    post(allUrl.truckLogistics.carriageDriverAdd,{...params}).then(res => {
                        if (res.success) {
                            message.success(res.msg || '修改用户信息成功')
                            setOperationVisible1(false)
                            getData()
                        } else {
                            message.error(res.msg || '修改用户信息失败')
                        }
                    })
                }else if(title=='编辑信息'){
                    post(allUrl.truckLogistics.carriageDriverUpdate,{...params}).then(res => {
                        if (res.success) {
                            message.success(res.msg || '修改用户信息成功')
                            setOperationVisible1(false)
                            getData()
                        } else {
                            message.error(res.msg || '修改用户信息失败')
                        }
                    })
                }

            })
        }else if(tabsKey === '2'){
            // 板车信息
            carform.validateFields().then(values=>{
                let params ={
                    id:recordObj.id,
                    licensePlate:values.licensePlate,
                    logisticsProviderNoList:values.logisticsProviderNo,
                }
                if (title=='添加信息'){
                    post(allUrl.truckLogistics.carriageTrailerAdd,{...params}).then(res => {
                        if (res.success) {
                            message.success(res.msg || '修改板车信息成功')
                            setOperationVisible2(false)
                            getData()
                        } else {
                            message.error(res.msg || '修改板车信息失败')
                        }
                    })
                }else if(title=='编辑信息'){
                    post(allUrl.truckLogistics.carriageTrailerUpdate,{...params}).then(res => {
                        if (res.success) {
                            message.success(res.msg || '修改板车信息成功')
                            setOperationVisible2(false)
                            getData()
                        } else {
                            message.error(res.msg || '修改板车信息失败')
                        }
                    })
                }
            })
        }
        else if(tabsKey === '3'){
            // 服务物流商信息
            logisticsProviderform.validateFields().then(values=>{
                let params ={
                    id:recordObj.id,
                    name:values.logisticsProviderNoName,
                    no:values.logisticsProviderNo,
                    orgId:values.orgId,
                    userName:values.logisticsProviderUserName,
                    telephone:values.logisticsProviderPhone,
                    type:values.type
                }
                if (title=='添加信息'){
                    post(allUrl.truckLogistics.logisticsProviderAdd,{...params}).then(res => {
                        if (res.success) {
                            message.success(res.msg || '新增服务物流商信息成功')
                            setOperationVisible3(false)
                            getData()
                        } else {
                            message.error(res.msg || '新增服务物流商信息失败')
                        }
                    })
                }else if(title=='编辑信息'){
                    if(recordObj.orgId){
                        params.orgId=recordObj.orgId
                    }
                    post(allUrl.truckLogistics.logisticsProviderUpdate,{...params}).then(res => {
                        if (res.success) {
                            message.success(res.msg || '修改服务物流商信息成功')
                            setOperationVisible3(false)
                            getData()
                        } else {
                            message.error(res.msg || '修改服务物流商信息失败')
                        }
                    })
                }
            })
        }

    }
    const unbindOrg = (record) =>{
        // 解绑组织
        let params ={
            id:record.id,
            name:record.name,
            no:record.no,
            orgId:null,
            userName:record.userName,
            telephone:record.telephone,

        }
        post(allUrl.truckLogistics.logisticsProviderUpdate,{...params}).then(res => {
            if (res.success) {
                message.success('解绑成功')
                getData()
            } else {
                message.error(res.msg || '解绑失败')
            }
        })
    }
    const processHandleOk = () =>{
        // 设置围栏
            if(startScope !== processStartScope){
                post(allUrl.truckLogistics.processSave,{processStatus:'START',scope:startScope}).then(res => {
                    if (res.success) {
                        message.success(res.msg || '设置围栏成功')
                        setprocessStartScope(startScope)
                        setOperationVisible4(false)
                    } else {
                        message.error(res.msg || '设置围栏失败')
                    }
                })
            }
            if(endScope !== processEndScope){
                post(allUrl.truckLogistics.processSave,{processStatus:'END',scope:endScope}).then(res => {
                    if (res.success) {
                        message.success(res.msg || '设置围栏成功')
                        setprocessEndScope(endScope)
                        setOperationVisible4(false)
                    } else {
                        message.error(res.msg || '设置围栏失败')
                    }
                })
            }
            setOperationVisible4(false)
    }
    const orgHandleOk =()=>{
        //绑定组织
        orgform.validateFields().then(values=>{
            let params ={
                id:recordObj.id,
                name:recordObj.name,
                no:recordObj.no,
                orgId:values.orgId,
                userName:recordObj.userName,
                telephone:recordObj.telephone,

            }
            post(allUrl.truckLogistics.logisticsProviderUpdate,{...params}).then(res => {
                if (res.success) {
                    message.success('绑定成功')
                    setOperationVisible5(false)
                    getData()
                } else {
                    message.error(res.msg || '绑定失败')
                }
            })
        })

    }
    const getScope = () =>{
        // 查询电子围栏
        post(allUrl.truckLogistics.getScope,{processStatus:'START'}).then(res => {
            if (res.success) {
                setprocessStartScope(res.resp[0].scope)
                setStartScope(res.resp[0].scope)
            } else {
                message.error(res.msg || '获取围栏信息失败')
            }
        })
        post(allUrl.truckLogistics.getScope,{processStatus:'END'}).then(res => {
            if (res.success) {
                setprocessEndScope(res.resp[0].scope)
                setEndScope(res.resp[0].scope)
            } else {
                message.error(res.msg || '获取围栏信息失败')
            }
        })
    }
    const setprocess =()=>{
        setOperationVisible4(true)
        // getScope()
    }

    const getData = () => {
        setLoading(true)
        let query = { ...defaultQuery }
        let params = { pageNum: current, pageSize, ...query }
        if(tabsKey === '1'){
            tableRef1.current.getTableData()
        }else if(tabsKey === '2'){
            tableRef2.current.getTableData()
        }else if(tabsKey === '3'){
            tableRef3.current.getTableData()
        }
    }
    const addHandleSelectType = (type) => {
        console.log('用户类型切换', type, dirverOrgs, guardOrgs)
        // todo 过滤组织商数据，只留对应的组织类型的数据
        if(type == 1) {
            if(dirverOrgs.length){
                // driverform.setFieldsValue({logisticsProviderNoList: dirverOrgs})
                setLogisticsProviderList(dirverOrgs)
            }else{
                getLogisticsProviderList(type)
            }
        }
        if(type == 2) {
            if(guardOrgs.length){
                // driverform.setFieldsValue({logisticsProviderNoList: guardOrgs})
                setLogisticsProviderList(guardOrgs)

            }else{
                getLogisticsProviderList(type)
            }
        }

    }
    useEffect(()=>{
        // 组织
        // dispatch(getDict({codes:'dray_logistics_provider_no'})).then(({payload})=>{
        //     setDictList(payload)
        // })
        getLogisticsProviderList()
        getOrgData()
    },[])
    useEffect(()=>{
        getScope()
    },[])
    useEffect(()=>{
        initPage()
    },[])
    useEffect(()=>{
        console.log('recordObj', recordObj)
        if(driverformRef.current){
            driverform.setFieldsValue({
                driverName:recordObj.driverName,
                driverPhone:recordObj.driverPhone,
                logisticsProviderNo:recordObj.logisticsProviderNo ? recordObj.logisticsProviderNo.split(',') : [],
                type: recordObj.type
            })
        }
        if(carformRef.current){
            carform.setFieldsValue({
                licensePlate:recordObj.licensePlate,
                logisticsProviderNo:recordObj.logisticsProviderNo ? recordObj.logisticsProviderNo.split(',') : []
            })
        }
        if(logisticsProvidermRef.current){
            logisticsProviderform.setFieldsValue({
                logisticsProviderNo:recordObj.no,
                logisticsProviderNoName:recordObj.name,
                orgId:recordObj.orgId,
                logisticsProviderUserName:recordObj.userName,
                logisticsProviderPhone:recordObj.telephone,
                type: recordObj.type
            })
        }
    },[recordObj])
    const columns1 = [
        { title: '账号ID', dataIndex: 'id', width: 100 },
        { title: '姓名', dataIndex: 'driverName', width: 100 },
        { title: '手机号', dataIndex: 'driverPhone', width: 100 },
        { title: '用户类型', dataIndex: 'type', width: 100, render: (text, record) => renderStatusName(USER_TYPE, text) },
        { title: '组织', dataIndex: 'logisticsProviderName', width: 200 },
        { title: '操作', width: 160, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation1(text, record) }
    ]
    const columns2 = [
        { title: '序号', dataIndex: 'index', fixed: 'left', width: 50, render: (text, record, index) => <div>{tableRef2.current.current * 10 + index - 9}</div> },
        { title: '板车车牌号', dataIndex: 'licensePlate', width: 100 },
        { title: '组织', dataIndex: 'logisticsProviderName', width: 100 },
        { title: '操作', width: 80, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation2(text, record) }
    ]
    const columns3 = [
        { title: '组织编码',dataIndex: 'no', width: 100},
        { title: '组织名称', dataIndex: 'name', width: 100 },
        { title: '组织类型', dataIndex: 'type', width: 100, render: (text, record) => renderStatusName(ORG_TYPE, text) },
        { title: '所属协同组织', dataIndex: 'orgName', width: 200 },
        { title: '管理员',dataIndex: 'userName', width: 100},
        { title: '管理员联系电话', dataIndex: 'telephone', width: 100 },
        { title: '操作', width: 200, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation3(text, record) }
    ]

    let searchList1 = [

        { label: '用户类型', name: 'type', type: 'Select', placeholder: '请输入', colSpan: 6, data:USER_TYPE},
        { label: '姓名', name: 'driverName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '手机号', name: 'driverPhone', type: 'Input', placeholder: '请输入', colSpan: 6 },
        {
            label: '组织', name: 'logisticsProviderNo', type: 'Select', placeholder: '请选择', colSpan: 6, data:logisticsProviderList || []
        }
    ]
    let searchList2 = [
        { label: '板车车牌号', name: 'licensePlate', type: 'Input', placeholder: '请输入', colSpan: 6 },
        {
            label: '组织', name: 'logisticsProviderNo', type: 'Select', placeholder: '请选择', colSpan: 6, data:logisticsProviderList || []
        }
    ]
    let searchList3 = [
        { label: '组织名称', name: 'name', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '组织类型', name: 'type', type: 'Select', placeholder: '请输入', colSpan: 6, data: ORG_TYPE },
    ]
    const orgFieldNames ={ label: 'title', value: 'key', children: 'children'}
    return (
        <div className='DriverAndCarManage'>
            <Tabs onChange={tabsCallback} type="card" activeKey={tabsKey} defaultActiveKey={tabsKey} destroyInactiveTabPane>
                { roleJudgment(userInfo,'TRUCK_DRIVER') ?
                <TabPane tab="用户" key="1">
                    <PublicTableQuery initPage={initPage} tabsKey={tabsKey} onSearch={onSearch} searchList={searchList1} defaultQuery={defaultQuery} />
                    <div className='tableData'>
                        <Row className='tableTitle'>
                            <Col span={12} className="text">板车物流用户列表</Col>
                            <Col span={12} className="bts"style={{textAlign:'right'}}>
                                {
                                    roleJudgment(userInfo,'TRUCK_DRIVER_ADD_INFORMATION') ?
                                        <Button type='primary' onClick={() => openModal(2)}>添加信息</Button>
                                        : null
                                }
                                {
                                roleJudgment(userInfo, 'TRUCK_DRIVER_IMPORT') ?
                                <>
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={10}
                                        action={baseURL.Host + allUrl.truckLogistics.carriageDriverImportExcel}
                                        UploadChange={UploadChange}
                                    >
                                        <Button loading={importLoading}>导入</Button>
                                    </UploadFile>
                                    <a onClick={download}>模版下载</a>
                                </>:null
                            }

                            </Col>
                        </Row>
                        {
                            tabsKey === '1' &&
                            <PublicTable
                                url={allUrl.truckLogistics.carriageDriverPage}
                                columns={columns1}
                                type={2}
                                rowSelection={false}
                                ref={tableRef1}
                                setCurrent={setCurrent}
                                setPageSize={setPageSize}
                                defaultQuery={{...defaultQuery}}
                            />
                        }
                    </div>
                </TabPane>
                : null }
                { roleJudgment(userInfo,'TRUCK_CAR') ?
                <TabPane tab="板车" key="2">
                    <PublicTableQuery initPage={initPage} tabsKey={tabsKey} onSearch={onSearch} searchList={searchList2} defaultQuery={defaultQuery} />
                    <div className='tableData'>
                        <Row className='tableTitle'>
                        <Col span={12} className="text">板车列表</Col>
                        <Col span={12} className="bts"style={{textAlign:'right'}}>
                                {
                                    roleJudgment(userInfo,'TRUCK_CAR_ADD_INFORMATION') ?
                                        <Button type='primary'onClick={() => openModal(2)}>添加信息</Button>
                                        : null
                                }
                                {
                                roleJudgment(userInfo, 'TRUCK_CAR_IMPORT') ?
                                <>
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={10}
                                        action={baseURL.Host + allUrl.truckLogistics.carriageTrailerImportExcel}
                                        UploadChange={UploadChange}
                                    >
                                        <Button loading={importLoading}>导入</Button>
                                    </UploadFile>
                                    <a onClick={download}>模版下载</a>
                                </>:null
                            }

                            </Col>
                        </Row>
                        {
                            tabsKey === '2' &&
                            <PublicTable
                                url={allUrl.truckLogistics.CarriageTrailerPage}
                                columns={columns2}
                                type={2}
                                rowSelection={false}
                                ref={tableRef2}
                                setCurrent={setCurrent}
                                setPageSize={setPageSize}
                                defaultQuery={{...defaultQuery}}
                            />
                        }
                    </div>
                </TabPane>
                : null }
                { roleJudgment(userInfo,'TRUCK_SERVICE') ?
                <TabPane tab="组织" key="3">
                    <PublicTableQuery initPage={initPage} tabsKey={tabsKey} onSearch={onSearch} searchList={searchList3} defaultQuery={defaultQuery} />
                    <div className='tableData'>
                        <Row className='tableTitle'>
                            <Col span={12}className="text">组织列表</Col>
                            <Col span={12} className="bts"style={{textAlign:'right'}}>
                                {
                                    roleJudgment(userInfo,'TRUCK_SERVICE_ADD_INFORMATION') ?
                                        <Button type='primary' onClick={() => openModal(2)}>添加信息</Button>
                                        : null
                                }
                                {
                                    roleJudgment(userInfo,'TRUCK_SERVICE_FENCE_SET') ?
                                        <Button type='primary' onClick={()=>setprocess()}>围栏设置</Button>
                                        : null
                                }
                            </Col>
                        </Row>
                        {
                            tabsKey === '3' &&
                            <PublicTable
                                url={allUrl.truckLogistics.logisticsProviderPage}
                                columns={columns3}
                                type={2}
                                rowSelection={false}
                                ref={tableRef3}
                                setCurrent={setCurrent}
                                setPageSize={setPageSize}
                                defaultQuery={{...defaultQuery}}
                            />
                        }
                    </div>
                </TabPane>
                 : null }
            </Tabs>
            {
            operationVisible1 &&
            // 用户增、改
            <Modal  width={800}  visible={operationVisible1} title={title} recordObj={recordObj} onOk={handleOk}  onCancel={()=>setOperationVisible1(false)}>
                <Form layout={'vertical'} form={driverform} ref={driverformRef}>
                    <Row className='FormRowCon'>
                        <Col span={6}>
                        <Form.Item wrapperCol={{ span: 20 }}label="用户姓名" name="driverName" rules={[{ required: true, message: '请输入姓名', }]}>
                            <Input placeholder="请输入"/>
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item wrapperCol={{ span: 20 }}label="手机号" name="driverPhone" validateTrigger='onSubmit' rules={[{ required: true, message: '请输入手机号'},
                                        {
                                            validator(_, value) {
                                                if (value && !/^1[3-9]\d{9}$/.test(value)) {
                                                    return Promise.reject(new Error('请输入格式正确的手机号码'));
                                                } else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}>
                                <Input placeholder="请输入" disabled={identification}/>
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                        <Form.Item wrapperCol={{ span: 20 }}label="用户类型" name="type" rules={[{ required: true, message: '请选择用户类型', }]}>
                        <Select
                            placeholder='请选择'
                            allowClear
                            disabled={identification}
                            defaultValue={identification ? recordObj.type : 1}
                            onChange={addHandleSelectType}
                            >
                               <Option key={1} value={1}>司机</Option>)
                               <Option key={2} value={2}>门卫</Option>)
                        </Select>
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                        <Form.Item wrapperCol={{ span: 20 }}label="组织" name="logisticsProviderNo" rules={[{ required: true, message: '请选择组织', }]}>
                        <Select
                            placeholder='请选择'
                            mode="multiple"
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            filterOption={false}>
                            {
                               logisticsProviderList.map((item, index) =>  <Option key={index} value={item.no}>{item.name}</Option>)
                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
            operationVisible2 &&
            // 板车增、改
            <Modal  width={600}  visible={operationVisible2} title={title} recordObj={recordObj} onOk={handleOk}  onCancel={()=>setOperationVisible2(false)}>
             <Form layout={'vertical'} form={carform} ref={carformRef}>
                    <Row className='FormRowCon'>
                        <Col span={12}>
                        <Form.Item wrapperCol={{ span: 20 }}label="板车车牌号" name="licensePlate" rules={[{ required: true, message: '请输入板车车牌号', }]}>
                            <Input disabled={identification} placeholder="请输入"/>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                        <Form.Item wrapperCol={{ span: 20 }}label="组织" name="logisticsProviderNo" rules={[{ required: true, message: '请选择组织', }]}>
                        <Select
                            placeholder='请选择'
                            mode="multiple"
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            filterOption={false}>
                            {
                               logisticsProviderList.map((item, index) =>  <Option key={index} value={item.no}>{item.name}</Option>)

                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
            operationVisible3 &&
            // 组织增、改
            <Modal  width={700}  open={operationVisible3} title={title} recordObj={recordObj} onOk={handleOk}  onCancel={()=>setOperationVisible3(false)}>
                <Form layout={'vertical'} form={logisticsProviderform} ref={logisticsProvidermRef}>
                    <Row className='FormRowCon'>
                        <Col span={12}>
                        <Form.Item wrapperCol={{ span: 20 }}label="组织编码" name="logisticsProviderNo" rules={[{ required: true, message: '请输入组织编码', }]}>
                            <Input placeholder="请输入"/>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                        <Form.Item wrapperCol={{ span: 20 }}label="组织名称" name="logisticsProviderNoName" rules={[{ required: true, message: '请输入组织名称', }]}>
                            <Input placeholder="请输入"/>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                        <Form.Item wrapperCol={{ span: 20 }}label="组织类型" name="type" rules={[{ required: true, message: '组织类型必选', }]}>
                            <Select
                                placeholder='请选择'
                                allowClear
                                optionFilterProp="children"
                                defaultActiveFirstOption={false}
                                showArrow={false}
                                defaultValue={1}
                                onChange={addHandleSelectType}
                                filterOption={false}>
                                     {
                               ORG_TYPE.map((item, index) =>  <Option key={index} value={item.value}>{item.name}</Option>)

                            }
                            </Select>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                        <Form.Item wrapperCol={{ span: 20 }}label="管理员" name="logisticsProviderUserName" rules={[{ required: true, message: '请输入管理员', }]}>
                            <Input placeholder="请输入"/>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                        <Form.Item wrapperCol={{ span: 20 }}label="管理员手机号" name="logisticsProviderPhone" rules={[{ required: true, message: '请输入手机号'},
                                        {
                                            validator(_, value) {
                                                if (value && !/^1[3-9]\d{9}$/.test(value)) {
                                                    return Promise.reject(new Error('请输入格式正确的手机号码'));
                                                } else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}>
                            <Input placeholder="请输入"/>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
            operationVisible4 &&
            // 围栏设置
            <Modal  width={500}  open={operationVisible4} title='围栏设置' recordObj={recordObj} onOk={processHandleOk}  onCancel={()=>setOperationVisible4(false)}>
             <Form layout={'horizontal'} form={processform} ref={processformRef}>
                    <Row className='FormRowCon'>
                        <Col span={24} style={{margin:'20px 0px',display:'flex',alignItems: 'center'}}>
                        <span style={{width:'10px',height:'10px',backgroundColor:'#1890ff',borderRadius:'5px',marginRight:'5px'}}></span>
                        起运有效范围：距离出发地半径
                        <Select
                            defaultValue={processStartScope}
                            value={startScope}
                            onChange={value =>ScopeChange(value,1)}
                            style={{margin:'0 10px',width:'100px'}}>
                              <Option key={1} value={null}>不限制</Option>
                              <Option key={2} value={3000}>3km</Option>
                              <Option key={3} value={5000}>5km</Option>
                              <Option key={4} value={7000}>7km</Option>
                              <Option key={5} value={10000}>10km</Option>
                              <Option key={6} value={15000}>15km</Option>
                        </Select>
                            内。
                        </Col>
                        <Col span={24} style={{margin:'20px 0px',display:'flex',alignItems: 'center'}}>
                            <span style={{width:'10px',height:'10px',backgroundColor:'#1890ff',borderRadius:'5px',marginRight:'5px'}}></span>
                        送达有效范围：距离目的地半径
                        <Select
                            placeholder='请选择'
                            defaultValue={processEndScope}
                            value={endScope}
                            onChange={value =>ScopeChange(value,2)}
                            style={{margin:'0 10px',width:'100px'}}>
                              <Option key={1} value={null}>不限制</Option>
                              <Option key={2} value={3000}>3km</Option>
                              <Option key={3} value={5000}>5km</Option>
                              <Option key={4} value={7000}>7km</Option>
                              <Option key={5} value={10000}>10km</Option>
                              <Option key={6} value={15000}>15km</Option>
                        </Select>
                        内。
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
            operationVisible5 &&
            // 绑定组织
            <Modal  width={600}  open={operationVisible5} title='绑定组织' recordObj={recordObj} onOk={orgHandleOk}  onCancel={()=>setOperationVisible5(false)}>
             <Form layout={'vertical'} form={orgform} ref={orgformRef}>
                    <Row className='FormRowCon'>
                        <Col span={24}>
                        <Form.Item wrapperCol={{ span: 20 }}label="所属组织" name="orgId" rules={[{ required: true, message: '请选择所属组织', }]}>
                        <TreeSelect
                                showSearch
                                style={{
                                    width: '100%',
                                }}
                                value={orgId}
                                dropdownStyle={{
                                    maxHeight: 400,
                                    overflow: 'auto',
                                }}
                                placeholder="请选择"
                                allowClear
                                dropdownMatchSelectWidth={false}
                                filterTreeNode={() => true}
                                onChange={orgOnChange}
                                onSearch={orgOnSearch}
                                treeData={orgDataList}
                                fieldNames={orgFieldNames}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
            phoneVisible &&
            <Modal  width={600}  open={phoneVisible} title='查看手机号' recordObj={recordObj} onOk={() => setPhoneVisible(false)}  onCancel={()=>setPhoneVisible(false)}>
                {pePhone}
            </Modal>
            }
        </div>
    )
}
export default DriverAndCarManage
