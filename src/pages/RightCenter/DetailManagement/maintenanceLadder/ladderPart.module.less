.ladder-part-wrap {
    width: 800px;
    margin-left: 12.5%;
}

.ladder-part {
    display: flex;
    border: 1px solid #9BCEFF;
    padding: 26px 0 15px 30px;
    margin-bottom: 20px;
    align-items: center;
    background: #F7FBFF;
}

.num {
    width: 44px;
    height: 34px;
    background: #FFFFFF;
    border: 1px solid #56ACFF;
    text-align: center;
    line-height: 34px;
    font-size: 21px;
    font-weight: 600;
    color: #56ACFF;
    margin-right: 30px;
}

.content {
    flex: 1;
}

.item-title {
    position: relative;
    display: inline-flex;
    align-items: center;
    max-width: 100%;
    height: 32px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;

    &::after {
        content: ':';
        position: relative;
        top: -0.5px;
        margin: 0 8px 0 2px;
    }
}

.m-l-8 {
    margin-left: 8px;
}

.tagStyle {
    padding: 0 5px;
    height: 24px;
    line-height: 24px;
    background: #1A69E9;
    border-radius: 3px;
    display: inline-block;
    color: #FFFFFF;
}