import React, { useState, memo, useMemo, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Modal, Form, Row, Col, DatePicker, message, Button, Input } from 'antd'
import { post, get, put } from '../../../utils/request';
import moment from 'moment'
import allUrl from '../../../utils/url';
const ChangeDeliveryTime = (props) => {
    const { handleCancel, visible, reloadChangeInfo } = props

    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const [btnDisabled, setBtnDisabled] = useState()
    const [deliveryTimeStatus, setDeliveryTimeStatus] = useState('')
    const [reasonStatus, setReasonStatus] = useState('')
    const dataSourceForm = useMemo(() => props.dataSource, [props.dataSource])
    props.dataSource.deliveryTime = moment(dataSourceForm.deliverOrderList[0].deliverDate)
    const formRef = useRef(null)
    const onChangeTime = () => {
        const { deliveryTime } = form.getFieldsValue()
        if (!deliveryTime) {
            setDeliveryTimeStatus('error')
        } else {
            setDeliveryTimeStatus('')
        }
    }
    const onChangeReason = () => {
        const { reason } = form.getFieldsValue()
        if (!reason) {
            setReasonStatus('error')
        } else {
            setReasonStatus('')
        }
    }
    const handleOk = () => {
        form.validateFields().then(values => {
            const accountNo = dataSourceForm.accountNo
            // const time = form.getFieldValue('deliveryTime')
            const time = values.deliveryTime
            const reason = values.reason
            // 检查一下
            onChangeTime()
            onChangeReason()
            if (time && reason) {
                setLoading(true)
                const params = {
                    deliveryTime: moment(time).format('YYYY-MM-DD HH:mm:ss'),
                    updateOrdersReason: reason
                }
                // 更改账户信息接口     730
                put(allUrl.AccountManagement.updateDeliverData({ accountNo }), { ...params }).then(res => {
                    if (res.success) {
                        message.success('更新成功！')
                        reloadChangeInfo();
                    } else {
                        // message.error('更新失败！')
                    }
                    setLoading(false)
                    handleCancel()
                })
            }

        })
    }

    // useEffect(() => {
    //     console.log(btnDisabled);
    // }, [btnDisabled])
    useEffect(() => {
        console.log(form.getFieldsValue());
        if (dataSourceForm) {

            formRef.current && form.setFieldsValue({
                ...dataSourceForm,

            })
        }
    }, [dataSourceForm, form])
    return (
        <Modal open={visible} okText={'保存'} onCancel={handleCancel} maskClosable={false} width='650px' title='订单信息更改'
            footer={[
                <Button key="back" onClick={handleCancel}>
                    取消
                </Button>,
                <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
                    保存
                </Button>,
            ]}>
            <Form name='change-accountInfo' ref={formRef} form={form}  >
                <Row>
                    <Col span={24}>
                        {/* onChange={(date) => setBtnDisabled(date)} */}
                        <Form.Item label='交付时间' name='deliveryTime' validateStatus={deliveryTimeStatus} hasFeedback required={true}>
                            <DatePicker style={{ width: '100%' }} showTime onChange={onChangeTime} />
                        </Form.Item>
                        <p style={{ color: 'red', fontSize: '12px', position: 'absolute', top: '38px', left: '82px' }}>若变更交付时间，则以交付时间作为权益开始时间的车辆权益将会同步更新，阶梯内生效策略暂时无法同步更新～</p>
                    </Col>
                    <Col span={24} style={{ marginTop: '24px' }}>
                        <Form.Item label='更改原因' name='reason' validateStatus={reasonStatus} hasFeedback required={true}>
                            <Input allowClear placeholder='请输入更改原因' maxLength={50} onChange={onChangeReason} />
                        </Form.Item>
                        <p style={{ color: 'red', fontSize: '12px', position: 'absolute', top: '38px', left: '82px' }}>最多支持50个字，不支持其它表情和特殊符号，如配置则无法正常展示</p>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(ChangeDeliveryTime)