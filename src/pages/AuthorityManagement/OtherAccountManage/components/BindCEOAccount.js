import React, { useState } from 'react'
import { Modal, AutoComplete, Select, message ,Alert} from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons';
import { post } from '@/utils/request'
import allUrl from '@/utils/url'
const { Option } = Select
const mockVal = (str, repeat = 1) => ({
    value: str.repeat(repeat),
});

const BindCEOAccount = (props) => {
    const { visible, onCancel, CEOData, rowData, getData } = props
    const [value, setValue] = useState('');
    const [confirmVisible,setConfirmVisible] = useState(false)
    const [data, setData] = useState(CEOData)
    const [result, setResult] = useState([]);


    const handleSearch = (value) => {
        let res = [];
        let arr = [...data]
        if (value) {
            arr.forEach(item => {
                if (item.ceoUserCode && item.ceoUserCode.indexOf(value) > -1) {
                    res.push(item)
                }
            })
            setResult(res);
        } else {
            res = []
            setResult(res);
        }
        setValue(value)
    };
    const handleSelect = (value) => {
        setValue(value)
    }


    const handleOk = () => {
        console.log(rowData)
        if (!value) {
            message.error('请输入CEO账号ID！')
            return
        }
        setConfirmVisible(true)
        
    }
    const reconfirm = () =>{
        post(allUrl.Authority.bindUser, { mobilePhone: rowData.mobilePhone, ceoUserCode: value }).then(res => {
            if (res.success) {
                message.success(res.msg)
                getData()
                onCancel()
            } else {
                // message.error(res.msg || res.msgCode)
            }
        })
    }

    return <>
        <Modal visible={visible} title={'提示'} onOk={handleOk} onCancel={onCancel} maskClosable={false}>
            <div style={{ display: 'flex' }}>
                <div style={{ height: '34px', lineHeight: '34px' }}>
                    <ExclamationCircleFilled style={{ marginRight: '7px', color: '#faad14', fontSize: '16px' }} />
                    请输入CEO账号ID：
                </div>
                <AutoComplete
                    style={{
                        width: 200,
                    }}
                    onSearch={handleSearch}
                    onSelect={handleSelect}
                    placeholder="请输入..."
                    allowClear
                    value={value}
                    autoFocus
                >
                    {result.map((item) => (
                        <Option key={item.id} value={item.ceoUserCode}>
                            {item.ceoUserCode}
                        </Option>
                    ))}
                </AutoComplete>
            </div>
        </Modal>
        {
            confirmVisible &&
            <Modal visible={confirmVisible} title={'绑定确认'} okText='确认绑定' onOk={()=>reconfirm()} onCancel={()=>setConfirmVisible(false)} maskClosable={false}>
                <div style={{padding:'20px'}}>
                    <p style={{marginBottom:'20px',fontSize:'14px'}}><span style={{fontWeight:500}}>SERES协同系统账号：</span>{rowData.mobilePhone}</p>
                    <p style={{fontSize:'14px'}}><span style={{fontWeight:500}}>CEO账号：</span>{value}</p>
                </div>
                <Alert
                    description="请确认账号无误，绑定后SERES协同账号的数据都将同步到对应的CEO账号。"
                    type="info"
                />
            </Modal>
        }
    </>
}
export default BindCEOAccount