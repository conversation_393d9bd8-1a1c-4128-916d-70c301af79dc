import { postSeresFileOrganizationSeresSave } from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { Form, Input, Modal, ModalProps } from 'antd';
import React, { FC, useCallback, useEffect } from 'react';

export interface DivisionModalProps extends ModalProps {
  onSave?: () => void;
  data?: FileManage.SeresOrganization | null;
}

const DivisionModal: FC<DivisionModalProps> = ({ onSave, data, ...resProps }) => {
  const [form] = Form.useForm();
  const handleOk = useCallback(async () => {
    try {
      await form.validateFields();
      await postSeresFileOrganizationSeresSave({ ...data, ...form.getFieldsValue() });
      onSave?.();
    } catch (error) {}
  }, [data, form, onSave]);

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data, form]);
  return (
    <Modal title="添加总部部门" afterClose={form.resetFields} onOk={handleOk} {...modalFormLayout} {...resProps}>
      <Form form={form}>
        <Form.Item label="部门名称" name="name" rules={[{ required: true, max: 40 }]}>
          <Input placeholder="请输入部门名称" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DivisionModal;
