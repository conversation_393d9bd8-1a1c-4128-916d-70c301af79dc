import React from 'react'
import { Menu, Layout } from 'antd'
import { connect } from 'react-redux'
import { <PERSON>, withRout<PERSON> } from 'react-router-dom'
import { setSiderNormal } from '../../../actions/normal'
import { PublicTabAdd, GetMenuList } from '../../../actions/async'
import Icon,{SlidersOutlined} from '@ant-design/icons';
import AccountAuthority from '../../../assets/img/AccountAuthority.svg'
import OvertimeRebate from '../../../assets/img/OvertimeRebate.svg'
import ClueManage from '@/assets/img/ClueManage.svg'
import StoreManage from '@/assets/img/store.svg'
import TruckLogistics from '@/assets/img/TruckLogistics.svg'
import OrderManage from '@/assets/img/OrderManage.svg'
import WeCom from '@/assets/img/wecom.svg'
import RightCenter from '@/assets/img/RightCenter.svg'
import Lightning from '@/assets/img/shandian.svg'
import Mendian from '@/assets/img/mendian.svg'
import Chezhu from '@/assets/img/chezhu.svg'
import Shuiyin from '@/assets/img/shuiyin.svg'
import Wenjian from '@/assets/img/wenjian.svg'
import Hezuo from '@/assets/img/hezuo.svg'
const { Sider } = Layout;
const { SubMenu } = Menu

class SiderCpt extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {
            collapsed: false,
            defaultOpenKeys: sessionStorage.getItem('openKeys') ? JSON.parse(sessionStorage.getItem('openKeys')) : [],
            defaultSelectedKeys: [],
            selectedKeys: [],
        }
    }
    getItemSider = (arr1, path) => {
        let temp = []
        let forFn1 = function (arr, path) {
            for (let i = 0; i < arr.length; i++) {
                let item = arr[i]
                if (item.path === path) {
                    temp.push(item)
                    forFn1(arr1, item.parentId)
                    break
                } else {
                    if (item.children) {
                        forFn1(item.children, path)
                    }
                }
            }
        }
        if (arr1 && arr1.length) {
            forFn1(arr1, path)
        }
        return temp
    }

    componentDidMount() {
        this.props.dispatch(GetMenuList()).then(({payload})=> {
            if(payload){
                let siderArr = payload ? payload:[]
                let url = window.location.href.split('#')[1]
                let itemArr = this.getItemSider(siderArr, url)
                this.setState({
                    defaultOpenKeys: sessionStorage.getItem('openKeys') ? JSON.parse(sessionStorage.getItem('openKeys')) : []
                }, () => {
                    sessionStorage.setItem('ActiveKey', itemArr.length ? JSON.stringify([`${itemArr[0].key}`]) : [])
                })
            }
        })
    }
    onCollapse = collapsed => {
        this.props.dispatch(setSiderNormal(collapsed))
    };
    renderMenu = (data) => {
        const { showRedDot } = this.props
        return data && data.length ? data.map((item, index) => {
            if (item.children && item.children.length) {
                return (
                    <SubMenu
                        key={item.key}
                        title={
                            <span>
                                {item.CustomIcon ? <Icon component={item.CustomIcon} />:<SlidersOutlined />}
                                <span>{item.title}</span>
                            </span>
                        }
                    >
                        {this.renderMenu(item.children)}
                    </SubMenu>
                )
            } else {
                return (
                    <Menu.Item key={item.key} title={item.title}>
                        <Link to={{ pathname: item.path }}>
                            {/* {item.CustomIcon ? <Icon component={item.CustomIcon} />:<SlidersOutlined />} */}
                            <span>{item.title}</span>
                            {(item.title === '下载中心' && showRedDot) && <div style={{ verticalAlign: 'top',display: 'inline-block', width: '5px', height: '5px', borderRadius: '5px', backgroundColor: 'red', marginTop: '10px'}}></div>}
                        </Link>
                    </Menu.Item>
                )
            }
        }) : null
    }

    onSelect = ({ item, key, keyPath, selectedKeys, }) => {
        const {MenuList} = this.props
        let temp = this.familyTree(MenuList, Number(key))
        temp.splice(temp.length - 1, 1)
        temp.reverse()
        this.props.dispatch(PublicTabAdd(temp))
        this.setState({ ActiveKey: [key] }, () => {
            sessionStorage.setItem('ActiveKey', JSON.stringify([key]))
        })
        window.location.reload()  // 优化 点击菜单重新刷新页面
        sessionStorage.setItem('ActiveKey', JSON.stringify([key]))
    }
    // 当前选中菜单与路由匹配
    setActiveKey = () => {
        let { MenuList, history } = this.props
        let url = history.location.pathname
        let itemArr = this.getItemSider(MenuList, url)
        
        if(itemArr.length === 0){
            // 详情页面路由匹配
            if(url.indexOf('Detail/') !== -1){
                let newURL = url.split('Detail/')[0]
                itemArr = this.getItemSider(MenuList, newURL)
            }
            // 新建页面路由匹配
            if(url.indexOf('Create/') !== -1){
                let newURL = url.split('Create/')[0]
                itemArr = this.getItemSider(MenuList, newURL)
            }
            // 关联页面路由匹配
            if(url.indexOf('Batch') !== -1){
                let newURL = url.split('Batch')[0]
                itemArr = this.getItemSider(MenuList, newURL)
            }
        }
        if(itemArr.length > 0){
            let temp = this.familyTree(MenuList, Number(itemArr[0].key))
            temp.splice(temp.length - 1, 1)
            temp.reverse()
            this.props.dispatch(PublicTabAdd(temp))
        } else {
            this.props.dispatch(PublicTabAdd([]))
        }

        return itemArr.length ? [`${itemArr[0].key}`] : sessionStorage.getItem('ActiveKey') ? sessionStorage.getItem('ActiveKey') : []
    }
    onOpenChange = (openKeys) => {
        if (openKeys.length) {
            sessionStorage.setItem('openKeys', JSON.stringify(openKeys))
        }
    }

    familyTree = (arr1, key) => {
        let temp = []
        let forFn = function (arr, key) {
            for (let i = 0; i < arr.length; i++) {
                let item = arr[i]
                if (item.key === key) {
                    temp.push(item)
                    forFn(arr1, item.parentId)
                    break
                } else {
                    if (item.children) {
                        forFn(item.children, key)
                    }
                }
            }
        }
        forFn(arr1, key)
        return temp
    }

    render() {
        const { SiderType ,MenuList} = this.props
        const { defaultOpenKeys, defaultSelectedKeys } = this.state
        
        return (
            <>
                <Sider
                    style={{
                        position: 'fixed',
                        left: 0,
                        bottom: 0,
                        top: '48px',
                        width: '208px'
                    }}
                    width={'208px'}
                    trigger={null}
                    collapsible
                    collapsed={SiderType}
                    onCollapse={this.onCollapse}
                >
                    <Menu
                        theme="dark"
                        onSelect={this.onSelect}
                        onOpenChange={this.onOpenChange}
                        selectedKeys={this.setActiveKey()}
                        defaultOpenKeys={defaultOpenKeys}
                        defaultSelectedKeys={defaultSelectedKeys}
                        mode="inline"
                    >
                        {this.renderMenu(MenuList && MenuList.length && MenuList[0].children ? MenuList[0].children:[])}
                    </Menu>
                </Sider>
            </>
        )
    }
}
export default withRouter(connect(({ common }) => {
    const { SiderType, MenuList, showRedDot } = common
    
    if(MenuList && MenuList.length){
        MenuList[0].children.forEach(item=>{
            if(item.title === '账号权限管理'){
                item.CustomIcon = AccountAuthority
            }else if(item.title === '补贴'){
                item.CustomIcon = OvertimeRebate
            }else if(item.title === '线索管理'){
                item.CustomIcon = ClueManage
            }else if(item.title === '门店管理'){
                item.CustomIcon = Mendian
            }else if(item.title === '板车物流'){
                item.CustomIcon = TruckLogistics
            }else if(item.title === '订单管理'){
                item.CustomIcon = OrderManage
            }else if(item.title === '企业微信'){
                item.CustomIcon = WeCom
            }else if(item.title === '权益中心'){
                item.CustomIcon = RightCenter
            }else if(item.title === '闪电交付'){
                item.CustomIcon = Lightning
            }else if(item.title === '合作伙伴'){
                item.CustomIcon = Hezuo
            }else if(item.title === '车主活动'){
                item.CustomIcon = Chezhu
            }else if(item.title === '水印管理'){
                item.CustomIcon = Shuiyin
            }else if(item.title === '文件管理'){
                item.CustomIcon = Wenjian
            }
        })
    }
    return {
        SiderType, MenuList, showRedDot
    }
})(SiderCpt))