import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
// import { AppContainer } from 'react-hot-loader';
// import store from './store'
import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import moment from 'moment';
import { PersistGate } from 'redux-persist/es/integration/react';
import RouterConfig from './route';
import configureStore from './store/configureStore';

import 'moment/locale/zh-cn';
moment.locale('zh-cn');

const { persistor, store } = configureStore();

// window.getVersion = version => {
// 	if ((localStorage.frontendVersion && version !== localStorage.frontendVersion) || (window.frontendVersion && version !== window.frontendVersion)) {
// 		// window.location.reload();//刷新页面
// 	}
// 	localStorage.frontendVersion = version;//保存以便下次使用判断
// 	window.frontendVersion = version;

// }

const render = (Component) => {
  ReactDOM.render(
    <Provider store={store}>
      {/* redux持久化 */}
      <PersistGate persistor={persistor}>
        {/* 热更新AppContainer配置 */}
        {/* 国际化ConfigProvider中文 */}
        <ConfigProvider locale={zhCN}>
          <Component />
        </ConfigProvider>
      </PersistGate>
    </Provider>,
    document.getElementById('root')
  );
};
render(RouterConfig);

if (module.hot) {
  module.hot.accept('./route', () => {
    //因为在App里使用的是export default语法，这里使用的是require,默认不会加载default的，所以需要手动加上
    const NextApp = require('./route').default;
    // 重新渲染到 document 里面
    render(NextApp);
  });
}

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
// serviceWorker.unregister();
