import './index.less'
import React, { useEffect, useState,useRef } from 'react'
import { useSelector,useDispatch } from 'react-redux'
import { Table,message, Tabs ,Row,Col,Button,Modal,Divider,Popconfirm,Form,Input,Select,Menu,Dropdown,Timeline,Tag} from 'antd'
import { DownOutlined } from '@ant-design/icons';
import moment from 'moment'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import { get,post } from '@/utils/request';
import {trim} from '@/utils'
import _ from "lodash";
import allUrl from '@/utils/url';
import {roleJudgment} from '@/utils/authority'
import {setStorage,getStorage} from '../../../utils/Storage'
import {getDict} from '../../../actions/async'
import ExtendedColumn from '@/components/Public/ExtendedColumn'
import ExtendedColumnNew from '@/components/Public/ExtendedColumnNew'
import { renderStatusName } from '@/utils'
import UploadFile from '@/components/Public/UploadFile'
import baseURL from '@/baseURL'
const { TabPane } = Tabs;
const { Option } = Select;
const LOG_TYPE = [ { name: '修改进场预约时间',key: 1, value: 1 },{ name: '修改进场时间限制',key: 2, value: 2 },{ name: '强制组板',key: 3, value: 3 },{ name: '物流管理员补偿送达',key: 4, value: 4 }]

const ClueList = (props) => {
    //创建ref节点
    const tableRef1 = useRef()
    const tableRef2 = useRef()
    const childRef1 = useRef(null);
    const childRef2 = useRef(null);
    const [driverform] = Form.useForm();
    const driverformRef = useRef(null);
    const dispatch = useDispatch()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const [current, setCurrent] = useState(1)
    const [pageSize, setPageSize] = useState(10)
    const [total, setTotal] = useState(0)
    const [tableHeight,setTableHeight] = useState(0)
    const [title,setTitle]=useState({})
    const [isExpand, setIsExpand] = useState(false)
    const [operationVisible,setOperationVisible] = useState(false)
    const [operationVisible1,setoperationVisible1] = useState(false)
    const [operationVisible2,setoperationVisible2] = useState(false)
    const [operationVisible3,setoperationVisible3] = useState(false)
    const [operationVisible4,setoperationVisible4] = useState(false)
    const [operationVisible5,setoperationVisible5] = useState(false)
    const [operationVisible6,setoperationVisible6] = useState(false)
    const [vinList, setvinList] = useState([])
    const [dataSource3, setDataSource3] = useState([])
    const [dataSource5, setDataSource5] = useState([])
    const [driverOptions,setdriverOptions] = useState([])
    const [carOptions,setcarOptions] = useState([])
    const [transferClockList,settransferClockList] = useState([])
    const [recordObj,setRecordObj] = useState({})
    const [driverName,setDriveName] = useState('')
    const [driverPhone,setdriverPhone] = useState('')
    const [logisticsProviderNo,setlogisticsProviderNo] = useState('')
    const [logisticsProviderName,setlogisticsProviderName] = useState('')
    const [transferInput,settransferInput] = useState(false)
    const [transferDefult,settransferDefult] = useState(true)
    const [toTrailerLicensePlate,settoTrailerLicensePlate] = useState('')
    const [turnform] = Form.useForm();
    const turnformRef = useRef(null);
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('CarrierListTabsKey') || '1')
    const [defaultQuery, setDefaultQuery] = useState(
        sessionStorage.getItem('CarrierListDefaultQuery') ? JSON.parse(sessionStorage.getItem('CarrierListDefaultQuery')):{carriageStatusList: [0, 1]}
        // {
        // carriageStatusList: [0, 1]
        // lastFollowTime:[moment().subtract(1,'months'),moment()],
        // createTime: [moment().subtract(1, 'months'), moment()],
        // }
    )
    const [dictList,setDictList] = useState({});
    const [logisticsProviderList,setLogisticsProviderList]= useState([]);
    const [importLoading, setImportLoading] = useState(false);
    const [errorList, setErrorList] = useState([]);
    const [phoneVisible, setPhoneVisible] = useState(false)
    const [pePhone, setPePhone] = useState('')
    const [operationVisible4Phone, setOperationVisible4PePhone] = useState('')
    // 获取打卡记录
    const gettransferClockList =(record)=>{
        get(allUrl.truckLogistics.getClockListByVinAndCarriageId,{vin:record.vin,carriageNo:record.carriageNo}).then(res => {
            if (res.success) {
                settransferClockList(res.resp[0])
                console.log(res.resp[0])
            } else {
                message.error(res.msg || '获取打卡记录失败')
            }
        })
    }
    // 获取物流服务商列表
    const getLogisticsProviderList =()=>{
        get(allUrl.truckLogistics.getAllLogisticsProvider).then(res => {
            if (res.success) {
                setLogisticsProviderList(res.resp[0])
            } else {
                message.error(res.msg || '获取物流服务商失败')
            }
        })
    }
    // 获得当前登录人对应的默认物流商
    const getDefaultLogisticsProvider =()=>{
        get(allUrl.truckLogistics.getDefaultLogisticsProvider).then(res => {
            if (res.success && res.resp[0]) {
                setlogisticsProviderNo(res.resp[0].value)
                setlogisticsProviderName(res.resp[0].name)
            } else {
                settransferDefult(false)
            }
        })
    }

    const UploadChange = ({ file, fileList }, type) => {
        console.log(file, type)
        const { response } = file
        setImportLoading(true)
        if (file.status) {
            if (file.status === 'done') {
                if(response.success) {
                    if (response.resp && response.resp.length) {
                        setoperationVisible6(true)
                        setErrorList(response.resp)
                    } else {
                        message.success(response.msg || response.msgCode)
                    }
                } else {
                    message.error(response.msg || response.msgCode)
                }
                setImportLoading(false);
            }
            if (file.status === 'error') {
                message.error(response.msg || response.msgCode)
                setImportLoading(false);
            }
        }else {
            setImportLoading(false);
        }
    }
    const download = () => {
        let url = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/excels/%E8%BF%90%E5%8D%95%E5%BD%92%E6%A1%A3%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
        window.open(url)
    }
    const logisticsProviderNoChange =(value,key)=>{
        // 更改物流服务商所触发的事件
        if(value){
            settransferInput(false)
        }
        if(!value){
            settransferInput(true)
        }
        setlogisticsProviderNo(value)
        if(key){
            setlogisticsProviderName(key.children)
        }
        turnform.setFieldsValue({
            toDriverName: '',
            toTrailerLicensePlate:'',
        })
        setdriverOptions([])
        setcarOptions([])


    }
    const onSearch = (values) => {
        if (values.beginTime && values.beginTime.length) {
            values.beginTimeStart = moment(values.beginTime[0]).format('YYYY-MM-DD')
            values.beginTimeEnd = moment(values.beginTime[1]).format('YYYY-MM-DD')
            delete values.beginTime
        }
        if (values.applyForceTime && values.applyForceTime.length) {
            values.applyForceTimeStart = moment(values.applyForceTime[0]).format('YYYY-MM-DD')
            values.applyForceTimeEnd = moment(values.applyForceTime[1]).format('YYYY-MM-DD')
            delete values.applyForceTime
        }
        setDefaultQuery(values)
        setStorage({
            Query: {...values},
            Type: window.location.hash,
        });
        if (sessionStorage.getItem('CarrierListTabsKey')=== '1') {
            if (childRef1.current ? !childRef1.current.formDown : true) {
                // 待处理运单未展开时手动添加搜索条件【0，1】
                let data ={...values,carriageStatusList: [0, 1]}
                setDefaultQuery(data)
                setStorage({
                    Query: {...data},
                    Type: window.location.hash,
                });
            } else {
                if (!values.carriageStatusList) {
                    // 重置时手动添加搜索条件【0，1】
                    let data ={...values,carriageStatusList: [0, 1]}
                    setDefaultQuery(data)
                    setStorage({
                        Query: {...data},
                        Type: window.location.hash,
                    });
                }
            }
        }
        if (sessionStorage.getItem('CarrierListTabsKey')=== '2') {
            if (childRef2.current ? !childRef2.current.formDown : true) {
                // 待处理运单未展开时手动添加搜索条件【2, 3】
                let data ={...values,carriageStatusList: [2, 3]}
                setDefaultQuery(data)
                setStorage({
                    Query: {...data},
                    Type: window.location.hash,
                });
            } else {
                if (!values.carriageStatusList) {
                    // 重置时手动添加搜索条件【2, 3】
                    let data ={...values,carriageStatusList: [2, 3]}
                    setDefaultQuery(data)
                    setStorage({
                        Query: {...data},
                        Type: window.location.hash,
                    });
                }
            }
        }
    }
    const getTableData = () => {
        setLoading(true)
        let query = { ...defaultQuery }
        let params = { pageNum: current, pageSize, ...query }
        if(tabsKey === '1'){
            tableRef1.current.getTableData()
        }else if(tabsKey === '2'){
            tableRef2.current.getTableData()
        }
    }
    const LookAt = (record) => {
        let data = {
            id: record.id,
            Type: 'LookAt',
            title: '承运单详情',
        }
        UniversalOpenWindow({
            JumpUrl: '/truckLogistics/CarrierListDetail', data,history:props.history
        })
    }
    const addDriver =(record)=>{
        setOperationVisible(true)
        if(driverformRef.current){
            driverform.setFieldsValue({
                driverName:recordObj.driverName,
                driverPhone:recordObj.driverPhone,
                logisticsProviderNo:recordObj.logisticsProviderNo
            })
        }
    }
    const handleCancel =()=>{
        setOperationVisible(false)
    }
    const handleOk = () => {
        // 司机信息
        driverform.validateFields().then(values=>{
            console.log(values);
            let params ={
                id:recordObj.id,
                driverName:values.driverName,
                driverPhone:values.driverPhone,
                logisticsProviderNo:values.logisticsProviderNo,
            }
            post(allUrl.truckLogistics.carriageDriverAdd,{...params}).then(res => {
                if (res.success) {
                    message.success(res.msg || '修改司机信息成功')
                    setOperationVisible(false)
                } else {
                    message.error(res.msg || '修改司机信息失败')
                }
            })
        })
    }
    const renderOperation = (text, record,key) => {
        const menu = (
            <Menu onClick={(e)=>onMenuClick(e,record)}>
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_CLOCKRERECORD') ?
                    <Menu.Item key='1'>
                            <a style={{ cursor: 'pointer' } } >打卡记录</a>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_TRANSFER') && key == '1' ?
                    <Menu.Item key='2'>
                            <a style={{ cursor: 'pointer' } }>转派</a>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_TRANRERECORD') ?
                    <Menu.Item key='3'>
                            <a style={{ cursor: 'pointer' } }>转派日志</a>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_COMPENSATIONDELIVERY') && record.carriageStatus + '' === '1' ? <Menu.Item key='4'>
                            <a style={{ cursor: 'pointer' } }>补偿送达</a>
                    </Menu.Item> : null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_COMPENSATIONDELIVERYLOG') ? <Menu.Item key='5'>
                            <a style={{ cursor: 'pointer' } }>补偿送达日志</a>
                    </Menu.Item> : null
                }
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_LIST_PHONE') ?
                     <Menu.Item key='6'>
                            <a style={{ cursor: 'pointer' } }>查看手机号</a>
                    </Menu.Item>
                    : null
                }
                {/* {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_DETAIL') ?
                    <Menu.Item key='6'>
                            <a style={{ cursor: 'pointer' } }>快速添加司机</a>
                    </Menu.Item>:null
                } */}
                {
                    roleJudgment(userInfo,'TRUCK_LOGISTICS_DETAIL') ?
                    <Menu.Item key='7'>
                            <a style={{ cursor: 'pointer' } }>详情</a>
                    </Menu.Item>:null
                }


            </Menu>
          );
        return <Dropdown overlay={menu} placement="bottomCenter" trigger={['click']} >
                    <a>更多操作</a>
                </Dropdown>
    }
    const onMenuClick = ({key},record) =>{
        setRecordObj(record)
        switch (key){
            case '1' :
            // 打卡记录
                gettransferClockList(record)
                setoperationVisible2(true)
                setTitle('打卡记录')
            break;
            case '2' :
                // 转派
                setoperationVisible1(true)
                setTitle('转派给')
            break;
            case '3' :
                // 转派日志
                setTitle('转派日志')
                setoperationVisible3(true)
                post(allUrl.truckLogistics.getTransferLog,{vin:record.vin,carriageNo:record.carriageNo}).then(res => {
                    if (res.success) {
                    let Dt = res.resp[0]
                    setDataSource3(Dt)
                    } else {
                        // message.error(res.msg)
                    }
                })
            break;
            case '4' :
                console.log('record', record)
                setTitle('补偿送达确认')
                openPhoneModal(record, 1)
            break;
            case '5' :
                // 转派日志
                setTitle('补偿送达确认日志')
                post(allUrl.truckLogistics.getLog,{operationType: 4, key: record.applyForceFinishLogKey}).then(res => {
                    if (res.success) {
                        let Dt = []
                        if(res.resp[0] && res.resp[0].list.length) {
                            Dt = res.resp[0].list
                        } else {
                            Dt = []
                        }
                    setDataSource5(Dt)
                    }
                })
            setoperationVisible5(true)
            break;
            case '6' :
            // addDriver(record)
            openPhoneModal(record)
            break;
            case '7' :
            LookAt(record)
            break;
        }
    }
    const  onSearch1 =_.debounce((value)=>{
        // 搜索
        value =trim(value)
        get(allUrl.truckLogistics.searchDriver, {keywords:value,logisticsProviderNo:logisticsProviderNo}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setdriverOptions(Dt)
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })

    }, 500);
    const onChange1 =(value,key)=>{
     setDriveName(driverOptions[key.key].driverName)
     setdriverPhone(value)
    }
    const  onSearch2 =_.debounce((value)=>{
        value =trim(value)
        get(allUrl.truckLogistics.searchTrailer, {keywords:value,logisticsProviderNo:logisticsProviderNo}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setcarOptions(Dt)
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })

    }, 500);
    const onChange2 =(value)=>{
        settoTrailerLicensePlate(value)
    }
    const handleOk1 = () => {
        // 确定转派
        turnform.validateFields().then(values=>{

            let params ={
                detailId:recordObj.detailId ||'',
                vin:recordObj.vin,
                toDriverName:driverName || '',
                toDriverPhone:driverPhone || '',
                toTrailerLicensePlate:values.toTrailerLicensePlate || '',
                logisticsProviderNo:logisticsProviderNo,
            }
            post(allUrl.truckLogistics.transfer,{...params}).then(res => {
                if (res.success) {
                    getTableData()
                    setoperationVisible1(false)
                    message.success(res.msg || '转派成功')
                } else {
                    message.error(res.msg || '转派失败')
                }
            })
        })
    }
    const handleOk2= () => {
        setoperationVisible2(false)
    }
    const handleOk3 = () => {
        setoperationVisible3(false)
    }
    const handleOk4 = (recordObj) => {
        setoperationVisible4(false)
        openPhoneModal(recordObj, 2)
    }
    const handleOk5 = () => {
        setoperationVisible5(false)
    }

    const tabsCallback = (key) => {
        setTabsKey(key)
        setCurrent(1)
        setPageSize(10)
        sessionStorage.setItem('CarrierListTabsKey',key)
        if(key === '1'){
            sessionStorage.setItem('CarrierListDefaultQuery',JSON.stringify({carriageStatusList: [0, 1]}))
              if(childRef1.current){
                onSearch(childRef1.current.query)
              }else{
                setDefaultQuery({
                    carriageStatusList: [0, 1]
                })
                setStorage({
                    Query: {...defaultQuery,carriageStatusList:[0,1]},
                    Type: window.location.hash,
                  });
              }

        }else if(key === '2'){
            sessionStorage.setItem('CarrierListDefaultQuery',JSON.stringify({carriageStatusList: [2, 3]}))
              if(childRef2.current){
                onSearch(childRef2.current.query)
            }else{
                setDefaultQuery({
                    carriageStatusList: [2, 3]
                })
                setStorage({
                    Query: {...defaultQuery,carriageStatusList:[2,3]},
                    Type: window.location.hash,
                });
            }
        }
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }
    const ExportExcel = (type) =>{
        let query = { ...defaultQuery }
        if (query.beginTime && query.beginTime.length) {
            query.beginTimeStart = moment(query.beginTime[0]).format('YYYY-MM-DD')
            query.beginTimeEnd = moment(query.beginTime[1]).format('YYYY-MM-DD')
            delete query.beginTime
        }
        if(tabsKey === '1'){
            query.carriageStatusList = [0,1]
        }else if(tabsKey === '2'){
            query.carriageStatusList = [2,3]
        }
        Modal.confirm({
            title:'确定导出吗？',
            onOk:()=>{
                setLoading(true)
                post(allUrl.truckLogistics.export,{...query,carriageStatus:tabsKey},{
                    headers:{
                        // 'Content-Type': "application/x-www-form-urlencoded;charset=UTF-8"
                        'Content-Type': "application/json"
                    },
                    responseType: "blob"
                }).then(res=>{
                    setLoading(false)
                    if(res){
                        let dataTime =getData(Date.now())
                        let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                        if (window.navigator.msSaveOrOpenBlob) {
                          //兼容ie
                          window.navigator.msSaveBlob(blob, `${tabsKey==='1'?'待处理运单':'历史运单'}${dataTime}.xlsx`);
                        } else {
                          let downloadElement = document.createElement('a');
                          let href = window.URL.createObjectURL(blob); //创建下载的链接
                          downloadElement.href = href;
                          downloadElement.download = `${tabsKey==='1'?'待处理运单':'历史运单'}${dataTime}.xlsx`; //下载后文件名
                          document.body.appendChild(downloadElement);
                          downloadElement.click(); //点击下载
                          document.body.removeChild(downloadElement); //下载完成移除元素
                          window.URL.revokeObjectURL(href); //释放掉blob对象
                        }
                        message.success(`${tabsKey==='1'?'待处理运单':'历史运单'}${dataTime}下载成功！`)
                    }
                })
            },
            onCancel:()=>{
                return false
            }
        })
    }

    const openPhoneModal = (record, sign) => {
        let url = allUrl.truckLogistics.carriageManagePeShow
         // 转派日志查看手机号
         if (sign === 3) {
            url = allUrl.truckLogistics.transferPeShow
        }
        // 补偿送达日志查看手机号
        if (sign === 4) {
            url = allUrl.truckLogistics.confirmLogPeShow
        }
        post(url,{pe:record.pe}).then(res => {
            if (res.success) {
                // 补偿送达确认
                if (sign===1) {
                    post(allUrl.truckLogistics.forceFinishQuery,{carriageId:record.carriageId, driverPhone: res.resp[0]}).then(response => {
                        if (response.success) {
                        let list = response.resp[0] || []
                        setvinList(list)
                        setOperationVisible4PePhone(res.resp[0])
                        setoperationVisible4(true)
                        }
                    })
                    return
                }
                // 确认补偿送达
                if (sign === 2) {
                    post(allUrl.truckLogistics.forceFinish,{carriageId:recordObj.carriageId,driverPhone: res.resp[0]}).then(response => {
                        if (response.success) {
                            getTableData()
                            setOperationVisible4PePhone(res.resp[0])
                            setoperationVisible4(false)
                            message.success(res.msg || '确认成功')
                        } else {
                            message.error(res.msg || '确认失败')
                        }
                    })
                    return
                }
                // settransferClockList(res.resp[0])
                setPePhone(res.resp[0])
                console.log(res.resp[0])
                setPhoneVisible(true)
            } else {
                message.error(res.msg || '获取手机号码失败')
            }
        })
    }
    const getData =(n)=> {
        let now = new Date(n),
          y = now.getFullYear(),
          m = now.getMonth() + 1,
          d = now.getDate(),
          h = now.getHours(),
          min = now.getMinutes(),
          s = now.getSeconds()
        return " " + y + "年" + (m < 10 ? "0" + m : m) + "月" + (d < 10 ? "0" + d : d) + "日" + (h < 10 ? "0"+h : h) + "时"+ (min < 10 ? "0"+min : min) + "分"+ (s < 10 ? "0"+s : s) + "秒";
      }
      useEffect(()=>{
        if(driverformRef.current){
            driverform.setFieldsValue({
                driverName:recordObj.driverName,
                driverPhone:recordObj.driverPhone,
                logisticsProviderNo:recordObj.logisticsProviderNo
            })
        }
        if(turnformRef.current){
            if(!transferDefult){
                settransferInput(false)
                if(! recordObj.logisticsProviderNo){
                    settransferInput(true)
                }
                turnform.setFieldsValue({
                    logisticsProviderNo:recordObj.logisticsProviderNo,
                })
                setlogisticsProviderNo(recordObj.logisticsProviderNo)
                setlogisticsProviderName(recordObj.logisticsProviderNoDesc)
            }
            if(transferDefult){
                turnform.setFieldsValue({
                    logisticsProviderNo:logisticsProviderNo,
                })
            }

        }
    },[recordObj])
    useEffect(()=>{
        // 服务物流商
        // dispatch(getDict({codes:'dray_logistics_provider_no'})).then(({payload})=>{
        //     setDictList(payload)
        // })
        getLogisticsProviderList()
        getDefaultLogisticsProvider()
    },[])
    useEffect(()=>{
        initPage()
    },[])
    const [columns1, setColums1] = useState([
        { title: '车辆VIN', dataIndex: 'vin', width: 120 ,fixed: 'left' },
        { title: '运单号', dataIndex: 'carriageNo', width: 100 },
        { title: '司机姓名', dataIndex: 'driverName', width: 100 },
        { title: '司机手机号', dataIndex: 'driverPhone', width: 100 },
        { title: '承运板车', dataIndex: 'carrierPallet', width: 120 },
        { title: '物流服务商', dataIndex: 'logisticsProviderNoDesc', width: 150, render:text=>text ? text : '-' },
        { title: '订单类型', dataIndex: 'carriageType', width: 100 , render:text=>text && text==1?'正常':'中转库' },
        { title: '出发地', dataIndex: 'warehouseAddress', width: 200, render:text=>text ? text : '-'},
        { title: '目的地', dataIndex: 'dealerReceiveAddress', width: 200, render:text=>text ? text : '-'  },
        { title: '目的地厅店', dataIndex: 'dealerName', width: 200, render:(text,record)=>record.carriageType == 1 &&text ? text : '-' },
        // { title: '最晚发车时间', dataIndex: 'latestTime', width: 140, render: text => text ? moment(text).format('YYYY年MM月DD日') : '-' },
        // { title: '当前位置', dataIndex: 'currentPosition', width: 200 },
        { title: '物流状态', dataIndex: 'carriageStatusCn', width: 100 },
        { title: '起运时间', dataIndex: 'beginTime', width: 140, render:text=>text ? text : '-'  },
        { title: '是否补偿起运', dataIndex: 'isForceStart', width: 120 ,render:text=>text && text==1?'是':'否' },
        { title: '是否被转派', dataIndex: 'transferFlag', width: 120 ,render:text=>text && text==1?'是':'否' },
        { title: '是否待起运转派', dataIndex: 'isTransferWait', width: 140 ,render:text=>text && text==1?'是':'否' },
        { title: '转派次数', dataIndex: 'transferCount', width: 100,isExtend: true,  },
        { title: '最新转派时间', dataIndex: 'transferMaxTime', width: 150,isExtend: true,  },
        { title: '是否在途转派', dataIndex: 'isTransferWay', width: 120 ,render:text=>text && text==1?'是':'否'},
        { title: '是否申请补偿送达', dataIndex: 'applyForceFinish', width: 160 ,render:text=>text && text==1?'是':'否'},
        { title: '申请补偿送达时间', dataIndex: 'applyForceTime', width: 150, render:text=>text ? text : '-'},
        { title: '打卡状态', dataIndex: 'clockStatus', width: 100 ,render:text=>text && text==1?'未打卡':'已打卡'},
        { title: '最新打卡时间', dataIndex: 'currentClockTime', isExtend: true, width: 140, render:text=>text !==null ? text : '-'  },
        { title: '当日目标合格打卡量', dataIndex: 'currentTargetCount', isExtend: true, width: 160, render: text =>text !==null ? text : '-'  },
        { title: '当日实际合格打卡量', dataIndex: 'currentCount', width: 160, isExtend: true,  render: text => text !==null ? text : '-'  },
        { title: '当日打卡合格率', dataIndex: 'currentClockRate', width: 140, isExtend: true,  render: text => text !==null ? text+'%' : '-'},
        { title: '昨日目标合格打卡量', dataIndex: 'yesterdayTargetCount', isExtend: true, width: 160, render: text => text !==null ? text : '-'  },
        { title: '昨日实际合格打卡量', dataIndex: 'yesterdayCount', width: 160, isExtend: true,  render: text => text !==null ? text : '-'  },
        { title: '昨日打卡合格率', dataIndex: 'yesterdayClockRate', width: 140, isExtend: true,  render: text => text !==null ? text+'%' : '-' },
        { title: '组板单号', dataIndex: 'boardNo', width: 150, isExtend: true,  render: text => text !==null ? text : '-'  },
        { title: '组板完成时间', dataIndex: 'boardFinalTime', width: 150, isExtend: true,  render: text => text !==null ? text : '-'  },
        { title: '操作', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record,1) }
    ])
    const [columns2, setColums2] = useState([
        { title: '车辆VIN', dataIndex: 'vin', width: 100,fixed: 'left' },
        { title: '运单号', dataIndex: 'carriageNo', width: 100 },
        { title: '司机姓名', dataIndex: 'driverName', width: 100 },
        { title: '司机手机号', dataIndex: 'driverPhone', width: 120 },
        { title: '承运板车', dataIndex: 'carrierPallet', width: 120 },
        { title: '物流服务商', dataIndex: 'logisticsProviderNoDesc', width: 150 , render:text=>text ? text : '-'},
        { title: '订单类型', dataIndex: 'carriageType', width: 100 , render:text=>text && text==1?'正常':'中转库' },
        { title: '出发地', dataIndex: 'warehouseAddress', width: 200 , render:text=>text ? text : '-' },
        { title: '目的地', dataIndex: 'dealerReceiveAddress', width: 200, render:text=>text ? text : '-'  },
        { title: '目的地厅店', dataIndex: 'dealerName', width: 200,  render:(text,record)=>record.carriageType == 1 &&text ? text : '-'   },
        { title: '起运时间', dataIndex: 'beginTime', width: 140, render:text=>text ? text : '-' },
        { title: '到达时间', dataIndex: 'endTime', width: 140, render:text=>text ? text : '-' },
        { title: '物流状态', dataIndex: 'carriageStatusCn', width: 100 },
        { title: '是否补偿起运', dataIndex: 'isForceStart', width: 120 ,render:text=>text && text==1?'是':'否' },
        { title: '是否补偿送达', dataIndex: 'isForceFinish', width: 120 ,render:text=>text && text==1?'是':'否' },
        { title: '是否被转派', dataIndex: 'transferFlag', width: 120 ,render:text=>text && text==1?'是':'否' },
        { title: '是否待起运转派', dataIndex: 'isTransferWait', width: 140 ,render:text=>text && text==1?'是':'否' },
        { title: '转派次数', dataIndex: 'transferCount', width: 100 ,isExtend: true, },
        { title: '最新转派时间', dataIndex: 'transferMaxTime', width: 150 ,isExtend: true, },
        { title: '是否在途转派', dataIndex: 'isTransferWay', width: 120 ,render:text=>text && text==1?'是':'否'},
        { title: '是否申请补偿送达', dataIndex: 'applyForceFinish', width: 160 ,render:text=>text && text==1?'是':'否'},
        { title: '申请补偿送达时间', dataIndex: 'applyForceTime', width: 150, render:text=>text ? text : '-'},
        { title: '目标合格打卡量', dataIndex: 'sumTargetCount', isExtend: true, width: 150, render: text => text !==null ? text : '-'  },
        { title: '实际合格打卡量', dataIndex: 'sumCount', width: 150, isExtend: true,  render: text => text !==null ? text : '-'  },
        { title: '打卡合格率', dataIndex: 'avgClockRate', width: 140, isExtend: true,  render: text => text !==null ? text+'%' : '-'},
        { title: '验收结果', dataIndex: 'checkResult', width: 100, isExtend: true,  render: text => text !==null ? text : '-'},
        { title: '验收时间', dataIndex: 'checkTime', width: 140, isExtend: true,  render: text => text !==null ? text : '-'},
        { title: '操作', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record,2) }
    ])

    let searchList1 = [
        { label: '车辆VIN', name: 'vin', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '运单号', name: 'carriageNo', type: 'Input', placeholder: '请输入', colSpan: 6 },

        { label: '司机姓名', name: 'driverName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '司机手机号', name: 'driverPhone', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '承运板车', name: 'carrierPallet', type: 'Input', placeholder: '请输入', colSpan: 6 },
        {
            label: '物流服务商', name: 'logisticsProviderNo', type: 'Select', placeholder: '请选择', colSpan: 6, data:logisticsProviderList || []
        },
        { label: '订单类型', name: 'carriageTypeList',mode: 'multiple', initialValue:[],type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '正常', value: 1 },
            { name: '中转库', value: 2 },
        ] },
        { label: '物流状态', name: 'carriageStatusList', mode: 'multiple',initialValue:[0,1],  width: 100, type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '待起运',key: 1, value: 0 },
            { name: '在途', key: 2, value: 1 },
        ],rules:[
            {
              required: true,
              message: '物流状态必选',
            },
          ]
        },
        { label: '是否被转派', name: 'transferFlag', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '否', value: '0' },
            { name: '是', value: '1' },
        ] },
        { label: '目的地厅店', name: 'destination', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '起运日期', name: 'beginTime', type: 'RangePicker', placeholder: ['开始时间', '截止时间'], colSpan: 6 },
        {
            label: '是否打卡', name: 'clockStatus', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '未打卡', value: '1' },
                { name: '已打卡', value: '2' },
            ]
        },
        {
            label: '是否申请补偿送达', name: 'applyForceFinish', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '否', value: '0' },
                { name: '是', value: '1' }
            ]
        },
        { label: '申请补偿送达时间', name: 'applyForceTime', type: 'RangePicker', placeholder: ['开始时间', '截止时间'], colSpan: 6 }
    ]
    let searchList2 = [
        { label: '车辆VIN', name: 'vin', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '运单号', name: 'carriageNo', type: 'Input', placeholder: '请输入', colSpan: 6 },

        { label: '司机姓名', name: 'driverName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '司机手机号', name: 'driverPhone', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '承运板车', name: 'carrierPallet', type: 'Input', placeholder: '请输入', colSpan: 6 },
        {
            label: '物流服务商', name: 'logisticsProviderNo', type: 'Select', placeholder: '请选择', colSpan: 6, data:logisticsProviderList || []
        },
        { label: '订单类型', name: 'carriageTypeList', mode: 'multiple',initialValue:[],type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '正常', value: 1 },
            { name: '中转库', value: 2 },
        ] },
        { label: '物流状态', name: 'carriageStatusList', mode: 'multiple',initialValue:[2,3],  width: 100, type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '到达',key: 3, value: 2 },
            { name: '已取消',key: 4, value: 3 },
        ],rules:[
            {
              required: true,
              message: '物流状态必选',
            },
          ]},
        { label: '是否被转派', name: 'transferFlag', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '否', value: '0' },
            { name: '是', value: '1' },
        ] },
        { label: '目的地厅店', name: 'destination', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '起运日期', name: 'beginTime', type: 'RangePicker', placeholder: ['开始时间', '截止时间'], colSpan: 6 },
        {
            label: '是否申请补偿送达', name: 'applyForceFinish', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '否', value: '0' },
                { name: '是', value: '1' }
            ]
        },
        { label: '申请补偿送达时间', name: 'applyForceTime', type: 'RangePicker', placeholder: ['开始时间', '截止时间'], colSpan: 6 }
    ]
    const InforData3 = {
        rowKey: record => record.key,
        bordered: true,
        dataSource:dataSource3,
        scroll: { x: 'max-content' },
        columns: [
            { title: '时间', dataIndex: 'transferTime', width: 180},
            { title: '司机姓名', dataIndex: 'driverName', width: 100 },
            { title: '手机号', dataIndex: 'driverPhone', width: 140, render: (text, record) => renderOperationPhone(text, record)  },
            { title: '物流服务商', dataIndex: 'driverLogisticsProviderName', width: 150 },
            { title: '承运板车', dataIndex: 'trailerLicensePlate', width: 120 },
            { title: '操作者', dataIndex: 'transferByName', width: 100},
            { title: '操作者ID', dataIndex: 'transferBy', width: 100 },
            { title: '操作者所属组织', dataIndex: 'transferByOrg', width: 180 }
        ],
        pagination: false,
        rowSelection: null,
    };

    const renderOperationPhone = (text, record) => {
        // 操作按钮
        return <div style={{ color: '#1890ff' }}>
        {
            roleJudgment(userInfo,'TRUCK_LOGISTICS_TRANRERECORD_PHONE') ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openPhoneModal(record, 3)}>查看手机号</span>,
                ] : null
        }
        </div>
    }

    // 补偿送达确认日志修改内容
    const renderConent = (text, record) => {
        if (record.operationType == 4) {
            return <div>
                {text}
                <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'TRUCK_LOGISTICS_COMPENSATIONDELIVERYLOG_PHONE') ?
                    [
                        <span key={1} style={{ cursor: 'pointer' }} onClick={() => openPhoneModal(record, 4)}>查看手机号</span>,
                    ] : null
            }
        </div>
            </div>


        }else {
        return <div>
                {text}
                </div>
        }

    }

    const InforData5 = {
        rowKey: record => record.key,
        bordered: true,
        dataSource:dataSource5,
        scroll: { x: 'max-content' },
        columns: [
            { title: '时间', dataIndex: 'createTime', width: 180},
            { title: '操作者', dataIndex: 'userName', width: 100},
            { title: '操作者ID', dataIndex: 'userId', width: 100 },
            { title: '操作者所属组织', dataIndex: 'orgNames', width: 180 },
            { title: '操作类型', dataIndex: 'operationType', width: 140, render: (text, record) => renderStatusName(LOG_TYPE, text)  },
            { title: '修改内容', dataIndex: 'content', width: 180, render: (text, record) => renderConent(text, record) },
        ],
        pagination: false,
        rowSelection: null,
    };
    let newColumns1 = _.cloneDeep({ columns1 }).columns1
    for (let i = 0; i < newColumns1.length; i++) {
        if (JSON.stringify(newColumns1[i]['checked']) !== undefined && !newColumns1[i].checked) {
            newColumns1.splice(i, 1)
            i--
        }
    }
    let newColumns2 = _.cloneDeep({ columns2 }).columns2
    for (let i = 0; i < newColumns2.length; i++) {
        if (JSON.stringify(newColumns2[i]['checked']) !== undefined && !newColumns2[i].checked) {
            newColumns2.splice(i, 1)
            i--
        }
    }

    return (
        <div className='CarrierList'>
            <Tabs onChange={tabsCallback} type="card" activeKey={tabsKey} defaultActiveKey={tabsKey} >
                <TabPane tab="待处理运单" key="1">
                    <PublicTableQuery initPage={initPage} tabsKey={tabsKey} onSearch={onSearch} searchList={searchList1}  isCatch={true} isFormDown={false} ref={childRef1}/>
                    <div className='tableData'>
                        <Row className='tableTitle'>
                            <Col span={24} style={{textAlign:'right'}}>
                                {
                                    roleJudgment(userInfo, 'TRUCK_LOGISTICS_DELIVERY_FILING') ?
                                    <>
                                        <UploadFile
                                            style={{ display: 'inline-block' }}
                                            extension={['xls', 'xlsx']}
                                            showUploadList={false}
                                            size={10}
                                            action={baseURL.Host + allUrl.truckLogistics.importArchiveVin}
                                            UploadChange={UploadChange}
                                        >
                                            <Button type='primary' loading={importLoading}>车辆送达归档</Button>
                                        </UploadFile>
                                    </>:null
                                }
                                {

                                    roleJudgment(userInfo, 'TRUCK_LOGISTICS_DOWNLOAD_TEMPLATE') ?
                                    <>
                                        <a style={{fontSize:'14px',margin:'6px 20px 0 10px'}} onClick={download}>模版下载</a>
                                    </>:null
                                }
                                {
                                    roleJudgment(userInfo,'TRUCK_LOGISTICS_CARRIAGETYPE1_EXPORT') ?
                                        <Button type='primary' onClick={()=>ExportExcel(1)} >导出</Button>
                                        : null
                                }
                                <ExtendedColumn setColums={setColums1} columns={columns1} />
                            </Col>
                        </Row>
                        {
                            tabsKey === '1' &&
                            <PublicTable
                                url={allUrl.truckLogistics.getCarriageList}
                                isCatch={true}
                                columns={newColumns1}
                                type={2}
                                rowSelection={false}
                                ref={tableRef1}
                                setCurrent={setCurrent}
                                setPageSize={setPageSize}
                                defaultQuery={{...defaultQuery}}
                            />
                        }
                    </div>
                </TabPane>
                <TabPane tab="历史运单" key="2">
                    <PublicTableQuery initPage={initPage} tabsKey={tabsKey} onSearch={onSearch} searchList={searchList2}  isCatch={true} isFormDown={false} ref={childRef2}/>
                    <div className='tableData'>
                        <Row className='tableTitle'>
                            <Col span={24} style={{textAlign:'right'}}>
                                {
                                    roleJudgment(userInfo,'TRUCK_LOGISTICS_CARRIAGETYPE1_EXPORT') ?
                                        <Button type='primary' onClick={()=>ExportExcel(1)} >导出</Button>
                                        : null
                                }
                                <ExtendedColumnNew setColums={setColums2} columns={columns2} />
                            </Col>
                        </Row>
                        {
                            tabsKey === '2' &&
                            <PublicTable
                                url={allUrl.truckLogistics.getCarriageList}
                                isCatch={true}
                                columns={newColumns2}
                                type={2}
                                rowSelection={false}
                                ref={tableRef2}
                                setCurrent={setCurrent}
                                setPageSize={setPageSize}
                                defaultQuery={{...defaultQuery}}
                            />
                        }
                    </div>
                </TabPane>
            </Tabs>
            {
            operationVisible &&
            <Modal  width={800}  visible={operationVisible} title='添加信息' recordObj={recordObj} onOk={handleOk}  onCancel={handleCancel}>
                <Form layout={'vertical'} form={driverform} ref={driverformRef}>
                    <Row className='FormRowCon'>
                        <Col span={8}>
                        <Form.Item wrapperCol={{ span: 20 }}label="司机姓名" name="driverName" rules={[{ required: true, message: '请输入司机姓名', }]}>
                            <Input placeholder="请输入" disabled />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item wrapperCol={{ span: 20 }}label="司机手机号" name="driverPhone" validateTrigger='onSubmit' rules={[{ required: true, message: '请输入司机手机号'},
                                        {
                                            validator(_, value) {
                                                if (value && !/^1[3-9]\d{9}$/.test(value)) {
                                                    return Promise.reject(new Error('请输入格式正确的手机号码'));
                                                } else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}>
                                <Input placeholder="请输入" disabled/>
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                        <Form.Item wrapperCol={{ span: 20 }}label="物流服务商" name="logisticsProviderNo" rules={[{ required: true, message: '请选择物流服务商', }]}>
                        <Select
                            placeholder='请选择'
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            filterOption={false}>
                            {
                               logisticsProviderList.map((item, index) =>  <Option key={index} value={item.value}>{item.name}</Option>)

                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
            operationVisible1 &&
            // 转派
            <Modal  width={520}
            footer={[
                        <Button onClick={()=>setoperationVisible1(false)} key={1}>取消</Button>,
                        <Popconfirm key={2} onConfirm={handleOk1} className='popconIcon' title={
                        <>
                        <h3 style={{marginBottom:'10px'}}>转派确认</h3>
                        <div style={{marginBottom:'10px'}}>是否确认将车辆{recordObj.vin}转给</div>
                        <div style={{marginBottom:'10px'}}>物流服务商：{logisticsProviderName || ''}</div>
                        <div style={{marginBottom:'10px'}}>司机姓名：{driverName || ''}     ({driverPhone || ''})</div>
                        <div>板车车牌号：{toTrailerLicensePlate || ''}</div>
                        </>
                    } okText='确定' cancelText='取消'>
                        <Button type="primary">确定</Button>
                        </Popconfirm>
                    ]}
            visible={operationVisible1} title={title} recordObj={recordObj} onOk={handleOk1}  onCancel={()=>setoperationVisible1(false)}>
                <Form  form={turnform} ref={turnformRef}>
                <Row className='FormRowCon'>
                        <Col span={4}></Col>
                        <Col span={15}>
                        <Form.Item wrapperCol={{ span: 20 }}label="物流商" name="logisticsProviderNo" rules={[{ required: true, message: '请选择物流服务商', }]}>
                        <Select
                            placeholder='请选择'
                            allowClear
                            showSearch
                            onChange={logisticsProviderNoChange}
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            filterOption={false}>

                            {
                               logisticsProviderList.map((item, index) =>  <Option key={index} value={item.value}>{item.name}</Option>)

                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row className='FormRowCon'>
                        <Col span={4}></Col>
                        <Col span={15}>
                        <Form.Item wrapperCol={{ span: 20 }}label="司&nbsp;&nbsp;&nbsp;&nbsp;机：" name="toDriverName" rules={[{ required: true, message: '请选择要转派的司机', }]}>
                        <Select
                            placeholder='请输入姓名/手机号后选择'
                            disabled={transferInput}
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            onSearch={onSearch1}
                            onChange={onChange1}
                            filterOption={false}>
                            {  driverOptions&&
                                driverOptions.map((item, index) =>  <Option key={index} value={item.driverPhone}>{item.driverName}({item.driverPhone})</Option>)

                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row className='FormRowCon'>
                        <Col span={4}></Col>
                        <Col span={15}>
                        <Form.Item wrapperCol={{ span: 20 }}label="板&nbsp;&nbsp;&nbsp;&nbsp;车：" name="toTrailerLicensePlate" rules={[{ required: true, message: '请选择要转派的司机', }]}>
                        <Select
                            placeholder='请输入车牌号进行搜索'
                            allowClear
                            disabled={transferInput}
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            onSearch={onSearch2}
                            onChange={onChange2}
                            filterOption={false}>
                            {   carOptions&&
                                carOptions.map((item, index) =>  <Option key={index} value={item.licensePlate}>{item.licensePlate}</Option>)

                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
                // 打卡记录
            operationVisible2 &&
            <Modal  width={600} footer={null} visible={operationVisible2} title={title} recordObj={recordObj} onOk={handleOk2}  onCancel={()=>setoperationVisible2(false)}>
               <div className='PunchInRecord'>
                    <Timeline>
                        {   transferClockList&&
                            transferClockList.map((item, index) => {
                                return (isExpand ? index < 5 : index >= 0)
                                    && <Timeline.Item key={index} color={index === 0 ? 'blue' : 'gray'}>
                                        <div className='TimelineItem_Con'>
                                            <span className='time' style={{marginRight:'20px'}}>{item.time}</span>
                                            <span className='statusCn'>
                                                { item.status =='1'?
                                                <Tag color="gold">{item.statusCn}</Tag> :
                                                [item.status =='2'?
                                                <Tag color="blue">{item.statusCn}</Tag> :
                                                <Tag color="green">{item.statusCn}</Tag>
                                                ]
                                                }
                                            </span>
                                            <p className='address'>位置：{item.address}</p>
                                            <p className='address'>承运板车：{item.carrierPallet}</p>
                                            <p className='address'>打卡人：{item.recorder}</p>
                                        </div>
                                    </Timeline.Item>
                            })
                        }
                    </Timeline>
                        <p className='more'>
                            {isExpand ?
                                <span onClick={() => {
                                    setIsExpand(false)
                                }}>点击查看更多<DownOutlined /></span>
                                : <span>没有更多打卡记录啦～</span>
                            }
                        </p>
                </div>
            </Modal>
            }
            {
                // 转派日志
            operationVisible3 &&
            <Modal  width={1200}  footer={null} visible={operationVisible3} title={title} recordObj={recordObj} onOk={handleOk3}  onCancel={()=>setoperationVisible3(false)}>
                <Table {...InforData3} />
            </Modal>
            }
            {
                // 补偿送达确认
            operationVisible4 &&
            <Modal
            okText='确认补偿送达'
            width={1200} visible={operationVisible4} title={title} recordObj={recordObj} onOk={handleOk4.bind(this, recordObj)}  onCancel={()=>setoperationVisible4(false)}>
                <p>运单号：{recordObj.carriageNo}</p>
                <p>司机姓名：{recordObj.driverName} <span style={{width: 100, marginRight: '30px'}}></span>手机号：{operationVisible4Phone}</p>
                <p>该司机运送的车辆列表：</p>
               {
                    vinList.length ?  vinList.map((item) => {
                       return( <p>{item}</p> )
                    }) : null
               }
            </Modal>
            }
            {
                // 补偿送达日志
            operationVisible5 &&
            <Modal  width={1200}  footer={null} visible={operationVisible5} title={title} recordObj={recordObj} onOk={handleOk5}  onCancel={()=>setoperationVisible5(false)}>
                <Table {...InforData5} />
            </Modal>
            }
            {/* 车辆送达归档 */}
            {
                operationVisible6 &&
                <Modal width={1200} footer={null} visible={operationVisible6} title='车辆送达归档' onCancel={()=>{setoperationVisible6(false);setErrorList([])}}>
                    <p>只有待起运/在途，且7天以前的VIN可以归档，以下VIN不符合，请检查后重新导入:</p>
                    {
                        errorList.length ? errorList.map((item,index) => {
                        return( <p style={{margin:'10px 0'}}>{index+1}. {item}</p> )
                        }) : null
                    }
                </Modal>
            }
            {
            phoneVisible &&
            <Modal  width={600}  open={phoneVisible} title='查看手机号' recordObj={recordObj} onOk={() => setPhoneVisible(false)}  onCancel={()=>setPhoneVisible(false)}>
                {pePhone}
            </Modal>
            }
        </div>
    )
}
export default ClueList
