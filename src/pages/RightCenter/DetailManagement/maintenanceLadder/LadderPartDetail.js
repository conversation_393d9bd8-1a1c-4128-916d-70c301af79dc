import React from 'react'
import { Modal, Row, Col, Empty } from 'antd';
import LadderPartBottom from './LadderPartBottom';
import { Descriptions, Button } from 'antd';
import ladderStyle from './ladderPart.module.less'

const LadderPartDetail = (props) => {
    const { ladderPartDetailVisible, handleLadderVisible, data, strategyConfig } = props
    //取出key值
    const { maintenanceLadderDetail, maintenanceLadderDetailItem, maintenanceLadderLimitFrame } = strategyConfig['maintenanceLadderFrame'].c
    const { ladderTimeFrame, ladderLimitYear, ladderLimitMonth, ladderMileageFrame, ladderLimitMileage, ladderNo } = maintenanceLadderDetailItem

    const _maintenanceLadderLimitFrame = data[maintenanceLadderLimitFrame.value]

    const LadderPartDetailItem = (item, index) => {
        return <div className={ladderStyle['ladder-part']} key={index}>
            <div className={ladderStyle['num']}>{index + 1}</div>
            <div className={ladderStyle['content']}>
                <Descriptions column={1}>
                    <Descriptions.Item label='阶梯明细编码'>{item[ladderNo.value] ?? '-'}</Descriptions.Item>
                    {
                        _maintenanceLadderLimitFrame !== 2 ?
                            <Descriptions.Item label='阶梯限制年限'>{`${item[ladderLimitYear.value] ?? 0}年${item[ladderLimitMonth.value] ?? 0}月`}</Descriptions.Item>
                            : null
                    }
                    {
                        _maintenanceLadderLimitFrame !== 1 ?
                            <Descriptions.Item label='阶梯限制里程'>{`${item[ladderLimitMileage.value]}公里`}</Descriptions.Item>
                            : null
                    }
                </Descriptions>
                <LadderPartBottom
                    {...props}
                    index={index}
                    ladderStyle={ladderStyle}
                />
            </div>
        </div>

    }

    return (
        <Modal
            title="全保养阶梯策略"
            visible={ladderPartDetailVisible}
            maskClosable={false}
            onCancel={handleLadderVisible}
            width={900}
            footer={<Button onClick={handleLadderVisible}>返回</Button>}
        >
            {
                data[maintenanceLadderDetail.value] ?
                    data[maintenanceLadderDetail.value].map((item, index) => LadderPartDetailItem(item, index)) :
                    <Empty />
            }

        </Modal>

    )
}

export default LadderPartDetail