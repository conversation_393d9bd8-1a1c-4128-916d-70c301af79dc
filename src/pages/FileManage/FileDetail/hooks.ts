import {
  postSeresFileOperateStorePreviewDownloadExport,
  postSeresFileOperateStorePreviewDownloadQuery,
  postSeresFileOperateSupplierPreviewDownloadExport,
  postSeresFileOperateSupplierPreviewDownloadQuery,
  postSeresFileStatisticStoreInCompleteList,
  postSeresFileStatisticSupplierInCompleteList,
} from '@/apis/file-manage';
import { ProTableColumn } from '@/components/pro-table';
import { useMemo } from 'react';
import { FileDeliverType } from '../constants';

export function useIsStore(file: FileManage.FileItemDetailVO | undefined) {
  const isStore = useMemo(
    () => [FileDeliverType.ALL_STORE, FileDeliverType.SPECIFIC_STORE].includes(file?.deliverType as FileDeliverType),
    [file]
  );

  return isStore;
}

export function useDeliverTypeLabel(file: FileManage.FileItemDetailVO | undefined) {
  const isStore = useIsStore(file);
  const deliverTypeLabel = useMemo(() => (isStore ? '门店' : '供应商'), [isStore]);

  return deliverTypeLabel;
}

export function useFileIncompleteApi(file: FileManage.FileItemDetailVO | undefined) {
  const isStore = useIsStore(file);

  return isStore ? postSeresFileStatisticStoreInCompleteList : postSeresFileStatisticSupplierInCompleteList;
}

export function useViewFileIncompleteApi(file: FileManage.FileItemDetailVO | undefined) {
  const isStore = useIsStore(file);

  return isStore ? postSeresFileOperateStorePreviewDownloadQuery : postSeresFileOperateSupplierPreviewDownloadQuery;
}

export function useDownloadFileIncompleteApi(file: FileManage.FileItemDetailVO | undefined) {
  const isStore = useIsStore(file);

  return isStore ? postSeresFileOperateStorePreviewDownloadExport : postSeresFileOperateSupplierPreviewDownloadExport;
}

export function useStatisticDetailTableViewed(file: FileManage.FileItemDetailVO | undefined) {
  const columns = useMemo<ProTableColumn[]>(
    () => [
      {
        title: '序号',
        key: 'index',
        valueType: 'index',
      },
      {
        title: '文件名',
        dataIndex: 'fileName',
      },
      {
        title: '门店',
        dataIndex: 'organizeName',
        hideInTable: ![FileDeliverType.ALL_STORE, FileDeliverType.SPECIFIC_STORE].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '供应商',
        dataIndex: 'organizeName',
        hideInTable: ![FileDeliverType.ALL_SUPPLIER, FileDeliverType.SPECIFIC_SUPPLIER].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '姓名',
        dataIndex: 'userName',
      },
      {
        title: '工号',
        dataIndex: 'userOneId',
        hideInTable: ![FileDeliverType.ALL_STORE, FileDeliverType.SPECIFIC_STORE].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '浏览次数',
        dataIndex: 'operateNum',
      },
      {
        title: '浏览时长（分）',
        dataIndex: 'previewDuration',
      },
    ],
    [file]
  );

  return columns;
}

export function useStatisticDetailTableDownloaded(file: FileManage.FileItemDetailVO | undefined) {
  const columns = useMemo<ProTableColumn[]>(
    () => [
      {
        title: '序号',
        key: 'index',
        valueType: 'index',
      },
      {
        title: '文件名',
        dataIndex: 'fileName',
      },
      {
        title: '门店',
        dataIndex: 'organizeName',
        hideInTable: ![FileDeliverType.ALL_STORE, FileDeliverType.SPECIFIC_STORE].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '供应商',
        dataIndex: 'organizeName',
        hideInTable: ![FileDeliverType.ALL_SUPPLIER, FileDeliverType.SPECIFIC_SUPPLIER].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '姓名',
        dataIndex: 'userName',
      },
      {
        title: '工号',
        dataIndex: 'userOneId',
        hideInTable: ![FileDeliverType.ALL_STORE, FileDeliverType.SPECIFIC_STORE].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '下载次数',
        dataIndex: 'operateNum',
      },
    ],
    [file]
  );

  return columns;
}

export function useStatisticDetailTableColumns(file: FileManage.FileItemDetailVO | undefined) {
  const columns = useMemo<ProTableColumn[]>(
    () => [
      {
        title: '序号',
        dataIndex: 'serialNo',
      },
      {
        title: '文件名',
        dataIndex: 'fileName',
      },
      {
        title: '门店',
        dataIndex: 'storeName',
        hideInTable: ![FileDeliverType.ALL_STORE, FileDeliverType.SPECIFIC_STORE].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '供应商',
        dataIndex: 'supplierName',
        hideInTable: ![FileDeliverType.ALL_SUPPLIER, FileDeliverType.SPECIFIC_SUPPLIER].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '姓名',
        dataIndex: 'userName',
      },
      {
        title: '工号',
        dataIndex: 'userNo',
        hideInTable: ![FileDeliverType.ALL_STORE, FileDeliverType.SPECIFIC_STORE].includes(
          file?.deliverType as FileDeliverType
        ),
      },
      {
        title: '岗位',
        dataIndex: 'positionName',
      },
    ],
    [file]
  );

  return columns;
}
