import React, { useEffect, useState } from 'react'
import { Tabs } from 'antd'
import TabBase from './TabBase'
import TabList from './TabList'
import { get } from '@/utils/request';
import allUrl from '@/utils/url';
import customTabStyle from './customTab.module.less'

const { TabPane } = Tabs

const PartsManagement = (props) => {
    const [DictCarData, setDictCarData] = useState([]) // 车型名称接口
    const [activeIndex, setActiveIndex] = useState(1)
    const [tabContent, setTabContent] = useState()

    //配件保修类型枚举值
    const PART_TYPE_OPTIONS = [
        { name: '全部', value: null },
        { name: '整车件', value: 1 },
        { name: '核心零配件', value: 2 },
        { name: '易损易耗件', value: 3 },
        { name: '不保件', value: 4 }
    ]

    // 车型名称字段接口
    const getCarType = () => {
        get(allUrl.common.entryLists, { codes: 'equity_car_type' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt['equity_car_type'].forEach(item => {
                    item.name = item.entryMeaning
                    item.value = item.entryMeaning
                })
                setDictCarData(Dt['equity_car_type'])
            }
        })
    }

    const tabChange = (key) => {
        setActiveIndex(key)
    }

    useEffect(() => {
        getCarType();
    }, [])

    return (
        <div className='parts-page'>
            <Tabs defaultActiveKey={activeIndex} onChange={tabChange} className={customTabStyle['custom-tab-box']}>
                <TabPane tab="配件基表" key="1">
                    <div style={{ width: '100%', height: 24, background: '#f0f2f5', fontSize: 0, textIndent: -999 }}>空白占位,仅做样式</div>
                    <TabBase DictCarData={DictCarData} PART_TYPE_OPTIONS={PART_TYPE_OPTIONS} />
                </TabPane>
                <TabPane tab="保外配件" key="2">
                    <div style={{ width: '100%', height: 24, background: '#f0f2f5', fontSize: 0, textIndent: -999 }}>空白占位,仅做样式</div>
                    <TabList DictCarData={DictCarData} PART_TYPE_OPTIONS={PART_TYPE_OPTIONS} />
                </TabPane>
            </Tabs>
        </div >
    )
}
export default PartsManagement
