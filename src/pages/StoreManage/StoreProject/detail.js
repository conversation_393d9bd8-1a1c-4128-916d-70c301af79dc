import React, { useEffect, useState } from 'react'
import { message, Button, Input, Popconfirm, Tooltip, Form, Card, Table, Radio, Select, DatePicker, InputNumber, Upload, Modal } from 'antd'
import { PlusOutlined, FormOutlined, DeleteOutlined } from '@ant-design/icons';
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get, post } from '@/utils/request'
import allUrl from '@/utils/url'
import { useSelector } from 'react-redux'
import Collapse, { Panel } from '@/components/Public/Collapse'
import AddOrganization from './components/addOrganization'
import moment from 'moment';
import baseURL from '@/baseURL'
import Cookies from 'js-cookie'
import PublicTooltip from '@/components/Public/PublicTooltip'
import ViewAttachments from './components/viewAttachments'
import {AccessSubmission} from './components/verificationProcess'
import PreviewImg from './components/previewImg'
import {verifyTrueField} from '@/utils'
import _ from 'lodash'
import { QuestionCircleOutlined } from '@ant-design/icons'
import './detail.less'
// import './Detail.less'
const { Option } = Select
const { Item } = Form

const fileType = [
    { name: 'mainContract_file', type: 1 },
    { name: 'accessoriesAgreement_file', type: 2 },
    { name: 'service_file', type: 3 },
    { name: 'brandAuthorization_file', type: 5 },
    { name: 'survey_file', type: 6 },
    { name: 'floorPlanComplete_file', type: 7 },
    { name: 'renderingComplete_file', type: 8 },
    { name: 'constructionDrawing_file', type: 9 },
    { name: 'constructionStart_file', type: 10 },
    { name: 'identificationMaterialOrder_file', type: 11 },
    { name: 'materialArrival_file', type: 12 },
    { name: 'financialContract_file', type: 13 },
    { name: 'insuranceDeal_file', type: 14 },
    { name: 'maintenanceQualification_file', type: 15 },
    { name: 'businessLicenseOfficialSeal_file', type: 16 },
    { name: 'pos_file', type: 17 },
    { name: 'motorVehicleSalesInvoiceInvoiceSeal_file', type: 18 }
]

const RadioOption1 = [{ name: '是', value: 1 }, { name: '否', value: 0 }]
const RadioOption2 = [{ name: '有', value: 1 }, { name: '无', value: 0 }]

let CardSourseArr = [
    {
        title: '合同签订情况',
        CardItem: [
            {
                type: 'Text', label: '合同类型', name: '交付与服务主合同', children: [
                    { type: 'Radio', label: '是否完成', name: 'mainContractStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'mainContractCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'mainContract_file', promptText: '《交付与服务主合同》',required:false , attachmentType: 1}
                ]
            },
            {
                type: 'Text', label: '合同类型', name: '配件协议', children: [
                    { type: 'Radio', label: '是否完成', name: 'accessoriesAgreementStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'accessoriesAgreementCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'accessoriesAgreement_file', promptText: '《配件协议》',required:false , attachmentType: 2 }
                ]
            },
            {
                type: 'Text', label: '合同类型', name: '服务协议', children: [
                    { type: 'Radio', label: '是否完成', name: 'serviceStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'serviceCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'service_file', promptText: '《服务协议》',required:false , attachmentType: 3 }
                ]
            },
            {
                type: 'Text', label: '合同类型', name: '代销协议', children: [
                    { type: 'Radio', label: '是否完成', name: 'proxySaleAgreementStatus', data: RadioOption1,required:true }
                ]
            },
            {
                type: 'Text', label: '合同类型', name: '品牌、服务授权书', children: [
                    { type: 'Radio', label: '是否完成', name: 'brandAuthorizationStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'brandAuthorizationCompleteTime' ,required:true},
                    { type: 'File', label: '证明文件', name: 'brandAuthorization_file', promptText: '《品牌、服务授权书》',required:false , attachmentType: 5 }
                ]
            },
        ]
    },
    {
        title: '装修情况', CardItem: [
            {
                type: 'Text', label: '装修环节', name: '勘测时间', children: [
                    { type: 'Radio', label: '是否完成', name: 'surveyStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'surveyCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'survey_file', promptText: '《勘测申请函》',required:false , attachmentType: 6 }
                ]
            },
            {
                type: 'Text', label: '装修环节', name: '平面图确认', children: [
                    { type: 'Radio', label: '是否完成', name: 'floorPlanStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'floorPlanCompleteTime',required:true},
                    { type: 'File', label: '证明文件', name: 'floorPlanComplete_file', promptText: '《平面图文件》',required:false , attachmentType: 7 }
                ]
            },
            {
                type: 'Text', label: '装修环节', name: '效果图确认', children: [
                    { type: 'Radio', label: '是否完成', name: 'renderingStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'renderingCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'renderingComplete_file', promptText: '《效果图文件》',required:false , attachmentType: 8 }
                ]
            },
            {
                type: 'Text', label: '装修环节', name: '施工图确认', children: [
                    { type: 'Radio', label: '是否完成', name: 'constructionDrawingStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'constructionDrawingCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'constructionDrawing_file', promptText: '《施工图确认文件》',required:false , attachmentType: 9 }
                ]
            },
            {
                type: 'Text', label: '装修环节', name: '施工启动', children: [
                    { type: 'Radio', label: '是否完成', name: 'constructionStartStatus', data: RadioOption1 ,required:true},
                    { type: 'DatePicker', label: '完成时间', name: 'constructionStartCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'constructionStart_file', promptText: '《施工启动文件》',required:false , attachmentType: 10 }
                ]
            },
            {
                type: 'Text', label: '装修环节', name: '标识物料下单', children: [
                    { type: 'Radio', label: '是否完成', name: 'identificationMaterialOrderStatus', data: RadioOption1 ,required:true},
                    { type: 'DatePicker', label: '完成时间', name: 'identificationMaterialOrderCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'identificationMaterialOrder_file', promptText: '《标识物料下单文件》',required:false , attachmentType: 11 }
                ]
            },
            {
                type: 'Text', label: '装修环节', name: '物料到货', children: [
                    { type: 'Radio', label: '是否完成', name: 'materialArrivalStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'materialArrivalCompleteTime',required:true },
                    { type: 'File', label: '证明文件', name: 'materialArrival_file', promptText: '《物料到货文件》',required:false , attachmentType: 12 }
                ]
            },
            {
                type: 'Text', label: '装修环节', name: '竣工时间', children: [
                    { type: 'Radio', label: '是否完成', name: 'completionStatus', data: RadioOption1,required:true },
                    { type: 'DatePicker', label: '完成时间', name: 'completionTimeComplete',required:true },
                ]
            },
        ]
    },
    {
        title: '硬件配备情况', CardItem: [
            {
                type: 'Text', label: '硬件类型', name: '充电桩', children: [
                    { type: 'Radio', label: '配备情况', name: 'chargingPileStatus', data: RadioOption2,required:true },
                    { type: 'InputNumber', label: '配备数量', name: 'chargingPileNumber',required:false },
                    { type: 'DatePicker', label: '到店时间', name: 'chargingPileArrivalTime',required:false },
                ]
            },
            {
                type: 'Text', label: '硬件类型', name: '首批配件订购', children: [
                    { type: 'Radio', label: '配备情况', name: 'firstPartsOrderStatus', data: RadioOption2,required:true },
                    { type: 'DatePicker', label: '到店时间', name: 'firstPartsOrderArrivalTime',required:false },
                ]
            },
            {
                type: 'Text', label: '硬件类型', name: '通用工具配置', children: [
                    { type: 'Radio', label: '配备情况', name: 'generalToolConfigurationStatus', data: RadioOption2 ,required:true},
                    { type: 'InputNumber', label: '配备数量', name: 'generalToolConfigurationNumber',required:false },
                    { type: 'DatePicker', label: '到店时间', name: 'generalToolConfigurationArrivalTime',required:false },
                ]
            },
            {
                type: 'Text', label: '硬件类型', name: '专用工具配置', children: [
                    { type: 'Radio', label: '配备情况', name: 'specialToolConfigurationStatus', data: RadioOption2,required:true },
                    { type: 'InputNumber', label: '配备数量', name: 'specialToolConfigurationNumber',required:false },
                    { type: 'DatePicker', label: '到店时间', name: 'specialToolConfigurationArrivalTime',required:false },
                ]
            },
            {
                type: 'Text', label: '硬件类型', name: '展车', children: [
                    { type: 'Radio', label: '配备情况', name: 'demoCarStatus', data: RadioOption2,required:true },
                    { type: 'InputNumber', label: '配备数量', name: 'demoCarNumber',required:false },
                    { type: 'DatePicker', label: '到店时间', name: 'demoCarArrivalTime',required:false },
                ]
            },
            {
                type: 'Text', label: '硬件类型', name: '试驾车', children: [
                    { type: 'Radio', label: '配备情况', name: 'testDriveCarStatus', data: RadioOption2,required:true },
                    { type: 'InputNumber', label: '配备数量', name: 'testDriveCarNumber',required:false },
                    { type: 'DatePicker', label: '到店时间', name: 'testDriveCarArrivalTime',required:false },
                ]
            },
            {
                type: 'Text', label: '硬件类型', name: '代步车', children: [
                    { type: 'Radio', label: '配备情况', name: 'insteadWalkingCarStatus', data: RadioOption2,required:true },
                    { type: 'InputNumber', label: '配备数量', name: 'insteadWalkingCarNumber',required:false },
                    { type: 'DatePicker', label: '到店时间', name: 'insteadWalkingCarArrivalTime',required:false },
                ]
            },
            {
                type: 'Text', label: '硬件类型', name: '服务车', children: [
                    { type: 'Radio', label: '配备情况', name: 'serviceCarStatus', data: RadioOption2,required:true },
                    { type: 'InputNumber', label: '配备数量', name: 'serviceCarNumber',required:false },
                    { type: 'DatePicker', label: '到店时间', name: 'serviceCarArrivalTime',required:false },
                ]
            },
        ]
    },
    {
        title: '软件配备情况', CardItem: [
            {
                type: 'Text', label: '软件类型', name: '金融签约', children: [
                    { type: 'Table', label: '机构名称', name: 'financialContractTable' },
                    { type: 'Button', label: '添加机构', name: 'financialContractButton' },
                ]
            },
            {
                type: 'Text', label: '软件类型', name: '保险办理',children: [
                    { type: 'Table', label: '机构名称', name: 'insuranceDealTable' },
                    { type: 'Button', label: '添加机构', name: 'insuranceDealButton' },
                ]
            },
            {
                type: 'Text', label: '软件类型', name: '维修资质', children: [
                    { type: 'Radio', label: '配备情况', name: 'maintenanceQualificationStatus', data: RadioOption2,required:true },
                    { type: 'Select', label: '类型等级', name: 'maintenanceQualificationLevel', data: [{ name: '一级', value: 1 }, { name: '二级', value: 2 }, { name: '三级', value: 3 }],required:true },
                    { type: 'File', label: '证明文件', name: 'maintenanceQualification_file', promptText: '《维修资质扫描件》',required:false , attachmentType: 15 }
                ]
            },
            // {
            //     type: 'Text', label: '软件类型', name: '营业执照+公章', children: [
            //         { type: 'Radio', label: '配备情况', name: 'businessLicenseOfficialSealStatus', data: RadioOption2,required:true },
            //         { type: 'File', label: '证明文件', name: 'businessLicenseOfficialSeal_file', promptText: '《营业执照扫描件+公章照片》',required:false , attachmentType: 16 }
            //     ]
            // },
            {
                type: 'Text', label: '软件类型', name: 'POS机', children: [
                    { type: 'Radio', label: '配备情况', name: 'posStatus', data: RadioOption2 ,required:true},
                    { type: 'File', label: '证明文件', name: 'pos_file', promptText: '《POS机照片》', attachmentType: 17 ,required:false }
                ]
            },
            {
                type: 'Text', label: '软件类型', name: '机动车销售发票+发票章', children: [
                    { type: 'Radio', label: '配备情况', name: 'motorVehicleSalesInvoiceInvoiceSealStatus', data: RadioOption2,required:true },
                    { type: 'File', label: '证明文件', name: 'motorVehicleSalesInvoiceInvoiceSeal_file', promptText: '《机动车销售发票+发票章照片》',required:false , attachmentType: 18 }
                ]
            },
            {
                type: 'Text', label: '软件类型', name: '临牌办理', children: [
                    { type: 'Radio', label: '配备情况', name: 'temporaryLicensingStatus', data: RadioOption1,required:true },
                ]
            },
            {
                type: 'Text', label: '软件类型', name: '上牌代办', children: [
                    { type: 'Radio', label: '配备情况', name: 'commissionLicensePlateStatus', data: RadioOption1,required:true },
                ]
            },
            {
                type: 'Text', label: '软件类型', name: '低压电工证', children: [
                    { type: 'Radio', label: '配备情况', name: 'lowVoltageElectricianCertificateStatus', data: RadioOption2,required:true },
                ]
            },
            {
                type: 'Text', label: '软件类型', name: '统一服装', children: [
                    { type: 'Radio', label: '配备情况', name: 'uniformStatus', data: RadioOption2,required:true },
                ]
            },
        ]
    }

]

const Detail_projectManager = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [form] = Form.useForm()
    const [locationParmas, setLocationParmas] = useState({})
    const [ID, setID] = useState('')
    const [dataSource, setDataSource] = useState({})
    const [Type, setType] = useState('')
    const [loading, setLoading] = useState('')
    const [deleteAttachmentIds, setDeleteAttachmentIds] = useState([])
    const [tableData, setTableData] = useState([])
    const [financialContractTable, setFinancialContractTableData] = useState([])
    const [insuranceDealTable, setInsuranceDealTable] = useState([])
    const [addOrganizationVisible, setAddOrganizationVisible] = useState(false)
    const [addOrganizationTitle, setAddOrganizationTitle] = useState('false')
    const [CardSourse, setCardSourse] = useState(CardSourseArr)
    const [mechanismType, setMechanismType] = useState('')
    const [institutions, setInstitutions] = useState([])
    const [deleteInstitutionIds, setDeleteInstitutionIds] = useState([])
    const [viewAttachmentsVisible, setViewAttachmentsVisible] = useState(false)
    const [recordFileList, setRecordFileList] = useState([])
    const [certifiedList, setCertifiedList] = useState([])
    const [recordTableData, setRecordTableData] = useState({})
    const [accessSubmissionVisible,setAccessSubmissionVisible] = useState(false)
    const [previewVisible,setPreviewVisible] = useState(false)
    const [certifiedVisible,setCertifiedVisible] = useState(false)
    
    const [previewFile,setPreviewFile] = useState({})
    
    const saveFields = (values, isSubmit) => {
        console.log(values)
        let fileList = []
        let fileStatus = {}
        for (let i in values) {
            if (i.indexOf('Time') > 0 && values[i]) {
                if(i!=='submitCompleteTime'){
                    values[i] = moment(values[i]).format('YYYY-MM-DD')
                }
            }
            if (i.indexOf('_file') > 0 && values[i] && values[i].length) {
                values[i].forEach(item => {
                    if(item.status && item.status === 'uploading'){
                        fileStatus.status = true
                        fileStatus.name = item.name || item.attachmentName
                    }else{
                        if(item.attachmentName && item.attachmentUrl){
                            fileList.push({
                                attachmentName: item.attachmentName,
                                attachmentType: item.attachmentType,
                                attachmentUrl: item.attachmentUrl,
                                id: item.id ? item.id : ''
                            })
                        }
                    }
                })
                delete values[i]
            }
        }
        values.id = ID
        values.saveAttachments = fileList
        values.deleteAttachmentIds = deleteAttachmentIds
        values.institutions = institutions
        values.deleteInstitutionIds = deleteInstitutionIds
        let url = isSubmit ? allUrl.StoreManage.submitPorjectDetails : allUrl.StoreManage.savePorjectDetails
        console.log(values)
        
        if(fileStatus.status){
            Modal.confirm({
                title:'文件正在上传中，现在保存将上传失败，是否继续？',
                onOk:()=>{
                    setLoading(true)
                    post(url, { ...values }).then(res => {
                        if (res.success) {
                            message.success(res.msg)
                            if (isSubmit) {
                                setLoading(false)
                            } else {
                                // getData(locationParmas)
                            }
                            onCancel()
                        } else {
                            // message.error(res.msg)
                            setLoading(false)
                        }
                    }).catch(error => {
                        setLoading(false)
                    })
                },
                onCancel:()=>{
                    return false
                }
            })
        }else{
            setLoading(true)
            post(url, { ...values }).then(res => {
                if (res.success) {
                    message.success(res.msg)
                    if (isSubmit) {
                        setLoading(false)
                    } else {
                        // getData(locationParmas)
                    }
                    onCancel()
                } else {
                    // message.error(res.msg)
                    setLoading(false)
                }
            }).catch(error => {
                setLoading(false)
            })
        }
        
    }

    const handleSave = () => {
        form.validateFields().then(values => {
            saveFields(values, 0)
        }).catch(error => {
            // form.scrollToField(error.errorFields[0].name[0])
            // let arr = []
            // error.errorFields.forEach(item => {
            //     arr.push(item.errors[0])
            // })
            // arr.join(' , ')
            // message.error(`${arr} 不能为空！`)

            saveFields(error.values, 0)
        })
    }

    const handleSubmit = () => {
        form.validateFields().then(values => {
            setAccessSubmissionVisible(true)
        }).catch(error => {
            form.scrollToField(error.errorFields[0].name[0])
            let arr = []
            error.errorFields.forEach(item => {
                arr.push(item.errors[0])
            })
            arr.join(' , ')
            message.error(`${arr} 不能为空！`)
        })
    }
    const onCancel = () => {
        history.goBack()
    }

    const normFile = (e, attachmentType) => {
        if (Array.isArray(e)) {
            e.forEach(item => {
                item.attachmentType = attachmentType
                if (item.response) {
                    item.attachmentName = item.name
                    item.attachmentUrl = item.response.resp[0].url
                }
            })
            return e;
        }
        e.fileList.forEach(item => {
            item.attachmentType = attachmentType
            if (item.response) {
                item.attachmentName = item.name
                item.attachmentUrl = item.response.resp[0].url
            }
        })
        return e && e.fileList;
    };

    const RadioChange = (value, name) => {
        let temp = [...CardSourse]
        if (value) {
            temp.forEach(item => {
                item.CardItem.forEach(ele => {
                    ele.children.forEach(h => {
                        if (h.name === name) {
                            ele.required = true
                        }
                    })
                })
            })
        } else {
            temp.forEach(item => {
                item.CardItem.forEach(ele => {
                    ele.children.forEach(h => {
                        if (h.name === name) {
                            ele.required = false
                            /**此处暂时隐藏，当文件为必填时打开 */
                            removeFields(ele.children)
                        }
                    })
                })
            })
        }
        setCardSourse(temp)
    }

    const removeFields = (arr) =>{
        let fidlds = []
        let removeFieldsIds = [...deleteAttachmentIds]
        arr.forEach(item=>{
            if(item.type !== 'Radio'){
                if(item.required){
                    fidlds.push(item.name)
                }else{
                    if(item.type === 'DatePicker' || item.type === 'InputNumber'){
                        fidlds.push(item.name)
                    }
                }
            }
            if(item.type === 'File'){
                let files = form.getFieldValue(item.name)
                if(files){
                    files.forEach(ele=>{
                        if(ele.id){
                            removeFieldsIds.push(ele.id)
                        }
                    })
                }
            }
        })
        form.resetFields(fidlds)
        /**---start--- 当文件为必填时打开 */
        // setDeleteAttachmentIds(removeFieldsIds)
        /**---end--- */
    }

    const hanldeMechanism = (h) => {
        setAddOrganizationTitle(h.name === 'financialContractButton' ? '添加金融签约机构' : '添加保险办理机构')
        setRecordTableData()
        setMechanismType(h.name === 'financialContractButton' ? 1 : 2)
        setAddOrganizationVisible(true)
    }

    const addOrganizationCB = (data) => {
        let temp = [...institutions]
        if (data.key) {
            let target = temp.filter(item => item.key === data.key)
            if (target.length) {
                target[0].institutionName = data.institutionName
                target[0].signContractTime = data.signContractTime
                target[0].attachments = data.attachments
            }
        } else {
            temp.push(data)
        }
        temp.map((item, index) => item.key = index + 1)
        setInstitutions(temp)
    }

    const delMechanism = (row) => {
        console.log(row)
        let temp = [...institutions]
        let delTemp = [...deleteInstitutionIds]
        let target = temp.filter(item => item.key != row.key)
        console.log(target, temp)
        if (row.id) {
            delTemp.push(row.id)
            setDeleteInstitutionIds(delTemp)
        }
        target.map((item,index)=>item.key=index+1)
        setRecordFileList(target)
        setInstitutions(target)
    }

    const editMechanism = (row) => {
        console.log(row)
        setAddOrganizationTitle(row.institutionType === 1 ? '编辑金融签约机构' : '编辑保险办理机构')
        setRecordTableData(row)
        setMechanismType(row.institutionType)
        setAddOrganizationVisible(true)
    }

    const AccessSubmissionCB = (data,cb) =>{
        form.validateFields().then(values => {
            values.submitCompleteTime = moment(data.submitCompleteTime).valueOf()
            cb(true)
            saveFields(values, 1)
        }).catch(error => {
            form.scrollToField(error.errorFields[0].name[0])
            let arr = []
            error.errorFields.forEach(item => {
                arr.push(item.errors[0])
            })
            arr.join(`  ,  `)
            message.error(`${arr} 不能为空！`)
        })
    }

    const initForm = (Dt) => {
        const filList = Dt.attachments
        /**给附件赋值 */
        for (let i in filList) {
            let target = fileType.filter(item => item.type == i)
            if (target.length) {
                filList[i].forEach((item, index) => {
                    item.name = item.attachmentName
                    item.url = item.attachmentUrl
                    item.uid = index + 1
                    item.status = 'done'
                })
                // console.log(i,filList[i],target[0].name)
                form.setFieldsValue({
                    [target[0].name]: filList[i]
                })
            }
        }

        Dt.institutions.map((item, index) => item.key = index + 1)

        setInstitutions(Dt.institutions)

        /**删除无用的字段 */
        delete Dt.dealerCode
        delete Dt.attachments
        delete Dt.id
        delete Dt.managerUserId
        delete Dt.status
        delete Dt.statusName
        delete Dt.institutions

        /**给日期和其他的字段赋值 */
        for (let i in Dt) {
            if (i.indexOf('Time') > 0) {
                form.setFieldsValue({ [i]: Dt[i] ? moment(Dt[i]) : null })
            } else {
                form.setFieldsValue({ [i]: verifyTrueField(Dt[i]) ? Dt[i] : null })
            }
        }
    }

    const initRequired = (Dt) => {
        let temp = [...CardSourse]
        for (let i in Dt) {
            if (i.indexOf('Status') > -1) {
                if (Dt[i]) {
                    temp.forEach(item => {
                        item.CardItem.forEach(ele => {
                            ele.children.forEach(h => {
                                if (h.name === i) {
                                    ele.required = true
                                }
                            })
                        })
                    })
                } else {
                    temp.forEach(item => {
                        item.CardItem.forEach(ele => {
                            ele.children.forEach(h => {
                                if (h.name === i) {
                                    ele.required = false
                                }
                            })
                        })
                    })
                }
            }
        }
        setCardSourse(temp)
    }

    const getData = (params) => {
        setLoading(true)
        get(allUrl.StoreManage.getProjectDetails, { dealerCode: params.dealerCode }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setDataSource(_.cloneDeep({ Dt }).Dt)
                setID(Dt.id)
                try {
                    initForm(Dt)
                } catch {

                }
                initRequired(Dt)
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }

    const lookFiles = (data) => {
        setRecordFileList(data)
        setViewAttachmentsVisible(true)
    }

    const delMechanismFile = (row) => {
        let temp = [...deleteAttachmentIds]
        if (row.id) {
            temp.push(row.id)
            setDeleteAttachmentIds(temp)
        }
    }

    const getFileExtendingName =(filename)=> {
        // 文件扩展名匹配正则
        var reg = /\.[^\.]+$/;
        var matches = reg.exec(filename);
        if (matches) {
          return matches[0];
        }
        return '';
    }

    const onPreview = (file) =>{
        let imgSuffix = ['.png','.gif','.jpeg','.jpg','.svg','.bmp']
        let extend = getFileExtendingName(file.attachmentName)
        if(imgSuffix.indexOf(extend)>-1){
            setPreviewFile(file)
            setPreviewVisible(true)
        }else{
            window.open(file.attachmentUrl)
        }
    }

    const uploadProps = {
        name: 'file',
        action: baseURL.Host + allUrl.StoreManage.uploadFile,
        data: { code: 'storePorject' },
        headers: {
            appid: 1,
            authorization: Cookies.get('scrm_token')
        },
        onChange(info) {
            if (info.file.status !== 'uploading') {
                // console.log(info.file, info.fileList);
            }
            if (info.file.status === 'done') {
                message.success(`${info.file.name} 上传成功！`);
            } else if (info.file.status === 'error') {
                message.error(`${info.file.name} 上传失败！`);
            }
        },
        onRemove(info) {
            console.log(info)
            let temp = [...deleteAttachmentIds]
            if (!info.id) return
            // let obj = {
            //     id: info.id,
            //     attachmentType: info.attachmentType,
            //     attachmentName: info.attachmentName,
            //     attachmentUrl: info.attachmentUrl
            // }
            temp.push(info.id)
            setDeleteAttachmentIds(temp)
        },
        onPreview:(file)=>onPreview(file)
    }

    const columns = [
        {
            title: <div style={{textAlign:'center'}}>模块</div>, dataIndex: 'modelCodeName', render: (text, record, index) => {
                const obj = {
                    children: <div style={{ textAlign: 'center', fontWeight: 'bold' }}>{text}</div>,
                    props: {
                        rowSpan:0
                    },
                };
                if(index ===0){
                    obj.props.rowSpan = 8
                }else if(index===8){
                    obj.props.rowSpan = 19
                }
                else if(index===27){
                    obj.props.rowSpan = 5
                }
                else if(index===32){
                    obj.props.rowSpan = 3
                }
                else if(index===35){
                    obj.props.rowSpan = 2
                }
                if(index ===37) {
                    obj.props.colSpan = 2
                    obj.props.rowSpan = 1
                }
                return obj
            },
        },
        {title: <div style={{textAlign:'center'}}>岗位名称</div>, dataIndex: 'positionName', render: (text,record,index) => {
            const obj = {
                children:<div style={{ textAlign: 'center' }}>{text || '-'}</div>,
                props:{}
            }
            if (index === tableData.length -1) {
                obj.props.colSpan = 0
            }
            return obj
        }},
        {
            title: <div>培训认证通过情况（通过数/在职人数）&nbsp;
            <Tooltip title='点击单元格数字可以查看人员信息'>
            <QuestionCircleOutlined />
            </Tooltip></div>,
            dataIndex: 'configurationRules', children: [
                { title: <div style={{textAlign:'center'}}>L1</div>, dataIndex: 'l1Number', 
                render: text => <div style={{ textAlign: 'center' }}>{text || '-'}</div>,
                onCell: (record) => ({
                    onClick:() => cellHandleClick(record, 'userIds'),
                }),
                },
                { title: <div style={{textAlign:'center'}}>L2</div>, dataIndex: 'l2Number', render: text => <div style={{ textAlign: 'center' }}>{text || '-'}</div>,
                onCell: (record) => ({
                    onClick:() => cellHandleClick(record, 'userIds'),
                })},
                { title: <div style={{textAlign:'center'}}>L3</div>, dataIndex: 'l3Number', render: text => <div style={{ textAlign: 'center' }}>{text || '-'}</div>,
                onCell: (record) => ({
                    onClick:() => cellHandleClick(record, 'userIds'),
                })},

            ]
        },
    ]
    // 培训认证通过信息查看
    const getCertifiedUserInfo = (userIds) => {
        if(!userIds || userIds.length === 0) {
            message.warn('暂无数据')
            return false
        }
        post(allUrl.StoreManage.getCertifiedUserInfo, { userIds }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setCertifiedList(Dt)
            } else {
                // message.error(res.msg)
            }
        })
    }
    useEffect(() => {
        certifiedList.length && setCertifiedVisible(true)
    }, [certifiedList]);
    const cellHandleClick = ((record, str) => {
        getCertifiedUserInfo(record[str])
    })
    const formColumns = [
        { title: '机构名称', dataIndex: 'institutionName', render: text => <PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '签约时间', dataIndex: 'signContractTime', width: 98, render: text => text ? <PublicTooltip title={moment(text).format('YYYY.MM.DD')}>{moment(text).format('YYYY.MM.DD')}</PublicTooltip> :'' },
        { title: '证明文件', dataIndex: 'attachments', width: 76, render: (text, record) => record.attachments&& record.attachments.length ? <div style={{ textAlign: 'center' }}><a onClick={() => lookFiles(record)}>查看</a></div> :'' },
        {
            title: '操作', dataIndex: 'active', width: 50, fixed: 'right', render: (text, record) => {
                return Type === 'EDIT' ? <>
                    <a style={{ marginRight: '5px' }} title='编辑' onClick={() => editMechanism(record)}><FormOutlined /></a>
                    <Popconfirm title="确定删除这条数据吗？" onConfirm={() => delMechanism(record)}>
                        <a title='删除' style={{ color: 'red' }}><DeleteOutlined /></a>
                    </Popconfirm>
                </> : null
            }
        },
    ]

    if (Type === 'LookAt') {
        formColumns.pop()
    }

    useEffect(() => {
        const params = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}
        get(allUrl.StoreManage.getStoreUserAndLevel, { dealerCode: params.dealerCode }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt.list.sort((a, b) => a.modelCode - b.modelCode)
                let lastData = { modelCodeName:'合计', l1Number: Dt.l1Total, l2Number: Dt.l2Total, l3Number: Dt.l3Total }
                Dt.list.push(lastData)
                Dt.list.map((item, index) => item.key = index + 1)
                setTableData(Dt.list)
            } else {
                // message.error(res.msg)
            }
        })
    }, [])
    
    useEffect(() => {
        const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}
        setLocationParmas(locationParmas)
        setType(locationParmas.Type)
        if (locationParmas.dealerCode && userInfo) {
            getData(locationParmas)
        }
    }, [userInfo])
    const columns2 = [
        { title: '序号', dataIndex: 'index',render:(text,record,index)=>index+1},
        { title: '用户名', dataIndex: 'userName',},
        { title: '手机号', dataIndex: 'mobilePhone', },
        { title: '岗位', dataIndex: 'positionName',},
        { title: '当前认证等级', dataIndex: 'currentLevel',},

    ]
    return <div className='detail'>
        <div className='detail_warp'>
            <div className='detail_title'>{locationParmas.title}</div>
            <div className='detail_con'>
                <Form form={form}>
                    <Collapse>
                        {
                            CardSourse.map((item, index) => {
                                return <Panel header={`${index + 1}. ${item.title}`} key={index}>
                                    {
                                        item.CardItem && item.CardItem.length ?
                                            item.CardItem.map((d, i) => {
                                                return <Card key={i}>
                                                    {
                                                        d.type === 'Text' ? <Item label={d.label} key={i}>
                                                            <span style={{ fontWeight: 600 }}>{d.name}</span>
                                                        </Item> : null

                                                    }
                                                    {
                                                        d.children && d.children.length ?
                                                            d.children.map((h, i) => {
                                                                const { type, label, name, data, promptText, attachmentType, required } = h
                                                                if (type === 'Radio') {
                                                                    return <Item label={label} name={name} key={i} rules={[{ required: true, message: `${d.name}` }]}>
                                                                        {
                                                                            Type === 'EDIT' ?
                                                                                <Radio.Group onChange={e => RadioChange(e.target.value, name)}>
                                                                                    {
                                                                                        data && data.length ?
                                                                                            data.map((g, i) => {
                                                                                                return <Radio key={i} value={g.value}>{g.name}</Radio>
                                                                                            }) : null
                                                                                    }
                                                                                </Radio.Group>
                                                                                : data.filter(item => item.value == dataSource[name])[0]?.name || '-'
                                                                        }
                                                                    </Item>
                                                                } else if (type === 'DatePicker') {
                                                                    return <Item label={label} name={name} key={i} rules={[{ required: d.required ? required : false, message: `${d.name}-${label}` }]}>
                                                                        {
                                                                            Type === 'EDIT' ?
                                                                                <DatePicker placeholder='选择时间' style={{ width: '88%' }} format="YYYY-MM-DD" disabled={!d.required} />
                                                                                : dataSource[name] ? moment(dataSource[name]).format('YYYY-MM-DD') : '-'
                                                                        }
                                                                    </Item>
                                                                } else if (type === 'InputNumber') {
                                                                    return <Item label={label} name={name} key={i} rules={[{ required: d.required ? required : false, message: `${d.name}-${label}` }]}>
                                                                        {
                                                                            Type === 'EDIT' ?
                                                                                <InputNumber placeholder='请输入' style={{ width: '88%' }} disabled={!d.required} />
                                                                                : dataSource[name] || '-'
                                                                        }
                                                                    </Item>
                                                                } else if (type === 'Input') {
                                                                    return <Item label={label} name={name} key={i} rules={[{ required: d.required ? required : false, message: `${d.name}-${label}` }]}>
                                                                        {
                                                                            Type === 'EDIT' ?
                                                                                <Input placeholder='请输入' style={{ width: '88%' }} disabled={!d.required} />
                                                                                : dataSource[name] || '-'
                                                                        }
                                                                    </Item>
                                                                } else if (type === 'Select') {
                                                                    return <Item label={label} name={name} key={i} rules={[{ required: d.required ? required : false, message: `${d.name}-${label}` }]}>
                                                                        {
                                                                            Type === 'EDIT' ?
                                                                                <Select placeholder='请选择' style={{ width: '88%' }} disabled={!d.required}>
                                                                                    {
                                                                                        data && data.length ?
                                                                                            data.map((item, index) => {
                                                                                                return <Option key={index} value={item.value}>{item.name}</Option>
                                                                                            }) : null
                                                                                    }
                                                                                </Select>
                                                                                : data.filter(item => item.value == dataSource[name])[0]?.name || '-'
                                                                        }
                                                                    </Item>
                                                                } else if (type === 'File') {
                                                                    return <Item
                                                                        name={name}
                                                                        label={label}
                                                                        key={i}
                                                                        valuePropName="fileList"
                                                                        getValueFromEvent={e => normFile(e, attachmentType)}
                                                                        className='file'
                                                                        // labelCol={{ span: d.required ? 6 : 5 }}
                                                                        labelCol={d.required ? {}:{ span: 5 }}
                                                                        wrapperCol={{ span: d.required ? 18 : 19 }}
                                                                        rules={[{ required: d.required ? required : false, message: `${d.name}-${label}` }]}
                                                                    >
                                                                        {
                                                                            Type === 'EDIT' ?
                                                                            <Upload {...uploadProps} disabled={Type === 'LookAt'}>
                                                                                <Button style={{ border: '1px solid #1890ff', color: '#1890ff', width: 78, padding: 0 }}>添加附件</Button>
                                                                                <span className='promptText' onClick={e=>e.stopPropagation()}>请上传{promptText}</span>
                                                                            </Upload>
                                                                            :<div style={{marginTop:'4px',height:'105px',overflowY:'auto'}}>
                                                                                {
                                                                                  dataSource.attachments && dataSource.attachments[attachmentType] !== undefined ?
                                                                                  dataSource.attachments[attachmentType].map((item,index)=>{
                                                                                      return <p key={index} onClick={()=>onPreview(item)} style={{color:'#1890ff',whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',cursor: 'pointer'}}>{index+1}、{item.attachmentName}</p>
                                                                                  }):'-'
                                                                                }
                                                                            </div>
                                                                        }
                                                                    </Item>
                                                                } else if (type === 'Button') {
                                                                    let Style = { height: '36px', lineHeight: '36px', color: '#1890FF', textAlign: 'center', border: 'dashed 1px #1890FF', cursor: 'pointer', marginTop: '10px' }
                                                                    return Type === 'EDIT' ?
                                                                        <div key={i} style={Style} onClick={() => hanldeMechanism(h)}>
                                                                            <PlusOutlined />
                                                                            {label}
                                                                        </div>
                                                                        : null
                                                                } else if (type === 'Table') {
                                                                    let mechanismType = name === 'financialContractTable' ? 1 : 2
                                                                    let dataSource = institutions.filter(item => item.institutionType == mechanismType)
                                                                    return <Table
                                                                        key={i}
                                                                        columns={formColumns}
                                                                        dataSource={dataSource}
                                                                        pagination={false}
                                                                        size='small'
                                                                        rowKey='key'
                                                                        bordered
                                                                        scroll={{ y: Type === 'EDIT' ? 110 : 150 }}
                                                                    />
                                                                }
                                                            }) : null
                                                    }
                                                </Card>
                                            })
                                            : null
                                    }
                                </Panel>
                            })
                        }
                        <Panel key={999} header={'5. 人员培训情况'}>
                            <div style={{ padding: '24px',background:'white', width: '100%' }}>
                                <Table columns={columns} rowKey='key' dataSource={tableData} bordered pagination={false} size='small' />
                            </div>
                        </Panel>
                    </Collapse>
                </Form>
            </div>
        </div>
        <div className='detail_btns'>
            {
                Type === 'LookAt' ? null :
                    <>
                    {
                        dataSource.status == 0 ?
                        <Button type="primary" onClick={handleSubmit} style={{ marginRight: '10px' }}>提交</Button> :null
                    }
                        <Button type="primary" onClick={handleSave} style={{ marginRight: '10px' }}>保存</Button>
                    </>
            }
            <Button onClick={onCancel}>取消</Button>
        </div>
        {
            addOrganizationVisible &&
            <AddOrganization visible={addOrganizationVisible} title={addOrganizationTitle} type={mechanismType} recordTableData={recordTableData} onCancel={() => setAddOrganizationVisible(false)} delMechanismFile={delMechanismFile} cb={addOrganizationCB} />
        }
        {
            viewAttachmentsVisible &&
            <ViewAttachments visible={viewAttachmentsVisible} data={recordFileList} onCancel={() => setViewAttachmentsVisible(false)} />
        }
        {
            accessSubmissionVisible && 
            <AccessSubmission recordNodes={dataSource} visible={accessSubmissionVisible} cb={AccessSubmissionCB} onCancel={()=>setAccessSubmissionVisible(false)} />
        }
        {
            previewVisible && 
            <PreviewImg data={previewFile} visible={previewVisible} onCancel={()=>setPreviewVisible(false)}/>
        }
        {
            certifiedVisible && 
            <Modal width={800} visible={certifiedVisible} onOk={()=>setCertifiedVisible(false)} onCancel={()=>setCertifiedVisible(false)}>
                <Table  width={800} columns={columns2} dataSource={certifiedList} pagination></Table>
            </Modal>
        }
    </div>
}
export default Detail_projectManager
