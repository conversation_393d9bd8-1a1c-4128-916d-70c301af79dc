import React, { useState } from 'react'
import './index.less'
import { RightOutlined,DownOutlined } from '@ant-design/icons';


export const Panel = ({ children, ...reset }) => {
    const [isOpen, setIsOpen] = useState(true)
    const [conStyle,setConStyle] = useState({})
    const extraChick = () => {
        if(isOpen){
            setConStyle({display: 'none'})
        }else{
            setConStyle({display: 'block'})
        }
        setIsOpen(!isOpen)
    }
    const { header } = reset
    return <div className='custom-Panel'>
        <div className='custom-Panel-header'>
            <div className='custom-Panel-header-text'>
                {header}
            </div>
            <span className='custom-Panel-header-extra' onClick={extraChick}>
                {
                    isOpen?
                    <>
                    <span>折叠</span>
                    <span className='icon'>
                        <DownOutlined />
                    </span>
                </>
                    :<>
                        <span>展开</span>
                        <span className='icon'>
                        <RightOutlined />
                        </span>
                    </>
                }
            </span>
        </div>
        <div className='custom-Panel-content-warp' style={{...conStyle}}>
            <div className='custom-Panel-content'>
                {children}
            </div>
        </div>
    </div>
}

export default ({ children, ...reset }) => {
    return <div className='custom-Collapse'>
        {children}
    </div>
}