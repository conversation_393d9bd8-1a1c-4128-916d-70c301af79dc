.import-container {
    background-color: #f0f2f5;
    padding-top: 24px;

    .import-flow {
        background: #EEF8FA;
        border-radius: 2px;
        border: 1px solid #61CBE0;
        margin: 24px 24px 0 24px;
        padding: 20px 0 34px 22px;

        .title {
            margin-bottom: 22px;
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            line-height: 28px;

        }

        .number {
            font-size: 16px;
            font-family: HelveticaNeue-Bold, HelveticaNeue;
            font-weight: bold;
            color: #FFFFFF;
            height: 36px;
            display: flex;
            flex-direction: row;

            .number-item {
                display: flex;
                flex-direction: row;
                align-items: center;

                .number-common {
                    width: 36px;
                    height: 36px;
                    line-height: 36px;
                    border-radius: 36px;
                    text-align: center;
                }

                .number-text {
                    font-size: 16px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: rgba(0, 0, 0, 0.85);
                    line-height: 36px;
                    margin-left: 8px;
                }

                .number-string {
                    display: inline-block;
                    width: 70px;
                    height: 1px;
                    background: #AFBECC;
                    margin-left: 8px;
                    margin-right: 8px;
                }

                .one {
                    background: #05C8CA;
                }

                .two {
                    background: #7872F9;
                }

                .three {
                    background: #E8A507;
                }

                .four {
                    background: #75CC02;
                }

                .five {
                    background: #09AFE4;
                }
            }
        }
    }

    .import-excel {
        height: 120px;
        background-color: #fff;
        margin: 24px 24px 0 24px;
        padding: 20px 0 0 32px;

        .top {
            .name {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.85);
                line-height: 32px;
            }

            .download {
                display: inline-block;
                width: 134px;
                height: 32px;
                line-height: 32px;
                text-align: center;
                background: #FFFFFF;
                border: 1px solid #1890FF;
            }

            .top-btn {
                display: inline-block;
                width: 134px;
                height: 32px;
                background: #FFFFFF;
                border: 1px solid #1890FF;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #1890FF;
                line-height: 22px;
                margin-left: 8px;
            }
        }

        .bottom {
            margin-top: 16px;

            .name {
                display: inline-block;
                width: 126px;
                text-align: end;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
            }

            .bottom-btn {
                display: inline-block;
                width: 134px;
                height: 32px;
                background: #1890FF;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #FFFFFF;
                line-height: 22px;
                margin-left: 8px;
            }
        }

        .upload-btn[disabled] {
            color: #fff;
            border-color: #1890ff;
            background: #1890ff;
        }

        .red-text {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #F5222D;
            line-height: 32px;
            margin-left: 20px;
        }
    }

    .import-search {
        background-color: #fff;
        margin: 24px 24px 0 24px;

        .help-text {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #F5222D;
            line-height: 22px;
        }
    }

    .import-list {
        margin: 24px 24px 0 24px;
        background-color: #fff;
        padding: 32px 32px 0 32px;

        .list-top {
            margin-bottom: 20px;

            .name {
                font-size: 20px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
                line-height: 28px;
            }

            .red-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #F5222D;
                line-height: 22px;
                margin-left: 20px
            }
        }

        .success-dot {
            display: inline-block;
            border-radius: 8px;
            width: 8px;
            height: 8px;
            background: #52C41A;
            margin-right: 7px;
        }

        .fail-dot {
            display: inline-block;
            border-radius: 8px;
            width: 8px;
            height: 8px;
            background: #F5222D;
            margin-right: 7px;
        }
    }
}

.PublicList_FormQuery {
    padding-top: 30px;
    margin-left: -20px;

    .ant-col-7 {
        .ant-form-item {
            .ant-form-item-control-input {
                width: 90%;

                .ant-form-item-control-input-content {
                    .ant-picker {
                        width: 100%;
                    }
                }
            }
        }
    }

    .FormQuerySubmit {
        display: flex;
        justify-content: flex-end;

        .operationButtons {
            span {
                color: #1890ff;
                cursor: pointer;

                .anticon {
                    margin-left: 6px;
                }
            }
        }
    }
}