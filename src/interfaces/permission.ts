export interface FullUri {
  uri: string;
  method: string;
}

export type Uri = string | FullUri;

export type PermissionType = 'MENU' | 'OPERATE';

export interface UserPermission {
  /**
   * 权限标识
   */
  code: string;
  /**
   * 权限名称
   */
  name?: string;
  /**
   * 权限值
   */
  value?: string;
  /**
   * 父权限标识
   */
  parent?: string;
  /**
   * 权限类型
   */
  type?: PermissionType;
  /**
   * 权限描述
   */
  description?: string;
  /**
   * 权限分组
   */
  groups?: string[];
  /**
   * 扩展字段
   */
  data?: any;
  /**
   * 对应uri集合
   */
  uris?: Uri[];
}
