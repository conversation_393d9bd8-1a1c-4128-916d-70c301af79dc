import React,{useState,useRef,useEffect} from 'react'
import {message, Modal,Form,Row,Col,Input,Button,Spin,Checkbox} from 'antd'
import PublicTable from '@/components/Public/PublicTable'
import PublicTooltip from '@/components/Public/PublicTooltip'
import allUrl from '@/utils/url'
import {post,get} from '@/utils/request'

const AllocationModal = (props) =>{
    const {visible,onCancel,tableSelectedRows,AllocationType,getData,recordData} = props
    // AllocationType=1为分配，等于2时为批量
    const tableRef = useRef()
    const [form] = Form.useForm()
    const [checkedOption, setCheckedOption] = useState([])
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [dealerCodeList,setDealerCodeList] =useState([])
    const [loading, setLoading] = useState(false)
    const [defaultQuery, setDefaultQuery] = useState(AllocationType===1 ? {
        provinceName:recordData.province,
        cityName:recordData.city
    }:{
        provinceName:tableSelectedRows[0].province,
        cityName:tableSelectedRows[0].city,
    })
    const handleOk = () =>{
        if(!dealerCodeList.length){
            message.error('请先选择一条数据！')
        }else{
            let clueIds=[]
            tableSelectedRows.map((item,index)=>{
                clueIds.push(item.id)
            })
            let param = AllocationType===1 ? {ids:[recordData.id],storeList:dealerCodeList} :{ids:clueIds,storeList:dealerCodeList}
            post(allUrl.ClueManagement.distributionList ,{...param}).then(res => {
                if (res.success) {
                    message.success(res.msg)
                    onCancel()
                    getData()
                } else {
                    message.error(res.msg)
                }
            })
        }
    }
    const onChange =(checkedValue)=>{
        setDealerCodeList(checkedValue)
    }
    const getStoreInfo = ()=>{
        setLoading(true)
         get(allUrl.ClueManagement.getCityDealer ,{...defaultQuery}).then(res => {
            if (res.success) {
                setLoading(false)
                setCheckedOption(res.resp)
            } else {
                message.error(res.msg)
            }
        })
    }
    const hanldeSubmit = () =>{
        form.validateFields().then(values=>{
            let queryParams = {
                ...defaultQuery,
                ...values
            }
            setDefaultQuery(queryParams)
        })
    }
    const handleReset = () =>{
        form.resetFields()
        setDefaultQuery(AllocationType===1 ? {
            provinceName:recordData.province,
            cityName:recordData.city
        }:{
            provinceName:tableSelectedRows[0].province,
            cityName:tableSelectedRows[0].city,
        })
    }
    useEffect(()=>{
        getStoreInfo()
    },[defaultQuery])

    return<Modal visible={visible} width={800} title='线索分配' onOk={handleOk} onCancel={onCancel} maskClosable={false}>
            <Form form={form}>
                <Row>
                    <Col span={14}>
                      
                    </Col>
                    <Col span={7}>
                        <Form.Item label='快速搜索' name='name'>
                            <Input placeholder='请输入...' allowClear onPressEnter={hanldeSubmit}/>
                        </Form.Item>
                    </Col>
                    <Col span={3} style={{textAlign:'right'}}>
                        {/* <Button style={{marginRight:20}} onClick={handleReset}>重置</Button> */}
                        <Button type='primary' onClick={hanldeSubmit}>查询</Button>
                    </Col>
                </Row>
                <Spin spinning={loading}>
                {/* 由于改为多对一或者一对一所以添加逻辑value={dealerCodeList[0] */}
                <Checkbox.Group  onChange={onChange} style={{width:'100%'}} value={dealerCodeList[0]} >
                    <Row>
                    {
                        checkedOption && checkedOption.map((item,index)=>{
                            return <Col span={8} key={index}><Checkbox value={item.dealerCode}>{item.name}</Checkbox></Col>
                        })
                    }
                    </Row>
                </Checkbox.Group>
                </Spin>
                
            </Form>
    </Modal>
}
export default AllocationModal