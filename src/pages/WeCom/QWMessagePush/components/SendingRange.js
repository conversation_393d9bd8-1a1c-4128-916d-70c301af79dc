import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react'
import { Modal, Row, Col, Tabs, Table, Tooltip, Divider, message, Tree, Checkbox, Button } from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import _ from 'lodash'
import BatchImportModal from "./BatchImportModal";
import { CloseOutlined } from '@ant-design/icons';
import UploadFile from "@/components/Public/UploadFile";
import baseURL from "@/baseURL";
import './SendingRange.less'
const { TabPane } = Tabs;
const SendingRange = forwardRef((props, ref) => {
    const { onCancel, visible,Type,cb,targetList,formData, orgNames } = props
    const [loading, setLoading] = useState(false)
    const [pageSize2, setPageSize2] = useState(10)
    const [pageSize3, setPageSize3] = useState(10)
    const [current2, setCurrent2] = useState(1)
    const [current3, setCurrent3] = useState(1)
    const [total2, setTotal2] = useState(0)
    const [total3, setTotal3] = useState(0)
    const [OrgtreeData, setOrgtreeData] = useState([])
    const [userTreeData, setUserTreeData] = useState([]) // 已选择对象列表
    const [dataSource2, setDataSource2] = useState([])
    const [dataSource3, setDataSource3] = useState([])
    const [defaultDataSource3, setDefaultDataSource3] = useState([])
    const [tabsKey, setTabsKey] = useState('1')
    const [defaultQuery, setDefaultQuery] = useState({
        pageNum: 1,
        pageSize: 10,
        positions: [],
        organizationId: ''
    })
    const [selectedRowKeys1, setSelectedRowKeys1] = useState([])
    const [selectedRowKeys2, setSelectedRowKeys2] = useState([])
    const [selectedRowKeys3, setSelectedRowKeys3] = useState([])
    const [selectedRows1, setSelectedRows1] = useState([])
    const [selectedRows2, setSelectedRows2] = useState([])
    const [selectedRows3, setSelectedRows3] = useState([])
    const [orgtreeselectedKeys, setOrgtreeselectedKeys] = useState([])
    const [orgtreeSelectedRows, setOrgtreeSelectedRows] = useState([])
    const [allRows, setAllRows] = useState([])
    const [isOrgAll, setIsOrgAll] = useState(false)
    const [OrgTreeDataFlatten, setOrgTreeDataFlatten] = useState([])
    const [entryList, setEntryList] = useState({})
    const [treeOrgData, setTreeOrgData] = useState([]);
    const [checkALLUsers, setCheckAllUsers] = useState([]);
    const [orgId, setOrgId] = useState(undefined);
    const [isChecked, setIsChecked]= useState(false)
    const [importModalVisible, setImportModalVisible] = useState(false);
    const [importData, setImportData] = useState([]);
    const [errorImportData, setErrorImportData] = useState([]);
    const [successImportData, setSuccessImportData] = useState([]);
    const [exportLoading, setExportLoading] = useState(false);
    const [importLoading, setImportLoading] = useState(false)
    const [useImportOrg,setUseImportOrg] = useState(false)
    const queryRef = useRef(null)
  const [tableVersion, setTableVersion] = useState(false)
  useImperativeHandle(ref, () => ({
    resetData: () => {
        setUserTreeData([])
        setSelectedRows3([])
        setSelectedRowKeys3([])
    }
  }));
  const handleOk = (props) => {
        // 讲选中的row传给父组件，以便多组织账号的组织名回传
        cb(selectedRows3,formData.qwAgentMessageId)
        onCancel()
    }
    const PageChange2 = (current, pageSize) => {
        setCurrent2(current)
        setPageSize2(pageSize)
    }
    const PageChange3 = (current, pageSize) => {
        setCurrent3(current)
        setPageSize3(pageSize)
        setDefaultQuery({
            ...defaultQuery,
            pageNum: current,
            pageSize: pageSize
        })
    }
    const importSuccessColumns = [
        {
          title: '序号',
          render: (text, record, index) => `${index + 1}`,
        },
        {
          title: "门店",
          dataIndex: "dealerCode",
          key: "dealerCode",
        },
        {
          title: "门店简称",
          dataIndex: "dealerName",
        },
      ];
      const importErrorColumns = [
        {
          title: '序号',
          render: (text, record, index) => `${index + 1}`,
        },
        {
          title: "门店编号",
          dataIndex: "dealerCode",
          key: "dealerCode",
        },
        {
          title: "门店简称",
          dataIndex: "dealerName",
        },
        {
          title: "失败原因",
          dataIndex: "failReason",
        },
    ];
    // 账号标签页，数据集
    const InforData3 = {
        rowKey: record => record.userOneId,
        bordered: true,
        dataSource: dataSource3,
        loading,
        // scroll: { x: 'max-content',y:tableHeight },
        scroll: { x: 'max-content', y: 470 },
        columns: [
            { title: '工号', dataIndex: 'userOneId' },
            { title: '姓名', dataIndex: 'userName' },
            { title: '手机号', dataIndex: 'mobilePhone' },
            { title: '岗位', dataIndex: 'positionName' },
            { title: '所属组织', dataIndex: 'orgName' },
        ],
        size: 'middle',
        pagination: {
            pageSize: pageSize3,
            onChange: PageChange3,
            current: current3,
            total: total3,
            showTotal: () => `共${total3}条，${pageSize3}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange3,
        },
        rowSelection: {
            // selections: [
            //     // Table.SELECTION_ALL,
            //     // Table.SELECTION_INVERT,
            //     // Table.SELECTION_NONE
            // ],
            columnWidth: 60,
            selectedRowKeys: selectedRowKeys3,
            // defaultSelectedRowKeys:selectedRowKeys3,
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys, selectedRows, info) => {
                console.log('selectedRowKeys', selectedRowKeys3, selectedRowKeys)
                console.log('selectedRows', selectedRows3, selectedRows)
                // 在重置或者切换搜索条件的时候，需要记录之前已经勾选过的数据
                let arr0 = selectedRows3.concat(selectedRows)
                arr0 = arr0.filter(i => (i !== undefined) && i.userOneId !== undefined )
                console.log('arr0', arr0)
                let userArr = []
                // 过滤取消的账号
                defaultDataSource3.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userOneId)!==-1)userArr.push(item)
                })
                dataSource3.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userOneId)!==-1)userArr.push(item)
                })
                arr0.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userOneId)!==-1)userArr.push(item)
                })

            userArr = unique([...new Set(userArr)],'userOneId')
            console.log('userArr', userArr)
                console.log('selectedRowKeys', selectedRowKeys)
                if(userArr.length) {
                    filterTreeData(userArr)
                    setSelectedRows3(userArr)
                    setSelectedRowKeys3(selectedRowKeys)
                } else {
                    // 没有选中用户时清零
                    setUserTreeData([])
                    setSelectedRows3([])
                    setSelectedRowKeys3([])
                }

            },
            getCheckboxProps:()=>{
                return{disabled:isOrgAll?true:false}
            },
            onSelectAll:(selected, selectedRows, changeRows) => {
                // console.log('全选参数', selected, selectedRows, changeRows)
            }
        },
    };
    // 添加筛选条件后，勾选账号时会出现重复数据，需要给对象数组去重
    const unique = (arr, key) => {
        if (!arr) return arr
        if (key === undefined) return [...new Set(arr)]
        const map = {
            'string': e => e[key],
            'function': e => key(e),
        }
        const fn = map[typeof key]
        const obj = arr.reduce((o,e) => (o[fn(e)]=e, o), {})
        return Object.values(obj)
    }
    const TabsChange = (key) => {
        setDefaultQuery({})
        setTabsKey(key)
    }

    // 删除操作
    const PersonnelDel5 = (row) => {
        let version = tableVersion
        // 树结构删除对应内容
        let rows = [...selectedRows3]
        let keys = [...selectedRowKeys3]
        console.log('账号删除', row, rows, userTreeData)
        console.log('selectedRows3', selectedRows3)
        console.log('selectedRowKeys3', selectedRowKeys3)
        // 已选择对象按用户删除
        if(row.userOneId) {
            rows = rows.filter(item => item.userOneId !== row.userOneId)
            keys = keys.filter(item => item !== row.userOneId)
            for (let i = 0; i < userTreeData.length; i++) {
                console.log('i', i)

                userTreeData[i].children.map((j,index2) => {
                    if(row.userOneId === j.userOneId) {
                        console.log('j', j)
                        userTreeData[i].children.splice(index2, 1)
                    }
                    // 组织人数减量更新
                    userTreeData[i].title = `${userTreeData[i].key}(${userTreeData[i].children.length})`
                    if(userTreeData[i].children.length === 0) {
                        userTreeData.splice(i, 1)
                        i = i - 1;
                    }
                })
                console.log('j.children', userTreeData)


            }
        } else {
            // 已选择对象按组织删除
            let epetitionRows = [] // 多组织用户id
            for(let i = 0; i < userTreeData.length; i++){
                if(row.key === userTreeData[i].key) {
                    userTreeData[i].children.map((j)=> {
                        const repetitionId = rows.filter(item => item.userOneId === j.userOneId)
                        console.log('repetitionId', repetitionId)
                        if(repetitionId.length) {
                            epetitionRows = epetitionRows.concat(repetitionId)
                        }
                        rows = rows.filter(item => item.userOneId !== j.userOneId)
                        keys = keys.filter(item => item !== j.userOneId)
                    })
                    userTreeData.splice(i, 1)
                    i-- // 解决splice导致原数组index改变bug问题

                }

            }
            for(let j = 0; j < userTreeData.length; j++){
                epetitionRows.map((m)=> {
                    userTreeData[j].children = userTreeData[j].children.filter(n => m.userOneId !== n.userOneId)
                    console.log('epetitionRows2', m, userTreeData[j].children)
                })
                userTreeData[j].title = `${userTreeData[j].key}(${userTreeData[j].children.length})`
                // 如果组织内没有了用户，删除该组织
                if(userTreeData[j].children.length === 0) {
                    userTreeData.splice(j, 1)
                    j--
                }
            }
        }
        console.log('last', rows, keys)
        setSelectedRows3(rows)
        setSelectedRowKeys3(keys)
        setUserTreeData([...userTreeData])
        setTableVersion(version+1)
    }

    const treeDatafilter = (arr) => {
        let newArr = _.cloneDeep({ obj: arr }).obj
        let res = []  // 用于存储递归结果（扁平数据）
        // 递归函数
        const fn = (source) => {
            source.forEach(el => {
                res.push(el)
                if(el.children && el.children.length > 0){
                    fn(el.children)
                }
            })
        }
        fn(newArr)
        return res
    }
    const onSearch = (values) => {
        if (values.userOneId === undefined && values.userName === undefined) {
            deleteImportData()
        }
        const obj = {...values, pageNum: 1, pageSize: 10}
        setIsChecked(false)
        setDefaultQuery(obj)
    }
    // 组织搜索
    const searchOrg = _.debounce((value) => {
        if (value) {
        setOrgId(value);
        GetOrgData(value);
        }
    }, 500);
    // 获取组织列表
    const GetOrgData = (name = "") => {
        post(allUrl.Authority.messageSendOrgList, { name }).then((res) => {
        if (res.success) {
            const arr = res.resp.slice(0);
            setTreeOrgData(arr);
        } else {
            message.error(res.msg);
        }
        });
    };
    // 选择组织id
    const selectOrg = (value) => {
        setOrgId(value);
    };
    // 全选请求数据
    const getAllUser = () => {
        setLoading(true)
        const params = {...defaultQuery}
        delete params.pageNum
        delete params.pageSize
        if(useImportOrg) {
            let arr = []
            successImportData.map((i) => {
                arr.push(i.dealerCode)
            })
            params.dealerCodes = arr
            delete params.organizationId
        } else {
            params.organizationId = defaultQuery.organizationId
            delete params.dealerCodes
        }

        post(allUrl.MessageManage.getAlluser, { ...params }).then(res => {
            if (res && res.success) {
                const list = res.resp[0]
                // 手机号脱敏
                list.forEach(function(item){
                    if(item && item.mobilePhone){
                        item.mobilePhone=item.mobilePhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                    }
                })
                setCheckAllUsers(list)
                setTotal3(list.length)
                setDataSource3(list)
                if(!defaultQuery.userName || defaultQuery.userName === ''){
                    setDefaultDataSource3(list)
                }
                let ids = list.map(item => item.userOneId)
                ids = unique((ids.concat(selectedRowKeys3)))
                const rows = unique((list.concat(selectedRows3)), 'userOneId')
                console.log('ids', ids)
                setSelectedRowKeys3(ids)
                setSelectedRows3(rows)
                console.log('key和row', ids, rows)
                filterTreeData(list, userTreeData)
            }
            setLoading(false)
        })
    }
    /**
     * 设置右侧已选择用户展示数据
     * @param {Array} userList
     * @param {Array} historyData
     * @returns
     */
    const filterTreeData = (userList, historyData) => {
        if(userList.length === 0 ) {
            setUserTreeData([])
            setSelectedRowKeys3([])
            setSelectedRows3([])
            return
        }

        // 确保 userList 中的用户数据是唯一的
        userList = unique(userList, 'userId')

        // 处理组织名称列表
        let orgArrs = []
        userList.forEach(item => {
            // 处理多组织的情况，将逗号分隔的组织名称拆分成数组
            const orgs = item.orgName ? item.orgName.split(',') : []
            orgs.forEach(org => {
                if(org && !orgArrs.includes(org)) {
                    orgArrs.push(org)
                }
            })
        })

        const newUserTreeData = []
        // 设置组织
        orgArrs.forEach(item => {
            newUserTreeData.push({
                title: item,
                key: item,
                children: []
            })
        })

        // 设置组织下的用户
        userList.forEach(user => {
            // 处理多组织的情况
            const userOrgs = user.orgName ? user.orgName.split(',') : []
            userOrgs.forEach(orgName => {
                newUserTreeData.forEach(org => {
                    // 修改判断逻辑，使用完全匹配而不是包含关系
                    if(orgName === org.key) {
                        // 检查该用户是否已经在该组织下
                        const isUserExist = org.children.some(child => child.userId === user.userId)
                        if(!isUserExist) {
                            org.children.push({
                                title: `${user.userName || user.targetName}`,
                                positionName: user.positionName,
                                key: user.userId ? `${org.key}_${user.userId}` : `${org.key}_${user.targetId}`,
                                userId: user.userId ? user.userId : user.targetId,
                                qwId: user.qywxUserId ? user.qywxUserId : user.qwId,
                            })
                            // 更新组织下的用户个数
                            org.title = `${org.key}(${org.children.length})`
                        }
                    }
                })
            })
        })

        console.log('最后的已选择对象数据：', newUserTreeData)
        setUserTreeData(newUserTreeData)
        if(historyData && historyData.length) {
            let finallyData = []
            console.log('历史已选择对象数据', historyData)
            historyData.forEach((m, index3) => {
               newUserTreeData.forEach((n, index4) => {
                   if(m.key === n.key) {
                       let children = (m.children.concat(n.children))
                       children = unique(children, 'userOneId')
                       console.log('children', children)
                    historyData[index3].children =children
                    historyData[index3].title =  `${m.key}(${children.length})`
                    newUserTreeData.splice(index4, 1)
                   }
               })
           })

           console.log('newUserTreeData', newUserTreeData)
           finallyData = historyData.concat(newUserTreeData)
           console.log('finallyData', finallyData.length)
           setUserTreeData(finallyData)
        }

    }
    // 点击全选按钮
    const handleSelectAll = (e) => {
        setIsChecked(e.target.checked)
        if(e.target.checked) {
            getAllUser()
        } else {
            setSelectedRowKeys3([])
            setSelectedRows3([])
            setUserTreeData([])
        }
    }
    const dealerUpload = ({ file, fileList }, type) => {
        const { response } = file;
        if (file.status === "uploading") {
          setImportLoading(true);
        }
        if (file.status === "done") {
          if (response.success) {
            let key = 2;
            message.loading({ content: "正在导入中，请稍后", key });
            setTimeout(() => {
              message.success({ content: "导入完成!", key, duration: 2 });
            }, 1000);
            console.log("导入返回", response);
            setSuccessImportData(response.resp[0].data);
            setErrorImportData(response.resp[0].errorData);
            setImportModalVisible(true);
          } else {
            message.error(response.msg);
          }
          setImportLoading(false);
        }
        if (file.status === "error") {
          message.error(response.msg || response.msgCode);
          setImportLoading(false);
        }
    }
    const downloadDealerImportTemplate = () => {
        get(
            allUrl.MessageManage.dealerExportTemplate,
            {},
            {
              responseType: "blob",
            }
          ).then((res) => {
            if (res) {
              setExportLoading(false);
              let blob = new Blob([res], { type: "application/vnd.ms-excel" });
              console.log("blob", blob);
              if (window.navigator.msSaveOrOpenBlob) {
                //兼容ie
                window.navigator.msSaveBlob(blob, "批量导入门店模板.xlsx");
              } else {
                let downloadElement = document.createElement("a");
                let href = window.URL.createObjectURL(blob); //创建下载的链接
                downloadElement.href = href;
                downloadElement.download = "批量导入门店模板" + ".xlsx"; //下载后文件名
                document.body.appendChild(downloadElement);
                downloadElement.click(); //点击下载
                document.body.removeChild(downloadElement); //下载完成移除元素
                window.URL.revokeObjectURL(href); //释放掉blob对象
              }
              message.success("导出成功！");
            } else {
              // message.error(res.msg)
            }
          });
    }
  const importCB = (data) => {
    console.log("导入门店组织回调", data);
    setImportData(data)
    setTreeOrgData([{code: '001', title: '导入门店组织', parentId: '001', key: '001'}])
    queryRef.current.setFieldValue('organizationId', '001')
    data.length > 0  ? setUseImportOrg(true) : setUseImportOrg(false)
  };
  const deleteImportData = () => {
    GetOrgData()
    queryRef.current.setFieldValue('organizationId', '')
    setUseImportOrg(false)
    setImportData([])
  }
    // 切换tab和默认查询参数
    useEffect(() => {
        setLoading(true)
        if (tabsKey === '3') {
            post(allUrl.Authority.messageSendOrgList, { ...defaultQuery }).then(res => {
                if (res && res.success) {
                    let Dt = res.resp
                    setOrgtreeData(Dt)
                    setOrgTreeDataFlatten(treeDatafilter(Dt))
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
        if (tabsKey === '2') {
            get(allUrl.common.entryLists, { codes: 'scrm_position' }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    for (let i in Dt) {
                        Dt[i].forEach(item => {
                            item.name = item.entryMeaning
                            item.value = item.entryValue
                            item.entryID = item.id
                            delete item.id
                        })
                    }
                    setDataSource2(Dt['scrm_position'])
                    setTotal2(Dt['scrm_position'].length)
                } else {
                    // message.error(res.message)
                }
                setLoading(false)
            })
        }
        if (tabsKey === '1') {
            const params = JSON.parse(JSON.stringify(defaultQuery))
            if(useImportOrg) {
                    let arr = []
                successImportData.map((i) => {
                    arr.push(i.dealerCode)
                })
                params.dealerCodes = arr
                delete params.organizationId
            } else {
                params.organizationId = defaultQuery.organizationId
                delete params.dealerCodes
            }
            console.log('useImportOrg', params,defaultQuery)
            post(allUrl.MessageManage.getUser, {...params} ).then(res => {
                if (res && res.success) {
                    // 手机号脱敏
                    res.resp[0].list.forEach(function(item){
                        if(item && item.mobilePhone){
                            item.mobilePhone=item.mobilePhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                        }
                    })
                    setTotal3(res.resp[0].total)

                    setDataSource3(res.resp[0].list)
                    if(!defaultQuery.userName || defaultQuery.userName === ''){
                        setDefaultDataSource3(res.resp[0].list)
                    }
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }, [defaultQuery, tabsKey])
    useEffect(() => {
       if(selectedRowKeys3.length > 0 && total3 == selectedRowKeys3.length) {
            setIsChecked(true)
        }
    }, [total3]);
    // 初始化
    useEffect(() => {
        // 获取岗位接口数据
        get(allUrl.common.entryLists, { codes: 'scrm_position' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setEntryList(Dt || {})
            } else {
                // message.error(res.message)
            }
        })
        // 获取组织树列表
        GetOrgData();
    }, [])

    // 父组件用户数据targetList
    useEffect(()=>{
        // 设置组织
        if(targetList.length) {
            filterTreeData(targetList)
            let newRows = []
            targetList.forEach((m,index3) => {
                newRows.push({

                    positionName: m.positionName,
                    position: m.position,
                    type: m.type,
                    qwUserId: m.qwUserId,
                    orgName: m.orgName,
                    orgId: m.orgId,
                    userId: m.userId,
                    name: m.name,
                    userName: m.name,
                    userOneId: m.userOneId
                })
            })
            console.log('targetList effect: ', targetList)
            // 在重置或者切换搜索条件的时候，需要记录之前已经勾选过的数据
            const userIdArr = unique(targetList.map((item) => item.userOneId))
            setSelectedRows3( unique([...new Set(newRows)]) )
            setSelectedRowKeys3(userIdArr)
        } else {
            // 没有选中用户时清零
            setUserTreeData([])
            setSelectedRows3([])
            setSelectedRowKeys3([])
        }

    },[targetList])

    // 全选按钮变动
    useEffect(() => {
        if(selectedRowKeys3.length < total3) {
            setIsChecked(false)
        }
        if(total3 > 0 && selectedRowKeys3.length == total3) {
            setIsChecked(true)
        }

    }, [selectedRowKeys3]);
    // 筛选条件项
    let searchList = [
        { label: '工号', name: 'userOneId', type: 'Input', placeholder: '请输入工号',labelAlign:'right',labelCol:{span: 9},colSpan: 6 },
        { label: '姓名', name: 'userName', type: 'Input', placeholder: '请输入姓名',labelAlign:'right',labelCol:{span: 9},colSpan: 6 },
        { label: '岗位', name: 'positions', type: 'Select',mode: "multiple", initialValue: [], placeholder: '请选择',labelAlign:'right', colSpan: 12, labelCol:{span: 4},wrapperCol:{span: 16}, data: entryList['scrm_position'] || [] },
        { label: '组织',
        name: 'organizationId',
        type: 'TreeSelect',
        placeholder: '请选择',
        colSpan: 12,
        labelCol:{span: 3},
        wrapperCol: {span: 20},
        data: treeOrgData || [],
        onSearch:searchOrg,
        onChange:selectOrg,
        fieldNames:{
            label: 'title', value: 'key', children: 'children'
        },
        disabled: useImportOrg
        },
        // { label: '是否主营店', name: 'isMainStore', type: 'Select', placeholder: '请选择', labelCol:{span: 10}, colSpan: 6,
        //   data: [
        //     { name:'是', value: 1 },
        //     { name:'否', value: 0 },
        //   ]
        // },
        { label: '营业状态', name: 'businessStatus', type: 'Select', placeholder: '请选择', labelCol:{span: 10}, colSpan: 6,
        data: [
            { name: '营业中', value: '899030000' },
            { name: '停业', value: '899030001' },
            { name: '搬迁', value: '899030002' },
            { name: '改建', value: '899030003' },
            { name: '在建', value: '102910000' },
            { name: '拟退网', value: '102910001' },
        ]
      },
    ]
    const renderNode = (item) => {
        return <div className="content_Item">

            <Tooltip title={item.positionName ? `${item.title}@${item.positionName}` : item.title}><div className="name">{item.title}{item.positionName ? <span style={{color: '#1890FF'}}>@{item.positionName}</span> : ''}</div></Tooltip>
            <div className="del">
                <img src={require('../../../../assets/img/trash.png')} onClick={() => PersonnelDel5(item)} />
            </div>
        </div>
    }
    return <Modal wrapClassName='SendingRange' title='选择发消息的范围' visible={visible} onCancel={onCancel} onOk={handleOk} width={1400} maskClosable={false}>
        <Row gutter={[12]}>
            <Col span={16} className='SendingRangeLeft'>
                {/* <Checkbox style={{position:'absolute',top:'14px',right:'0'}} checked={isOrgAll} indeterminate={isIndeterminate} onClick={(e) => OrgCleckAll(e)}>全选</Checkbox> */}
                {/* <Tabs defaultActiveKey="1" onChange={TabsChange} active={tabsKey}> */}
                    {/* <TabPane tab="账号" key="1"> */}
                        <PublicTableQuery ref={queryRef} tabsKey={tabsKey} onSearch={onSearch} isFormDown={true} searchList={searchList} defaultQuery={defaultQuery} />
                        <div style={{display:'flex', justifyContent: 'space-between', alignItems: 'baseline'}}>
                        <Checkbox className="checkALL" onChange={handleSelectAll} checked={isChecked}>全选</Checkbox>
                        <div style={{ marginTop: 10 }}>
                        {
                          // roleJudgment(userInfo, 'STAFF_USER_INTERVIEW_IMPORT_RESULT') ?
                          <>
                            {
                               importData.length ? <p style={{marginRight: 20}}><span style={{marginRight: 10}}>导入门店组织{importData.length}个 </span><CloseOutlined onClick={() => deleteImportData()} /></p> : null
                            }
                            <UploadFile
                              // method={'get'}
                              style={{ display: "inline-block" }}
                              extension={["xls", "xlsx"]}
                              showUploadList={false}
                              size={10}
                              // headers={{'Content-Type': "multipart/form-data"}}
                              action={baseURL.Host + allUrl.MessageManage.dealerImportFile}
                              UploadChange={dealerUpload}
                            >
                              <Button type="primary" loading={importLoading}>导入门店组织</Button>
                            </UploadFile>
                            <a
                              style={{ fontSize: "14px", margin: "6px 6px 0 10px" }}
                              onClick={downloadDealerImportTemplate}
                            >
                              模版下载
                            </a>
                          </>
                          // :null
                        }
                        </div>
                        </div>
                        <Table key={tableVersion} {...InforData3} />
                    {/* </TabPane> */}
                {/* </Tabs> */}
            </Col>
            <Col span={8} className='SendingRangeRight'>
                <div className='title'>已选择对象<span className='selectedSum'>（{ selectedRows3.length }）</span></div>
                <div className='contentBox'>
                    {/* 树形结构 */}
                    {
                        <Tree
                            titleRender={renderNode}
                            // onSelect={onSelect}
                            // onCheck={onCheck}
                            treeData={userTreeData}
                        >
                        </Tree>
                    }
                </div>
            </Col>
        </Row>
        {importModalVisible && (
        <BatchImportModal
          title="批量导入门店"
          visible={importModalVisible}
          onCancel={() => setImportModalVisible(false)}
          successImportData={successImportData}
          errorImportData={errorImportData}
          importErrorColumns={importErrorColumns}
          importSuccessColumns={importSuccessColumns}
          importDataUrl={allUrl.MessageManage.dealerImportFile}
          exportErrorDataUrl={allUrl.MessageManage.dealerExportFromData}
          cb={importCB}
        />
      )}
    </Modal>
})
export default SendingRange
