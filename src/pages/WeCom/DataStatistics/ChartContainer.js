import React from 'react'
import { Card, Col, Spin } from 'antd'
import './ChartContainer.less'

const ChartContainer = props => {
    const { children, themeColor, title, subtitle, extended, width, loading ,statisticalTime ,ColSpan} = props
    return <Col span={24} xxl={ColSpan ? ColSpan : 12} >
        <Card className='chartContainer-card' style={{ width: width || 'auto', height:'auto', minHeight: 540 }}>
                <div className='chartContainer-card-header'>
                    <div className='title'>
                        <div className='title_warp'>
                            <div style={{ background: themeColor }} className='theme'></div>
                            {title}
                        </div>
                        {extended?<div className='extended'>{extended}</div>:null}
                    </div>
                    <div className='subtitle'>
                        {subtitle}
                        <div className='subtitle-statisticalTime'>{statisticalTime}</div>
                    </div>
                </div>
                <div className='chartContainer-card-content'>
                    <Spin spinning={loading?false:true}>
                    {children}
                    </Spin>
                </div>
        </Card>
    </Col>
}
export default ChartContainer