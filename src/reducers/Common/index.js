import {GET_SIDER_TYPE,USERINFO,COMMON_TABADD,GET_MENULIST,CHANGE_PWD_STATUS,LIST_EXPAND, CHANGE_SHOWREDDOT, STAFF_LIST_EXPAND} from '../../constants'
const defaultState = {
    SiderType:false ,
    TabList:[] ,
    MenuList:[],
    userInfo:null,
    changePwdVisible:false,
    ListExpand:[],
    staffListExpand: [],
    showRedDot: false
}
const Common = (state = {...defaultState }, { type, payload }) => {
    switch (type) {
        case GET_SIDER_TYPE:return {...state,SiderType:payload};    //sider展开折叠状态
        case USERINFO : return { ...state, userInfo: payload?payload:null }; //获取当前登陆人信息
        case COMMON_TABADD :return {...state,TabList: payload}  //获取tab页list
        case GET_MENULIST :return {...state,MenuList:payload}   //获取用户菜单
        case CHANGE_PWD_STATUS :return {...state,changePwdVisible:payload}   //修改密码弹出
        case LIST_EXPAND: return{...state,ListExpand:payload}   //扩展列状态保存
        case STAFF_LIST_EXPAND: return{...state,staffListExpand:payload} //门店员工列表扩展列状态保存
        case CHANGE_SHOWREDDOT: return { ...state, showRedDot: payload} //是否展示小红点状态保存
        default : return  state
    }
};

export default Common