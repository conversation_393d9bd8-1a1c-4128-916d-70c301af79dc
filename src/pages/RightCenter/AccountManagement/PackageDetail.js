import React, { useState, memo, useMemo, useEffect, useRef, useContext } from 'react';
import { useSelector } from 'react-redux';
import { Tabs, Descriptions, Image, Tag } from 'antd';
import { post, get, put } from '../../../utils/request';
import BasePayTable from './basePayTable/Index';
import HuaWeiTable from './huaWeiTable/Index';
import LoadTipsModal from './loadTipsModal/Index';
import moment from 'moment';
import { InjectMessage } from '@/pages/RightCenter/AccountManagement/Detail';
import styles from '../antd.module.less';
const { TabPane } = Tabs;
const display_MAP = {
  0: '展示',
  1: '不展示',
  null: '-',
};
const status_MAP = {
  1: '草稿',
  2: '已生效',
  3: '未生效',
  4: '已失效',
  5: '已下架',
};
const packEffect_MAP = {
  1: '未生效',
  2: '已生效',
  4: '已失效',
  null: '-',
};

const spanCss = {
  color: '#F5222D',
  marginLeft: 10,
};

const partTips = '(仅适用于包内基础权益，包内延保权益优先执行延保策略)';
const allTips = '(适用于所有权益业务场景)';

const PackageDetail = props => {
  const { detail, callbackFn, price } = props;
  const [logImgVisible, setLogImgVisible] = useState({});
  const [isLoadTipsModalOpen, setIsLoadTipsModalOpen] = useState(false); //下载弹窗提示
  const { type } = useContext(InjectMessage);

  const outputFile = data => {
    return data.map((item, i) => {
      const imgSuffixArr = ['png', 'jpg', 'jpeg', 'bmp', 'webp'];
      const fileSuffix = item.fileName.split('.').pop();
      if (imgSuffixArr.includes(fileSuffix)) {
        return (
          <span key={i} className="file-name" onClick={() => setLogImgVisible({ [item.fileName]: true })}>
            {item.fileName}

            <Image
              src={item.url}
              preview={{
                visible: logImgVisible[item.fileName] ?? false,
                src: item.url,
                onVisibleChange: value => {
                  setLogImgVisible({ [item.fileName]: value });
                },
              }}
            />
          </span>
        );
      }
      if (fileSuffix === 'pdf') {
        return (
          <span className="file-name" key={i}>
            <a href={item.url} target="_blank">
              {item.fileName}
            </a>
          </span>
        );
      }
      return (
        <span className="file-name" key={i} onClick={() => setIsLoadTipsModalOpen(true)}>
          <a href={item.url}>{item.fileName}</a>
        </span>
      );
    });
  };

  const renderPackEffectStrategy = () => {
    const packEffectStrategyObj = {
      //历史数据 -1
      //根据根据明细策略生效
      0: () => (
        <>
          {/* <Tag>根据根据明细策略生效</Tag> */}
          {detail.haveExtendedWarranty && <span style={spanCss}>{allTips}</span>}
        </>
      ),
      //相对时间
      1: () => (
        <>
          {/* <Tag>相对时间</Tag> */}
          <span>
            {detail.packRelativeTimeStr}-
            {detail.packRelativeTimeDate ? moment(detail.packRelativeTimeDate).format('YYYY年MM月DD日 HH:mm:ss') : '-'}
          </span>
          {detail.haveExtendedWarranty && <span style={spanCss}>{partTips}</span>}
        </>
      ),
      //固定时间
      2: () => (
        <>
          {/* <Tag>固定时间</Tag> */}
          <span>
            开始时间：
            {detail.packFixedStartTimeDate
              ? moment(detail.packFixedStartTimeDate).format('YYYY年MM月DD日 HH:mm:ss')
              : '-'}
            &nbsp; &nbsp; 结束时间：
            {detail.packFixedEndTimeDate ? moment(detail.packFixedEndTimeDate).format('YYYY年MM月DD日 HH:mm:ss') : '-'}
          </span>
          {detail.haveExtendedWarranty && <span style={spanCss}>{partTips}</span>}
        </>
      ),
    };

    return (
      <>
        <Tag className={styles['custom-ant-tag-light-yellow']}>{detail.packEffectStrategyStr}</Tag>
        {detail.packEffectStrategy !== '-1' && packEffectStrategyObj[detail.packEffectStrategy]?.()}
      </>
    );
  };

  return (
    <>
      <div className="detailPanel content-div" style={{ marginBottom: 20 }}>
        {/* <Descriptions title="">
                    <Descriptions.Item label='权益包编码'>{detail.packNo || '-'}</Descriptions.Item>
                    <Descriptions.Item label='权益包名称'>{detail.name || '-'}</Descriptions.Item>
                    <Descriptions.Item label='权益包属性'>{detail.attributeName || '-'}</Descriptions.Item>

                    <Descriptions.Item label='权益车型'>{detail.carTypeName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='权益包车型配置'>{detail.carConfigName ? `${detail.carOfYear}/${detail.powerModelName}/${detail.carConfigName}` : '-'}</Descriptions.Item>
                    <Descriptions.Item label='车辆市场类型' span={1}>{detail.carMarketTypeName || '-'}</Descriptions.Item>

                    <Descriptions.Item label='权益包类型'>{detail.typeName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='权益包销售价格'>{detail.price || '-'}</Descriptions.Item>
                    <Descriptions.Item label='权益包成本价格'>{detail.costPrice || '-'}</Descriptions.Item>

                    <Descriptions.Item label='车辆销售类型'>{detail.packSalesTypeName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='是否在APP上展示'>{display_MAP[detail.display] || '-'}</Descriptions.Item>
                    <Descriptions.Item label='活动名额'>{detail.promotionQuota || '-'}</Descriptions.Item>

                    <Descriptions.Item label='剩余活动名额'>{'-'}</Descriptions.Item>
                    <Descriptions.Item label='活动时间'>{detail.promotionStartTime ? `${moment(detail.promotionStartTime).format('YYYY年MM月DD日 HH:mm:ss')}-${moment(detail.promotionEndTime).format('YYYY年MM月DD日 HH:mm:ss')}` : '-'}</Descriptions.Item>
                    <Descriptions.Item label='活动状态'>{detail.promotionStatusName || '-'}</Descriptions.Item>

                    <Descriptions.Item label='宣传时间' span={1}>
                        {
                            detail.activityBeginTime && detail.activityEndTime ?
                                <span>
                                    {moment(detail.activityBeginTime).format('YYYY年MM月DD日 HH:mm:ss')}~{moment(detail.activityEndTime).format('YYYY年MM月DD日 HH:mm:ss')}
                                </span>
                                : '-'
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label='宣传状态' span={2}>{status_MAP[detail.status] || '-'}</Descriptions.Item>

                    <Descriptions.Item label='权益包描述' span={3}>{detail.description || '-'}</Descriptions.Item>

                    <Descriptions.Item label='权益支持区域' span={3}><span className='area-box'>{detail.allAreaName || '-'}</span></Descriptions.Item>
                    <Descriptions.Item label='权益支持门店' span={3}><span className='area-box'>{detail.allDealerName || '-'}</span></Descriptions.Item>
                </Descriptions> */}

        <Descriptions>
          <Descriptions.Item label="权益包编码">{detail.packNo || '-'}</Descriptions.Item>
          <Descriptions.Item label="权益包名称">{detail.name || '-'}</Descriptions.Item>
          <Descriptions.Item label="是否在APP上展示">{display_MAP[detail.display] || '-'}</Descriptions.Item>
        </Descriptions>
        <Descriptions>
          <Descriptions.Item label="宣传时间">
            {moment(detail.activityBeginTime).format('YYYY年MM月DD日 HH:mm:ss')} ~{' '}
            {moment(detail.activityEndTime).format('YYYY年MM月DD日 HH:mm:ss')}
          </Descriptions.Item>
          {price ? (
            <>
              <Descriptions.Item label="权益包销售价格">{detail.price || 0}元</Descriptions.Item>
              <Descriptions.Item label="权益包成本价格">{detail.costPrice || 0}元</Descriptions.Item>
            </>
          ) : (
            ''
          )}
          {/* {price ? <Descriptions.Item label='权益包价格'>{detail.price || '0'}元</Descriptions.Item> : ''} */}
        </Descriptions>
        {type === 'driver' ? (
          <Descriptions>
            {/*  <Descriptions.Item label='生效策略'>{detail.description || '-'}</Descriptions.Item> */}
            <Descriptions.Item label="权益包生效状态">{packEffect_MAP[detail.packEffect] || '-'}</Descriptions.Item>
          </Descriptions>
        ) : null}
        {type !== 'base' && type !== 'huawei' ? (
          <Descriptions>
            {/*  <Descriptions.Item label='生效策略'>{detail.description || '-'}</Descriptions.Item> */}
            <Descriptions.Item label="生效策略">{renderPackEffectStrategy()}</Descriptions.Item>
          </Descriptions>
        ) : null}

        <Descriptions>
          {detail.accountAttachmentList && detail.accountAttachmentList.length > 0 ? (
            <Descriptions.Item label="附件">
              {/* <span className='file-box'><img style={{ width: '16px', height: '16px' }} src={require('@/assets/img/filename-icon.png')} alt="" /><span className='file-name'>{detail.accountAttachmentList[0].fileName}</span></span> */}
              <div>
                {outputFile(detail.accountAttachmentList)}
                {/* {detail.accountAttachmentList.map(item => {
                                    // return <div className='file-box'><img style={{ width: '16px', height: '16px' }} src={require('@/assets/img/filename-icon.png')} alt="" /><a href={item.url} target="blank" className='file-name'>{item.fileName}</a></div>
                                    return <span className='file-name' onClick={() => setIsLoadTipsModalOpen(true)}><a href={item.url} target="blank">{item.fileName}</a></span>
                                })} */}
              </div>
            </Descriptions.Item>
          ) : null}
        </Descriptions>
        <Descriptions>
          <Descriptions.Item label="权益描述">{detail.description || '-'}</Descriptions.Item>
        </Descriptions>
      </div>
      <>
        {type === 'huawei' ? (
          <div style={{ paddingTop: 10, paddingBottom: 20, backgroundColor: '#F1F8FF' }}>
            <div className="title">权益明细信息-内外一致</div>
            <HuaWeiTable dataSource={detail.detailList} />
          </div>
        ) : (
          <>
            <div style={{ paddingTop: 10, paddingBottom: 20, backgroundColor: '#F1F8FF' }}>
              <div className="title">
                权益明细信息-{detail.sortMapExternal && detail.sortMapExternal.length ? '对内核销策略' : '内外一致'}
              </div>
              <Tabs onChange={callbackFn} type="card">
                {detail.sortMap &&
                  detail.sortMap.length &&
                  detail.sortMap.map(ele => (
                    <TabPane tab={ele.name} key={ele.name}>
                      <div className="content-div content-div-nopad">
                        <BasePayTable
                          dataSource={ele.content}
                          strategy={detail.sortMapExternal && detail.sortMapExternal.length ? 'in' : 'same'}
                          packType={detail.type}
                        />
                      </div>
                    </TabPane>
                  ))}
              </Tabs>
            </div>
            {detail.sortMapExternal && detail.sortMapExternal.length ? (
              <div style={{ paddingTop: 10, paddingBottom: 20, backgroundColor: '#F1F8FF' }}>
                <div className="title">权益明细信息-对外展示内容</div>
                <Tabs onChange={callbackFn} type="card">
                  {detail.sortMapExternal.map(ele => (
                    <TabPane tab={ele.name} key={ele.name}>
                      <div className="content-div content-div-nopad">
                        <BasePayTable dataSource={ele.content} strategy={'out'} packType={detail.type} />
                      </div>
                    </TabPane>
                  ))}
                </Tabs>
              </div>
            ) : null}
          </>
        )}
      </>

      {isLoadTipsModalOpen && (
        <LoadTipsModal isLoadTipsModalOpen={isLoadTipsModalOpen} setIsLoadTipsModalOpen={setIsLoadTipsModalOpen} />
      )}
    </>
  );
};
export default memo(PackageDetail);
