
.custom-Collapse{
    width: 100%;
    background-color: #F0F2F5;
    .custom-Panel{
        margin-bottom: 14px;
        background-color: #F0F2F5;
        .custom-Panel-header{
            height: 50px;
            // line-height: 50px;
            background: #d6ebff;
            padding: 0 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .custom-Panel-header-text{
                font-size: 16px;
                font-family: PingFangSC, PingFangSC-Medium;
                // font-weight: 500;
                color: rgba(0,0,0,0.85);
            }
            .custom-Panel-header-extra{
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Regular;
                // font-weight: 400;
                // text-align: left;
                color: #1890ff;
                cursor: pointer;
                -moz-user-select: none;
                -webkit-user-select: none;
                -ms-user-select: none;
                .icon{
                    margin-left: 4px;
                }
            }
        }
        .custom-Panel-content-warp{
            background-color: #F0F2F5;
            .custom-Panel-content{
                // padding-bottom: 14px;
                padding: 14px 0 14px 0;

            }
        }
    }
}