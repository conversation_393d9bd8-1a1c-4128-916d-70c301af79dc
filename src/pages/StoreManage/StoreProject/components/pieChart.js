import React,{useEffect,useState} from 'react'
import {message} from 'antd'
import './legend.less'
import ReactEcharts from "echarts-for-react";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import {get} from '@/utils/request'
import allUrl from '@/utils/url';


const PieChart = (props) => {
    const colors = ["#5470C6", "#EE6666", "#91CC75",'#FAC858','#DA65B4' ,"#42BEFF", "#2BB4B6","#FFAA67","#CBCBCB","#6B6B6B"];

    const [leftPieArr,setLeftPieArr] = useState([])
    const [rightPieArr,setRightPieArr] = useState([])
    const getOption = () => {
        return  {
            grid: {
                left: 0,
                containLabel: true,
            },
            tooltip: {
              trigger: 'item',
              formatter:function(params){
                return `${params.seriesName}<br />${params.marker} ${params.name}   ${params.value}%`
              }
            },
            legend: {
              left: 'center',
              top:10,
              icon: "roundRect",
              itemWidth:8,
              itemHeight:8,
              textStyle:{
                fontSize:13
              },
            },
            color:colors,
            series: [
              // {
              //   name:'门店类型',
              //   type: 'pie',
              //   radius: '50%',
              //   center: ['33%', '56%'],
              //   label: {
              //       fontSize: 12,
              //       color:'rgba(0,0,0,0.65)',
              //   },
              //   labelLine: {
              //       show: true,
              //       length: 8,
              //       length2: 10,
              //       minTurnAngle:60
              //   },
              //   emphasis:{
              //       disabled:true
              //   },
              //   data: leftPieArr ,
              // },
              // {
              //   name:'门店类型',
              //   type: 'pie',
              //   radius: '50%',
              //   label: {
              //     position: 'inner',
              //     fontSize: 12,
              //     color:'#fff',
              //     formatter:function (params){
              //       return params.value + '%'
              //       }
              //   },
              //   center: ['33%', '56%'],
              //   labelLine: {
              //     show: false
              //   },
              //   data: leftPieArr
              // },


              {
                name:'营业状态',
                type: 'pie',
                radius: '50%',
                center: ['50%', '56%'],
                label: {
                    fontSize: 12,
                    color:'rgba(0,0,0,0.65)'
                },
                // labelLine: {
                //     show: true,
                //     length: 8,
                //     length2: 10,
                //     minTurnAngle:60
                // },
                emphasis:{
                    disabled:true
                },
                data: rightPieArr,
              },
              {
                name:'营业状态',
                type: 'pie',
                radius: '50%',
                center: ['50%', '56%'],
                label: {
                  position: 'inner',
                  fontSize: 11,
                  color:'#fff',
                  formatter:function (params){
                    return params.value + '%'
                }
                },
                labelLine: {
                  show: false
                },
                
                data:rightPieArr
              },
            ]
          };
    }

    useEffect(()=>{
      get(allUrl.StoreManage.getPieChart).then(res=>{
          if(res.success){
              let Dt = res.resp[0]
              let bigChart = Dt.bigChart.map(item=>{
                return {
                  name:item.valueName,
                  value:item.value
                }
              })
              let smallChart = Dt.smallChart.map(item=>{
                return {
                  name:item.valueName,
                  value:item.value
                }
              })
              setLeftPieArr(bigChart)
              setRightPieArr(smallChart)
          }else{
              // message.error(res.msg)
          }
      })
  },[])

    return (
        <div className='legend' style={{ borderLeft: 'solid 12px #f0f2f5' }}>
            {/* <div className='legend_title'>
                项目管理数据统计
            </div> */}
            <div className='legend_wrapper' style={{position: 'relative'}}>
                <ReactEcharts
                    style={{ minHeight: "400px" }}
                    option={getOption()}
                />
                {/* <div className='legend_Mask'>
                  <hr className='legend_Mask_Line1' />
                  <hr className='legend_Mask_Line2' />
                </div> */}
                <div className='legend_name'>金康入网用户中心营业状态分布图</div>
                <div className='legend_secondLevelName'>注：除“拟退网/停业”之外的用户中心均纳入项目管理</div>
            </div>
        </div>
    )
}
export default PieChart
