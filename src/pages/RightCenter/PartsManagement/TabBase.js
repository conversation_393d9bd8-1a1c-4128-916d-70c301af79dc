import React, { useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import { message, But<PERSON>, Tabs } from 'antd'
import { get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { roleJudgment } from '@/utils/authority'
import moment from 'moment'
import PartsDetail from './PartsDetail'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import './index.less'
import styles from '../antd.module.less'
import tableModuleStyles from '../tableLayout.module.less'

const TabBase = (props) => {
    const { DictCarData, PART_TYPE_OPTIONS } = props
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const [tableHeight, setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({})
    const [DictData, setDictData] = useState([])
    const [isModalVisibleDetail, setIsModalVisibleDetail] = useState(false);
    const [detailInfoSource, setDetailInfoSource] = useState([])
    const ref = useRef()
    const onSearch = (values) => {
        if (values.updateTime) {
            values.updateTime = moment(values.updateTime).format('YYYY-MM-DD HH:mm:ss')
        }
        if (values.syncTime) {
            values.syncTime = moment(values.syncTime).format('YYYY-MM-DD HH:mm:ss')
        }
        setDefaultQuery(values)
    }

    //查看详情
    const LookAt = (record) => {
        setIsModalVisibleDetail(true)
        let params = {
            id: record.id
        }
        get(allUrl.PartsManagement.getPartInfoDetail, { ...params }).then(res => {
            if (res.success) {
                res.resp.map((item, index) => item.key = index + 1)
                setDetailInfoSource(res.resp[0])
            } else {
                // message.error(res.msg)
            }
            // setLoading(false)
        })

    }
    const handleCancel = () => {
        setIsModalVisibleDetail(false)
    };

    const initPage = (flag) => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if (!flag) {
            h = winH - 47 - 54 - 345
        } else {
            h = winH - 47 - 54 - 400
        }
        setTableHeight(h)
    }

    useEffect(() => {
        initPage();
    }, [])

    const searchList = [
        { label: '配件编码', name: 'partNo', type: 'Input', placeholder: '请输入配件编码', colSpan: 6 },
        {
            label: '车型', name: 'carType', type: 'Select', placeholder: '请选择', colSpan: 6, data: DictCarData || []
        },
        { label: '配件名称', name: 'partName', type: 'Input', placeholder: '请输入配件名称', colSpan: 6 },
        { label: '配件包修类型', name: 'partType', type: 'Select', colSpan: 6, data: PART_TYPE_OPTIONS },
        // { label: '配件包修所属', name: 'mobilePhone', type: 'Select', colSpan: 6, data: [{ name: '原车件', value: '1' }, { name: '配件', value: '0' }] },
        { label: '生效状态', name: 'partStatus', type: 'Select', colSpan: 6, placeholder: '请选择', data: [{ name: '生效', value: '0' }, { name: '失效', value: '1' }] },
        // { label: '失效时间', name: 'vin', type: 'DatePicker', colSpan: 6 },
        { label: '更新时间', name: 'updateTime', type: 'DatePicker', colSpan: 6, placeholder: '请选择更新时间' },
        { label: '同步时间', name: 'syncTime', type: 'DatePicker', colSpan: 6, placeholder: '请选择同步时间' },
    ];

    const columns = [
        { title: '序号', dataIndex: 'index', width: 70, fixed: 'left', render: (text, record, index) => (ref?.current?.current - 1) * ref?.current?.pageSize + index + 1 },

        {
            title: '车型', dataIndex: 'carType', width: 120, fixed: 'left', render: (text) => text || '-'
        },
        {
            title: '配件名称', dataIndex: 'partName', width: 230, fixed: 'left', render: (text) => text || '-'
        },
        {
            title: '配件编码', dataIndex: 'partNo', width: 180, fixed: 'left', render: (text) => text || '-'
        },
        {
            title: '配件包修类型', dataIndex: 'partTypeName', width: 120, render: (text) => text || '-'
        },
        {
            title: '原车保修期（月）', dataIndex: 'originalPartTime', width: 160, render: (text) => text || '-'
        },
        {
            title: '原车保修里程（公里）', dataIndex: 'originalPartMileage', width: 180, render: (text) => text || '-'
        },
        {
            title: '配件保修期（月）', dataIndex: 'partTime', width: 160, render: (text) => text || '-'
        },
        {
            title: '配件保修里程（公里）', dataIndex: 'partMileage', width: 180, render: (text) => text || '-'
        },
        // {
        //     title: '权益明细编码', dataIndex: 'detailNo', width: 120, render: (text) => text || '-'
        // },
        // {
        //     title: '权益明细名称', dataIndex: 'detailName', width: 180, render: (text) => text || '-'
        // },
        // {
        //     title: '业务编码', dataIndex: 'businessCode', width: 180, render: (text) => text || '-'
        // },
        {
            title: '状态', dataIndex: 'partStatusName', width: 120, render: (text) => text || '-'
        },
        // { title: '生效时间', dataIndex: 'groupTime', width: 180, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-' },
        // { title: '失效时间', dataIndex: 'groupTime', width: 180, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-' },
        { title: '更新时间', dataIndex: 'updateTime', width: 180, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-' },
        { title: '同步时间', dataIndex: 'syncTime', width: 180, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-' },

        {
            title: '操作', width: 140, fixed: 'right', dataIndex: 'Operation', render: (text, record) => (<>
                {roleJudgment(userInfo, 'PARTS_MANAGEMENT_DETAIL') ?
                    <Button type='link' size='small' onClick={() => LookAt(record)}>详情</Button> : null}
            </>)
        }
    ]

    return (
        <div className={`${tableModuleStyles.PublicList}`}>
            <PublicTableQuery
                isCatch={false}
                isFormDown={false}
                onSearch={onSearch}
                searchList={searchList}
            />

            <div className="tableData">
                <PublicTable
                    ref={ref}
                    type={6}
                    rowSelection={false}
                    defaultQuery={defaultQuery}
                    url={allUrl.PartsManagement.getPartInfoList}
                    columns={columns}
                    className={styles["blue-table"]}
                />
            </div>

            {isModalVisibleDetail &&
                <PartsDetail
                    handleCancel={handleCancel}
                    visible={isModalVisibleDetail}
                    detailInfoSource={detailInfoSource}
                />
            }
        </div>
    )
}
export default TabBase
