import React, { useState, useEffect, useImperativeHandle, forwardRef, memo, useCallback, useMemo } from 'react'
import { message, Table } from 'antd'
import { setStorage, getStorage } from '@/utils/Storage'
import * as request from '@/utils/request'
import { useCallbackState } from '@/hooks'

const getPageStorage = (key) => {
    let res = getStorage(window.location.hash, true)
    return res && res[key] ? res[key] : null
}
const PublicTable = forwardRef((props, ref) => {
    const {
        columns,
        defaultQuery,
        url,
        rowSelectionChange = () => { },
        scroll,
        rowSelection,
        radio,
        type,
        getCheckboxProps,
        method,
        sticky,
        tableWidth,
        rowKey,
        manualPaging,
        isCatch,
        className
    } = props
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const [current, setCurrent] = useState(isCatch && getPageStorage('pagination') ? getPageStorage('pagination').current : 1)
    const [pageSize, setPageSize] = useState(isCatch && getPageStorage('pagination') ? getPageStorage('pagination').pageSize : 10)
    const [total, setTotal] = useState(0)
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [selectedRows, setSelectedRows] = useState([])
    const [tableQuery, setTableQuery] = useState(JSON.stringify(defaultQuery))
    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
        if (isCatch) {
            // 翻页是也存储查询参数
            let obj = getStorage(window.location.hash, true) || {}
            obj = { ...obj, ...{ pagination: { current, pageSize }, Type: window.location.hash } }

            setStorage(obj)
        }
    }

    const getTableData = async (flag) => {
        //点击查询或者重置恢复到第一页
        setLoading(true)
        const queryParams = isCatch && getPageStorage('Query') ? getPageStorage('Query') : defaultQuery
        if (type === 1) {
            let params = { ...queryParams }
            request[method || 'post'](url, { ...params }).then(res => {
                if (res.success) {
                    res.resp.map((item, index) => item.key = index + 1)
                    setDataSource(res.resp || [])
                    setTotal(res.resp.length || 0)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(() => {
                setLoading(false)
            })
        } else if (type === 2) {//尹得鑫
            let params = { pageSize, pageNum: current, ...queryParams }
            request[method || 'post'](url, { ...params }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    Dt.list.map((item, index) => item.key = index + 1)
                    setDataSource(Dt.list || [])
                    setTotal(Dt.total || res.resp[0].pagination?.total || 0)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(() => {
                setLoading(false)
            })
        } else if (type === 3 && manualPaging === true) { //无分页
            let params = { ...queryParams }
            request[method || 'post'](url, { ...params }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    console.log('Dt', Dt)
                    Dt.map((item, index) => item.key = index + 1)
                    setDataSource(Dt || [])
                    setTotal(Dt.length || 0)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(() => {
                setLoading(false)
            })
        } else if (type === 4) {//尹得鑫
            let param = { pageSize, current: current, params: { ...queryParams } }
            console.log('params', param)
            request[method || 'post'](url, { ...param }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    Dt.list.map((item, index) => item.key = index + 1)
                    setDataSource(Dt.list || [])
                    setTotal(Dt.total || 0)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(() => {
                setLoading(false)
            })
        } else if (type === 5) {   //王志强
            let params = { pageSize, pageIndex: current, ...queryParams }
            request[method || 'post'](url, { ...params }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    Dt.list.map((item, index) => item.key = index + 1)
                    setDataSource(Dt.list || [])
                    setTotal(Dt.total || 0)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(() => {
                setLoading(false)
            })
        } else if (type === 6) {
            let params = { pageSize, pageNum: current, ...queryParams }
            request[method || 'post'](url, { ...params }).then(res => {
                if (res.success) {
                    if (res.resp) {
                        res.resp.map((item, index) => item.key = index + 1)
                        setDataSource(res.resp)
                    } else {
                        setDataSource([])
                    }
                    setTotal(res.total || 0)
                    setCurrent(current)
                } else {
                    setCurrent(1)
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(() => {
                setCurrent(1)
                setLoading(false)
            })
        }
        else {
            let params = { pageSize, current, params: queryParams }
            request[method || 'post'](url, { ...params }).then(res => {
                if (res.success) {
                    res.resp[0].list.map((item, index) => item.key = index + 1)
                    setDataSource(res.resp[0].list || [])
                    setTotal(res.resp[0].pagination.total || 0)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(() => {
                setLoading(false)
            })
        }

        if (rowSelection === false) {

        } else {
            setSelectedRowKeys([])
            setSelectedRows([])
            rowSelectionChange({ selectedRowKeys: [], selectedRows: [] })
        }
    }
    useEffect(() => {
        setTableQuery(JSON.stringify(defaultQuery))
    }, [defaultQuery])

    useEffect(() => {
        // getTableData(true)
        if (Object.keys(defaultQuery).indexOf('_t') !== -1) {
            if (current !== 1) {
                setCurrent(1)
            } else {
                getTableData(true)
            }
        } else {
        }

    }, [tableQuery])
    useEffect(() => {
        getTableData()
    }, [current, pageSize])

    useImperativeHandle(ref, () => ({
        getTableData,
        total,
        current,
        pageSize,
        dataSource,
        setDataSource,
        setCurrent,
        updateDataSource: (newValue) => {
            setDataSource(newValue)
        },
        setTotal
    }))

    const InforData = {
        rowKey: record => rowKey ? rowKey : record.key,
        bordered: true,
        dataSource,
        sticky: sticky ? true : false,
        loading,
        scroll: scroll ? scroll : { x: 'max-content' },
        columns,
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: rowSelection === false ? null : {
            type: radio ? 'radio' : '',
            selectedRowKeys, selectedRows,
            onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRowKeys(selectedRowKeys)
                setSelectedRows(selectedRows)
                rowSelectionChange({ selectedRowKeys, selectedRows })
            },
            getCheckboxProps
            // getCheckboxProps: record => ({ disabled: record.rebateState !== 1 })
        },
    };
    return <Table {...InforData} className={className} />
})
export default memo(PublicTable)