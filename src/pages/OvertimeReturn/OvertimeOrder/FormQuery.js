import React from 'react'
import { Form, Button, Input, Select, Row, Col ,DatePicker} from 'antd'
import moment from 'moment'
const { Option } = Select
const { RangePicker } = DatePicker;

const FormQuery = (props) => {
    const {onSearch} = props
    const [form] = Form.useForm()
    // const [formDown,ChangeFormDown] = useState(true)
    const handleSearch = () =>{
        form.validateFields().then(values => {
            if (values.Time && values.Time.length) {
                values.startTime = moment(values.Time[0]).format('YYYY-MM-DD')
                values.endTime = moment(values.Time[1]).format('YYYY-MM-DD')
            }else{
                values.startTime = ''
                values.endTime = ''
            }
            if (values.deliverTime && values.deliverTime.length) {
                values.deliverTimeStart = moment(values.deliverTime[0]).format('YYYY-MM-DD')
                values.deliverTimeEnd = moment(values.deliverTime[1]).format('YYYY-MM-DD')
            }else{
                values.deliverTimeStart = ''
                values.deliverTimeEnd = ''
            }
            values.orderState = values.orderState ? values.orderState :''
            delete values.Time
            delete values.deliverTime
            onSearch(values)
        })
    }
    const onReset = () =>{
        form.resetFields()
        onSearch()
    }
    const FormItemChange = (value,field) =>{
        console.log(value,field)
        value = value.replace(/(^\s*)|(\s*$)/g, "")
        form.setFieldsValue({
            [field]:value
        })
    }
    return (
        <>
            <Form className='PublicList_FormQuery' form={form}>
                <Row>
                    <Col span={7}>
                        <Form.Item name='orderNumber'>
                            <Input placeholder="订单号" allowClear onPressEnter={handleSearch} onChange={(e)=>FormItemChange(e.target.value,'orderNumber')} />
                        </Form.Item>
                    </Col>
                    <Col span={7}>
                        <Form.Item name='phone'>
                            <Input placeholder="手机号" allowClear onPressEnter={handleSearch} onChange={(e)=>FormItemChange(e.target.value,'phone')} />
                        </Form.Item>
                    </Col>
                    <Col span={7}>
                        <Form.Item name='Time'>
                            <RangePicker placeholder={['导入订单开始时间', '导入订单截止时间']} />
                        </Form.Item>
                    </Col>
                    
                    <Col span={7}>
                        <Form.Item name='key'>
                            <Input placeholder="关键字" allowClear onPressEnter={handleSearch} onChange={(e)=>FormItemChange(e.target.value,'key')} />
                        </Form.Item>
                    </Col>
                    <Col span={7}>
                        <Form.Item name='orderState'>
                            <Select placeholder='订单状态' allowClear>
                                <Option value={'1'}>不可补贴</Option>
                                <Option value={'2'}>补贴中</Option>
                                <Option value={'3'}>补贴结束</Option>
                                <Option value={'4'}>可提现</Option>
                                <Option value={'5'}>提现中</Option>
                                <Option value={'6'}>已提现</Option>
                                <Option value={'7'}>提现失败</Option>
                                <Option value={'8'}>不可提现</Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={7}>
                        <Form.Item name='deliverTime'>
                            <RangePicker placeholder={['大定支付开始时间', '大定支付截止时间']} />
                        </Form.Item>
                    </Col>
                    <Col span={3}>
                        <Form.Item>
                            <Button type='primary' onClick={handleSearch}>查询</Button>
                        </Form.Item>
                    </Col>
                    {/* <Col span={3}>
                        <Form.Item>
                            <Button onClick={onReset}>重置</Button>
                        </Form.Item>
                    </Col> */}
                    {/* <Col span={7} className='FormQuerySubmit'>
                        <Col span={6} className='Reset'>
                            <Button onClick={onReset}>重置</Button>
                        </Col>
                        <Col span={6} className='Search'>
                            <Button type='primary' onClick={handleSearch}>查询</Button>
                        </Col>
                        <Col span={6} className='operationButtons'>
                            <Form.Item>
                                {
                                    formDown ?
                                    <span onClick={()=>ChangeFormDown(false)}>
                                        <span>收起</span><UpOutlined />
                                    </span>
                                    :<span onClick={()=>ChangeFormDown(true)}>
                                        <span>展开</span><DownOutlined />
                                    </span>
                                }
                            </Form.Item>
                        </Col>
                    </Col> */}
                </Row>
            </Form>
        </>
    )
}
export default FormQuery