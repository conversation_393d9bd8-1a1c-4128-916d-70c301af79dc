import React from 'react'
import { <PERSON>, message, <PERSON><PERSON>, Row, Col, Upload, Modal, Divider, Popconfirm, Menu, Dropdown } from 'antd'
import { connect } from 'react-redux'
import { get, post } from '../../../utils/request'
import allUrl from '../../../utils/url'
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import moment from 'moment'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import EditFlowInfo from './components/EditFlowInfo'
import AddFlowInfo from './components/AddFlowInfo'
import Cookies from 'js-cookie'
import RebateSettings2 from './components/RebateSettings2'
import baseURL from '../../../baseURL'
import FormQuery from './FormQuery'
import '../index.less'
import { roleJudgment } from '@/utils/authority'
class OvertimeOrder extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            dataSource: [],
            visible: false,
            title: '',
            TabHeight: 0,
            selectedRowKeys: [],
            selectedRows: [],
            pageSize: 10,
            pageIndex: 0,
            current: 1,
            total: 0,
            defaultQuery: {},
            rowData: {},
            FlowInfoVisible: false,
            importErrorvisible: true,
            loading: false,
            exportloading: false,
            importVisible: false
        }
        this.UserID = ''
    }
    formRef = React.createRef();

    GetData = () => {
        const { defaultQuery } = this.state
        this.setState({ loading: true }, () => {
            post(allUrl.orderTimeOut.OvertimeOrderList, { ...defaultQuery }).then(res => {
                if (res && res.success) {
                    this.setState({
                        dataSource: res.resp,
                        total: res.resp.length,
                    })
                } else {
                    // message.error(res.msg || '获取列表信息失败！')
                }
                this.setState({ loading: false })
            })
        })
    }
    PageChange = (current, pageSize) => {
        this.setState({ current: current, pageSize }, () => {
            this.GetData()
        });
    }

    InitPage = () => {
        let WinHeight = document.documentElement.clientHeight;
        this.setState({ TabHeight: WinHeight - 540 });
    }
    RowAdd = () => {
        this.setState({ AddFlowInfoVisible: true })
    }
    RowEdit = (record) => {
        this.setState({ EditFlowInfoVisible: true, rowData: record })
    }
    RowDetail = (record) => {
        let data = {
            id: record.orderNumber,
            type: 'LookAt',
            data: record,
            title: '返利明细'
        }
        UniversalOpenWindow({
            JumpUrl: '/OvertimeReturn/OvertimeOrder', data, history: this.props.history
        })
    }
    RowDel = (record) => {
        get(allUrl.orderTimeOut.deleteOrder, { orderNumber: record.orderNumber }).then(res => {
            if (res.success) {
                message.success('删除成功！')
                this.GetData()
            } else {
                // message.error(res.msg)
            }
        })
    }
    renderOperation = (text, record) => {
        const { userInfo } = this.props
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo, 'PERM_OVERTIMEORDER_EDIT') ?
                    (record.orderState !== 5 && record.orderState !== 6) ?
                        <span style={{ cursor: 'pointer' }} onClick={() => this.RowEdit(record)}>编辑</span>
                        : null
                    : null
            }
            {/* <Divider type="vertical" />
            <span style={{cursor:'pointer'}} onClick={()=>this.RowDetail(record)}>详情</span> */}
            {
                roleJudgment(userInfo, 'PERM_OVERTIMEORDER_DELETE') ?
                    [
                        <Divider key={1} type="vertical" />,
                        <Popconfirm key={2} onConfirm={() => this.RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                            <span style={{ cursor: 'pointer' }}>删除</span>
                        </Popconfirm>
                    ] : null
            }
        </div>
    }
    handleModal = (visible, title) => {
        this.setState({ visible, title })
    }
    // exportBillingExcel =(data, res)=> {
    //     if (!data) {//如果没有data数据就不进行操作
    //       return
    //     }
    //     // 获取headers中的filename文件名
    //     let tempName = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
    //     // iconv-lite解决中文乱码
    //     let iconv = require('iconv-lite')
    //     iconv.skipDecodeWarning = true// 忽略警告
    //     let fileName = iconv.decode(tempName, 'gbk')
    //     let blob = new Blob([data], { type: 'application/vnd.ms-excel' })//转换成二进制对象
    //     if ('download' in document.createElement('a')) { // 不是IE浏览器
    //       let url = window.URL.createObjectURL(blob)//二进制对象转换成url地址
    //       let link = document.createElement('a')
    //       link.style.display = 'none'
    //       link.href = url
    //       link.setAttribute('download', fileName)
    //       document.body.appendChild(link)
    //       link.click()
    //       document.body.removeChild(link) // 下载完成移除元素
    //       window.URL.revokeObjectURL(url) // 释放掉blob对象
    //     } else {
    //       window.navigator.msSaveBlob(blob, fileName)
    //     }
    // }
    onSearch = (values) => {
        this.setState({ defaultQuery: values, current: 1 }, () => {
            this.GetData()
        })
    }

    TemplateDownload = (key) => {
        post(`${allUrl.orderTimeOut.OvertimeOrderDownLoad}?subsidyType=${key}`, {}, {
            responseType: "blob",
            headers: {
                "Content-Type": 'application/x-www-form-urlencoded'
            }
        }).then(res => {
            if (res) {
                let blob = new Blob([res], { type: "application/vnd.ms-excel" });
                let name = key === '1' ? '返利补贴订单模板' : '其他订单模版'
                if (window.navigator.msSaveOrOpenBlob) {
                    //兼容ie
                    window.navigator.msSaveBlob(blob, name + '.xlsx');
                } else {
                    let downloadElement = document.createElement('a');
                    let href = window.URL.createObjectURL(blob); //创建下载的链接
                    downloadElement.href = href;
                    downloadElement.download = name + '.xlsx'; //下载后文件名
                    document.body.appendChild(downloadElement);
                    downloadElement.click(); //点击下载
                    document.body.removeChild(downloadElement); //下载完成移除元素
                    window.URL.revokeObjectURL(href); //释放掉blob对象
                }
                message.success('模板下载成功！')
            }
        })
    }
    BulkRebate = () => {
        this.setState({ FlowInfoVisible: true })
    }
    renderDescription = (record) => {
        const { userInfo } = this.props
        return userInfo && userInfo.permissionCodeList.indexOf('PERM_OVERTIMEORDER_DETAILED') > -1 ?
            record.subsidyType === '1' ? <div style={{ textAlign: 'center', color: '#1890ff', cursor: 'pointer' }} onClick={() => this.RowDetail(record)}>明细</div> : null
            : null
    }

    UploadChange = (info) => {
        if (info.file.status !== 'uploading') {
            console.log(info.file, info.fileList);
            this.setState({ loading: true })
        }
        if (info.file.status === 'done') {
            console.log(info)
            this.setState({ loading: false })
            if (info.file.response && info.file.response.success) {
                message.success(info.file.response.msg)
                this.setState({ importVisible: false })
                this.GetData()
            } else {
                if (info.file.response.resp) {
                    let arr = info.file.response.resp
                    if (arr.length) {
                        Modal.error({
                            title: '提示',
                            content: `文档中订单号为${arr.join(',')}重复，请检查后重新导入！`
                        });
                    } else {
                        message.error(info.file.response.msg)
                    }
                } else {
                    message.error(info.file.response.msg)
                }
            }
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name}上传失败！`);
            this.setState({ loading: false })
        }
    }
    Export = () => {
        this.setState({ exportloading: true }, () => {
            post(allUrl.orderTimeOut.exportAllOrder, {}, {
                headers: {
                    'Content-Type': "application/x-www-form-urlencoded;charset=UTF-8"
                },
                responseType: "blob"
            }).then(res => {
                this.setState({ exportloading: false })
                console.log(res)
                if (res) {
                    let blob = new Blob([res], { type: "application/vnd.ms-excel" });
                    if (window.navigator.msSaveOrOpenBlob) {
                        //兼容ie
                        window.navigator.msSaveBlob(blob, '订单信息.xlsx');
                    } else {
                        let downloadElement = document.createElement('a');
                        let href = window.URL.createObjectURL(blob); //创建下载的链接
                        downloadElement.href = href;
                        downloadElement.download = '订单信息' + '.xlsx'; //下载后文件名
                        document.body.appendChild(downloadElement);
                        downloadElement.click(); //点击下载
                        document.body.removeChild(downloadElement); //下载完成移除元素
                        window.URL.revokeObjectURL(href); //释放掉blob对象
                    }
                    message.success('订单信息下载成功！')
                }
            })
        })
    }

    MenuClick = ({ key }) => {
        if (key === '1') {
            this.TemplateDownload(key)
        } else if (key === '2') {
            this.TemplateDownload(key)
        }
    }

    render() {
        const { dataSource, pageSize, total, current, selectedRowKeys, importVisible, selectedRows, rowData, exportloading, loading } = this.state
        const { userInfo } = this.props
        const InforData = {
            rowKey: record => record.id,
            bordered: true,
            dataSource,
            loading,
            scroll: { x: 'max-content' },
            columns: [
                // { title: '序号', dataIndex: 'index',width:80,render:(text,record,index)=><div style={{textAlign:'center'}}>{(pageSize * (pageIndex+1))- pageSize +  index+1}</div>},
                { title: '序号', dataIndex: 'index', fixed: 'left', width: 70, render: (text, record, index) => <div style={{ textAlign: 'center' }}>{index + 1}</div> },
                { title: '导入订单时间', dataIndex: 'importTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD') : '' },
                { title: '姓名', dataIndex: 'name', width: 90 },
                { title: '手机号', dataIndex: 'phone', width: 100 },
                { title: '补贴类型', dataIndex: 'subsidyTypeName', width: 140 },
                { title: '类型名称', dataIndex: 'typeName', width: 160 },
                { title: '订单号', dataIndex: 'orderNumber', width: 140 },
                { title: '大定支付时间', dataIndex: 'payTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD') : '' },
                { title: '预计交付时间', dataIndex: 'deliverTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD') : '' },
                { title: '车辆到店时间', dataIndex: 'carToStoreTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD') : '' },
                { title: '用户提车时间', dataIndex: 'userTakeCarTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD') : '' },
                { title: '超时天数', dataIndex: 'overtimeDay', width: 100 },
                { title: '返利金额', dataIndex: 'rebateAmount', width: 120 },
                { title: '订单状态', dataIndex: 'orderStateName', width: 110 },
                { title: '返利明细', dataIndex: 'description', fixed: 'right', width: 100, render: (text, record) => this.renderDescription(record) },
                { title: '操作', width: 120, fixed: 'right', dataIndex: 'Operation', render: (text, record) => this.renderOperation(text, record) }
            ],
            pagination: {
                pageSize: pageSize,
                onChange: this.PageChange,
                current: current,
                total: total,
                showTotal: () => `共${total}条，${pageSize}条/页`,
                showSizeChanger: true,
                showQuickJumper: true,
                onShowSizeChange: this.PageChange,
            },
            rowSelection: {
                selectedRowKeys, selectedRows,
                onChange: (selectedRowKeys, selectedRows) => { this.setState({ selectedRowKeys, selectedRows }) },
                // getCheckboxProps: record => ({ disabled: record.rebateState !== 1 })
            },
        };
        const UploadProps = {
            name: 'file',
            action: baseURL.Host + allUrl.orderTimeOut.OvertimeOrderexcelImport,
            headers: {
                Authorization: Cookies.get('scrm_token'),
                appId: 1
            },
            showUploadList: false
        };

        const menu = (<Menu onClick={this.MenuClick}>
            <Menu.Item key='1'>
                返利补贴模板
            </Menu.Item>
            <Menu.Item key='2'>
                其他模板
            </Menu.Item>
        </Menu>)
        return (
            <div className='PublicList'>
                <Row className='queryArea'>
                    {
                        roleJudgment(userInfo, 'PERM_OVERTIMEORDER_ADD') ?
                            <Col className='add'><Button style={{ marginRight: '16px' }} onClick={() => this.RowAdd()} icon={<PlusOutlined />}>增加返利订单</Button></Col>
                            : null
                    }
                    {/* {
                        roleJudgment(userInfo,'PERM_OVERTIMEORDER_BATCHADD') ?
                        <Col className='add'><Button style={{ marginRight: '16px' }} disabled={!selectedRows.length} onClick={this.BulkRebate}>批量添加返利</Button></Col>
                        :null
                    } */}
                    {
                        roleJudgment(userInfo, 'PERM_OVERTIMEORDER_EXPORT') ?
                            <Col className='add'>
                                <Button style={{ marginRight: '16px' }} onClick={this.Export} loading={exportloading}>全部导出</Button>
                            </Col>
                            : null
                    }
                    {
                        roleJudgment(userInfo, 'PERM_OVERTIMEORDER_IMPORT') ?
                            <Col className='add'>
                                <Button onClick={() => this.setState({ importVisible: true })} style={{ marginRight: '16px' }} icon={<PlusOutlined />}>批量导入</Button>
                            </Col>
                            : null
                    }
                    {
                        userInfo && userInfo.permissionCodeList.indexOf('PERM_OVERTIMEORDER_IMPORT') > -1 ?
                            <Dropdown overlay={menu}>
                                <a style={{ paddingTop: ' 10px' }}>模板下载</a>
                            </Dropdown>
                            : null
                    }
                </Row>
                <FormQuery onSearch={this.onSearch} />
                <Row className='tableTiele'>超时订单列表</Row>
                <div className='tableData'>
                    <Table {...InforData} />
                </div>
                {
                    this.state.AddFlowInfoVisible &&
                    <AddFlowInfo
                        visible={this.state.AddFlowInfoVisible}
                        title={'增加返利订单'}
                        onCancel={() => this.setState({ AddFlowInfoVisible: false })}
                        onCk={() => this.setState({ AddFlowInfoVisible: false })}
                        GetData={this.GetData}
                    />
                }
                {
                    this.state.EditFlowInfoVisible &&
                    <EditFlowInfo
                        visible={this.state.EditFlowInfoVisible}
                        title={'编辑返利订单'}
                        onCancel={() => this.setState({ EditFlowInfoVisible: false })}
                        onCk={() => this.setState({ EditFlowInfoVisible: false })}
                        GetData={this.GetData}
                        rowData={rowData}
                    />
                }
                {
                    this.state.FlowInfoVisible &&
                    <RebateSettings2
                        visible={this.state.FlowInfoVisible}
                        title={'返利设置'}
                        onCancel={() => this.setState({ FlowInfoVisible: false })}
                        onCk={() => this.setState({ FlowInfoVisible: false })}
                        cb={this.GetData}
                        selectedRows={selectedRows}
                    />
                }
                {
                    importVisible &&
                    <Modal title='请选择导入类型' visible={importVisible} onCancel={() => this.setState({ importVisible: false })} footer={null} maskClosable={false}>
                        <Row justify='center'>
                            <Col span={12} style={{ marginBottom: '20px' }}>
                                <Upload
                                    {...UploadProps}
                                    action={baseURL.Host + allUrl.orderTimeOut.OvertimeOrderexcelImport}
                                    onChange={this.UploadChange}
                                >
                                    <Button icon={<UploadOutlined />} type='primary' style={{ width: '200px' }}>返利补贴导入</Button>
                                </Upload>
                            </Col>
                        </Row>
                        <Row justify='center'>
                            <Col span={12} >
                                <Upload
                                    {...UploadProps}
                                    action={baseURL.Host + allUrl.orderTimeOut.excelImportOther}
                                    onChange={this.UploadChange}
                                >
                                    <Button icon={<UploadOutlined />} type='primary' style={{ width: '200px' }}>其他导入</Button>
                                </Upload>
                            </Col>
                        </Row>


                    </Modal>
                }
            </div>
        )
    }
    componentDidMount() {
        this.InitPage()
        this.GetData()
    }
    componentWillUnmount() {
        this.setState = () => {
            return;
        };
    }
}
export default connect(({ common }) => {
    const { userInfo } = common
    return { userInfo }
})(OvertimeOrder)
