import React ,{useEffect, useState} from 'react'
import { Modal ,Popover,Button, Table ,Tooltip, Row} from 'antd'
import allUrl from '@/utils/url'
import {post} from '@/utils/request'
import PublicTooltip from '@/components/Public/PublicTooltip'
import '../index.less'
import moment from 'moment'
import { QuestionCircleOutlined } from '@ant-design/icons'
import {fileDown} from '@/utils'
import { roleJudgment } from '@/utils/authority'
import { COLORS } from '@/constants/index'
import { useSelector } from 'react-redux'
import _ from 'lodash'
import ExtendedColumn from './ExtendedColumn'


export default (props) =>{
    const {visible,title,onCancel,data, exportParams} = props
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [dataSource1,setDataSource1] = useState([])
    const [dataSource2,setDataSource2] = useState([])
    const [total1,setTotal1] = useState(0)
    const [total2,setTotal2] = useState(0)
    const [pageSize1,setPageSize1] = useState(10)
    const [pageSize2,setPageSize2] = useState(10)
    const [current1,setCurrent1] = useState(1)
    const [current2,setCurrent2] = useState(1)
    const [visible1,setVisible1] = useState(false)
    const [visible2,setVisible2] = useState(false)
    const [recordInfo, setRecordInfo] = useState({
        shortName: ''
    })
    // 渲染悬浮
    const renderPopver = (text, record, key) => {
        const content = (
            <div>
            {     
                record.deliverChildren.length ? record.deliverChildren.map((item,index) => {
                    return (<div style={{display: 'flex', alignItems: 'center'}}>
                                <i style={{marginRight: 5, borderRadius: '50%', backgroundColor:COLORS[index], width: 10, height: 10}}></i>
                                <span>{item.deliverStatusName}:{item[key]}</span>
                            </div>)
                }) : null
            }
            </div>
        );
        return (
            <Popover
            content={content}
            title={
                <div>
                <p>{record.date || recordInfo.date}</p>
                {/* <p>管家数量</p> */}
                </div>
            }
            >
            <i style={{borderRadius: '50%', }}></i>{text}
            </Popover>
        );
    }
    const [columns1, setColums1] = useState([
        { title: '统计时间', dataIndex: 'date',width:140, fixed: 'left'},
        { title: '管家群量', dataIndex: 'groupCount',width:100,
        render: (text, record) => renderPopver(text, record, 'groupCount')
        },
        { title: 
            <div>
                已开启对话量&nbsp;
                <Tooltip title='指当天开启的但不一定当天被回复的对话轮。'>
                <QuestionCircleOutlined />
                </Tooltip>
            </div>,  dataIndex: 'roundCount',width:150,
            render: (text, record) => renderPopver(text, record, 'roundCount')},
        { title: 
            <div>
                响应量&nbsp;
                <Tooltip title='指被回复的对话轮数量'>
                <QuestionCircleOutlined key='1' />
                </Tooltip>
            </div>, 
         dataIndex: 'roundCompleteCount',width:100,
         render: (text, record) => renderPopver(text, record, 'roundCompleteCount')},
        { title: 
            <div>
                当日响应量&nbsp;
                <Tooltip title='当日23:59:59+10分钟内被回复的对话量'>
                <QuestionCircleOutlined key='2'/>
                </Tooltip>
            </div>, 
        dataIndex: 'intradayRoundCompleteCount',width:140,
        render: (text, record) => renderPopver(text, record, 'intradayRoundCompleteCount')},
        { title: 
            <div>
                及时响应量&nbsp;
                <Tooltip title='10分钟内被回复的会话轮数'>
                <QuestionCircleOutlined key='3'/>
                </Tooltip>
            </div>, 
        dataIndex: 'lt10MinCount',width:140,
        render: (text, record) => renderPopver(text, record, 'lt10MinCount')},
        { title: 
            <div>
                超过10分钟响应量&nbsp;
                <Tooltip title='响应量-10分钟内被回复的会话轮数'>
                <QuestionCircleOutlined key='4'/>
                </Tooltip>
            </div>, 
         dataIndex: 'gt10MinCount',width:170,
         render: (text, record) => renderPopver(text, record, 'gt10MinCount')},
         { title: 
            <div>
                超过30分钟响应量&nbsp;
                <Tooltip title='响应量-30分钟内被回复的会话轮数'>
                <QuestionCircleOutlined key='5'/>
                </Tooltip>
            </div>, 
         dataIndex: 'gt30MinCount',width:170,
         render: (text, record) => renderPopver(text, record, 'gt30MinCount')},
         { title: 
            <div>
                超过72小时响应量&nbsp;
                <Tooltip title='响应量-72小时内被回复的会话轮数'>
                <QuestionCircleOutlined key='6'/>
                </Tooltip>
            </div>, 
         dataIndex: 'timeoutRoundCount',width:170,
         render: (text, record) => renderPopver(text, record, 'gt30MinCount')},
        { title:
            <div>
                72小时平均响应时长&nbsp;
                <Tooltip title='72小时平均响应时长=总响应时长/响应量，超过72小时的对话视为耗费72小时才响应'>
                <QuestionCircleOutlined key='7'/>
                </Tooltip>
            </div>,
        dataIndex: 'allAvgRoundTime',width:180,
        render: (text, record) => renderPopver(text, record, 'allAvgRoundTime')},
        { title:
            <div>
                当日平均响应时长&nbsp;
                <Tooltip title='当日平均响应时长=当日响应时长/当日响应量'>
                <QuestionCircleOutlined key='8'/>
                </Tooltip>
            </div>,
        dataIndex: 'intradayAvgRoundTime',width:170,
        render: (text, record) => renderPopver(text, record, 'intradayAvgRoundTime')},
        { title: 
            <div>
                平均及时回复率&nbsp;
                <Tooltip title='当日及时回复率=当日10 分钟内被回复的会话轮数/当日开启的对话轮数'>
                <QuestionCircleOutlined key='9'/>
                </Tooltip>
            </div>, 
         dataIndex: 'timelyReplyPercent',width:160,
         render: (text, record) => renderPopver(text, record, 'timelyReplyPercent')},
         {title: '当日平均响应时长（工作时间）',width:170, checked: false,dataIndex: 'workingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'workingTimeAvgRoundTime')},
         {title: '当日平均响应时长（非工作时间）', width:170,checked: false,dataIndex: 'nonworkingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'nonworkingTimeAvgRoundTime')},
         {title: '平均及时回复率（工作时间）',width:170, checked: false,dataIndex: 'workingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'workingTimelyReplyPercent')},
         {title: '平均及时回复率（非工作时间）',width:170,checked: false, dataIndex: 'nonworkingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'nonworkingTimelyReplyPercent')},
        { title: '大区排名明细',width:120,fixed:'right',render:(text,record)=><a onClick={()=>{
             getBigAreaRankDataByDate(record)
             setRecordInfo(record)
             setVisible1(true)
         }}>详情</a>},
        { title: '省区排名明细',width:120,fixed:'right',render:(text,record)=><a onClick={()=>{
            getAreaRankDataByDate(record)
            setRecordInfo(record)
            setVisible2(true)
        }}>详情</a>},
        


    ])

    const [columns2, setColums2] = useState( [
        { title: '排名', dataIndex: 'rank',fixed:'left',width:100, render: (text, record, index) => serialNumber2(current2,pageSize2,index+1) },
        { title: '所属大区', dataIndex: 'bigAreaName',fixed:'left',width:100,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '管家群量', dataIndex: 'groupCount',render: (text, record) => renderPopver(text, record, 'groupCount')},
        { title: '开启对话量', dataIndex: 'roundCount',render: (text, record) => renderPopver(text, record, 'roundCount')},
        { title: '响应量', dataIndex: 'roundCompleteCount',render: (text, record) => renderPopver(text, record, 'roundCompleteCount')},
        { title: '及时响应量', dataIndex: 'lt10MinCount',render: (text, record) => renderPopver(text, record, 'lt10MinCount')},
        { title: '超10分钟响应量', dataIndex: 'gt10MinCount',render: (text, record) => renderPopver(text, record, 'gt10MinCount')},
        { title: '超30分钟响应量', dataIndex: 'gt30MinCount',render: (text, record) => renderPopver(text, record, 'gt30MinCount')},
        { title: '超72小时响应量', dataIndex: 'timeoutRoundCount',render: (text, record) => renderPopver(text, record, 'timeoutRoundCount')},
        { title: '72小时平均响应时长', dataIndex: 'allAvgRoundTime',render: (text, record) => renderPopver(text, record, 'allAvgRoundTime')},
        { title: '当日平均响应时长', dataIndex: 'intradayAvgRoundTime',render: (text, record) => renderPopver(text, record, 'intradayAvgRoundTime')},
        { title: '平均及时回复率', dataIndex: 'timelyReplyPercent',render: (text, record) => renderPopver(text, record, 'timelyReplyPercent')},
        {title: '当日平均响应时长（工作时间）',width:170, checked: false,dataIndex: 'workingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'workingTimeAvgRoundTime')},
        {title: '当日平均响应时长（非工作时间）', width:170,checked: false,dataIndex: 'nonworkingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'nonworkingTimeAvgRoundTime')},
        {title: '平均及时回复率（工作时间）',width:170, checked: false,dataIndex: 'workingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'workingTimelyReplyPercent')},
        {title: '平均及时回复率（非工作时间）',width:170,checked: false, dataIndex: 'nonworkingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'nonworkingTimelyReplyPercent')},
    ])
    const [columns3, setColums3] = useState( [
        { title: '排名', dataIndex: 'rank',fixed:'left',width:100,render: (text, record, index) => serialNumber2(current2,pageSize2,index+1) },
        { title: '所属大区', dataIndex: 'bigAreaName',fixed:'left',width:100,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '所属省区', dataIndex: 'areaName',fixed:'left',width:100,render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        { title: '管家群量', dataIndex: 'groupCount',render: (text, record) => renderPopver(text, record, 'groupCount')},
        { title: '开启对话量', dataIndex: 'roundCount',render: (text, record) => renderPopver(text, record, 'roundCount')},
        { title: '响应量', dataIndex: 'roundCompleteCount',render: (text, record) => renderPopver(text, record, 'roundCompleteCount')},
        { title: '及时响应量', dataIndex: 'lt10MinCount',render: (text, record) => renderPopver(text, record, 'lt10MinCount')},
        { title: '超10分钟响应量', dataIndex: 'gt10MinCount',render: (text, record) => renderPopver(text, record, 'gt10MinCount')},
        { title: '超30分钟响应量', dataIndex: 'gt30MinCount',render: (text, record) => renderPopver(text, record, 'gt30MinCount')},
        { title: '超72小时响应量', dataIndex: 'timeoutRoundCount',render: (text, record) => renderPopver(text, record, 'timeoutRoundCount')},
        { title: '72小时平均响应时长', dataIndex: 'allAvgRoundTime',render: (text, record) => renderPopver(text, record, 'allAvgRoundTime')},
        { title: '当日平均响应时长', dataIndex: 'intradayAvgRoundTime',render: (text, record) => renderPopver(text, record, 'intradayAvgRoundTime')},
        { title: '平均及时回复率', dataIndex: 'timelyReplyPercent',render: (text, record) => renderPopver(text, record, 'timelyReplyPercent')},
        {title: '当日平均响应时长（工作时间）',width:170, checked: false,dataIndex: 'workingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'workingTimeAvgRoundTime')},
        {title: '当日平均响应时长（非工作时间）', width:170,checked: false,dataIndex: 'nonworkingTimeAvgRoundTime',render: (text, record) => renderPopver(text, record, 'nonworkingTimeAvgRoundTime')},
        {title: '平均及时回复率（工作时间）',width:170, checked: false,dataIndex: 'workingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'workingTimelyReplyPercent')},
        {title: '平均及时回复率（非工作时间）',width:170,checked: false, dataIndex: 'nonworkingTimelyReplyPercent',render: (text, record) => renderPopver(text, record, 'nonworkingTimelyReplyPercent')},
    ])

    const serialNumber2 =(pageIndex, pageSize, index)=>{
        return (current1-1) * pageSize + index;
    }
    // 获取省区数据
    const getAreaRankDataByDate = (record) => {
        post(allUrl.WeCom.getAreaRankDataByDate,{date:record.date, deliverStatus: record.deliverStatus}).then(res=>{
            if(res.success){
                if(res.resp && res.resp[0].length){
                    res.resp[0].map(item=> {
                        item.date = record.date
                    })
                }
                setDataSource2(res.resp[0])
            }else{
                // message.error(res.msg)
            }
        })
    }
    // 获取大区数据
    const getBigAreaRankDataByDate = (record) => {
        post(allUrl.WeCom.getBigAreaRankDataByDate,{date:record.date, deliverStatus: record.deliverStatus}).then(res=>{
            if(res.success){
                if(res.resp && res.resp[0].length){
                    res.resp[0].map(item=> {
                        item.date = record.date
                    })
                }
                setDataSource1(res.resp[0])
            }else{
                // message.error(res.msg)
            }
        })
    }
    // 数据流导出Excel 导出全国数据
    const exportNationTableData = () => {
        post(allUrl.WeCom.exportNationTableData, {...exportParams},{responseType: "blob"}).then(res=>{
            console.log('res.fileName',res.fileNameß)
            fileDown(res,res.fileName)
        })
    }
    // 导出省区数据
    const exportAreaRankDataByDate = () => {
        post(allUrl.WeCom.exportAreaRankDataByDate, {date: recordInfo.date, deliverStatus: exportParams.deliverStatus},{responseType: "blob"}).then(res=>{
            fileDown(res,res.fileName)
        })
    }
    // 导出大区数据
    const exportBigAreaRankDataByDate = () => {
        post(allUrl.WeCom.exportBigAreaRankDataByDate, {date: recordInfo.date, deliverStatus: exportParams.deliverStatus}, {responseType: "blob"}).then(res=>{
            fileDown(res,res.fileName)
        })
    }

    let newColumns1 = _.cloneDeep({ columns1 }).columns1
    for (let i = 0; i < newColumns1.length; i++) {
        if (JSON.stringify(newColumns1[i]['checked']) !== undefined && !newColumns1[i].checked) {
            newColumns1.splice(i, 1)
            i--
        }
    }
    const InforData = {
        columns:newColumns1,
        dataSource:data || [],
        bordered:true,
        rowKey:'id',
        size: 'small',
        scroll:{x:2200,y:600},
        pagination: false
    }

    let newColumns2 = _.cloneDeep({ columns2 }).columns2
    for (let i = 0; i < newColumns2.length; i++) {
        if (JSON.stringify(newColumns2[i]['checked']) !== undefined && !newColumns2[i].checked) {
            newColumns2.splice(i, 1)
            i--
        }
    }
    const InforData1 = {
        columns:newColumns2,
        dataSource:dataSource1,
        bordered:true,
        rowKey:'id',
        size: 'small',
        scroll:{x:2200,y:600},
        pagination: false
    }

    let newColumns3 = _.cloneDeep({ columns3 }).columns3
    for (let i = 0; i < newColumns3.length; i++) {
        if (JSON.stringify(newColumns3[i]['checked']) !== undefined && !newColumns3[i].checked) {
            newColumns3.splice(i, 1)
            i--
        }
    }
    const InforData2 = {
        columns:newColumns3,
        dataSource:dataSource2,
        bordered:true,
        rowKey:'id',
        size: 'small',
        scroll:{x:2200,y:600},
        pagination: false
    }
    return <Modal title=
                {
                    <div>
                        <div>{title}<span style={{marginLeft:'20px',fontSize:'14px',color: 'rgba(0,0,0,0.45)',fontWeight: 400}}>{`统计截止${moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD 23:59:59')}`}</span></div>
                            <div>
                                {
                                    roleJudgment(userInfo, 'GROUP_RESPONSE_EXPORT') ?
                                    <Button style={{marginTop: 10}} type="primary" disabled={InforData.dataSource.length === 0} onClick={exportNationTableData}>导出</Button> :null
                                }
                                <ExtendedColumn setColums={setColums1} columns={columns1}/>
                            </div>
                    </div>
                
                } visible={visible} onCancel={onCancel} maskClosable={false} footer={null} width={1300}>
                <Table {...InforData} />
                <Modal visible={visible1} 
                title=
                {
                    <div>
                        <div>{`大区排名明细(${recordInfo.date || ''})`}</div>
                            <div>
                                {
                                roleJudgment(userInfo, 'GROUP_RESPONSE_BIGAREA_EXPORT') ?
                                <Button style={{marginTop: 10}} type="primary" disabled={InforData1.dataSource.length === 0} onClick={exportBigAreaRankDataByDate}>导出</Button>: null
                                }
                                 <ExtendedColumn setColums={setColums2} columns={columns2}/>
                            </div>
                        </div>
                
                } 
               footer={null} onCancel={()=>setVisible1(false)} width={1000}>
                    <Table {...InforData1} />
                </Modal>
                <Modal visible={visible2} 
                title=
                {
                    <div>
                        <div>{`省区排名明细(${recordInfo.date || ''})`}</div>
                            <div>
                                {
                                roleJudgment(userInfo, 'GROUP_RESPONSE_PROVINCE_EXPORT') ?
                                <Button style={{marginTop: 10}} type="primary" disabled={InforData2.dataSource.length === 0} onClick={exportAreaRankDataByDate}>导出</Button>:null
                                }
                                <ExtendedColumn setColums={setColums3} columns={columns3}/>
                            </div>
                    </div>
                
                } 
                 footer={null} onCancel={()=>setVisible2(false)} width={1000}>
                    <Table {...InforData2} />
                </Modal>
            </Modal>
}