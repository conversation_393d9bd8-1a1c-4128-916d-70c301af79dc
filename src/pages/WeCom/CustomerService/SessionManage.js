
import React, { useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Row, Col, Button, Spin, Divider, Modal, message } from 'antd'
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import PublicTable from '@/components/Public/PublicTable'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import SessionList from './components/sessionList'
import _ from 'lodash'
import moment from 'moment';
import { roleJudgment } from '@/utils/authority'
const SessionManage = () => {
    const tableRef = useRef()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [tableHeight, setTableHeight] = useState(0)
    const [codeVisible, setCodeVisible] = useState(false)
    const [sessionItem, setSessionItem] = useState({})
    const [spinLoading, setSpinLoading] = useState(false);
    const [defaultQuery, setDefaultQuery] = useState({})
    const [columns, setColums] = useState([
        { title: '会话id', dataIndex: 'sessionId', width: 80 },
        { title: '账号名称', dataIndex: 'accountName', width: 140, },
        { title: '用户微信昵称', dataIndex: 'customerName', width: 160},
        { title: '接待坐席姓名', dataIndex: 'servicerName', width: 160 },
        { title: '接待开始时间', dataIndex: 'startTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''  },
        { title: '接待结束时间', dataIndex: 'endTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''  },
        { title: '历史会话记录', width: 100, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
    ])
    const onSearch = (values) => {
        if (values.dateTime && values.dateTime.length) {
            values.startTime = moment(values.dateTime[0]).format('YYYY-MM-DD')
            values.endTime = moment(values.dateTime[1]).format('YYYY-MM-DD')
            delete values.dateTime
        }
        setDefaultQuery(values)
    }
    const RowCode = (record) => {
        setCodeVisible(true)
        setSessionItem(record)
    }
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff',textAlign:'center' }}>
            {
                roleJudgment(userInfo, 'SESSION_MANAGE_DETAIL') ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowCode(record, 1)}>查看</span>
                    ] 
                    : null
            }
            

        </div>
    }

    const initPage = () => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let FormQueryH = document.getElementsByClassName('PublicList_FormQuery')[0].clientHeight
        let h = winH - FormQueryH - 47 - 54 - 305
        setTableHeight(h)
    }
    const handleCancel = () => {
        setCodeVisible(false);
    };

    useEffect(() => {
        // onSearch()
        initPage()
    }, [])

    let searchList = [
        { label: '客服名称', name: 'name', type: 'Input', colSpan: 6 },
        { label: '客户名称', name: 'customerName', type: 'Input', colSpan: 6 },
        { label: '时间', name: 'dateTime', type: 'RangePicker', colSpan: 6 },
    ]
    let newColumns = _.cloneDeep({ columns }).columns
    for (let i = 0; i < newColumns.length; i++) {
        if (JSON.stringify(newColumns[i]['checked']) !== undefined && !newColumns[i].checked) {
            newColumns.splice(i, 1)
            i--
        }
    }

    return (
        <div className='SessionManage'>
            <PublicTableQuery isCatch={true}  isFormDown={false} onSearch={onSearch} searchList={searchList} />
            <div className='tableData'>
                <Row className='tableTitle'>
                    <Col className='text'>会话管理列表</Col>
                </Row>
                <PublicTable isCatch={true} type = {5} scroll={{x:'max-content',y:tableHeight}} sticky={true} ref={tableRef} rowSelection={false} columns={newColumns} defaultQuery={defaultQuery} url={allUrl.WeCom.querySession} />
            </div> 
            <Modal className="codeContainer" title="会话记录" destroyOnClose={true} open={codeVisible} footer={null} width={448} maskClosable={true} onCancel={handleCancel}>
                <SessionList sessionItem={sessionItem}></SessionList>
            </Modal>
          
        </div>
    )
}
export default SessionManage
