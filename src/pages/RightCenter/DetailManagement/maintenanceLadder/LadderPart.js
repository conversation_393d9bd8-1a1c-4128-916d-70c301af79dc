import React, { useRef, useState, useContext, useEffect } from 'react'
import { Form, Input, InputNumber, Radio, Space, Row, Col } from 'antd';
import _ from 'lodash'
import LadderPartBottom from './LadderPartBottom'
import ladderStyle from './ladderPart.module.less'

const LadderPart = (props) => {
    const { index, strategyConfig, form, data } = props
    const { maintenanceLadderDetailItem, maintenanceLadderLimitFrame, maintenanceLadderDetail } = strategyConfig['maintenanceLadderFrame'].c
    // const { fixedBeginMileage } = strategyConfig['effectMileageFrame'].c
    const { ladderLimitYear, ladderLimitMonth, ladderLimitMileage } = maintenanceLadderDetailItem

    const _maintenanceLadderLimitFrame = data[maintenanceLadderLimitFrame.value]

    //重置阶梯年限input框的值
    const resetLimitTimeInput = (index) => {
        setFieldCustom(index, ladderLimitYear.value, 0)
        setFieldCustom(index, ladderLimitMonth.value, 0)
    }
    //重置阶梯里程input框的值
    const resetLadderMileage = (index) => {
        setFieldCustom(index, ladderLimitMileage.value, null)
    }

    // 设置单个field的值
    const setFieldCustom = (index, item, value) => {
        const fields = form.getFieldValue(maintenanceLadderDetail.value);
        Object.assign(fields[index], { [item]: value });
        form.setFieldsValue({ fields });
    }

    //清空输入框的值，没有输入的情况下，重置为0
    const handleInput = (value, index, field) => {
        if (!value) setFieldCustom(index, field, 0)
    }

    useEffect(() => {
        if (_maintenanceLadderLimitFrame === 2) {
            resetLimitTimeInput(index)
        }
        if (_maintenanceLadderLimitFrame === 1) {
            resetLadderMileage(index)
        }
    }, [_maintenanceLadderLimitFrame])


    return (

        <div className={`${ladderStyle['ladder-part']} ${ladderStyle['ladder-part-wrap']}`}>
            <div className={ladderStyle['num']}>{index + 1}</div>
            <div className={ladderStyle['content']}>
                {
                    _maintenanceLadderLimitFrame !== 2 ?
                        <div>
                            <span className={ladderStyle['item-title']}>阶梯限制年限</span>
                            <>
                                <Form.Item
                                    name={[index, ladderLimitYear.value]}
                                    style={{ display: 'inline-block' }}
                                    initialValue={0}
                                >
                                    <InputNumber min={0} style={{ width: 90 }} onBlur={e => handleInput(e.target.value, index, ladderLimitYear.value)} />
                                </Form.Item>
                                <span style={{ marginLeft: 10, marginRight: 10 }}>年</span>
                                <Form.Item
                                    name={[index, ladderLimitMonth.value]}
                                    style={{ display: 'inline-block', width: '90px' }}
                                    initialValue={0}
                                >
                                    <InputNumber min={0} style={{ width: 90, lineHeight: '30px' }} onBlur={e => handleInput(e.target.value, index, ladderLimitMonth.value)} />
                                </Form.Item>
                                <span style={{ marginLeft: 10, marginRight: 10, lineHeight: '30px' }}>月</span>
                            </>
                        </div>
                        : null
                }

                {
                    _maintenanceLadderLimitFrame !== 1 ?
                        <div>
                            <span className={ladderStyle['item-title']}>阶梯限制里程</span>
                            <>
                                <Form.Item
                                    name={[index, ladderLimitMileage.value]}
                                    style={{ display: 'inline-block' }}
                                    rules={[{ required: true, message: '请输入阶梯限制里程' }]}
                                >
                                    <InputNumber min={1} style={{ width: 235 }} placeholder='只能输入数字，例如：160000' />
                                </Form.Item>
                                <span style={{ marginLeft: 10 }}>公里</span>
                            </>
                        </div>
                        : null
                }

                <LadderPartBottom
                    {...props}
                    ladderStyle={ladderStyle}
                />
            </div>
        </div >
    )
}

export default LadderPart