import React, { useEffect, useState } from "react";
import { Dropdown, Checkbox, Popconfirm, Button } from "antd";
import _ from "lodash";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import "./index.less";
import { useDispatch, useSelector } from "react-redux";
import {ListExpandAdd} from '@/actions/async'
const ExtendedColumn = (props) => {
  const dispath =useDispatch()
  const { userInfo, ListExpand} = useSelector(state => state.common)
  
  const { columns, setColums } = props;
  const [extendedColumn, setExtendedColumn] = useState([]);
  const [refreshData, setRefreshData] = useState(0)
  ;
  const [visible, setVisible] = useState(false);
  const CheckboxChange = (e, row) => {
    let arr = [...extendedColumn];
    let checkArr = []
    extendedColumn.map((item) => {
      if(item.dataIndex === row.dataIndex){
        item.checked = e.target.checked
      } 
      checkArr.push(item.checked || false)
    })
    setExtendedColumn(arr);
    dispath(ListExpandAdd(checkArr))
    
  };
  useEffect(() => {
    confirm()
    console.log('拓展列初始化获取ListExpand', ListExpand)
  }, [])
  const menu = (
    <div style={{ height: 300, overflowY: "auto", paddingRight: "40px" }}>
      {extendedColumn.map((item, index) => {
        return (
          <div key={index}>
            <Checkbox
              onChange={(e) => CheckboxChange(e, item)}
              name={item.dataIndex}
              checked={item.checked || false}
           
            >
              {item.title}
            </Checkbox>
          </div>
        );
      })}
    </div>
  );
  const confirm = () => {
    let newColumns = [...columns];
    newColumns.forEach((item) => {
      extendedColumn.forEach((ele) => {
        if (item.dataIndex === ele.dataIndex) {
          item.checked = ele.checked;
        }
      });
    });
    setColums(newColumns);
  };
  const onVisibleChange = (visible) => {
    setVisible(visible);
  };
  useEffect(() => {
    let arr = [...columns];
    arr = arr.filter((item) => item.isExtend); // isExtend标识拓展列进行过滤
    arr.forEach((ele, index) => {
      ele.checked = ListExpand !== undefined ? ListExpand[index] || false : false; // 路由刷新后从redux取值赋值恢复状态
    });
    setExtendedColumn(arr);
  }, [columns, refreshData]);
  return (
    <Popconfirm
      onVisibleChange={onVisibleChange}
      overlayClassName="ExtendedColumn"
      placement="bottomRight"
      icon={<></>}
      style={{ paddingLeft: 0 }}
      title={menu}
      okText="确定"
      cancelText="取消"
      onConfirm={confirm}
    >
      {extendedColumn.length ? (
        <a style={{ marginLeft: "20px" }} onClick={() => setRefreshData(refreshData + 1)}>
          <span style={{ marginRight: "4px" }}>扩展列</span>
          {visible ? <UpOutlined /> : <DownOutlined />}
        </a>
      ) : null}
    </Popconfirm>
  );
};
export default ExtendedColumn;

