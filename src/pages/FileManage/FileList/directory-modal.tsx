import { postSeresFileDirectoryCreate, postSeresFileDirectoryUpdate } from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { Form, Input, Modal, ModalProps } from 'antd';
import React, { FC, useCallback, useEffect, useState } from 'react';
import { FILE_BIZ_TYPE_MAP, FileBizType, ROOT_DIRECTORY_ID } from '../constants';

export interface DirectoryModalProps extends Omit<ModalProps, 'onOk'> {
  onSuccess: () => void;
  data?: FileManage.DirectoryDTO;
  /**
   * 业务类型
   */
  bizType: FileBizType;
  /**
   * 目录层级
   */
  level?: number;
  /**
   * 父级目录 ID
   */
  parentDirId?: number;
  /**
   * 父级目录完整路径
   */
  parentDirPath?: string;
}

const DirectorModal: FC<DirectoryModalProps> = ({
  onSuccess,
  data,
  bizType,
  level = 1,
  parentDirId = ROOT_DIRECTORY_ID,
  parentDirPath,
  ...modalProps
}) => {
  const [form] = Form.useForm();
  const [pending, setPending] = useState(false);

  const handleModalOk = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setPending(true);
      if (data) {
        // 编辑
        await postSeresFileDirectoryUpdate({
          id: data.id,
          oldName: data.name,
          newName: values.name,
        });
      } else {
        // 新增
        await postSeresFileDirectoryCreate({
          ...values,
          bizType,
          parentDirId: parentDirId,
        });
      }
      onSuccess();
    } catch (error) {
    } finally {
      setPending(false);
    }
  }, [bizType, data, form, onSuccess, parentDirId]);

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data, form]);

  return (
    <Modal
      maskClosable={false}
      {...modalProps}
      title={!data?.id ? '添加目录' : '编辑目录'}
      forceRender
      afterClose={form.resetFields}
      onOk={handleModalOk}
      confirmLoading={pending}
    >
      <Form {...modalFormLayout} form={form} initialValues={{ bizType, ...data }}>
        <Form.Item label="业务类型">{FILE_BIZ_TYPE_MAP[bizType]}</Form.Item>
        <Form.Item label="目录层级">{level}级</Form.Item>
        <Form.Item label="上级目录">{parentDirPath ? parentDirPath : '无'}</Form.Item>
        <Form.Item name="name" label="目录名称" rules={[{ required: true, message: '请输入目录名称' }]}>
          <Input placeholder="请输入目录名称" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DirectorModal;
