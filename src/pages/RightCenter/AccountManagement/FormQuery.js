import React, { useState,memo, useEffect,useCallback} from 'react'
import { Form, Button, Input, Select, Row, Col ,DatePicker} from 'antd'
import {UpOutlined,DownOutlined} from '@ant-design/icons';
import { useSelector} from 'react-redux'
import {roleJudgment} from '@/utils/authority'
import Item from 'antd/lib/list/Item';
const { Option } = Select
const { RangePicker } = DatePicker;

const FormQuery = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const {onSearch,searchList,defaultQuery,initPage,resetPage} = props
    const [form] = Form.useForm()
    const [formDown,ChangeFormDown] = useState(false)
    const [searchFormList,changeSearchList] = useState([])
    const [isPutItAway,changeIsPutItAway] = useState(true)
    const handleSearch = useCallback(() =>{
        form.validateFields().then(values => {
            values.account && (values.account = values.account.trim());
            values.accountNo && (values.accountNo.trim());
            values.bookOrderNo && (values.bookOrderNo.trim());
            values.formalOrderNo && (values.formalOrderNo.trim());
            values.hwOrderNo && (values.hwOrderNo.trim());
            values.saleOrderNo && (values.saleOrderNo.trim());
            values.deliverOrderNo && (values.deliverOrderNo.trim());
            values.vin && (values.vin.trim());
            values.packNo && (values.packNo.trim());
            values.packNo2 && (values.packNo2.trim());     //630
            values.carConfigName && (values.carConfigName.trim());  // 基础配置
            for(let i in values){
                
                console.log('v', values)
                if(!values[i]){
                    values[i] = null
                }
                if(!i){
                    delete values[i]
            }}
            onSearch(values)
        })
    })
    const onReset = () =>{
        form.resetFields()
        onSearch()
        resetPage()
    }
    const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
    };
    useEffect(()=>{
        searchList.forEach(item=>{
            item.onPressEnter = handleSearch
        })
        let colSpanNum=searchList.reduce((num,item)=>num+item.colSpan,0);
        let ColNum = 24 * Math.ceil(colSpanNum/24) - colSpanNum -  6
        if(colSpanNum === 18){
            changeIsPutItAway(false)
        }else{
            if(ColNum <0){
                searchList.push({colSpan:18})
            }else if(ColNum  === 12){
                searchList.push({colSpan:12})
            }else if(ColNum  === 6){
                searchList.push({colSpan:6})
            }
        }
        changeSearchList(searchList)
    },[handleSearch, searchList])
    useEffect(()=>{
        if(JSON.stringify(defaultQuery) !== '{}'){
            form.setFieldsValue(defaultQuery)
        }
    },[defaultQuery, form])
    return (
        <>
            <Form className='PublicList_FormQuery' form={form} {...layout}>
                <Row style={{paddingRight:'20px',paddingLeft:'0px'}}>
                    {
                        searchFormList.map((item,index)=>{
                            return ( index <=2 &&
                            <Col span={item.colSpan} key={index}>
                                <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []}>
                                    {
                                        item.type === 'Input'?
                                        <Input placeholder={item.placeholder} allowClear onPressEnter={item.onPressEnter} />
                                        :item.type === 'Select' ?
                                        <Select placeholder='请选择' allowClear mode={item.mode || ''}>
                                            {
                                                item.data.map((ele,i)=>{
                                                    return <Option key={i} value={ele.value}>{ele.name}</Option>
                                                })
                                            }
                                        </Select>
                                        :item.type === 'RangePicker' ?
                                        <RangePicker placeholder={item.placeholder} showTime={item.showTime || false} />
                                        :item.type === 'DatePicker' ?
                                        <DatePicker placeholder={item.placeholder} style={{width:'100%'}} showTime={item.showTime || false} />
                                        :null
                                    }
                                </Form.Item>
                            </Col>)
                        })
                    }
                    {
                        searchFormList.map((item,index)=>{
                            return ( index >2 && formDown &&
                            <Col span={item.colSpan} key={index}>
                                <Form.Item label={item.label || ''} name={item.name || ''} rules={item.rules || []}>
                                    {
                                        item.type === 'Input'?
                                        <Input placeholder={item.placeholder} allowClear onPressEnter={item.onPressEnter} />
                                        :item.type === 'Select' ?
                                        <Select placeholder={item.placeholder || '请选择'} 
                                                mode={item.mode}
                                                allowClear 
                                                showSearch={item.search}
												optionFilterProp="value"
												filterOption={(input, option) =>
												option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
												}
                                        >
                                            {
                                                item.data.map((ele,i)=>{
                                                    return <Option key={i} value={ele.value || ele.packNo}>{ele.name}</Option>
                                                })
                                            }
                                        </Select>
                                        :item.type === 'RangePicker' ?
                                        <RangePicker placeholder={item.placeholder} showTime={item.showTime || false} />
                                        :item.type === 'DatePicker' ?
                                        <DatePicker placeholder={item.placeholder} style={{width:'100%'}} showTime={item.showTime || false} />
                                        :null
                                    }
                                </Form.Item>
                            </Col>)
                        })
                    }
                    <Col span={6} className='FormQuerySubmit' style={{paddingLeft:'7%'}}>
                        <Row>
                        { roleJudgment(userInfo,'ACCOUNT_MANAGEMENT_RESET') ?  
                            <Col span={6} className='Reset' offset={6} style={{marginLeft:'10%'}}>
                                <Button onClick={onReset}>重置</Button>
                            </Col>:null
                        }
                          { roleJudgment(userInfo,'ACCOUNT_MANAGEMENT_SEARCH') ? 
                            <Col span={6} className='Search' style={{marginLeft:'8%'}}>
                                <Button type='primary' onClick={handleSearch}>查询</Button>
                            </Col>:null
                        }
                            {
                                isPutItAway?
                                <Col span={6} className='operationButtons' style={{cursor:'pointer',marginLeft:'7%'}}>
                                    <Form.Item>
                                        {
                                            formDown ?
                                            <span style={{color:'#1890ff'}} onClick={()=>{
                                                ChangeFormDown(false)
                                                initPage()
                                                }}>
                                                <span>收起</span><UpOutlined />
                                            </span>
                                            :<span style={{color:'#1890ff'}} onClick={()=>{
                                                ChangeFormDown(true)
                                                initPage(true)
                                            }}>
                                                <span>展开</span><DownOutlined />
                                            </span>
                                        }
                                    </Form.Item>
                                </Col>:null
                            }
                        </Row>
                    </Col>
                </Row>
            </Form>
        </>
    )
}
export default memo(FormQuery)