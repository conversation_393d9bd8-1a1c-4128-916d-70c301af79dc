.blue-table {
  :global {
    .ant-table {
      .ant-table-thead>tr>th {
        background-color: #E2F1FF;
      }
    }
  }
}

.gray-table {
  :global {
    .ant-table {
      .ant-table-thead>tr>th {
        background-color: #EBEFF2;
      }
    }
  }
}

.custom-form-label160 {
  :global {
    .ant-form-item {
      .ant-form-item-label {
        width: 160px;
      }
    }
  }
}

.custom-ant-tabs {
  :global {
    .ant-tabs-nav[role="tablist"]::before {
      border-bottom: 1px solid #D1D6DA
    }

    .ant-tabs-nav[role="tablist"] {
      margin-bottom: 0;

      .ant-tabs-nav-wrap {
        .ant-tabs-tab {
          background: rgba(0, 0, 0, 0.02);
          border-radius: 2px 2px 0px 0px;
          border: 1px solid rgba(0, 0, 0, 0.15);
          color: rgba(0, 0, 0, 0.65);
          border-bottom: 0;
        }

        .ant-tabs-tab.ant-tabs-tab-active {
          background: #fff;
          color: #1890FF;
        }
      }
    }
  }
}

.custom-ant-cascader {
  width: 100%;

  :global {
    .ant-cascader-menu {
      .ant-cascader-menu-item:last-child {
        width: 200px;
      }
    }

    // .ant-cascader-menu:last-child {
    //   width: 200px;
    //   background-color: red;
    // }
  }
}

.common() {
  border-radius: 2px !important;
  margin-right: 6px !important;
}

.ml5 {
  margin-left: 5px !important;
}

.custom-ant-tag-blue {
  background: #E6F7FF !important;
  border: 1px solid #91D5FF !important;
  color: #1890FF !important;
  .common()
}

.custom-ant-tag-green {
  color: rgba(2, 202, 85, 1) !important;
  background: rgba(0, 255, 106, 0.06) !important;
  border: 1px solid rgba(2, 202, 85, 1) !important;
  .common()
}

.custom-ant-tag-yellow {
  color: rgba(168, 107, 8, 1) !important;
  background: rgba(255, 171, 0, 0.06) !important;
  border: 1px solid rgba(168, 107, 8, 1) !important;
  .common()
}

.custom-ant-tag-light-yellow {
  color: rgba(169, 125, 38, 1) !important;
  background: rgba(255, 251, 230, 1) !important;
  border: 1px solid rgba(244, 200, 54, 1) !important;
  .common()
}

.custom-ant-tag-pink {
  color: rgba(168, 107, 8, 1) !important;
  background: rgba(255, 171, 0, 0.06) !important;
  border: 1px solid rgba(168, 107, 8, 1) !important;
  .common()
}

.custom-ant-tag-purple {
  color: rgba(168, 107, 8, 1) !important;
  background: rgba(255, 171, 0, 0.06) !important;
  border: 1px solid rgba(168, 107, 8, 1) !important;
  .common()
}

.custom-ant-tag-grey {
  color: rgba(0, 0, 0, 0.65) !important;
  background: rgba(0, 0, 0, 0.04) !important;
  border: 1px solid rgba(0, 0, 0, 0.65) !important;
  .common()
}

.config-check-group {
  :global {
    .ant-checkbox-wrapper {
      display: inline;
    }

    .ant-checkbox {
      display: none;
    }

    .ant-checkbox+span {
      padding: 0;
    }

    .config-dh:last-child {
      display: none;
    }
  }

}

//表格行为蓝色
.table-row-height72 {
  height: 72px;
}

.table-row-blue {
  background-color: #E6F7FF;
  .table-row-height72();

  :global {

    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      background-color: #E6F7FF;
    }

  }

  &:hover>td {
    background-color: #CDEFFF !important;
  }
}

.table-pad-left {
  padding-left: 25px !important;
}

//表格为青色
.table-row-cyan {
  background-color: #DCFDFF;

  :global {

    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      background-color: #DCFDFF;
    }

  }

  &:hover>td {
    background-color: #DCFDFF !important;
  }
}

//角标
.custom-mark {
  width: 44px;
  height: 44px;
  position: absolute;
  top: 0;
  left: 0;

  &.bg1 {
    background-image: linear-gradient(135deg, #1890FF 50%, rgba(255, 255, 255, 0) 50%);

    .custom-mark-content {
      background-image: linear-gradient(135deg, #1890FF 50%, rgba(255, 255, 255, 0) 50%);
      color: #fff;
    }
  }

  &.bg2 {
    background-image: linear-gradient(135deg, #069DAC 50%, rgba(255, 255, 255, 0) 50%);

    .custom-mark-content {
      background-image: linear-gradient(135deg, #E2FCFF 50%, rgba(255, 255, 255, 0) 50%);
      color: #058A97;
    }
  }

  &.bg3 {
    background-image: linear-gradient(135deg, #EE8E00 50%, rgba(255, 255, 255, 0) 50%);

    .custom-mark-content {
      background-image: linear-gradient(135deg, #EE8E00 50%, rgba(255, 255, 255, 0) 50%);
      color: #fff;

      span {
        transform: rotate(-45deg) scale(.86);
      }
    }
  }

  .custom-mark-content {
    width: 40px;
    height: 40px;
    position: absolute;
    top: 1px;
    left: 1px;
    font-size: 12px;
    overflow: hidden;

    span {
      display: block;
      width: 100%;
      transform: rotate(-45deg) scale(.76);
      text-align: center;
      position: absolute;
      left: 0;
      top: 0;
      transform-origin: 21px 19px;
      white-space: nowrap;
      display: flex;
      justify-content: center;
      font-weight: bold;
    }
  }
}

.more-info-btn {
  position: absolute;
  bottom: 3px;
  left: 50px;

  span {
    margin-left: 3px !important;
  }
}

.globalHide {
  display: none;
}

.blue-Table-nest {
  :global {
    .ant-table {
      padding: 0 !important;
      margin: 0 !important;
      background-color: transparent;

      .ant-table-thead>tr>th {
        background-color: #D6EBFF;
      }

      td {
        background-color: #F1F8FF;
      }
    }
  }
}

.pack-table-merge-column {

  padding-top: 0px;
  padding-bottom: 0px;
  height: 0px;

  :global {
    .pack-table-merge-wrap {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .pack-table-row {
      flex-grow: 1;
      position: relative;

      .detai {
        height: 100%;
        display: flex;
        align-items: center;
        min-height: 75px;
      }

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        height: 1px;
        margin-left: -16px;
        margin-right: -16px;
        background-color: #f0f0f0;
      }
    }
  }

  &.table-pad-left {
    :global {
      .pack-table-row {
        &:not(:last-child)::after {
          margin-left: -26px;
        }
      }
    }
  }
}