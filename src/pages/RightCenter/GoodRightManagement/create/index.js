import React, { useState, memo, useEffect, useRef } from 'react';
import { Modal, Form, Select, Input, message } from 'antd';
import { post, get } from '@/utils/request';
import allUrl from '@/utils/url';
const { Option } = Select;
const CreateForm = props => {
  const { onModalCancel, openModal, onModalSuccess, rightOptions } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const handleOk = () => {
    form
      .validateFields()
      .then(async values => {
        setLoading(true);
        const { success } = await post(allUrl.BoutiqueMaterial.save, { ...values });
        if (success) {
          onModalSuccess();
          message.success('保存成功！');
        }
        setLoading(false);
      })
      .catch(err => {});
  };
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 15 },
  };

  useEffect(() => {
    if (openModal) {
      form.resetFields();
    }
  }, [openModal]);
  return (
    <Modal
      open={openModal}
      onOk={handleOk}
      okText={'保存'}
      onCancel={onModalCancel}
      maskClosable={false}
      width="650px"
      title="精品权益信息"
    >
      <Form form={form} {...layout}>
        <Form.Item
          label="精品权益名称"
          rules={[{ required: true, message: '请输入精品权益名称!' }]}
          name="materialName"
        >
          <Input allowClear placeholder="请输入精品权益名称，注意错别字，建议30字以内" maxLength={30} />
        </Form.Item>
        <Form.Item label="业务编码" name="businessCode" initialValue={552}>
          <Input allowClear disabled />
        </Form.Item>
        <Form.Item label="SE物料编码" name="materialCode" rules={[{ required: true, message: '请输入SE物料编码!' }]}>
          <Input allowClear placeholder="请输入SE物料编码" />
        </Form.Item>
        <Form.Item label="权益分类" name="sortCode" initialValue={'142'}>
          <Select allowClear disabled>
            {rightOptions?.map(item => (
              <Option value={item.code} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default memo(CreateForm);
