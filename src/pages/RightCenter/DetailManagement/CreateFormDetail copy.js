import React, { useMemo, useEffect, useRef, useState, useReducer } from 'react';
import './CreateForm.less';
import { Form, Input, InputNumber, Select, Button, Radio, Space, Modal, message, Table, Row, Col } from 'antd';
import moment from 'moment';
import { get, post, axiosDelete } from '@/utils/request';
import allUrl from '@/utils/url';
import styles from '../antd.module.less';
const { Option } = Select;
const { TextArea } = Input;

const CreateFormDetail = props => {
  const {
    sortOptions,
    typeOptions,
    detailNameOptions,
    pageType,
    getSortList,
    formRef,
    dataSource,
    formConfig,
    setPerformingListObj,
    goodsCategory,
    setGoodsCategory,
  } = props;
  const [form] = Form.useForm();
  const formRefModal = useRef(null);
  const [isModalVisibleDetail, setIsModalVisibleDetail] = useState(false); // 新增权益明细modal
  const [isModalVisible, setIsModalVisible] = useState(false); // 新增分类modal
  const [entryList, setEntryList] = useState({});
  const [businessCodeTypeOptions, setBusinessCodeTypeOptions] = useState([]);
  const [computeTypeOptions, setComputeTypeOptions] = useState([]);
  const [allBusinessCode, setAllBusinessCode] = useState([]);
  const [typeCode, setTypeCode] = useState(''); //权益类别
  const [boutiqueMaterialOptions, setBoutiqueMaterialOptions] = useState([]); //创建精品权益时需要的下拉列表

  // 虚拟类对应业务编码分类【1,2】，实物类对应业务编码分类【3】
  const categoryHash = {
    0: [1, 2],
    1: [3],
  };

  const handleDetailOk = () => {
    let addDetailName = formRefModal.current.getFieldValue(['addDetailName']);
    if (!addDetailName) return message.error('请填写权益明细名称！');
    formRef.current.setFieldsValue({ name: addDetailName });
    setIsModalVisibleDetail(false);
    message.success('新增权益明细成功！');
  };

  const showSampleModal = () => {
    props.showSampleModal && props.showSampleModal();
  };
  // getAllBusinessCode 查询全部业务编码
  const getAllBusinessCode = () => {
    get(allUrl.DetailedManageInterests.getAllBusinessCode).then(res => {
      const { resp, success } = res;
      if (success) {
        setAllBusinessCode(resp);
      }
    });
  };

  //V15权益业务编码 新增业务分类随业务编码选择自动带出
  const onBusinessCode = async value => {
    if (!value) {
      formRef.current.setFieldsValue({ businessCodeType: '' });
      return;
    }
    const { success, resp } = await get(allUrl.DetailedManageInterests.queryByBusinessCode, { businessCode: value });
    if (success) {
      setBusinessCodeTypeOptions(resp || []);
      formRef.current.setFieldsValue({ businessCodeType: resp[0]?.businessCodeType || '' });
    }
  };

  // 权益履约方
  const performingListChangeHandle = (value, option) => {
    let arr = [];
    option &&
      option.map(item => {
        const _numerical = item.value || item.numerical;
        arr.push({
          name: item.label || item.name,
          numerical: _numerical,
          id: dataSource?.performingList?.filter(performingItem => performingItem.numerical == _numerical)[0]?.id,
        });
      });
    setPerformingListObj(arr);
  };

  const handleOk = () => {
    let sortCodeName = formRefModal.current.getFieldValue(['sortCodeName']);
    if (!sortCodeName) return message.error('请填写权益分类名称！');
    post(allUrl.DetailedManageInterests.addSort, { name: sortCodeName }).then(res => {
      if (res.success) {
        message.success('新增分类成功！');
        getSortList().then(res => {
          let target = res.filter(item => item.name === sortCodeName)[0];
          formRef.current.setFieldsValue({ sortCode: target.code });
        });
        setIsModalVisible(false); //增加成功隐藏
      } else {
        // message.error('新增分类失败！')
      }
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const filterperformingList = list => {
    let arr = [];
    if (list) {
      list.map(item => {
        arr.push(item.numerical.toString());
      });
    }
    return arr;
  };

  const onChangeGoodsCategory = e => {
    let value;
    if (Object.prototype.toString.call(e) === '[object Object]') {
      value = e.target.value;
    } else {
      value = e;
    }
    setGoodsCategory(value);
    formRef.current.setFieldsValue({ goodsCategory: value }); // 设置权益商品分类
    formRef.current.setFieldsValue({ [formConfig['typeCode'].name]: undefined }); //设置权益类别
    formRef.current.setFieldsValue({ [formConfig['businessCode'].name]: undefined }); //设置业务编码
    resetGoodsCategoryParams(value);
  };

  //获取创建精品权益时需要的下拉列表
  const getBoutiqueMaterialOptions = async () => {
    const { success, resp } = await get(allUrl.BoutiqueMaterial.selectList);
    if (success) {
      setBoutiqueMaterialOptions(resp);
    }
  };

  useEffect(() => {
    get(allUrl.common.entryLists, {
      codes:
        'equity_car_attr,equity_belong,equity_detail_refund,equity_detail_performance,equity_business_code, equity_performing,equity_repeatable_purchase,equity_goods_list',
    }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }
        setEntryList(Dt || {});
      } else {
        //   message.error(res.message)
      }
    });
    getAllBusinessCode();
    getBoutiqueMaterialOptions();
  }, []);

  // 初始化以及重置一些关于权益商品分类的字段
  const resetGoodsCategoryParams = goodsCategory => {
    // console.log(dataSource);
    if (goodsCategory === 1) {
      // 权益类别需要单独处理（精品权益）
      const types = typeOptions.filter(item => {
        return item.code === '106';
      });
      setComputeTypeOptions(types);
    } else {
      // 虚拟类
      // 权益类别需要单独处理
      const types = typeOptions.filter(item => {
        return item.code !== '106';
      });
      // console.log(typeOptions, types);

      setComputeTypeOptions(types);
    }
  };

  useEffect(() => {
    if (pageType === 'Add' && typeOptions && typeOptions.length) {
      onChangeGoodsCategory(0);
    }
  }, [pageType, typeOptions]);

  useEffect(() => {
    if (dataSource) {
      const performingListValue = filterperformingList(dataSource.performingList);
      formRef.current.setFieldsValue({
        ...dataSource,
        performingList: performingListValue,
      });
      const _goodsCategory = dataSource.goodsCategory;
      // 商品分类设置
      setGoodsCategory(_goodsCategory);
      resetGoodsCategoryParams(_goodsCategory);
      setTypeCode(dataSource.typeCode);
      // 实物类
      if (_goodsCategory === 1) {
        // 权益名需要单独处理一下
        formRef.current.setFieldsValue({
          materialName: dataSource.name,
          name: undefined,
        });
      }

      //业务编码+业务分类
      onBusinessCode(dataSource.businessCode);
      //权益履约方
      performingListChangeHandle(performingListValue, dataSource.performingList);
    }
  }, [dataSource]);

  return (
    <>
      {props.scene === 1 ? (
        <>
          {/* 权益明细名称 - 基础 */}
          <Form.Item
            name="goodsCategory"
            label={formConfig['name'].label}
            className={formConfig['businessCode'].hide ? `${styles.globalHide} prefix-red` : 'prefix-red'}
            style={{ marginBottom: 0 }}
          >
            {/* <Form.Item> */}
            <Radio.Group
              className="create-detail-radiogroup"
              onChange={onChangeGoodsCategory}
              disabled={pageType === 'edit'}
            >
              <Space direction="vertical">
                <div className="create-detail-name" id="name">
                  <Radio value={0}>虚拟类</Radio>

                  <Form.Item
                    name={formConfig['name'].name}
                    style={{ display: 'inline-block', width: 'calc(100% - 200px)' }}
                    rules={[{ required: goodsCategory === 0, message: '请选择权益明细名称!' }]}
                    help={
                      <div className="custom-item-help custom-item-help-mt6">
                        售后权益需选择虚拟类，请选择已有明细，如没有该明细，支持新增，点击新增明细即可
                      </div>
                    }
                  >
                    <Select
                      placeholder="请选择已有明细，如没有该明细，支持新增，点击新增明细即可"
                      allowClear
                      disabled={goodsCategory === 1}
                      showSearch
                      filterOption={(input, option) => {
                        return (option?.value ?? '').toLowerCase().includes(input.toLowerCase());
                      }}
                      getPopupContainer={() => document.getElementById('name')}
                    >
                      {detailNameOptions.map((item, index) => (
                        <Option value={item.name} key={index}>
                          {item.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Button type="primary" onClick={() => setIsModalVisibleDetail(true)} style={{ marginLeft: '8px' }}>
                    新增明细
                  </Button>
                  <Button type="link" onClick={showSampleModal} style={{ color: '#FFA500' }}>
                    示例展示?
                  </Button>
                </div>
                <div className="create-detail-radiogroup" id="materialName">
                  <Radio value={1}>实物类</Radio>
                  <Form.Item
                    name="materialName"
                    style={{ display: 'inline-block', width: 'calc(100% - 175px)' }}
                    rules={[{ required: goodsCategory === 1, message: '请选择权益明细名称!' }]}
                    help={
                      <div className="custom-item-help custom-item-help-mt6">
                        售前权益需选择实物类，如没有实物类名称，请联系管理员维护
                      </div>
                    }
                  >
                    <Select
                      placeholder="请选择实物类权益名称，如有新增实物类名称，请联系管理员维护"
                      allowClear
                      onClick={e => e.stopPropagation()}
                      disabled={goodsCategory === 0 || (pageType === 'edit' && typeCode === '106')}
                      showSearch
                      filterOption={(input, option) => {
                        console.log('o', option, option.children);
                        // 获取显示的文本内容进行匹配
                        const displayText = option?.children || '';
                        // 如果children是数组，则合并为字符串
                        const searchText = Array.isArray(displayText) ? displayText.join('') : displayText.toString();
                        return searchText.toLowerCase().includes(input.toLowerCase());
                      }}
                      getPopupContainer={() => document.getElementById('materialName')}
                    >
                      {boutiqueMaterialOptions.length
                        ? boutiqueMaterialOptions.map((item, index) => (
                            <Option value={item.materialName + '$id$' + item.id} key={index}>
                              {item.materialName}
                              {item.materialCode ? `【${item.materialCode}】` : ''}
                            </Option>
                          ))
                        : ''}
                    </Select>
                  </Form.Item>
                </div>
              </Space>
            </Radio.Group>

            {/* </Form.Item> */}
          </Form.Item>
        </>
      ) : (
        <>
          {/* 权益明细名称 - 延保 */}
          <Form.Item
            label={formConfig['name'].label}
            className={formConfig['businessCode'].hide ? `${styles.globalHide} prefix-red` : 'prefix-red'}
            style={{ marginBottom: 0 }}
          >
            <Form.Item
              name={formConfig['name'].name}
              style={{ display: 'inline-block', width: 'calc(100% - 200px)' }}
              rules={[{ required: true, message: '请选择权益明细名称!' }]}
              help={
                <div className="custom-item-help custom-item-help-mt6">
                  请选择已有明细，如没有该明细，支持新增，点击新增明细即可
                </div>
              }
            >
              <Select placeholder="请填写标题，最多100个字，建议50字以内，不影响页面展示" allowClear>
                {detailNameOptions.map((item, index) => (
                  <Option value={item.name} key={index}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Button type="primary" onClick={() => setIsModalVisibleDetail(true)} style={{ marginLeft: '8px' }}>
              新增明细
            </Button>
            <Button type="link" onClick={showSampleModal} style={{ color: '#FFA500' }}>
              示例展示?
            </Button>
          </Form.Item>
        </>
      )}

      <div id="businessCode">
        <Form.Item
          name={formConfig['businessCode'].name}
          label={formConfig['businessCode'].label}
          rules={[{ required: true, message: 'Please select time!' }]}
          style={{ marginBottom: 0 }}
          className={formConfig['businessCode'].hide ? styles.globalHide : ''}
          help={
            <div className="custom-item-help custom-item-help-mt6">
              {pageType === 'edit' ? '如需变更业务编码，请联系管理员' : '如有新增分类需要联系管理员维护'}
            </div>
          }
        >
          <Select
            placeholder="请选择业务编码"
            disabled={formConfig['businessCode'].disabled || pageType === 'edit'}
            onChange={onBusinessCode}
            allowClear
            showSearch
            filterOption={(input, option) => {
              console.log('option', option);
              // 获取显示的文本内容进行匹配
              const displayText = option?.children || '';
              // 如果children是数组，则合并为字符串
              const searchText = Array.isArray(displayText) ? displayText.join('') : displayText.toString();
              return searchText.toLowerCase().includes(input.toLowerCase());
            }}
            getPopupContainer={() => document.getElementById('businessCode')}
          >
            {goodsCategory != undefined && allBusinessCode.length
              ? allBusinessCode
                  .filter(i => categoryHash[goodsCategory].includes(i.businessCodeType))
                  .map((item, index) => (
                    <Option key={index} value={item.businessCode}>
                      <div className="bussiness-options">
                        {item.businessCodeName}({item.businessCode})
                        <span className="bussiness-description">业务应用：{item.businessUseComment || '-'}</span>
                      </div>
                    </Option>
                  ))
              : null}
          </Select>
        </Form.Item>
      </div>
      <Form.Item
        name={formConfig['businessCodeType'].name}
        rules={[{ required: true, message: '请选业务分类' }]}
        className={formConfig['businessCodeType'].hide ? styles.globalHide : ''}
        label={formConfig['businessCodeType'].label}
      >
        <Select disabled placeholder="业务分类" allowClear>
          {businessCodeTypeOptions.map((item, index) => (
            <Option key={index} value={item.businessCodeType}>
              {item.businessCodeTypeName}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        label={formConfig['performingList'].label}
        help={<div className="custom-item-help custom-item-help-mt6">如有新增权益履约方请联系系统管理维护</div>}
        className={formConfig['performingList'].hide ? styles.globalHide : ''}
        name={formConfig['performingList'].name}
      >
        <Select
          mode="multiple"
          showArrow
          placeholder="请选择权益履约方"
          onChange={(value, option) => performingListChangeHandle(value, option)}
          disabled={formConfig['performingList'].disabled}
          filterOption={(input, option) => (option?.label ?? '').includes(input)}
          allowClear
        >
          {entryList['equity_performing'] &&
            entryList['equity_performing'].map(item => (
              <Option value={item.entryValue} key={item.id} label={item.entryMeaning}>
                {item.entryMeaning}
              </Option>
            ))}
        </Select>
      </Form.Item>
      <Form.Item
        label={formConfig['sortCode'].label}
        style={{ marginBottom: 0 }}
        className={formConfig['performingList'].hide ? `${styles.globalHide} prefix-red` : 'prefix-red'}
      >
        <Form.Item
          name={formConfig['sortCode'].name}
          style={{ display: 'inline-block', width: 'calc(100% - 200px)' }}
          rules={[{ required: true, message: '请选择权益分类!' }]}
          help={
            <div className="custom-item-help custom-item-help-mt6">
              请选择分类，如没有该分类，支持新增，点击新增分类即可
            </div>
          }
        >
          <Select placeholder="超长质保" allowClear disabled={formConfig['sortCode'].disabled}>
            {sortOptions.map(item => (
              <Option value={item.code} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Button type="primary" onClick={showModal} style={{ marginLeft: '8px' }}>
          新增分类
        </Button>
        <Button type="link" onClick={showSampleModal} style={{ color: '#FFA500' }}>
          示例展示?
        </Button>
      </Form.Item>

      <Form.Item
        name={formConfig['typeCode'].name}
        label={formConfig['typeCode'].label}
        rules={[{ required: true, message: '请选择权益类别!' }]}
        className={formConfig['typeCode'].hide ? styles.globalHide : ''}
        help={
          <div className="custom-item-help custom-item-help-mt6">售前权益需选择精品权益，售后权益选择其他类别权益</div>
        }
      >
        <Select
          placeholder="售前权益需选择精品权益，售后权益选择其他类别权益"
          allowClear
          disabled={formConfig['typeCode'].disabled}
        >
          {computeTypeOptions.map(item => (
            <Option value={item.code} key={item.id}>
              {item.name}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name={formConfig['carAttr'].name}
        label={formConfig['carAttr'].label}
        rules={[{ required: true, message: '请选择车辆属性!' }]}
        initialValue={2}
        className={formConfig['carAttr'].hide ? styles.globalHide : ''}
      >
        <Select placeholder="非营运车辆" allowClear disabled={formConfig['carAttr'].disabled}>
          {entryList['equity_car_attr'] && entryList['equity_car_attr'].length
            ? entryList['equity_car_attr'].map((item, index) => (
                <Option key={index} value={Number(item.value)}>
                  {item.name}
                </Option>
              ))
            : null}
        </Select>
      </Form.Item>
      <Form.Item
        name={formConfig['belong'].name}
        label={formConfig['belong'].label}
        rules={[]}
        className={formConfig['belong'].hide ? styles.globalHide : ''}
        help={
          <div className="custom-item-help">
            如需要业务侧进行核销则选择业务侧，如为车辆本身，无需售后配合，请选择车辆本身，例如：赠送轮毂，具体参照事例
          </div>
        }
      >
        <Radio.Group disabled={formConfig['belong'].disabled}>
          {entryList['equity_belong'] && entryList['equity_belong'].length
            ? entryList['equity_belong'].map((item, index) => (
                <Radio key={index} value={Number(item.value)}>
                  {item.name}
                </Radio>
              ))
            : null}
        </Radio.Group>
      </Form.Item>
      <Form.Item
        name={formConfig['statement'].name}
        label={formConfig['statement'].label}
        rules={[]}
        className={formConfig['statement'].hide ? styles.globalHide : ''}
        help={
          <div className="custom-item-help custom-item-help-mt6">
            如配置在声明里的权益策略，例如：一次补胎、两次补漆等 不作为权益策略，均无法进行核销
          </div>
        }
      >
        <TextArea
          rows={4}
          placeholder="此处需要对外展示，建议描述时，谨慎，例如：赛力斯为您所购买的赛力斯汽车提供整车及增程器包修期限为4年不限里程（以先到者为准，运营车辆除外），详见《保修及保养手册》质量担保明细"
          allowClear
        />
      </Form.Item>
      <Form.Item name={formConfig['remark'].name} label={formConfig['remark'].label} rules={[]}>
        <TextArea rows={4} placeholder="例如：如需备注其他内容，在此描述，此描述不对外展示" allowClear />
      </Form.Item>

      <Form.Item
        name={formConfig['refund'].name}
        label={formConfig['refund'].label}
        className={formConfig['refund'].hide ? styles.globalHide : ''}
      >
        <Radio.Group disabled={formConfig['refund'].disabled}>
          {entryList['equity_detail_refund'] && entryList['equity_detail_refund'].length
            ? entryList['equity_detail_refund'].map((item, index) => (
                <Radio key={index} value={Number(item.value)}>
                  {item.name}
                </Radio>
              ))
            : null}
        </Radio.Group>
      </Form.Item>
      <Form.Item
        name={formConfig['performance'].name}
        label={formConfig['performance'].label}
        className={formConfig['performance'].hide ? styles.globalHide : ''}
      >
        <Radio.Group disabled={formConfig['performance'].disabled}>
          {entryList['equity_detail_performance'] && entryList['equity_detail_performance'].length
            ? entryList['equity_detail_performance'].map((item, index) => (
                <Radio key={index} value={Number(item.value)}>
                  {item.name}
                </Radio>
              ))
            : null}
        </Radio.Group>
      </Form.Item>
      {/* {/* {能否重复购买 - 实物类权益隐藏} */}
      <Form.Item
        name={formConfig['isRepeatablePurchase'].name}
        label={formConfig['isRepeatablePurchase'].label}
        className={formConfig['isRepeatablePurchase'].hide || goodsCategory === 1 ? styles.globalHide : ''}
        initialValue={0}
      >
        <Radio.Group disabled={formConfig['isRepeatablePurchase'].disabled}>
          {entryList['equity_repeatable_purchase'] && entryList['equity_repeatable_purchase'].length
            ? entryList['equity_repeatable_purchase'].map((item, index) => (
                <Radio key={index} value={Number(item.value)}>
                  {item.name}
                </Radio>
              ))
            : null}
        </Radio.Group>
      </Form.Item>

      {isModalVisibleDetail && (
        <Modal
          title="新增权益明细"
          visible={isModalVisibleDetail}
          maskClosable={false}
          onOk={handleDetailOk}
          onCancel={() => setIsModalVisibleDetail(false)}
        >
          <Form name="add-detail-name" ref={formRefModal} form={form}>
            <Form.Item
              name="addDetailName"
              label="权益明细名称"
              rules={[]}
              help={
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px', marginBottom: '15px' }}>
                  新增权益明细名称，需要自行配置相关权益明细的核销策略
                </div>
              }
            >
              <Input
                style={{ width: 360 }}
                rules={[{ max: 100, message: '请输入填写标题，最多100个字，建议50字以内，不影响展示' }]}
                placeholder="请输入填写标题，最多100个字，建议50字以内，不影响展示"
                allowClear
              />
            </Form.Item>
          </Form>
        </Modal>
      )}

      {isModalVisible && (
        <Modal
          title="新增权益分类"
          visible={isModalVisible}
          maskClosable={false}
          onOk={handleOk}
          onCancel={handleCancel}
        >
          <Form name="add-sortCode" ref={formRefModal} form={form}>
            <Form.Item
              name="sortCodeName"
              label="权益分类名称"
              rules={[]}
              help={
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px', marginBottom: '15px' }}>
                  支持新增，可能会核销时存在问题，建议联系产品补充逻辑
                </div>
              }
            >
              <Input
                style={{ width: 360 }}
                rules={[{ max: 100, message: '请输入填写标题，最多100个字，建议50字以内，不影响展示' }]}
                placeholder="请输入填写标题，最多100个字，建议50字以内，不影响展示"
              />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
};
export default CreateFormDetail;
