import React, { useState } from 'react'
import { Modal, Checkbox, Row, Col } from 'antd'
import { detailOptions } from '@/utils/checkOptions'

const allCheckedOpt = detailOptions.map(item => item.value);
// const allCheckedOpt = ['detailNo', 'name', 'businessScene', 'performingLists', 'businessCode', 'businessCodeType', 'businessUseComment', 'sortCode', 'typeCode', 'carAttr', 'belong', 'refund', 'performance', 'statement', 'remark', 'createTime', 'limitYear', 'relativeTime', 'limitLegend', 'effectMileage', 'frequency', 'identity', 'carIdentity', 'paintDesc', 'traffic', 'deduction', 'extendWarrantyActiveTime']
const ExportModal = (props) => {
    const [checkedList, setCheckedList] = useState(allCheckedOpt)
    const [indeterminate, setIndeterminate] = useState(false)
    const [checkAll, setCheckAll] = useState(true)
    const [exportDisabled, setExportDisabled] = useState(false)

    const onChange = (list) => {
        setCheckedList(list)
        setIndeterminate(!!list.length && list.length < allCheckedOpt.length)
        setCheckAll(list.length === allCheckedOpt.length)
        setExportDisabled(list.length < 1)
    }
    const onCheckAllChange = (e) => {
        setCheckedList(e.target.checked ? allCheckedOpt : [])
        setIndeterminate(false)
        setCheckAll(e.target.checked)
        setExportDisabled(!e.target.checked)
    }
    const onOk = (data) => {
        setExportDisabled(false)
        props.submitHandle(checkedList)
        onCancel()
    }
    const onCancel = () => {
        props.handleCancel()
        setCheckedList(allCheckedOpt)
        setIndeterminate(false)
        setCheckAll(true)
        setExportDisabled(false)
    }
    return (
        <Modal
            width={900}
            visible={props.showExport}
            onCancel={onCancel}
            title={<div><span style={{ display: 'inline-block', marginRight: '20px' }}>权益明细导出</span><Checkbox indeterminate={indeterminate} checked={checkAll} onChange={onCheckAllChange}>全选</Checkbox></div>}
            onOk={onOk}
            okText="导出"
            okButtonProps={exportDisabled ? { disabled: true } : { disabled: false }}
        >
            <Checkbox.Group style={{ width: '100%' }} onChange={onChange} value={checkedList}>
                <Row>
                    {detailOptions.map(item => {
                        return (
                            <Col span={6} style={{ marginBottom: '20px' }}>
                                <Checkbox value={item.value}>{item.label}</Checkbox>
                            </Col>
                        )
                    })}
                </Row>
            </Checkbox.Group>
        </Modal>
    )
}
export default ExportModal