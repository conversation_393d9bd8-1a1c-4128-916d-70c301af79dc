import './index.less'
import React, { useEffect, useState, forwardRef, useRef } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Row, Col, Button, Divider, message, Modal, Spin, Menu, Dropdown } from 'antd'
import AddOrEditStore from './components/AddOrEditStore'
import SelectOrg from './components/SelectOrg'
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import PublicTable from '@/components/Public/PublicTable'
import TotalNumberOfStores from '@/assets/img/TotalNumberOfStores.png'
import StorePersonnel from '@/assets/img/StorePersonnel.png'
import NoAccountStore from '@/assets/img/NoAccountStore.png'
import { utilsDict } from '@/utils/utilsDict'
import ImportButton from './components/ImportButton'
import Personnel from './components/Personnel'
import ExtendedColumn from '@/components/Public/ExtendedColumn'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import ExportExcel from './components/ExportExcel'
import _ from 'lodash'
import moment from 'moment';
import UploadFile from '@/components/Public/UploadFile'
import ImportResultQuery from './components/ImportResultQuery'
import baseURL from '@/baseURL'
import { roleJudgment } from '@/utils/authority'
import {fileDown} from '@/utils'
const ClueList = () => {
    const tableRef = useRef()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const dispatch = useDispatch()
    const [defaultQuery, setDefaultQuery] = useState({
        businessStatus: "*********"
    })
    const [container, setContainer] = useState(null);
    const [AddOrEditStoreVisible, setAddOrEditStoreVisible] = useState(false)
    const [SelectOrgVisible, setSelectOrgVisible] = useState(false)
    const [AddOrEditStoreTitle, setAddOrEditStoreTitle] = useState('')
    const [cityList, setCityList] = useState([])
    const [personnelVisible, setPersonnelVisible] = useState(false)
    const [type, setType] = useState('')
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [selectedRows, setSelectedRows] = useState([])
    const [rowData, setRowData] = useState({})
    const [storeInfo, setStoreInfo] = useState({})
    const [importLoading, setImportLoading] = useState(false)
    const [tableHeight, setTableHeight] = useState(0)
    const [ExportExcelVisible, setExportExcelVisible] = useState(false)
    const [exportLoading, setExportLoading] = useState(false)
    const [exportDataObj, setExportDataObj] = useState({})
    const [loading, setLoading] = useState(false)
    const [importResultVisible,setImportResultVisible] = useState(false)
    const [columns, setColums] = useState([
        { title: '门店编号', dataIndex: 'dealerCode', width: 120, fixed: 'left' },
        { title: '门店简称', dataIndex: 'name', width: 200, fixed: 'left' },
        { title: '门店名称', dataIndex: 'fullName', width: 260 },
        { title: '所属大区', dataIndex: 'bigAreaName', width: 120 },
        { title: '所属区域', dataIndex: 'middleAreaName', width: 140 },
        { title: '所属省', dataIndex: 'provinceName', width: 120 },
        { title: '所属市', dataIndex: 'cityName', width: 160 },
        { title: '所属区', dataIndex: 'districtName', width: 160 },
        { title: '门店类型', dataIndex: 'dealerType', width: 140, render: text => utilsDict('dealerType', text) },
        { title: '渠道类型', dataIndex: 'channel', width: 100, render: text => utilsDict('channel', text) },
        { title: '合作类型', dataIndex: 'dealerisDirect', width: 100, render: text => utilsDict('dealerisDirect', text) },
        { title: '门店状态', dataIndex: 'dealerStates', width: 100, render: text => utilsDict('dealerStates', text) },
        { title: '营业状态', dataIndex: 'businessStatus', width: 100, render: text => utilsDict('businessStatus', text) },
        { title: '是否可销售', dataIndex: 'isSales', width: 110, isExtend: true, render: text => text ? '是' : '否' },
        { title: '是否有展车', dataIndex: 'exhibitionCars', width: 110, isExtend: true, render: text => text ? '是' : '否' },
        { title: '是否有试驾', dataIndex: 'ifTestDrive', width: 110, isExtend: true, render: text => text ? '是' : '否' },
        { title: '是否可交付', dataIndex: 'isDeliverables', width: 110, isExtend: true, render: text => text ? '是' : '否' },
        { title: '是否可服务', dataIndex: 'isService', width: 110, isExtend: true, render: text => text ? '是' : '否' },
        { title: '创建时间', dataIndex: 'createTime', width: 120, isExtend: true, render: text => text ? moment(text).format('YYYY-MM-DD') : '-' },
        // { title: '申请等级', dataIndex: 'districtName',width:120,checked:false },
        { title: '认定等级', dataIndex: 'level', width: 120, isExtend: true },
        { title: '开始建设时间', dataIndex: 'constructionStartDate', width: 120, isExtend: true },
        { title: '完工时间', dataIndex: 'completionDate', width: 120, isExtend: true },
        // { title: '一验日期', dataIndex: 'firstInspectionDate', width: 120, isExtend: true },
        { title: '一验合格日期', dataIndex: 'firstAcceptanceDate', width: 120, isExtend: true },
        { title: '二验合格时间', dataIndex: 'sececdCheckTime', width: 120, isExtend: true },
        // { title: '综合验收日期', dataIndex: 'comprehensiveInspectionDate', width: 120, isExtend: true },
        { title: '综合合格日期', dataIndex: 'comprehensiveAcceptanceDate', width: 150, isExtend: true },
        { title: '项目状态', dataIndex: 'storeProjectStatusName', width: 120, isExtend: true },
        { title: '项目经理', dataIndex: 'managerUserName', width: 120, isExtend: true },
        { title: '监控设备', dataIndex: 'isMonitoringEquipment', width: 100, isExtend: true, render: text => text === 'Y' ? '有' : '无' },
        { title: '总经理', dataIndex: 'generalManager', width: 120, isExtend: true },
        { title: '电话', dataIndex: 'phone', width: 120, isExtend: true },
        { title: '门店备案邮箱', dataIndex: 'email', width: 120, isExtend: true },
        { title: '所属组织', dataIndex: 'belongOrganizationName', width: 200, fixed: 'right' },
        { title: '操作', width: 180, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
    ])
    const onSearch = (values) => {
        if (values.createTime && values.createTime.length) {
            values.createTimeStart = moment(values.createTime[0]).format('YYYY-MM-DD')
            values.createTimeEnd = moment(values.createTime[1]).format('YYYY-MM-DD')
            delete values.createTime
        }
        setDefaultQuery(values)
        rowSelectionChange({ selectedRowKeys: [], selectedRows: [] })
    }
    const RowAdd = () => {

    }
    const RowEdit = (record) => {
        setRowData(record)
        setType('Edit')
        setAddOrEditStoreVisible(true)
        setAddOrEditStoreTitle('编辑门店')
    }
    const RowDetail = (record) => {
        setRowData(record)
        setType('LookAt')
        setAddOrEditStoreVisible(true)
        setAddOrEditStoreTitle('门店详情')
    }
    const RowPersonnelDetail = (record) => {
        setRowData(record)
        setPersonnelVisible(true)
    }
    const rowSelectionChange = ({ selectedRowKeys, selectedRows }) => {
        console.log(selectedRowKeys, selectedRows)
        setSelectedRowKeys(selectedRowKeys)
        setSelectedRows(selectedRows)
    }
    const selectOrg = () => {
        let nameS1 = []
        let nameS2 = []
        selectedRows.forEach(item => {
            if (item.belongOrganizationName) {
                nameS1.push(item.name)
            } else {
                nameS2.push(item.name)
            }
        })
        if (nameS1.length) {
            Modal.confirm({
                content: `”${nameS1.join('、')}“已有组织，是否替换？`,
                onOk() {
                    setSelectOrgVisible(true)
                },
                onCancel() {
                    return false
                },
            });
        } else {
            setSelectOrgVisible(true)
        }
    }
    const download = () => {
        let url = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/template/门店人员培训认证及记录导入模板v2.14.1.xlsx'
        window.open(url)
        // post(allUrl.StoreManage.template, {}, {
        //     headers: {
        //         'Content-Type': "application/x-www-form-urlencoded;charset=UTF-8"
        //     },
        //     responseType: "blob"
        // }).then(res => {
        //     if (res?.type) {
        //         let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        //         if (window.navigator.msSaveOrOpenBlob) {
        //             //兼容ie
        //             window.navigator.msSaveBlob(blob, '门店人员培训认证及记录导入模板.xlsx');
        //         } else {
        //             let downloadElement = document.createElement('a');
        //             let href = window.URL.createObjectURL(blob); //创建下载的链接
        //             downloadElement.href = href;
        //             downloadElement.download = '门店人员培训认证及记录导入模板' + '.xlsx'; //下载后文件名
        //             document.body.appendChild(downloadElement);
        //             downloadElement.click(); //点击下载
        //             document.body.removeChild(downloadElement); //下载完成移除元素
        //             window.URL.revokeObjectURL(href); //释放掉blob对象
        //         }
        //         message.success('模板下载成功！')
        //     } else {
        //         message.error(res.msg)
        //     }
        // })
    }
    const exportExcel = () => {
        setExportLoading(true)
        setLoading(true)
        post(allUrl.StoreManage.exportStoreQuery, { ...defaultQuery }).then(res => {
            if (res.success) {
                setExportDataObj(res.resp[0])
                setExportExcelVisible(true)
            } else {
                // message.error(res.msg)
            }
            setExportLoading(false)
            setLoading(false)
        })
    }

    const exportExcelToTrainingInfo = () =>{
        setLoading(true)
        post(allUrl.StoreManage.exprotStoreUser, { },{responseType: "blob"}).then(res => {
            fileDown(res,'门店人员认证等级及培训信息导出')
            setLoading(false)
        })
    }
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo, 'PERM_STORELIST_EDIT') ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowEdit(record)}>编辑</span>,
                        <Divider key={1} type="vertical" />,
                    ]
                    : null
            }
            {
                roleJudgment(userInfo, 'PERM_STORELIST_VIEW') ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowDetail(record)}>详情</span>,
                        <Divider key={1} type="vertical" />,
                    ] : null
            }
            {
                roleJudgment(userInfo, 'PERM_STORELIST_PERSONNEL_VIEW') ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowPersonnelDetail(record)}>人员查看</span>,
                    ] : null
            }
            {/* {
                roleJudgment(userInfo,'PERM_STORELIST_DETAIL') ?
                    [
                        <Divider key={1} type="vertical" />,
                        <Popconfirm key={2} onConfirm={() => this.RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                            <span style={{ cursor: 'pointer' }}>删除</span>
                        </Popconfirm>
                    ] : null
            } */}
        </div>
    }

    const UploadChange = ({ file, fileList }, type) => {
        console.log(file, type)
        const { response } = file
        if (file.status === 'uploading') {
            setImportLoading(true)
        }
        if (file.status === 'done') {
            if (response.success) {
                message.success('文件上传完成，可点击“导入结果”查看任务详情')
                tableRef.current.getTableData()
            } else {
                message.error(response.msg || response.msgCode)
            }
            setImportLoading(false)
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msgCode)
            setImportLoading(false)
        }
    }

    let searchList = [
        { label: '门店编码', name: 'dealerCode', type: 'Input', colSpan: 6 },
        { label: '门店简称', name: 'name', type: 'Input', colSpan: 6 },
        { label: '渠道类型', name: 'channel', type: 'Select', colSpan: 6, data: [{ name: '金康', value: '0' }, { name: '华为', value: '1' }, { name: '其他', value: '2' }] },
        // { label: '所属城市', name: 'cityCode', type: 'Select', showSearch: true, colSpan: 6, data: cityList },
        { label: '合作类型', name: 'dealerisDirect', type: 'Select', colSpan: 6, data: [{ name: '直营', value: '0' }, { name: '经销', value: '2' }] },
        {
            label: '营业状态', name: 'businessStatus', type: 'Select', colSpan: 6, data: [
                { name: '营业中', value: '*********' },
                { name: '停业', value: '899030001' },
                { name: '搬迁', value: '899030002' },
                { name: '改建', value: '899030003' },
                { name: '在建', value: '102910000' },
                { name: '拟退网', value: '102910001' },
            ], initialValue: '*********'
        },
        {
            label: '门店类型', name: 'dealerType', type: 'Select', colSpan: 6, data: [
                { name: '体验中心', value: '1' },
                { name: '用户中心', value: '2' },
                { name: '兼营服务中心', value: '3' },
                { name: '专营服务中心', value: '4' },
            ]
        },
        { label: '所属大区', name: 'bigAreaName', type: 'Input', colSpan: 6, },
        { label: '所属区域', name: 'middleAreaName', type: 'Input', colSpan: 6, },
        { label: '所属省', name: 'provinceName', type: 'Input', colSpan: 6, },
        { label: '所属市', name: 'cityName', type: 'Input', colSpan: 6, },
        { label: '创建时间', name: 'createTime', type: 'RangePicker', colSpan: 6, },

    ]

    const getCheckboxProps = record => {
        return { disabled: record.dealerCode ? false : true }
    }

    const initPage = () => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let FormQueryH = document.getElementsByClassName('PublicList_FormQuery')[0].clientHeight
        let h = winH - FormQueryH - 47 - 54 - 410
        setTableHeight(h)
    }

    useEffect(() => {
        // onSearch()
        initPage()
    }, [])
    useEffect(() => {
        get(allUrl.StoreManage.cityList, null).then(res => {
            if (res.success) {
                res.resp.forEach(item => {
                    item.value = item.cityCode
                    item.name = item.displayName
                })
                setCityList([...res.resp])
            } else {
                // message.error(res.message)
            }
        })
    }, [])
    useEffect(() => {
        get(allUrl.StoreManage.statistics).then(res => {
            if (res.success) {
                setStoreInfo(res.resp[0])
            } else {
                // message.error(res.msg)
            }
        })
    }, [])

    useEffect(() => {
        if (!Object.keys(defaultQuery).length) {
            setDefaultQuery({
                businessStatus: '*********'
            })
        }
    }, [defaultQuery])

    const buttonMenu = () => {
        return <Menu>
            {
                roleJudgment(userInfo, 'PERM_STORELIST_EXPORT') ?
                <Menu.Item key='1'>
                    {/* <div onClick={exportExcel}>门店人员信息导出</div> */}
                </Menu.Item>:null
            }
            {
                roleJudgment(userInfo, 'PERM_STORELIST_EXPORT_TRAINING_INFO') ?
                <Menu.Item key='2'>
                    <div onClick={exportExcelToTrainingInfo}>门店人员培训记录导出</div>
                </Menu.Item>:null
            }
        </Menu>
    }

    let newColumns = _.cloneDeep({ columns }).columns
    for (let i = 0; i < newColumns.length; i++) {
        if (JSON.stringify(newColumns[i]['checked']) !== undefined && !newColumns[i].checked) {
            newColumns.splice(i, 1)
            i--
        }
    }

    const updateList = () => {
        message.info('门店数据更新中，请 10 分钟后搜索验证。')
        get(allUrl.StoreManage.syncAllStore)
    }

    return (
        <div className='StoreList'>
            <Spin spinning={loading}>
                <Row className='InStoreInfo'>
                    <Col span={6}>
                        <div className='InStoreInfoLeft'>
                            <img src={TotalNumberOfStores} />
                        </div>
                        <div className='InStoreInfoRight'>
                            <div className='title'>营业门店总数 (间)</div>
                            <span className='num'>{storeInfo?.storeTotal || 0}</span>
                            <div className='classification'>
                                <div className='jk'>金康<span className='jkNum'>{storeInfo.seresStoreTotal}</span></div>
                                <div className='hw'>华为<span className='hwNum'>{storeInfo.huaWeiStoreTotal}</span></div>
                            </div>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div className='InStoreInfoLeft'>
                            <img src={StorePersonnel} />
                        </div>
                        <div className='InStoreInfoRight'>
                            <p className='title'>营业门店人员总数 (人)</p>
                            <span className='num'>{storeInfo?.storeUserTotal || 0}</span>
                            <div className='classification'>
                                <div className='jk'>金康<span className='jkNum'>{storeInfo.seresStoreUserTotal}</span></div>
                                <div className='hw'>华为<span className='hwNum'>{storeInfo.huaWeiStoreUserTotal}</span></div>
                            </div>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div className='InStoreInfoLeft'>
                            <img src={NoAccountStore} />
                        </div>
                        <div className='InStoreInfoRight'>
                            <p className='title'>无SERES协同系统账号门店总数 (间)</p>
                            <span className='num'>{storeInfo?.withoutUserStoreNum || 0}</span>
                            <div className='classification'>
                                <div className='jk'>金康<span className='jkNum'>{storeInfo.seresWithoutUserStoreNum}</span></div>
                                <div className='hw'>华为<span className='hwNum'>{storeInfo.huaWeiWithoutUserStoreNum}</span></div>
                            </div>
                        </div>
                    </Col>
                </Row>
                <PublicTableQuery isFormDown={false} onSearch={onSearch} searchList={searchList} />
                <div className='tableData'>
                    <Row className='tableTitle'>
                        <Col className='text'>门店列表</Col>
                        <Col className='bts'>
                            {
                                roleJudgment(userInfo, 'PERM_STORELIST_HANDLEUPDATE') ?
                                    <Button type='primary' onClick={updateList}>手动更新</Button>
                                    : null
                            }
                        
                            {/* {
                                roleJudgment(userInfo, 'PERM_STORELIST_SELECTORG') ?
                                    <Button type='primary' onClick={selectOrg} disabled={!selectedRows.length}>选择组织</Button>
                                    : null
                            } */}
                            {
                                !roleJudgment(userInfo, 'PERM_STORELIST_EXPORT') && !roleJudgment(userInfo, 'PERM_STORELIST_EXPORT_TRAINING_INFO') ?null
                                :<Dropdown overlay={buttonMenu} arrow>
                                    <Button loading={exportLoading}>导出</Button>
                                </Dropdown>
                            }
                            {
                                roleJudgment(userInfo, 'PERM_STORELIST_IMPORT_TRAINING_RECORDS') ?
                                <>
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={10}
                                        action={baseURL.Host + allUrl.StoreManage.importExcel}
                                        UploadChange={UploadChange}
                                    >
                                        <Button loading={importLoading}>导入</Button>
                                    </UploadFile> 
                                    <a onClick={download}>模版下载</a>
                                </>:null
                            }
                            {/* {
                                roleJudgment(userInfo, 'PERM_STORELIST_EXPORT') ?
                                    <Button onClick={exportExcel} loading={exportLoading}>导出</Button>
                                    : null
                            } */}
                            
                            {/* {
                                roleJudgment(userInfo, 'PERM_STORELIST_IMPORT') ?
                                    <>
                                        <ImportButton
                                            url={'/store/v1/ports/importStoreInfo'}
                                            loading={importLoading}
                                            UploadChange={UploadChange}
                                        >
                                            导入
                                        </ImportButton>
                                        <a onClick={download}>模版下载</a>
                                    </>
                                    : null
                            } */}
                            {
                                roleJudgment(userInfo, 'PERM_STORELIST_IMPORT_RESULT') ?
                                <a style={{marginLeft:10}} onClick={()=>setImportResultVisible(true)}>导入结果</a>:null
                            }

                            <ExtendedColumn setColums={setColums} columns={columns} />
                        </Col>
                    </Row>
                    <PublicTable sticky={true} ref={tableRef} getCheckboxProps={getCheckboxProps} columns={newColumns} defaultQuery={defaultQuery} url={allUrl.StoreManage.list} rowSelectionChange={rowSelectionChange} />
                </div>
                {
                    AddOrEditStoreVisible &&
                    <AddOrEditStore getTableData={tableRef ? tableRef.current.getTableData : null} type={type} rowData={rowData} title={AddOrEditStoreTitle} visible={AddOrEditStoreVisible} onCancel={() => setAddOrEditStoreVisible(false)} />
                }
                {
                    SelectOrgVisible &&
                    <SelectOrg getTableData={tableRef ? tableRef.current.getTableData : null} selectedRows={selectedRows} title={'选择组织'} visible={SelectOrgVisible} onCancel={() => setSelectOrgVisible(false)} />
                }
                {
                    personnelVisible &&
                    <Personnel rowData={rowData} title={'人员查看'} visible={personnelVisible} onCancel={() => setPersonnelVisible(false)} />
                }
                {
                    ExportExcelVisible &&
                    <ExportExcel visible={ExportExcelVisible} defaultQuery={defaultQuery} onCancel={() => setExportExcelVisible(false)} exportDataObj={exportDataObj} />
                }
                {
                    importResultVisible &&
                    <ImportResultQuery visible={importResultVisible} title='导入结果查看' onCancel={()=>setImportResultVisible(false)} />
                }
            </Spin>
        </div>
    )
}
export default ClueList
