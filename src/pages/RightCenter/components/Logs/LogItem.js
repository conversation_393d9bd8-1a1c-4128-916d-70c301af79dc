import React, { useEffect, useState } from 'react'
import moment from 'moment'


const LogItem = (props) => {
    const { type, children, item, operatemap } = props
    return <div className='log-item'>
        <div className='log-time'>{item.operTime ? moment(item.operTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
        <div className='log-icon'></div>
        <div className='log-content'>
            <div>
                <span>操作人：<span>{item.operName || '-'}</span></span>
            </div>
            <div>
                <span>操作：<span>{item.operType ? operatemap[type][item.operType] : '-'}</span></span>
            </div>
            {children(item)}
        </div>
    </div>
}
export default LogItem