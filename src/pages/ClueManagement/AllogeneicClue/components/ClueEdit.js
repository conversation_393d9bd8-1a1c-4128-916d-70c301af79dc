import React from 'react'
import { Modal, message, Form, Input,Select,Row,Col} from 'antd'
import allUrl from '../../../../utils/url'
import { post, get } from '../../../../utils/request'
import { connect } from 'react-redux'
const {Option} = Select
const { TextArea } = Input;
class ClueEdit extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {

        }
    }
    formRef = React.createRef();
    handleOk = () => {
        const { onCancel ,getData,recordData} = this.props
        this.formRef.current.validateFields().then(values => {
            if (values) {
                let data = {
                    status: values.status,
                    orderStatus: values.orderStatus,
                    settleStatus: values.settleStatus,
                    externalNo: values.externalNo,
                    remark:values.remark,
                    id:recordData.id
                }
                post(allUrl.ClueManagement.updateClueObj,{...data}).then(res=>{
                    if(res.success){
                        message.success(res.msg)
                        onCancel()
                        getData()
                    }else{
                        message.error(res.msg)
                    }
                })
                
            }
        })
    }

    ininForm = () => {
        const { recordData } = this.props
            this.formRef.current.setFieldsValue({
                status: recordData.status,
                orderStatus: recordData.orderStatus,
                settleStatus: recordData.settleStatus,
                externalNo: recordData.externalNo,
                remark: recordData.remark,
            })

    }

    render() {
        const { visible, title,onCancel,recordData } = this.props
        const { loading } = this.state
        console.log(this.state.current)
        const layout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 20 },
        };
            return (
                <Modal
                    visible={visible}
                    title={title}
                    onOk={this.handleOk}
                    onCancel={onCancel}
                    maskClosable={false}
                    width={800}
                    // bodyStyle={{ padding: '50px' }}

                >
                    <Form  {...layout} name="vertical" onFinish={this.onFinish} ref={this.formRef}>
                        <Row>
                        <Col span={12}>
                            <Form.Item label="线索状态" name="status">
                                 <Select placeholder='请选择...' allowClear>
                                    <Option value={1}>待分配</Option>
                                    <Option value={2}>已分配</Option>
                                </Select>
                            </Form.Item>
                            </Col>
                            <Col span={12}>
                            <Form.Item label="订单交付状态"  name="orderStatus">
                                <Select placeholder='请选择...' allowClear>
                                    <Option value={1}>待交付</Option>
                                    <Option value={2}>已交付</Option>
                                </Select>
                            </Form.Item>
                            </Col>
                            <Col span={12}>
                            <Form.Item label="结算状态"  name="settleStatus">
                                <Select placeholder='请选择...' allowClear>
                                    <Option value={1}>待结算</Option>
                                    <Option value={2}>已结算</Option>
                                </Select>
                            </Form.Item>
                            </Col>
                            <Col span={12}>
                            <Form.Item label="外部关联订单号" name="externalNo">
                                <Input placeholder='请输入...' allowClear  />
                            </Form.Item>
                            </Col>
                            <Col span={24}>
                            <Form.Item label="备注" name="remark">
                                <TextArea />
                            </Form.Item>
                            </Col>
                            </Row>
                        </Form>
                </Modal>
            )
        }
        componentDidMount(){
            this.ininForm()
        }
    }
    export default connect()(ClueEdit)