import React from 'react'
import { message, But<PERSON>, Row, Col, Form, Table, Modal } from 'antd'
import { connect } from 'react-redux'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get ,post} from '../../../utils/request'
import allUrl from '../../../utils/url'
import RebateSettings from './components/RebateSettings'
import '../index.less'
import moment from 'moment'
import {roleJudgment} from '@/utils/authority'

class Detail extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            loading: false,
            locationParmas: {},
            kjym: [

            ],
            treeData: [

            ],
            treeselectedKeys: [],
            treeSelectedRows: [],
            permissionIds: [],
            Type: '',
            formData: {},
            FlowInfoVisible: false,
            dataSource: []
        }
        this.id = 0
    }
    formRef = React.createRef();

    onCancel = () => {
        const { handleDrawerCancel } = this.props
        if (handleDrawerCancel) {
            handleDrawerCancel()
        } else {
            history.goBack()
        }
    }
    CheckboxChange = (e, row) => {
        let { permissionIds } = this.state
        if (e.target.checked) {
            if (permissionIds.indexOf(e.target.value) > -1) { //存在
                // permissionIds.remove(e.target.value)
            } else {  //不存在
                permissionIds.push(e.target.value)
            }
        } else {
            permissionIds = permissionIds.filter(item => item !== e.target.value)
        }
        this.setState({ permissionIds })
    }

    treeSelect = (selectedKeys, selectedNodes, event) => {
        this.setState({
            treeselectedKeys: selectedKeys,
            treeSelectedRows: selectedNodes.selectedNodes,
            kjym: selectedNodes.node.functionChildren
        })
    }

    AddFlowInfo = () => {
        get(allUrl.orderTimeOut.OvertimeOrderGetCheck, { orderNumber: this.id }).then(res => {
            if (res && res.success) {
                this.setState({ FlowInfoVisible: true })
            } else {
                Modal.info({
                    title: res.msg,
                })
            }
        })
    }
    RebateSettingsCB = () => {
        this.getData(this.id)
    }
    Export = () =>{
        this.setState({loading:true},()=>{
            post(allUrl.orderTimeOut.exportDetail + '?orderNumber=' + this.id,{},{
                headers:{
                    'Content-Type': "application/x-www-form-urlencoded;charset=UTF-8"
                },
                responseType: "blob"
            }).then(res=>{
                console.log(res)
                this.setState({loading:false})
                if(res){
                    let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                    if (window.navigator.msSaveOrOpenBlob) {
                      //兼容ie
                      window.navigator.msSaveBlob(blob, '返利明细信息.xlsx');
                    } else {
                      let downloadElement = document.createElement('a');
                      let href = window.URL.createObjectURL(blob); //创建下载的链接
                      downloadElement.href = href;
                      downloadElement.download = '返利明细信息' + '.xlsx'; //下载后文件名
                      document.body.appendChild(downloadElement);
                      downloadElement.click(); //点击下载
                      document.body.removeChild(downloadElement); //下载完成移除元素
                      window.URL.revokeObjectURL(href); //释放掉blob对象
                    }
                    message.success('返利明细信息下载成功！')
                }
            })
        })
    }
    render() {
        const { locationParmas, dataSource } = this.state
        const { userInfo } = this.props
        const formItemLayout = {
            labelCol: { span: 14 },
            wrapperCol: { span: 14 },
        }
        const InforData = {
            rowKey: record => record.id,
            bordered: true,
            dataSource,
            columns: [
                // { title: '序号', dataIndex: 'index',width:80,render:(text,record,index)=><div style={{textAlign:'center'}}>{(pageSize * (pageIndex+1))- pageSize +  index+1}</div>},
                { title: '序号', dataIndex: 'index', fixed: 'left', width: 70, render: (text, record, index) => <div style={{ textAlign: 'center' }}>{index + 1}</div> },
                { title: '时间', dataIndex: 'createTime', width: 120, render: text => text ? moment(text).format('YYYY-MM-DD') : '' },
                { title: '资金状态', dataIndex: 'fundStateName', width: 100 },
                { title: '金额', dataIndex: 'moneyAmount', width: 120 },
                { title: '总额', dataIndex: 'moneyTotal', width: 120 }
            ],
            pagination: false,
        };
        return (
            <div className='PublicDetail'>
                <div className='DetailBox'>
                    {
                        locationParmas.title &&
                        <div className='DetailTitle'>{locationParmas.title}</div>
                    }
                    <div className='DetailCon'>
                        <Form
                            {...formItemLayout}
                            layout={'vertical'}
                            ref={this.formRef}
                            initialValues={{ remember: true, }}
                        >
                            <div className='FormRow'>
                                <p className='FormRowTitle'>返利明细信息</p>
                                <Row className='FormRowCon'>
                                    <Table {...InforData} />
                                </Row>
                                {/* <Row style={{ padding: '20px 0', textAlign: 'center' }}>
                                    {
                                        roleJudgment(userInfo,'PERM_OVERTIMEORDER_BATCHADD') ?
                                            <Col span={6}><Button onClick={this.AddFlowInfo}>增加流水信息</Button></Col>
                                            : null
                                    }
                                </Row> */}
                                <Row style={{ padding: '20px 0', textAlign: 'center' }}>
                                    {
                                        roleJudgment(userInfo,'PERM_OVERTIMEORDER_EXPORT') ?
                                            <Col span={10}><Button onClick={this.Export}>导出流水</Button></Col>
                                            : null
                                    }
                                </Row>
                            </div>
                        </Form>
                    </div>
                </div>
                <div className='DetailBtns'>
                    {/* <Button type="primary" onClick={this.Save} style={{ marginRight: '10px' }} onClick={() => this.Save()}>确定</Button> */}
                    <Button onClick={this.onCancel}>返回</Button>
                </div>
                {
                    this.state.FlowInfoVisible &&
                    <RebateSettings
                        visible={this.state.FlowInfoVisible}
                        title={'返利设置'}
                        onCancel={() => this.setState({ FlowInfoVisible: false })}
                        onCk={() => this.setState({ FlowInfoVisible: false })}
                        locationParmas={locationParmas}
                        cb={this.RebateSettingsCB}
                    />
                }
            </div>
        )
    }
    componentDidMount() {
        let locationParmas = this.props.match.params.data ? JSON.parse(DecryptByAES(this.props.match.params.data)) : {}
        this.id = locationParmas.id
        this.setState({ locationParmas, Type: locationParmas.type }, () => {
            if (locationParmas.id) {
                this.getData(locationParmas.id)
            }
        })
    }
    getData = (id) => {
        get(allUrl.orderTimeOut.OvertimeOrderGetDetail, { orderNumber: id }).then(res => {
            if (res && res.success) {
                this.setState({
                    dataSource: res.resp
                })
            } else {
                // message.error(res.msg || '获取失败！')
            }
        })
    }
}
export default connect(({ common }) => {
    const { userInfo } = common
    return { userInfo }
})(Detail)
