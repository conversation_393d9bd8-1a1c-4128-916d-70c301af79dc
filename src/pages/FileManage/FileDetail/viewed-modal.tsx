import ProTable from '@/components/pro-table';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { downloadBlob } from '@/utils/file';
import { Modal, ModalProps } from 'antd';
import React, { FC, useCallback, useState } from 'react';
import { useParams } from 'react-router';
import { OperatedQueryType } from '../constants';
import { useDownloadFileIncompleteApi, useStatisticDetailTableViewed, useViewFileIncompleteApi } from './hooks';

export interface ViewedModalProps extends ModalProps {
  orgCode?: string;
  file?: FileManage.FileItemDetailVO;
}

const ViewedModal: FC<ViewedModalProps> = ({ file, orgCode, ...props }) => {
  const [downloading, setDownloading] = useState(false);
  const { id } = useParams<{ id: string }>();

  const columns = useStatisticDetailTableViewed(file);
  const inCompleteDownloadRequest = useDownloadFileIncompleteApi(file);
  const handleExportClick = useCallback(async () => {
    try {
      setDownloading(true);
      const blob = await inCompleteDownloadRequest(
        { fileId: id as unknown as number, queryType: OperatedQueryType.VIEWED, organizeCode: orgCode },
        {
          responseType: 'blob',
        }
      );
      const filename = file?.name?.split('.')[0] ?? '';
      downloadBlob(blob as unknown as Blob, `${filename}已浏览人员明细数据.xlsx`);
    } catch (error) {
    } finally {
      setDownloading(false);
    }
  }, [file, id, orgCode]);

  const inCompleteListRequest = useViewFileIncompleteApi(file);
  const unDownloadTableRequest = usePaginationTableListStyleRequest(async ({ current, pageSize, params }) =>
    inCompleteListRequest({
      fileId: id as unknown as number,
      pageNum: current,
      pageSize,
      queryType: OperatedQueryType.VIEWED,
      ...params,
    })
  );
  return (
    <Modal maskClosable={false} width={1000} footer={null} title="文件已浏览人员明细" {...props}>
      <ProTable
        columns={columns}
        ghost
        rowKey={'index'}
        params={{ organizeCode: orgCode }}
        request={unDownloadTableRequest}
        actions={[
          {
            children: '导出数据',
            type: 'primary',
            onClick: handleExportClick,
            loading: downloading,
          },
        ]}
      />
    </Modal>
  );
};

export default ViewedModal;
