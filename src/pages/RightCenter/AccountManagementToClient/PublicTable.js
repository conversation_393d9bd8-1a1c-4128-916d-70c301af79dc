import React, { useState, useEffect, useImperativeHandle, forwardRef, memo, useCallback, useMemo } from 'react'
import { Empty, Table } from 'antd'
import { setStorage, getStorage } from '@/utils/Storage'
import * as request from '@/utils/request'

const getPageStorage = (key) => {
    let res = getStorage(window.location.hash, true)
    return res && res[key] ? res[key] : null
}
const PublicTable = forwardRef((props, ref) => {
    const {
        columns,
        defaultQuery,
        url,
        rowSelectionChange = () => { },
        scroll,
        rowSelection,
        radio,
        getCheckboxProps,
        method,
        sticky,
        rowKey,
        isCatch,
        className,
    } = props
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const [current, setCurrent] = useState(isCatch && getPageStorage('pagination') ? getPageStorage('pagination').current : 1)
    const [pageSize, setPageSize] = useState(isCatch && getPageStorage('pagination') ? getPageStorage('pagination').pageSize : 10)
    const [total, setTotal] = useState(0)
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [selectedRows, setSelectedRows] = useState([])
    const PageChange = (current, pageSize) => {
        setCurrent(current)
        setPageSize(pageSize)
        if (isCatch) {
            // 翻页是也存储查询参数
            let obj = getStorage(window.location.hash, true) || {}
            obj = { ...obj, ...{ pagination: { current, pageSize }, Type: window.location.hash } }

            setStorage(obj)
        }
    }

    //是否初始化查询 或 重置
    const isFlag = () => {
        const tmpl = Object.keys(defaultQuery)
        //初始化
        const isInit = tmpl.length === 0
        //重置
        const isReset = tmpl.length === 1 && tmpl[0] === '_t'
        //详情页返回，列表页是否保留了之前的查询参数
        const isBackByParams = getPageStorage('Query') !== null
        return isInit && !isBackByParams || isReset
    }

    const getTableData = async () => {
        //点击查询或者重置恢复到第一页
        setLoading(true)
        const queryParams = isCatch && getPageStorage('Query') ? getPageStorage('Query') : defaultQuery
        let params = { pageSize, pageNum: current, ...queryParams }
        request[method || 'post'](url, { ...params }).then(res => {
            if (res.success) {
                if (res.resp) {
                    res.resp.map((item, index) => item.key = index + 1)
                    setDataSource(res.resp)
                } else {
                    setDataSource([])
                }
                setTotal(res.total || 0)
                setCurrent(current)
            } else {
                setCurrent(1)
                // message.error(res.msg)
            }
            setLoading(false)
        }).catch(() => {
            setCurrent(1)
            setLoading(false)
        })

        if (rowSelection === false) {

        } else {
            setSelectedRowKeys([])
            setSelectedRows([])
            rowSelectionChange({ selectedRowKeys: [], selectedRows: [] })
        }
    }

    useEffect(() => {
        if (Object.keys(defaultQuery).indexOf('_t') !== -1) {
            if (current !== 1) {
                setCurrent(1)
            } else {
                if (!isFlag()) {
                    getTableData()
                } else {
                    setDataSource([])
                    setTotal(0)
                    setCurrent(1)
                }

            }
        } else {
        }

    }, [defaultQuery])
    useEffect(() => {
        if (!isFlag()) {
            getTableData()
        } else {
            setDataSource([])
            setTotal(0)
            setCurrent(1)
        }
    }, [current, pageSize])

    // useImperativeHandle(ref, () => ({
    //     getTableData,
    //     total,
    //     current,
    //     pageSize,
    //     dataSource,
    //     setDataSource,
    //     setCurrent,
    //     updateDataSource: (newValue) => {
    //         setDataSource(newValue)
    //     },
    //     setTotal
    // }))

    const InforData = {
        rowKey: record => rowKey ? rowKey : record.key,
        bordered: true,
        dataSource,
        sticky: sticky ? true : false,
        loading,
        scroll: scroll ? scroll : { x: 'max-content' },
        columns,
        locale: {
            emptyText: <div style={{ textAlign: 'center', padding: 20 }}>
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<div>{isFlag() ? "请查询后查看权益数据" : "暂无数据"}</div>} />
            </div>
        },
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: rowSelection === false ? null : {
            type: radio ? 'radio' : '',
            selectedRowKeys, selectedRows,
            onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRowKeys(selectedRowKeys)
                setSelectedRows(selectedRows)
                rowSelectionChange({ selectedRowKeys, selectedRows })
            },
            getCheckboxProps
            // getCheckboxProps: record => ({ disabled: record.rebateState !== 1 })
        },
    };
    return <Table {...InforData} className={className} />
})
export default memo(PublicTable)