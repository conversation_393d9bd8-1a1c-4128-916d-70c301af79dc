import React, { useState } from 'react'
import { Modal, Checkbox, Row, Col } from 'antd'
import './index.less'
import { packageInfoOptions, packageDetailOptions } from '@/utils/checkOptions'

const allCheckedOpt = packageInfoOptions.map(item => item.value);
const alldetailCheckedOpt = packageDetailOptions.map(item => item.value);
// const allCheckedOpt = ['packNo', 'packName', 'attribute', 'type', 'carMarketType', 'activityBeginTime', 'createTime', 'updateTime', 'carType', 'carTypeConfig', 'allArea', 'allDealer', 'status', 'description']
// const alldetailCheckedOpt = ['detailNo', 'sortCode', 'name', 'typeCode', 'carAttr', 'belong', 'deduction', 'limitLegend', 'limitYear', 'identity', 'carIdentity', 'frequency', 'traffic', 'relativeTime', 'effectMileage', 'businessCode', 'refund', 'performance', 'statement', 'remark']

const ExportModal = (props) => {
    const [checkedInfoList, setInfoCheckedList] = useState(allCheckedOpt)
    const [checkedDetailList, setCheckedDetailList] = useState(alldetailCheckedOpt)
    const [showDetail, setShowDetail] = useState(true)
    const [indeterminateInfo, setIndeterminateInfo] = useState(false)
    const [checkAllInfo, setCheckAllInfo] = useState(true)
    const [indeterminateDetail, setIndeterminateDetail] = useState(false)
    const [checkAllDetail, setCheckAllDetail] = useState(true)
    const [exportDisabled, setExportDisabled] = useState(false)

    const onInfoChange = (list) => {
        setInfoCheckedList(list);
        setIndeterminateInfo(!!list.length && list.length < allCheckedOpt.length);
        setCheckAllInfo(list.length === allCheckedOpt.length);
        setExportDisabled(!list.length)
        if (list.includes('packNo') || list.includes('packName')) {
            setShowDetail(true)
            if (!checkedDetailList.length) {
                setCheckAllDetail(false)
                setIndeterminateDetail(false)
            }
        } else {
            setShowDetail(false)
            setCheckedDetailList([])
        }
    }
    const onDetailChange = (list) => {
        setCheckedDetailList(list);
        setIndeterminateDetail(!!list.length && list.length < alldetailCheckedOpt.length);
        setCheckAllDetail(list.length === alldetailCheckedOpt.length);
    }
    //权益包全选btn
    const onCheckAllInfoChange = (e) => {
        setInfoCheckedList(e.target.checked ? allCheckedOpt : []);
        setIndeterminateInfo(false);
        setCheckAllInfo(e.target.checked);
        setExportDisabled(!e.target.checked)
        setShowDetail(e.target.checked);
        // if (e.target.checked) {
        //     setCheckedDetailList(alldetailCheckedOpt);
        //     setIndeterminateDetail(false);
        //     setCheckAllDetail(true);
        // }
    }

    //权益明细全选btn
    const onCheckAllDetailChange = (e) => {
        setCheckedDetailList(e.target.checked ? alldetailCheckedOpt : []);
        setIndeterminateDetail(false);
        setCheckAllDetail(e.target.checked);
    }

    const onOk = () => {
        props.submitHandle(checkedInfoList.concat(checkedDetailList))
        onCancel()
    }
    const onCancel = () => {
        props.handleCancel()
        setInfoCheckedList(allCheckedOpt)
        setCheckedDetailList(alldetailCheckedOpt)
        setShowDetail(true)
        setIndeterminateInfo(false)
        setCheckAllInfo(true)
        setIndeterminateDetail(false)
        setCheckAllDetail(true)
    }
    return (
        <Modal
            width={900}
            visible={props.showExport}
            onCancel={onCancel}
            title={<div><span>权益包导出</span></div>}
            onOk={onOk}
            okText="导出"
            okButtonProps={exportDisabled ? { disabled: true } : { disabled: false }}
        >
            <div className='export-content'>
                <div className='export-top'><span style={{ display: 'inline-block', marginRight: '20px' }}>权益包信息</span><Checkbox indeterminate={indeterminateInfo} checked={checkAllInfo} onChange={onCheckAllInfoChange}>全选</Checkbox></div>
                <div className='export-bottom'>
                    <Checkbox.Group style={{ width: '100%' }} onChange={onInfoChange} value={checkedInfoList}>
                        <Row >
                            {packageInfoOptions.map(item => {
                                return (
                                    <Col span={6} style={{ marginBottom: '20px' }} key={item.value}>
                                        <Checkbox value={item.value}>{item.label}</Checkbox>
                                    </Col>
                                )
                            })}
                        </Row>
                    </Checkbox.Group>
                </div>
            </div>
            {showDetail && <div className='export-content'>
                <div className='export-top'><span style={{ display: 'inline-block', marginRight: '20px' }}>权益明细信息</span><Checkbox indeterminate={indeterminateDetail} checked={checkAllDetail} onChange={onCheckAllDetailChange}>全选</Checkbox></div>
                <div className='export-bottom'>
                    <Checkbox.Group style={{ width: '100%' }} onChange={onDetailChange} value={checkedDetailList}>
                        <Row >
                            {packageDetailOptions.map(item => {
                                return (
                                    <Col span={6} style={{ marginBottom: '20px' }} key={item.value}>
                                        <Checkbox value={item.value}>{item.label}</Checkbox>
                                    </Col>
                                )
                            })}
                        </Row>
                    </Checkbox.Group>
                </div>
            </div>}
        </Modal>
    )
}
export default ExportModal