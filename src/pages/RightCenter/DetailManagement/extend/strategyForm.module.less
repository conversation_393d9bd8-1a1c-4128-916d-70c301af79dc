.StrategyForm {
  :global {
    .ant-radio-disabled .ant-radio-inner::after {
      background: rgba(24, 144, 255, 0.5)
    }

    .hide {
      display: none;
    }

    .abreast {
      color: #2C73B4;
      font-size: 15px;
      font-weight: bold;
      padding: 20px;
    }

    .w350 {
      width: 350px;
    }

    .relative-tips {
      width: 540px;
      position: absolute;
      height: 32px;
      display: flex;
      align-items: center;
    }

    .remarkMileage {
      display: block;
      color: #F5222D;
      font-size: 12px;
      white-space: normal;
      width: 670px;
    }

    .end-title {
      padding-top: 32px;
    }

    .mileage {
      .ant-space-item {
        height: 32px;
      }
    }

    .timeEndRadio,
    .mileageEnd {
      white-space: nowrap;
      height: 32px;
      line-height: 32px;
      padding: 16px 0;
      display: flex;
      align-items: baseline;
      color: rgba(0, 0, 0, .85);
      position: absolute;
      left: 0;
      top: 32px;
      margin-left: 8px;

      &.mileageEnd {
        position: relative;
        padding: 0;
        top: -8px;
      }

      &.polymerization {
        margin-left: 0px;
      }

      .box {
        background-color: #F2F9FF;
        border-radius: 2px;
        padding: 0 10px;
        border: 1px solid rgba(0, 0, 0, .1);
        height: 46px;
        line-height: 46px;
      }

      span {
        color: #065FB1;
      }

      b {
        display: inline-block;
        margin: 0 4px;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 2px;
        min-width: 40px;
        height: 32px;
        text-align: center;
        line-height: 32px;
      }

      .red-tips {
        color: #F5222D;
        font-size: 12px;
      }
    }

    .s-title {
      padding-left: 25px;
      line-height: 30px;
      border-bottom: 1px solid #E9E9E9;
      margin: 20px 0;
      font-size: 15px;
      color: #2C73B4;
      font-weight: bold;
    }

    .input {
      width: 776px;
    }

    .clientShow {
      margin: 0 32px 32px;
      border: 1px solid #BADEFF;
      padding-bottom: 20px;

      .method {
        background-color: #DBEEFF;
        height: 42px;
        padding-top: 5px;
        padding-left: 20px;

        .ant-form-item-label {
          width: 123px;
          margin-left: -16px;

          &>label {
            color: #2C73B4;
            font-size: 15px;
            font-weight: bold;
          }
        }

      }
    }

    .strategy-title {
      margin-right: 32px;
    }

    .limit-legend-radio .ant-radio-wrapper-in-form-item {
      line-height: 32px;
    }
  }
}

.remark {
  display: block;
  color: #F5222D;
  font-size: 12px;
  white-space: normal;
}

.endTimeTips {
  color: red;
  font-size: 12px;
  position: absolute;
  width: 790px;
  top: 0;
  height: 32px;
  display: flex;
  align-items: center;
  left: 401px;
}

.title {
  width: 100%;
  border-bottom: 1px solid #000;
  font-size: 16px;
  line-height: 40px;
  text-indent: 32px;
  padding-top: 20px;
}

.originTable {
  :global {
    .ant-table {
      padding-top: 0px !important;
    }
  }
}