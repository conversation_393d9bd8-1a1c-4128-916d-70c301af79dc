import React from 'react'
import { Table, message, <PERSON><PERSON>, Popconfirm, Input, Row, Col,Dropdown,Menu,Modal} from 'antd'
import { PlusOutlined } from '@ant-design/icons';
import { connect } from 'react-redux'
import { post,get } from '../../../utils/request'
import allUrl from '../../../utils/url'
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
// import PostStandard from './Modal/PostStandard'
import PostStandardUp from './Modal/PostStandardUp'
import PostStandardDown from './Modal/PostStandardDown'

import PartTimeJob from './Modal/PartTimeJob'
import Interview from './Modal/Interview'
import Detail from './Modal/Detail'
import PublicTooltip from '../../../components/Public/PublicTooltip'
import './index.less'
import '../index.less'
import moment from 'moment';
import {roleJudgment} from '@/utils/authority'
import {trim} from '@/utils'
import {utilsDict} from '@/utils/utilsDict'
import UploadFile from '@/components/Public/UploadFile'
import baseURL from '@/baseURL'

const { Search } = Input

class UserManagement extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            dataSource: [],
            TabHeight: 0,
            selectedRowKeys: [],
            selectedRows: [],
            pageSize: 10,
            total: 0,
            current:1,
            defaultQuery:{},
            PostStandardVisible:false,
            PartTimeJobVisible:false,
            interviewVisible:false,
            rowData:{},
            extendInfo:{},
            PostStandardInfo:{},
            loading:false,
            detailVisible:false,
            importLoading:false
        }
    }
    downloadDataFailed=(response)=>{
        let url = response.resp[0]
        window.open(url)
    }
    UploadChange = ({ file, fileList }, type) => {
        console.log(file,type,'file的数据')
        const { response } = file
        if (file.status === 'uploading') {
            this.setState({importLoading:true})
        }
        if (file.status === 'done') {
            if (response.success) {
                let key =2;
                message.loading({ content: '正在导入中，请稍后', key });
                setTimeout(() => {
                    message.success({ content: '导入成功!', key, duration: 2 });
                }, 1000);
            } else {
                let key =2;
                message.loading({ content: '正在导入中，请稍后', key });
                setTimeout(() => {
                    message.error({content:<span>未找到部分面试者/面试官手机号，点击"<a onClick={() =>this.downloadDataFailed(response)}>确定</a>"下载导入失败数据</span>, key, duration: 10 });
                }, 1000);
            }
            this.setState({importLoading:false})
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msgCode)
            this.setState({importLoading:false})
        }
    }
    download = () => {
        let url = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/template/%E9%9D%A2%E8%AF%95%E6%88%90%E7%BB%A9%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
        window.open(url)
    }

    GetData = () => {
        const {defaultQuery,current,pageSize} = this.state
        this.setState({loading:true},()=>{
            post(allUrl.Authority.userList, {...defaultQuery,pageSize,current}).then(res => {
                if (res && res.success) {
                    this.setState({
                        total: res.resp[0].pagination.total
                    },()=>{
                        let arr = []
                        res.resp[0].list.forEach(item=>{
                            arr.push(item.id)
                        })
                        this.getQYWXInfo(arr, res.resp[0].list)
                    })
                } else {
                    // message.error(res.msg)
                }
                this.setState({loading:false})
            })
        })
    }
    getQYWXInfo = (arr, list) =>{
        get(allUrl.Authority.getUserInfo, {userIds:arr.join(',')}).then(res => {
            if (res && res.success) {
                let Dt = res.resp[0]
                list.forEach(item=>{
                    for(let i in Dt){
                        if(item.id === Number(i)){
                            console.log('i', item, Dt)
                            item.qywxUserId = Dt[i]?.qywxUserId
                            item.partTimePosition = Dt[i]?.partTimePosition
                            item.partTimePositionName = Dt[i]?.partTimePositionName
                            item.qywxStatus = Dt[i]?.qywxStatus
                        }
                    }
                })
                this.setState({
                    dataSource:list
                })
            } else {
                // message.error(res.msg)
            }
        })
    }
    PageChange = (current, pageSize) => { this.setState({ current: current, pageSize },()=>{
        this.GetData()
    }); }

    InitPage = () => {
        let WinHeight = document.documentElement.clientHeight;
        this.setState({ TabHeight: WinHeight - 410 });
    }
    RowAdd = () => {
        let data = {
            id: 0,
            Type: 'Add',
            title:'添加账号'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/UserManagementDetail',data,history:this.props.history
        })
    }
    RowEdit = (record) => {
        let data = {
            id: record.id,
            Type: 'Edit',
            title:'编辑账号'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/UserManagementDetail',data,history:this.props.history
        })

    }
    RowDel = (record) => {
        post(allUrl.Authority.userDelete +'/' + record.id).then(res => {
            if (res.success) {
                message.success(res.msg || '删除用户成功！')
                this.GetData()
            } else {
                // message.error(res.msg || '删除用户失败！')
            }
        })
    }
    resetPassword = (record) =>{
        post(allUrl.Authority.resetPsd,{id:record.id,mobilePhone:record.mobilePhone}).then(res=>{
            if(res.success){
                message.success(res.msg || '密码重置成功！')
            }else{
                // message.error(res.msg || '密码重置失败！')
            }
        })
    }
    relatedWechat = (record)=>{
        console.log(record)
        if(record.qywxUserId){
            Modal.confirm({
                title:'解绑后SERES协同账号与企微账号将不互通，是否解绑？',
                onOk:()=>{
                    post(allUrl.Authority.bindQywxUser,{userId:record.id,qywxUserId:''}).then(res=>{
                        if(res.success){
                            message.success(res.msg || '成功')
                            this.GetData()
                        }else{
                            // message.error(res.msg || '失败')
                        }
                    })
                },
                onCancel:()=>{
                    return
                }
            })
        }else{
            Modal.confirm({
                title:'提示！',
                content:(
                    <div>
                        <p style={{marginBottom:'10px'}}>请输入企业微信的userID：</p>
                        <p><Input placeholder='请输入...' allowClear ref={node=>this.userIDRef = node} /></p>
                    </div>
                ),
                onOk:()=>{
                    console.log(this.userIDRef )
                    let WxUserID = this.userIDRef.input.value
                    if(!WxUserID){
                        message.error('请输入企业微信的userID！')
                        return
                    }
                    post(allUrl.Authority.bindQywxUser,{userId:record.id,qywxUserId:WxUserID}).then(res=>{
                        if(res.success){
                            message.success(res.msg || '成功')
                            this.GetData()
                        }else{
                            // message.error(res.msg || '失败')
                        }
                    })
                },
                onCancel:()=>{
                    return false
                }
            })
        }
    }
    enableORdisable = (record) =>{
        console.log(record)
        post(allUrl.Authority.userStatus,{id:record.id,status:record.status?0:1}).then(res=>{
            if(res.success){
                message.success('解锁成功！')
                this.GetData()
            }else{
                // message.error(res.msg)
            }
        })
    }
    configSSOToken = (record) =>{
        console.log(record)
        post(allUrl.Authority.sendSSO,{userIds: [record.id]}).then(res=>{
            if(res.success){
                message.success('推送成功！')
                this.GetData()
            }else{
                // message.error(res.msg)
            }
        })
    }
    loginUnlock = (record) =>{
        post(allUrl.Authority.userUnlock,{mobilePhone:record.mobilePhone}).then(res=>{
            if(res.success){
                message.success(res.msg)
                this.GetData()
            }else{
                // message.error(res.msg)
            }
        })
    }
    rePushWecomStatus = (record) =>{
        //不管绑没绑定直接重推
        get(allUrl.Authority.rePushQwUser,{userId:record.id}).then(res=>{
            if(res.success){
                message.success('推送成功！')
                this.GetData()
            }else{
                // message.error(res.msg)
            }
        })
    }
    onMenuClick = ({key},record) =>{
        console.log(key,record)
        switch (key){
            case '1' :
            // this.setState({rowData:record})
            this.relatedWechat(record)
            break;
            case '2':
            this.setState({rowData:record})
            this.setState({PartTimeJobVisible:true});
            break;
            case '5':
                this.RowEdit(record);
            break;
            case '7':
                this.setState({rowData:record})
                this.setState({detailVisible:true})
            break
            case '10':
            this.setState({rowData:record})
            this.setState({interviewVisible:true});
            break;
        }
    }
    renderOperation = (text, record) => {
        const {userInfo} = this.props
        const menu = (
            <Menu onClick={(e)=>this.onMenuClick(e,record)}>
                {
                    roleJudgment(userInfo,'PERM_USER_RESET_RELATEDWECHAT') ?
                    <Menu.Item key='1'>
                        {/* <Popconfirm placement="top" onConfirm={() => this.relatedWechat(record)} title={`确定${record.qywxStatus?'解绑':'绑定'}[${record.mobilePhone}]的企业微信吗？`} okText='确定' cancelText='取消'> */}
                            <a style={{ cursor: 'pointer' }}>{`${record.qywxUserId ?'解绑':'绑定'}企业微信`}</a>
                        {/* </Popconfirm> */}
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'PERM_USER_RE_PUSH_WECOM_STATUS') && record.qywxStatus == 0 || record.qywxStatus == 6 ?
                    <Menu.Item key='9'>
                        <Popconfirm placement="top" onConfirm={() => this.rePushWecomStatus(record)} title={`确定重新推送该用户的企微账号吗？`} okText='确定' cancelText='取消'>
                            <a style={{ cursor: 'pointer' }}>推送企微账号</a>
                        </Popconfirm>
                    </Menu.Item>:null
                }
                {/* {
                    roleJudgment(userInfo,'PERM_USER_RESET_PARTTIMEJOB') ?
                    <Menu.Item key='2'><a>设置兼职岗位</a></Menu.Item>:null
                }
                {
                    record.position == 'POST_SHOP_MANAGER' //总经理
                    || record.position == 'POST_DELIVERY_MANAGER' //交付经理
                    || record.position == 'POST_TM'      //技术专家
                    || record.position == 'POST_SERVICESTATION_MASTER'  //服务经理
                    || record.position == 'POST_MANAGER' //用户关爱经理
                    || record.position == 'POST_FINANCIALINSURANCE_SPECIALIST' //金融保险专员
                    ? 
                   ( roleJudgment(userInfo,'PERM_USER_RESET_INTERVIEW') ?
                    <Menu.Item key='10'>面试信息维护</Menu.Item>:null):null
                } */}
                {
                    roleJudgment(userInfo,'PERM_USER_RESET_ENABLEORDISABLE') ?
                    <Menu.Item key='3'>
                        <Popconfirm placement="top" onConfirm={() => this.enableORdisable(record)} title={`确定${record.status === 1?'禁用':'启用'}[${record.mobilePhone}]吗？`} okText='确定' cancelText='取消'>
                            <a style={{ cursor: 'pointer' }}>{`${record.status === 1?'禁用':'启用'}账号`}</a>
                        </Popconfirm>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'PERM_USER_PUSH_SSO') ?
                    <Menu.Item key='3'>
                        <Popconfirm placement="top" onConfirm={() => this.configSSOToken(record)} title={`确定推送SSO吗？`} okText='确定' cancelText='取消'>
                            <a style={{ cursor: 'pointer' }}>{`推送SSO登录权限`}</a>
                        </Popconfirm>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'PERM_USER_RESET_PASSWORD') ?
                    <Menu.Item key='4'>
                        <Popconfirm placement="top" onConfirm={() => this.resetPassword(record)} title={`确定重置[${record.mobilePhone}]的密码吗？`} okText='确定' cancelText='取消'>
                            <a style={{ cursor: 'pointer' }}>重置密码</a>
                        </Popconfirm>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'PERM_USER_LOGIN_UNLOCK') ?
                    <Menu.Item key='8'>
                        <Popconfirm placement="top" onConfirm={() => this.loginUnlock(record)} title={`确定解锁[${record.mobilePhone}]的登陆限制吗？`} okText='确定' cancelText='取消'>
                            <a style={{ cursor: 'pointer' }}>登录解锁</a>
                        </Popconfirm>
                    </Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'PERM_USER_DETAIL') ?
                    <Menu.Item key='7'>详情</Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'PERM_USER_EDIT') ?
                    <Menu.Item key='5'>编辑</Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'PERM_USER_DELETE') ?
                    <Menu.Item key='6'>
                        <Popconfirm placement="top" onConfirm={() => this.RowDel(record)} title={`确定删除[${record.mobilePhone}]吗？`} okText='确定' cancelText='取消'>
                            <a style={{ cursor: 'pointer' }}>删除</a>
                        </Popconfirm>
                    </Menu.Item>:null
                }
                
            </Menu>
          );
        return <Dropdown overlay={menu} placement="bottomCenter" trigger={['click']} >
                    <a>更多操作</a>
                </Dropdown>
        // return record.id !== 1 ? <div style={{ color: '#1890ff' }}>
        //     {
        //         roleJudgment(userInfo,'PERM_USER_EDIT') ?
        //         [
        //             <span key={1} style={{ cursor: 'pointer' }} onClick={() => this.RowEdit(record)}>
        //                 编辑
        //             <Divider key={2} type="vertical" />
        //             </span>,
        //         ]:''
        //     }
        //     {
        //         roleJudgment(userInfo,'PERM_USER_DELETE') ?
                
        //             <Popconfirm onConfirm={() => this.RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
        //                 <span style={{ cursor: 'pointer' }}>删除</span>
        //             </Popconfirm>
        //         :''
        //     }
        //     {
        //         userInfo && userInfo.id === 1 && roleJudgment(userInfo,'PERM_USER_RESET_PASSWORD') &&
        //         <Popconfirm onConfirm={() => this.resetPassword(record)} title='确定重置密码吗？' okText='确定' cancelText='取消'>
        //             <Divider key={1} type="vertical" />
        //             <span key={2} style={{ cursor: 'pointer' }}>重置</span>
        //         </Popconfirm>
        //     }
        //     {
        //         roleJudgment(userInfo,'PERM_USER_RESET_PARTTIMEJOB') ?
        //         [
        //             <Divider key={2} type="vertical" />,
        //             <span key={1} style={{ cursor: 'pointer' }} onClick={() => this.RowEdit(record)}>
        //                 设置兼职岗位
        //             </span>,
        //         ]:''
        //     }
        //     {
        //         roleJudgment(userInfo,'PERM_USER_RESET_RELATEDWECHAT') ?
        //         [
        //             <Divider key={2} type="vertical" />,
        //             <span key={1} style={{ cursor: 'pointer' }} onClick={() => this.RowEdit(record)}>
        //                 关联企微
        //             </span>,
        //         ]:''
        //     }
        //     {
        //         roleJudgment(userInfo,'PERM_USER_RESET_ENABLEORDISABLE') ?
        //         [
        //             <Divider key={2} type="vertical" />,
        //             <span key={1} style={{ cursor: 'pointer' }} onClick={() => this.RowEdit(record)}>
        //                 启用/禁用
        //             </span>,
        //         ]:''
        //     }
            
        // </div> : null
    }

    onSearch = (value) => {
        const {defaultQuery} = this.state
        value = trim(value)
        defaultQuery.params = {}
        defaultQuery.params.text = value
        this.setState({defaultQuery},()=>{
            this.GetData()
        })
    }

    render() {
        const { dataSource, loading, pageSize, total,detailVisible ,current,PostStandardVisible,PartTimeJobVisible,interviewVisible,rowData,PostStandardInfo} = this.state
        const {userInfo} = this.props
        const InforData = {
            rowKey: record => record.id,
            bordered: true,
            dataSource,
            loading,
            scroll: { x: 'max-content' },
            columns: [
                { title: 'UserID', dataIndex: 'id', width: 80 },
                { title: '统一登录账号', dataIndex: 'employeeNumber', width: 120 },
                { title: '姓名', dataIndex: 'userName', width: 140 },
                { title: '手机号', dataIndex: 'mobilePhone', width: 80 },
                { title: '角色', dataIndex: 'rolesNameDescription',width:200,render:text=><PublicTooltip title={text}>{text}</PublicTooltip>},
                { title: '岗位', dataIndex: 'positionName', width: 120},
                { title: '兼职岗位', dataIndex: 'partTimePositionName', width: 140},
                { title: '入职时间', dataIndex: 'inductionTime', width: 120,render:text=> text?moment(text).format('YYYY-MM-DD'):''},
                { title: '是否在职', dataIndex: 'isOnJob', width: 100,render:text=>text && text == 1 ?'在职':'不在职' },
                { title: '所属组织', dataIndex: 'organizationsDescription', width: 220 ,render:text=><PublicTooltip title={text}>{text}</PublicTooltip>},     
                { title: 'SSO登录权限', dataIndex: 'ssoStatus', width: 120,render:text=> text == 1 ?'是':'否'},
                { title: '企微激活状态', dataIndex: 'qywxStatus', width: 120,render:text=>utilsDict('qywxStatus',text) },
                { title: '是否启用', dataIndex: 'status', width: 100,render:text=>text?'是':'否'},
                { title: '操作', width: 100,dataIndex: 'Operation',fixed:'right', render: (text, record) => this.renderOperation(text, record) }
            ],
            // rowSelection : {
            //     selectedRowKeys,selectedRows,
            //     onChange: (selectedRowKeys,selectedRows)=>{this.setState({selectedRowKeys,selectedRows})},
            // },
            pagination: {
                pageSize: pageSize,
                onChange: this.PageChange,
                current: current,
                scroll: { x: 1270 },
                total: total,
                showTotal: () => `共${total}条，${pageSize}条/页`,
                showSizeChanger: true,
                showQuickJumper: true,
                onShowSizeChange: this.PageChange,
            }
        };
        return (
            <div className='UserManagement PublicList'>
                <Row className='queryArea'>
                    {
                        roleJudgment(userInfo,'PERM_USER_ADD') ?
                        <Col className='add' style={{marginRight:'16px'}}><Button onClick={() => this.RowAdd()} icon={<PlusOutlined />}>添加账号</Button></Col>
                        :null
                    }
                    <Col className='search'>
                        <Search placeholder="搜成员/手机号关键字" allowClear onSearch={this.onSearch}/>
                    </Col>
                </Row>
                <Row className='tableTiele'>
                    <Col className='text'>账号列表
                        {/* <a style={{fontSize:'14px',margin:'6px 0 0 10px'}} onClick={()=>this.setState({PostStandardVisible:true})}>查看岗位和人员标准</a> */}
                    </Col>
                    {/* <Col className='bts'>
                            {
                                roleJudgment(userInfo, 'PERM_USER_INTERVIEW_IMPORT_RESULT') ?
                                <>
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={10}
                                        action={baseURL.Host + allUrl.Authority.importExcel}
                                        UploadChange={this.UploadChange}
                                    >
                                        <Button loading={this.state.importLoading}>导入</Button>
                                    </UploadFile> 
                                    <a style={{fontSize:'14px',margin:'6px 60px 0 10px'}} onClick={this.download}>模版下载</a>
                                </>:null
                            }
                        </Col> */}
                </Row>
                <div className='tableData'>
                    <Table {...InforData} />
                </div>
                {/* {
                    PostStandardVisible &&
                    <PostStandardUp visible={PostStandardVisible} PostStandardInfo={PostStandardInfo} onCancel={()=>this.setState({PostStandardVisible:false})} />

                } */}
                {
                    PartTimeJobVisible &&
                    <PartTimeJob visible={PartTimeJobVisible} onCancel={()=>this.setState({PartTimeJobVisible:false})} GetData={this.GetData} rowData={rowData}/>
                }
                {
                    interviewVisible &&
                    <Interview visible={interviewVisible} onCancel={()=>this.setState({interviewVisible:false})} GetData={this.GetData} rowData={rowData}/>
                }
                <Modal
                    title="Modal"
                    visible={this.state.visible}
                    onOk={this.relatedWechat}
                    onCancel={()=>this.setState({visible:false})}
                    okText="确认"
                    cancelText="取消"
                    >
                    <p>Bla bla ...</p>
                    <p>Bla bla ...</p>
                    <p>Bla bla ...</p>
                </Modal>
                {
                    detailVisible &&
                    <Detail visible={detailVisible} rowData={rowData} title='账号详情' onCancel={()=>this.setState({detailVisible:false})} />
                }
            </div>
        )
    }
    componentDidMount() {
        this.InitPage()
        this.GetData()
    }
    componentWillUnmount() {
        this.setState = () => {
            return;
        };
    }
}
export default connect(({ common }) => {
    const { SiderType, MenuList,userInfo } = common
    return {
        SiderType, MenuList,userInfo
    }
})(UserManagement)