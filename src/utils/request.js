import axios from 'axios';
import { notification } from 'antd';
import Cookies from 'js-cookie';
import history from './history';
import baseURL from '../baseURL';

const clearUserInfo = () => {
  Cookies.remove('scrm_token');
  history.push('/');
  // window.location.reload()
};

const openNotification = (status, message) => {
  notification.destroy();
  notification.error({
    message: `错误提示：`,
    description: message || '系统繁忙，请稍后再试',
  });
};

axios.defaults.withCredentials = false;
/**
 * 设置默认请求地址
 */
if (process.env.NODE_ENV === 'development') {
  //本地调试地址
  // axios.defaults.baseURL = '/ajax';
  axios.defaults.baseURL = baseURL.Host; //开发环境地址
} else {
  axios.defaults.baseURL = baseURL.Host;
}

/**
 * 设置请求拦截器
 */
axios.interceptors.request.use(
  (config) => {
    const scrm_token = Cookies.get('scrm_token');
    if (scrm_token) {
      config.headers['Authorization'] = scrm_token;
    }
    config.headers['appId'] = 1;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 设置响应拦截器
 */
axios.interceptors.response.use(
  (response) => {
    const { status, data, config } = response;
    if (data && config.responseType == 'blob') {
      // 如果是流导出文件，通过headers获取后端返回的文件名
      data.fileName = decodeURI(response.headers['export-filename'] || '');
      return response.data;
    } else if (data && data.success) {
      return response.data;
    } else {
      if (config.notificationConfig !== true) {
        openNotification(status, data?.msg || data?.msgCode);
      }

      return Promise.resolve(response.data || { success: false, msg: '接口请求出错，请联系管理员！' });
    }
  },
  (error) => {
    const {
      response,
      response: { status, data },
    } = error;
    const { config } = response;
    if (error && response) {
      let msg = '';
      if (status === 401 || status === 403) {
        msg = '您的账号登录超时或已在其他地方登录，若不是本人操作，请注意账号安全！';
        clearUserInfo();
      } else if (status === 404) {
        msg = '接口404，未找到资源路径！';
        history.push('/error404');
      } else if (status === 500) {
        // history.push('/error500')
        msg = data.msg || data.msgCode;
      }
      if (config.notificationConfig !== true) {
        openNotification(status, msg);
      }

      return Promise.resolve({ success: false, msg: (data && data.msg) || `接口请求出错${status}，请联系管理员！` });
    } else {
      return Promise.resolve({ success: false, msg: '接口请求出错，请联系管理员！' });
    }
  }
);

/**
 * 普通post请求
 * @param {请求地址} url
 * @param {请求数据} data
 */
export const post = (url, data = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: url,
      data: options.isForm ? data : JSON.stringify(data),
      headers: { 'Content-Type': options.isForm ? 'application/x-www-form-urlencoded' : 'application/json' },
      ...options,
    })
      .then((response) => {
        resolve(response);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

/**
 * 普通post请求 参数不序列化
 * @param {请求地址} url
 * @param {请求数据} data
 */
export const postForm = (url, data = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: url,
      data: data,
      headers: { 'Content-Type': 'application/json' },
      ...options,
    })
      .then((response) => {
        resolve(response);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

/**
 * 普通get请求
 * @param {请求地址} url
 * @param {请求参数} params
 */
export const get = (url, params = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'get',
      url: url,
      params,
      ...options,
    })
      .then((response) => {
        resolve(response);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

/**
 * put请求
 * @param {请求地址} url
 * @param {请求参数} params
 */
export const put = (url, data = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'put',
      url: url,
      data: data,
      headers: { 'Content-Type': 'application/json' },
      ...options,
    })
      .then((response) => {
        resolve(response);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

/**
 * delete请求
 * @param {请求地址} url
 * @param {请求参数} params
 */
export const axiosDelete = (url, data = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'delete',
      url: url,
      data: data,
      headers: { 'Content-Type': options.isForm ? 'application/x-www-form-urlencoded' : 'application/json' },
      ...options,
    })
      .then((response) => {
        resolve(response);
      })
      .catch((error) => {
        reject(error);
      });
  });
};
