import React,{useEffect,useState} from 'react'
import { Modal, Select, Form,Row,Col,Input ,message } from 'antd'
import { post,get } from '@/utils/request'
import allUrl from '@/utils/url'
import {DictList} from '@/utils/utilsDict'
const {Option} = Select
const PartTimeJob = (props) => {
    const { visible, onCancel,rowData,GetData } = props
    const [form] = Form.useForm();
    const [positionOptions,setPositionOptions] = useState([])
    const handleOk = () => {
        form.validateFields().then(values=>{
            console.log(values)
            post(allUrl.Authority.updPartTimePosition,{userId:rowData.id,partTimePosition:values.partTimePosition}).then(res => {
                if (res.success) {
                    message.success(res.msg || '修改兼职岗位成功')
                    onCancel()
                    GetData()
                } else {
                    // message.error(res.msg || '修改兼职岗位失败！')
                }
            })
        })
    }
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    }
    useEffect(()=>{
        form.setFieldsValue({
            userName:rowData.userName,
            position:rowData.position,
            partTimePosition:rowData.partTimePosition
        })
    },[rowData])
    useEffect(()=>{
        get(allUrl.common.entryLists, {codes:'scrm_position'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                // setEntryList({...Dt})
                setPositionOptions(Dt['scrm_position'])
            } else {
                // message.error(res.message)
            }
        })
    },[])
    return <Modal visible={visible} title='设置兼职岗位' onOk={handleOk} onCancel={onCancel} maskClosable={false}>
        <Form
            {...formItemLayout}
            layout={'horizontal'}
            form={form}
        >
            <Row className='FormRowCon'>
                <Col span={24}>
                    <Form.Item label="姓名" name="userName" rules={[{ required: false, message: '请输入姓名！', }]}>
                        <Input allowClear disabled />
                    </Form.Item>
                    <Form.Item label="岗位" name="position" rules={[{ required: false, message: '请选择岗位！', }]}>
                        <Select disabled>
                            {
                                positionOptions.map((item, index) => <Option key={index} value={item.entryValue}>{item.entryMeaning}</Option>)

                            }
                        </Select>
                    </Form.Item>
                    <Form.Item label="兼职岗位" name="partTimePosition" rules={[{ required: false, message: '请选择兼职岗位！', }]}>
                        <Select
                            placeholder='请选择'
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            filterOption={(input, option) =>option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}>
                            {
                                positionOptions.map((item, index) => item.entryValue!==rowData.position && <Option key={index} value={item.entryValue}>{item.entryMeaning}</Option>)

                            }
                        </Select>
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    </Modal>
}
export default PartTimeJob