import React from 'react'
import {  message, Button,  Input, Row, Col, Form, Steps, Tree } from 'antd'
import { connect } from 'react-redux'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get, post } from '../../../utils/request'
import allUrl from '../../../utils/url'
import './Detail.less'
import '../index.less'
const { Step } = Steps
class Detail extends React.PureComponent {
    state = {
        loading: false,
        locationParmas: {},
        treeData: [],
        treeselectedKeys: [],
        treeSelectedRows: [],
        Type: ''
    }
    formRef = React.createRef();

    Save = () => {
        const { treeSelectedRows } = this.state
        // alert('保存')
        console.log(this.formRef.current)
        this.formRef.current.validateFields()
            .then(values => {
                console.log(values, '结果信息')
                let data = {
                    id: 0,
                    parent: treeSelectedRows[0].key,
                    level: treeSelectedRows[0].level ? treeSelectedRows[0].level + 1 : 1,
                    name: values.name,
                    code: values.code,
                    path: values.path,
                    icon: values.icon
                }
                console.log('参数为：', data)
                post(allUrl.Authority.saveMenu, data).then(res => {
                    if (res.success) {
                        message.success(res.msg)
                        history.goBack()
                    } else {
                        // message.error(res.msg)
                    }
                })
            })
            .catch(error => {
                console.log(error, '错误信息')
            })
    }
    onCancel = () => {
        history.goBack()
    }

    treeSelect = (selectedKeys, selectedNodes, event) => {
        this.setState({ treeselectedKeys: selectedKeys, treeSelectedRows: selectedNodes.selectedNodes })
    }
    render() {
        const { locationParmas, treeData, treeselectedKeys } = this.state

        const formItemLayout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 18 },
        }
        return (
            <div className='PublicDetail OrgManagementDetail'>
                <div className='DetailBox'>
                    <div className='DetailTitle'>{locationParmas.title}</div>
                    <div className='DetailCon'>
                        <Form
                            {...formItemLayout}
                            layout={'vertical'}
                            ref={this.formRef}
                            initialValues={{ remember: true, }}
                        >
                            <div className='progressBar'>
                                <Steps current={treeselectedKeys.length ? 1 : 0}>
                                    {['选择上级菜单', '添加下级菜单', '完成'].map((item, index) => (
                                        <Step key={index} title={item} />
                                    ))}
                                </Steps>
                            </div>
                            <div style={{ background: '#f0f2f5', height: '24px' }}></div>
                            <Row className='OrgManagementCon'>
                                {/* <Row className='DetailInfo_Con'> */}
                                <Col span={8} className='OrgManagementLeft'>
                                    {treeData.length ?
                                        <Tree defaultExpandedKeys={[0]} treeData={treeData} onSelect={this.treeSelect} />
                                        : null
                                    }
                                </Col>
                                <Col span={16} className='OrgManagementRight'>
                                    <p style={{ padding: '34px 0', fontSize: '16px', fontWeight: 500 }}>菜单信息</p>
                                    <Row>
                                        <Col span={12}>
                                            <Form.Item label="菜单名称" name="name" rules={[{ required: true, message: '请输入组织名称！', }]}>
                                                <Input placeholder="请输入..." disabled={!treeselectedKeys.length} allowClear />
                                            </Form.Item>
                                        </Col>
                                        <Col span={12}>
                                            <Form.Item label="菜单编码" name="code" rules={[{ required: true, message: '请输入菜单Code！', }]}>
                                                <Input placeholder="请输入..." disabled={!treeselectedKeys.length} allowClear />
                                            </Form.Item>
                                        </Col>

                                    </Row>
                                    <Row>
                                        <Col span={12}>
                                            <Form.Item label="Path" name="path" rules={[{ required: true, message: '请输入Url！', }]}>
                                                <Input placeholder="请输入..." disabled={!treeselectedKeys.length} allowClear />
                                            </Form.Item>
                                        </Col>
                                        <Col span={12}>
                                            <Form.Item label="Icon" name="icon" rules={[{ required: false, message: '请输入Icon！', }]}>
                                                <Input placeholder="请输入..." disabled={!treeselectedKeys.length} allowClear />
                                            </Form.Item>
                                        </Col>

                                    </Row>
                                </Col>
                            </Row>

                        </Form>
                    </div>
                </div>
                <div className='DetailBtns'>
                    <Button type="primary" onClick={this.Save} style={{ marginRight: '10px' }}>确定</Button>
                    <Button onClick={this.onCancel}>取消</Button>
                </div>
            </div>
        )
    }
    componentDidMount() {
        let locationParmas = this.props.match.params.data ? JSON.parse(DecryptByAES(this.props.match.params.data)) : {}
        this.setState({ locationParmas, Type: locationParmas.Type }, () => {
            this.GetData()
        })
    }

    GetData = () => {
        get(allUrl.Authority.MenuTreeList).then(res => {
            let arr = [{ title: '根节点', key: 0, children: res.resp, code: 'RootNode' }]
            if (res.success) {
                this.setState({
                    treeData: arr
                })
            } else {
                // message.error(res.msg || '系统繁忙请稍后再试！')
            }
        })
    }
}
export default connect()(Detail)