import React, { useState,useEffect } from 'react'
import { Modal, Form, DatePicker, Select, Upload, message, But<PERSON>, Spin } from 'antd'
import baseURL from '@/baseURL'
import Cookies from 'js-cookie'
import allUrl from '@/utils/url'
import { get, post } from '@/utils/request'
import moment from 'moment'


const { Item } = Form
const { Option } = Select


/**准入提交弹窗 */
export const AccessSubmission = (props) => {
    const { visible, onCancel, cb ,recordNodes } = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const handleOk = () => {
        setLoading(true)
        form.validateFields().then(values => {
            cb(values,(res)=>{
                console.log(res)
                if(res){
                    setLoading(false)
                    onCancel()
                }else{
                    onCancel()
                }
            })
        }).catch(()=>{
            setLoading(false)
        })
    }

    useEffect(()=>{
        console.log(recordNodes)
        if(recordNodes && recordNodes.completeTime){
            form.setFieldsValue({
                submitCompleteTime:moment(recordNodes.completeTime)
            })
        }
        if(recordNodes && recordNodes.submitCompleteTime){
            form.setFieldsValue({
                submitCompleteTime:moment(recordNodes.submitCompleteTime)
            })
        }
    },[recordNodes])
    return <Modal title='准入提交' visible={visible} onOk={handleOk} onCancel={onCancel} maskClosable={false} confirmLoading={loading}>
        <Spin spinning={loading}>
            <Form form={form}>
                <Item label='完成时间' name='submitCompleteTime' rules={[{ required: true }]}>
                    <DatePicker placeholder='选择时间' showTime style={{ width: '100%' }} />
                </Item>
                <div>注：请填写实际达到一验标准时间，不一定为当前提交时间，逻辑上小于一验验收完成时间。</div>
            </Form>
        </Spin>
    </Modal>
}


/**一验 && 二验 结果弹窗 */
export const VerificationResults = props => {
    const { visible, onCancel, cb ,rowData,ResultsID,resultTiele,recordNodes} = props
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const [deleteReportIds,setDeleteReportIds] = useState([])
    const handleOk = () => {
        form.validateFields().then(values => {
            console.log(values)
            console.log(rowData)
            let reports = values.file.map(item=>{
                return {
                    acceptReportName:item.acceptReportName,
                    acceptReportUrl:item.acceptReportUrl,
                    id:item.id ? item.id :''
                }
            })
            let data = {
                id:ResultsID,
                completeTime:values.completeTime ? new Date(moment(values.completeTime).format('YYYY-MM-DD HH:mm:ss')).getTime():'',
                dealerCode:rowData.dealerCode,
                checkResult:values.checkResult,
                reports,
                deleteReportIds
            }
            console.log(data)
            setLoading(true)
            post(allUrl.StoreManage.acceptanceProject,data).then(res=>{
                if(res.success){
                    onCancel()
                    cb(values)
                    message.success(res.msg)
                }else{
                    // message.error(res.msg)
                }
                setLoading(false)
            }).catch(error=>{
                setLoading(false)
            })
        })
    }

    const normFile = (e, attachmentType) => {
        if (Array.isArray(e)) {
            e.forEach(item => {
                if (item.response && item.response.resp) {
                    item.acceptReportName = item.name
                    item.acceptReportUrl = item.response.resp[0].url
                }
            })
            return e;
        }
        e.fileList.forEach(item => {
            if (item.response) {
                item.acceptReportName = item.name
                item.acceptReportUrl = item.response.resp[0].url
            }
        })
        return e && e.fileList;
    };

    const uploadProps = {
        name: 'file',
        action: baseURL.Host + allUrl.StoreManage.uploadFile,
        data: { code: 'storePorject' },
        headers: {
            appid: 1,
            authorization: Cookies.get('scrm_token')
        },
        onChange(info) {
            if (info.file.status !== 'uploading') {
                // console.log(info.file, info.fileList);
            }
            if (info.file.status === 'done') {
                message.success(`${info.file.name} 上传成功！`);
            } else if (info.file.status === 'error') {
                message.error(`${info.file.name} 上传失败！`);
            }
        },
        onRemove(info) {
            console.log(info)
            let temp = [...deleteReportIds]
            if (!info.id) return
            temp.push(info.id)
            setDeleteReportIds(temp)
        }
    }
    console.log(recordNodes)
    useEffect(()=>{
        if(recordNodes && Object.keys(recordNodes).length>0){
            let file = recordNodes.reports.map((item,index)=>{
                return {
                    ...item,
                    name :item.acceptReportName,
                    url : item.acceptReportUrl,
                    id : item.id,
                    uid : index + 1,
                    status : 'done'
                }
            })
            form.setFieldsValue({
                checkResult:recordNodes.checkResult,
                completeTime:moment(recordNodes.completeTime),
                file
            })
        }
    },recordNodes)


    return <Modal title={resultTiele} visible={visible} onOk={handleOk} onCancel={onCancel} maskClosable={false} confirmLoading={loading}>
        <Spin spinning={loading}>
            <Form form={form}>
                <Item label={resultTiele} name='checkResult' rules={[{ required: true }]}>
                    <Select allowClear placeholder='请选择' style={{ width: '100%' }}>
                        <Option value={true}>通过</Option>
                        <Option value={false}>未通过</Option>
                    </Select>
                </Item>
                <Item label='完成时间' name='completeTime' rules={[{ required: true }]}>
                    <DatePicker placeholder='选择时间' showTime style={{ width: '100%' }} />
                </Item>
                <Item
                    name='file'
                    label='证明文件'
                    valuePropName="fileList"
                    getValueFromEvent={e => normFile(e)}
                    className='file'
                    wrapperCol={{ span:19 }}
                    rules={[{ required: true, message: '请上传证明文件' }]}
                >
                    <Upload {...uploadProps}>
                        <Button style={{ border: '1px solid #1890ff', color: '#1890ff', width: 78, padding: 0 }}>文件上传</Button>
                    </Upload>
                </Item>
            </Form>
        </Spin>
    </Modal>
}
