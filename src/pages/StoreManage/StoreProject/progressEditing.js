import React, { useEffect, useState } from 'react'
import './detail.less'
import './progressEditing.less'
import { message, Button, Row, Col, Form, Card, Timeline, Modal } from 'antd'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get, post } from '@/utils/request'
import allUrl from '@/utils/url'
import { useSelector } from 'react-redux'
import SetProjectManage from './components/setProjectManage'
import { VerificationResults } from './components/verificationProcess'
import { AccessSubmission } from './components/verificationProcess'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import {getInervalHour} from '@/utils'
import PreviewImg from './components/previewImg'
// import noDate from '@/assets/img/noData.svg'

import moment from 'moment'

const Detail_projectManager = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [form] = Form.useForm()
    const [locationParmas, setLocationParmas] = useState({})
    const [Type, setType] = useState('')
    const [projectManageVisible, setProjectManageVisible] = useState(false)
    const [verificationResultsVisible, setVerificationResultsVisible] = useState(false)
    const [accessSubmissionVisible, setAccessSubmissionVisible] = useState(false)
    const [dataSource, setDataSource] = useState({})
    const [progressNodes, setProgressNodes] = useState([])
    const [ResultsID, setResultsID] = useState('')
    const [currentNodes, setCurrentNodes] = useState('')
    const [recordNodes, setRecordNodes] = useState({})
    const [resultTiele,setResultTitle] = useState('')
    const [previewVisible,setPreviewVisible] = useState(false)
    const [previewFile,setPreviewFile] = useState({})

    const onCancel = () => {
        history.goBack()
    }

    //修改验证结果
    const updateResult = (row) => {
        setResultsID( row && row.id ? row.id :'')
        setRecordNodes(row)
        if (row.acceptType === 1) {
            setAccessSubmissionVisible(true)
        }else if(row.acceptType === 2){
            setResultTitle('一验结果')
            setVerificationResultsVisible(true)
        }else if(row.acceptType === 3 || row.acceptType === 4){
            setResultTitle('二验结果')
            setVerificationResultsVisible(true)
        }else if(row.acceptType === 5 || row.acceptType === 6 || row.acceptType === 7){
            setResultTitle('综验结果')
            setVerificationResultsVisible(true)
        }
    }

    //一验结果
    const firstVerification = () =>{
        setResultsID('')
        setRecordNodes()
        setResultTitle('一验结果')
        setVerificationResultsVisible(true)
    }

    //二验结果
    const secondVerification = () =>{
        setResultsID('')
        setRecordNodes()
        setResultTitle('二验结果')
        setVerificationResultsVisible(true)
    }

    const syntheticVerification = () =>{
        setResultsID('')
        setRecordNodes()
        setResultTitle('综验结果')
        setVerificationResultsVisible(true)
    }

    const getData = (params) => {
        get(allUrl.StoreManage.getProjectProgress, { dealerCode: params.dealerCode }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setDataSource(Dt)
                setProgressNodes(Dt.progressNodes || [])
                if(Dt.progressNodes && Dt.progressNodes.length){
                    setCurrentNodes(Dt.progressNodes[Dt.progressNodes.length - 1].acceptType)
                }
            } else {
                // message.error(res.msg)
            }
        })
    }

    const projectManageVisibleCB = () => {
        getData(locationParmas)
    }

    const AccessSubmissionCB = (values, cb) => {
        let data = {
            id: ResultsID,
            completeTime: values.submitCompleteTime ? new Date(values.submitCompleteTime).getTime() : ''
        }
        post(allUrl.StoreManage.updateSubmitCreateTime, data).then(res => {
            if (res.success) {
                message.success(res.msg)
                getData(locationParmas)
                cb(true)
            } else {
                // message.error(res.msg)
                cb(false)
            }
        }).catch((cb) => {
            cb(false)
        })
    }

    const VerificationResultsCB = (values) => {
        getData(locationParmas)
    }

    const lookAtDetail = () => {
        let data = {
            dealerCode: dataSource.dealerCode,
            Type: 'LookAt',
            title: `AITO授权用户中心-${dataSource.storeName}(${dataSource.dealerCode})`
        }
        console.log(data, props)
        UniversalOpenWindow({
            JumpUrl: '/StoreManage/StoreProject1/edit', data, history: props.history
        })
    }

    const getFileExtendingName =(filename)=> {
        // 文件扩展名匹配正则
        var reg = /\.[^\.]+$/;
        var matches = reg.exec(filename);
        if (matches) {
          return matches[0];
        }
        return '';
    }

    const onPreview = (file) =>{
        let imgSuffix = ['.png','.gif','.jpeg','.jpg','.svg','.bmp']
            let extend = getFileExtendingName(file.acceptReportName)
            if(imgSuffix.indexOf(extend)>-1){
                file.attachmentUrl = file.acceptReportUrl
                setPreviewFile(file)
                setPreviewVisible(true)
            }else{
                window.open(file.acceptReportUrl)
            }
    }

    useEffect(() => {
        const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}
        setLocationParmas(locationParmas)
        setType(locationParmas.Type)
        if (locationParmas.dealerCode && userInfo) {
            getData(locationParmas)
        }
    }, [userInfo])
    return <div className='detail progressEditing'>
        <div className='detail_warp'>
            <div className='detail_title'>{locationParmas.title}</div>
            <div className='detail_con'>
                <Card>
                    <div className='title'>
                        <span className='projectManage'>项目经理：</span>
                        <span className='name'>{dataSource.managerUserName}</span>
                        {
                            Type === 'EDIT' ?
                                <span className='update'><a onClick={() => setProjectManageVisible(true)}>修改</a></span> 
                            : null
                        }
                    </div>
                </Card>
                <Card>
                    <div className='title'>
                        <span className='projectManage'>项目管理进度：</span>
                        {
                            // Type === 'EDIT' ?
                                <div className='remarks'>仅支持修改48小时之内维护的进度</div>
                                // : null
                        }
                    </div>
                    <div className='timeAxis'>
                        {
                            !progressNodes.length ? 
                            <div style={{margin:'0 auto',marginTop:'20px',width:'90px',textAlign:'center',fontSize:'13px',color: 'rgba(0,0,0,0.4)'}}>
                                <img src={require("../../../assets/img/noData2.png")} style={{width:'100%'}} />
                            <div>暂无数据</div>
                            </div>
                            :<Row>
                                <Col span={10}>
                                    <Timeline mode='left'>
                                        {
                                            progressNodes.map((item, index) => {
                                                return <Timeline.Item label={item.completeTime ? moment(item.completeTime).format('YYYY-MM-DD HH:mm:ss') : ''} key={index}>
                                                    <div className='timeAxis_item'>
                                                        <div className='title'>{item.acceptTypeName}
                                                            {
                                                                // Type === 'EDIT' && progressNodes.length === index+1 ?
                                                                progressNodes.length === index+1 ?
                                                                    item.createTime && getInervalHour(item.createTime,new Date()) >48 ? null :
                                                                    dataSource.isStoreWillOffline ? null:
                                                                    <span className='title_update' onClick={() => updateResult(item)}>修改</span>
                                                                : null
                                                            }
                                                        </div>
                                                        {
                                                            item.acceptType == 1 ?
                                                            <div className='enterDetails' onClick={() => lookAtDetail()}>准入详情查看</div>
                                                            :null
                                                        }
                                                        {
                                                            item.acceptType != 1 ?
                                                            <div className='fileView'>
                                                                <div className='fileView_title'>文件查看：</div>
                                                                <div className='fileView_list'>
                                                                    {
                                                                        item.reports.map((item,index)=>{
                                                                            return <div key={index} className='fileView_list_item'>
                                                                                <span onClick={()=>onPreview(item)}>{index+1}、{item.acceptReportName}</span>
                                                                            </div>
                                                                        })
                                                                    }
                                                                </div>
                                                            </div>:null
                                                        }
                                                        <div className='modifyInfo'>
                                                            <div className='modifyInfo_item'>
                                                                <span>提交人：{item.createBy}</span>
                                                                <span style={{ marginLeft: 26 }}>提交时间：{item.createTime ? moment(item.createTime).format('YYYY-MM-DD HH:mm:ss') : ''}</span>
                                                            </div>
                                                            {
                                                                item.updateBy && item.updateTime ?
                                                                    <div className='modifyInfo_item'>
                                                                        <span>修改人：{item.updateBy}</span>
                                                                        <span style={{ marginLeft: 26 }}>修改时间：{item.updateTime ? moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss') : ''}</span>
                                                                    </div> : null
                                                            }
                                                        </div>
                                                    </div>
                                                </Timeline.Item>
                                            })
                                        }
                                        {
                                            (currentNodes === 1 || currentNodes === 2) &&  Type === 'EDIT' ?
                                            <Timeline.Item>
                                                <div className='timeAxis_item'>
                                                    <div className='title'>一验结果<span className='title_update' onClick={() => firstVerification()}>修改</span></div>
                                                </div>
                                            </Timeline.Item>:null
                                        }
                                        {
                                            (currentNodes === 3 || currentNodes === 4) &&  Type === 'EDIT' ?
                                            <Timeline.Item>
                                                <div className='timeAxis_item'>
                                                    <div className='title'>二验结果<span className='title_update' onClick={() => secondVerification()}>修改</span></div>
                                                </div>
                                            </Timeline.Item>:null
                                        }
                                        {
                                            (currentNodes === 5 || currentNodes === 6) &&  Type === 'EDIT' ?
                                            <Timeline.Item>
                                                <div className='timeAxis_item'>
                                                    <div className='title'>综验结果<span className='title_update' onClick={() => syntheticVerification()}>修改</span></div>
                                                </div>
                                            </Timeline.Item>:null
                                        }
                                        {
                                            dataSource.isStoreWillOffline ? 
                                            <Timeline.Item label="2015-09-01 09:12:11">
                                            <div className='timeAxis_item'>
                                                <div className='title'>门店拟退网，项目结束</div>
                                            </div>
                                        </Timeline.Item> :null
                                        }
                                    </Timeline>
                                </Col>
                            </Row>
                        }
                    </div>
                </Card>
            </div>
        </div>
        <div className='detail_btns'>
            <Button onClick={onCancel}>取消</Button>
        </div>
        {
            projectManageVisible &&
            <SetProjectManage dealerCode={locationParmas.dealerCode} locationParmas={locationParmas} rowData={dataSource} visible={projectManageVisible} onCancel={() => setProjectManageVisible(false)} cb={projectManageVisibleCB} />
        }
        {
            verificationResultsVisible &&
            <VerificationResults resultTiele={resultTiele} recordNodes={recordNodes} rowData={dataSource} ResultsID={ResultsID} visible={verificationResultsVisible} onCancel={() => setVerificationResultsVisible(false)} cb={VerificationResultsCB} />
        }
        {
            accessSubmissionVisible &&
            <AccessSubmission visible={accessSubmissionVisible} recordNodes={recordNodes} onCancel={() => setAccessSubmissionVisible(false)} cb={(data, cb) => AccessSubmissionCB(data, cb)} />
        }
        {
            previewVisible && 
            <PreviewImg data={previewFile} visible={previewVisible} onCancel={()=>setPreviewVisible(false)}/>
        }
    </div>
}
export default Detail_projectManager