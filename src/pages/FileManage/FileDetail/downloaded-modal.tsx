import ProTable from '@/components/pro-table';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { downloadBlob } from '@/utils/file';
import { Modal, ModalProps } from 'antd';
import React, { FC, useCallback, useState } from 'react';
import { useParams } from 'react-router';
import { OperatedQueryType } from '../constants';
import { useDownloadFileIncompleteApi, useStatisticDetailTableDownloaded, useViewFileIncompleteApi } from './hooks';

export interface DownloadedModalProps extends ModalProps {
  orgCode?: string;
  file?: FileManage.FileItemDetailVO;
}

const DownloadedModal: FC<DownloadedModalProps> = ({ file, orgCode, ...props }) => {
  const [downloading, setDownloading] = useState(false);
  const { id } = useParams<{ id: string }>();

  const columns = useStatisticDetailTableDownloaded(file);
  const inCompleteDownloadRequest = useDownloadFileIncompleteApi(file);
  const handleExportClick = useCallback(async () => {
    try {
      setDownloading(true);
      const blob = await inCompleteDownloadRequest(
        { fileId: id as unknown as number, queryType: OperatedQueryType.DOWNLOADED, organizeCode: orgCode },
        {
          responseType: 'blob',
        }
      );
      const filename = file?.name?.split('.')[0] ?? '';
      downloadBlob(blob as unknown as Blob, `${filename}已下载人员明细数据.xlsx`);
    } catch (error) {
    } finally {
      setDownloading(false);
    }
  }, [file, id, orgCode]);

  const inCompleteListRequest = useViewFileIncompleteApi(file);
  const unDownloadTableRequest = usePaginationTableListStyleRequest(async ({ current, pageSize, params }) =>
    inCompleteListRequest({
      fileId: id as unknown as number,
      pageNum: current,
      pageSize,
      queryType: OperatedQueryType.DOWNLOADED,
      ...params,
    })
  );
  return (
    <Modal maskClosable={false} width={900} footer={null} title="文件已下载人员明细" {...props}>
      <ProTable
        columns={columns}
        ghost
        rowKey={'index'}
        params={{ organizeCode: orgCode }}
        request={unDownloadTableRequest}
        actions={[
          {
            children: '导出数据',
            type: 'primary',
            onClick: handleExportClick,
            loading: downloading,
          },
        ]}
      />
    </Modal>
  );
};

export default DownloadedModal;
