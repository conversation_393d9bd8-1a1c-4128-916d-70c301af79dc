import { postSeresFileStatisticInCompleteListExport } from '@/apis/file-manage';
import ProTable from '@/components/pro-table';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { downloadBlob } from '@/utils/file';
import { Modal, ModalProps } from 'antd';
import React, { FC, useCallback, useState } from 'react';
import { useParams } from 'react-router';
import { StatisticQueryType } from '../constants';
import { useFileIncompleteApi, useStatisticDetailTableColumns } from './hooks';

export interface UnViewedModalProps extends ModalProps {
  orgCode?: string;
  file?: FileManage.FileItemDetailVO;
}

const UnViewedModal: FC<UnViewedModalProps> = ({ file, orgCode, ...props }) => {
  const [downloading, setDownloading] = useState(false);
  const { id } = useParams<{ id: string }>();

  const columns = useStatisticDetailTableColumns(file);
  const handleExportClick = useCallback(async () => {
    try {
      setDownloading(true);
      const blob = await postSeresFileStatisticInCompleteListExport(
        { fileId: id as unknown as number, queryType: StatisticQueryType.NOT_VIEWED, organizeCode: orgCode },
        {
          responseType: 'blob',
        }
      );
      const filename = file?.name?.split('.')[0] ?? '';
      downloadBlob(blob as unknown as Blob, `${filename}未浏览人员明细数据.xlsx`);
    } catch (error) {
    } finally {
      setDownloading(false);
    }
  }, [file, id, orgCode]);

  const inCompleteListRequest = useFileIncompleteApi(file);
  const unDownloadTableRequest = usePaginationTableListStyleRequest(async ({ current, pageSize, params }) =>
    inCompleteListRequest({
      fileId: id as unknown as number,
      pageNum: current,
      pageSize,
      queryType: StatisticQueryType.NOT_VIEWED,
      ...params,
    })
  );
  return (
    <Modal maskClosable={false} width={860} footer={null} title="文件未浏览人员明细" {...props}>
      <ProTable
        columns={columns}
        ghost
        rowKey={'serialNo'}
        params={{ organizeCode: orgCode }}
        request={unDownloadTableRequest}
        actions={[
          {
            children: '导出数据',
            type: 'primary',
            onClick: handleExportClick,
            loading: downloading,
          },
        ]}
      />
    </Modal>
  );
};

export default UnViewedModal;
