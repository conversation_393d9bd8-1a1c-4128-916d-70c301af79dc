.timeoutSearch {
  .divider {
    display: flex;
    align-items: center;
    width: 100%;
    color: rgba(0, 0, 0, 0.25);
    font-size: 10px;
    border-color: #e0e0e0;
    border-style: solid;
    border-width: 0;
  }
  .divider:before,
  .divider:after {
    content: "";
    display: block;
    flex: 1;
    box-sizing: border-box;
    height: 1px;
    transform: matrix(1, 0, 0, 0.5, 0, 0);
    border-style: inherit;
    border-width: 1px 0 0 0;
  }
  .divider:before {
    margin-right: 16px;
  }
  .divider:after {
    margin-left: 16px;
  }
  .flex {
    display: flex;
    align-items: flex-start;
  }
  .flex-align {
    align-items: center;
  }
  .flex-right {
    display: flex;
    justify-content: right;
  }
  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .mr10 {
    margin-right: 10px;
  }
  .space-between {
    justify-content: space-between;
  }
  .text-right {
    text-align: right;
  }
  .ant-pagination {
    padding-right: 20px;
  }
  .tableData {
    background-color: white;
    border-top: solid 24px #f0f2f5;
    border-right: solid 24px #f0f2f5;
    border-bottom: solid 24px #f0f2f5;
    border-left: solid 24px #f0f2f5;
    display: flex;
    .table {
      width: 65%;
      border-right: 24px solid #f0f2f5;
    }
    .record {
        width: 35%;
        background-color: #f5f9fe;

    }
    .record-info {
        margin: 10px;
        margin-bottom: 0;
        padding: 6px 0;
        border-bottom: 1px solid #e0e0e0;

      .name {
        font-size: 13px;
        max-width: 250px;
        margin-bottom: 10px;
      }
    }
    .record-list::-webkit-scrollbar { 
      /* 隐藏默认的滚动条 */
      -webkit-appearance: none;
    }
    .record-list::-webkit-scrollbar:vertical { 
      /* 设置垂直滚动条宽度 */
      // width: 2px;
    }
    
    
    .record-list {
        padding: 10px;
        background-color: #f5f9fe;
        height: 600px;
        overflow-y: auto;
      .bg-red {
          color: #ff7875;
      }
      .flex-end {
        display: flex;
        justify-content: right;
      }
      .flex {
        display: flex;
      }
      .row {
        margin-bottom: 20px;

        .avatar {
          margin-right: 10px;
        }
        .detail {
          .userInfo {
            margin-bottom: 4px;
            color: rgba(0, 0, 0, 0.44);
            font-size: 12px;
          }
          .content {
            border-radius: 5px;
            // background-color: #eee;
            // padding: 10px;
            &-text {
              font-size: 12px;
              max-width: 400px;
              overflow: auto;
            }
          }
        }
      }
    }
    .record-empty {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .empty {
        font-size: 14px;
        text-align: center;
        margin-top: 158px;
        &-img {
          margin-bottom: 12px;
        }
      }
    }
    .ant-table-wrapper {
      background-color: white;
      .ant-table {
        padding: 24px 24px 0 24px;
        .ant-table-container {
          border: 0;
          .ant-table-content {
            // overflow: auto hidden;
            .ant-table-thead {
              tr > th,
              tr > td {
                // border: 0;
              }
            }
          }
          .ant-table-pagination {
            margin: 16px 24px;
          }
        }
      }
    }
    .tableTitle {
      padding: 24px 24px 0px 32px;
      justify-content: space-between;
      .text {
        font-size: 20px;
        font-family: PingFangSC, PingFangSC-Medium, sans-serif;
        font-weight: 500;
        text-align: left;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
      }
      .bts {
        .ant-btn {
          margin: 0 7px;
        }
      }
    }
    .ant-table-sticky-scroll {
      display: none;
    }
  }
  .PublicList_FormQuery {
    padding-top: 24px;
    padding-left: 24px;
    .ant-col-7 {
      .ant-form-item {
        .ant-form-item-control-input {
          width: 90%;
          .ant-form-item-control-input-content {
            .ant-picker {
              width: 100%;
            }
          }
        }
      }
    }
    .FormQuerySubmit {
      display: flex;
      justify-content: flex-end;
      .operationButtons {
        span {
          color: #1890ff;
          cursor: pointer;
          .anticon {
            margin-left: 6px;
          }
        }
      }
    }
  }
}
