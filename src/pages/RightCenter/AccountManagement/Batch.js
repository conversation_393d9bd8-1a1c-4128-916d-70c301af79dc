import React, { useEffect, useState, useReducer } from 'react'
import { Row, Col, Descriptions, Button, Radio, Input, Space, DatePicker, Table, Spin, Modal, message, Upload } from 'antd'
import { get, postForm } from '@/utils/request'
import allUrl from '@/utils/url'
import history from '@/utils/history'
import { UploadOutlined } from '@ant-design/icons';
import './Batch.less'
import moment from 'moment'
import PublicTooltip from '@/components/Public/PublicTooltip'
import BatchAdd from './BatchAdd'
import { columns } from '@/utils/columns'
import styles from '../antd.module.less'
const { Search } = Input


function reduder(state, action) {
    switch (action.type) {
        case 'update': {
            let { vinChecked, rows, checked } = action.payload

            return {
                ...state,
                ...{
                    vinChecked,
                    rows,
                    checked
                }
            }
        }
        case 'delete': {
            let { row } = action.payload

            let rows = state.rows.slice()
            let vinChecked = state.vinChecked.slice()
            let checked = state.checked.slice()

            let deleteIndex = checked.indexOf(row.id)
            checked.splice(deleteIndex, 1)
            vinChecked.splice(deleteIndex, 1)
            rows.splice(deleteIndex, 1)

            return {
                ...state,
                ...{
                    rows,
                    vinChecked,
                    checked
                }
            }
        }
        case 'search': {
            let { searchObj } = action.payload

            return {
                ...state,
                ...{
                    searchObj
                }
            }
        }
        default:
            return state
    }
}

const AccountManagementBatch = () => {
    const [state, dispatch] = useReducer(reduder, { vinChecked: [], rows: [], checked: [], searchObj: { type: 2 } })

    const [dataSource, setDataSource] = useState([])
    const [detaillTop, setDetailTop] = useState({})
    const [loading, setLoading] = useState(false)
    const [showBatchAdd, setShowBatchAdd] = useState(false)
    const [showConfirm, setShowConfirm] = useState(false)
    const [formFileList, setFormFileList] = useState([])
    const [showTip, setShowTip] = useState(false)
    const [tableColumns, setTableColumns] = useState([])
    const [packName, setPackName] = useState('付费权益包编码')

    const getYearUnitName = (key) => {
        return {
            'year': '年',
            'month': '月',
            'day': '日'
        }[key] || ''
    }

    useEffect(() => {
        let newColumns = columns.slice()
        newColumns.push({ title: '操作', width: 80, fixed: 'right', dataIndex: 'Operation', render: (text, record) => <Button type='link' size='small' onClick={(record) => { onDelete(record) }}>删除</Button> })
        setTableColumns(newColumns)
    }, [])

    const onDelete = (row) => {
        dispatch({ type: 'delete', payload: { row } })
    }

    const onBack = () => {
        history.push('/RightCenter/AccountManagement')
    }

    const onShowConfirm = (value) => {
        setShowConfirm(value)
    }

    const searchChangeHandle = (type, value) => {
        if (type === 'type') {
            setPackName(value == 2 ? '付费权益包编码' : '赠送权益包编码')
        }
        let newObj = {
            ...state.searchObj,
            ...{
                [type]: value
            }
        }
        dispatch({ type: 'search', payload: { searchObj: newObj } })
    }

    const onSave = () => {
        onShowConfirm(false)
        let { packNo, effectTime, endTime } = state.searchObj
        if (!packNo) {
            message.warn('权益包编码不能为空')
            return
        }
        if (!effectTime) {
            message.warn('生效时间不能为空')
            return
        }

        if (endTime && (new Date(moment(endTime).format('YYYY-MM-DD HH:mm:ss')).getTime() - new Date(moment(effectTime).format('YYYY-MM-DD HH:mm:ss')).getTime() <= 0)) {
            message.warn('结束时间不能在生效时间之前')
            return
        }

        let formData = new FormData()
        formData.append('vinList', state.vinChecked)
        formData.append('packNo', packNo)
        formData.append('effectTime', moment(effectTime).format('YYYY-MM-DD HH:mm:ss'))
        if (endTime) {
            formData.append('endTime', moment(endTime).format('YYYY-MM-DD HH:mm:ss'))
        }
        if (state.searchObj.type === 2) {
            formData.append('fileList', [])
        } else {
            if (formFileList && formFileList.length === 0) {
                onShowConfirm(false)
                return message.warn('上传附件不能为空')
            } else {
                formFileList.map(item => {
                    formData.append('fileList', item)
                })
            }
        }
        setLoading(true)

        postForm(allUrl.AccountManagement.batchRelationEquityPack, formData, {
            headers: {
                'Content-Type': "multipart/form-data"
            }
        }).then(res => {
            setLoading(false)
            message.success(res.msg)

            setTimeout(() => {
                onBack()
            }, 1000)
        })
    }

    const onSearch = (values) => {
        if (values) {
            setLoading(true)
            setShowTip(false)
            const params = {
                ...state.searchObj,
                // ...{carType: 1}
            }
            get(allUrl.AccountManagement.getSnapshotPackObj, params).then(res => {
                if (res.success) {
                    if (res.resp && res.resp.length > 0) {
                        let Dtt = res.resp[0]
                        setDataSource(Dtt.details);
                        setDetailTop(Dtt)
                    } else {
                        setShowTip(true)
                    }
                }
                setLoading(false)
            }).catch(err => {
                setLoading(false)
            })
        }

    }

    const batchAddHandle = (value) => {
        setShowBatchAdd(value)
    }

    const batchAddSubmit = (state) => {
        let { vinChecked, rows, checked } = state
        dispatch({ type: 'update', payload: { vinChecked, rows, checked } })
        batchAddHandle(false)
    }

    // 文件上传处理
    const beforeUpload = (file, fileList) => {
        const isLt2M = file.size / 1024 / 1024 < 10;
        if (isLt2M) {
            let arr = formFileList.slice().concat(fileList)
            setFormFileList(arr)
        }
        return !isLt2M
    }

    const onRemove = (file) => {
        let newFileList = formFileList.slice()
        const index = newFileList.findIndex((item) => item.uid === file.uid);
        newFileList.splice(index, 1);
        setFormFileList(newFileList.slice())
    }

    return (
        <div className='batch-PublicList'>
            <Spin spinning={loading}>
                <div className='detail-overflow'>
                    <Row className='tableTitle' style={{ paddingBottom: 15, paddingTop: 0 }}>
                        <Col className='text'>批量关联权益包</Col>
                    </Row>
                    <div className='tableData'>
                        <div className='table-top'>
                            <Descriptions title="权益包信息" >
                            </Descriptions>
                        </div>
                        <div className='table-bottom'>
                            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                                <div className="padding-left">
                                    <Space.Compact block className="form-item">
                                        权益包类型：
                                        <Radio.Group value={state.searchObj.type} onChange={(e) => searchChangeHandle('type', e.target.value)}>
                                            <Radio value={2}>付费权益包</Radio>
                                            <Radio value={4}>赠送权益包</Radio>
                                        </Radio.Group>
                                    </Space.Compact>
                                    <Row>
                                        <Col style={{ marginRight: '67px' }}>
                                            <span style={{ lineHeight: '32px' }}>权益包编码：</span>
                                            <Search placeholder={packName} allowClear onSearch={onSearch} style={{ width: 300 }} onChange={(e) => searchChangeHandle('packNo', e.target.value)} />
                                            {
                                                showTip && <div style={{ color: 'red', marginLeft: '84px', marginTop: '8px' }}>{`${state.searchObj.type === 2 ? '该权益包不是付费权益包，请确认' : '该权益包不是赠送权益包，请确认'}`}</div>
                                            }
                                        </Col>
                                        <Col style={{ marginRight: '67px' }}>生效时间：<DatePicker showTime={true} onChange={(value) => searchChangeHandle('effectTime', value)} /></Col>
                                        <Col style={{ marginRight: '10px' }}>结束时间：<DatePicker showTime={true} onChange={(value) => searchChangeHandle('endTime', value)} /></Col>
                                    </Row>
                                </div>
                                <Space size="middle" className="padding-left">
                                    权益包名称：
                                    <span>{detaillTop.name || '-'}</span>
                                    宣传时间：
                                    <span>{detaillTop.activityBeginTime ?
                                        `${moment(detaillTop.activityBeginTime).format('YYYY年MM月DD日 HH:mm:ss')} ~ ${moment(detaillTop.activityEndTime).format('YYYY年MM月DD日 HH:mm:ss')}` : '-'}</span>
                                </Space>
                                <Space.Compact block className="padding-left">
                                    权益描述：
                                    <span>
                                        {detaillTop.description || '-'}
                                    </span>
                                </Space.Compact>
                                <Space.Compact block className="padding-left" style={{ marginBottom: '-20px' }}>
                                    <h3>权益明细信息</h3>
                                </Space.Compact>
                                <Table
                                    dataSource={dataSource}
                                    rowKey={'id'}
                                    size='small'
                                    pagination={false}
                                    scroll={{ x: 3140, y: 300 }}
                                    bordered
                                    className={styles['gray-table']}
                                >
                                    <Table.Column title='权益明细编码' key='detailNo' dataIndex='detailNo' width={200} fixed='left' render={text => text || '-'} />
                                    <Table.Column title='权益明细分类' key='sortCodeName' dataIndex='sortCodeName' width={140} render={text => text || '-'} fixed={'left'} />
                                    <Table.Column title='权益明细名称' key='name' dataIndex='name' render={text => text || '-'} width={200} fixed='left' />
                                    <Table.Column title='权益类别' key='typeCodeName' dataIndex='typeCodeName' width={120} render={text => text || '-'} />
                                    <Table.Column title='车辆属性' key='carAttrName' dataIndex='carAttrName' width={120} render={text => text || '-'} />
                                    <Table.Column title='权益归属' key='belongName' dataIndex='belongName' width={120} render={text => text || '-'} />
                                    <Table.Column title='权益抵扣金额' key='deduction' dataIndex='deduction' width={200} render={(text, record) => {
                                        return record.payAmount && record.deduction ?
                                            <span>{record.payAmount}  元  ~  {record.deduction}  元</span>
                                            : '-'
                                    }} />
                                    <Table.Column title='权益限制里程(公里)' key='limitLegend' dataIndex='limitLegend' width={140} render={text => text || '-'} />
                                    {/* <Table.Column title='权益包属性' key='sortCodeName' dataIndex='sortCodeName' render={text=>text || '-'} /> */}
                                    {/* <Table.Column title='权益车型' key='carAttrName' dataIndex='carAttrName' /> */}
                                    <Table.Column title='权益限制年限' key='limitYear' dataIndex='limitYear' width={120} render={(text, record) => {
                                        let span = <span>-</span>
                                        if (record.limitTimeFrame == -1) {
                                            span = <span>永久有效</span>
                                        } else if (record.limitTimeFrame == 1) {
                                            span = <span>{`${Number(record.limitYear)}年${Number(record.limitMonth)}月${Number(record.limitDay)}日`}</span>
                                        }
                                        return span

                                        // return record.limitYear && record.yearUnit ? record.limitYear + getYearUnitName(record.yearUnit) :'不限年限'
                                    }} />
                                    <Table.Column title='权益属性' key='identityName' dataIndex='identityName' width={120} render={text => text || '-'} />
                                    <Table.Column title='享有车主身份' key='carIdentityName' dataIndex='carIdentityName' width={120} render={text => text || '-'} />
                                    <Table.Column title='享有权益频次' key='frequency' dataIndex='frequency' width={120} render={text => {
                                        let a = ''
                                        if (text) {
                                            a = text < 0 ? '不限次数' : text + '次';
                                        } else {
                                            a = '-'
                                        }
                                        return a
                                    }} />
                                    <Table.Column title='车联网流量' key='traffic' dataIndex='traffic' width={120} render={(text, record) => {

                                        let a = ''
                                        if (text) {
                                            a = text < 0 ? '不限流量' : text + record.trafficUnit;
                                        } else {
                                            a = '-'
                                        }
                                        return a

                                        // return record.traffic  && record.trafficUnit ? record.traffic + ' ' + record.trafficUnit : '不限流量'
                                    }} />
                                    <Table.Column title='权益生效时间' key='relativeTime' dataIndex='relativeTime' width={210} render={(text, record) => {
                                        return record.relativeTimeName ?
                                            record.relativeTimeName
                                            : <>
                                                {
                                                    record.fixedBeginTime && record.fixedEndTime ? moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') + ' ~ ' + moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')
                                                        : '-'
                                                }
                                            </>

                                    }} />
                                    <Table.Column title='权益支持区域' key='provinceName' dataIndex='provinceName' width={200} ellipsis render={(text, record) => {
                                        let str = ''
                                        if (record.areas && record.areas.length) {
                                            let obj = record.areas[0]
                                            str = `${obj.provinceName || ''}  ${obj?.cityName || ''} ${obj?.areaName || ''}`
                                        } else {
                                            str = '-'
                                        }
                                        return <div>
                                            {
                                                record.allArea === '0' ? '全部区域' : <PublicTooltip placement="topLeft" title={str}>{str}</PublicTooltip>
                                            }
                                        </div>
                                    }} />
                                    <Table.Column title='权益支持门店' key='dealersArr' dataIndex='dealersArr' width={200} ellipsis={true} render={(text, record) => {
                                        let temp = []
                                        if (record.dealers && record.dealers.length) {
                                            record.dealers.forEach(item => {
                                                temp.push(item.dealerCodeName)
                                            })
                                        } else {
                                            temp = ['-']
                                        }
                                        return <div>
                                            {
                                                record.allDealer === '0' ? '全部门店' : <PublicTooltip placement="topLeft" title={temp.join(',')}>{temp.join(',')}</PublicTooltip>
                                            }
                                        </div>
                                    }} />
                                    <Table.Column title='是否支持退款' key='refundName' dataIndex='refundName' width={120} render={text => text || '-'} />
                                    <Table.Column title='是否三方履约' key='performanceName' dataIndex='performanceName' width={120} render={text => text || '-'} />
                                    {/* <Table.Column title='权益声明' key='statement' dataIndex='statement' width={300} ellipsis render={text=>text?<Tooltip title={text}><div style={{overflow: 'hidden',textOverflow: 'ellipsis',whiteSpace: 'nowrap'}}>{text}</div></Tooltip> : '-'} /> */}
                                    <Table.Column title='权益声明' key='statement' dataIndex='statement' width={300} ellipsis render={text => text ? <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>{text}</PublicTooltip> : '-'} />
                                    <Table.Column title='权益备注' key='remark' dataIndex='remark' width={300} ellipsis render={text => text ? <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>{text}</PublicTooltip> : '-'} />
                                </Table>
                            </Space>
                        </div>
                    </div>
                    <div className='tableData' style={{ paddingTop: '0px' }}>
                        <div className='table-top' style={{ paddingTop: 0, display: 'flex', justifyContent: 'space-between' }}>
                            <div>
                                <span className='top-text'>权益账户信息</span>
                                <span className='red-text'>权益账户会根据填写的权益包：车型、权益包关联情况、交付情况筛选</span>
                            </div>
                            <Button type="primary" style={{ marginRight: '20px', marginTop: '9px' }} onClick={() => batchAddHandle(true)} disabled={state.searchObj.packNo === '' || state.searchObj.packNo === undefined}>添加关联</Button>
                        </div>
                        <Table
                            bordered
                            rowKey={'id'}
                            size="small"
                            dataSource={state.rows}
                            scroll={{ x: 3140, y: 300 }}
                            columns={tableColumns}
                            className={styles['gray-table']}
                        >
                        </Table>
                    </div>
                    {state.searchObj.type === 4 && <div className='tableData' style={{ marginTop: -24 }}>
                        <div className='table-top'>
                            <Descriptions title="上传附件" >
                            </Descriptions>
                        </div>
                        <div className='table-bottom' style={{ paddingLeft: '22px' }}>
                            <h4>上传附件<span style={{ color: 'red', marginLeft: 5, opacity: 0.5 }}>*</span></h4>
                            <div style={{ marginTop: 10 }}>
                                {/* <span>附件：</span> */}
                                <div style={{ position: 'relative' }}>
                                    <div style={{ marginTop: 25, marginBottom: 10 }}> <span>附件：</span>
                                        <Upload
                                            action={allUrl.AccountManagement.accountUploadFile}
                                            beforeUpload={beforeUpload}
                                            accept=".xls,.xlsx,.pdf,.jpg,.png,.jpeg,.docx,.doc"
                                            multiple={false}
                                            maxCount={9}
                                            onRemove={onRemove}
                                        >
                                            {formFileList.length < 9 ? <Button icon={<UploadOutlined />}>上传附件</Button> : null}
                                        </Upload>
                                    </div>
                                    <div style={{ color: 'red', position: 'absolute', left: '180px', top: '5px' }}>附件仅支持上传：图片、Excel、Word、PDF格式文件</div>
                                </div>
                            </div>
                        </div>
                    </div>}

                </div>
                <Row className='tableTitle box-shadow'>
                    <Col className='text'></Col>
                    <Col className='bts'>
                        <Button onClick={onBack}>返回</Button>
                        <Button type="primary" onClick={() => onShowConfirm(true)}>保存</Button>
                    </Col>
                </Row>
                <BatchAdd showBatchAdd={showBatchAdd} batchAddHandle={batchAddHandle} carType={detaillTop.carType} packNo={detaillTop.packNo} batchAddSubmit={batchAddSubmit} state={state} />
            </Spin>
            <Modal title="请确认" open={showConfirm} onOk={onSave} onCancel={() => onShowConfirm(false)}>
                <p>确认是否保存，一旦保存将无法无法编辑和修改</p>
            </Modal>
        </div>
    )
}
export default AccountManagementBatch