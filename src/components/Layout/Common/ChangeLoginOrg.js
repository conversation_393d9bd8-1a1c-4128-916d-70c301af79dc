import React from 'react'
import { Modal, message ,Spin } from 'antd'
import { post } from '../../../utils/request'
import allUrl from '../../../utils/url'
import history from '@/utils/history'
import Cookies from 'js-cookie'
import { GetUserInfo, GetMenuList } from '../../../actions/async'
import {connect} from 'react-redux' 
import './changeLoginOrg.less'
class ChangeLoginOrg extends React.Component {
    state = {
        loading:false,
        organizationId: '',
        current: {}
    }
    handleOk = () => {
        const { current, organizationId } = this.state
        console.log('current', current, organizationId)
        this.props.setCurOrgInfo(current)
        this.props.setCurrentOrgId(organizationId)

            this.setState({loading:true},()=>{
                post(allUrl.Authority.changeOrg,{organizationId}).then(res=>{
                    if(res.success){
                        message.success('组织切换成功！')
                        Cookies.set('scrm_token', res.resp[0].accessToken)
                        // 切换成功后重新调用户信息接口
                        sessionStorage.clear()
                        this.props.dispatch(GetUserInfo())
                        this.props.dispatch(GetMenuList())
                        this.props.onOk()
                        history.push('/')
                    }else{
                        // message.error(res.msg || '组织切换失败')
                    }
                    this.setState({loading:false})
                })
            })
    }
    refreshToken = (token) => {
        
    }
    componentDidMount(){
        console.log('选择组织组件初始化', this.props.list)
        this.setState({
            organizationId: this.props.curOrgInfo.id
        })
    }
    filterName = (str) => {
        const index = str.indexOf('(')
        let obj = {}
        if(index > -1) {
            obj = {
                org: str.slice(0, index),
                pos: str.slice(index+1, str.length - 1)
            }
        } else {
            obj = {
                org: str
            }
        }
        
        return obj
    }
    selectOrg = (item) => {
        console.log('item', item)
        this.setState({current: item, organizationId:item.id})

    }
    render() {
        const { visible, onCancel, list } = this.props
        const { organizationId,loading } = this.state
        const layout = {
            labelCol: { span: 4 },
            wrapperCol: { span: 24 },
        };
        return (
            <Modal className='change-login-org' title='点击方框以切换组织' visible={visible} onCancel={onCancel} onOk={this.handleOk} bodyStyle={{ padding: '54px' }} confirmLoading={loading} maskClosable={false}>
                <Spin spinning={loading}>
                    <div className='box'>
                    {
                        list.map((item, index) => {
                            return (
                                <div key={index} className={`card ${organizationId === item.id ? 'active' : ''}`} onClick={() => this.selectOrg(item)} key={item.id}>
                                    <div className='org'>
                                        <span className='name'>{this.filterName(item.organizationPositionName).org}</span>
                                        {
                                            this.props.curOrgInfo.id === item.id && <span className='default'>当前</span>
                                        }
                                        </div>
                                    <div className='pos'>{this.filterName(item.organizationPositionName).pos}</div>
                                </div>
                            )
                        })
                    }
                    </div>
                </Spin>
            </Modal>
        )
    }
}
export default connect()(ChangeLoginOrg)