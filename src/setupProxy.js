const { createProxyMiddleware } = require('http-proxy-middleware');
const indevelopment = process.env.NODE_ENV === 'development';
module.exports = (app) => {
  if (indevelopment) {
    const isHost = false;
    app.use(
      '/ajax/user',
      createProxyMiddleware({
        // 万齐校
        target: isHost ? 'http://***********:12000/' : 'https://api-dev.seres.cn/',
        changeOrigin: true,
        logLevel: 'debug',
        pathRewrite: {
          '^/ajax': '',
        },
      })
    );
    app.use(
      '/ajax/v1/gateUrl',
      createProxyMiddleware({
        // 万齐校
        target: isHost ? 'http://***********:12000/' : 'https://api-dev.seres.cn/',
        changeOrigin: true,
        logLevel: 'debug',
        pathRewrite: {
          '^/ajax': '',
        },
      })
    );
    app.use(
      '/ajax/scrms/orderTimeOut',
      createProxyMiddleware({
        // 杨力波
        target: true ? 'http://***********:12000/' : 'https://api-dev.seres.cn/',
        changeOrigin: true,
        logLevel: 'debug',
        pathRewrite: {
          '^/ajax/scrms': '',
        },
      })
    );
  }
};
