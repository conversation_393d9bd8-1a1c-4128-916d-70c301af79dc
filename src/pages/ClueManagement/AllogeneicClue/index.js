import './index.less'
import React,{useEffect, useState,useRef} from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {Row,Col,Table,Button,Divider,Popconfirm, message} from 'antd'
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment'
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
import ClueEdit from './components/ClueEdit'
import ClueAdd from './components/ClueAdd'
import ClueDetail from './components/ClueDetail'
import AllocationModal from './components/AllocationModal'
import { post ,get} from '@/utils/request';
import allUrl from '@/utils/url'
import baseURL from '@/baseURL'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import {roleJudgment} from '@/utils/authority'
import UploadFile from '@/components/Public/UploadFile'
const AllogeneicClue = (props) =>{
    const childRef = useRef(null);
    const {userInfo} = useSelector(state => state.common)
    const [loading,setLoading] = useState(false)
    const [dataSource,changeDataSource] = useState([])
    const [current,changeCurrent] = useState(1)
    const [pageSize,changePageSize] = useState(10)
    const [total,changeTotal] = useState(0)
    const [selectedRowKeys,changeSelectedRowKeys] = useState([])
    const [selectedRows,changeSelectedRows] = useState([])
    const [ClueEditVisible,setClueEditVisible] = useState(false)
    const [ClueAddVisible,setClueAddVisible] = useState(false)
    const [ClueDetailVisible,setClueDetailVisible] = useState(false)
    const [recordData,setRecordData] = useState({})
    const [AllocationModalVisible,setAllocationModalVisible] = useState(false)
    const [importLoading, setImportLoading] = useState(false)
    const [exportLoading, setExportLoading] = useState(false)
    const [regionList,setRegionList] = useState([])
    const [cityList,setCityList] = useState([])
    const [defaultQuery,setDefaultQuery] = useState({})
    const [AllocationType,setAllocationType] = useState(0)
    const cityListOchange =(value,key)=>{
        regionList[key.key].children.map((item,index)=>(
            item.value =item.name,
            item.key =index
        ))
        let cityData = regionList[key.key].children
        setCityList(cityData)
        childRef.current.setFieldValue('city', '')
    }
    const searchList=[
        {label:'客户名称',name:'name',type:'Input',placeholder:'请输入客户名称',colSpan:6},
        {label:'手机号',name:'phone',type:'Input',placeholder:'请输入手机号',colSpan:6},
        {label:'线索状态',name:'status',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'已分配',value:'2'},
            {name:'待分配',value:'1'},
        ]},
        {label:'意向购车型号',name:'carType',type:'Select',placeholder:'型号',colSpan:6,data:[
            {name:'M9',value:'M9'},
            {name:'M5',value:'M5'},
            {name:'M5 EV',value:'M5 EV'},
            {name:'M7',value:'M7'},
            {name:'SF5',value:'SF5'},
        ]},
        {label:'意向购车省区',name:'province',type:'Select',placeholder:'请选择省区',onChange:cityListOchange,colSpan:6,data:regionList},
        {label:'意向购车城市',name:'city',type:'Select',placeholder:'选择省区后选择',colSpan:6,data:cityList},
        {label:'导入标签',name:'importTag',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'SE官方直播间小雪花',value:'4'},
            {name:'SE智享生活直播号',value:'3'},
            {name:'SE官方品牌号',value:'2'},
            {name:'SE官方直播间',value:'1'},
        ]},
        {label:'是否有效',name:'invalid',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'失效',value:'1'},
            {name:'有效',value:'0'},
        ]},
    ]
    
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const onSearch = (values) =>{
        console.log(values)
        setDefaultQuery(values)

    }
    const download = () => {
        let url = ''
        if (process.env.REACT_APP_ENV === 'prod') {
            url = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/templates/异地线索.xlsx'
        } else {
            url = 'https://scrm-dev-oss.oss-cn-shanghai.aliyuncs.com/templates/异地线索.xlsx'
        }
        window.open(url)
    }
    const UploadChange = ({ file, fileList }, type) => {
        console.log(file, type)
        const { response } = file
        if (file.status === 'uploading') {
            setImportLoading(true)
        }
        if (file.status === 'done') {
            if (response.success) {
                message.success(response.msg || '导入成功')
                getData()
            } else {
                message.error(response.msg || response.msg)
            }
            setImportLoading(false)
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msg)
            setImportLoading(false)
        }
    }
    const addClue = () => {
            // 新增线索
            setClueAddVisible(true)
       
    }
    const LookAt = (record) =>{
        setRecordData(record)
        setClueDetailVisible(true)
    }
    const RowEdit = (record) =>{
            setRecordData(record)
            setClueEditVisible(true)
    }
    const renderOperation = (text, record) => {
        return  <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_EDIT') ?
                <span style={{ cursor: 'pointer' }} onClick={() => RowEdit(record)}>编辑</span>
                :null
            }
            {
                roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_DETAIL') ?
                [
                    <Divider key={1} type="vertical" />,
                    <span key={2} style={{cursor:'pointer'}} onClick={()=>LookAt(record)}>详情</span>,
                ]:null
            }
            {
                roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_ALLOCATION')&&record.status === 1 ?
                [
                    <Divider key={1} type="vertical" />,
                    <span key={2} style={{cursor:'pointer'}} onClick={()=>{
                        setAllocationType(1)
                        setRecordData(record)
                        setAllocationModalVisible(true)
                    }}>分配</span>,
                ]:null
            }
            {
                roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_DELETE') ?
                [
                    <Divider key={1} type="vertical" />,
                    <Popconfirm onConfirm={() => ClueDel(2,record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                        <span key={2} style={{cursor:'pointer'}}>删除</span>
                    </Popconfirm>
                    ,
                ]:null
            }
        </div>
    }
    const ClueDel = (type,record) => {
        // 删除/批量删除
        let clueIds=[]
        selectedRows.map((item,index)=>{
                clueIds.push({id:item.id,distributionId:item.distributionId})
            })
            
        const delList =type === 1? clueIds:[{id:record.id,distributionId:record.distributionId}]
        post(allUrl.ClueManagement.deleteClueList, {delList:delList}).then((res) => {
          if (res.success) {
              getData()
            message.success("删除成功！");
          } else {
            message.error(res.msg)
          }
        });
      };
    const Export = () => {
        // 导出
        setExportLoading(true);
        post(allUrl.ClueManagement.exportClueExcel, {...defaultQuery},{
            headers:{
                'Content-Type': "application/json"
            },
            responseType: "blob"
        }).then((res) => {
            if(res){
                setExportLoading(false);
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                if (window.navigator.msSaveOrOpenBlob) {
                  //兼容ie
                  window.navigator.msSaveBlob(blob, '异地线索.xlsx');
                } else {
                  let downloadElement = document.createElement('a');
                  let href = window.URL.createObjectURL(blob); //创建下载的链接
                  downloadElement.href = href;
                  downloadElement.download = '异地线索' + '.xlsx'; //下载后文件名
                  document.body.appendChild(downloadElement);
                  downloadElement.click(); //点击下载
                  document.body.removeChild(downloadElement); //下载完成移除元素
                  window.URL.revokeObjectURL(href); //释放掉blob对象
                }
                message.success("导出成功！");
            }else {
            // message.error(res.msg)
          }
          
        });
      };
    const getData = () =>{
        setLoading(true)
            let query = {...defaultQuery}
            let params = {pageNum:current,pageSize,...query}
            post(allUrl.ClueManagement.queryClueList,{...params}).then(res=>{
                if(res.success){
                    changeDataSource(res.resp)
                    changeSelectedRowKeys([])
                    changeSelectedRows([])
                    changeTotal(res.total)
                    setLoading(false)
                }else{
                    message.error(res.msg)
                }
                // setLoading(false)
            })
        
    }
    const getRegionList =()=>{
        // 获取省市数据
        post(allUrl.ClueManagement.getRegionList).then(res=>{
            if(res.success){
                 // 遍历，新增value
                res.resp.map((item,index)=>(item.value = item.name,item.key = index))
                setRegionList(res.resp)
            }else{
                message.error(res.msg)
            }
        })
    }
    useEffect(()=>{
        getRegionList()
    },[])
    useEffect(()=>{
        if(userInfo){
            getData()
        }
    },[defaultQuery,userInfo,current,pageSize])
    const InforData = {
        rowKey: record => record.frontId,
        bordered: true,
        dataSource:dataSource,
        loading:loading,
        scroll: { x: 'max-content' },
        columns: 
        [
            { title: '序号', dataIndex: 'index',width:80,render:(text,record,index)=><div>{(pageSize * current)- pageSize +  index+1}</div>},
            { title: '客户姓名', dataIndex: 'name', width: 100 },
            { title: '联系方式', dataIndex: 'phone', width: 100 },
            { title: '意向购车城市', dataIndex: 'city', width: 140},
            { title: '意向购车型号', dataIndex: 'carType', width: 140},
            { title: '留资时间', dataIndex: 'infoTime', width: 150},
            { title: '分配时间', dataIndex: 'distributionTime', width: 150},
            { title: '接收门店', dataIndex: 'receiveDealerName', width: 140 },
            { title: '线索状态', dataIndex: 'statusName', width: 100 },
            { title: '定单状态', dataIndex: 'orderStatusName', width: 140 },
            { title: '结算状态', dataIndex: 'settleStatusName', width: 100 },
            { title: '上传人', dataIndex: 'createBy', width: 100 },
            { title: '分配人', dataIndex: 'distribution', width: 110 },
            { title: '导入标签', dataIndex: 'importTagName', width: 110},
            { title: '是否有效', dataIndex: 'invalidName', width: 110 },
            { title: '操作', width: 200, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: {
            selectedRowKeys, selectedRows,
            onChange: (selectedRowKeys, selectedRows) => { 
                console.log(selectedRows,'xinxi');
                changeSelectedRowKeys(selectedRowKeys)
                changeSelectedRows(selectedRows)
            },
            // getCheckboxProps: record => ({ disabled:record.status == 2 || (selectedRows[0]&& (record.city !==selectedRows[0].city ? true :false ))})
        },
       
    };


    let FPDisabled = false
    if(!selectedRows.length){
        FPDisabled = true
    }else{
        for(let i = 0 ;i<selectedRows.length;i++){
            console.log(selectedRows);
            if(selectedRows[i].saleConsultantName){
                FPDisabled = true
                break
            }
        }
    }
    return(
            <div className='ClueList'>
                <PublicTableQuery onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery} key='1'ref={childRef}/>
                <div className='tableData' key='2'>
                    <Row className='tableTitle' key='1'>
                        <Col className='text'>异地线索列表</Col>
                       <Col className='bts'>
                                {
                                    roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_ADD') ?
                                    <Button type='primary' icon={<PlusOutlined />} onClick={() => addClue()}>新增线索</Button>:null
                                }
                                {
                                    roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_ALLOCATION') ?
                                    <Button type='primary' disabled={ FPDisabled} onClick={()=>{
                                        setAllocationType(2)
                                        setAllocationModalVisible(true)
                                    }}>批量分配</Button>:null
                                }
                                {
                                    roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_DELETE') ?
                                        <Popconfirm disabled={FPDisabled} onConfirm={() => ClueDel(1)} title='确定删除吗？' okText='确定' cancelText='取消'>
                                            <Button type='primary' disabled={FPDisabled} 
                                            >批量删除</Button>
                                        </Popconfirm>
                                    :null
                                }
                                {
                                    roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_EXPORT') ?
                                    <Button type='primary' onClick={()=>Export()} loading={exportLoading}>导出</Button>:null
                                }
                                {
                                    roleJudgment(userInfo,'PERM_ALLOGENEICCLUE_IMPORT') ?
                                    <>
                                    <UploadFile
                                        style={{ display: 'inline-block' }}
                                        extension={['xls', 'xlsx']}
                                        showUploadList={false}
                                        size={10}
                                        action={baseURL.Host + allUrl.ClueManagement.importClueDistributionExcel}
                                        UploadChange={UploadChange}
                                    >
                                        <Button loading={importLoading}>导入</Button>
                                        <a onClick={download}>模版下载</a>
                                    </UploadFile>
                                    </>
                                    :null
                                }
                                
                            </Col>
                    </Row>
                    <Table {...InforData} />
                </div>
                {
                    ClueEditVisible &&
                    <ClueEdit visible={ClueEditVisible} getData={getData} recordData={recordData} title='编辑线索' onCancel={()=>setClueEditVisible(false)}/>
                }
                {
                    ClueAddVisible &&
                    <ClueAdd visible={ClueAddVisible} regionList={regionList} getData={getData} recordData={recordData} title='新增线索' onCancel={()=>setClueAddVisible(false)}/>
                }
                {
                    ClueDetailVisible &&
                    <ClueDetail visible={ClueDetailVisible} getData={getData} recordData={recordData} title='线索详情' onCancel={()=>setClueDetailVisible(false)}/>
                }
                {
                    AllocationModalVisible && 
                    <AllocationModal AllocationType={AllocationType} getData={getData} recordData={recordData}  visible={AllocationModalVisible} tableSelectedRows={selectedRows} onCancel={()=>setAllocationModalVisible(false)}  />
                }
            </div>
    )
}
export default AllogeneicClue
