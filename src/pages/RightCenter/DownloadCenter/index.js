import React, { useEffect, useState, useRef } from 'react'
import { post } from '@/utils/request'
import allUrl from '@/utils/url'
import { useDispatch } from 'react-redux'
import { changeShowRedDot } from '@/actions/normal'
import './style.less'
import { Button, Table } from 'antd'
import { CloseOutlined, CheckOutlined } from '@ant-design/icons';
import styles from '../antd.module.less'

const DownloadCenter = (props) => {
    const dispatch = useDispatch()
    const [dataSource, setDataSource] = useState([])
    const [current, setCurrent] = useState(1)
    const [total, setTotal] = useState(0)
    const [pageSize, setPageSize] = useState(10)
    const [loading, setLoading] = useState(false)

    const queryExcelList = (params) => {
        post(allUrl.DownloadCenter.queryExcelList, params).then(res => {
            if (res.success) {
                setDataSource(res.resp)
                setTotal(res.total)
            }
            setLoading(false)
        })
    }
    useEffect(() => {
        dispatch(changeShowRedDot(false))
        setLoading(true)
        let params = {
            pageSize: pageSize,
            pageNum: current
        }
        queryExcelList(params)
        const timer = setInterval(async () => {
            queryExcelList()
        }, 30000)
        return () => {
            clearInterval(timer)
        }
    }, [])

    const columns = [{
        title: '文件名称',
        dataIndex: 'fileName',
        key: 'fileName',
        render: (text) => text
    },
    {
        title: '导出时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        render: (text) => text
    },
    {
        title: '状态',
        dataIndex: 'statusName',
        key: 'statusName',
        render: (text, record) => {
            if (record.status == 1) {
                return <div className='status'><span style={{ width: '16px', height: '16px', marginRight: '5px' }}><img src={require('@/assets/img/success.png')}></img></span>{text}</div>
            } else if (record.status == 3) {
                return <div><span style={{ width: '16px', height: '16px', marginRight: '5px' }}><img src={require('@/assets/img/failure.png')}></img></span><span style={{ color: '#FF4D4F' }}>{text}</span></div>
            } else {
                return <div style={{ marginLeft: '20px' }}>{text}</div>
            }
        }
    },
    {
        title: '操作人',
        dataIndex: 'createBy',
        key: 'createBy',
        render: (text) => text || '-'
    },
    {
        title: '操作',
        dataIndex: 'url',
        key: 'url',
        render: (text, record) => {
            if (record.status == 1) {
                return <a onClick={() => {
                    window.open(text)
                }}>下载</a>
            } else {
                return <a className='disabled'>下载</a>
            }

        }
    }]
    const PageChange = (current, pageSize) => {
        setLoading(true)
        setPageSize(pageSize)
        setCurrent(current)
        let params = {
            pageSize: pageSize,
            pageNum: current
        }
        queryExcelList(params)
    }
    return (<div>
        <div className='PublicList download-container'>
            <div className='info-top'>下载中心</div>
            <div className='tableData table-box'>
                <Table
                    bordered
                    className={styles['blue-table']}
                    loading={loading}
                    columns={columns}
                    dataSource={dataSource}
                    pagination={{
                        pageSize: pageSize,
                        onChange: PageChange,
                        current: current,
                        total: total,
                        showTotal: () => `共${total}条，${pageSize}条/页`,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        onShowSizeChange: PageChange,
                    }}
                />
                <div style={{ textAlign: "center", fontSize: "12px", color: "rgba(0,0,0,0.25)", marginTop: 15 }}>最多保留最近30天的下载任务记录</div>
            </div>
        </div>
    </div>)
}
export default DownloadCenter