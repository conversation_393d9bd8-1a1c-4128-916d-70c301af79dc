import React from 'react'
import { Card, Col, Spin } from 'antd'
import './ChartContainer1.less'

const ChartContainer = props => {
    const { children, themeColor, title, info, style, chart, tab, filters, subtitle, extended, loading ,statisticalTime ,ColSpan} = props
    return <Col span={24} xxl={ColSpan ? ColSpan : 12} >
        <Card className='chartContainer-card1' style={style}>
                <div className='chartContainer-card-header'>
                    <div className='title-row'>
                        <div className='title-warp'>
                            <div style={{ background: themeColor }} className='theme'></div>
                            <span className='title'>{title}</span>
                            <span className='subtitle'>{subtitle}</span>
                            <span className='subtitle-statisticalTime'>{statisticalTime}</span>
                        </div>
                        {extended?<div className='extended'>{extended}</div>:null}
                    </div>
                    <div className="tab">
                        {tab}
                    </div>
                    <div className="filters">
                        {filters}
                    </div>
                </div>
                <div className='chartContainer-card-content'>
                    <Spin spinning={loading?false:true}>
                    {children}
                    </Spin>
                </div>
                <div className="chart-wrap">{chart}</div>
        </Card>
    </Col>
}
export default ChartContainer