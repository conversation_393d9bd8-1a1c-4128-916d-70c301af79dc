import React, { useEffect, useState } from 'react'

import { useSelector } from 'react-redux'
import { Table, message, Button, Descriptions, Row, Col, Spin } from 'antd'
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import './Detail.less'
import history from '@/utils/history'
import { get, post } from '@/utils/request';
import allUrl from '@/utils/url';
import { utilsDict } from '@/utils/utilsDict'
import moment from 'moment'
import { DecryptByAES } from '@/components/Public/Decrypt'
import DetailInfo from './detailComponents/DetailInfo';
import BaseStrategy from './detailComponents/BaseStrategy';
import ExtendStrategy from './detailComponents/ExtendStrategy';
import { columnsPart } from '@/utils/columns'
import styles from '../antd.module.less'
import Logs from '../components/Logs'

const PackManagementDetail = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const [id, setId] = useState(null)
    const [locationParmas, setLocationParmas] = useState({})
    const [Type, setType] = useState('')
    const [isItemLabel, setIsItemLabel] = useState(false)
    const [loading, setLoading] = useState(false)
    const [areasArr, setAreasArr] = useState([])
    const [dealersArr, setdealersArr] = useState([])
    const [ladderPartDetailVisible, setLadderPartDetailVisible] = useState(false)
    const [ladderFormConfig, setLadderFormConfig] = useState(null)

    const [detail, setDetail] = useState({})
    const [originDetail, setOriginDetail] = useState({})
    const getYearUnitName = (key) => {
        return {
            'year': '年',
            'month': '月',
            'day': '日'
        }[key] || ''
    }
    useEffect(() => {
        const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}

        setLocationParmas(locationParmas)
        setId(locationParmas.id)
        setType(locationParmas.Type)
        console.log(locationParmas)
        // if (locationParmas.id && userInfo) {
        //     getData(locationParmas)
        // } else {

        // }
    }, [props.match.params.data, userInfo])

    // 编辑状态 通过id调用权益明细详情接口
    useEffect(() => {
        if (!id) {
            return
        }

        let query = { id: id }
        let url = allUrl.DetailedManageInterests.getDetailObj
        setLoading(true)
        if (locationParmas && locationParmas.from == 'account') {
            url = allUrl.DetailedManageInterests.getSnapshotDetailObj
            query = {
                detailId: id
            }
        }
        get(url, { ...query }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]

                // 权益履约方
                if (Dt.performingList) {
                    let arr = []
                    Dt.performingList.map(item => {
                        arr.push(item.name)
                    })
                    Dt.performingListDisplay = arr.join(',')
                }

                //补漆面
                let temp1 = [], temp2 = []
                // 内外一致
                if (Dt.paintFrame == 1 && Dt.paintList) {
                    let arr = []
                    let sum = 0
                    Dt.paintList.map(item => {
                        if (item.num > 0) {
                            arr.push(`${item.name}*${item.num}`)
                            sum += item.num
                        }
                    })
                    arr.push(`共${sum}面`)
                    Dt.paintListDisplay = arr.join(',')
                    // setPaintListDisplay(arr.join(','))
                }
                if (Dt.consumptionStrategy == 2) {
                    if (Dt.outsidePaintFrame == 1 && Dt.outsidePaintList) {
                        let arr = []
                        let sum = 0
                        Dt.outsidePaintList.map(item => {
                            if (item.num > 0) {
                                arr.push(`${item.name}*${item.num}`)
                                sum += item.num
                            }
                        })
                        arr.push(`共${sum}面`)
                        Dt.outsidePaintListDisplay = arr.join(',')
                        // setOutsidePaintListDisplay(arr.join(','))
                    }
                }
                setDetail(Dt)

                Dt.dealers && Dt.dealers.length &&
                    Dt.dealers.forEach(item => {
                        temp2.push(item.dealerCodeName)
                    })
                setdealersArr(temp2)
                Dt.areas && Dt.areas.length &&
                    setAreasArr(Dt.areas)
            } else {
                // message.error(res.msg)
            }
        }).finally(() => setLoading(false))
    }, [id])

    useEffect(() => {
        const { originDetailNo } = locationParmas
        if (originDetailNo) {
            get(allUrl.DetailedManageInterests.getDetailInfo, { detailNo: originDetailNo }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    setOriginDetail(Dt)
                }
            })
        }
    }, [locationParmas.originDetailNo])

    const onCancel = () => {
        // history.push('/RightCenter/DetailManagement')
        history.goBack()
    }

    return (
        <div className='PublicListDetail'>
            <Spin spinning={loading}>
                <div className='detail-overflow'>
                    <Row className='tableTitle' style={{ paddingBottom: 15, paddingTop: 0 }}>
                        <Col className='text'>权益明细详情</Col>
                    </Row>
                    <div className='tableData'>
                        <div className='top-box'>
                            <span className='text'>权益明细内容</span>
                            {
                                detail.businessScene === 1 ?
                                    <span style={{ color: '#F5222D', display: 'inline-block', marginLeft: 20 }}>对内核销策略与对外展示内容：{detail.consumptionStrategy == 2 ? '不一致' : '一致'}</span>
                                    : null
                            }
                        </div>
                        <div className="detailPanel">
                            <div>
                                <div className='detail-title'>
                                    <div className='left-title'>
                                        权益明细信息
                                    </div>
                                </div>

                                <DetailInfo detail={detail} />
                            </div>
                            <div>
                                <div className='detail-title'>
                                    <div className='left-title'>
                                        权益明细策略
                                    </div>
                                </div>
                                {
                                    detail.businessScene === 2 ?
                                        <ExtendStrategy detail={detail} styles={styles} originDetail={originDetail} /> :
                                        <BaseStrategy detail={detail} styles={styles} />
                                }
                            </div>

                        </div>

                        <div className='top-box'>
                            <span className='text'>履约信息</span>
                        </div>
                        <div className="detailPanel">
                            <Descriptions title="" column={1}>
                                <Descriptions.Item label='履约结算价格'>
                                    <span className='text'>{detail.settlementPriceFrame == 0 ? '-' : [
                                        detail.settlementPriceFrame == 1 ? '实际结算' : [
                                            detail.settlementPriceFrame == 2 ? `${detail.settlementPrice}元` : '-'
                                        ]
                                    ]}</span>
                                </Descriptions.Item>
                                <Descriptions.Item label='延保承保方'>
                                    <span className='text'>{detail.extendWarrantyAssurerName || '-'}</span>
                                </Descriptions.Item>
                            </Descriptions>
                        </div>

                        <div className='top-box'>
                            <span className='text'>权益配件关联</span>
                        </div>
                        <div className="detailPanel" style={{ padding: 0 }}>
                            <Table
                                rowKey={'id'}
                                dataSource={detail.partRelationList || []}
                                scroll={{ y: 300 }}
                                columns={columnsPart}
                                className={styles['gray-table']}
                                bordered
                                size='small'
                                style={{ padding: '20px 32px' }}
                            >
                            </Table>
                        </div>
                        <div className='top-box'>
                            {isItemLabel ? <img className='icon-box' src={require('@/assets/img/up-icon.png')} onClick={() => {
                                setIsItemLabel(false)
                            }} alt="" /> : <img className='icon-box' src={require('@/assets/img/down-icon.png')} onClick={() => {
                                setIsItemLabel(true)
                            }} alt="" />}
                            <span className='text'>操作信息</span>
                        </div>
                        <div className="detailPanel">
                            <div className='log-wrap'>
                                <Logs type="detail" dataCode={locationParmas.detailNo} showAll={isItemLabel} >
                                    {
                                        (item) => (
                                            <div>
                                                {
                                                    item.changeContent && typeof item.changeContent === 'object' &&
                                                    Object.entries(item.changeContent).map((k) => <>{k[0]}：{k[1]}</>)
                                                }
                                            </div>
                                        )
                                    }

                                </Logs>
                            </div>
                        </div>

                    </div>
                </div>
                <Row className='tableTitle' style={{ paddingBottom: 24, boxShadow: '0px -2px 6px 0px rgba(0,0,0,0.08)', zIndex: 1000 }}>
                    <Col className='text'></Col>
                    <Col className='bts'>
                        <Button onClick={onCancel}>返回</Button>
                    </Col>
                </Row>
            </Spin>
        </div>
    )
}
export default PackManagementDetail
