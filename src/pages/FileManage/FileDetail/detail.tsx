import { postSeresFileStatisticSummaryDetail, postSeresFileStatisticSummaryDetailExport } from '@/apis/file-manage';
import ProTable from '@/components/pro-table';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { downloadBlob } from '@/utils/file';
import { formatFloat, formatPercentage } from '@/utils/format';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Card, Input, Space, Tooltip, Typography } from 'antd';
import React, { FC, Key, useCallback, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { FilePermission } from '../constants';
import DownloadedModal, { DownloadedModalProps } from './downloaded-modal';
import { useDeliverTypeLabel } from './hooks';
import UnDownloadModal, { UnDownloadModalProps } from './undonwload-modal';
import UnViewedModal, { UnViewedModalProps } from './unviewed-modal';
import ViewedModal, { ViewedModalProps } from './viewed-modal';

export interface FileDeliverDetailProps {
  file?: FileManage.FileItemDetailVO;
}

const FileDeliverDetail: FC<FileDeliverDetailProps> = ({ file }) => {
  const { id } = useParams<{ id: string }>();
  const [downloading, setDownloading] = useState(false);

  const deliverTypeLabel = useDeliverTypeLabel(file);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const handleExportClick = useCallback(async () => {
    try {
      setDownloading(true);
      const blob = await postSeresFileStatisticSummaryDetailExport(
        {
          fileId: id as unknown as number,
          ...currentSorterRequest.current,
          organizeCodes: selectedRowKeys.map(String),
        },
        {
          responseType: 'blob',
        }
      );
      const filename = file?.name?.split('.')[0] ?? '';
      downloadBlob(blob as unknown as Blob, `${filename}下发明细数据.xlsx`);
    } catch (error) {
    } finally {
      setDownloading(false);
    }
  }, [file, id, selectedRowKeys]);
  const currentSorterRequest = useRef<Record<string, boolean>>({});
  const tableRequest = usePaginationTableListStyleRequest(({ current, pageSize, params, sorter }) => {
    const sorterParams = Object.entries(sorter ?? {}).reduce((acc, [key, value]) => {
      if (value) {
        acc[key] = value === 'descend';
      }
      return acc;
    }, {} as Record<string, boolean>);
    currentSorterRequest.current = sorterParams;
    return postSeresFileStatisticSummaryDetail({
      pageNum: current,
      pageSize,
      ...params,
      ...sorterParams,
    });
  });

  const [unViewedModal, setUnViewedModal] = useState<Pick<UnViewedModalProps, 'open' | 'orgCode'>>({
    open: false,
    orgCode: undefined,
  });
  const [unDownloadModal, setUnDownloadModal] = useState<Pick<UnDownloadModalProps, 'open' | 'orgCode'>>({
    open: false,
    orgCode: undefined,
  });
  const [viewedModal, setViewedModal] = useState<Pick<ViewedModalProps, 'open' | 'orgCode'>>({
    open: false,
    orgCode: undefined,
  });
  const [downloadedModal, setDownloadedModal] = useState<Pick<DownloadedModalProps, 'open' | 'orgCode'>>({
    open: false,
    orgCode: undefined,
  });
  return (
    <Card
      title="明细数据"
      extra={
        <Space>
          <Tooltip title="只导出选中的数据，若未选中任何数据，则导出全量数据">
            <InfoCircleOutlined />
          </Tooltip>
          <Button loading={downloading} type="primary" onClick={handleExportClick}>
            导出数据
          </Button>
        </Space>
      }
    >
      <ProTable
        ghost
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys: selectedRowKeys,
          onChange: newSelectedRowKeys => {
            console.log(newSelectedRowKeys);
            setSelectedRowKeys(newSelectedRowKeys);
          },
          preserveSelectedRowKeys: true,
        }}
        rowKey={'organizeCode'}
        params={{ fileId: id }}
        onReset={() => {
          setSelectedRowKeys([]);
        }}
        columns={[
          {
            title: '序号',
            valueType: 'index',
            fixed: 'left',
          },
          {
            title: `下发${deliverTypeLabel}`,
            dataIndex: 'organizeName',
            fixed: 'left',
            search: {
              type: Input,
              fieldProps: {
                placeholder: '请输入',
              },
            },
          },
          {
            title: '下发人数',
            dataIndex: 'sendCount',
            sorter: { multiple: 1 },
          },
          {
            title: '已浏览人数',
            dataIndex: 'previewCount',
            sorter: { multiple: 2 },
            render: (value, record) =>
              value ? (
                <Typography.Link
                  onClick={() =>
                    setViewedModal({
                      open: true,
                      orgCode: record.organizeCode,
                    })
                  }
                >
                  {value}
                </Typography.Link>
              ) : (
                value
              ),
          },
          {
            title: '未浏览人数',
            dataIndex: 'notPreviewCount',
            sorter: { multiple: 3 },
            render: (value, record) =>
              value ? (
                <Typography.Link
                  onClick={() =>
                    setUnViewedModal({
                      open: true,
                      orgCode: record.organizeCode,
                    })
                  }
                >
                  {value}
                </Typography.Link>
              ) : (
                value
              ),
          },
          {
            title: '浏览率',
            sorter: { multiple: 4 },
            dataIndex: 'previewRate',
            render: value => `${formatPercentage(value)}`,
          },
          {
            title: '总浏览次数',
            dataIndex: 'totalPreviewCount',
            sorter: { multiple: 5 },
          },
          {
            title: '人均浏览次数',
            sorter: { multiple: 6 },
            dataIndex: 'previewAvgCount',
            render: value => `${formatFloat(value)}`,
          },
          {
            title: '总浏览时长(分)',
            sorter: { multiple: 7 },
            dataIndex: 'totalPreviewDuration',
          },
          {
            title: '人均浏览时长(分)',
            sorter: { multiple: 8 },
            dataIndex: 'previewAvgDuration',
            render: value => `${formatFloat(value)}`,
          },
          {
            title: '已下载人数',
            dataIndex: 'downloadCount',
            sorter: file?.permission === FilePermission.DOWNLOAD ? { multiple: 9 } : false,
            render: (value, record) =>
              file?.permission === FilePermission.DOWNLOAD ? (
                value ? (
                  <Typography.Link
                    onClick={() =>
                      setDownloadedModal({
                        open: true,
                        orgCode: record.organizeCode,
                      })
                    }
                  >
                    {value}
                  </Typography.Link>
                ) : (
                  value
                )
              ) : (
                '-'
              ),
          },
          {
            title: '未下载人数',
            dataIndex: 'notDownloadCount',
            sorter: file?.permission === FilePermission.DOWNLOAD ? { multiple: 10 } : false,
            render: (value, record) =>
              file?.permission === FilePermission.DOWNLOAD ? (
                value ? (
                  <Typography.Link
                    onClick={() =>
                      setUnDownloadModal({
                        open: true,
                        orgCode: record.organizeCode,
                      })
                    }
                  >
                    {value}
                  </Typography.Link>
                ) : (
                  value
                )
              ) : (
                '-'
              ),
          },
          {
            title: '下载率',
            sorter: file?.permission === FilePermission.DOWNLOAD ? { multiple: 11 } : false,
            dataIndex: 'downloadRate',
            render: value => (file?.permission === FilePermission.DOWNLOAD ? `${formatPercentage(value)}` : '-'),
          },
          {
            title: '总下载次数',
            sorter: file?.permission === FilePermission.DOWNLOAD ? { multiple: 12 } : false,
            dataIndex: 'totalDownloadCount',
            render: value => (file?.permission === FilePermission.DOWNLOAD ? value : '-'),
          },
          {
            title: '人均下载次数',
            sorter: file?.permission === FilePermission.DOWNLOAD ? { multiple: 13 } : false,
            dataIndex: 'downloadAvgCount',
            render: value => (file?.permission === FilePermission.DOWNLOAD ? value : '-'),
          },
        ]}
        search={{
          labelWidth: 80,
        }}
        request={tableRequest}
      />
      <UnViewedModal file={file} {...unViewedModal} onCancel={() => setUnViewedModal({ open: false })} />
      <UnDownloadModal file={file} {...unDownloadModal} onCancel={() => setUnDownloadModal({ open: false })} />
      <ViewedModal
        file={file}
        {...viewedModal}
        onCancel={() => {
          setViewedModal({ open: false });
        }}
      />
      <DownloadedModal file={file} {...downloadedModal} onCancel={() => setDownloadedModal({ open: false })} />
    </Card>
  );
};

export default FileDeliverDetail;
