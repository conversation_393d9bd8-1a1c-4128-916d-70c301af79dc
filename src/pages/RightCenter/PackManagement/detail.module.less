.ant-descriptions-custom {
    :global {
        .ant-descriptions-header {
            border-bottom: 2px solid #5095d5;
            height: 30px;
            margin-bottom: 20px;
            margin-right: 17px;
        }

        .ant-descriptions-title {
            flex: none;
            background: #5095d5;
            color: #fff;
            font-family: PingFangSC-Medium, PingFang SC;
            font-size: 15px;
            font-weight: 500;
            height: 30px;
            line-height: 30px;
            text-align: center;
            padding: 0 15px;
            // width: 122px;
        }
    }
}

.item-red {
    :global {

        .ant-descriptions-item-label,
        .ant-descriptions-item-content {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 14px;
            color: #F10486;
        }

    }
}

//权益车型配置
.packCarConfigCss {
    display: flex;
    gap: 8px;
    margin-bottom: 18px;

    .common {
        padding: 2px 7px;
    }

    .flexCommom {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    :global {
        .carType {
            width: 54px;
            .common();
            .flexCommom();
        }



        .a2 {
            display: flex;
            gap: 8px;

            .year {
                width: 54px;
                margin-bottom: 8px;
                .common();
                .flexCommom();
            }

            .a4 {
                display: flex;
                gap: 8px;

                .power {
                    width: 102px;
                    margin-bottom: 8px;
                    border-radius: 2px;
                    .common();
                    .flexCommom();
                }

                .a6 {
                    display: flex;
                    gap: 8px;
                    flex-wrap: wrap;
                    margin-bottom: 8px;

                    .config {
                        border: 1px solid #d5d5d5;
                        background: #F8F8F8;
                        border-radius: 2px;
                        .common();
                    }
                }
            }
        }

        .a2:last-child {
            .year {
                margin-bottom: 0;
            }

            .a4:last-child {
                .power {
                    margin-bottom: 0;
                }

                .a6:last-child {
                    margin-bottom: 0 !important;
                }
            }
        }

        .c0,
        .c4 {
            background: #E8FDFF;
            border: 1px solid #08BACC;
            color: #05737E;
            // font-weight: 600;
        }

        .c1,
        .c5 {
            background: #EFFFF3;
            border: 1px solid #55C46F;
            color: #018620;
            // font-weight: 600;
        }

        .c2,
        .c6 {
            background: #FFFBEB;
            border: 1px solid #DFB105;
            color: #775F05;
            // font-weight: 600;
        }

        .c3,
        .c7 {
            background: #F5F5FF;
            border: 1px solid #9594DC;
            color: #4C4BB5;
            // font-weight: 600;
        }
    }
}