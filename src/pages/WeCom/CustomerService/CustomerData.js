
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { Table, message, Tabs, Button, Input, Row, Col} from 'antd'
import moment from 'moment'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import {fileDown} from '@/utils'
import PublicTooltip from '@/components/Public/PublicTooltip'
import {roleJudgment} from '@/utils/authority'
const CustomerData = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading, setLoading] = useState(false)
    const [exportLoading, setExportLoading] = useState(false)
    const [dataSource, changeDataSource] = useState([])
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)
    const [total, changeTotal] = useState(0)
    const [tableHeight,setTableHeight] = useState(0)
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('CustomerDataTabsKey') || '1')
    const [defaultQuery, setDefaultQuery] = useState({})
    let searchList1 = [
        { label: '业务领域', name: 'businessArea', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '线下销售', value: '线下销售' },
            { name: '线上销售', value: '线上销售' },
            { name: '营销', value: '营销' },
            { name: '服务', value: '服务' },
            { name: '产品支持', value: '产品支持' },
        ] },
        { label: '诉求类型', name: 'appealType', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '咨询', value: '咨询' },
            { name: '投诉', value: '投诉' },
            { name: '建议', value: '建议' },
            { name: '救援', value: '救援' },
            { name: '表扬', value: '表扬' },
            { name: '合作', value: '合作' },
        ] },
        { label: '城市', name: 'city', type: 'Input',  colSpan: 6},
        { label: '车型', name: 'model', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
            { name: '问界M5', value: '问界M5' },
            { name: '问界M5 EV', value: '问界M5 EV' },
            { name: '问界M7', value: '问界M7' },
            { name: '华为智选SF5', value: '华为智选SF5' },
            { name: 'SF5', value: 'SF5' },
        ] },
        { label: '更新时间', name: 'dateTime', type: 'RangePicker', colSpan: 6 },
    ]
   

    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
   
    const SessionData = {
        rowKey: record => record.sessionId,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content', y: tableHeight},
        columns: [
            { title: '会话ID', dataIndex: 'sessionId', width: 100,  },
            { title: '用户微信昵称', dataIndex: 'nickName', width: 140 },
            { title: '用户姓名', dataIndex: 'name', width: 100 },
            { title: '性别', dataIndex: 'gender', width: 100,  render: (text, record) => renderGender(text, record) },
            { title: '用户联系方式', dataIndex: 'phones', width: 120, render: (text, record) => renderArr(text, record) },
            { title: '用户车型', dataIndex: 'carTypes', width: 160, render: (text, record) => renderArr(text, record) },
            { title: '用户车架号', dataIndex: 'carVins', width: 120, render: (text, record) => renderArr(text, record) },
            { title: '用户车牌号', dataIndex: 'licensePlates', width: 130, render: (text, record) => renderArr(text, record) },
            { title: '车主姓名', dataIndex: 'ownerName', width: 100 },
            { title: '车主手机号', dataIndex: 'ownerPhone', width: 140 },
            { title: '车辆类型', dataIndex: 'model', width: 100 },
            { title: '车架号', dataIndex: 'carVin', width: 100 },
            { title: '交付时间', dataIndex: 'deliveredTime', width: 160, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '' },
            { title: '交付门店', dataIndex: 'deliverStoreName', width: 180 },
            { title: '省份', dataIndex: 'province', width: 100 },
            { title: '城市', dataIndex: 'city', width: 100 },
            { title: '接待坐席Id', dataIndex: 'servicerUserid', width: 120 },
            { title: '接待坐席姓名', dataIndex: 'servicerName', width: 160 },
            { title: '客服账号', dataIndex: 'accountName', width: 100 },
            // { title: '会话内容', dataIndex: 'sessionContent', width: 200, render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
            { title: '业务领域', dataIndex: 'businessArea', width: 100 },
            { title: '诉求类型', dataIndex: 'appealType', width: 100 },
            { title: '问题类型', dataIndex: 'questionType1', width: 100 },
            { title: '工单号', dataIndex: 'workOrderNo', width: 100 },
            { title: '问题小结', dataIndex: 'remarks', width: 200},
            { title: '人工接入时间', dataIndex: 'receptionTime', width: 160, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '' },
            { title: '排队时长（秒）', dataIndex: 'lineupDuration', width: 140 },
            { title: '首次响应时间', dataIndex: 'firstResponseTime', width: 160, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '' },
            { title: '首次响应时长（秒）', dataIndex: 'firstResponseDuration', width: 160 },
            { title: '平均响应时长（秒）', dataIndex: 'avgResponseDuration', width: 160 },
            { title: '会话结束类型', dataIndex: 'endType', width: 160, render: (text, record) => renderEndType(text, record)},
            { title: '会话结束时间', dataIndex: 'endTime', width: 160, render: text => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '' },
            { title: '用户消息量', dataIndex: 'customerMsgNum', width: 120 },
            { title: '客服消息量', dataIndex: 'serviceMsgNum', width: 120 },
            { title: '会话是否有效', dataIndex: 'isValid', width: 140, render: text => text ? '是' : '否' },
            { title: '是否邀评', dataIndex: 'isSatisfa', width: 100, render: text => text ? '是' : '否' },
            { title: '评价得分', dataIndex: 'satisfaScore', width: 100 },
            { title: '是否转接', dataIndex: 'isTransfer', width: 100, render: text => text ? '是' : '否' },
            // { title: '流转日志', dataIndex: 'transferLog', width: 200, render:text=><PublicTooltip title={text}>{text}</PublicTooltip> },
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: null,
    };
    const renderGender = (text, record) => {
        let gender = null
        if(text == 1) {
            gender = "男"
        }else if(text == 2) {
            gender = "女"
        }else {
            gender = "未知"
        }
        return gender
    }
    const renderArr = (text, record) => {
        let arr = text && text.split(',') || []
        return arr.map((item, index) => {
            return  <div key={index}>{ item }</div>
        }) 
    }
    const renderEndType = (text, record) => {
        let endType = null
        if(text == 1){
            endType = '客服主动结束'
        }else if(text == 2) {
            endType = '用户超时结束'
        }else if(text == 3) {
            endType = '客服超时未回复结束'
        }
        return endType
    }
    const exportExcel = () => {
        setExportLoading(true)
        setLoading(true)
        post(allUrl.WeCom.exportCustomerQuestion, { ...defaultQuery }, {responseType: "blob"}).then(res => {
            fileDown(res,'问题标注')
            setExportLoading(false)
            setLoading(false)
        })
    }
    const getData = () => {
        setLoading(true)
        let query = { ...defaultQuery }
        let params = { pageIndex: current, pageSize, ...query }
        post(allUrl.WeCom.queryCustomerQuestion, { ...params }).then(res => {
            if (res.success) {
                // 处理回执情况字段，把两个字段包到一个字段中
                changeDataSource(res.resp[0].list)
                changeTotal(res.resp[0].total)
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }
    
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }
  
    useEffect(()=>{
        initPage()
    },[])
    useEffect(() => {
        if (userInfo) {
            getData()
        }
    }, [defaultQuery, userInfo, current, pageSize,tabsKey])

    
    const onSearch = (values) => {
        if (values.dateTime && values.dateTime.length) {
            values.startTime = moment(values.dateTime[0]).format('YYYY-MM-DD')
            values.endTime = moment(values.dateTime[1]).format('YYYY-MM-DD')
            delete values.dateTime
        }
        setDefaultQuery(values)
    }
    
    return (
        <div className='CustomerData'>
            <PublicTableQuery tabsKey={tabsKey} onSearch={onSearch} searchList={searchList1} defaultQuery={defaultQuery} />
                
            <div className='tableData'>
                <Row className='tableTitle'>
                    <Col className='text'>问题标注列表</Col>
                    <div className='btn-box' style={{textAlign: 'right'}}>
                    {
                        roleJudgment(userInfo, 'CUSTOMER_DATA_EXPORT') ?     
                        <Button type='primary' onClick={exportExcel}>导出表格</Button>
                        :null
                    }
                    </div>
                </Row>
                <Table {...SessionData} />
            </div>
        </div>
    )
}
export default CustomerData
