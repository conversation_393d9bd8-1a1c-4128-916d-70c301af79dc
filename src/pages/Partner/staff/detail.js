import React, { useState } from "react";
import { Row, Col, Button, Modal, Form, Input, Select } from "antd";
import _ from "lodash";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
import { useEffect } from "react";
import DebounceSelect from '@/components/Public/DebounceSelect'
import { object } from "prop-types";
const { Option } = Select;

const PartnerForm = ({
  visible,
  onCancel,
  onSave,
  initialValues,
  isViewMode,
  jobStatus,
  loading,
  renderStatusName,
}) => {
  const [form] = Form.useForm();
  const [partnerOptions, setPartnerOptions] = useState([]);
  const onFinish = (values) => {
    let data
    let codeArr = []
    if (initialValues) {
      data = Object.assign(initialValues, values, {});
    } else {
      data = values;
    }
    if (data.partnerCodes.length) {
      data.partnerCodes.length && data.partnerCodes.map((i) => {
        codeArr.push(i.value || i)
      })
      data.partnerCodes = codeArr
    }
    console.log('data1', data)
    onSave(data);
    form.resetFields();
  };
  const searchPartnerCode = (option) => {
    const codes = option.map((i) => i.value || i);
    console.log("searchPartnerCode", option, codes);
    if (option.length) {
      form.setFieldsValue({
        partnerCodes: option
      })
    } else {
      // form.resetFields(['partnerCodes'], [])
    }
  };

  const validateEmail = (_, value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!value || emailRegex.test(value)) {
      return Promise.resolve();
    }
    return Promise.reject(new Error("请输入正确的邮箱地址"));
  };
  useEffect(() => {
    handleSearch("");
    if (!isViewMode && initialValues !== {}) {
      // initialValues.partnerCodes = initialValues.partnerCode ? initialValues.partnerCode.split(',') : []
      console.log('init', initialValues)
      // form.setFieldValue('partnerCodes', initialValues.partnerCodes )
    } else {
      form.setFieldValue('partnerCodes', [])
    }

  }, []);
  const handleSearch = _.debounce((e) => {
    console.log("handleSearch", e);
    post(allUrl.Partner.getAllPartner, { input: e }).then((res) => {
      if (res.success && res.resp.length) {
        const data = res.resp.map((item) => ({
          value: item.partnerName,
          text: item.partnerCode,
        }));
        setPartnerOptions(data);
      }
    });
  }, 500);
  const getPartnerList = (e) => {
    return post(allUrl.Partner.getAllPartner, { input: e }).then(res => {
      if (res.success && res.resp.length) {
        const data = res.resp.map((item) => ({
          name: item.partnerName,
          dealerCode: item.partnerCode,
        }));
        setPartnerOptions(data);
        return data
      } else {
        return []
      }
    })
  }
  return (
    <Modal open={visible} onCancel={onCancel} closable={true} footer={null} width={1200}>
      <Form
        form={form}
        onFinish={onFinish}
        initialValues={initialValues}
        labelCol={{ span: 14 }}
        wrapperCol={{ span: 10 }}
        layout="vertical"
      >
        {isViewMode ? (
          <h4 style={{ marginBottom: 20, fontSize: 18 }}>员工基础信息</h4>
        ) : (
          <h4 style={{ marginBottom: 20, fontSize: 18 }}>
            {initialValues.id ? "编辑" : "新增"}员工基础信息
          </h4>
        )}
        <Row>
          <Col span={8}>
            <Form.Item
              label="姓名"
              name="staffName"

              rules={[{ required: true, message: "" }]}
            >
              {isViewMode ? (
                <p>{initialValues.staffName || "-"}</p>
              ) : (
                <Input placeholder="请输入姓名" />
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="手机号"
              name="phone"
              rules={[
                { required: false, message: "请输入手机号！" },
                {
                  validator(_, value) {
                    if (value && !/^[1][0-9][0-9]{9}$/.test(value)) {
                      return Promise.reject(new Error("请输入正确的手机号！"));
                    } else {
                      return Promise.resolve();
                    }
                  },
                },
              ]}
            >
              {isViewMode ? <p>{initialValues.phone || "-"}</p> : <Input placeholder="请输入手机号" />}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="岗位"
              name="position"
              rules={[{ required: true, message: "" }]}
            >
              {isViewMode ? (
                <p>{initialValues.position || "-"}</p>
              ) : (
                <Input placeholder="请输入岗位" />
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="邮箱"
              name="emailAddress"
              rules={[{ validator: validateEmail }]}
            >
              {isViewMode ? (
                <p>{initialValues.emailAddress || "-"}</p>
              ) : (
                <Input placeholder="请输入邮箱" />
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="在职状态"
              name="isOnJob"
              rules={[{ required: true, message: "请选择合作状态" }]}
            >
              {isViewMode ? (
                <p>{renderStatusName(jobStatus, initialValues.isOnJob)}</p>
              ) : (
                <Select placeholder="请选择在职状态">
                  <Option value={1}>在职</Option>
                  <Option value={0}>离职</Option>
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="所属企业"
              name="partnerCodes"
              rules={[{ required: true, message: "" }]}
            >
              {isViewMode ? (
                <p>{initialValues.partnerName}</p>
              ) : (
                <DebounceSelect
                  showSearch
                  // initialValue={[initialValues.partnerCodes]}
                  mode="multiple"
                  allowClear
                  placeholder={'请选择所属企业'}
                  fetchOptions={getPartnerList}
                  onChange={searchPartnerCode}
                >
                  {/* {partnerOptions.map((option) => (
                    <Select.Option key={option.text} value={option.text}>
                      {option.value}
                    </Select.Option> */}
                  {/* ))} */}
                </DebounceSelect>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row justify="end" align="middle">
          {!isViewMode && (
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存
              </Button>
            </Form.Item>
          )}
        </Row>
      </Form>
    </Modal>
  );
};

export default PartnerForm;
