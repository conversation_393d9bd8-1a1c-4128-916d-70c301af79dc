import { postSeresFilePostFrequentSave } from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { Form, Input, Modal, ModalProps, Select } from 'antd';
import React, { FC, useCallback, useEffect, useState } from 'react';
import SupplierJobSelect from '../components/store-job-select';
import StoreJobSelect from '../components/supplier-job-select';
import { COMMON_JOB_TYPE_OPTIONS, CommonJobType } from '../constants';

export interface CommonJobModalProps extends Omit<ModalProps, 'onOk'> {
  onSuccess: () => void;
  data?: FileManage.FrequentPost;
}

const CommonJobModal: FC<CommonJobModalProps> = ({ onSuccess, data, ...props }) => {
  const [form] = Form.useForm<FileManage.FrequentPostVO>();
  const [pending, setPending] = useState(false);

  const handleModalOk = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setPending(true);
      await postSeresFilePostFrequentSave({
        ...data,
        ...values,
      });
      onSuccess();
    } catch (error) {
    } finally {
      setPending(false);
    }
  }, [data, form, onSuccess]);

  useEffect(() => {
    const codes = data?.codeList?.split(',') ?? [];
    const names = data?.nameList?.split(',') ?? [];
    form.setFieldsValue(
      {
        ...data,
        list: codes.map((code, index) => ({ code, name: names[index] })),
      } ?? {}
    );
  }, [data, form]);
  return (
    <Modal
      maskClosable={false}
      title={data?.id ? '编辑常用岗位' : '添加常用岗位'}
      {...props}
      onOk={handleModalOk}
      afterClose={form.resetFields}
      confirmLoading={pending}
    >
      <Form form={form} {...modalFormLayout} initialValues={{ type: CommonJobType.STORE }}>
        <Form.Item name="type" label="类型" rules={[{ required: true, message: '请选择常用岗位类型' }]}>
          <Select
            onChange={() => {
              form.resetFields(['list']);
            }}
            options={COMMON_JOB_TYPE_OPTIONS}
            placeholder="请选择常用岗位类型"
          />
        </Form.Item>
        <Form.Item name="name" label="常用岗位名称" rules={[{ required: true, message: '请输入常用岗位名称' }]}>
          <Input placeholder="请输入常用岗位名称" />
        </Form.Item>
        <Form.Item noStyle dependencies={['type']}>
          {({ getFieldValue }) => {
            const type = getFieldValue('type') as CommonJobType;
            const JobSelect = type === CommonJobType.STORE ? StoreJobSelect : SupplierJobSelect;

            return (
              <Form.Item
                name="list"
                label="岗位明细"
                rules={[{ required: true, message: '请选择岗位明细' }]}
                getValueFromEvent={(_, options) => {
                  return options;
                }}
                getValueProps={(value) => {
                  return {
                    value: value?.map((item) => item.code),
                  };
                }}
              >
                <JobSelect placeholder="请选择岗位明细" mode="multiple" />
              </Form.Item>
            );
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CommonJobModal;
