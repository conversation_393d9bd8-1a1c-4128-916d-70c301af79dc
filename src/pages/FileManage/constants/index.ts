import { fileManageListPermissions } from '@/permissions/file';
/**
 * 文件状态
 */
export enum FileStatus {
  /**
   * 已删除
   */
  DELETED = 0,
  /**
   * 待下发
   */
  PENDING = 1,
  /**
   * 已下发
   */
  ISSUED = 2,
  /**
   * 已过时
   */
  OUTDATED = 3,
  /**
   * 已作废
   */
  DEPRECATED = 4,
}

export const FILE_STATUS_MAP: Record<FileStatus, string> = {
  [FileStatus.DELETED]: '已删除',
  [FileStatus.PENDING]: '待下发',
  [FileStatus.ISSUED]: '已下发',
  [FileStatus.OUTDATED]: '已过时',
  [FileStatus.DEPRECATED]: '已作废',
};

export const FILE_STATUS_OPTIONS = Object.entries(FILE_STATUS_MAP)
  .map(([value, label]) => ({
    value: Number(value),
    label,
  }))
  .filter(({ value }) => value !== FileStatus.DELETED);

export enum FileBizType {
  /**
   * 服务
   */
  AFTER_SALES = 2,
  /**
   * 交付
   */
  DELIVERY = 1,
}

export const FILE_BIZ_TYPE_MAP: Record<FileBizType, string> = {
  [FileBizType.AFTER_SALES]: '服务类',
  [FileBizType.DELIVERY]: '交付类',
};

export const FILE_BIZ_TYPE_PERMISSION_MAP: Record<FileBizType, string> = {
  [FileBizType.AFTER_SALES]: fileManageListPermissions.afterSale.code,
  [FileBizType.DELIVERY]: fileManageListPermissions.delivery.code,
};

export const FILE_BIZ_TYPE_OPTIONS = Object.entries(FILE_BIZ_TYPE_MAP).map(([value, label]) => ({
  value: Number(value),
  label,
}));

export const FILE_BIZ_TYPE_TABS = [
  {
    key: FileBizType.AFTER_SALES.toString(),
    label: FILE_BIZ_TYPE_MAP[FileBizType.AFTER_SALES],
    code: FILE_BIZ_TYPE_PERMISSION_MAP[FileBizType.AFTER_SALES],
  },
  {
    key: FileBizType.DELIVERY.toString(),
    label: FILE_BIZ_TYPE_MAP[FileBizType.DELIVERY],
    code: FILE_BIZ_TYPE_PERMISSION_MAP[FileBizType.DELIVERY],
  },
];

/**
 * 根目录ID
 */
export const ROOT_DIRECTORY_ID = 0;

/**
 * 文件下发范围
 */
export enum FileDeliverType {
  /**
   * 所有门店
   */
  ALL_STORE = 1,
  /**
   * 指定门店
   */
  SPECIFIC_STORE = 2,
  /**
   * 所有供应商
   */
  ALL_SUPPLIER = 3,
  /**
   * 指定供应商
   */
  SPECIFIC_SUPPLIER = 4,
}

export const FILE_DELIVER_TYPE_MAP: Record<FileDeliverType, string> = {
  [FileDeliverType.ALL_STORE]: '所有门店',
  [FileDeliverType.SPECIFIC_STORE]: '指定门店',
  [FileDeliverType.ALL_SUPPLIER]: '所有供应商',
  [FileDeliverType.SPECIFIC_SUPPLIER]: '指定供应商',
};

export const FILE_DELIVER_TYPE_OPTIONS = Object.entries(FILE_DELIVER_TYPE_MAP).map(([value, label]) => ({
  value: Number(value),
  label,
}));

/**
 * 文件下发岗位
 */
export enum FilePostType {
  /**
   * 所有岗位
   */
  ALL_POST = 1,
  /**
   * 指定岗位
   */
  SPECIFIC_POST = 2,
}

export const FILE_POST_TYPE_MAP: Record<FilePostType, string> = {
  [FilePostType.ALL_POST]: '所有岗位',
  [FilePostType.SPECIFIC_POST]: '指定岗位',
};

export const FILE_POST_TYPE_OPTIONS = Object.entries(FILE_POST_TYPE_MAP).map(([value, label]) => ({
  value: Number(value),
  label,
}));

/**
 * 文件权限
 */
export enum FilePermission {
  /**
   * 浏览
   */
  VIEW = 1,
  /**
   * 下载
   */
  DOWNLOAD = 2,
}

export const FILE_PERMISSION_MAP: Record<FilePermission, string> = {
  [FilePermission.VIEW]: '仅可浏览',
  [FilePermission.DOWNLOAD]: '可浏览和下载',
};

export const FILE_PERMISSION_OPTIONS = Object.entries(FILE_PERMISSION_MAP).map(([value, label]) => ({
  value: Number(value),
  label,
}));

/**
 * 常用岗位类型
 */
export enum CommonJobType {
  /**
   * 门店
   */
  STORE = 0,
  /**
   * 供应商
   */
  SUPPLIER = 1,
}

export const COMMON_JOB_TYPE_MAP: Record<CommonJobType, string> = {
  [CommonJobType.STORE]: '门店',
  [CommonJobType.SUPPLIER]: '供应商',
};

export const COMMON_JOB_TYPE_OPTIONS = Object.entries(COMMON_JOB_TYPE_MAP).map(([value, label]) => ({
  value: Number(value),
  label,
}));

/**
 * 文件下发类型到常用岗位类型的映射
 */
export const FILE_DELIVER_TYPE_TO_COMMON_JOB_TYPE_MAP: Record<FileDeliverType, CommonJobType> = {
  [FileDeliverType.SPECIFIC_STORE]: CommonJobType.STORE,
  [FileDeliverType.ALL_STORE]: CommonJobType.STORE,
  [FileDeliverType.SPECIFIC_SUPPLIER]: CommonJobType.SUPPLIER,
  [FileDeliverType.ALL_SUPPLIER]: CommonJobType.SUPPLIER,
};

/**
 * 文件统计查询类型
 */
export enum StatisticQueryType {
  /**
   * 未浏览人员
   */
  NOT_VIEWED = 1,
  /**
   * 未下载人员
   */
  NOT_DOWNLOADED = 2,
}

export enum OperatedQueryType {
  /**
   * 已预览
   */
  VIEWED = 1,
  /**
   * 已下载
   */
  DOWNLOADED = 2,
}

export enum YesOrNo {
  YES = 1,
  NO = 0,
}

export const YES_OR_NO_OPTIONS = Object.entries({
  [YesOrNo.YES]: '是',
  [YesOrNo.NO]: '否',
}).map(([value, label]) => ({
  value: Number(value),
  label,
}));
