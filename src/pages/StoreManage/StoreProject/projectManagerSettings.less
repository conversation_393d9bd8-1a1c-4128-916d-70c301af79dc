.projectManagerSettings{
    .projectManagerSettings_con{
        padding: 32px;
        .projectManagerSettings_con_title{
            display: flex;
            align-items: center;
            .number{
                width: 26px;
                height: 26px;
                background: #1890ff;
                border-radius: 50%;
                font-size: 16px;
                font-family: HelveticaNeue, HelveticaNeue-Regular;
                font-weight: 400;
                text-align: center;
                color: #ffffff;
                margin-right: 10px;
            }
            .title{
                font-size: 16px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                color: rgba(0,0,0,0.85);
            }
        }
        .remarks{
            font-size: 12px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: rgba(0,0,0,0.65);
            margin-left: 36px;
            margin-top: 6px;
        }
        .projectManagerSettings_con_content{
            padding: 32px;
            .bindStore{
                border: 1px solid rgba(0,0,0,0.10);
                padding: 20px;
                overflow-y: auto;
                height: 350px;
                .bindStore_item{
                    display: flex;
                    justify-content: space-between;
                    padding: 4px 10px 4px 10px;
                    .title{
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Regular;
                        font-weight: 400;
                        color: rgba(0,0,0,0.65);
                    }
                    .icon{
                        line-height: 22px;
                        .anticon-close{
                            color:#a9a5a5;
                            cursor: pointer;
                        }
                    }
                }
                .bindStore_item:hover{
                    background-color: #edf9ff;
                }
            }
        }
    }
}