.SendingRange{
    .ant-modal-content{
        // width: 1184px;
        .SendingRangeLeft{
            .checkALL {
                margin-bottom: 10px;
                margin-left: 9px;
                margin-top: 10px;
            }
            .FilterConditionItem{
                display: inline-block;
                line-height: 36px;
                margin-right:10px;
            }
            .ant-modal-content {
                width: 1184px;
                height: 700px;
            }
            .PublicList_FormQuery{
                .ant-row{
                    .ant-col:first-child{
                        .ant-row{
                            width:147px;
                            .ant-form-item-control-input{
                                width:140px;
                            }
                        }
                    }
                    .ant-col:nth-child(3){
                        .ant-col-8{
                            max-width: 31.333333%;
                        }
                        .ant-col-16{
                            max-width: 68.666667%;
                        }
                    }
                    .FormQuerySubmit{
                        display: flex;
                        .Reset{
                            max-width: 22%;
                            margin-left: 20px;
                            margin-right: 14px;
                        }
                        .Search{
                            max-width: 28%;
                            margin-left: 20px;
                            margin-right: 14px;
                        }
                    }
                }
            }
            .ant-table-wrapper{
                .ant-table-container{
                    .ant-table-body{
                        overflow: auto auto !important;
                    }
                }
            }
            .ant-tree{
                .ant-tree-list{
                    height: 548px;
                    overflow-y: auto;
                    .ant-tree-node-selected{
                        background: white;
                    }
                }
            }
        }
        .SendingRangeRight{
            .title{
                height:46px;
                padding:14px;
                padding-left:20px;
                background:#D7ECFF;
                color:rgba(0,0,0,0.65);
                font-size:14px;
            }
            .contentBox{
                background:#EFF7FF;
                padding-top:18px;
                padding-right:10px;
                padding-left:10px;
                height:600px;
                overflow-y:auto;
                border-radius: 2px;
                .content_Item{
                    // .treeNode {
                        // display: flex;
                        // justify-content: space-between;
                    // }
                    overflow: hidden;
                    .name{
                        float: left;
                        padding: 6px 0px 6px 10px;
                        overflow: hidden;
                        width: 310px;
                        text-overflow:ellipsis;
                        white-space: nowrap;
                    }
                    .del{
                        float: right;
                        padding: 6px 10px;
                        text-align: center;
                        img{
                            margin-right:-4px;
                            width: 16px;
                            opacity: 0.65;
                            cursor:pointer;
                        }
                    }
                }
                // .content_Item {
                //     background-color: #cee2f6;
                // }
                .content_Item:hover{
                    background-color: #cee2f6;
                }
                .content_Item:hover .del{
                    background-color: #cee2f6;
                }
                .separate-line{
                    margin-top: 14px;
                    padding-bottom: 14px;
                    width: 100%;
                    height: 1px;
                    background-image: linear-gradient(to right, #C4D5E6 0%, #C4D5E6 70%, transparent 50%);
                    background-size: 8px 1px;
                    background-repeat: repeat-x;
                }
                
            }
            .ant-tree {
                background-color: #EFF7FF;
                .ant-tree-treenode{
                    width: 100%;
                }
                .ant-tree-node-content-wrapper{
                    width: 100%;
                    padding: 0;
                }
                .ant-tree-switcher {
                    align-self: center;
                }
            }
        }
    }
}
