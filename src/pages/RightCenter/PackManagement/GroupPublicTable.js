import React, { memo, useState } from 'react';
import { Table, Tag, Button, Divider } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import _ from 'lodash';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
import { extendFlag, groupFlag } from '../publicMethods';
import styles from '../antd.module.less';

const DetailPubliTable = props => {
  const { className, dataSource, loading, rowKey, pagination, rowSelection, children, strategy, equityType } = props;
  const [detail, setDetail] = useState({});

  const renderHtml = data => (
    <div className="pack-table-merge-wrap">
      {data.map((item, index) => {
        return (
          <>
            {/* 明细信息 */}
            <div className="pack-table-row">
              <div className="detai">{item}</div>
            </div>
          </>
        );
      })}
    </div>
  );

  const outsideCombination = data => {
    return (
      <>
        <div style={{ position: 'relative' }}>
          <div className={`${styles['custom-mark']} ${styles['bg3']}`} style={{ top: -8, left: -25 }}>
            <div className={styles['custom-mark-content']}>
              <span>组合</span>
            </div>
          </div>
        </div>
        {renderHtml(data)}
      </>
    );
  };

  return (
    <>
      <Table
        className={className}
        dataSource={dataSource}
        loading={loading}
        rowKey={rowKey}
        size="small"
        scroll={{ x: 3140 }}
        bordered
        pagination={pagination}
        rowSelection={rowSelection}
        rowClassName={(record, index) => {
          return `${styles['table-row-height72']}`;
        }}
      >
        <Table.Column
          title="组合明细编码"
          key="groupDetailNo"
          dataIndex="groupDetailNo"
          width={160}
          render={text => (text ? groupFlag({ text }) : '-')}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细编码"
          key="detailNo"
          dataIndex="detailNo"
          width={200}
          fixed="left"
          className={`${styles['table-pad-left']} ${styles['pack-table-merge-column']}`}
          render={(text, record) => {
            if (record.detailNoList && !!record.detailNoList.length) {
              //组合
              return outsideCombination(record.detailNoList);
            }
            // 其他
            return text;
          }}
        />
        <Table.Column
          title="权益明细分类"
          key="sortCodeName"
          dataIndex="sortCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细名称"
          key="name"
          dataIndex="name"
          render={text => text || '-'}
          width={200}
          fixed="left"
        />
        <Table.Column
          title="权益业务场景"
          key="businessSceneName"
          dataIndex="businessSceneName"
          render={text => text || '-'}
          width={120}
        />
        <Table.Column
          title="权益类别"
          key="typeCodeName"
          dataIndex="typeCodeName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="车辆属性"
          key="carAttrName"
          dataIndex="carAttrName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益归属"
          key="belongName"
          dataIndex="belongName"
          width={120}
          render={text => text || '-'}
        />
        {strategy === 'in' ? (
          <Table.Column
            title="延保激活时间"
            key="extendWarrantyActiveTimeName"
            dataIndex="extendWarrantyActiveTimeName"
            width={120}
            render={text => text || '-'}
          />
        ) : null}
        <Table.Column
          title="权益限制年限"
          key="limitYear"
          dataIndex="limitYear"
          width={120}
          className={styles['table-pad-left']}
          render={(text, record) => {
            let span = <span>-</span>;
            if (record.limitTimeFrame == -1) {
              span = <span>永久有效</span>;
            } else if (record.limitTimeFrame == 1) {
              span = (
                <span>{`${Number(record.limitYear)}年${Number(record.limitMonth)}月${Number(record.limitDay)}日`}</span>
              );
            }
            // if (record.businessScene === 2) {
            //   return extendFlag({ text: span });
            // } else {
            //   return span;
            // }
            return record.groupDetailNo ? groupFlag({ text: span }) : span;
          }}
        />
        <Table.Column
          title="权益生效时间"
          key="relativeTime"
          dataIndex="relativeTime"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            let tmpl = '';
            if (record.relativeTime !== null) {
              tmpl = record.relativeTimeName ?? '-';
            } else if (record.fixedBeginTime && record.fixedEndTime) {
              tmpl =
                moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') +
                ' ~ ' +
                moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss');
            } else {
              tmpl = '-';
            }
            // if (record.businessScene === 2) {
            //   return extendFlag({ text: tmpl });
            // } else {
            //   return tmpl;
            // }
            return record.groupDetailNo ? groupFlag({ text: tmpl }) : tmpl;
          }}
        />
        <Table.Column
          title="权益限制里程(基准)"
          key="limitLegend"
          dataIndex="limitLegend"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            const limitLegendFrameMap = {
              0: () => '-', //无此策略
              1: () => (
                <span>
                  {record.limitLegend ?? '-'}
                  <Tag className={[styles['custom-ant-tag-green'], styles['ml5']]}>行驶</Tag>
                </span>
              ), //行驶里程
              2: () => (
                <span>
                  {record.extenderMileage ?? '-'}
                  <Tag className={[styles['custom-ant-tag-yellow'], styles['ml5']]}>增程器</Tag>
                </span>
              ), //增程器里程
            };

            let tmpl = '';
            if (record.limitLegendFrame === null) {
              tmpl = record.limitLegend || record.extenderMileage;
            } else {
              tmpl = limitLegendFrameMap[record.limitLegendFrame]();
            }

            if (record.businessScene === 2) {
              return extendFlag({ text: tmpl });
            } else {
              return tmpl;
            }
            // if (record.limitLegendFrame === null) {
            //     return record.limitLegend || record.extenderMileage
            // } else {
            //     return limitLegendFrameMap[record.limitLegendFrame]()
            // }
          }}
        />
        <Table.Column
          title="权益生效里程"
          key="effectMileageFrame"
          dataIndex="effectMileageFrame"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            const effectMileageFrameMap = {
              //无此策略
              0: '-',
              //固定里程
              1: record.fixedBeginMileage ?? '-',
              //相对里程
              2: record.relativeBeginMileageStrategyName,
            };
            if (record.businessScene === 2) {
              return extendFlag({ text: effectMileageFrameMap[record.effectMileageFrame] });
            } else {
              return effectMileageFrameMap[record.effectMileageFrame];
            }
          }}
        />
        <Table.Column
          title="享有权益频次"
          key="frequency"
          dataIndex="frequency"
          width={120}
          render={text => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限次数' : text + '次';
            } else {
              a = '-';
            }
            return a;
          }}
        />
        <Table.Column
          title="车辆权益变更属性"
          key="identityName"
          dataIndex="identityName"
          width={160}
          render={text => text || '-'}
        />
        <Table.Column
          title="享有车主身份"
          key="carIdentityName"
          dataIndex="carIdentityName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="商品分类"
          key="goodsCategoryName"
          dataIndex="goodsCategoryName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="全保养阶梯策略"
          key="maintenanceLadderDesc"
          dataIndex="maintenanceLadderDesc"
          width={120}
          render={(text, record) => {
            if (text === '-') return '-';
            // return <>{text}<Button type='link' size='small' onClick={() => getLadderData(record)}>详情</Button></>
            return <>{text}</>;
          }}
        />
        <Table.Column
          title="补漆面部位"
          key="paintDesc"
          dataIndex="paintDesc"
          width={200}
          ellipsis
          render={text => (text ? <PublicTooltip title={text}>{text}</PublicTooltip> : '-')}
        />
        <Table.Column
          title="车联网流量"
          key="traffic"
          dataIndex="traffic"
          width={120}
          render={(text, record) => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限流量' : text + record.trafficUnit;
            } else {
              a = '-';
            }
            return a;
          }}
        />
        <Table.Column
          title="权益抵扣金额"
          key="deduction"
          dataIndex="deduction"
          width={200}
          render={(text, record) => {
            return record.payAmount && record.deduction ? (
              <span>
                {record.payAmount} 元 ~ {record.deduction} 元
              </span>
            ) : (
              '-'
            );
          }}
        />
        <Table.Column
          title="权益履约方"
          key="performingDesc"
          dataIndex="performingDesc"
          width={200}
          ellipsis
          render={text => (text ? <PublicTooltip title={text}>{text}</PublicTooltip> : '-')}
        />
        <Table.Column
          title="履约结算价格"
          key="settlementPriceDesc"
          dataIndex="settlementPriceDesc"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="延保承保方"
          key="extendWarrantyAssurerName"
          dataIndex="extendWarrantyAssurerName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="业务编码"
          key="businessCodeDesc"
          dataIndex="businessCodeDesc"
          width={120}
          render={(text, record) => {
            if (record.businessCodeDescList && !!record.businessCodeDescList.length) {
              return [...new Set(record.businessCodeDescList)].join('、');
            }
            return text || '-';
          }}
        />
        <Table.Column
          title="是否支持退款"
          key="refundName"
          dataIndex="refundName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="是否三方履约"
          key="performanceName"
          dataIndex="performanceName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益声明"
          key="statement"
          dataIndex="statement"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益备注"
          key="remark"
          dataIndex="remark"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益明细版本编码"
          key="detailVersion"
          dataIndex="detailVersion"
          width={200}
          className={styles['pack-table-merge-column']}
          ellipsis
          render={(text, record) => {
            if (record.detailVersionList && !!record.detailVersionList.length) {
              return renderHtml(record.detailVersionList);
            }
            return text || '-';
          }}
        />
        <Table.Column
          title="客户端展示形式"
          key="displayFrame"
          dataIndex="displayFrame"
          width={120}
          ellipsis
          render={(text, record) => {
            const obj = {
              1: '并列',
              2: '聚合',
              3: '组合',
            };
            return text ? obj[text] : '-';
          }}
        />
        <Table.Column
          title="是否在客户端展示"
          key="isDisplay"
          dataIndex="isDisplay"
          width={150}
          ellipsis
          render={(text, record) => {
            const obj = {
              1: '在客户端展示',
              2: '不在客户端展示',
            };
            return record.isDisplay ? obj[record.isDisplay] : '-';
          }}
        />
      </Table>
    </>
  );
};
export default memo(DetailPubliTable);
