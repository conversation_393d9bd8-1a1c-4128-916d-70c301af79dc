import React from 'react'
import { Modal, message, Form, Input,Select,Row,Col,Spin,} from 'antd'
import { SyncOutlined } from '@ant-design/icons';
import allUrl from '../../../../utils/url'
import { post, get } from '../../../../utils/request'
import './Detail.less'
import { connect } from 'react-redux'
const {Option} = Select
class ClueDetail extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {
            loading:false,
            formData:{}
        }
    }
    formRef = React.createRef();

    ininForm = () => {
        const { recordData } = this.props
        this.setState(
            {
                loading:true
            }
        )
        get(allUrl.ClueManagement.getClueObj,{id:recordData.id,distributionId:recordData.distributionId}).then(res=>{
            if(res.success){
                this.setState(
                    {
                        loading:false,
                        formData:res.resp[0]

                    }
                )
            }else{
                message.error(res.msg)
                
            }
        })

    }
    refreshClueOrderStatus = () =>{
        const { recordData } = this.props
        
        let data ={id:recordData.id}
        post(allUrl.ClueManagement.refreshClueOrderStatus,{...data}).then(res=>{
            if(res.success){
                this.ininForm()
            }else{
                message.error(res.msg)
                
            }
        })
    }
    invalidStoreClue = () =>{
        const { recordData,getData } = this.props
        
        let data ={id:recordData.id}
        post(allUrl.ClueManagement.invalidStoreClue,{...data}).then(res=>{
            if(res.success){
                this.ininForm()
                getData()
            }else{
                message.error(res.msg)
                
            }
        })
    }

    render() {
        const { visible, title,onCancel,getData } = this.props
        const { loading,formData } = this.state
        console.log(this.state.current)
        const layout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 20 },
        };
            return (
                <Modal visible={visible} title={title} onCancel={onCancel} getData={getData} maskClosable={false}width={800} footer={null}>
                    <Spin spinning={loading}>
                        <Row>
                            <Col span={8} style={{fontSize:'20px',marginBottom:'12px'}}>用户信息</Col>
                        </Row>
                        <Row>
                            <Col span={8} ><Form.Item label='客户姓名'>{formData.name ? formData.name :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='联系电话'>{formData.phone ? formData.phone :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='战区'>{formData.bigAreaName ? formData.bigAreaName :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='省份'>{formData.provinceName ? formData.provinceName :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='城市'>{formData.cityName ? formData.cityName :'-'}</Form.Item></Col>
                        </Row>
                        <Row>
                            <Col span={8} style={{fontSize:'20px',marginBottom:'12px'}}>线索信息</Col>
                        </Row>
                        <Row>
                            <Col span={8} ><Form.Item label='门店编码'>{formData.dealerCode ? formData.dealerCode :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='门店名称'>{formData.dealerName ? formData.dealerName :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='购车意向城市'>{formData.city ? formData.city :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='意向购车型号'>{formData.carType ? formData.carType :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='留资时间'>{formData.infoTime ? formData.infoTime :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='提交时间'>{formData.reserveTime ? formData.reserveTime :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='分配时间'>{formData.distributionTime ? formData.distributionTime :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='接收门店编码'>{formData.receiveDealerCode ? formData.receiveDealerCode :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='接收门店名称'>{formData.receiveDealerName ? formData.receiveDealerName :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='导入标签'>{formData.importTagName ? formData.importTagName :'-'}</Form.Item></Col>
                        </Row>
                        <Row>
                            <Col span={8} style={{fontSize:'20px',marginBottom:'12px'}}>线索状态
                            <a style={{marginLeft:"20px"}} onClick={this.refreshClueOrderStatus}><SyncOutlined /></a>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={8} ><Form.Item label='线索状态'>{formData.statusName ? formData.statusName :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='定单交付状态'>{formData.orderStatusName ? formData.orderStatusName :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='结算状态'>{formData.settleStatusName ? formData.settleStatusName :'-'}</Form.Item></Col>
                            <Col span={8} ><Form.Item label='是否有效'>{formData.invalidName ? formData.invalidName :'-'}</Form.Item></Col>
                            {formData.invalidName == '有效' ? 
                            <Col span={16} ><Form.Item label=''><div onClick={this.invalidStoreClue} style={{color:'#1890ff',cursor:'pointer'}}>线索失效</div></Form.Item></Col>
                            : null }
                            <Col span={24} ><Form.Item label='外部关联订单号'>{formData.externalNo ? formData.externalNo :'-'}</Form.Item></Col>
                        </Row>
                        <Row>
                            <Col span={8} style={{fontSize:'20px',marginBottom:'12px'}}>备注</Col>
                        </Row>
                        <Row>
                            <Col span={24} ><Form.Item label=''>{formData.remark ? formData.remark :'-'}</Form.Item></Col>
                        </Row>
                    </Spin>
                </Modal>
            )
        }
        componentDidMount(){
            this.ininForm()
        }
    }
    export default connect()(ClueDetail)