import React, { useMemo, useEffect, useRef, useState, useCallback } from 'react';

import {
  Form,
  Input,
  InputNumber,
  Space,
  Select,
  Button,
  DatePicker,
  Radio,
  message,
  Modal,
  Cascader,
  Checkbox,
  TreeSelect,
} from 'antd';
import moment from 'moment';
import _ from 'lodash';
import history from '@/utils/history';
import { post, get, axiosDelete } from '../../../utils/request';
import allUrl from '../../../utils/url';
import styles from '../antd.module.less';
import { UPGRADE_DETAILAPI } from './upgradeSetting';
import { ExclamationCircleFilled } from '@ant-design/icons';

import CarConfigModal from './carConfig';
import formStyle from './create.form.module.less';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { confirm } = Modal;
const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 14 },
};
const dateFormat = 'YYYY-MM-DD';

const Component = props => {
  const {
    initPage,
    id,
    dataDetail,
    Type,
    tableList,
    getData,
    setData,
    checkDetailUpdate,
    checkSameConfigPack,
    validateCarConfig,
  } = props;
  const [form] = Form.useForm();
  const formRef = useRef(null);

  const [attrValue, setAttrValue] = useState('');
  // const [typeValue, setTypeValue] = useState('') 这里被挪到外面了
  const [typeValue2, setTypeValue2] = useState('');
  // const [carTypeValue, setCarTypeValue] = useState('')
  // const [entryList,setEntryList] = useState({})  //权益车型
  const [entryList2, setEntryList2] = useState({}); //权益包付费属性
  const [entryList3, setEntryList3] = useState({}); //权益包属性
  const [entryList4, setEntryList4] = useState({}); //车辆销售类型
  const dataSourceForm = useMemo(() => props.dataSource, [props.dataSource]);
  const [carTypeData, setCarTypeData] = useState([]);
  // const [carConfigData,setCarConfigData] = useState([])
  const [carMarketData, setCarMarketData] = useState([]);
  const [backWidth, setBackWidth] = useState({});
  const [promotionStatusDisplay, setPromotionStatusDisplay] = useState(0);
  const [allAreaDisplay, setallAreaDisplay] = useState(0);
  const [allDealerDisplay, setAllDealerDisplay] = useState(0);
  const [equitySourceDisplay, setEquitySourceDisplay] = useState(1);
  const [activityDisplay, setActivityDisplay] = useState(true);
  const [price, setPrice] = useState(0); // 销售价格
  const priceRef = useRef(0);
  const percentageRef = useRef(0);
  const [salesIncome, setSalesIncome] = useState(0);
  const [promotionTimeFrameDisplay, setPromotionTimeFrameDisplay] = useState(1);
  //车型配置弹窗
  const [showCarConfig, setShowCarConfig] = useState(false);
  const [allSelectedConfigObj, setAllSelectedConfigObj] = useState({}); //所有数据 {"车型code":["车型code/2023/EVR/9999","车型code/2023/EVR/31"]}
  const [carConfigInfo, setCarConfigInfo] = useState([]); //allSelectedConfigObj数据的扁平化，格式["2023/EVR/9999","2023/EVR/31"]
  //权益车型全选
  const [indeterminateCarType, setIndeterminateCarType] = useState(null);
  const [checkAllCarType, setCheckAllCarType] = useState(false);

  const { setCarConfigData, setCarTypeHasValue, setLoading, setTypeValue, check, setTableList, setGroupDetails } =
    setData;
  const { carTypeHasValue, carConfigData, typeValue, groupDetails } = getData;

  useEffect(() => {
    let item = document.getElementsByClassName('Content-con')[0];
    setBackWidth({ width: `${item.clientWidth}px`, height: '60px' });
  }, []);

  //车型配置相关方法部分  start
  const getModalData = val => {
    if (val) {
      const TMPL = Object.values(val).flat();
      form.setFieldsValue({ tmplCarConfig: TMPL });
      setCarConfigInfo(TMPL);
      setAllSelectedConfigObj(val);
    }
  };

  const handleModal = () => {
    if (carTypeHasValue) {
      setShowCarConfig(true);
    }
  };

  //编辑状态下，处理后段返回的数据成 ["1/2023/EVR/9999","3/2023/EVR/31"]
  const handleCarConfigData = data => {
    if (!!data?.length) {
      return data.map(item => item.carType + '/' + item.carOfYear + '/' + item.powerModelCode + '/' + item.carConfig);
    }
  };

  //返回的数据成 {1:[{},{}]}
  const handleCarData = (key, data) => {
    return data.reduce((acc, current) => {
      const keyValue = current[key];
      if (!acc[keyValue]) {
        acc[keyValue] = [];
      }
      acc[keyValue].push(
        current.carType + '/' + current.carOfYear + '/' + current.powerModelCode + '/' + current.carConfig
      );
      return acc;
    }, {});
  };

  //车型配置想干方法部分  end

  useEffect(() => {
    if (dataSourceForm) {
      setTypeValue(dataSourceForm.type);
      formRef.current &&
        form.setFieldsValue({
          ...dataSourceForm,
          activityTime:
            dataSourceForm.activityBeginTime && dataSourceForm.activityEndTime
              ? [moment(dataSourceForm.activityBeginTime), moment(dataSourceForm.activityEndTime)]
              : null,
          promotionTime:
            dataSourceForm.promotionStartTime && dataSourceForm.promotionEndTime
              ? [moment(dataSourceForm.promotionStartTime), moment(dataSourceForm.promotionEndTime)]
              : null,
          promotionQuotaFrame: dataSourceForm.promotionQuota > 0 ? 1 : 0,
          //第一次创建权益包，权益包类型选择基础权益包时，promotionExist这个字段，前端都没传，数据库给了默认值0；编辑权益包，啥都不改，promotionExist这个字段后端又变成了null,后端设计有问题。
          //权益包类型选择基础权益包时，这里特殊处理为null,是为了前端处理这个字段的默认值选择1
          promotionExist: dataSourceForm.type === 1 ? null : dataSourceForm.promotionExist,
          tmplCarConfig: handleCarConfigData(dataSourceForm.tmplCarConfig) || [],
        });
      // 活动名额
      promotionStatusChange(dataSourceForm.promotionQuota > 0 ? 1 : 0);
      //是否应用于互动中
      promotionExistChange(dataSourceForm.type === 1 ? null : dataSourceForm.promotionExist);
      //活动时间
      promotionTimeFrameChange(dataSourceForm.promotionTimeFrame);
      if (dataSourceForm.type == 2 || dataSourceForm.type == 4 || dataSourceForm.type == 5) {
        formRef.current &&
          form.setFieldsValue({
            askingTime:
              dataSourceForm.askingStartTime && dataSourceForm.askingEndTime
                ? [moment(dataSourceForm.askingStartTime), moment(dataSourceForm.askingEndTime)]
                : null,
          });
        setEquitySourceDisplay(dataSourceForm.equitySource);

        priceRef.current = dataSourceForm.price || 0;
        percentageRef.current = dataSourceForm.percentage || 0;
        if (dataSourceForm.price > 0 && dataSourceForm.percentage > 0) {
          // priceRef.current = dataSourceForm.price
          // percentageRef.current = dataSourceForm.percentage
          setSalesIncome(dataSourceForm.price * ((100 - Number(dataSourceForm.percentage)) / 100));
        }
      }
      //车型配置回显
      setCarConfigInfo(handleCarConfigData(dataSourceForm.tmplCarConfig) || []);
      setAllSelectedConfigObj(handleCarData('carType', dataSourceForm.tmplCarConfig) || []);
      //权益车型全选按钮
      setIndeterminateCarType(!!dataSourceForm.carType.length && dataSourceForm.carType.length < carTypeData.length);
      setCheckAllCarType(dataSourceForm.carType.length === carTypeData.length);
    }
  }, [dataSourceForm, form]);

  useEffect(() => {
    get(allUrl.common.entryLists, { codes: 'equity_pay_type,equity_trilateral_sales, equity_direct_sales' }).then(
      res => {
        if (res.success) {
          let Dt = res.resp[0];
          for (let i in Dt) {
            Dt[i].forEach(item => {
              item.name = item.entryMeaning;
              item.value = item.entryValue;
            });
          }
          setEntryList2(Dt || {});
        } else {
          // message.error(res.message)
        }
      }
    );
  }, []);
  // 车辆销售类型
  useEffect(() => {
    get(allUrl.common.entryLists, { codes: 'pack_sales_type' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }
        setEntryList4(Dt || {});
      } else {
        // message.error(res.message)
      }
    });
  }, []);

  useEffect(() => {
    get(allUrl.common.entryLists, { codes: 'equity_attribute' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning;
            item.value = item.entryValue;
          });
        }
        setEntryList3(Dt || {});
      } else {
        // message.error(res.message)
      }
    });
  }, []);
  const onFinish = async values => {
    let query = queryHandler(values);
    if (validate(query)) return;
    const isNeedUpdate = await checkDetailUpdate();
    if (isNeedUpdate) return;
    props.onCreate && props.onCreate(query);
  };

  const validate = query => {
    if (query.promotionTimeFrame === 1) {
      if (!query.promotionEndTime || !query.promotionStartTime) {
        message.error('请选择活动时间！');
        return true;
      }
      //v13版本增加校验：发布和保存草稿时，若填写的活动时间和宣传时间无交集，则提示权益包无法生效，保存不了。
      const pEndTime = new Date(query.promotionEndTime).getTime();
      const pStartTime = new Date(query.promotionStartTime).getTime();
      const aBeginTime = new Date(query.activityBeginTime).getTime();
      const aEndTime = new Date(query.activityEndTime).getTime();
      if (pEndTime < aBeginTime || pStartTime > aEndTime) {
        message.error('当前选择的活动时间与宣传时间无交集，权益包无法生效，请检查！');
        return true;
      }
    }
  };

  const onChangeAttr = e => {
    setAttrValue(e.target.value);
  };

  const onChangeType = e => {
    let _value = e.target.value;
    // setTypeValue(_value);
    // 切换成功之后处理
    const changed = () => {
      {
        //V23版本：权益包类型切换，车型、车型配置清空,全部车型选项重置Indeterminate
        form.setFieldsValue({ carType: [] });
        resetCarCofig();
        setIndeterminateCarType(false);
        setCheckAllCarType(false);
      }
      setTypeValue(prev => {
        // if ((prev !== 6 && _value === 6) || (prev === 6 && _value !== 6)) {
        setTableList([]);
        setGroupDetails([]);
        // }
        return _value;
      });
      if (_value === 2 || _value === 4 || _value === 5) {
        setFieldValue('equitySource', 1);
        setFieldValue('promotionInitiator', 1);
        setFieldValue('salesSubject', 1);
        setFieldValue('equitySalesSubject', '0000090375');
        setFieldValue('promotionExist', 1);
        promotionExistChange(1);
        setFieldValue('promotionTimeFrame', 1);
        promotionTimeFrameChange(1);
      }
      if (_value === 4) {
        // 赠送权益包 固定选择不在App展示
        form.setFieldValue('display', 1);
      } else {
        form.setFieldValue('display', 0);
      }
    };
    if (tableList.length) {
      // 如果关联权益包有内容
      confirm({
        title: '切换权益包类型将清空已关联权益、组合及升级信息，请确认是否切换？',
        icon: <ExclamationCircleFilled />,
        // content: '取消后，已填写的组合信息将不再被保留，明细与权益包关联关系不变。',
        okText: '确认',
        onOk() {
          changed();
        },
        onCancel() {
          form.setFieldValue('type', typeValue);
        },
      });
    } else {
      changed();
    }
  };

  // const onChangeType = e => {
  //   let _value = e.target.value;
  //   {
  //     //V23版本：权益包类型切换，车型、车型配置清空,全部车型选项重置Indeterminate
  //     form.setFieldsValue({ carType: [] });
  //     resetCarCofig();
  //     setIndeterminateCarType(false);
  //     setCheckAllCarType(false);
  //   }

  //   // setTypeValue(_value);
  //   setTypeValue(prev => {
  //     if ((prev !== 6 && _value === 6) || (prev === 6 && _value !== 6)) {
  //       setTableList([]);
  //     }
  //     return _value;
  //   });
  //   if (_value === 2 || _value === 4 || _value === 5) {
  //     setFieldValue('equitySource', 1);
  //     setFieldValue('promotionInitiator', 1);
  //     setFieldValue('salesSubject', 1);
  //     setFieldValue('equitySalesSubject', '0000090375');
  //     setFieldValue('promotionExist', 1);
  //     promotionExistChange(1);
  //     setFieldValue('promotionTimeFrame', 1);
  //     promotionTimeFrameChange(1);
  //   }
  //   if (_value === 4) {
  //     // 赠送权益包 固定选择不在App展示
  //     form.setFieldValue('display', 1);
  //   } else {
  //     changed();
  //   }
  // };

  /**
   *
   * @param {*} field
   * @param {*} value
   *
   * field字段值为null 或者 undefined ,则设置默认值value
   */
  const setFieldValue = (field, value) => {
    form.getFieldValue(field) ?? form.setFieldValue(field, value);
  };
  // 车辆销售类型
  const onChangeType2 = e => {
    // console.log('radio checked', e.target.value,typeof(e.target.value));
    setTypeValue2(e.target.value);
  };

  useEffect(() => {
    carTypeChange();
  }, []);
  // 权益车型接口
  const carTypeChange = () => {
    get(allUrl.EquityPackageManage.getCarType).then(res => {
      if (res.success) {
        setCarTypeData(res.resp);
      } else {
        // message.error(res.msg)
      }
    });
  };
  // 权益车型配置接口
  const carConfigChange = useCallback(
    _.debounce(carTypeList => {
      get(allUrl.EquityPackageManage.getCarConfig, { carTypeList: carTypeList.join(',') }).then(res => {
        if (res.success) {
          setCarConfigData(res.resp);
        }
      });
      if (!!carTypeList?.length) {
        setCarTypeHasValue(true);
      } else {
        setCarTypeHasValue(false);
      }
    }, 500),
    []
  );

  useEffect(() => {
    return () => {
      carConfigChange.cancel();
    };
  }, []);
  // 车辆市场类型
  useEffect(() => {
    post(allUrl.EquityPackageManage.getCarMarketTypeAll).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        Dt.forEach((item, index) => {
          item.key = index + 1;
          item.value = item.code;
        });
        setCarMarketData(Dt);
      } else {
        // message.error(res.message)
      }
    });
  }, []);
  // 活动名额
  const promotionStatusChange = value => {
    setPromotionStatusDisplay(value);
  };
  //活动时间
  const promotionTimeFrameChange = value => {
    setPromotionTimeFrameDisplay(value);
  };
  // 支持区域
  const allAreaChange = value => {
    setallAreaDisplay(value);
  };
  // 支持门店
  const allDealerChange = value => [setAllDealerDisplay(value)];
  // 权益来源
  const equitySourceChange = value => {
    setEquitySourceDisplay(value);
    form.setFieldsValue({
      salesChannel: '',
    });
  };
  //是否应用于活动
  const promotionExistChange = value => {
    if (value === 1) {
      setActivityDisplay(true);
      setFieldValue('promotionTimeFrame', 1);
      promotionTimeFrameChange(1);
    } else {
      setActivityDisplay(false);
    }
  };
  // 销售价格
  const priceHandler = (e, type) => {
    // if (type == 'price') {
    //   priceRef.current = e
    //   if (percentageRef.current > 0) {
    //     setSalesIncome((e) * ((100 - percentageRef.current) / 100))
    //   }
    // }
    // if (type == 'percentage') {
    //   percentageRef.current = e
    //   if (priceRef.current > 0) {
    //     setSalesIncome(priceRef.current * ((100 - e) / 100))
    //   }
    // }

    type === 'price' && (priceRef.current = e);
    type === 'percentage' && (percentageRef.current = e);

    if (percentageRef.current > 0 && priceRef.current > 0) {
      setSalesIncome(priceRef.current * ((100 - percentageRef.current) / 100));
    } else {
      setSalesIncome(0);
    }
  };
  const queryHandler = query => {
    //  车型配置数据组装[{}]
    const arr = query?.tmplCarConfig || [];
    if (!!arr.length) {
      const arrTmpl = [];
      arr.forEach(item => {
        const tmpl = item.split('/');
        arrTmpl.push({
          carType: tmpl[0],
          carOfYear: tmpl[1],
          powerModelCode: tmpl[2],
          carConfig: tmpl[3],
        });
      });
      query.carConfigList = arrTmpl;
    }
    //权益车型
    query.carType_alias = query.carType; //用于后面车型配置选择的校验
    query.carType = null;

    // 活动时间
    if (query.promotionTimeFrame === 0) {
      delete query.promotionTime;
    }
    if (query.promotionTime && query.promotionTime.length) {
      query.promotionStartTime = moment(query.promotionTime[0]).format('YYYY-MM-DD HH:mm:ss');
      query.promotionEndTime = moment(query.promotionTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete query.promotionTime;
    }
    // 宣传时间
    if (query.activityTime && query.activityTime.length) {
      query.activityBeginTime = moment(query.activityTime[0]).format('YYYY-MM-DD HH:mm:ss');
      query.activityEndTime = moment(query.activityTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete query.activityTime;
    } else {
      query.activityBeginTime = '';
      query.activityEndTime = '';
    }
    // 付费权益包
    if (typeValue === 2 || typeValue === 4 || typeValue === 5) {
      // 权益请示活动有效期
      if (query.askingTime && query.askingTime.length) {
        query.askingStartTime = moment(query.askingTime[0]).format('YYYY-MM-DD HH:mm:ss');
        query.askingEndTime = moment(query.askingTime[1]).format('YYYY-MM-DD HH:mm:ss');
      }
      // // 权益来源默认三方采购
      // if (query.equitySource == undefined) {
      //   query.equitySource = 1
      // }
      // // 活动发起部门
      // if (query.promotionInitiator == undefined) {
      //   query.promotionInitiator = 1
      // }
      // // 销售结算主体
      // if (query.salesSubject == undefined) {
      //   query.salesSubject = 1
      // }
      // // 销售包主体
      // if (query.equitySalesSubject == undefined) {
      //   query.equitySalesSubject = '0000090375'
      // }
      // //是否应用于活动
      // if(query.promotionExist == undefined){
      //   query.promotionExist = 1
      // }
      // 销售收入
      if (salesIncome > 0) {
        query.salesIncome = salesIncome;
      }
    }

    let relationDetails = [];
    tableList.map((item, index) => {
      relationDetails[index] = {
        originDetail: {
          detailNo: item[UPGRADE_DETAILAPI.ORIGIN].detailNo,
          detailVersion: item[UPGRADE_DETAILAPI.ORIGIN].detailVersion,
        },
        upgradeDetail: item[UPGRADE_DETAILAPI.UPGRADE],
        detailDisplay: item[UPGRADE_DETAILAPI.DISPLAY],
      };

      // if (item.flag) {
      //   relationDetails.push({ detailNo: item.detailNo })
      // } else {
      //   relationDetails.push({ detailNo: item.detailNo, id: item.id })
      // }
    });
    query.relationDetails = relationDetails;
    return query;
  };

  const Submit = () => {
    const fn = ({ relationDetails }) => {
      form.validateFields().then(value => {
        let query = queryHandler(value);
        if (validate(query)) return;
        query = { ...query, relationDetails, groupDetails };
        // V13版本 当前发布时间不在宣传时间范围内，改成后端判断
        Modal.confirm({
          title: '确定发布吗？',
          onOk: async close => {
            close();
            const isNeedUpdate = await checkDetailUpdate();
            if (isNeedUpdate) {
              return;
            }

            // 校验是否已存在相同配置的基础包,若存在点击取消则不发布，若不存在or存在点击确定，直接继续发布
            if (query.type === 1) {
              const isPublic = await checkSameConfigPack(query);
              if (!isPublic) return;
            }

            setLoading(true);

            if (id && Type === 'edit') {
              const packNo = dataSourceForm.packNo;
              const params = {
                id,
                packNo,
                ...query,
              };
              post(allUrl.EquityPackageManage.updateEquityPack, { ...params }).then(res => {
                if (res.success) {
                  post(allUrl.EquityPackageManage.publishEquityPack, { id, packNo }).then(res => {
                    if (res.success) {
                      message.success('发布成功！');
                      // 保存成功跳转到列表
                      history.push('/RightCenter/PackManagement');
                    } else {
                      // message.error('发布失败！')
                    }
                    setLoading(false);
                  });
                } else {
                }
                setLoading(false);
              });
            } else {
              // 发布接口
              post(allUrl.EquityPackageManage.createAndPublish, { ...query }).then(res => {
                if (res.success) {
                  message.success('发布成功！');
                  // 保存成功跳转到列表
                  // sessionStorage.removeItem('PackManagementCode')
                  history.push('/RightCenter/PackManagement');
                } else {
                  // message.error('发布失败！')
                }
                setLoading(false);
              });
            }
          },
          onCancel: () => {
            setLoading(false);
            return false;
          },
        });
      });
    };
    // 基础权益包走check
    if (typeValue == 1) {
      check(fn);
    } else {
      let relationDetails = [];
      tableList.map((item, index) => {
        relationDetails[index] = {
          originDetail: {
            detailNo: item[UPGRADE_DETAILAPI.ORIGIN].detailNo,
            detailVersion: item[UPGRADE_DETAILAPI.ORIGIN].detailVersion,
            goodsCategory: item[UPGRADE_DETAILAPI.ORIGIN].goodsCategory,
          },
          upgradeDetail: null,
          detailDisplay: null,
        };
      });
      fn({ relationDetails });
    }
  };
  const disabledDate = current => {
    // Can not select days before today and today
    return current && current < moment().endOf('day').add(-1, 'days');
  };
  //  比较时间大小的方法
  function compareDate(date1, date2) {
    var date1 = new Date(date1);
    var date2 = new Date(date2);
    if (date1.getTime() - date2.getTime() < 0) {
      return '第二个时间大';
    } else {
      return '第一个时间大';
    }
  }

  //删除权益包
  const handleDelete = async () => {
    const { packNo } = dataSourceForm;
    const { success } = await get(allUrl.EquityPackageManage.ableToDelete, { packNo });
    if (success) {
      Modal.confirm({
        title: '确定删除该权益包吗？删除后不可恢复！',
        onOk: async () => {
          const { success } = await axiosDelete(`${allUrl.EquityPackageManage.delete}/${packNo}`, {}, { isForm: true });
          if (success) {
            message.success('删除成功');
            history.push('/RightCenter/PackManagement');
          }
        },
        onCancel: () => {
          return false;
        },
      });
    }
  };

  //V23版本，权益车型支持全选
  const onCheckAllChangeCarType = e => {
    const { checked } = e.target;
    const allCarType = carTypeData?.map(option => option.selectKey) || [];
    form.setFieldsValue({ carType: checked ? allCarType : [] });
    setIndeterminateCarType(false);
    setCheckAllCarType(checked);
    if (checked) {
      carConfigChange(allCarType);
    } else {
      resetCarCofig();
    }
  };

  //重置车型配置
  const resetCarCofig = () => {
    setCarConfigInfo([]);
    setAllSelectedConfigObj({});
    formRef.current.resetFields(['tmplCarConfig']);
    setCarTypeHasValue(false);
  };
  //V23版本，权益车型支持全选,treeSelect配置
  const tProps = {
    treeData: carTypeData,
    fieldNames: { label: 'selectValue', value: 'selectKey' },
    onChange: newValue => {
      if (newValue.length === 0) {
        setIndeterminateCarType(false);
        setCheckAllCarType(false);
        resetCarCofig();
      } else if (newValue.length === carTypeData.length) {
        setIndeterminateCarType(false);
        setCheckAllCarType(true);
        carConfigChange(newValue);
      } else {
        setIndeterminateCarType(true);
        setCheckAllCarType(false);
        carConfigChange(newValue);
      }

      //车型取消选择，车型配置删除相应车型的选择
      const c = { ...allSelectedConfigObj };
      const t = {};
      newValue.forEach(i => c[i] && (t[i] = c[i]));
      const t_new = Object.values(t).flat();
      form.setFieldsValue({ tmplCarConfig: t_new });
      setCarConfigInfo(t_new);
      setAllSelectedConfigObj(t);
    },
    dropdownRender: menu => (
      <>
        {form.getFieldValue('type') !== 1 && (
          <div style={{ marginLeft: 24, marginBottom: 8 }} className={formStyle.carTypeCustom}>
            <Checkbox
              indeterminate={indeterminateCarType}
              checked={checkAllCarType}
              onChange={onCheckAllChangeCarType}
              style={{ width: '100%' }}
            >
              全部车型
            </Checkbox>
          </div>
        )}
        {menu}
      </>
    ),
    treeCheckable: true,
    placeholder: '赛力斯SF(华为智选)',
    style: {
      width: '100%',
    },
  };

  useEffect(() => {
    let nowTime = new Date();
    let currentTime = new Date().getTime(); // 获取当前时间的时间戳
    console.log(
      compareDate('2020-06-10 10:10:12', '2020-05-10 12:10:12'),
      '当前时间' + nowTime,
      '当前时间戳' + currentTime
    );
  }, []);

  return (
    <Form
      {...layout}
      name="create"
      ref={formRef}
      form={form}
      onFinish={onFinish}
      className={formStyle['form-style-item']}
    >
      {/* <Form.Item name='packNo' label="权益包编码" rules={[]}>
        <Input disabled />
      </Form.Item> */}
      <Form.Item
        name="name"
        label="权益包名称"
        rules={[
          { required: true, message: '请填写标题，最多30字' },
          {
            max: 30,
            message: '请填写标题，最多30字',
          },
        ]}
      >
        <Input allowClear placeholder="请填写标题，最多30字" />
      </Form.Item>
      <Form.Item
        name="attribute"
        label="权益包属性"
        rules={[]}
        help={
          <div style={{ color: 'red', fontSize: '12px', marginBottom: '20px' }}>
            每次仅支持创建一条权益包属性，如需创建付费权益，请选择销售订单
          </div>
        }
      >
        <Radio.Group onChange={onChangeAttr} value={attrValue}>
          {/* <Radio value={1}>意向订单</Radio>
          <Radio value={2}>正式订单</Radio> */}
          {entryList3['equity_attribute'] && entryList3['equity_attribute'].length
            ? entryList3['equity_attribute'].map((item, index) => (
                <Radio key={index} value={Number(item.value)}>
                  {item.name}
                </Radio>
              ))
            : null}
        </Radio.Group>
      </Form.Item>
      {/* v16.0 */}
      <Form.Item name="type" label="权益包类型" rules={[]} initialValue={1}>
        <Radio.Group onChange={onChangeType}>
          {/* <Radio value={1}>基础权益包</Radio>
            <Radio value={2}>付费权益包</Radio> */}
          {entryList2['equity_pay_type'] && entryList2['equity_pay_type'].length
            ? entryList2['equity_pay_type'].map((item, index) => (
                <Radio key={index} value={Number(item.value)}>
                  {item.name}
                </Radio>
              ))
            : null}
        </Radio.Group>
      </Form.Item>
      <Form.Item name="carType" label="权益车型" rules={[{ required: true, message: '请选择权益车型!' }]}>
        {!!carTypeData?.length && <TreeSelect {...tProps} getPopupContainer={triggerNode => triggerNode.parentNode} />}
      </Form.Item>
      <Form.Item name="tmplCarConfig" label="权益车型配置" rules={[{ required: true, message: '请选择权益车型配置!' }]}>
        {/* 车型为空或者 carConfigInfo 没有选车型配置的时候显示 */}
        {(!carTypeHasValue || !carConfigInfo.length) && (
          <Input
            style={{
              color: '#bfbfbf',
            }}
            disabled={!carTypeHasValue}
            value={'请选择车型配置'}
            onClick={handleModal}
            readOnly
          />
        )}
        {/* carConfigInfo 有选择车型配置的时候显示 */}
        {!carConfigInfo.length || (
          <Checkbox.Group
            style={{
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              wordBreak: 'break-all',
              border: '1px solid #d5d5d5',
              borderRadius: '2px',
              height: '32px',
              paddingLeft: '8px',
              paddingRight: '8px',
            }}
            readOnly
            onClick={handleModal}
            value={carConfigInfo}
            className={styles['config-check-group']}
          >
            {carConfigData.map(i => (
              <>
                {i.configList.map(carOfYear => (
                  <>
                    {carOfYear.carOfYearConfig.map(configList => (
                      <>
                        {configList.configList.map(item => {
                          const POWER_MODEL_CODE =
                            i.carType +
                            '/' +
                            carOfYear.carOfYear +
                            '/' +
                            configList.powerModelCode +
                            '/' +
                            item.configKey;

                          const POWER_MODEL_NAME =
                            carOfYear.carOfYear + '/' + configList.powerModelName + '/' + item.configValue;
                          return (
                            carConfigInfo.includes(POWER_MODEL_CODE) && (
                              <>
                                <Checkbox value={POWER_MODEL_CODE} key={POWER_MODEL_CODE}>
                                  {POWER_MODEL_NAME}
                                </Checkbox>
                                <span className="config-dh">,</span>
                              </>
                            )
                          );
                        })}
                      </>
                    ))}
                  </>
                ))}
              </>
            ))}
          </Checkbox.Group>
        )}
        {/* 车型配置选择弹窗 */}
        {showCarConfig && (
          <CarConfigModal
            allSelectedConfigObj={allSelectedConfigObj}
            setAllSelectedConfigObj={setAllSelectedConfigObj}
            showCarConfig={showCarConfig}
            carConfigData={carConfigData}
            setShowCarConfig={setShowCarConfig}
            getModalData={getModalData}
          />
        )}
      </Form.Item>

      <Form.Item
        name="carMarketType"
        label="车辆市场类型"
        initialValue={1}
        rules={[]}
        help={
          <div style={{ color: 'red', fontSize: '12px', marginBottom: '20px', marginTop: '8px' }}>
            需要业务选择权益包可以支持的车辆市场类型，商品车：SF5、M5等
          </div>
        }
      >
        <Select placeholder="请选择车辆市场类型" allowClear getPopupContainer={triggerNode => triggerNode.parentNode}>
          {carMarketData && carMarketData.length
            ? carMarketData.map((item, index) => (
                <Option key={index} value={Number(item.code)}>
                  {item.name}
                </Option>
              ))
            : null}
        </Select>
      </Form.Item>

      <Form.Item name="packSalesType" label="车辆销售类型" rules={[]} initialValue={1}>
        <Radio.Group onChange={onChangeType2} value={typeValue2}>
          {/* <Radio value={1}>基础权益包</Radio>
          <Radio value={2}>付费权益包</Radio> */}
          {entryList4['pack_sales_type'] && entryList4['pack_sales_type'].length
            ? entryList4['pack_sales_type'].map((item, index) => (
                <Radio key={index} value={Number(item.value)}>
                  {item.name}
                </Radio>
              ))
            : null}
        </Radio.Group>
      </Form.Item>

      <Form.Item
        name="display"
        label="是否在APP上显示"
        rules={[]}
        initialValue={0}
        help={
          <div style={{ color: 'red', fontSize: '12px', marginBottom: '20px' }}>
            选择不在APP上展示，则该权益包信息及其所有权益明细信息均无法在APP端展示
          </div>
        }
      >
        <Radio.Group disabled={form.getFieldValue('type') == 4}>
          <Radio value={0}>在APP展示</Radio>
          <Radio value={1}>不在APP上展示</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item
        name="activityTime"
        label="宣传时间"
        rules={[{ required: true, message: '请选择宣传时间!' }]}
        help={
          <div style={{ color: 'red', fontSize: '12px', marginBottom: '20px', marginTop: '8px' }}>
            宣传时间只是对外展示的时间范围，尽量设置的时间相对长一些，宣传时间失效后，可能会影响权益包的绑定
          </div>
        }
      >
        {/* V13发布过的权益包，宣传开始时间不能改  isPublished  是否发布过（0-否，1-是） */}
        <RangePicker
          showTime
          disabledDate={disabledDate}
          disabled={dataSourceForm && [!!dataSourceForm.isPublished, false]}
          placeholder={['权益宣传的开始时间', '权益宣传的结束时间']}
          getPopupContainer={triggerNode => triggerNode.parentNode}
        />
      </Form.Item>

      {/* {typeValue === 2 || typeValue === 4 || typeValue === 5 ? ( */}
      {[2, 4, 5, 7].includes(typeValue) ? (
        <>
          <Form.Item name="equitySource" label="权益来源" rules={[]} initialValue={1}>
            <Radio.Group onChange={e => equitySourceChange(e.target.value)}>
              <Radio value={1}>直销</Radio>
              <Radio value={2}>三方采购</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="销售渠道">
            <Form.Item name="salesChannel" style={{ marginBottom: 0 }}>
              <Select
                placeholder="请选择销售渠道"
                style={{ display: 'inline-block', width: '500px' }}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {/* {
                    equitySourceDisplay == 1 && entryList2['equity_direct_sales'] && entryList2['equity_direct_sales'].length ?
                      entryList2['equity_direct_sales'].map((item, index) => <Option key={index} value={item.value}>{item.name}</Option>)
                      : null
                  }
                  {
                    equitySourceDisplay == 2 && entryList2['equity_trilateral_sales'] && entryList2['equity_trilateral_sales'].length ?
                      entryList2['equity_trilateral_sales'].map((item, index) => <Option key={index} value={item.value}>{item.name}</Option>)
                      : null
                  } */}
                {entryList2['equity_direct_sales'] && entryList2['equity_direct_sales'].length
                  ? entryList2['equity_direct_sales']
                      .filter(item => item.extendField1 == equitySourceDisplay)
                      .map((item, index) => (
                        <Option key={index} value={item.value}>
                          {item.name}
                        </Option>
                      ))
                  : null}
              </Select>
            </Form.Item>
            <span style={{ color: 'red', fontSize: '12px', marginTop: '8px', display: 'inline-block' }}>
              如有新增销售渠道，请联系管理员维护
            </span>
          </Form.Item>

          <Form.Item name="promotionName" label="活动名称">
            <Input allowClear placeholder="请输入活动名称" />
          </Form.Item>
          <Form.Item name="askingName" label="权益请示名称">
            <Input allowClear placeholder="请输入权益请示名称" />
          </Form.Item>
          <Form.Item name="askingNo" label="权益请示编码">
            <Input allowClear placeholder="请输入权益请示编码" maxLength={64} />
          </Form.Item>
          <Form.Item label="权益请示活动有效期" style={{ marginBottom: 0 }}>
            <Form.Item name="askingTime" style={{ display: 'inline-block', width: '500px' }}>
              <RangePicker
                showTime
                style={{ width: '400px' }}
                placeholder={['权益请示活动有效期开始时间', '权益请示活动有效期结束时间']}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
            </Form.Item>
          </Form.Item>
          <Form.Item name="askingCount" label="权益请示活动数量">
            <InputNumber allowClear placeholder="请输入权益请示活动数量" style={{ width: '400px' }} />
          </Form.Item>
          <Form.Item name="budgetSingle" label="单车预算">
            <InputNumber allowClear placeholder="请输入单车预算" style={{ width: '400px' }} />
          </Form.Item>
          <Form.Item name="budgetAmount" label="合计预算">
            <InputNumber allowClear placeholder="请输入合计预算" style={{ width: '400px' }} />
          </Form.Item>
          <Form.Item name="promotionInitiator" label="活动发起部门" rules={[]} initialValue={1}>
            <Radio.Group>
              <Radio value={1}>品牌策划部</Radio>
              <Radio value={2}>战区支持部</Radio>
              <Radio value={3}>用户服务部</Radio>
              <Radio value={4}>华为</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="salesSubject" label="销售结算主体" rules={[]} initialValue={1}>
            <Radio.Group>
              <Radio value={1}>品牌策划部</Radio>
              <Radio value={2}>战区支持部</Radio>
              <Radio value={3}>用户服务部</Radio>
              <Radio value={4}>华为</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item label="成本价格">
            <Form.Item name="costPrice" style={{ display: 'inline-block', marginBottom: '0' }}>
              <InputNumber allowClear placeholder="请输入数字" style={{ width: '400px' }} />
            </Form.Item>
            <span style={{ marginLeft: '10px' }}>元</span>
          </Form.Item>
          <Form.Item label="销售价格">
            <Form.Item name="price" style={{ display: 'inline-block', marginBottom: '0' }}>
              <InputNumber
                allowClear
                placeholder="请输入数字"
                style={{ width: '400px' }}
                onChange={e => {
                  priceHandler(e, 'price');
                }}
              />
            </Form.Item>
            <span style={{ marginLeft: '10px' }}>元</span>
          </Form.Item>
          <Form.Item label="采购价格">
            <Form.Item name="purchasePrice" style={{ display: 'inline-block', marginBottom: '0' }}>
              <InputNumber allowClear placeholder="请输入数字" style={{ width: '400px' }} />
            </Form.Item>
            <span style={{ marginLeft: '10px' }}>元</span>
          </Form.Item>
          <Form.Item label="销售服务费结算比例">
            <Form.Item name="percentage" style={{ display: 'inline-block', marginBottom: '0' }}>
              <InputNumber
                allowClear
                placeholder="请输入数字"
                style={{ width: '400px' }}
                onChange={e => {
                  priceHandler(e, 'percentage');
                }}
              />
            </Form.Item>
            <span style={{ marginLeft: '10px' }}>%</span>
          </Form.Item>
          <Form.Item label="销售收入">
            <Form.Item style={{ display: 'inline-block', marginBottom: '0' }}>
              <Input
                allowClear
                placeholder="填写服务费结算比例和销售价格后自动计算"
                style={{ width: '400px' }}
                disabled={true}
                value={salesIncome}
              />
            </Form.Item>
            <span style={{ color: 'red', fontSize: '12px', marginLeft: '10px', lineHeight: '32px' }}>
              填写服务费结算比例和销售价格后自动计算
            </span>
          </Form.Item>
          <Form.Item name="equitySalesSubject" label="权益包销售主体" rules={[]} initialValue={'0000090375'}>
            <Radio.Group>
              <Radio style={{ display: 'block' }} value={'0000090375'}>
                0000090375 重庆问界汽车销售有限公司（1900）
              </Radio>
              <Radio style={{ display: 'block' }} value={'0000009990'}>
                0000009990 重庆赛力斯新电动汽车销售有限公司（1020）
              </Radio>
              <Radio style={{ display: 'block' }} value={'0000093235'}>
                0000093235 重庆问界智选精品汽车备件有限公司（2300）
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="是否应用于活动" rules={[]} initialValue={1}>
            <Form.Item
              help={
                <div style={{ color: 'red', fontSize: '12px' }}>
                  需要选择该权益是否应用于活动，如果应用于活动，则会在固定活动时间开启，且不能与基础权益包中明细内容重复，否则按追加处理
                </div>
              }
              name="promotionExist"
              style={{ display: 'inline-block', marginBottom: '0' }}
            >
              <Radio.Group onChange={e => promotionExistChange(e.target.value)}>
                <Radio value={1}>该权益包应用于活动中</Radio>
                <Radio value={0}>该权益包不应用于活动中</Radio>
              </Radio.Group>
            </Form.Item>
          </Form.Item>
          {
            // 赠送权益包+付费权益包 且  是否应用于活动 选择“该权益包应用于活动中” 才展示活动时间、活动名额
            activityDisplay ? (
              <>
                <Form.Item label="活动名额" style={{ marginBottom: 0 }}>
                  <Form.Item
                    name="promotionQuotaFrame"
                    style={{ display: 'inline-block', width: '200px' }}
                    initialValue={0}
                  >
                    <Radio.Group onChange={e => promotionStatusChange(e.target.value)}>
                      <Radio value={0}>不限名额</Radio>
                      <Radio value={1}>具体名额</Radio>
                    </Radio.Group>
                  </Form.Item>
                  {promotionStatusDisplay == 1 && (
                    <>
                      <Form.Item name="promotionQuota" style={{ display: 'inline-block', marginLeft: 0 }}>
                        <InputNumber min={1} style={{ width: 190 }} placeholder={'请输入权益活动名额数量'} />
                      </Form.Item>
                    </>
                  )}
                </Form.Item>

                <Form.Item
                  help={
                    <div style={{ color: 'red', fontSize: '12px', marginTop: -18, marginBottom: 24 }}>
                      配置固定时间，则会在某活动固定时间绑定该权益包，比如：双十一当天、元旦当天、春节当天等等，以实际配置的具体日期为准
                    </div>
                  }
                  required
                  label="活动时间"
                  style={{ marginBottom: 0 }}
                >
                  <Form.Item
                    name="promotionTimeFrame"
                    style={{ display: 'inline-block', width: '200px' }}
                    initialValue={1}
                  >
                    <Radio.Group onChange={e => promotionTimeFrameChange(e.target.value)}>
                      <Radio value={0}>不限时间</Radio>
                      <Radio value={1}>固定时间</Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item
                    name="promotionTime"
                    style={{ display: 'inline-block', marginBottom: 0 }}
                    rules={[{ required: promotionTimeFrameDisplay === 1, message: '' }]}
                  >
                    <RangePicker
                      showTime
                      style={{ width: '380px' }}
                      placeholder={['权益活动开始时间', '权益活动结束时间']}
                      disabled={promotionTimeFrameDisplay !== 1}
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                    />
                  </Form.Item>
                </Form.Item>
              </>
            ) : null
          }
        </>
      ) : null}

      <Form.Item name="description" label="权益描述" rules={[]}>
        <TextArea rows={4} placeholder="如有需要备注或者解释说明的字段，请在此处填写" />
      </Form.Item>
      <Form.Item label="权益支持城市">
        <Form.Item
          name="allArea"
          style={{ display: 'inline-block', width: '300px', marginBottom: '10px' }}
          initialValue={0}
        >
          <Radio.Group onChange={e => allAreaChange(e.target.value)}>
            <Radio value={0}>全国</Radio>
            <Radio value={1}>其他</Radio>
          </Radio.Group>
        </Form.Item>
        {allAreaDisplay == 1 && (
          <>
            <div
              style={{
                width: '776px',
                height: '100px',
                background: '#FFFFFF',
                borderRadius: '2px',
                border: '1px solid rgba(0,0,0,0.15)',
              }}
            >
              <Select style={{ width: 120, marginLeft: '16px', marginTop: '16px' }} disabled={true}></Select>
            </div>
          </>
        )}
      </Form.Item>
      <Form.Item label="权益支持门店">
        <Form.Item
          name="allDealer"
          style={{ display: 'inline-block', width: '300px', marginBottom: '10px' }}
          initialValue={0}
        >
          <Radio.Group onChange={e => allDealerChange(e.target.value)}>
            <Radio value={0}>全部门店</Radio>
            <Radio value={1}>其他</Radio>
          </Radio.Group>
        </Form.Item>
        {allDealerDisplay == 1 && (
          <>
            <div
              style={{
                width: '776px',
                height: '100px',
                background: '#FFFFFF',
                borderRadius: '2px',
                border: '1px solid rgba(0,0,0,0.15)',
              }}
            >
              <Select style={{ width: 120, marginLeft: '16px', marginTop: '16px' }} disabled={true}></Select>
            </div>
          </>
        )}
      </Form.Item>
      {props.children}
      <div style={{ height: 20, backgroundColor: '#F0F2F5' }}></div>
      <div className="bottom-back" style={backWidth}>
        <Button
          style={{ marginRight: '8px' }}
          onClick={() => {
            history.goBack();
          }}
        >
          返回
        </Button>
        <Button style={{ marginRight: '8px' }} htmlType="submit">
          保存草稿
        </Button>
        {dataSourceForm ? (
          <Button
            type="danger"
            disabled={dataSourceForm.status !== 1}
            style={{ marginRight: '8px' }}
            onClick={handleDelete}
          >
            删除
          </Button>
        ) : null}
        <Button style={{ marginRight: '8px' }} type="primary" onClick={Submit}>
          发布
        </Button>
      </div>
    </Form>
  );
};
Component.displayName = 'DatabaseForm';
export default Component;
