{"swagger": "2.0", "info": {"description": "文件管理列表SwaggerAPI管理", "version": "1.0", "title": "文件管理列表API"}, "host": "localhost:12000", "basePath": "/", "tags": [{"name": "nacos-controller", "description": "Nacos Controller"}, {"name": "人员管理", "description": "User Controller"}, {"name": "岗位管理", "description": "Post Controller"}, {"name": "数据统计", "description": "Statistic Controller"}, {"name": "文件上传服务", "description": "Upload Controller"}, {"name": "文件操作控制器", "description": "File Operate Controller"}, {"name": "文件管理", "description": "File Item Controller"}, {"name": "目录管理", "description": "Directory Controller"}, {"name": "组织管理", "description": "Organization Controller"}], "paths": {"/nacos/deregister": {"get": {"tags": ["nacos-controller"], "summary": "deregisterInstance", "operationId": "deregisterInstanceUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/nacos/register": {"get": {"tags": ["nacos-controller"], "summary": "registerInstance", "operationId": "registerInstanceUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/directory/create": {"post": {"tags": ["目录管理"], "summary": "创建目录", "operationId": "createDirectoryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "directoryDTO", "description": "directoryDTO", "required": true, "schema": {"$ref": "#/definitions/DirectoryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«DirectoryDTO»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/directory/list": {"post": {"tags": ["目录管理"], "summary": "展开目录", "operationId": "listNodesUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "directoryQuery", "description": "directoryQuery", "required": true, "schema": {"$ref": "#/definitions/DirectoryQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«NodeListVO»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/directory/remove": {"post": {"tags": ["目录管理"], "summary": "删除目录", "operationId": "removeDirectoryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "removeNodeCommand", "description": "removeNodeCommand", "required": true, "schema": {"$ref": "#/definitions/RemoveNodeCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/directory/sort": {"post": {"tags": ["目录管理"], "summary": "移动排序", "operationId": "sortUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "movingDirectoryDTO", "description": "movingDirectoryDTO", "required": true, "schema": {"$ref": "#/definitions/MovingDirectoryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«DirectoryDTO»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/directory/update": {"post": {"tags": ["目录管理"], "summary": "修改目录名称", "operationId": "updateDirectoryNameUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "updateNodeCommand", "description": "updateNodeCommand", "required": true, "schema": {"$ref": "#/definitions/UpdateNodeCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/access/config": {"get": {"tags": ["文件管理"], "summary": "查看访问许可配置", "operationId": "queryAccessConfigUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FileItemAccessConfigVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}, "post": {"tags": ["文件管理"], "summary": "编辑访问许可配置", "operationId": "accessConfigUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "fileItemAccessConfigDTO", "description": "fileItemAccessConfigDTO", "required": true, "schema": {"$ref": "#/definitions/FileItemAccessConfigDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«long»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/cancel": {"post": {"tags": ["文件管理"], "summary": "文件操作：作废", "operationId": "cancelUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "updateFileItemStateCommand", "description": "updateFileItemStateCommand", "required": true, "schema": {"$ref": "#/definitions/UpdateFileItemStateCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/deliver": {"post": {"tags": ["文件管理"], "summary": "文件操作：下发", "operationId": "deliverUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "updateFileItemStateCommand", "description": "updateFileItemStateCommand", "required": true, "schema": {"$ref": "#/definitions/UpdateFileItemStateCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/detail": {"get": {"tags": ["文件管理"], "summary": "详情信息", "operationId": "detailFileItemUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FileItemDetailVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/editDetail": {"get": {"tags": ["文件管理"], "summary": "编辑信息", "operationId": "getFileItemEditDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FileItemEditVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/expire": {"post": {"tags": ["文件管理"], "summary": "设置过期时间", "operationId": "expireUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "updateExpireTimeCommand", "description": "updateExpireTimeCommand", "required": true, "schema": {"$ref": "#/definitions/UpdateExpireTimeCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/list": {"post": {"tags": ["文件管理"], "summary": "搜索查询", "operationId": "listFileItemsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "fileItemQueryDTO", "description": "fileItemQueryDTO", "required": true, "schema": {"$ref": "#/definitions/FileItemQueryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«FileItemListVO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/move": {"post": {"tags": ["文件管理"], "summary": "移动文件", "operationId": "moveDirUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "moveDIrCommand", "description": "moveDIrCommand", "required": true, "schema": {"$ref": "#/definitions/MoveDIrCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/notice": {"post": {"tags": ["文件管理"], "summary": "设置下发消息推送", "operationId": "setMsgSendUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "switchCommand", "description": "switchCommand", "required": true, "schema": {"$ref": "#/definitions/SwitchCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/online": {"get": {"tags": ["文件管理"], "summary": "在线文件搜索", "operationId": "searchOnlineFileItemsUsingGET", "produces": ["*/*"], "parameters": [{"name": "bizType", "in": "query", "description": "bizType", "required": false, "type": "integer", "format": "int32"}, {"name": "name", "in": "query", "description": "name", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«LinkFileVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/organization/list": {"post": {"tags": ["文件管理"], "summary": "指定的下发范围", "operationId": "listOrganizationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "fileOrganizationRelationQuery", "description": "fileOrganizationRelationQuery", "required": true, "schema": {"$ref": "#/definitions/FileOrganizationRelationQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«Organization»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/permission": {"post": {"tags": ["文件管理"], "summary": "设置文件下载权限", "operationId": "setPermissionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "updatePermissionCommand", "description": "updatePermissionCommand", "required": true, "schema": {"$ref": "#/definitions/UpdatePermissionCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/post/list": {"post": {"tags": ["文件管理"], "summary": "指定的岗位", "operationId": "listPostUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "filePostRelationQuery", "description": "filePostRelationQuery", "required": true, "schema": {"$ref": "#/definitions/FilePostRelationQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«Post»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/save": {"post": {"tags": ["文件管理"], "summary": "保存文件", "operationId": "saveFileItemUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "fileItemEditDTO", "description": "fileItemEditDTO", "required": true, "schema": {"$ref": "#/definitions/FileItemEditDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«long»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/seresUser/query": {"get": {"tags": ["文件管理"], "summary": "获取总部人员下发范围", "operationId": "querySeresUserUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FileSeresUserVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/seresUser/save": {"post": {"tags": ["文件管理"], "summary": "保存总部人员下发范围", "operationId": "saveSeresUserUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "fileSeresUserDTO", "description": "fileSeresUserDTO", "required": true, "schema": {"$ref": "#/definitions/FileSeresUserDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/status/update": {"post": {"tags": ["文件管理"], "summary": "变更文件状态（下发、作废、撤回）", "operationId": "updateFileStatusUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "updateFileItemStateCommand", "description": "updateFileItemStateCommand", "required": true, "schema": {"$ref": "#/definitions/UpdateFileItemStateCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/item/withdraw": {"post": {"tags": ["文件管理"], "summary": "文件操作：撤回", "operationId": "withdrawUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "updateFileItemStateCommand", "description": "updateFileItemStateCommand", "required": true, "schema": {"$ref": "#/definitions/UpdateFileItemStateCommand"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/multi/upload": {"post": {"tags": ["文件上传服务"], "summary": "附件文件批量上传", "operationId": "uploadFilesUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "files", "in": "formData", "description": "files", "required": true, "type": "array", "items": {"type": "file"}, "collectionFormat": "multi"}, {"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«OSSRecordVO»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/generateURL": {"get": {"tags": ["文件操作控制器"], "summary": "厂端预览的url", "operationId": "generateLinkUsingGET", "produces": ["*/*"], "parameters": [{"name": "objectName", "in": "query", "description": "objectName", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/innerOptDetailQuery": {"post": {"tags": ["文件操作控制器"], "summary": "用户未下载、未预览人数分页查询", "operationId": "innerOptDetailQueryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "detailPageQry", "description": "detailPageQry", "required": true, "schema": {"$ref": "#/definitions/SeresDetailPageQryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«SeresDetailPageRespDTO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/innerUserSummary": {"get": {"tags": ["文件操作控制器"], "summary": "内部用户数据汇总", "operationId": "innerUserSummaryUsingGET", "produces": ["*/*"], "parameters": [{"name": "fileId", "in": "query", "description": "fileId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FileSummaryDTO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/recognition": {"post": {"tags": ["文件操作控制器"], "summary": "文件识别", "operationId": "recognitionUsingPOST", "consumes": ["multipart/form-data"], "produces": ["*/*"], "parameters": [{"name": "file", "in": "formData", "description": "file", "required": true, "type": "file"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FileRecognitionDTO»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/seresSummaryExport": {"get": {"tags": ["文件操作控制器"], "summary": "汇总数据导出", "operationId": "seresSummaryExportUsingGET", "produces": ["*/*"], "parameters": [{"name": "fileId", "in": "query", "description": "fileId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/seresUserDetailExport": {"get": {"tags": ["文件操作控制器"], "summary": "内部用户汇总数据详情导出", "operationId": "seresUserDetailExportUsingGET", "produces": ["*/*"], "parameters": [{"name": "downloadType", "in": "query", "description": "downloadType", "required": true, "type": "integer", "format": "int32"}, {"name": "fileId", "in": "query", "description": "fileId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/storePreviewDownloadExport": {"post": {"tags": ["文件操作控制器"], "summary": "门店已预览、已下载导出接口", "operationId": "storePreviewDownloadExportUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "query", "description": "query", "required": true, "schema": {"$ref": "#/definitions/PreviewDownloadQryDTO"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/storePreviewDownloadQuery": {"post": {"tags": ["文件操作控制器"], "summary": "门店已预览、已下载查询接口", "operationId": "storePreviewDownLoadQueryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "query", "description": "query", "required": true, "schema": {"$ref": "#/definitions/PreviewDownloadQryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«PreviewDownloadDTO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/supplierPreviewDownloadExport": {"post": {"tags": ["文件操作控制器"], "summary": "供应商已预览、已下载导出接口", "operationId": "supplierPreviewDownloadExportUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "query", "description": "query", "required": true, "schema": {"$ref": "#/definitions/PreviewDownloadQryDTO"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/operate/supplierPreviewDownloadQuery": {"post": {"tags": ["文件操作控制器"], "summary": "供应商已预览、已下载查询接口", "operationId": "supplierPreviewDownLoadQueryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "query", "description": "query", "required": true, "schema": {"$ref": "#/definitions/PreviewDownloadQryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«PreviewDownloadDTO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/seres/delete": {"get": {"tags": ["组织管理"], "summary": "删除总部人员部门", "operationId": "deleteSeresOrganizationUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/seres/list": {"get": {"tags": ["组织管理"], "summary": "获取总部人员部门列表-全量", "operationId": "listSeresOrganizationUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«SeresOrganization»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/seres/page": {"post": {"tags": ["组织管理"], "summary": "获取总部人员部门列表-分页", "operationId": "listSeresOrganizationByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "pageQuery", "description": "pageQuery", "required": true, "schema": {"$ref": "#/definitions/PageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«SeresOrganization»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/seres/save": {"post": {"tags": ["组织管理"], "summary": "保存总部人员部门", "operationId": "saveSeresOrganizationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "seresOrganization", "description": "seresOrganization", "required": true, "schema": {"$ref": "#/definitions/SeresOrganization"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«SeresOrganization»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/store/list": {"get": {"tags": ["组织管理"], "summary": "门店树", "operationId": "listStoreOrganizationUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«StoreOrganizationVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/store/page": {"post": {"tags": ["组织管理"], "summary": "门店列表-分页", "operationId": "listStoreOrganizationByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "pageQuery", "in": "query", "description": "pageQuery", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«Organization»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/supplier/list": {"get": {"tags": ["组织管理"], "summary": "获取供应商列表-全量", "operationId": "listSupplierOrganizationUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«SupplierOrganization»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/organization/supplier/page": {"post": {"tags": ["组织管理"], "summary": "获取供应商列表-分页", "operationId": "listSupplierOrganizationByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "pageQuery", "description": "pageQuery", "required": true, "schema": {"$ref": "#/definitions/PageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«Organization»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/frequent/delete": {"get": {"tags": ["岗位管理"], "summary": "删除常用岗位", "operationId": "deleteFrequentPostUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/frequent/list": {"get": {"tags": ["岗位管理"], "summary": "常用岗位列表-全量", "operationId": "listFrequentPostsUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FrequentPostVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/frequent/page": {"post": {"tags": ["岗位管理"], "summary": "常用岗位列表-分页", "operationId": "listFrequentPostsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "pageQuery", "description": "pageQuery", "required": true, "schema": {"$ref": "#/definitions/PageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«FrequentPost»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/frequent/save": {"post": {"tags": ["岗位管理"], "summary": "保存常用岗位", "operationId": "saveFrequentPostUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "frequentPostDTO", "description": "frequentPostDTO", "required": true, "schema": {"$ref": "#/definitions/FrequentPostDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«long»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/seres/delete": {"get": {"tags": ["岗位管理"], "summary": "删除总部人员岗位", "operationId": "deleteSeresPostUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/seres/list": {"get": {"tags": ["岗位管理"], "summary": "总部人员岗位列表-全量", "operationId": "listSeresPostUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«SeresPost»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/seres/page": {"post": {"tags": ["岗位管理"], "summary": "总部人员岗位列表-分页", "operationId": "listSeresPostByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "pageQuery", "description": "pageQuery", "required": true, "schema": {"$ref": "#/definitions/PageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«SeresPost»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/seres/save": {"post": {"tags": ["岗位管理"], "summary": "保存总部人员岗位信息", "operationId": "saveSeresPostUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "SeresPost", "description": "SeresPost", "required": true, "schema": {"$ref": "#/definitions/SeresPost"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«SeresPost»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/store/list": {"get": {"tags": ["岗位管理"], "summary": "门店岗位列表-全量", "operationId": "listStorePostUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«StorePost»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/store/page": {"post": {"tags": ["岗位管理"], "summary": "门店岗位列表-分页", "operationId": "listStorePostByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "pageQuery", "description": "pageQuery", "required": true, "schema": {"$ref": "#/definitions/PageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«Post»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/supplier/list": {"get": {"tags": ["岗位管理"], "summary": "供应商岗位列表-全量", "operationId": "listSupplierPostUsingGET", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«SupplierPost»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/post/supplier/page": {"post": {"tags": ["岗位管理"], "summary": "供应商岗位列表-分页", "operationId": "listSupplierPostByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "pageQuery", "description": "pageQuery", "required": true, "schema": {"$ref": "#/definitions/PageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«Post»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/statistic/inComplete/list/export": {"post": {"tags": ["数据统计"], "summary": "未浏览、未下载人员明细-导出", "operationId": "exportInCompleteListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "storeDetail", "description": "storeDetail", "required": true, "schema": {"$ref": "#/definitions/StoreDetailPageQryDTO"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/statistic/store/inComplete/list": {"post": {"tags": ["数据统计"], "summary": "(门店)未浏览、未下载人员明细-查看", "operationId": "storeInCompleteListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "storeDetail", "description": "storeDetail", "required": true, "schema": {"$ref": "#/definitions/StoreDetailPageQryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«StoreDetailPageRespDTO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/statistic/summary": {"get": {"tags": ["数据统计"], "summary": "汇总数据-查看", "operationId": "statisticSummaryUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«FileSummaryDTO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/statistic/summary/detail": {"post": {"tags": ["数据统计"], "summary": "明细数据-查看", "operationId": "statisticDetailUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "queryDTO", "description": "queryDTO", "required": true, "schema": {"$ref": "#/definitions/SummaryQueryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«FileOptSummary»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/statistic/summary/detail/export": {"post": {"tags": ["数据统计"], "summary": "明细数据-导出", "operationId": "exportStatisticDetailUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "detailExport", "description": "detailExport", "required": true, "schema": {"$ref": "#/definitions/SummaryExportQueryDTO"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/statistic/summary/export": {"get": {"tags": ["数据统计"], "summary": "汇总数据-导出", "operationId": "exportStatisticUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/statistic/supplier/inComplete/list": {"post": {"tags": ["数据统计"], "summary": "(供应商)未浏览、未下载人员明细-查看", "operationId": "supplierInCompleteListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "supplierDetail", "description": "supplierDetail", "required": true, "schema": {"$ref": "#/definitions/SupplierDetailPageQryDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«SupplierDetailPageRespDTO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/upload": {"post": {"tags": ["文件上传服务"], "summary": "单个在线文件上传", "operationId": "uploadUsingPOST", "consumes": ["multipart/form-data"], "produces": ["*/*"], "parameters": [{"name": "file", "in": "formData", "description": "file", "required": true, "type": "file"}, {"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«OSSRecordVO»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/user/partner/page": {"post": {"tags": ["人员管理"], "summary": "合作员工信息列表-分页", "operationId": "listPartnerUserByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "userPageQuery", "description": "userPageQuery", "required": true, "schema": {"$ref": "#/definitions/UserPageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«SupplierUserVO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/user/seres/count/org": {"get": {"tags": ["人员管理"], "summary": "统计总部人员部门下的人数", "operationId": "countSeresOrganizationRelationUsingGET", "produces": ["*/*"], "parameters": [{"name": "orgId", "in": "query", "description": "orgId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«int»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/user/seres/count/post": {"get": {"tags": ["人员管理"], "summary": "统计总部人员岗位下的人数", "operationId": "countSeresPostRelationUsingGET", "produces": ["*/*"], "parameters": [{"name": "orgId", "in": "query", "description": "orgId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«int»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/user/seres/delete": {"post": {"tags": ["人员管理"], "summary": "删除总部人员信息", "operationId": "deleteSeresUserUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/user/seres/import": {"post": {"tags": ["人员管理"], "summary": "导入总部人员信息", "operationId": "importSeresUserUsingPOST", "consumes": ["multipart/form-data"], "produces": ["*/*"], "parameters": [{"name": "file", "in": "formData", "description": "file", "required": true, "type": "file"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/user/seres/page": {"post": {"tags": ["人员管理"], "summary": "总部人员信息列表-分页", "operationId": "listSeresUserByPageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "userPageQuery", "description": "userPageQuery", "required": true, "schema": {"$ref": "#/definitions/UserPageQuery"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData«PageRespDTO«SeresUserDTO»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/seres/file/user/seres/save": {"post": {"tags": ["人员管理"], "summary": "保存总部人员信息", "operationId": "saveSeresUserUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "seresUser", "description": "seresUser", "required": true, "schema": {"$ref": "#/definitions/SeresUserDTO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResponseData"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "definitions": {"Attachment": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "fileName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "linkFileId": {"type": "integer", "format": "int64"}, "ossObjectName": {"type": "string"}, "size": {"type": "integer", "format": "int64"}}, "title": "Attachment"}, "AttachmentVO": {"type": "object", "properties": {"fileName": {"type": "string"}, "linkFileId": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}, "title": "AttachmentVO"}, "DirectoryDTO": {"type": "object", "properties": {"bizType": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "parentDirId": {"type": "integer", "format": "int64"}, "parentDirPath": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}}, "title": "DirectoryDTO"}, "DirectoryQuery": {"type": "object", "properties": {"bizType": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}}, "title": "DirectoryQuery"}, "FileItemAccessConfigDTO": {"type": "object", "properties": {"deliverScope": {"type": "array", "items": {"$ref": "#/definitions/Organization"}}, "deliverType": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "postType": {"type": "integer", "format": "int32"}, "specificPost": {"type": "array", "items": {"$ref": "#/definitions/Post"}}, "specificUserList": {"type": "array", "items": {"type": "string"}}}, "title": "FileItemAccessConfigDTO"}, "FileItemAccessConfigVO": {"type": "object", "properties": {"deliverScope": {"type": "array", "items": {"$ref": "#/definitions/Organization"}}, "deliverType": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "postType": {"type": "integer", "format": "int32"}, "specificPost": {"type": "array", "items": {"$ref": "#/definitions/PostVO"}}, "specificUserList": {"type": "array", "items": {"$ref": "#/definitions/UserVO"}}}, "title": "FileItemAccessConfigVO"}, "FileItemDetailVO": {"type": "object", "properties": {"attachmentList": {"type": "array", "items": {"$ref": "#/definitions/AttachmentVO"}}, "bizType": {"type": "integer", "format": "int32"}, "deliverType": {"type": "integer", "format": "int32"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "ossObjectName": {"type": "string"}, "parentDirId": {"type": "integer", "format": "int64"}, "parentDirPath": {"type": "string"}, "permission": {"type": "integer", "format": "int32"}, "postType": {"type": "integer", "format": "int32"}, "sendBy": {"type": "string"}, "sendTime": {"type": "string", "format": "date-time"}, "seresUser": {"type": "string"}, "specificUserList": {"type": "array", "items": {"$ref": "#/definitions/UserVO"}}, "status": {"type": "integer", "format": "int32"}, "updateBy": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}}, "title": "FileItemDetailVO"}, "FileItemEditDTO": {"type": "object", "properties": {"attachmentList": {"type": "array", "items": {"$ref": "#/definitions/Attachment"}}, "bizType": {"type": "integer", "format": "int32"}, "deliverScope": {"type": "array", "items": {"$ref": "#/definitions/Organization"}}, "deliverType": {"type": "integer", "format": "int32"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "isSendMsg": {"type": "integer", "format": "int32"}, "linkFileIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string"}, "ossObjectName": {"type": "string"}, "parentDirId": {"type": "integer", "format": "int64"}, "parentDirPath": {"type": "string"}, "permission": {"type": "integer", "format": "int32"}, "postType": {"type": "integer", "format": "int32"}, "seresOrgIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "seresUserOneIdList": {"type": "array", "items": {"type": "string"}}, "size": {"type": "integer", "format": "int64"}, "specificPost": {"type": "array", "items": {"$ref": "#/definitions/Post"}}, "specificUserList": {"type": "array", "items": {"type": "string"}}}, "title": "FileItemEditDTO"}, "FileItemEditVO": {"type": "object", "properties": {"attachmentList": {"type": "array", "items": {"$ref": "#/definitions/Attachment"}}, "bizType": {"type": "integer", "format": "int32"}, "deliverScope": {"type": "array", "items": {"$ref": "#/definitions/Organization"}}, "deliverType": {"type": "integer", "format": "int32"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "isSendMsg": {"type": "integer", "format": "int32"}, "linkFileList": {"type": "array", "items": {"$ref": "#/definitions/LinkFileVO"}}, "name": {"type": "string"}, "ossObjectName": {"type": "string"}, "parentDirId": {"type": "integer", "format": "int64"}, "parentDirPath": {"type": "string"}, "permission": {"type": "integer", "format": "int32"}, "postType": {"type": "integer", "format": "int32"}, "seresOrgIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "seresUserList": {"type": "array", "items": {"$ref": "#/definitions/SeresUserVO"}}, "size": {"type": "integer", "format": "int64"}, "specificPost": {"type": "array", "items": {"$ref": "#/definitions/PostVO"}}, "specificUserList": {"type": "array", "items": {"$ref": "#/definitions/UserVO"}}}, "title": "FileItemEditVO"}, "FileItemListVO": {"type": "object", "properties": {"bizType": {"type": "integer", "format": "int32"}, "deliverType": {"type": "integer", "format": "int32"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "isSendMsg": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "organization": {"type": "string"}, "parentDirId": {"type": "integer", "format": "int64"}, "parentDirPath": {"type": "string"}, "permission": {"type": "integer", "format": "int32"}, "position": {"type": "string"}, "postType": {"type": "integer", "format": "int32"}, "sendBy": {"type": "string"}, "sendTime": {"type": "string", "format": "date-time"}, "seresUser": {"type": "string"}, "specificUser": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "updateBy": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}}, "title": "FileItemListVO"}, "FileItemQueryDTO": {"type": "object", "properties": {"bizType": {"type": "integer", "format": "int32"}, "dirId": {"type": "integer", "format": "int64"}, "endTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time"}, "statusList": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "title": "FileItemQueryDTO"}, "FileOptSummary": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "createUser": {"type": "string"}, "downloadAvgCount": {"type": "number"}, "downloadCount": {"type": "integer", "format": "int32"}, "downloadRate": {"type": "number"}, "fileId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "innerUserOpt": {"type": "integer", "format": "int32"}, "notDownloadCount": {"type": "integer", "format": "int32"}, "notPreviewCount": {"type": "integer", "format": "int32"}, "organizeCode": {"type": "string"}, "organizeName": {"type": "string"}, "previewAvgCount": {"type": "number"}, "previewAvgDuration": {"type": "number"}, "previewCount": {"type": "integer", "format": "int32"}, "previewRate": {"type": "number"}, "sendCount": {"type": "integer", "format": "int32"}, "sendType": {"type": "integer", "format": "int32", "minimum": -128, "maximum": 127}, "totalDownloadCount": {"type": "integer", "format": "int32"}, "totalPreviewCount": {"type": "integer", "format": "int32"}, "totalPreviewDuration": {"type": "number"}, "updateTime": {"type": "string", "format": "date-time"}, "updateUser": {"type": "string"}}, "title": "FileOptSummary"}, "FileOrganizationRelationQuery": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "title": "FileOrganizationRelationQuery"}, "FilePostRelationQuery": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "title": "FilePostRelationQuery"}, "FileRecognitionDTO": {"type": "object", "properties": {"bizCategory": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "fileName": {"type": "string"}, "fromOrg": {"type": "string"}, "fromUser": {"type": "string"}, "fromUserNo": {"type": "string"}, "sendTime": {"type": "integer", "format": "int64"}, "sendUser": {"type": "string"}}, "title": "FileRecognitionDTO"}, "FileSeresUserDTO": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "seresOrgIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "seresUserOneIdList": {"type": "array", "items": {"type": "string"}}}, "title": "FileSeresUserDTO"}, "FileSeresUserVO": {"type": "object", "properties": {"seresOrgIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "seresUserList": {"type": "array", "items": {"$ref": "#/definitions/SeresUserVO"}}}, "title": "FileSeresUserVO"}, "FileSummaryDTO": {"type": "object", "properties": {"downloadAvgCount": {"type": "number"}, "downloadCount": {"type": "integer", "format": "int32"}, "downloadRate": {"type": "number"}, "notDownloadCount": {"type": "integer", "format": "int32"}, "notPreviewCount": {"type": "integer", "format": "int32"}, "previewAvgCount": {"type": "number"}, "previewAvgDuration": {"type": "number"}, "previewCount": {"type": "integer", "format": "int32"}, "previewRate": {"type": "number"}, "sendPeopleCount": {"type": "integer", "format": "int32"}, "sendStoreCount": {"type": "integer", "format": "int32"}, "sendType": {"type": "integer", "format": "int32", "minimum": -128, "maximum": 127}, "totalDownloadCount": {"type": "integer", "format": "int32"}, "totalPreviewCount": {"type": "integer", "format": "int32"}, "totalPreviewDuration": {"type": "number"}}, "title": "FileSummaryDTO"}, "FrequentPost": {"type": "object", "properties": {"codeList": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "nameList": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}, "updateTime": {"type": "string", "format": "date-time"}}, "title": "FrequentPost"}, "FrequentPostDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/definitions/PostVO"}}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}}, "title": "FrequentPostDTO"}, "FrequentPostVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/definitions/PostVO"}}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}}, "title": "FrequentPostVO"}, "LinkFileVO": {"type": "object", "properties": {"bizType": {"type": "integer", "format": "int32"}, "createBy": {"type": "string"}, "fileName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}}, "title": "LinkFileVO"}, "MoveDIrCommand": {"type": "object", "properties": {"dirId": {"type": "integer", "format": "int64"}, "fileIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "MoveDIrCommand"}, "MovingDirectoryDTO": {"type": "object", "properties": {"sortedDirIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "MovingDirectoryDTO"}, "NodeListVO": {"type": "object", "properties": {"children": {"type": "array", "items": {"$ref": "#/definitions/DirectoryDTO"}}}, "title": "NodeListVO"}, "OSSRecordVO": {"type": "object", "properties": {"fileName": {"type": "string"}, "ossObjectName": {"type": "string"}}, "title": "OSSRecordVO"}, "Organization": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}, "title": "Organization"}, "PageQuery": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "PageRespDTO«FileItemListVO»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/FileItemListVO"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«FileItemListVO»"}, "PageRespDTO«FileOptSummary»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/FileOptSummary"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«FileOptSummary»"}, "PageRespDTO«FrequentPost»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/FrequentPost"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«FrequentPost»"}, "PageRespDTO«Organization»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/Organization"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«Organization»"}, "PageRespDTO«Post»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/Post"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«Post»"}, "PageRespDTO«PreviewDownloadDTO»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/PreviewDownloadDTO"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«PreviewDownloadDTO»"}, "PageRespDTO«SeresDetailPageRespDTO»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/SeresDetailPageRespDTO"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«SeresDetailPageRespDTO»"}, "PageRespDTO«SeresOrganization»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/SeresOrganization"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«SeresOrganization»"}, "PageRespDTO«SeresPost»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/SeresPost"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«SeresPost»"}, "PageRespDTO«SeresUserDTO»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/SeresUserDTO"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«SeresUserDTO»"}, "PageRespDTO«StoreDetailPageRespDTO»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/StoreDetailPageRespDTO"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«StoreDetailPageRespDTO»"}, "PageRespDTO«SupplierDetailPageRespDTO»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/SupplierDetailPageRespDTO"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«SupplierDetailPageRespDTO»"}, "PageRespDTO«SupplierUserVO»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/SupplierUserVO"}}, "pagination": {"$ref": "#/definitions/Pagination"}}, "title": "PageRespDTO«SupplierUserVO»"}, "Pagination": {"type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}, "title": "Pagination"}, "Post": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}, "title": "Post"}, "PostVO": {"type": "object", "properties": {"code": {"type": "string"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}}, "title": "PostVO"}, "PreviewDownloadDTO": {"type": "object", "properties": {"exportTime": {"type": "string", "format": "date-time"}, "fileId": {"type": "integer", "format": "int64"}, "fileName": {"type": "string"}, "operateNum": {"type": "integer", "format": "int32"}, "organizeCode": {"type": "string"}, "organizeName": {"type": "string"}, "previewDuration": {"type": "number"}, "sequenceNo": {"type": "integer", "format": "int32"}, "userName": {"type": "string"}, "userOneId": {"type": "string"}}, "title": "PreviewDownloadDTO"}, "PreviewDownloadQryDTO": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "organizeCode": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "queryType": {"type": "integer", "format": "int32"}}, "title": "PreviewDownloadQryDTO"}, "RemoveNodeCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "RemoveNodeCommand"}, "ResponseData": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"type": "object"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData"}, "ResponseData«DirectoryDTO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/DirectoryDTO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«DirectoryDTO»"}, "ResponseData«FileItemAccessConfigVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/FileItemAccessConfigVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«FileItemAccessConfigVO»"}, "ResponseData«FileItemDetailVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/FileItemDetailVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«FileItemDetailVO»"}, "ResponseData«FileItemEditVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/FileItemEditVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«FileItemEditVO»"}, "ResponseData«FileRecognitionDTO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/FileRecognitionDTO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«FileRecognitionDTO»"}, "ResponseData«FileSeresUserVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/FileSeresUserVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«FileSeresUserVO»"}, "ResponseData«FileSummaryDTO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/FileSummaryDTO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«FileSummaryDTO»"}, "ResponseData«FrequentPostVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/FrequentPostVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«FrequentPostVO»"}, "ResponseData«LinkFileVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/LinkFileVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«LinkFileVO»"}, "ResponseData«NodeListVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/NodeListVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«NodeListVO»"}, "ResponseData«OSSRecordVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/OSSRecordVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«OSSRecordVO»"}, "ResponseData«PageRespDTO«FileItemListVO»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«FileItemListVO»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«FileItemListVO»»"}, "ResponseData«PageRespDTO«FileOptSummary»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«FileOptSummary»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«FileOptSummary»»"}, "ResponseData«PageRespDTO«FrequentPost»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«FrequentPost»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«FrequentPost»»"}, "ResponseData«PageRespDTO«Organization»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«Organization»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«Organization»»"}, "ResponseData«PageRespDTO«Post»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«Post»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«Post»»"}, "ResponseData«PageRespDTO«PreviewDownloadDTO»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«PreviewDownloadDTO»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«PreviewDownloadDTO»»"}, "ResponseData«PageRespDTO«SeresDetailPageRespDTO»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«SeresDetailPageRespDTO»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«SeresDetailPageRespDTO»»"}, "ResponseData«PageRespDTO«SeresOrganization»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«SeresOrganization»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«SeresOrganization»»"}, "ResponseData«PageRespDTO«SeresPost»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«SeresPost»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«SeresPost»»"}, "ResponseData«PageRespDTO«SeresUserDTO»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«SeresUserDTO»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«SeresUserDTO»»"}, "ResponseData«PageRespDTO«StoreDetailPageRespDTO»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«StoreDetailPageRespDTO»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«StoreDetailPageRespDTO»»"}, "ResponseData«PageRespDTO«SupplierDetailPageRespDTO»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«SupplierDetailPageRespDTO»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«SupplierDetailPageRespDTO»»"}, "ResponseData«PageRespDTO«SupplierUserVO»»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/PageRespDTO«SupplierUserVO»"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«PageRespDTO«SupplierUserVO»»"}, "ResponseData«SeresOrganization»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/SeresOrganization"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«SeresOrganization»"}, "ResponseData«SeresPost»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/SeresPost"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«SeresPost»"}, "ResponseData«StoreOrganizationVO»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/StoreOrganizationVO"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«StoreOrganizationVO»"}, "ResponseData«StorePost»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/StorePost"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«StorePost»"}, "ResponseData«SupplierOrganization»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/SupplierOrganization"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«SupplierOrganization»"}, "ResponseData«SupplierPost»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"$ref": "#/definitions/SupplierPost"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«SupplierPost»"}, "ResponseData«int»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«int»"}, "ResponseData«long»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«long»"}, "ResponseData«object»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"type": "object"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«object»"}, "ResponseData«string»": {"type": "object", "properties": {"msg": {"type": "string"}, "msgCode": {"type": "string"}, "resp": {"type": "array", "items": {"type": "string"}}, "success": {"type": "boolean"}, "total": {"type": "integer", "format": "int32"}}, "title": "ResponseData«string»"}, "SeresDetailPageQryDTO": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "queryType": {"type": "integer", "format": "int32"}}, "title": "SeresDetailPageQryDTO"}, "SeresDetailPageRespDTO": {"type": "object", "properties": {"exportTime": {"type": "string", "format": "date-time"}, "fileName": {"type": "string"}, "sendTime": {"type": "string", "format": "date-time"}, "sequenceNo": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "userNo": {"type": "string"}}, "title": "SeresDetailPageRespDTO"}, "SeresOrganization": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "SeresOrganization"}, "SeresPost": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "SeresPost"}, "SeresUserDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "seresOrgId": {"type": "integer", "format": "int64"}, "seresOrgName": {"type": "string"}, "seresPostId": {"type": "integer", "format": "int64"}, "seresPostName": {"type": "string"}, "userName": {"type": "string"}, "userOneId": {"type": "string"}}, "title": "SeresUserDTO"}, "SeresUserVO": {"type": "object", "properties": {"userName": {"type": "string"}, "userOneId": {"type": "string"}}, "title": "SeresUserVO"}, "StoreDetailPageQryDTO": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "organizeCode": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "queryType": {"type": "integer", "format": "int32"}}, "title": "StoreDetailPageQryDTO"}, "StoreDetailPageRespDTO": {"type": "object", "properties": {"exportTime": {"type": "string", "format": "date-time"}, "fileId": {"type": "integer", "format": "int64"}, "fileName": {"type": "string"}, "positionCode": {"type": "string"}, "positionName": {"type": "string"}, "sendTime": {"type": "string", "format": "date-time"}, "serialNo": {"type": "integer", "format": "int32"}, "storeCode": {"type": "string"}, "storeName": {"type": "string"}, "userName": {"type": "string"}, "userNo": {"type": "string"}}, "title": "StoreDetailPageRespDTO"}, "StoreOrganizationVO": {"type": "object", "properties": {"area": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/definitions/StoreOrganizationVO"}}, "code": {"type": "string"}, "name": {"type": "string"}}, "title": "StoreOrganizationVO"}, "StorePost": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}, "title": "StorePost"}, "SummaryExportQueryDTO": {"type": "object", "properties": {"downloadAvgCount": {"type": "boolean"}, "downloadCount": {"type": "boolean"}, "downloadRate": {"type": "boolean"}, "fileId": {"type": "integer", "format": "int64"}, "notDownloadCount": {"type": "boolean"}, "notPreviewCount": {"type": "boolean"}, "organizeCodes": {"type": "array", "items": {"type": "string"}}, "previewAvgCount": {"type": "boolean"}, "previewAvgDuration": {"type": "boolean"}, "previewCount": {"type": "boolean"}, "previewRate": {"type": "boolean"}, "sendCount": {"type": "boolean"}, "totalDownloadCount": {"type": "boolean"}, "totalPreviewCount": {"type": "boolean"}, "totalPreviewDuration": {"type": "boolean"}}, "title": "SummaryExportQueryDTO"}, "SummaryQueryDTO": {"type": "object", "properties": {"downloadAvgCount": {"type": "boolean"}, "downloadCount": {"type": "boolean"}, "downloadRate": {"type": "boolean"}, "fileId": {"type": "integer", "format": "int64"}, "notDownloadCount": {"type": "boolean"}, "notPreviewCount": {"type": "boolean"}, "organizeName": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "previewAvgCount": {"type": "boolean"}, "previewAvgDuration": {"type": "boolean"}, "previewCount": {"type": "boolean"}, "previewRate": {"type": "boolean"}, "sendCount": {"type": "boolean"}, "totalDownloadCount": {"type": "boolean"}, "totalPreviewCount": {"type": "boolean"}, "totalPreviewDuration": {"type": "boolean"}}, "title": "SummaryQueryDTO"}, "SupplierDetailPageQryDTO": {"type": "object", "properties": {"fileId": {"type": "integer", "format": "int64"}, "organizeCode": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "queryType": {"type": "integer", "format": "int32"}}, "title": "SupplierDetailPageQryDTO"}, "SupplierDetailPageRespDTO": {"type": "object", "properties": {"exportTime": {"type": "string", "format": "date-time"}, "fileId": {"type": "integer", "format": "int64"}, "fileName": {"type": "string"}, "positionCode": {"type": "string"}, "positionName": {"type": "string"}, "sendTime": {"type": "string", "format": "date-time"}, "serialNo": {"type": "integer", "format": "int32"}, "supplierCode": {"type": "string"}, "supplierName": {"type": "string"}, "userName": {"type": "string"}, "userNo": {"type": "string"}}, "title": "SupplierDetailPageRespDTO"}, "SupplierOrganization": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}, "title": "SupplierOrganization"}, "SupplierPost": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}, "title": "SupplierPost"}, "SupplierUserVO": {"type": "object", "properties": {"organization": {"type": "string"}, "position": {"type": "string"}, "userName": {"type": "string"}, "userOneId": {"type": "string"}}, "title": "SupplierUserVO"}, "SwitchCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "result": {"type": "boolean"}}, "title": "SwitchCommand"}, "UpdateExpireTimeCommand": {"type": "object", "properties": {"expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}}, "title": "UpdateExpireTimeCommand"}, "UpdateFileItemStateCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int32"}}, "title": "UpdateFileItemStateCommand"}, "UpdateNodeCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "newName": {"type": "string"}, "oldName": {"type": "string"}}, "title": "UpdateNodeCommand"}, "UpdatePermissionCommand": {"type": "object", "properties": {"download": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}}, "title": "UpdatePermissionCommand"}, "UserPageQuery": {"type": "object", "properties": {"orgCodeList": {"type": "array", "items": {"type": "string"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "searchText": {"type": "string"}}, "title": "UserPageQuery"}, "UserVO": {"type": "object", "properties": {"position": {"type": "string"}, "userName": {"type": "string"}, "userOneId": {"type": "string"}}, "title": "UserVO"}}}