import { message } from 'antd';
import moment from 'moment';
/**
 * 根据文件类型获取文件扩展名
 * @param {String} fileType //文件类型
 * @returns String
 */
export const getFileExtension = fileType => {
  return {
    'application/msword': 'doc',
    'application/msword': 'docx',
    'application/octet-stream': 'dll',
    'application/pdf': 'pdf',
    'application/postscript': 'ai',
    'application/vnd.ms-excel': 'xlsx',
    'application/vnd.ms-powerpoint': 'ppt',
    'application/x-director': 'dir',
    'application/x-javascript': 'js',
    'application/x-shockwave-flash': 'swf',
    'application/xhtml+xml': 'xht',
    'application/zip': 'zip',
    'audio/midi': 'midi',
    'audio/mpeg': 'mp3',
    'audio/x-pn-realaudio': 'rm',
    'audio/x-pn-realaudio-plugin': 'rpm',
    'audio/x-wav': 'wav',
    'image/bmp': 'bmp',
    'image/gif': 'gif',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'text/css': 'css',
    'text/html': 'htm',
    'text/plain': 'txt',
    'text/xml': 'xml',
    'video/mpeg': 'mpg',
    'video/x-msvideo': 'avi',
    'video/x-sgi-movie': 'movie',
  }[fileType];
};

/**
 * 根据后端文件流下载文件
 * @param {Blob} data 已转为Blob格式的文件对象
 * @param {String} name 文件名称
 */
export const downFile = (data, name) => {
  const blob = new Blob([data]); //创建一个blob对象
  const a = document.createElement('a'); //创建一个标签
  a.href = URL.createObjectURL(blob); // response is a blob
  a.download = name; //文件名称
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  a.remove();
  setTimeout(() => {
    message.success(name + '下载成功！');
  }, 2000);
};
/**
 *
 * @param {Blob} blob 文件流
 * @param {String} fileName 文件名称
 * @param {Boolean} random 文件名是否带有随机数
 * @returns
 */
export const fileDown = (blob, fileName, random = true) => {
  try {
    if ((!blob) instanceof Blob) return message.error('文件流处理错误，请联系管理员！');
    if ((!blob) instanceof Blob && blob instanceof Object && !blob.success) return message.error(blob.msg);
    const extension = getFileExtension(blob.type);

    if (!extension) return message.error('文件格式获取错误，请联系管理员！');
    const timeStamp = moment().format('YYYYMMDDHHmmss');
    const name = `${fileName}${random ? '-' + timeStamp : null}.${extension}`;
    if (window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveBlob(blob, name);
    } else {
      const downloadElement = document.createElement('a');
      const href = window.URL.createObjectURL(blob); //创建下载的链接
      downloadElement.href = href;
      downloadElement.download = name; //下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); //点击下载
      document.body.removeChild(downloadElement); //下载完成移除元素
      window.URL.revokeObjectURL(href); //释放掉blob对象
    }
    setTimeout(() => {
      message.success(`文件下载成功！`);
    }, 500);
  } catch (error) {
    console.log(error);
  }
};

/**
 * 获取某节点的所有父节点数据
 * @param {Array} dataSource 数据源,带有children字段的数据源
 * @param {String} fieldName 节点字段值的键
 * @param {*} fieldValue 节点字段的值
 * @returns {Object}
 */
export const getAllParentData = (dataSource, fieldName, fieldValue) => {
  const targetData = {};
  const loops = (data = [], parent) => {
    return data.map(item => {
      const { children, [fieldName]: value } = item;
      const node = { value, parent, current: item };
      targetData[value] = node;
      node.children = loops(children, node);
      return item;
    });
  };
  const getNode = value => {
    let node = [];
    let currentNode = targetData[value];
    console.log(currentNode);
    if (currentNode) {
      node.push(currentNode.current);
      if (currentNode.parent) {
        node = [...getNode(currentNode.parent.value), ...node];
      }
    }
    return node;
  };
  loops(dataSource);
  console.log(dataSource);
  return getNode(fieldValue);
};

/**
 * 获取某节点的所有子级数据(包括本身)
 * @param {Array} dataSource 数据源,带有children字段的数据源
 * @param {String} fieldName 节点字段值的键
 * @param {*} fieldValue 节点字段的值
 * @returns {Object}
 */
export const getChildData = (dataSource, fieldName, fieldValue) => {
  let temp = {};
  const fn = (arr, field, value) => {
    arr.forEach(item => {
      if (item[field] === value) {
        temp = item;
        return;
      }
      if (item.children && item.children.length) {
        fn(item.children, field, value);
      }
    });
  };
  fn(dataSource, fieldName, fieldValue);
  return temp;
};

/**
 * 把一个数组按组进行分割
 * @param {Array} array 数据源
 * @param {Number} subGroupLength 每组的个数
 * @returns []
 */

export const arrayGroup = (array, subGroupLength) => {
  let index = 0;
  let newArray = [];
  while (index < array.length) {
    newArray.push(array.slice(index, (index += subGroupLength)));
  }
  return newArray;
};
/*
 * 判断数据类型
 * @param {*} data 数据源
 * @returns *
 */
export const getDataType = data => {
  return Object.prototype.toString.call(data).slice(8, -1);
};

/**
 * 判断数据是否为真假，并根据参数输出值
 * @param {*} str 数据源
 * @param {*} yes 为真是输出的值
 * @param {*} no 为假时输出的值
 * @param {*} empty 当数据源为Null和Undefined的输出值
 * @returns *
 */
export const JudgeExistence = (str, yes = '是', no = '否', empty = '') => {
  console.log(str);
  if (getDataType(str) === 'Null') return empty;
  if (getDataType(str) === 'Undefined') return empty;
  if (isNaN(str)) {
    return str ? yes : no;
  } else {
    str = Number(str);
    return str ? yes : no;
  }
};

/**
 * 数组对象去重
 * @param {Array} arr 将要去重的数据源
 * @param {String} field 需要去重的字段名称
 * @returns Array
 */
export const arrUnique = (arr, field) => {
  let map = new Map();
  for (let item of arr) {
    if (!map.has(item[field])) {
      map.set(item[field], item);
    }
  }
  return [...map.values()];
};

/**
 * 将按组分割后的数据组合
 * @param {Array} array 数据源
 * @returns Array
 */
export const composeArray = array => {
  let arr = [];
  array.forEach(item => {
    arr.push(...item);
  });
  return arr;
};

/**
 * 判断字符串长度（英文占1个字符，汉字占2个字符）
 * @param {String} str 数据源
 * @returns Number
 */
export const strlen = str => {
  var len = 0;
  for (var i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 127 || str.charCodeAt(i) == 94) {
      len += 2;
    } else {
      len++;
    }
  }
  return len;
};

/**
 *
 * @param {String} value 文本内容
 * @param {Object} name 字段名称
 * @param {Object} form 表单方法
 * @returns String
 */
export const trim = (value, name, form) => {
  value = value.replace(/(^\s*)|(\s*$)/g, '');
  if (!form) return value;
  form.setFieldsValue({ [name]: value });
};

/**
 * 获取两个时间相差的小时数
 * @param {String} startDate 开始时间
 * @param {String} endDate 结束时间
 * @returns number
 */
export const getInervalHour = (startDate, endDate) => {
  var ms = new Date(endDate).getTime() - new Date(startDate).getTime();
  if (ms < 0) return 0;
  return Math.ceil(ms / 1000 / 60 / 60);
};

/**
 * 判断数据不是null、undefined
 * @param {*} value
 * @returns Blon
 */
export const verifyTrueField = value => {
  if (getDataType(value) !== 'Null' && getDataType(value) !== 'Undefined') {
    return true;
  }
};
/**
 * 秒转化为-- 时分秒
 * @param {number} value
 * @returns
 */
export const formatSeconds = value => {
  var theTime = parseInt(value); // 秒
  var theTime1 = 0; // 分
  var theTime2 = 0; // 小时
  if (theTime > 60) {
    theTime1 = parseInt(theTime / 60);
    theTime = parseInt(theTime % 60);
    if (theTime1 > 60) {
      theTime2 = parseInt(theTime1 / 60);
      theTime1 = parseInt(theTime1 % 60);
    }
  }
  var result = '' + parseInt(theTime) + '秒';
  if (theTime1 > 0) {
    result = '' + parseInt(theTime1) + '分' + result;
  }
  if (theTime2 > 0) {
    result = '' + parseInt(theTime2) + '小时' + result;
  }
  return result;
};
/**
 * 在数组中查找指定的项 ，支持多维数组 ，数组对象嵌套
 *
 * @param needle 要查找的值
 * @param haystack 被查找的数组
 * @param property 当被查找项是对象时（ 数组对象嵌套 ）这个参数作为要查找的值的属性名称 ，如果是多维数组不会有任何影响
 * @param children 当被查找项是对象时（ 数组对象嵌套 ）这个参数作为子集合属性名称 ，如果是多维数组不会有任何影响
 * @return undefined | Object | *
 */
export const array_search = (
  needle,
  haystack,
  property = 'id',
  children = 'children',
  result = { search: undefined }
) => {
  result.search = haystack.find((value, index, arr) => {
    return property === '' ? value == needle : value[property] == needle;
  });

  let child;

  if (result.search === undefined) {
    haystack.forEach((value, index, arr) => {
      child = Array.isArray(value) ? value : value[children];
      if (child.length >= 0 && result.search === undefined) {
        array_search(needle, child, property, result);
      }
    });
  }

  return result.search;
};

export function getUrlParams(name, str) {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
  const r = str.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return null;
}

// 从url中查询到指定名称的参数值
export const getParam = function (name, defaultValue) {
  var query = window.location.search.substring(1);
  var vars = query.split('&');
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=');
    if (pair[0] == name) {
      return pair[1];
    }
    return defaultValue == undefined ? null : defaultValue;
  }
};

/**
 * 设置弹窗的宽度 = 右侧内容区域的宽度
 * @returns
 */
export const getWidth = () => {
  const winWidth = window.innerWidth;
  //获取左侧菜单宽度
  const siderWidth = document.getElementsByClassName('ant-layout-sider')[0].clientWidth;
  //右侧内容区域宽度，设为弹窗宽度
  return winWidth - siderWidth - 48;
};
/**
 *
 * @returns 设置弹窗水平对齐方式，跟右侧内容的最左侧边对齐
 */
export const offsetLeft = () => {
  const winWidth = window.innerWidth;
  //弹窗左侧距离浏览器左侧的宽度
  const gapWidth = (winWidth - getWidth()) / 2;
  //获取左侧菜单宽度
  const siderWidth = document.getElementsByClassName('ant-layout-sider')[0].clientWidth;
  //弹窗需左偏移的宽度
  return siderWidth + 24 - gapWidth;
};

/*
 * 删除table末页的最后1条数据，自动计算页数
 * @param {*} total 总条数
 * @param {*} pageNo 页码
 * @param {*} pageSize 每页显示条数
 * @param {*} delNum 删除的数量
 * @returns
 */
export const calcPageNo = (total, pageNo = 1, pageSize = 10, delNum = 1) => {
  const restNum = total - pageSize * (pageNo - 1);
  let pageNoDiff = Math.floor((delNum - restNum) / pageSize) + 1;
  pageNoDiff < 0 && (pageNoDiff = 0);
  pageNo = pageNo - pageNoDiff;
  pageNo < 1 && (pageNo = 1);
  return pageNo;
};

/**
 * 根据字典和id返回状态名称
 * @param {Array} dict
 * @param {string} id
 * @returns
 */
export const renderStatusName = (dict, id) => {
  let str = '';
  dict.map(item => {
    if (item.value == id) {
      str = item.name;
    }
  });
  return str;
};
