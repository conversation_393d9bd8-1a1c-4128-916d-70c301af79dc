import React, { memo, useState } from 'react';
import { Table, Tag, Button, Divider } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import _ from 'lodash';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
import LadderPartDetail from '@/pages/RightCenter/DetailManagement/maintenanceLadder/LadderPartDetail';
import { get } from '@/utils/request';
import allUrl from '@/utils/url';
import { utilsDict } from '@/utils/utilsDict';
import { extendFlag } from '../publicMethods';
import { strategyConfig, outsideStrategyConfig } from '@/pages/RightCenter/DetailManagement/formConfig';
import PublicTable from './PublicTable';
import styles from '../antd.module.less';

const DetailPubliTable = props => {
  const { className, dataSource, loading, rowKey, pagination, rowSelection, children, strategy, equityType } = props;
  const [detail, setDetail] = useState({});
  const [ladderPartDetailVisible, setLadderPartDetailVisible] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]); // table需要展开的行
  const [originDetail, setOriginDetail] = useState([]);
  // const formConfig = strategy === 'out' ? outsideStrategyConfig : strategyConfig
  let _strategyConfig = _.cloneDeep(strategyConfig);
  //对外的需要需求阶梯保养maintenanceLadderDetailItem内的字段，带outside开头
  _strategyConfig.maintenanceLadderFrame.c.maintenanceLadderDetailItem =
    outsideStrategyConfig.maintenanceLadderFrame.c.maintenanceLadderDetailItem;
  const formConfig = strategy === 'out' ? _strategyConfig : strategyConfig;

  //阶梯保养
  const handleLadderVisible = () => {
    setLadderPartDetailVisible(!ladderPartDetailVisible);
  };

  const getLadderData = record => {
    get(allUrl.EquityPackageManage.getDetailLadderInfo, {
      detailNo: record.detailNo,
      inside: strategy === 'out' ? 2 : 1, //对内对外标识  对内为1 对外为2
    })
      .then(res => {
        const { success, resp } = res;
        if (success) {
          if (!!resp.length) {
            // const maintenanceLadderLimitFrame = strategy === 'out' ? 'outsideMaintenanceLadderLimitFrame' : 'maintenanceLadderLimitFrame'
            // const maintenanceLadderDetail = strategy === 'out' ? 'outsideMaintenanceLadderDetail' : 'maintenanceLadderDetail'
            const maintenanceLadderDetail = 'maintenanceLadderDetail';
            // setDetail({ ...record, [maintenanceLadderLimitFrame]: record.maintenanceLadderLimitFrame, [maintenanceLadderDetail]: resp })
            setDetail({ ...record, [maintenanceLadderDetail]: resp });
          }
        }
      })
      .finally(() => {
        handleLadderVisible();
      });
  };

  // table默认展开（expandedRowKeys设置之后就无法收起），需要配合此函数进行展开和收起
  const onExpand = (expanded, record) => {
    if (expanded) {
      setExpandedRowKeys([record.detailNo]);
      const { originDetailNo } = record;
      if (originDetailNo) {
        setOriginDetail([]);
        get(allUrl.DetailedManageInterests.getDetailInfo, { detailNo: originDetailNo }).then(res => {
          if (res.success) {
            let Dt = res.resp;
            Dt[0].displayFrame = record.displayFrame;
            Dt[0].isDisplay = record.displayFrame == 1 ? 1 : 2;
            setOriginDetail(Dt);
          }
        });
      }
    } else {
      setExpandedRowKeys([]);
      setOriginDetail([]);
    }
  };

  //table展开收起
  const expandableProps = {
    showExpandColumn: false,
    expandedRowRender: record => (
      <PublicTable
        className={styles['blue-Table-nest']}
        dataSource={originDetail}
        pagination={false}
        insideTable={true}
      />
    ),
    rowExpandable: record => record.businessScene === 2,
    expandedRowKeys: expandedRowKeys, //展开的行
    onExpand,
  };

  //table-columns
  const insideUpgrade = (text, record) => {
    return (
      <div>
        <div className={`${styles['custom-mark']} ${styles['bg1']}`}>
          <div className={styles['custom-mark-content']}>
            <span>已升级</span>
          </div>
        </div>
        <span>{text}</span>
      </div>
    );
  };

  const insideExtend = (text, record) => {
    const children = () => {
      return (
        <>
          {expandedRowKeys.includes(record.detailNo) ? (
            <Button
              type="link"
              size="small"
              className={`${styles['more-info-btn']}`}
              onClick={() => onExpand(false, record)}
            >
              <UpOutlined />
              前权益
            </Button>
          ) : (
            <Button
              type="link"
              size="small"
              className={`${styles['more-info-btn']}`}
              onClick={() => onExpand(true, record)}
            >
              <DownOutlined />
              前权益
            </Button>
          )}
        </>
      );
    };
    return extendFlag({ text, children });
  };

  const outsideUpgrade = record => {
    return (
      <div style={{ position: 'relative' }}>
        <div className={`${styles['custom-mark']} ${styles['bg1']}`} style={{ top: -25, left: -25 }}>
          <div className={styles['custom-mark-content']}>
            <span>已升级</span>
          </div>
        </div>
        <div style={{ marginTop: 15 }}>{record.originDetailNo}</div>
        <div style={{ marginLeft: -25, marginRight: -17, marginTop: 20 }}>
          <Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} />
        </div>
        <div>{record.detailNo}</div>
      </div>
    );
  };

  const outsideExtend = record => {
    return (
      <div style={{ position: 'relative' }}>
        <div className={`${styles['custom-mark']} ${styles['bg2']}`} style={{ top: -25, left: -25 }}>
          <div className={styles['custom-mark-content']}>
            <span>延保</span>
          </div>
        </div>
        <div style={{ marginTop: 15 }}>{record.originDetailNo}</div>
        <div style={{ marginLeft: -25, marginRight: -17, marginTop: 20 }}>
          <Divider style={{ marginTop: 0, backgroundColor: '#f0f0f0' }} />
        </div>
        <div>{record.detailNo}</div>
      </div>
    );
  };
  return (
    <>
      <Table
        className={className}
        dataSource={dataSource}
        loading={loading}
        rowKey={rowKey}
        size="small"
        scroll={{ x: 3140 }}
        bordered
        pagination={pagination}
        expandable={expandableProps}
        rowSelection={rowSelection}
        rowClassName={(record, index) => {
          // if (strategy === 'out') return ''
          if (record.businessScene === 3) {
            if ((record.displayFrame === 1 && strategy === 'out') || strategy === 'in')
              return `${styles['table-row-blue']}`;
          }
          return `${styles['table-row-height72']}`;
        }}
      >
        {/* <Table.Column title='权益明细编码' key='detailNo' dataIndex='detailNo' width={200} fixed='left' render={text => text || '-'} /> */}
        <Table.Column
          title="权益明细编码"
          key="detailNo"
          dataIndex="detailNo"
          width={200}
          fixed="left"
          className={styles['table-pad-left']}
          render={(text, record) => {
            if (record.displayFrame === 2) {
              if (strategy === 'out') {
                if (record.businessScene === 2) {
                  return outsideExtend(record);
                }
                if (record.businessScene === 3) {
                  return outsideUpgrade(record);
                }
                // return (
                //     <div className={styles['merge-column']}>
                //         <p >{record.originDetailNo}</p>
                //         <br />
                //         <p >{record.detailNo}</p>
                //     </div>
                // )
              }
            }
            // 对内升级
            if (record.businessScene === 3) {
              return insideUpgrade(text);
            }
            // 对内延保
            if (record.businessScene === 2) {
              return insideExtend(text, record);
            }
            // 其他
            return (
              <div>
                <span>{text}</span>
              </div>
            );
          }}
        />
        <Table.Column
          title="权益明细分类"
          key="sortCodeName"
          dataIndex="sortCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细名称"
          key="name"
          dataIndex="name"
          render={text => text || '-'}
          width={200}
          fixed="left"
        />
        <Table.Column
          title="权益业务场景"
          key="businessSceneName"
          dataIndex="businessSceneName"
          render={text => text || '-'}
          width={120}
        />
        <Table.Column
          title="权益类别"
          key="typeCodeName"
          dataIndex="typeCodeName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="车辆属性"
          key="carAttrName"
          dataIndex="carAttrName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益归属"
          key="belongName"
          dataIndex="belongName"
          width={120}
          render={text => text || '-'}
        />
        {strategy === 'in' ? (
          <Table.Column
            title="延保激活时间"
            key="extendWarrantyActiveTimeName"
            dataIndex="extendWarrantyActiveTimeName"
            width={120}
            render={text => text || '-'}
          />
        ) : null}
        <Table.Column
          title="权益限制年限"
          key="limitYear"
          dataIndex="limitYear"
          width={120}
          className={styles['table-pad-left']}
          render={(text, record) => {
            let span = <span>-</span>;
            if (record.limitTimeFrame == -1) {
              span = <span>永久有效</span>;
            } else if (record.limitTimeFrame == 1) {
              span = (
                <span>{`${Number(record.limitYear)}年${Number(record.limitMonth)}月${Number(record.limitDay)}日`}</span>
              );
            }
            if (record.businessScene === 2) {
              return extendFlag({ text: span });
            } else {
              return span;
            }
            // return record.limitYear && record.yearUnit ? record.limitYear + getYearUnitName(record.yearUnit) :'不限年限'
          }}
        />
        <Table.Column
          title="权益生效时间"
          key="relativeTime"
          dataIndex="relativeTime"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            let tmpl = '';
            if (record.relativeTimeName) {
              tmpl = record.relativeTimeName;
            } else if (record.fixedBeginTime && record.fixedEndTime) {
              tmpl =
                moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') +
                ' ~ ' +
                moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss');
            } else {
              tmpl = '-';
            }
            if (record.businessScene === 2) {
              return extendFlag({ text: tmpl });
            } else {
              return tmpl;
            }
            // return record.relativeTimeName ?
            //     record.relativeTimeName
            //     : <>
            //         {
            //             record.fixedBeginTime && record.fixedEndTime ? moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') + ' ~ ' + moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')
            //                 : '-'
            //         }
            //     </>
          }}
        />
        <Table.Column
          title="权益限制里程(基准)"
          key="limitLegend"
          dataIndex="limitLegend"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            const limitLegendFrameMap = {
              0: () => '-', //无此策略
              1: () => (
                <span>
                  {record.limitLegend ?? '-'}
                  <Tag className={[styles['custom-ant-tag-green'], styles['ml5']]}>行驶</Tag>
                </span>
              ), //行驶里程
              2: () => (
                <span>
                  {record.extenderMileage ?? '-'}
                  <Tag className={[styles['custom-ant-tag-yellow'], styles['ml5']]}>增程器</Tag>
                </span>
              ), //增程器里程
            };

            let tmpl = '';
            if (record.limitLegendFrame === null) {
              tmpl = record.limitLegend || record.extenderMileage;
            } else {
              tmpl = limitLegendFrameMap[record.limitLegendFrame]();
            }

            if (record.businessScene === 2) {
              return extendFlag({ text: tmpl });
            } else {
              return tmpl;
            }
            // if (record.limitLegendFrame === null) {
            //     return record.limitLegend || record.extenderMileage
            // } else {
            //     return limitLegendFrameMap[record.limitLegendFrame]()
            // }
          }}
        />
        <Table.Column
          title="权益生效里程"
          key="effectMileageFrame"
          dataIndex="effectMileageFrame"
          width={180}
          className={styles['table-pad-left']}
          render={(text, record) => {
            const effectMileageFrameMap = {
              //无此策略
              0: '-',
              //固定里程
              1: record.fixedBeginMileage ?? '-',
              //相对里程
              2: record.relativeBeginMileageStrategyName,
            };
            if (record.businessScene === 2) {
              return extendFlag({ text: effectMileageFrameMap[record.effectMileageFrame] });
            } else {
              return effectMileageFrameMap[record.effectMileageFrame];
            }
          }}
        />
        <Table.Column
          title="享有权益频次"
          key="frequency"
          dataIndex="frequency"
          width={120}
          render={text => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限次数' : text + '次';
            } else {
              a = '-';
            }
            return a;
          }}
        />
        <Table.Column
          title="车辆权益变更属性"
          key="identityName"
          dataIndex="identityName"
          width={160}
          render={text => text || '-'}
        />
        <Table.Column
          title="享有车主身份"
          key="carIdentityName"
          dataIndex="carIdentityName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="商品分类"
          key="goodsCategoryName"
          dataIndex="goodsCategoryName"
          width={120}
          render={text => text || '-'}
        />
        {
          // 精品权益专属字段
          equityType === 6 && (
            <Table.Column
              title="权益所属"
              key="goodsTypeName"
              dataIndex="goodsTypeName"
              width={120}
              render={text => text || '-'}
            />
          )
        }
        <Table.Column
          title="全保养阶梯策略"
          key="maintenanceLadderDesc"
          dataIndex="maintenanceLadderDesc"
          width={120}
          render={(text, record) => {
            if (text === '-') return '-';
            // return <>{text}<Button type='link' size='small' onClick={() => getLadderData(record)}>详情</Button></>
            return <>{text}</>;
          }}
        />
        <Table.Column
          title="补漆面部位"
          key="paintDesc"
          dataIndex="paintDesc"
          width={200}
          ellipsis
          render={text => (text ? <PublicTooltip title={text}>{text}</PublicTooltip> : '-')}
        />
        <Table.Column
          title="车联网流量"
          key="traffic"
          dataIndex="traffic"
          width={120}
          render={(text, record) => {
            let a = '';
            if (text) {
              a = text < 0 ? '不限流量' : text + record.trafficUnit;
            } else {
              a = '-';
            }
            return a;

            // return record.traffic  && record.trafficUnit ? record.traffic + ' ' + record.trafficUnit : '不限流量'
          }}
        />
        <Table.Column
          title="权益抵扣金额"
          key="deduction"
          dataIndex="deduction"
          width={200}
          render={(text, record) => {
            return record.payAmount && record.deduction ? (
              <span>
                {record.payAmount} 元 ~ {record.deduction} 元
              </span>
            ) : (
              '-'
            );
          }}
        />
        {/* <Table.Column title='权益支持区域' key='provinceName' dataIndex='provinceName' width={200} ellipsis render={(text, record) => {
                        let str = ''
                        if (record.areas && record.areas.length) {
                            let obj = record.areas[0]
                            str = `${obj.provinceName || ''}  ${obj?.cityName || ''} ${obj?.areaName || ''}`
                        } else {
                            str = '-'
                        }
                        return <div>
                            {
                                record.allArea == '0' ? '全部区域' : <PublicTooltip placement="topLeft" title={str}>{str}</PublicTooltip>
                            }
                        </div>
                    }} />
                    <Table.Column title='权益支持门店' key='dealersArr' dataIndex='dealersArr' width={200} ellipsis={true} render={(text, record) => {
                        let temp = []
                        if (record.dealers && record.dealers.length) {
                            record.dealers.forEach(item => {
                                temp.push(item.dealerCodeName)
                            })
                        } else {
                            temp = ['-']
                        }
                        return <div>
                            {
                                record.allDealer == '0' ? '全部门店' : <PublicTooltip placement="topLeft" title={temp.join(',')}>{temp.join(',')}</PublicTooltip>
                            }
                        </div>
                    }} /> */}
        <Table.Column
          title="权益履约方"
          key="performingDesc"
          dataIndex="performingDesc"
          width={200}
          ellipsis
          render={text => (text ? <PublicTooltip title={text}>{text}</PublicTooltip> : '-')}
        />
        <Table.Column
          title="履约结算价格"
          key="settlementPriceDesc"
          dataIndex="settlementPriceDesc"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="延保承保方"
          key="extendWarrantyAssurerName"
          dataIndex="extendWarrantyAssurerName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="业务编码"
          key="businessCodeDesc"
          dataIndex="businessCodeDesc"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="是否支持退款"
          key="refundName"
          dataIndex="refundName"
          width={120}
          render={text => text || '-'}
        />
        <Table.Column
          title="是否三方履约"
          key="performanceName"
          dataIndex="performanceName"
          width={120}
          render={text => text || '-'}
        />
        {strategy === 'in' ? (
          <Table.Column
            title="能否重复购买"
            key="isRepeatablePurchaseName"
            dataIndex="isRepeatablePurchaseName"
            width={120}
            render={text => text || '-'}
          />
        ) : null}
        <Table.Column
          title="权益声明"
          key="statement"
          dataIndex="statement"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益备注"
          key="remark"
          dataIndex="remark"
          width={300}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益明细版本编码"
          key="detailVersion"
          dataIndex="detailVersion"
          width={200}
          ellipsis
          render={text => text || '-'}
        />
        <Table.Column
          title="客户端展示形式"
          key="displayFrame"
          dataIndex="displayFrame"
          width={120}
          ellipsis
          render={(text, record) => {
            const obj = {
              1: '并列',
              2: '聚合',
              3: '组合',
            };
            return text ? obj[text] : '-';
          }}
        />
        <Table.Column
          title="是否在客户端展示"
          key="isDisplay"
          dataIndex="isDisplay"
          width={150}
          ellipsis
          render={(text, record) => {
            const obj = {
              1: '在客户端展示',
              2: '不在客户端展示',
            };
            return record.isDisplay ? obj[record.isDisplay] : '-';
          }}
        />
      </Table>
      {/* 阶梯保养详情 */}
      {ladderPartDetailVisible && (
        <LadderPartDetail
          handleLadderVisible={handleLadderVisible}
          ladderPartDetailVisible={ladderPartDetailVisible}
          data={{
            ...detail,
            rTimeName: detail.relativeTimeName,
            rBeginMileagegName: detail[formConfig['effectMileageFrame'].c.relativeBeginMileageStrategyName.value],
          }}
          strategyConfig={formConfig}
        />
      )}
    </>
  );
};
export default memo(DetailPubliTable);
