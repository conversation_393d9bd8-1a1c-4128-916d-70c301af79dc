import { postSeresFileUserSeresPage } from '@/apis/file-manage';
import { useRequest } from '@/hooks/request';
import { Button, Input, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

interface HeadQuarterPersonSelectorProps {
  value?: FileManage.SeresUserDTO[]; // 父组件传递的已选人员
  onChange?: (value: FileManage.SeresUserDTO[]) => void; // 选择发生变化时回调
  rowKey?: string; // 唯一键属性名称, 默认为'id'
}

const HeadQuarterPersonSelector: React.FC<HeadQuarterPersonSelectorProps> = ({ value, onChange, rowKey = 'id' }) => {
  const [selectedRow, setSelectedRow] = useState<FileManage.SeresUserDTO[]>([]); // 已选人员
  const [searchValue, setSearchValue] = useState(''); // 输入框内容
  const [query, setQuery] = useState(''); // 当前查询条件

  useEffect(() => {
    if (value) {
      setSelectedRow(value); // 更新已选人员
    }
  }, [value]);

  // 请求数据
  const { data: [{ list: data = [] } = { list: [] }] = [], loading } = useRequest(
    () => {
      if (query) return postSeresFileUserSeresPage({ pageNum: 1, pageSize: 999, searchText: query });
      return Promise.resolve([]);
    },
    { deps: [query] }
  );

  // 表格列配置
  const columns: ColumnsType<FileManage.SeresUserDTO> = [
    { title: '姓名', dataIndex: 'userName', key: 'userName' },
    { title: '工号', dataIndex: 'userOneId', key: 'userOneId' },
    { title: '总部部门', dataIndex: 'seresOrgName', key: 'seresOrgName' },
    { title: '总部岗位', dataIndex: 'seresPostName', key: 'seresPostName' },
  ];

  // 表格行选择配置
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: FileManage.SeresUserDTO[]) => {
      setSelectedRow(prevSelectedFiles => {
        const selectedRowKeysSet = new Set(selectedRowKeys);

        // 未选中项
        const unSelectedRow = data.filter(row => !selectedRowKeysSet.has(row[rowKey] as React.Key));
        // 保留当前选中项，并移除未选中项
        const filteredSelectedRow = prevSelectedFiles.filter(
          row => !unSelectedRow.some(unSelectedFile => unSelectedFile[rowKey] === row[rowKey])
        );

        // 添加新选中的项
        const newSelectedRow = selectedRows.filter(
          row => !prevSelectedFiles.some(rowItem => row[rowKey] === rowItem[rowKey])
        );
        if (onChange) {
          onChange([...filteredSelectedRow, ...newSelectedRow]);
        }
        return [...filteredSelectedRow, ...newSelectedRow];
      });
    },
    hideSelectAll: true,
    selectedRowKeys: selectedRow.map(row => row[rowKey]).filter((key): key is React.Key => key !== undefined),
  };

  // 查询
  const handleSearch = () => {
    setQuery(searchValue); // 更新查询条件
  };

  // 重置查询和选择
  const handleReset = () => {
    setSearchValue('');
    setQuery('');
  };

  const handleRemoveFile = (key: React.Key) => {
    setSelectedRow(prev => {
      const updatedFiles = prev.filter(row => row[rowKey] !== key);
      if (onChange) {
        onChange(updatedFiles);
      }
      return updatedFiles;
    });
  };

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder="请输入姓名或工号"
          value={searchValue}
          onChange={e => setSearchValue(e.target.value)}
          style={{ width: 350 }}
          onPressEnter={handleSearch}
        />
        <Button type="primary" onClick={handleSearch}>
          查询
        </Button>
        <Button onClick={handleReset}>重置</Button>
      </Space>

      <Table
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        rowKey={record => record[rowKey] as string}
        scroll={{ y: 275 }}
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
      />

      <div style={{ marginTop: 16 }}>
        {selectedRow.map(row => (
          <Tag
            key={row[rowKey]}
            closable
            onClose={() => handleRemoveFile(row[rowKey] as React.Key)}
            title={row.userName}
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              maxWidth: '100%',
              marginBottom: 8,
            }}
          >
            <span
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: 150,
              }}
            >
              {`${row.userName}${row.userOneId}`}
            </span>
          </Tag>
        ))}
      </div>
    </div>
  );
};

export default HeadQuarterPersonSelector;
