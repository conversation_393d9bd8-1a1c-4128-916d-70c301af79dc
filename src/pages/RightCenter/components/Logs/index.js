import React, { useEffect, useState } from 'react';
import moment from 'moment';
import allUrl from '@/utils/url';
import { get } from '@/utils/request';
import LogItem from './LogItem';
import NoData from '@/assets/img/noData.png';
import './index.less';
// 操作类型：0-删除 1-关联，2-更改，3-系统创建，4-手动创建，5-权益解绑，6-邮件创建，7-账户还原，8-账户注销，9-账户改配，10-权益包创建并发布，11-权益包发布，12-权益包下架， 13: '退定', 14: '异常关单'

const OPERATEMAP = {
  detail: {
    2: '编辑',
    3: '创建',
    6: '邮件创建',
  },
  pack: {
    2: '编辑',
    3: '创建',
    10: '创建并发布',
    11: '发布',
    12: '下架',
    20: '邮件创建并发布',
  },
  account: {
    1: '关联',
    2: '变更',
    3: '系统创建',
    4: '人工创建',
    5: '解绑',
    6: '邮件创建',
    7: '还原',
    8: '注销',
    9: '改配',
    13: '退定',
    14: '异常关单',
    21: '激活',
    27: '自动更新',
    28: '手动更新',
    30: '车衣溯源-创建',
    31: '车衣溯源-作废',
  },
};
/**
 * @param {component} children 子组件
 * @param {String} dataCode 编码，权益明细传明细编码，权益包传包编码，权益账户是账户编码
 * @param {String} type 类型，“detail":明细，“pack":包，“account":账户
 * @param {Boolean} showAll 是否展示全部
 */
const Logs = props => {
  const { children = () => {}, dataCode, type, showAll } = props;
  const [logData, setLogData] = useState([]);

  useEffect(() => {
    if (!dataCode) return;
    get(allUrl.AccountManagement.getLog, { dataCode }).then(res => {
      if (res.success) {
        if (res.resp.length) {
          res.resp.map((item, index) => {
            item.key = index + 1;
            //权益账户特殊处理changeContent
            if (item.changeContent?.startsWith('{')) {
              item.changeContent = JSON.parse(item.changeContent);
            }
          });
          setLogData(res.resp);
        }
      }
    });
  }, [dataCode]);

  return (
    <div className="log-wrap">
      {logData && !!logData.length ? (
        logData &&
        logData.map((item, index) => {
          if (showAll) {
            return <LogItem item={item} operatemap={OPERATEMAP} type={type} children={children} />;
          } else {
            if (index === 0) return <LogItem item={item} operatemap={OPERATEMAP} type={type} children={children} />;
          }
        })
      ) : (
        <div style={{ textAlign: 'center', paddingBottom: 20 }}>
          <img style={{ width: 124, height: 104 }} src={NoData} />
          <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
        </div>
      )}
    </div>
  );
};
export default Logs;
