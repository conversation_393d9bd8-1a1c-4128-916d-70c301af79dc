import React from "react";
import "./index.less";
import { Form, Input, Checkbox, Button, message, Spin, Row, Col, Modal, Skeleton } from "antd";
import { connect } from "react-redux";
import { post, get } from "../../utils/request";
import allUrl from "../../utils/url";
import { GetUserInfo } from "../../actions/async";
import { changePsd } from "../../actions/normal";
import { EncryptByAES } from "../../components/Public/Encryption";
import { DecryptByAES } from "../../components/Public/Decrypt";
import Cookies from "js-cookie";
import moment from "moment";
import baseURL from "@/baseURL";
import { getUrlParams, getParam } from "@/utils";
import { LockOutlined, MobileOutlined, MailOutlined } from "@ant-design/icons";

class Login extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      errorVisible: false,
      msg: "手机号或密码错误",
      imgCodeObj: {},
      userAgreementTips: false,
      fullLoading: true,
      isFirstGetTicketLogin: true,
    };
    this.userAgreement = process.env.NODE_ENV == "development" ? true : false;
  }
  formRef = React.createRef();
  componentWillUnmount() {
    clearInterval(this.timer);
    this.setState = () => {
      return;
    };
  }
  componentWillMount() {
    // console.log('this.props.history', this.props.history)
    // this.props.history.push("/home")
  };
  refreshValidation = () => {
    get(allUrl.Authority.kaptchaimageCode).then((res) => {
      console.log('获取验证码返回值', res);
      if (res.success) {
        this.setState({ imgCodeObj: res.resp[0] });
      } else {
        // message.error(res.msg)
      }
    });
  }
  // 判断是否sso登录
  getTicketLogin = () => {
    const ticket = getParam("ticket");
    if (ticket) {
      post(allUrl.Authority.ticketLogin, {
        applicationId: 1,
        clientId: 927656,
        ticket,
      }).then((res) => {
        console.log('getTicketLogin返回值', res);
        this.setState({isFirstGetTicketLogin: false})
        if (res && res.success) {
          let Dt = res.resp[0];
          Cookies.set("scrm_token", Dt.accessToken);
          Cookies.remove("isTransfer");
          sessionStorage.setItem("changePwdStatus", Dt.changePwdStatus);
          this.props.dispatch(GetUserInfo()).then((info) => {
            console.log("用户信息返回值", info);
            window.location.href = '/#/home'
          });
          // this.setState({ errorVisible: true, msg: res.msg });
        } else {
          console.log('error', res)
          // message.error(res.msg)
        }
      });
    }
  };
  componentDidMount() {
    console.log('componentDidMount', !Cookies.get("scrm_token"), getParam("ticket"))
    // 只要传了ticket，就重新登录
    if (getParam("ticket") && this.state.isFirstGetTicketLogin) {
      this.getTicketLogin();
    } else {
      Cookies.get("scrm_token") && (window.location.href = '/#/home')
    }
    if (localStorage.getItem("remember")) {
      let data = JSON.parse(localStorage.getItem("remember"));
      data.mobilePhone = DecryptByAES(data.mobilePhone, "5566778899ABCDEF", "ABCDEF5566778899");
      data.password = DecryptByAES(data.password, "5566778899ABCDEF", "ABCDEF5566778899");
      // this.formRef.current.setFieldsValue({ ...data })
    }
   setTimeout(() => {
    this.setState({fullLoading: false})
   }, 500);
  

    
   
    this.refreshValidation();
    this.timer = setInterval(() => {
      this.refreshValidation();
    }, 60000);
  }

  onFinish = (values) => {
    const { imgCodeObj } = this.state;
    if (!this.userAgreement) {
      this.setState({
        userAgreementTips: true,
      });
      return;
    } else {
      this.setState({
        userAgreementTips: false,
      });
    }
    this.setState({ loading: true }, () => {
      let mobilePhone = EncryptByAES(values.mobilePhone, "5566778899ABCDEF", "ABCDEF5566778899");
      let password = EncryptByAES(values.password, "5566778899ABCDEF", "ABCDEF5566778899");
      let obj = {
        password: EncryptByAES(values.password, "5566778899ABCDEF", "ABCDEF5566778899"),
        mobilePhone: values.mobilePhone,
        verifyCode: values.verifyCode,
        codeId: imgCodeObj.codeId,
      };
      try {
        post(allUrl.Authority.phoneLogin, obj).then((res) => {
          if (res && res.success) {
            let Dt = res.resp[0];
            //记住密码功能
            if (values.remember) {
              localStorage.setItem("remember", JSON.stringify({ mobilePhone, password }));
            } else {
              localStorage.removeItem("remember");
            }
            Cookies.set("scrm_token", Dt.accessToken);
            Cookies.remove("isTransfer");
            sessionStorage.setItem("changePwdStatus", Dt.changePwdStatus);
            this.props.dispatch(GetUserInfo(() => window.location.href = '/#/home'));
            // this.props.history.push('/home')
            if (Dt.changePwdStatus) {
              setTimeout(() => {
                Modal.warning({
                  title: "为保证账号使用安全，请修改初始密码。",
                  onOk: () => {
                    this.props.dispatch(changePsd(true));
                  },
                });
              }, 400);
            }
          } else {
            // message.error(res.msg)
            this.refreshValidation();
            this.setState({ errorVisible: true, msg: res.msg });
          }
          this.setState({ loading: false });
        });
      } catch (error) {
        this.setState({ loading: false });
      }
    });
  };

  onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };
  TabsCallback = (key) => {
    console.log(key);
  };

  rememberChange = () => {
    message.info("请联系系统管理员重置密码!");
  };

  alertClose = () => {
    setTimeout(() => {
      this.setState({ errorVisible: false });
    }, 200);
  };

  agreeChangHandle = (e) => {
    this.userAgreement = e.target.checked;
  };
  jumpSSO = () => {
    const str = `${window.location.origin}/`;
    // const filteredStr = str.replace(/\/#\//g, "");
    window.location.href = `${baseURL.EXSSO_HOST}?redirect=${str}&appId=927656&_t=${new Date().getTime()}`;
  };
  render() {
    const { imgCodeObj } = this.state;
    const layout = {
      labelCol: { span: 0 },
      wrapperCol: { span: 24 },
    };
    const tailLayout = {
      wrapperCol: { offset: 0, span: 24 },
    };
    const submitLayout = {
      wrapperCol: { offset: 0, span: 24 },
    };

    return (
      <div style={{height: '100%'}}> 
        {
        this.state.fullLoading ? (
          <Spin size="large" spinning={this.state.fullLoading}></Spin>
        ) : (
          <div className="login_warp">
            <div className="login">
              <div className="login_con">
                <div className="login_con_left"></div>
                <div className="login_con_right">
                  <div className="login_con_right_logo">
                    <img src={require("@/assets/img/login_logo.png")} alt="" />
                  </div>
                  <Form
                    {...layout}
                    className="login_form"
                    name="basic"
                    initialValues={{ remember: true }}
                    ref={this.formRef}
                    onFinish={this.onFinish}
                    onFinishFailed={this.onFinishFailed}
                  >
                    <Spin spinning={this.state.loading}>
                      <Form.Item
                        name="mobilePhone"
                        rules={[
                          {
                            required: true,
                            message: "请输入手机号！",
                          },
                        ]}
                      >
                        <Input
                          size="large"
                          prefix={<MobileOutlined style={{ color: "#1890FF" }} />}
                          autoComplete={"off"}
                          placeholder="请输入手机号"
                        />
                      </Form.Item>

                      <Form.Item
                        name="password"
                        rules={[
                          {
                            required: true,
                            message: "请输入密码！",
                          },
                        ]}
                      >
                        <Input.Password
                          size="large"
                          prefix={<LockOutlined style={{ color: "#1890FF" }} />}
                          placeholder="请输入密码"
                        />
                      </Form.Item>
                      <Row>
                        <Col span={15}>
                          <Form.Item
                            name="verifyCode"
                            rules={[
                              {
                                required: true,
                                message: "请输入验证码！",
                              },
                            ]}
                          >
                            <Input
                              size="large"
                              prefix={<MailOutlined style={{ color: "#1890FF" }} />}
                              placeholder="验证码"
                            />
                          </Form.Item>
                        </Col>
                        <Col span={8} offset={1}>
                          <Form.Item>
                            {imgCodeObj ? (
                              <img
                                src={`data:image/png;base64,${imgCodeObj.img}`}
                                title="点击刷新验证码"
                                alt=""
                                style={{ fontSize: "100px", cursor: "pointer" }}
                                onClick={this.refreshValidation}
                              />
                            ) : null}
                          </Form.Item>
                        </Col>
                        {/* <Col span={2} style={{ paddingTop: '10px', paddingLeft: '20px' }}>
                                                    <ReloadOutlined style={{ cursor: 'pointer', color: 'rgb(0 0 0 / 30%)', fontSize: '16px' }} onClick={this.} />
                                                </Col> */}
                      </Row>
                      <div className="login_bts">
                        <Form.Item {...tailLayout} name="remember" valuePropName="checked">
                          <Checkbox className="remember">记住密码</Checkbox>
                        </Form.Item>
                        <Form.Item {...tailLayout} name="remember" valuePropName="checked">
                          <span className="forget" onClick={this.rememberChange}>
                            忘记密码
                          </span>
                        </Form.Item>
                      </div>

                      <Form.Item {...submitLayout} style={{ marginBottom: 5 }}>
                        <Button type="primary" htmlType="submit" className="submit">
                          登录
                        </Button>
                      </Form.Item>
                      <Row>
                        <Col>
                          {this.state.userAgreementTips ? (
                            <span
                              style={{
                                color: "rgba(245,34,45,1)",
                                fontSize: "13px",
                                lineHeight: 1.5,
                              }}
                            >
                              请阅读并勾选用户协议和保密协议！
                            </span>
                          ) : (
                            <span>&nbsp;</span>
                          )}
                        </Col>
                      </Row>
                      <Checkbox onChange={(e) => this.agreeChangHandle(e)}>
                        <div>
                          已阅读并同意
                          <a
                            target="_blank"
                            style={{ cursor: "pointer" }}
                            href="https://scrm.seres.cn/SERES%E5%8D%8F%E5%90%8C%E7%B3%BB%E7%BB%9F%E7%94%A8%E6%88%B7%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.html"
                          >
                            {" "}
                            《用户服务协议》
                          </a>
                          和
                          <a
                            target="_blank"
                            href="https://scrm.seres.cn/SERES%E5%8D%8F%E5%90%8C%E7%B3%BB%E7%BB%9F%E6%95%B0%E6%8D%AE%E4%BF%9D%E5%AF%86%E5%8D%8F%E8%AE%AE.html"
                            style={{ cursor: "pointer" }}
                          >
                            {" "}
                            《数据保密协议》
                          </a>
                        </div>
                      </Checkbox>
                      <div className="group_8 flex-row justify-between">
                        <img className="image_3" src={require("@/assets/img/line.png")} />
                        <span className="text_12">或</span>
                        <img className="image_4" src={require("@/assets/img/line.png")} />
                      </div>
                      <div className="group_9 flex-row" onClick={this.jumpSSO}>
                        <div className="image-text_6 flex-row justify-between">
                          <img className="thumbnail_5" src={require("@/assets/img/sso-logo.png")} />
                          <span className="text-group_6">SERES统一登录（支持短信/工号登录）</span>
                        </div>
                      </div>
                    </Spin>
                  </Form>
                </div>
              </div>
              <div className="copyright_info">
                Copyright &copy; 2021-{moment().format("YYYY")} SERES. All Rights Reserved.
                北京赛力斯智行科技有限公司 版权所有
              </div>

              {/* <div className='login_img'>
                                <img src={require('@/assets/img/login.png')} alt='' />
                            </div>
                            <div className='login_con'>
                                <div className='login_logo'>
                                    <img src={require('@/assets/img/login_logo.png')} alt='' />
                                </div>
                                <Form
                                    {...layout}
                                    className='login_form'
                                    name="basic"
                                    initialValues={{ remember: true, }}
                                    ref={this.formRef}
                                    onFinish={this.onFinish}
                                    onFinishFailed={this.onFinishFailed}
                                >
                                    <Spin spinning={this.state.loading}>
                                        <div className='tabs'>手机号密码登陆</div>
                                        {
                                            this.state.errorVisible &&
                                            <Alert message={this.state.msg} type="error" showIcon closable onClose={() => this.alertClose()} />
                                        }
                                        <Form.Item
                                            name="mobilePhone"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请输入手机号！',
                                                },
                                            ]}
                                        >
                                            <Input size='large' prefix={<MobileOutlined style={{ color: '#1890FF' }} />} allowClear autoComplete={'off'} placeholder='手机号' />
                                        </Form.Item>

                                        <Form.Item
                                            name="password"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请输入密码！',
                                                },
                                            ]}
                                        >
                                            <Input.Password size='large' prefix={<LockOutlined style={{ color: '#1890FF' }} />} allowClear placeholder='密码' />
                                        </Form.Item>
                                        <Row>
                                            <Col span={12}>
                                                <Form.Item
                                                    name="verifyCode"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请输入验证码！',
                                                        },
                                                    ]}
                                                >
                                                    <Input size='large' prefix={<MailOutlined style={{ color: '#1890FF' }} />} allowClear placeholder='验证码' />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8} offset={1}>
                                                <Form.Item>
                                                    {
                                                        imgCodeObj ?
                                                            <img src={`data:image/png;base64,${imgCodeObj.img}`} alt='' style={{ fontSize: '100px' }} />
                                                            : null
                                                    }
                                                </Form.Item>
                                            </Col>
                                            <Col span={2} style={{ paddingTop: '10px', paddingLeft: '20px' }}>
                                                <ReloadOutlined style={{ cursor: 'pointer', color: 'rgb(0 0 0 / 30%)', fontSize: '16px' }} onClick={this.refreshValidation} />
                                            </Col>
                                        </Row>
                                        <div className='login_bts'>
                                            <Form.Item {...tailLayout} name="remember" valuePropName='checked'>
                                                <Checkbox className='remember'>记住密码</Checkbox>
                                            </Form.Item>
                                            <Form.Item {...tailLayout} name="remember" valuePropName='checked'>
                                                <span className='forget' onClick={this.rememberChange}>忘记密码</span>
                                            </Form.Item>
                                        </div>

                                        <Form.Item {...submitLayout}>
                                            <Button type="primary" htmlType="submit" className='submit'>登录</Button>
                                        </Form.Item>
                                    </Spin>
                                </Form>
                            </div> */}
            </div>
          </div>
        )}
      </div>
    );
  }
}
export default (connect()(Login));
