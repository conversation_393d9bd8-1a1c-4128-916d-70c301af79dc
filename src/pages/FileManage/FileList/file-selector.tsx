import { getSeresFileItemOnline } from '@/apis/file-manage';
import { useRequest } from '@/hooks/request';
import { Button, Input, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import { FILE_BIZ_TYPE_MAP } from '../constants';

interface FileSelectorProps {
  value?: FileManage.LinkFileVO[]; // 父组件传递的已选文件
  onChange?: (value: FileManage.LinkFileVO[]) => void; // 选择发生变化时回调
  bizType?: number; // 业务类型
}

const FileSelector: React.FC<FileSelectorProps> = ({ value, onChange, bizType }) => {
  const [selectedFiles, setSelectedFiles] = useState<FileManage.LinkFileVO[]>([]); // 已选文件
  const [searchValue, setSearchValue] = useState(''); // 输入框内容
  const [query, setQuery] = useState(''); // 当前查询条件

  useEffect(() => {
    if (value) {
      setSelectedFiles(value); // 更新已选文件
    }
  }, [value]);

  // 请求数据
  const { data = [], loading } = useRequest(
    () => {
      if (query) return getSeresFileItemOnline({ bizType, name: query });
      return Promise.resolve([]);
    },
    { deps: [query] }
  );

  // 表格列配置
  const columns: ColumnsType<FileManage.LinkFileVO> = [
    { title: '文件名称', dataIndex: 'fileName', key: 'fileName', ellipsis: true },
    { title: '业务类型', dataIndex: 'bizType', key: 'bizType', render: (bizType) => FILE_BIZ_TYPE_MAP[bizType] },
    { title: '创建人', dataIndex: 'createBy', key: 'createBy' },
  ];

  // 表格行选择配置
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: FileManage.LinkFileVO[]) => {
      // 更新本地已选文件
      setSelectedFiles((prevSelectedFiles) => {
        const selectedRowKeysSet = new Set(selectedRowKeys);

        // 未选中项
        const unSelectedFiles = data.filter((file) => !selectedRowKeysSet.has(file.id as React.Key));
        // 保留当前选中项，并移除未选中项
        const filteredSelectedFiles = prevSelectedFiles.filter(
          (file) => !unSelectedFiles.some((unSelectedFile) => unSelectedFile.id === file.id)
        );

        // 添加新选中的项
        const newSelectedFiles = selectedRows.filter((row) => !prevSelectedFiles.some((file) => file.id === row.id));
        if (onChange) {
          onChange([...filteredSelectedFiles, ...newSelectedFiles]);
        }
        return [...filteredSelectedFiles, ...newSelectedFiles];
      });
    },
    hideSelectAll: true,
    selectedRowKeys: selectedFiles.map((file) => file.id).filter((id): id is number => id !== undefined),
  };

  // 查询文件
  const handleSearch = () => {
    setQuery(searchValue); // 更新查询条件
  };

  // 重置查询和选择
  const handleReset = () => {
    setSearchValue('');
    setQuery('');
  };

  const handleRemoveFile = (id: number) => {
    setSelectedFiles((prev) => {
      const updatedFiles = prev.filter((file) => file.id !== id);
      // 更新已选文件时同步调用 onChange
      if (onChange) {
        onChange(updatedFiles);
      }
      return updatedFiles;
    });
  };

  return (
    <div>
      {/* 查询和重置 */}
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder="请输入文件名称"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          style={{ width: 350 }}
          onPressEnter={handleSearch}
        />
        <Button type="primary" onClick={handleSearch}>
          查询
        </Button>
        <Button onClick={handleReset}>重置</Button>
      </Space>

      {/* 文件表格 */}
      <Table
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        rowKey={(record) => record.id as unknown as string}
        scroll={{ y: 275 }}
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
      />

      {/* 已选文件列表 */}
      <div style={{ marginTop: 16 }}>
        {selectedFiles.map((file) => (
          <Tag
            key={file.id}
            closable
            onClose={() => handleRemoveFile(file.id as number)}
            title={file.fileName}
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              maxWidth: '100%',
              marginBottom: 8,
            }}
          >
            <span
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: 150,
              }}
            >
              {file.fileName}
            </span>
          </Tag>
        ))}
      </div>
    </div>
  );
};

export default FileSelector;
