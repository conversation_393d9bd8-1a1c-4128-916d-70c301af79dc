import { getSeresFileItemAccessConfig, postSeresFileItemAccessConfig } from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { useRequest } from '@/hooks/request';
import { Form, Modal, ModalProps, Radio } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import CommonJobSelect from '../components/common-job-select';
import SupplierJobSelect from '../components/store-job-select';
import StoreSelect from '../components/store-select';
import StoreJobSelect from '../components/supplier-job-select';
import SupplierSelect from '../components/supplier-select';
import {
  FILE_DELIVER_TYPE_OPTIONS,
  FILE_DELIVER_TYPE_TO_COMMON_JOB_TYPE_MAP,
  FILE_POST_TYPE_OPTIONS,
  FileDeliverType,
  FilePostType,
} from '../constants';
import PersonSelector from './person-selector';

export interface DeliverScopeModalProps extends ModalProps {
  open: boolean;
  fileId?: number;
  onOk?: () => void;
}

const DeliverScopeModal: React.FC<DeliverScopeModalProps> = ({ open, fileId, onOk, ...restProps }) => {
  const [form] = Form.useForm();
  const [pending, setPending] = useState(false);
  const { data: [fileDetail] = [] } = useRequest(
    () => {
      if (fileId) {
        return getSeresFileItemAccessConfig({ id: fileId });
      }
      return Promise.resolve(undefined);
    },
    {
      deps: [fileId],
    }
  );
  const initEditForm = useCallback(async () => {
    if (fileDetail) {
      form.setFieldsValue({
        ...fileDetail,
        supplierPerson: fileDetail.specificUserList,
      });
    }
  }, [fileDetail, form]);

  useEffect(() => {
    initEditForm();
  }, [initEditForm]);

  const handleModalOk = useCallback(async () => {
    try {
      const { supplierPerson, ...values } = await form.validateFields();
      const { specificPost = [], frequentPost = [] } = values;
      const frequentPostList = frequentPost.map(item => item.list).flat();
      const post = [...specificPost, ...frequentPostList];
      // 把post中的对象，根据code去重
      const postSet = new Set();
      const postArray = post.filter(item => {
        if (postSet.has(item.code)) {
          return false;
        }
        postSet.add(item.code);
        return true;
      });
      delete values.frequentPost;
      const params = {
        id: fileId,
        ...values,
        ...(postArray?.length ? { specificPost: postArray } : {}),
        specificUserList: supplierPerson?.map(item => item.userOneId) || [],
      };
      console.log('params', params);
      setPending(true);
      await postSeresFileItemAccessConfig(params);
      onOk?.();
    } catch (error) {
    } finally {
      setPending(false);
    }
  }, [form, onOk, fileId]);

  return (
    <Modal
      afterClose={() => {
        form.resetFields();
      }}
      width={800}
      confirmLoading={pending}
      title="下发范围"
      open={open}
      onOk={handleModalOk}
      {...restProps}
    >
      <Form
        form={form}
        {...modalFormLayout}
        initialValues={{
          deliverType: FileDeliverType.ALL_STORE,
          postType: FilePostType.ALL_POST,
        }}
      >
        <Form.Item label="下发范围" name="deliverType" rules={[{ required: true, message: '请选择下发范围' }]}>
          <Radio.Group
            options={FILE_DELIVER_TYPE_OPTIONS}
            onChange={() => form.resetFields(['deliverScope', 'specificPost', 'frequentPost'])}
          />
        </Form.Item>
        <Form.Item noStyle dependencies={['deliverType']}>
          {({ getFieldValue }) => {
            const deliverType = getFieldValue('deliverType') as FileDeliverType;
            if (deliverType === FileDeliverType.SPECIFIC_STORE) {
              return (
                <Form.Item
                  label="门店"
                  name="deliverScope"
                  rules={[{ required: true, message: '请选择门店' }]}
                  getValueFromEvent={(values, labels) => {
                    return values?.map((code, index) => ({
                      code,
                      name: labels[index],
                    }));
                  }}
                  getValueProps={values => {
                    return {
                      value: values?.map(item => item.code),
                    };
                  }}
                >
                  <StoreSelect multiple placeholder="请选择门店" />
                </Form.Item>
              );
            } else if (deliverType === FileDeliverType.SPECIFIC_SUPPLIER) {
              return (
                <Form.Item
                  label="供应商"
                  name="deliverScope"
                  rules={[{ required: true, message: '请选择供应商' }]}
                  getValueFromEvent={(_, options) => {
                    return options;
                  }}
                  getValueProps={value => {
                    return {
                      value: value?.map(item => item.code),
                    };
                  }}
                >
                  <SupplierSelect
                    onChange={() => {
                      form.resetFields(['supplierPerson']);
                    }}
                    mode="multiple"
                    placeholder="请选择下发供应商"
                  />
                </Form.Item>
              );
            }
            return null;
          }}
        </Form.Item>
        <Form.Item name="postType" label="岗位" rules={[{ required: true, message: '请选择岗位' }]}>
          <Radio.Group
            options={FILE_POST_TYPE_OPTIONS}
            onChange={() => form.resetFields(['specificPost', 'frequentPost', 'supplierPerson'])}
          />
        </Form.Item>
        <Form.Item noStyle dependencies={['postType', 'deliverType']}>
          {({ getFieldValue }) => {
            const deliverType = getFieldValue('deliverType') as FileDeliverType;
            const postType = getFieldValue('postType') as FilePostType;
            if (postType === FilePostType.SPECIFIC_POST) {
              const JobSelect =
                deliverType === FileDeliverType.SPECIFIC_STORE || deliverType === FileDeliverType.ALL_STORE
                  ? StoreJobSelect
                  : SupplierJobSelect;
              return (
                <>
                  <Form.Item
                    wrapperCol={{ ...modalFormLayout.wrapperCol, offset: modalFormLayout.labelCol.span }}
                    name="specificPost"
                    getValueFromEvent={(_, options) => {
                      return options;
                    }}
                    getValueProps={value => {
                      return {
                        value: value?.map(item => item.code),
                      };
                    }}
                  >
                    <JobSelect mode="multiple" maxTagCount={10} allowClear placeholder="请选择岗位" />
                  </Form.Item>
                  <Form.Item wrapperCol={{ ...modalFormLayout.wrapperCol }} name="frequentPost" label="常用岗位">
                    <CommonJobSelect type={FILE_DELIVER_TYPE_TO_COMMON_JOB_TYPE_MAP[deliverType]} />
                  </Form.Item>
                </>
              );
            }
            return null;
          }}
        </Form.Item>
        <Form.Item noStyle dependencies={['deliverType', 'postType', 'deliverScope']}>
          {({ getFieldValue }) => {
            const deliverType = getFieldValue('deliverType') as FileDeliverType;
            const postType = getFieldValue('postType') as FilePostType;
            const deliverScope = getFieldValue('deliverScope');

            if (deliverType === FileDeliverType.SPECIFIC_SUPPLIER && postType === FilePostType.SPECIFIC_POST) {
              return (
                <Form.Item label="指定人员" name="supplierPerson">
                  <PersonSelector rowKey="userOneId" orgCodeList={deliverScope?.map(item => item.code)} />
                </Form.Item>
              );
            }
            return null;
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DeliverScopeModal;
