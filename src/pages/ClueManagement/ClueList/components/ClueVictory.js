import React from 'react'
import { Modal, message, Form, Input,Select,Row,Col,Spin,Button} from 'antd'
import { SyncOutlined } from '@ant-design/icons';
import allUrl from '../../../../utils/url'
import { post, get } from '../../../../utils/request'
import { connect } from 'react-redux'
const {Option} = Select
class ClueVictory extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {
            loading:false,
            orderInfoData:{},
            saleInfoData:{},
            modalVisable:false,
            modalVisable2:false,
        }
    }
    formRef = React.createRef();

    ininForm = () => {
        const { recordData } = this.props
        get(allUrl.ClueManagement.reviewVictoryDetails,{planId:recordData.id}).then(res=>{
            if(res.success){
                if(res.resp[0].orderInfo){
                    this.setState({orderInfoData:res.resp[0].orderInfo})
                }
                if(res.resp[0].saleInfo){
                    this.setState({saleInfoData:res.resp[0].saleInfo})
                }
                
            }else{
                message.error(res.msg)
            }
        })
    }
    openModalVisable = () => {
        this.setState( { modalVisable:true} )
    }
    handleOk = (key) => {
        const { onCancel , getData,recordData} = this.props
        let pass = key===1? true :false
        post(allUrl.ClueManagement.approveVictory,{planId:recordData.id,pass:pass}).then(res=>{
            if(res.success){
                getData()
                onCancel()
                this.setState( { modalVisable:false,modalVisable2:false })
            }else{
                message.error(res.msg)
            }
        })
        
    }
    render() {
        const { visible, title,onCancel,recordData } = this.props
        const { loading,modalVisable,orderInfoData,saleInfoData,modalVisable2 } = this.state
        console.log(this.state.current)
        const layout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 20 },
        };
            return (
                <Modal visible={visible} title={title} onCancel={onCancel}  maskClosable={false}width={700} onOk={this.openModalVisable}
                footer={[
                    <Button type="primary" onClick={this.openModalVisable}> 已战赢</Button>,
                    <Button  onClick={()=>this.setState( { modalVisable2:true})}> 未战赢</Button>,
                  ]}
                >
                    <Spin spinning={loading}>
                        <Row>
                            <Col span={24} style={{marginBottom:'12px'}}>请核对订单信息和销售信息，确认该销售是否战赢：</Col>
                        </Row>
                        <Row>
                            <Col span={24} style={{fontSize:'14px',marginBottom:'12px',fontWeight:'500'}}>一、订单信息</Col>
                        </Row>
                        <Row style={{paddingLeft:'20px'}}>
                            <Col span={22} >1、交付单编号：{orderInfoData.orderNo ? orderInfoData.orderNo :'-'}</Col>
                            <Col span={22} >2、交付单状态：{orderInfoData.orderStatus  ? orderInfoData.orderStatus  :'-'}</Col>
                            <Col span={22} >3、购车人：{orderInfoData.buyerName  ? orderInfoData.buyerName +
                            `(${orderInfoData.buyerPhoneNum  ? orderInfoData.buyerPhoneNum  :'-'})`:'-'}</Col>
                            <Col span={22} >4、车主：{orderInfoData.carOwnerName  ? orderInfoData.carOwnerName +
                            `(${orderInfoData.carOwnerPhone  ? orderInfoData.carOwnerPhone  :'-'})` :'-'}</Col>
                            <Col span={22} >5、车型配置：{orderInfoData.itemName  ? orderInfoData.itemName  :'-'}</Col>
                            <Col span={22} >6、大定创建时间：{orderInfoData.orderTime  ? orderInfoData.orderTime  :'-'}</Col>
                            <Col span={22} >7、大定支付时间：{orderInfoData.orderPayTime  ? orderInfoData.orderPayTime  :'-'}</Col>
                            <Col span={22} >8、交付单创建时间：{orderInfoData.deliveryCreateTime  ? orderInfoData.deliveryCreateTime  :'-'}</Col>
                        </Row>
                        <Row>
                            <Col span={24} style={{fontSize:'14px',marginBottom:'12px',fontWeight:'500'}}>二、销售信息</Col>
                        </Row>
                        <Row style={{paddingLeft:'20px'}}>
                            <Col span={22} >1、销售工号：{saleInfoData.employeeNumber  ? saleInfoData.employeeNumber  :'-'}</Col>
                            <Col span={22} >2、销售姓名：{saleInfoData.name  ? saleInfoData.name  :'-'}</Col>
                            <Col span={22} >3、销售手机号：{saleInfoData.mobilePhone  ? saleInfoData.mobilePhone  :'-'}</Col>
                            <Col span={22} >4、所属门店：{saleInfoData.dealerName  ? saleInfoData.dealerName	 :'-'}</Col>
                            <Col span={22} >5、所属门店编号：{saleInfoData.dealerCode  ? saleInfoData.dealerCode  :'-'}</Col>
                        </Row>
                       
                    </Spin>
                    {
                        modalVisable ? 
                        <Modal visible={modalVisable} title='战赢确定' onCancel={() => {
                            this.setState({ modalVisable:false} )
                        }} maskClosable={false}width={500} onOk={()=>this.handleOk(1)}>
                            <Row>
                                <Col span={22} >若确认销售顾问{saleInfoData.name? saleInfoData.name :'-'}战赢线索{orderInfoData.buyerName  ? orderInfoData.buyerName +
                            `(${orderInfoData.buyerPhoneNum  ? orderInfoData.buyerPhoneNum  :'-'})`:'-'}</Col>
                                <Col span={22} >将无法修改状态，是否确认战赢？</Col>
                           </Row>
                        </Modal>:null
                    }
                    {
                        modalVisable2 ? 
                        <Modal visible={modalVisable2} title='审核结果确定' onCancel={() => {
                            this.setState({ modalVisable2:false} )
                        }} maskClosable={false}width={500} onOk={()=>this.handleOk(2)}>
                            <Row>
                                <Col span={22} >若确认销售顾问{saleInfoData.name? saleInfoData.name :'-'}未战赢线索{orderInfoData.buyerName  ? orderInfoData.buyerName +
                            `(${orderInfoData.buyerPhoneNum  ? orderInfoData.buyerPhoneNum  :'-'})`:'-'}</Col>
                                <Col span={22} >将退回申请，是否确认？</Col>
                           </Row>
                        </Modal>:null
                    }
                </Modal>
                
            )
        }
        componentDidMount(){
            this.ininForm()
        }
    }
    export default connect()(ClueVictory)