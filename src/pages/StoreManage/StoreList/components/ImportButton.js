import React,{useEffect, useState} from 'react'
import {Modal,Input,Button} from 'antd'
import {ExclamationCircleFilled} from '@ant-design/icons';
import baseURL from '@/baseURL'
import UploadFile from '@/components/Public/UploadFile'
const ImportID = (props) =>{
    const {title,visible,onCancel,UploadChange,url,loading} = props
    const handleOk = () =>{

    }
    return <Modal title={title} visible={visible} footer={null} maskClosable={false} onCancel={onCancel}>
                <div style={{display:'flex'}}>
                    <div >
                        <ExclamationCircleFilled style={{marginRight:'7px',color:'#faad14',fontSize:'16px'}} />导入会覆盖所有的门店信息，是否继续导入？
                    </div>
                </div>
                <div style={{marginTop:'20px',textAlign:'right'}}>
                    <Button style={{margin:'0 10px'}} onClick={onCancel} >取消</Button>
                    <UploadFile
                        style={{ display: 'inline-block' }}
                        extension={['xls', 'xlsx']}
                        showUploadList={false}
                        size={10}
                        action={baseURL.Host + url}
                        UploadChange={UploadChange}
                    >
                        <Button loading={loading} style={{margin:'0 10px'}} type='primary' onClick={handleOk}>继续导入</Button>
                    </UploadFile>
                </div>

    </Modal>
}

const ImportButton = (props) =>{
    const {loading,children,style,url} = props
    const [ImportIDVisible,setImportIDVisible] = useState(false)
    const UploadChange = ({file,fileList},type) =>{
        setImportIDVisible(false)
        props.UploadChange({file,fileList},type)
    }
    return <>
        <Button loading={loading} onClick={()=>setImportIDVisible(true)} style={{...style}}>{children}</Button>
        <ImportID url={url} loading={loading} title='提示' UploadChange={UploadChange} visible={ImportIDVisible} onCancel={()=>setImportIDVisible(false)}/>
    </>
}


export default ImportButton