import {
  postSeresFileDirectoryList,
  postSeresFileDirectoryRemove,
  postSeresFileDirectorySort,
  postSeresFileItemExpire,
  postSeresFileItemList,
  postSeresFileItemNotice,
  postSeresFileItemPermission,
  postSeresFileItemStatusUpdate,
} from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import ProTable, { ProTableActions } from '@/components/pro-table';
import UserPermissionAuthorize from '@/components/user-permission-authorize';
import { useCheckPermission } from '@/hooks/auth';
import { useRequest } from '@/hooks/request';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { fileManageListPermissions } from '@/permissions/file';
import {
  Card,
  Col,
  DatePicker,
  Empty,
  Input,
  message,
  Modal,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Tabs,
  Tree,
  TreeProps,
} from 'antd';
import { EventDataNode } from 'antd/lib/tree';
import React, { FC, Key, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { Link } from 'react-router-dom';
import './index.less';

import PublicTooltip from '@/components/Public/PublicTooltip';
import moment, { Moment } from 'moment';
import {
  FILE_BIZ_TYPE_MAP,
  FILE_BIZ_TYPE_TABS,
  FILE_DELIVER_TYPE_MAP,
  FILE_POST_TYPE_MAP,
  FILE_STATUS_MAP,
  FILE_STATUS_OPTIONS,
  FileBizType,
  FilePermission,
  FileStatus,
  ROOT_DIRECTORY_ID,
  YesOrNo,
} from '../constants';
import DeliverScopeModal, { DeliverScopeModalProps } from './deliver-scope-modal';
import type { DirectoryModalProps } from './directory-modal';
import DirectorModal from './directory-modal';
import FileModal, { FileModalProps } from './file-modal';
import HeadquarterPersonModal, { HeadquarterPersonModalProps } from './headquarter-person-modal';
import MoveFileModal, { MoveFileModalProps } from './move-file-modal';
import { DirectoryDataNode } from './types';

const getDirectoryNode = async (params: FileManage.DirectoryQuery) => {
  const [node] = await postSeresFileDirectoryList(params);
  return {
    ...node,
    parentId: params.id ?? ROOT_DIRECTORY_ID,
  };
};

const updateTreeData = (nodes: DirectoryDataNode[], map: Record<number, DirectoryDataNode[]>): DirectoryDataNode[] =>
  nodes.map(node => {
    const children = map[node.id as number] ?? [];
    if (children.length) {
      return {
        ...node,
        children: updateTreeData(children, map),
      };
    }
    return node;
  });

const FileList: FC = () => {
  const actionRef = useRef<ProTableActions>(null);
  const [directoryModal, setDirectoryModal] = useState<Pick<DirectoryModalProps, 'data' | 'open'>>({
    open: false,
  });
  const [deliverScopeModal, setDeliverScopeModal] = useState<DeliverScopeModalProps>({
    open: false,
  });

  const [directoryChildrenMap, setDirectoryChildrenMap] = useState<Record<number, DirectoryDataNode[]>>({});

  const [modelFileLoading, setModelFileLoading] = useState(false);

  const checkPermission = useCheckPermission();
  // 有权限的tab
  const tabsWithPermission = useMemo(() => {
    return FILE_BIZ_TYPE_TABS.filter(tab => checkPermission(tab));
  }, [checkPermission]);
  const [bizType, setBizType] = useState<FileBizType>(tabsWithPermission[0]?.key as unknown as FileBizType);
  const [selectedDirectory, setSelectedDirectory] = useState<EventDataNode<DirectoryDataNode>>();

  const { data: rootNode, loading: directoryLoading } = useRequest(
    async () => getDirectoryNode({ id: ROOT_DIRECTORY_ID, bizType }),
    {
      deps: [bizType],
    }
  );

  useEffect(() => {
    // 切换业务类型时，清空选中目录
    setSelectedDirectory(undefined);
    setSelectedRowKeys([]);
  }, [bizType]);

  useEffect(() => {
    if (rootNode) {
      setDirectoryChildrenMap({ [ROOT_DIRECTORY_ID]: rootNode.children ?? [] });
    }
  }, [rootNode]);

  const treeData = useMemo(() => {
    if (rootNode) {
      const nodes = updateTreeData(
        rootNode.children
          ? [
              {
                id: 0,
                children: rootNode.children,
                name: '根目录',
              },
            ]
          : [],
        directoryChildrenMap
      );

      if (nodes.length) {
        return nodes[0].children ?? [];
      }
    }
    return [];
  }, [directoryChildrenMap, rootNode]);

  const handleTreeDataLoad = useCallback(
    async ({ id, children }: Pick<EventDataNode<DirectoryDataNode>, 'id' | 'children'>) => {
      setModelFileLoading(true);
      if (!children) {
        const node = await getDirectoryNode({ id, bizType });
        setDirectoryChildrenMap(origin => ({ ...origin, [id as number]: node.children ?? [] }));
        // 有选中的节点，更新选中节点信息
        if (selectedDirectory) {
          const newDirectory = node.children?.find(item => item.id === selectedDirectory?.id);
          setSelectedDirectory({ ...selectedDirectory, ...newDirectory });
          if (selectedDirectory.id === id) {
            actionRef.current?.reload();
          }
        }
      }
      setModelFileLoading(false);
    },
    [bizType, selectedDirectory]
  );

  const handleTreeNodeSelect = useCallback<Required<TreeProps<DirectoryDataNode>>['onSelect']>((value, { node }) => {
    setSelectedDirectory(node);
  }, []);

  const handleDirectoryModalSuccess = useCallback(() => {
    const parentId = directoryModal.data ? directoryModal.data.parentDirId : selectedDirectory?.id ?? ROOT_DIRECTORY_ID;
    handleTreeDataLoad({ id: parentId });
    setDirectoryModal({ open: false });
  }, [directoryModal.data, handleTreeDataLoad, selectedDirectory]);

  /**
   * 添加目录
   */
  const handleDirectAddClick = useCallback(() => {
    if (selectedDirectory) {
      setDirectoryModal({
        open: true,
      });
    } else {
      setDirectoryModal({
        open: true,
      });
    }
  }, [selectedDirectory]);

  /**
   * 编辑目录
   */
  const handleDirectoryEditClick = useCallback(() => {
    if (selectedDirectory) {
      setDirectoryModal({
        open: true,
        data: selectedDirectory,
      });
    } else {
      Modal.info({
        title: '编辑目录',
        content: '您尚未选中目录，请选中目录后进行编辑！',
      });
    }
  }, [selectedDirectory]);

  /**
   * 删除目录
   */
  const handleDirectoryDeleteClick = useCallback(() => {
    if (selectedDirectory) {
      Modal.confirm({
        title: '删除目录',
        content: (
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 8 }}>您正在删除目录：{selectedDirectory.name}</div>
            <div>该目录及其次级目录下无文件，可正常删除； 删除该目录将同时删除其次级目录，请确认</div>
          </div>
        ),
        onOk: async () => {
          try {
            await postSeresFileDirectoryRemove({ id: selectedDirectory.id, name: selectedDirectory.name });
            message.success('删除成功');
            // 更新父级目录
            await handleTreeDataLoad({ id: selectedDirectory.parentDirId });
            setSelectedDirectory(undefined);
          } catch (error) {
            // TODO: 展示错误信息
          }
        },
      });
    } else {
      Modal.info({
        title: '删除目录',
        content: '您尚未选中目录，请选中目录后进行删除！',
      });
    }
  }, [handleTreeDataLoad, selectedDirectory]);

  const selectedKeys = useMemo(() => (selectedDirectory ? [selectedDirectory.key as number] : []), [selectedDirectory]);

  const level = useMemo(() => {
    const currentLevel = selectedDirectory?.pos.split('-').length ?? 1;

    if (directoryModal.data) {
      // 编辑时返回上一层级
      return currentLevel - 1;
    } else {
      return currentLevel;
    }
  }, [directoryModal.data, selectedDirectory]);

  const tableRequest = usePaginationTableListStyleRequest(({ current: pageNum, pageSize, params }) => {
    return postSeresFileItemList({ ...params, pageNum, pageSize });
  });

  const changeFileStatus = useCallback(async (id: number | undefined, status: FileStatus) => {
    await postSeresFileItemStatusUpdate({
      id,
      status,
    });
    message.success('操作成功');
    actionRef.current?.reload();
  }, []);

  /**
   * 下发文件
   */
  const handleFileIssueClick = useCallback(
    (file: FileManage.FileItemListVO) => {
      Modal.confirm({
        title: '下发文件',
        content: '下发后，店端将收到文件！',
        onOk: async () => changeFileStatus(file.id, FileStatus.ISSUED),
      });
    },
    [changeFileStatus]
  );

  /**
   * 撤回文件
   */
  const handleFileRecallClick = useCallback(
    (file: FileManage.FileItemListVO) => {
      Modal.confirm({
        title: '撤回文件',
        content: '撤回后，店端将无法查看文件！',
        onOk: async () => changeFileStatus(file.id, FileStatus.PENDING),
      });
    },
    [changeFileStatus]
  );

  /**
   * 作废文件
   */
  const handleFileDeprecateClick = useCallback(
    (file: FileManage.FileItemListVO) => {
      Modal.confirm({
        title: '作废文件',
        content: '作废后，店端将无法查看文件，且文件将无法再进行任何操作！',
        onOk: async () => changeFileStatus(file.id, FileStatus.DEPRECATED),
      });
    },
    [changeFileStatus]
  );

  const tableParams = useMemo<Partial<FileManage.FileItemQueryDTO>>(
    () => ({ bizType: bizType, dirId: selectedDirectory?.id }),
    [bizType, selectedDirectory]
  );

  const [fileModal, setFileModal] = useState<Pick<FileModalProps, 'data' | 'open'>>({
    open: false,
  });

  const handleFileAddClick = useCallback(() => {
    if (!selectedDirectory?.id) {
      Modal.info({
        title: '添加文件',
        content: '您尚未选中目录，请选中目录后进行添加！',
      });
    } else {
      setFileModal({
        open: true,
      });
    }
  }, [selectedDirectory]);

  const handleFileEditClick = useCallback((file: FileManage.FileItemListVO) => {
    setFileModal({
      open: true,
      data: file,
    });
  }, []);

  const handleFileModalSuccess = useCallback(() => {
    actionRef.current?.reload();
    setFileModal({ open: false });
  }, []);

  const directoryModalParentDirPath = useMemo(() => {
    if (directoryModal.data) {
      // 编辑
      return directoryModal.data.parentDirPath;
    } else {
      // 新增
      return [selectedDirectory?.parentDirPath, selectedDirectory?.name].filter(Boolean).join('');
    }
  }, [directoryModal.data, selectedDirectory]);

  const [tempDateValue, setTempDateValue] = useState<number | null>(null);
  const [editingDateKey, setEditingDateKey] = useState<number | null>(null);
  const handleDateChange = (date: moment.Moment | null) => {
    if (date) {
      setTempDateValue(date.valueOf());
    }
  };

  const handleDateBlur = async (id: number) => {
    if (tempDateValue === null) {
      setEditingDateKey(null);
      return;
    }
    try {
      await postSeresFileItemExpire({ expireTime: tempDateValue as unknown as string, id });
      actionRef.current?.reload();
      message.success('日期更新成功');
    } catch (error) {
      console.error(error);
    } finally {
      setEditingDateKey(null);
      setTempDateValue(null);
    }
  };

  const handleSwitchChange = async (id: number, checked: boolean) => {
    try {
      await postSeresFileItemPermission({ id, download: checked });
      message.success('更新成功');
      actionRef.current?.reload();
    } catch (error) {
      console.error(error);
      message.error('请求接口失败');
    }
  };

  const [personModal, setPersonModal] = useState<HeadquarterPersonModalProps>({
    open: false,
  });
  const handleEditSeresUser = useCallback((id: number) => {
    setPersonModal({
      open: true,
      data: { id },
    });
  }, []);

  const isSameLevel = (a: EventDataNode<DirectoryDataNode>, b: EventDataNode<DirectoryDataNode>) => {
    const aLevel = a.pos.split('-').length;
    const bLevel = b.pos.split('-').length;

    return aLevel === bLevel;
  };

  const isSameParent = (a: EventDataNode<DirectoryDataNode>, b: EventDataNode<DirectoryDataNode>) => {
    const aLevel = a.pos.split('-');
    const bLevel = b.pos.split('-');
    aLevel.pop();

    return aLevel.join('') === bLevel.join('');
  };

  const isDropToFirst = (a: EventDataNode<DirectoryDataNode>, b: EventDataNode<DirectoryDataNode>) => {
    const aLevel = a.pos.split('-');
    const bLevel = b.pos.split('-');
    aLevel.pop();
    return aLevel.join('') === bLevel.join('');
  };

  const handleDrop = useCallback<Required<TreeProps<DirectoryDataNode>>['onDrop']>(
    async info => {
      const { dragNode, dropPosition } = info;
      const dragParent = dragNode.parentDirId;
      // 限制同级拖拽
      const canDrop =
        (isSameLevel(info.dragNode, info.node) && info.dropToGap) ||
        (isSameParent(info.dragNode, info.node) && !info.dropToGap);
      if (!canDrop) {
        message.error('不支持跨目录层级拖拽');
        return;
      }

      // 获取当前目录的同级子节点
      const siblings = directoryChildrenMap[dragParent as number] ?? [];
      const oldIndex = siblings.findIndex(item => item.id === dragNode.id);

      // 若未找到拖拽节点，直接返回
      if (oldIndex === -1) return;

      // 生成新顺序
      const newSiblings = siblings.filter(item => item.id !== dragNode.id);

      const insertIndex = isDropToFirst(info.dragNode, info.node)
        ? 0
        : dropPosition === -1
        ? 0
        : dropPosition >= siblings.length
        ? newSiblings.length
        : dropPosition > oldIndex
        ? dropPosition - 1
        : dropPosition;

      newSiblings.splice(insertIndex, 0, dragNode);

      try {
        await postSeresFileDirectorySort({
          sortedDirIds: newSiblings.map(item => item.id as number),
        });

        // 重新加载目录
        handleTreeDataLoad({ id: dragParent });

        message.success('目录排序成功');
      } catch (error) {
        console.error('目录排序失败:', error);
        message.error('目录排序失败，请稍后重试');
      }
    },
    [directoryChildrenMap, handleTreeDataLoad]
  );

  const handleChangeSendMsg = useCallback(async (id: number, checked: boolean) => {
    try {
      await postSeresFileItemNotice({ id, result: checked });
      actionRef.current?.reload();
    } catch (e) {
      console.error(e);
    }
  }, []);

  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  useEffect(() => {
    setSelectedRowKeys([]);
  }, [selectedDirectory]);

  const [moveFileModal, setMoveFileModal] = useState<MoveFileModalProps>({
    open: false,
  });

  return (
    <PageContainer>
      <Tabs
        type="card"
        tabBarStyle={{ paddingLeft: 24, background: '#fff', marginBottom: 0 }}
        onChange={key => setBizType(Number(key))}
        activeKey={bizType.toString()}
        items={tabsWithPermission}
      />
      {tabsWithPermission?.length > 0 && (
        <ProTable
          title="文件列表"
          request={tableRequest}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys: selectedRowKeys,
            onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys),
            preserveSelectedRowKeys: true,
          }}
          rowKey={'id'}
          params={tableParams}
          actionRef={actionRef}
          onReset={() => {
            setSelectedDirectory(undefined);
            setSelectedRowKeys([]);
          }}
          columns={[
            {
              title: '序号',
              key: 'index',
              valueType: 'index',
              fixed: 'left',
            },
            {
              title: '业务类型',
              dataIndex: 'bizType',
              valueType: 'enum',
              valueMap: FILE_BIZ_TYPE_MAP,
              width: 100,
            },
            {
              title: '目录',
              dataIndex: 'parentDirPath',
              width: 200,
            },
            {
              title: '文件名称',
              dataIndex: 'name',
              search: {
                type: Input,
                fieldProps: {
                  placeholder: '请输入文件名称',
                },
              },
              width: 300,
            },
            {
              title: '状态',
              dataIndex: 'status',
              valueType: 'enum',
              valueMap: FILE_STATUS_MAP,
              search: {
                type: Select,
                name: 'statusList',
                fieldProps: {
                  options: FILE_STATUS_OPTIONS,
                  placeholder: '请选择状态',
                  mode: 'multiple',
                },
              },
            },
            {
              title: '下发范围',
              dataIndex: 'deliverType',
              render: (text, record) => {
                const editable = !(
                  [FileStatus.DEPRECATED].includes(record.status as FileStatus) ||
                  !checkPermission(fileManageListPermissions.editFile.code)
                );
                return (
                  <a
                    onClick={() => {
                      editable &&
                        setDeliverScopeModal({
                          open: true,
                          fileId: record.id as number,
                          onOk: () => {
                            setDeliverScopeModal({ open: false });
                            actionRef.current?.reload();
                          },
                        });
                    }}
                    style={{
                      color: editable ? '#1890ff' : 'rgba(0, 0, 0, 0.85)',
                      cursor: editable ? 'pointer' : 'not-allowed',
                    }}
                  >
                    <PublicTooltip placement="left" title={record.organization}>
                      {FILE_DELIVER_TYPE_MAP[text]}
                    </PublicTooltip>
                    <PublicTooltip placement="left" title={record.position}>
                      {FILE_POST_TYPE_MAP[record.postType ?? '']}
                    </PublicTooltip>
                    {record.specificUser && (
                      <PublicTooltip placement="left" title={record.specificUser}>
                        指定人员
                      </PublicTooltip>
                    )}
                  </a>
                );
              },
            },
            {
              title: '消息推送',
              dataIndex: 'isSendMsg',
              render: (value, record) => (
                <Switch
                  checked={value === YesOrNo.YES}
                  disabled={
                    [FileStatus.DEPRECATED].includes(record.status as FileStatus) ||
                    !checkPermission(fileManageListPermissions.editFile.code)
                  }
                  onChange={checked => {
                    handleChangeSendMsg(record.id as number, checked);
                  }}
                />
              ),
            },
            {
              title: '是否可下载',
              dataIndex: 'permission',
              render: (value, record) => (
                <Switch
                  checked={value === FilePermission.DOWNLOAD}
                  disabled={
                    [FileStatus.DEPRECATED].includes(record.status as FileStatus) ||
                    !checkPermission(fileManageListPermissions.editFile.code)
                  }
                  onChange={checked => handleSwitchChange(record.id as unknown as number, checked)}
                />
              ),
            },
            {
              title: '失效日期',
              dataIndex: 'expireTime',
              render: (value, record) => {
                const isEditing = editingDateKey === record.id;
                return isEditing ? (
                  <DatePicker
                    defaultValue={value ? moment(value) : undefined}
                    format="YYYY-MM-DD"
                    autoFocus
                    onChange={handleDateChange}
                    onBlur={() => handleDateBlur(record.id as unknown as number)}
                    disabledDate={date => {
                      return date.isBefore(moment().endOf('day'));
                    }}
                    showToday={false}
                  />
                ) : (
                  <a
                    onClick={() => setEditingDateKey(record.id as unknown as number)}
                    style={{
                      pointerEvents:
                        [FileStatus.DEPRECATED].includes(record.status as FileStatus) ||
                        !checkPermission(fileManageListPermissions.editFile.code)
                          ? 'none'
                          : 'auto',
                      color:
                        [FileStatus.DEPRECATED].includes(record.status as FileStatus) ||
                        !checkPermission(fileManageListPermissions.editFile.code)
                          ? 'rgba(0, 0, 0, 0.85)'
                          : '#1890ff',
                    }}
                  >
                    {value ? moment(value).format('YYYY-MM-DD') : '点击设置日期'}
                  </a>
                );
              },
            },
            {
              title: '总部人员',
              dataIndex: 'seresUser',
              width: 120,
              render: (text, record) => (
                <PublicTooltip title={text}>
                  <a
                    style={{
                      pointerEvents:
                        [FileStatus.DEPRECATED].includes(record.status as FileStatus) ||
                        !checkPermission(fileManageListPermissions.editFile.code)
                          ? 'none'
                          : 'auto',
                      color:
                        [FileStatus.DEPRECATED].includes(record.status as FileStatus) ||
                        !checkPermission(fileManageListPermissions.editFile.code)
                          ? 'rgba(0, 0, 0, 0.85)'
                          : '#1890ff',
                    }}
                    onClick={() => handleEditSeresUser(record.id as number)}
                  >
                    {text}
                  </a>
                </PublicTooltip>
              ),
            },
            {
              title: '下发时间',
              dataIndex: 'sendTime',
              valueType: 'dateTime',
              width: 180,
            },
            {
              title: '下发日期',
              dataIndex: 'sendDate',
              hideInTable: true,
              search: {
                type: DatePicker.RangePicker,
                fieldProps: {
                  disabledDate: (current: Moment) => {
                    return current.isAfter(moment().endOf('day'));
                  },
                },
              },
            },
            {
              title: '下发人',
              dataIndex: 'sendBy',
            },
            {
              title: '更新时间',
              dataIndex: 'updateTime',
              valueType: 'dateTime',
              width: 180,
            },
            {
              title: '更新人',
              dataIndex: 'updateBy',
            },
            {
              title: '操作',
              fixed: 'right',
              width: 130,
              render: (_, record) => (
                <Space wrap>
                  {record.status === FileStatus.PENDING ? (
                    <UserPermissionAuthorize {...fileManageListPermissions.issue}>
                      <a onClick={() => handleFileIssueClick(record)}>下发</a>
                    </UserPermissionAuthorize>
                  ) : null}
                  {record.status === FileStatus.ISSUED ? (
                    <UserPermissionAuthorize {...fileManageListPermissions.recall}>
                      <a onClick={() => handleFileRecallClick(record)}>撤回</a>
                    </UserPermissionAuthorize>
                  ) : null}
                  {record.status !== FileStatus.DEPRECATED ? (
                    <UserPermissionAuthorize {...fileManageListPermissions.invalid}>
                      <a onClick={() => handleFileDeprecateClick(record)}>作废</a>
                    </UserPermissionAuthorize>
                  ) : null}
                  {[FileStatus.PENDING, FileStatus.OUTDATED].includes(record.status as FileStatus) ? (
                    <UserPermissionAuthorize {...fileManageListPermissions.editFile}>
                      <a onClick={() => handleFileEditClick(record)}>编辑</a>
                    </UserPermissionAuthorize>
                  ) : null}
                  <UserPermissionAuthorize {...fileManageListPermissions.viewDetail}>
                    <Link to={`/FileManage/FileDetail/${record.id}`} target="_blank">
                      查看
                    </Link>
                  </UserPermissionAuthorize>
                  {record.status === FileStatus.ISSUED ? (
                    <UserPermissionAuthorize {...fileManageListPermissions.copyUrl}>
                      <CopyToClipboard
                        text={`${process.env.REACT_APP_FILE_PSA_URL}/preview/${record.id}`}
                        onCopy={(_, result) => {
                          if (result) {
                            message.success('复制成功');
                          }
                        }}
                      >
                        <a>复制 URL</a>
                      </CopyToClipboard>
                    </UserPermissionAuthorize>
                  ) : null}
                </Space>
              ),
            },
          ]}
          actionPosition="toolbar"
          actions={[
            {
              type: 'primary',
              children: '添加文件',
              onClick: handleFileAddClick,
              permission: fileManageListPermissions.addFile,
            },
            {
              type: 'default',
              children: '添加目录',
              onClick: handleDirectAddClick,
              permission: fileManageListPermissions.addDirectory,
            },
            {
              type: 'default',
              children: '编辑目录',
              onClick: handleDirectoryEditClick,
              permission: fileManageListPermissions.editDirectory,
            },
            {
              type: 'primary',
              danger: true,
              children: '删除目录',
              onClick: handleDirectoryDeleteClick,
              permission: fileManageListPermissions.deleteDirectory,
            },
            {
              type: 'default',
              children: '移动文件',
              onClick: () => {
                setMoveFileModal({
                  open: true,
                  data: { selectedRowKeys },
                  bizType,
                  onOk: () => {
                    setMoveFileModal({ open: false });
                    setSelectedRowKeys([]);
                    actionRef.current?.reload();
                  },
                });
              },
              disabled: selectedRowKeys.length === 0,
              permission: fileManageListPermissions.moveFile,
            },
          ]}
          tableRender={table => (
            <Row gutter={24}>
              <Col span={6} xxl={5}>
                <Card style={{ height: '100%' }}>
                  <Row className="pro-table-head">
                    <Col className="pro-table-title">文件目录</Col>
                  </Row>
                  <Spin spinning={directoryLoading}>
                    {treeData?.length ? (
                      <Tree.DirectoryTree<DirectoryDataNode>
                        onDrop={handleDrop}
                        blockNode
                        draggable={(node: DirectoryDataNode) => {
                          if (node.parentDirId === 0 && node.name === '公告') {
                            return false;
                          }
                          return true;
                        }}
                        showIcon
                        onDragStart={info => {
                          console.log('拖拽开始时节点的 classList:', info.node.className);
                          console.log('拖拽开始时节点的 style:', info.node.style);
                        }}
                        onDragOver={info => {
                          console.log('悬停时节点的 classList:', info.node.className);
                          console.log('悬停时节点的 style:', info.node.style);
                        }}
                        expandAction={false}
                        defaultExpandParent
                        selectedKeys={selectedKeys}
                        rootClassName="FileList-DirectoryTree"
                        onSelect={handleTreeNodeSelect}
                        treeData={treeData}
                        loadData={handleTreeDataLoad}
                        fieldNames={{
                          title: 'name',
                          key: 'id',
                        }}
                      />
                    ) : (
                      <Empty />
                    )}
                  </Spin>
                </Card>
              </Col>
              <Col span={18} xxl={19} style={{ height: '100%' }}>
                {table}
              </Col>
            </Row>
          )}
          search={{
            transform: values => {
              const { sendDate, ...params } = values;
              if (values.sendDate && values.sendDate[0] && values.sendDate[1]) {
                params.startTime = values.sendDate[0].startOf('day').valueOf();
                params.endTime = values.sendDate[1].endOf('day').valueOf();
              }
              return params;
            },
          }}
        />
      )}
      <DirectorModal
        {...directoryModal}
        level={level}
        bizType={bizType}
        parentDirId={selectedDirectory?.id}
        parentDirPath={directoryModalParentDirPath}
        onSuccess={handleDirectoryModalSuccess}
        onCancel={() => setDirectoryModal({ open: false })}
      />
      <FileModal
        onSuccess={handleFileModalSuccess}
        bizType={bizType}
        treeData={treeData}
        parentDirPath={[selectedDirectory?.parentDirPath, selectedDirectory?.name].filter(Boolean).join('')}
        parentDirId={selectedDirectory?.id}
        loading={modelFileLoading}
        onCancel={() => setFileModal({ open: false })}
        handleTreeNodeSelect={handleTreeNodeSelect}
        handleLoad={handleTreeDataLoad}
        {...fileModal}
      />
      <HeadquarterPersonModal
        onCancel={() => {
          setPersonModal({ open: false });
        }}
        onSuccess={() => {
          setPersonModal({ open: false });
          actionRef.current?.reload();
        }}
        {...personModal}
      />
      <MoveFileModal
        {...moveFileModal}
        bizType={bizType}
        onCancel={() => {
          setMoveFileModal({ open: false });
        }}
      />
      <DeliverScopeModal
        onCancel={() => {
          setDeliverScopeModal({ open: false });
        }}
        {...deliverScopeModal}
      />
    </PageContainer>
  );
};

export default FileList;
