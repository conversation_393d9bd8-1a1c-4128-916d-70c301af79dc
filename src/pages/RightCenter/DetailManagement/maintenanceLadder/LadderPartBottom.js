import React, { useEffect, useState } from 'react'
import { Descriptions } from 'antd';

const LadderPartBottom = (props) => {
    const { strategyConfig, index, data, ladderStyle } = props
    // 取出key值
    const { maintenanceLadderDetailItem, maintenanceLadderDetail, maintenanceLadderLimitFrame } = strategyConfig['maintenanceLadderFrame'].c
    const { ladderLimitMonth, ladderLimitYear, ladderLimitMileage } = maintenanceLadderDetailItem
    const { effectMileageFrame, fixedBeginMileage, relativeEndMileageStrategy, fixedEndMileageStrategy } = strategyConfig['effectMileageFrame'].c

    const effectMileageFrameValue = data[effectMileageFrame.value]   //权益生效里程radio的值
    const fixedBeginMileageValue = data[fixedBeginMileage.value] || 0  //权益生效里程相固定开始里程的值
    const _relativeEndMileageStrategy = data[relativeEndMileageStrategy.value]
    const _fixedEndMileageStrategy = data[fixedEndMileageStrategy.value]
    const _maintenanceLadderLimitFrame = data[maintenanceLadderLimitFrame.value]

    const _relativeTimeName = data.rTimeName
    const _rBeginMileagegName = data.rBeginMileagegName

    const ladderDetailData = data[maintenanceLadderDetail.value]

    const sumFn = (arr, field) => {
        return arr?.reduce(function (prev, cur) {
            return prev + (cur?.[field.value] ?? 0);
        }, 0);
    }

    const computedYear = (totalMonth) => {
        const _year = Math.trunc(totalMonth / 12)
        if (_year > 0) {
            return _year + '年'
        } else {
            return ''
        }
    }
    const computedMonth = (totalMonth) => {
        const _month = totalMonth % 12
        if (_month > 0) {
            return _month + '月'
        } else {
            return ''
        }
    }

    const renderLadderBeginTime = () => {
        // 阶梯开始时间为权益生效相对时间+上个阶梯配置的限制年限
        if (index === 0) {
            return _relativeTimeName || '-'
        } else {
            const totalMonth = (sumFn(ladderDetailData?.slice(0, index), ladderLimitYear) * 12 + sumFn(ladderDetailData?.slice(0, index), ladderLimitMonth))
            return _relativeTimeName ? `${_relativeTimeName}+${computedYear(totalMonth)}${computedMonth(totalMonth)}1天` : '-'
        }
    }

    const renderLadderEndTime = () => {
        //阶梯开始时间为权益生效相对时间+本次阶梯配置的限制年限
        const totalMonth = (sumFn(ladderDetailData?.slice(0, index + 1), ladderLimitYear) * 12 + sumFn(ladderDetailData?.slice(0, index + 1), ladderLimitMonth))
        if (!computedYear(totalMonth) && !computedMonth(totalMonth)) {
            return _relativeTimeName ? `${_relativeTimeName}` : '-'
        }
        return _relativeTimeName ? `${_relativeTimeName}<span class="${ladderStyle['tagStyle']} ${ladderStyle['m-l-8']}">+${computedYear(totalMonth)}${computedMonth(totalMonth)}</span>` : '-'
    }


    const renderLadderBeginMileage = () => {
        if (index === 0) {
            const obj = {
                0: '0公里',
                1: `${fixedBeginMileageValue}公里`,
                2: _rBeginMileagegName ? `${_rBeginMileagegName}` : '-'
            }
            //权益生效里程effectMileageFrameValue 0=>无此策略， 1=》固定 ，2=》 相
            return obj[effectMileageFrameValue]

        } else {
            // console.log('leicj-------------_relativeEndMileageStrategy', fixedBeginMileageValue, _relativeEndMileageStrategy, _fixedEndMileageStrategy);
            /**
             *  权益生效结束里程 1=计算到里程和  2=计算到里程策略
             *  1，阶梯开始里程则展示 权益生效开始里程+阶梯里程累计和；
             *  2，阶梯开始里程则展示 （第一阶梯展示权益生效开始里程） 其他阶梯展示前阶梯里程累计和
             */
            const calcFixedEndMileage = {
                1: `${fixedBeginMileageValue + sumFn(ladderDetailData?.slice(0, index), ladderLimitMileage) + 1}公里`,  //计算到里程和
                2: `${sumFn(ladderDetailData?.slice(0, index), ladderLimitMileage) + 1}公里`   //计算到里程策略
            }

            const calcRelativeEndMileage = {
                1: `${_rBeginMileagegName}+${sumFn(ladderDetailData?.slice(0, index), ladderLimitMileage) + 1}公里`,
                2: `${sumFn(ladderDetailData?.slice(0, index), ladderLimitMileage) + 1}公里`
            }

            const obj = {
                0: `${sumFn(ladderDetailData?.slice(0, index), ladderLimitMileage) + 1}公里`,
                1: calcFixedEndMileage[_fixedEndMileageStrategy],
                2: _rBeginMileagegName ? calcRelativeEndMileage[_relativeEndMileageStrategy] : '-'
            }
            //权益生效里程effectMileageFrameValue 0=>无此策略， 1=》固定 ，2=》 相
            return obj[effectMileageFrameValue]
        }
    }

    const renderLadderEndMileage = () => {
        // console.log('leicj-------------_relativeEndMileageStrategy1', fixedBeginMileageValue, _relativeEndMileageStrategy, _fixedEndMileageStrategy);
        /**
         *  权益生效结束里程 1=计算到里程和  2=计算到里程策略
         *  1，阶梯开始里程则展示 权益生效开始里程+阶梯里程累计和；
         *  2，阶梯开始里程则展示 （第一阶梯展示权益生效开始里程） 其他阶梯展示阶梯里程累计和
         */
        const calcFixedEndMileage = {
            1: `${fixedBeginMileageValue + sumFn(ladderDetailData?.slice(0, index + 1), ladderLimitMileage)}公里`,  //计算到里程和
            2: `${sumFn(ladderDetailData?.slice(0, index + 1), ladderLimitMileage)}公里`   //计算到里程策略
        }

        const calcRelativeEndMileage = {
            1: `${_rBeginMileagegName}<span class="${ladderStyle['tagStyle']} ${ladderStyle['m-l-8']}">+${sumFn(ladderDetailData?.slice(0, index + 1), ladderLimitMileage)}公里</span>`,
            2: `<span class=${ladderStyle['tagStyle']}>${sumFn(ladderDetailData?.slice(0, index + 1), ladderLimitMileage)}公里</span>`
        }

        const obj = {
            0: `<span class=${ladderStyle['tagStyle']}>${sumFn(ladderDetailData?.slice(0, index + 1), ladderLimitMileage)}公里</span>`,
            1: `<span class=${ladderStyle['tagStyle']}>${calcFixedEndMileage[_fixedEndMileageStrategy]}</span>`,
            2: _rBeginMileagegName ? calcRelativeEndMileage[_relativeEndMileageStrategy] : '-'
        }
        //权益生效里程effectMileageFrameValue 0=>无此策略， 1=》固定 ，2=》 相
        return obj[effectMileageFrameValue]
    }

    return (
        <Descriptions column={2}>
            {
                _maintenanceLadderLimitFrame !== 2 ?
                    <>
                        <Descriptions.Item label='阶梯开始时间'>{renderLadderBeginTime()}</Descriptions.Item>
                        <Descriptions.Item label='阶梯结束时间'><span dangerouslySetInnerHTML={{ __html: renderLadderEndTime() }}></span></Descriptions.Item>
                    </>
                    : null
            }

            {
                _maintenanceLadderLimitFrame !== 1 ?
                    <>
                        <Descriptions.Item label='阶梯开始里程'>{renderLadderBeginMileage()}</Descriptions.Item>
                        <Descriptions.Item label='阶梯结束里程'><span dangerouslySetInnerHTML={{ __html: renderLadderEndMileage() }}></span></Descriptions.Item>
                    </>
                    : null
            }
        </Descriptions>
    )
}

export default LadderPartBottom