import React, { useState, memo, useEffect, useContext } from 'react';
import { Table, Tag } from 'antd';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
import styles from '../../antd.module.less';

const effectMap = {
  1: {
    color: '#F5222D',
    name: '未生效',
  },
  2: {
    color: '#52C41A',
    name: '已生效',
  },
  3: {
    color: '#872EF1',
    name: '已使用',
  },
  4: {
    color: '#BBBBBB',
    name: '已失效',
  },
  5: {
    color: '#F87E02',
    name: '激活中',
  },
  //精品权益生效状态 ：6=》待履约  7=》已履约
  6: {
    color: '#F5222D',
    name: '待履约',
  },
  7: {
    color: '#872EF1',
    name: '已履约',
  },
  null: {
    color: '',
    name: '',
  },
};

const effectStatusMap = {
  1: {
    name: '未使用',
  },
  2: {
    name: '使用中',
  },
  3: {
    name: '已使用',
  },
  4: {
    name: '已用完',
  },
  5: {
    name: '不可用',
  },
  null: {
    name: '',
  },
};

const PERFORMANCE_STATUS = {
  Released: '已发放',
  toBeReleased: '待发放',
  Expired: '已失效',
  Processing: '处理中',
  Activated: '已激活',
  Failed: '处理失败',
};

const PublicTable = props => {
  const { dataSource } = props;

  return (
    <>
      <Table
        className={`${styles['gray-table']} basePayTable`}
        dataSource={dataSource.filter(item => Boolean(item))}
        rowKey={record => record.benefitsNo.toString()}
        size="small"
        bordered={true}
        pagination={false}
        scroll={{ x: 1840 }}
      >
        <Table.Column
          title="权益明细编码"
          key="benefitsNo"
          dataIndex="benefitsNo"
          width={220}
          fixed="left"
          render={(text, record) => {
            return (
              <div>
                <span>{text}</span>
              </div>
            );
          }}
        />
        <Table.Column
          title="权益分类"
          key="benefitsTypeName"
          dataIndex="benefitsTypeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="业务编码"
          key="businessCodeName"
          dataIndex="businessCodeName"
          width={140}
          render={text => text || '-'}
          fixed={'left'}
        />
        <Table.Column
          title="权益明细名称"
          key="benefitsName"
          dataIndex="benefitsName"
          render={text => text || '-'}
          width={200}
          fixed="left"
        />
        {/* 权益生效状态：固定=》已履约 */}
        <Table.Column
          title="权益生效状态"
          key="effect"
          dataIndex="effect"
          width={120}
          render={(text, record) => {
            const index = record?.effect || false;
            const RENDER_DOM = (
              <div style={{ fontSize: '12px', display: 'flex', alignItems: 'center' }}>
                <b
                  style={{
                    background: effectMap[index]?.color,
                    width: '6px',
                    height: '6px',
                    borderRadius: '6px',
                    marginRight: '8px',
                  }}
                ></b>
                <span style={{ color: effectMap[index]?.color }}>{effectMap[index]?.name}</span>
              </div>
            );
            return typeof index === 'number' ? RENDER_DOM : '-';
          }}
        />
        {/* 权益使用状态：固定=》已使用 */}
        <Table.Column
          title="权益使用状态"
          key="status"
          dataIndex="status"
          width={120}
          render={(text, record) => {
            const index = record?.status || false;
            if (typeof index === 'number') {
              return (
                <div style={{ fontSize: '12px', display: 'flex', alignItems: 'center' }}>
                  <span>{effectStatusMap[index]?.name}</span>
                </div>
              );
            } else {
              return '-';
            }
          }}
        />
        {/* 权益业务场景：固定=》售前场景 */}
        <Table.Column
          title="权益业务场景"
          key="businessSceneName"
          dataIndex="businessSceneName"
          width={120}
          render={text => text || '-'}
        />
        {/* 权益限制年限：固定=》永久有效 */}
        <Table.Column
          title="权益限制年限"
          key="limitTimeFrame"
          dataIndex="limitTimeFrame"
          width={120}
          className={styles['table-pad-left']}
          render={(text, record) => {
            // 固定永久有效
            let span = <span>-</span>;
            if (record.limitTimeFrame == -1) {
              span = <span>永久有效</span>;
            }
            return span;
          }}
        />
        <Table.Column
          title="权益生效时间"
          key="effectTime"
          dataIndex="effectTime"
          width={280}
          render={(text, record) => (record.effectTime ? moment(record.effectTime).format('YYYY-MM-DD HH:mm:ss') : '-')}
        />
        <Table.Column
          title="权益所属"
          key="benefitsBelongs"
          dataIndex="benefitsBelongs"
          width={140}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益额度"
          key="benefitsDiscountAmount"
          dataIndex="benefitsDiscountAmount"
          width={140}
          render={text => text || '-'}
        />
        <Table.Column
          title="权益激活时间"
          key="benefitsActiveTime"
          dataIndex="benefitsActiveTime"
          width={180}
          render={(text, record) =>
            record.benefitsActiveTime ? moment(record.benefitsActiveTime).format('YYYY-MM-DD HH:mm:ss') : '-'
          }
        />
        <Table.Column
          title="权益履行状态"
          key="benefitsPerformanceStatus"
          dataIndex="benefitsPerformanceStatus"
          width={140}
          render={text => PERFORMANCE_STATUS[text] || '-'}
        />
        <Table.Column
          title="权益履行时间"
          key="benefitsPerformanceTime"
          dataIndex="benefitsPerformanceTime"
          width={180}
          render={(text, record) =>
            record.benefitsPerformanceTime ? moment(record.benefitsPerformanceTime).format('YYYY-MM-DD HH:mm:ss') : '-'
          }
        />
        <Table.Column
          title="消费者页面的权益说明"
          key="consumerPagDesc"
          dataIndex="consumerPagDesc"
          width={160}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益说明"
          key="rightsDesc"
          dataIndex="rightsDesc"
          width={140}
          ellipsis
          render={text =>
            text ? (
              <PublicTooltip title={<div style={{ overflowY: 'auto', maxHeight: 500 }}>{text}</div>}>
                {text}
              </PublicTooltip>
            ) : (
              '-'
            )
          }
        />
        <Table.Column
          title="权益有效期"
          key="validityPeriod"
          dataIndex="validityPeriod"
          width={140}
          render={text => text || '-'}
        />
        <Table.Column
          title="活动开始时间"
          key="activityStartTime"
          dataIndex="activityStartTime"
          width={140}
          render={(text, record) => (text ? moment(Number(text)).format('YYYY-MM-DD') : '-')}
        />
        <Table.Column
          title="活动结束时间"
          key="activityEndTime"
          dataIndex="activityEndTime"
          width={140}
          render={(text, record) => (text ? moment(Number(text)).format('YYYY-MM-DD') : '-')}
        />
      </Table>
    </>
  );
};
export default memo(PublicTable);
