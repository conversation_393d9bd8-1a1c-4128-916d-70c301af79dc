/* eslint-disable no-unused-expressions */
import React, { useState, useEffect } from 'react'
import { Modal, Row, Col, Tabs, Input,Spin, message, Tree, Checkbox } from 'antd'
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import _ from 'lodash'
import './SendingRange.less'

const { TabPane } = Tabs;
const SendingRange = (props) => {
    const { onCancel, visible,Type,cb,targetList } = props
    const [loading, setLoading] = useState(false)
    const [OrgtreeData, setOrgtreeData] = useState([])
    const [tabsKey, setTabsKey] = useState('1')
    const [defaultQuery, setDefaultQuery] = useState({})
    const [orgtreeselectedKeys, setOrgtreeselectedKeys] = useState([])
    const [orgtreeSelectedRows, setOrgtreeSelectedRows] = useState([])
    const hanldeOk = (props) => {
        let data = [...orgtreeSelectedRows]
        let temp = []
        data.forEach(item=>{
            let obj = {}
            if(Type==='EDIT'){
                obj.id = item.id
            }else{
               
            }
            obj.orgId = item.key
            obj.orgName = item.title
            
            temp.push(obj)
        })
        cb(temp)
        onCancel()
    }
  
    const TabsChange = (key) => {
        setDefaultQuery({})
        setTabsKey(key)
    }
    const handleSearch = (value, type, field) => {
        let obj = {}
        obj[field] = value
        setDefaultQuery(obj)
    }
    const PersonnelDel = (row) => {
        let rows = [...orgtreeSelectedRows]
        let keys = [...orgtreeselectedKeys]
        rows = rows.filter(item => item.key !== row.key)
        keys = keys.filter(item => item !== row.key)
        setOrgtreeSelectedRows(rows)
        setOrgtreeselectedKeys(keys)
    }
   
    const getAllChild = (nodeData) =>{
        let arr = []
        arr.push(nodeData)
        if(nodeData.children && nodeData.children.length){
            const fn = (arr1) =>{
                arr1.forEach(item=>{
                    if(item.children && item.children.length){
                        arr.push(item)
                        fn(item.children)
                    }else{
                        arr.push(item)
                    }
                })
            }
            fn(nodeData.children)
        }

        return arr
    }

    const removeDuplicateObj = (arr,key) => {
        let obj = {};
        arr = arr.reduce((newArr, next) => {
          obj[next[key]] ? "" : (obj[next[key]] = true && newArr.push(next));
          return newArr;
        }, []);
        return arr;
    };

    const treeTitleClick = (nodeData) => {
        const allChild = getAllChild(nodeData)
        let newOrgtreeSelectedRows = [...orgtreeSelectedRows]
        let newOrgtreeselectedKeys = [...orgtreeselectedKeys]
        console.log(allChild)
        console.log(newOrgtreeSelectedRows)
        console.log(newOrgtreeselectedKeys)
        if(newOrgtreeselectedKeys.indexOf(nodeData.key)!==-1){    //取消
            allChild.forEach(ele => {
                newOrgtreeSelectedRows = newOrgtreeSelectedRows.filter(item => item.key !== ele.key)
                newOrgtreeselectedKeys = newOrgtreeselectedKeys.filter(item => item !== ele.key)
            })
        }else{
            newOrgtreeSelectedRows.push(...allChild)
            newOrgtreeselectedKeys.push(...allChild.map(item=>item.key))
        }
        setOrgtreeSelectedRows(removeDuplicateObj(newOrgtreeSelectedRows,'key'))
        setOrgtreeselectedKeys([...new Set(newOrgtreeselectedKeys)])
    }
    const Uncheck = (row) =>{
        console.log(row)
        let rows = [...orgtreeSelectedRows]
        let keys = [...orgtreeselectedKeys]
        if(keys.indexOf(row.key)!==-1){
            rows = rows.filter(item => item.key !== row.key)
            keys = keys.filter(item => item !== row.key)
        }else{
            rows.push(row)
            keys.push(row.key)
        }
        setOrgtreeSelectedRows(rows)
        setOrgtreeselectedKeys(keys) 
    }
    const treeTitleRender = (nodeData) => {
        return <>
            <div style={{ marginRight: '40px', flex: 1 }} >
                <Checkbox checked={orgtreeselectedKeys.indexOf(nodeData.key) > -1 ? true : false} onClick={() => treeTitleClick(nodeData)}>
                    <div onClick={()=>Uncheck(nodeData)}>{nodeData.title}</div>
                </Checkbox>
                {/* {
                    orgtreeselectedKeys.indexOf(nodeData.key)!==-1 ?
                    <div style={{width:'25px',height:'25px',display: 'inline-block'}} title='取消勾选' >
                        <CheckOutlined style={{color:'#1890ff'}} />   
                    </div>
                    :null
                } */}
            </div>
        </>
    }

    useEffect(() => {
        setLoading(true)
        if (tabsKey === '1') {
            post(allUrl.Authority.messageSendOrgList, { ...defaultQuery }).then(res => {
                if (res && res.success) {
                    let Dt = res.resp
                    setOrgtreeData(Dt)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }, [defaultQuery, tabsKey])

    useEffect(()=>{
        console.log(targetList)
            let OrgRows = [],OrgKeys = [];
            targetList.forEach(item=>{
                let obj = {
                    key:Number(item.orgId),
                    title:item.orgName,
                    id:item.orgId,
                }
                OrgRows.push(obj)
                OrgKeys.push(Number(item.orgId))
            })
            setOrgtreeSelectedRows(OrgRows)
            setOrgtreeselectedKeys(OrgKeys)
    },[targetList])
    console.log('orgtreeSelectedRows',orgtreeSelectedRows)
    console.log('orgtreeselectedKeys',orgtreeselectedKeys)
    return <Modal wrapClassName='SendingRange' title='选择规则适用的范围' visible={visible} onCancel={onCancel} onOk={hanldeOk} width={1000} maskClosable={false}>
        <Row gutter={[12]}>
            <Col span={14} className='SendingRangeLeft'>
                <Tabs defaultActiveKey="1" onChange={TabsChange} active={tabsKey}>
                    <TabPane tab="组织" key="1">
                        <Input.Search style={{ marginBottom: '16px' }} onSearch={(value) => handleSearch(value, 3, 'name')} placeholder='搜组织关键字' />
                        <Spin spinning={!OrgtreeData.length?true:false} />
                            {
                                OrgtreeData.length ?
                                    <Tree multiple={true} defaultSelectedKeys={orgtreeselectedKeys} defaultExpandedKeys={[1]} treeData={OrgtreeData} titleRender={treeTitleRender} />
                                    : null
                            }
                    </TabPane>
                </Tabs>
            </Col>
            <Col span={10} className='SendingRangeRight'>
                <div className='title'>已选择对象<a>（{orgtreeSelectedRows.length}）</a></div>
                <div className='contentBox'>
                    {
                        orgtreeSelectedRows.length ? orgtreeSelectedRows.map((item, index) => {
                            return <Row key={index} className='content_Item'>
                                <Col span={22} className='name'>{item.title}</Col>
                                <Col span={2} className='del'>
                                    <img src={require('../../../../assets/img/trash.png')} style={{ width: '20px', cursor: 'pointer' }} onClick={() => PersonnelDel(item)} />
                                </Col>
                            </Row>
                        }):null
                    }
                </div>
            </Col>
        </Row>
    </Modal>
}
export default SendingRange