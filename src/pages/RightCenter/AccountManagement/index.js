import React, { useEffect, useState, useRef } from 'react';
// import styled from 'styled-components'
import { useSelector, useDispatch } from 'react-redux';
import { changeShowRedDot } from '@/actions/normal';
import {
  Table,
  message,
  Button,
  Popconfirm,
  Input,
  Select,
  Row,
  Col,
  Divider,
  Tooltip,
  Dropdown,
  Menu,
  Modal,
} from 'antd';
import './style.less';
import tableModuleStyles from '../tableLayout.module.less';
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { roleJudgment } from '@/utils/authority';
import moment from 'moment';
import { CheckOutlined } from '@ant-design/icons';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import { setStorage, getStorage } from '@/utils/Storage';
import PublicTableQuery from '@/components/Public/PublicTableQuery';
import PublicTable from '@/components/Public/PublicTable';
import ExportModal from './ExportModal';
import AddAccountModal from './AddAccountModal';
import styles from '../antd.module.less';
import _ from 'lodash';

const getPageStorage = key => {
  let res = getStorage(window.location.hash, true);
  return res && res[key] ? res[key] : null;
};

const AccountManagement = props => {
  const tableRef = useRef();
  const dispatch = useDispatch();
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [loading, setLoading] = useState(false);
  const [dataSource, changeDataSource] = useState([]);
  const [isCatch, setisCatch] = useState(true);
  const [current, changeCurrent] = useState(
    isCatch && getPageStorage('pagination') ? getPageStorage('pagination').current : 1
  );
  const [pageSize, changePageSize] = useState(
    isCatch && getPageStorage('pagination') ? getPageStorage('pagination').pageSize : 10
  );
  const [total, changeTotal] = useState(0);
  const [tableHeight, setTableHeight] = useState(0);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [sortCodeList, setSortCode] = useState([]);
  const [ManageInterests, setManageInterests] = useState([]);
  const [DictData, setDictData] = useState([]);
  // const [allPackNameList, setAllPackNameList] = useState([])
  const [allCarMarketType, setAllCarMarketType] = useState([]);
  const [showExport, setShowExport] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [showAddAccount, setShowAddAccount] = useState(false);
  const statusMapList = [
    { name: '已交付', value: '1' },
    { name: '未交付', value: '2' },
    { name: '已退定', value: '3' },
    { name: '异常关单', value: '4' },
    { name: '已注销', value: '5' },
  ];
  const orderCreateSourceMap = [
    { name: 'DMS(VMall)', value: '1' },
    { name: 'DMS(PMall)', value: '2' },
    { name: '营销云(JKSales)', value: '3' },
    { name: '人工创建权益账户', value: '4' },
    { name: '未知', value: '5' },
  ];
  const changeShowAddAccount = val => {
    setShowAddAccount(val);
  };

  //一进来页面调用接口
  // useEffect(()=>{
  //     get(allUrl.DetailedManageInterests.getSortList).then(res => {
  //             if (res.success) {
  //                 res.resp.forEach((item, index) => {
  //                     item.key = index + 1
  //                     item.value = item.code
  //                 })
  //                 setSortCode(res.resp)
  //             } else {
  //                 message.error(res.msg)
  //             }
  //         })
  // },[])
  useEffect(() => {
    // 后端预加载
    get(allUrl.AccountManagement.getSnapshotPackName);
    // 获取车辆市场类型接口参数 EquityPackageManage.getCarMarketTypeAll
    post(allUrl.EquityPackageManage.getCarMarketTypeAll).then(res => {
      if (res.success) {
        setAllCarMarketType(res.resp[0]);
      }
    });
  }, []);

  const PageChange = (current, pageSize) => {
    changeCurrent(current);
    changePageSize(pageSize);
    if (isCatch) {
      setStorage({
        Type: window.location.hash,
        pagination: { current, pageSize },
      });
    }
  };
  const resetPage = () => {
    changeCurrent(1);
    changePageSize(10);
  };
  const onSearch = values => {
    if (values.createTime) {
      // values.beginTime = moment(values.createTime[0]).format('YYYY-MM-DD HH:mm:ss')
      // values.endTime = moment(values.createTime[1]).format('YYYY-MM-DD HH:mm:ss')
      values.createTimeBegin = moment(values.createTime[0]).format('YYYY-MM-DD HH:mm:ss');
      values.createTimeEnd = moment(values.createTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete values.createTime;
    }
    setDefaultQuery(values);
    resetPage();
  };
  const initPage = flag => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let h = 0;
    if (!flag) {
      h = winH - 47 - 54 - 345;
    } else {
      h = winH - 47 - 54 - 400;
    }
    setTableHeight(h);
  };
  useEffect(() => {
    initPage();
  }, []);

  const LookAt = record => {
    let data = {
      accountNo: record.accountNo,
      Type: 'LookAt',
      title: '权益账户信息',
    };
    UniversalOpenWindow({
      JumpUrl: '/RightCenter/AccountManagementDetail',
      data,
      history: props.history,
    });
  };
  // 批量关联
  // const onBatchHandle = () => {
  //     UniversalOpenWindow({
  //         JumpUrl: '/RightCenter/AccountManagementBatch', history:props.history
  //     })
  // }

  const filterOption = (input, option) => {
    return option?.name.includes(input);
  };
  const createMethods = [
    { name: '系统创建', value: 1 },
    { name: '手动创建', value: 2 },
    { name: '邮件创建', value: 3 },
  ];
  let searchList = [
    {
      label: '购车人手机号',
      name: 'account',
      type: 'Input',
      placeholder: '请输入购车人手机号',
      colSpan: 6,
      rules: [{ pattern: /^[0-9]*$/, message: '只支持数字，请正确输入' }],
    },
    {
      label: '车辆VIN',
      name: 'vin',
      type: 'Input',
      placeholder: '请输入车辆vin',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    //交付状态
    {
      label: '订单状态',
      name: 'accountStatus',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: statusMapList,
    },
    {
      label: '订单来源',
      name: 'orderCreateSource',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: orderCreateSourceMap,
    },

    {
      label: '车型',
      name: 'carAttr',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: DictData && DictData['equity_car_type'] ? _.cloneDeep(DictData['equity_car_type']) : [],
    },
    {
      label: '基础配置',
      name: 'carConfigName',
      type: 'Input',
      placeholder: '请输入基础配置（支持模糊搜索）',
      colSpan: 6,
    }, // 基础配置
    {
      label: '车辆销售类型',
      name: 'transferType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: [
        { name: '一手车', value: '1' },
        { name: '二手车', value: '2' },
      ],
    },
    {
      label: '车辆市场类型',
      name: 'carMarketType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      fieldNames: { value: 'code', label: 'name' },
      data: allCarMarketType || [],
    },
    { label: '权益包名称', name: 'packNoName', type: 'Input', placeholder: '请输入权益包名称', colSpan: 6 },
    { label: '权益包编码', name: 'packNo2', type: 'Input', placeholder: '请输入权益包编码', colSpan: 6 },
    {
      label: '权益包类型',
      name: 'type',
      type: 'Select',
      placeholder: '请选择权益包类型',
      colSpan: 6,
      data: DictData && DictData['equity_pay_type'] ? _.cloneDeep(DictData['equity_pay_type']) : [],
    },

    { label: '权益账户编码', name: 'accountNo', type: 'Input', placeholder: '请输入权益账户编码', colSpan: 6 },
    { label: '意向订单', name: 'bookOrderNo', type: 'Input', placeholder: '请输入意向订单号', colSpan: 6 },
    {
      label: '正式订单',
      name: 'formalOrderNo',
      type: 'Input',
      placeholder: '请输入XCO、CO开头正式订单号',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    {
      label: '华为订单',
      name: 'hwOrderNo',
      type: 'Input',
      placeholder: '请输入1H、1G、VF开头华为订单号',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    {
      label: '交付订单',
      name: 'deliverOrderNo',
      type: 'Input',
      placeholder: '请输入DO开头交付订单号',
      colSpan: 6,
      rules: [{ pattern: /^[a-zA-Z0-9]*$/, message: '只支持数字、英文，不区分大小写，请正确输入' }],
    },
    { label: '销售订单', name: 'saleOrderNo', type: 'Input', placeholder: '请输入销售订单号', colSpan: 6 },
    { label: '车联网手机号', name: 'certificationPhone', type: 'Input', placeholder: '请输入车联网手机号', colSpan: 6 },
    { label: '车主手机号', name: 'carOwnerPhone', type: 'Input', placeholder: '请输入车主手机号', colSpan: 6 },
    {
      label: '账户创建方式',
      name: 'orderSource',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: createMethods,
    },
    { label: '车主姓名', name: 'carOwnerName', type: 'Input', placeholder: '请输入车主姓名', colSpan: 6 },

    {
      label: '账户创建时间',
      name: 'createTime',
      type: 'RangePicker',
      showTime: true,
      placeholder: ['起始时间', '结束时间'],
      colSpan: 6,
      extra: (
        <div style={{ color: 'red', fontSize: '14px', marginTop: '7px' }}>创建时间可选择时间点、时间段进行查询</div>
      ),
    },
  ];
  const columns = [
    {
      title: '权益账户编码',
      dataIndex: 'accountNo',
      width: 120,
      fixed: 'left',
      render: (text, record) => {
        if (text) {
          // return text
          const res = statusMapList.filter(i => i.value == record.accountStatus)[0];
          return (
            <div>
              <div className={'mark bg' + res?.value}>
                <div className="mark-content">
                  <span>{res?.name}</span>
                </div>
              </div>
              <div>{text}</div>
            </div>
          );
        } else {
          return <div>{'——'}</div>;
        }
      },
    },

    {
      title: '购车人手机号',
      dataIndex: 'carBuyerPhone',
      width: 120,
      fixed: 'left',
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },

    {
      title: '购车人',
      dataIndex: 'carBuyerName',
      width: 120,
      fixed: 'left',
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },

    {
      title: '账户创建时间',
      dataIndex: 'createTime',
      width: 120,
      render: (text, record) => {
        if (text) {
          return text;
          // return moment(text).format('YYYY-MM-DD HH:MM:SS')
        } else {
          return <div>{'——'}</div>;
        }
      },
    },
    {
      title: '账户创建方式',
      dataIndex: 'orderSource',
      width: 120,
      render: (text, record) => {
        if (text) {
          return createMethods.filter(i => i.value == text)[0]['name'];
        } else {
          return <div>{'——'}</div>;
        }
      },
    },

    {
      title: '车型',
      dataIndex: 'carTypeName',
      width: 120,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆VIN',
      dataIndex: 'vin',
      width: 100,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆销售类型',
      dataIndex: 'transferTypeName',
      width: 120,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '基础配置',
      dataIndex: 'carConfigName',
      width: 190,
      render: (text, record) => {
        if (text) {
          return <div style={{ whiteSpace: 'nowrap' }}>{text}</div>;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '车辆市场类型',
      dataIndex: 'carMarketTypeName',
      width: 120,
      render: (text, record) => {
        if (text) {
          return <div>{text}</div>;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '订单来源',
      dataIndex: 'orderCreateSource',
      width: 120,
      render: (text, record) => {
        if (text) {
          return orderCreateSourceMap.filter(i => i.value == text)[0]['name'];
        }
        return <div>{'—'}</div>;
      },
    },
    {
      title: '意向订单号',
      dataIndex: 'bookOrderNo',
      width: 110,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '正式订单号',
      dataIndex: 'formalOrderNo',
      width: 140,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '华为订单号',
      dataIndex: 'hwOrderNo',
      width: 110,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '销售订单号',
      dataIndex: 'saleOrderNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '交付订单号',
      dataIndex: 'deliverOrderNo',
      width: 200,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '基础权益包名称',
      dataIndex: 'baseName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '基础权益包编码',
      dataIndex: 'baseNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    //  630
    {
      title: '付费权益包名称',
      dataIndex: 'payName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '付费权益包编码',
      dataIndex: 'payNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '赠送权益包名称',
      dataIndex: 'giveName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '赠送权益包编码',
      dataIndex: 'giveNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '精品权益包名称',
      dataIndex: 'boutiqueName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return '—';
        }
      },
    },
    {
      title: '精品权益包编码',
      dataIndex: 'boutiqueNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return '—';
        }
      },
    },
    {
      title: '增值权益包名称',
      dataIndex: 'valueAddName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '增值权益包编码',
      dataIndex: 'valueAddNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '驾行权益包名称',
      dataIndex: 'safeName',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },
    {
      title: '驾行权益包编码',
      dataIndex: 'safeNo',
      width: 160,
      render: (text, record) => {
        if (text) {
          return text;
        } else {
          return <div>{'—'}</div>;
        }
      },
    },

    {
      title: '操作',
      width: 100,
      fixed: 'right',
      dataIndex: 'Operation',
      render: (text, record) => (
        <>
          {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_DETAIL') ? (
            <Button type="link" size="small" onClick={() => LookAt(record)}>
              详情
            </Button>
          ) : null}
        </>
      ),
    },
  ];

  useEffect(() => {
    get(allUrl.common.entryLists, { codes: 'equity_car_type,equity_pay_type' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        for (let i in Dt) {
          Dt[i].forEach(item => {
            if (i === 'equity_pay_type') {
              item.name = item.entryMeaning.replace(/（[^）]*）/g, '');
              item.value = Number(item.entryValue);
            } else {
              item.name = item.entryMeaning;
              item.value = item.entryValue;
            }
          });
        }
        setDictData(Dt || {});

        // let Dt = res.resp[0]
        // Dt['equity_car_type'].forEach(item => {
        //     item.name = item.entryMeaning
        //     item.value = item.entryValue
        // })
        // setDictData(Dt['equity_car_type'])
      } else {
        // message.error(res.message)
      }
    });
  }, []);

  //导出弹窗
  const onExportHandle = () => {
    setShowExport(true);
  };

  const handleCancel = () => {
    setShowExport(false);
  };

  //导出弹窗提交
  const submitHandle = data => {
    console.log('minjie---submitHandle--->', data);
    setShowMessage(true);
    post(
      allUrl.AccountManagement.accountExport,
      { ...defaultQuery, ...{ fields: data } },
      { responseType: 'blob' }
    ).then(res => {
      dispatch(changeShowRedDot(true));
    });
  };

  return (
    <div className={`${tableModuleStyles.PublicList} account-PublicList`}>
      <PublicTableQuery isCatch={true} isFormDown={false} onSearch={onSearch} searchList={searchList} />
      {/* <FormQuery initPage={initPage} onSearch={onSearch} resetPage={resetPage} searchList={searchList} defaultQuery={defaultQuery} /> */}
      <div className="tableData">
        <Row className="tableTitle">
          <Col className="text" span={6} style={{ color: 'black', fontSize: 20 }}>
            权益账户列表
            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_EXPORT') ? (
              <Button style={{ marginLeft: '20px' }} type="primary" span={3} onClick={onExportHandle}>
                导出
              </Button>
            ) : null}
          </Col>
          {/* { roleJudgment(userInfo,'ACCOUNT_MANAGEMENT_BATCH') && <Col><Button type="primary" onClick={onBatchHandle}>批量关联</Button></Col>} */}
          {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_ADD') && (
            <Col>
              <Button
                type="primary"
                onClick={() => {
                  setShowAddAccount(true);
                }}
              >
                账户新增
              </Button>
            </Col>
          )}
        </Row>
        <PublicTable
          isCatch={true}
          ref={tableRef}
          type={6}
          rowSelection={false}
          defaultQuery={defaultQuery}
          url={allUrl.AccountManagement.getAccountList}
          columns={columns}
          className={styles['blue-table']}
        />
      </div>
      <ExportModal showExport={showExport} handleCancel={handleCancel} submitHandle={submitHandle} />
      <Modal
        width={420}
        open={showMessage}
        footer={null}
        centered
        closable={false}
        onCancel={() => {
          setShowMessage(false);
        }}
      >
        <div class="modal-result-box">
          <div className="modal-top">
            <div>
              <span style={{ marginRight: '5px' }}>
                <img style={{ width: '22px', height: '22px' }} src={require('@/assets/img/result-icon.png')}></img>
              </span>
              <span>文件：</span>
              <span className="bule-text">{`权益账户管理-${moment(new Date()).format('YYYYMMDDHHMMSS')}`}</span>
            </div>
            <div className="left-text">已进入下载中心</div>
          </div>
          <div className="modal-bottom">
            <Button
              className="btn"
              key="submit"
              type="primary"
              onClick={() => {
                setShowMessage(false);
              }}
            >
              我知道了
            </Button>
          </div>
        </div>
      </Modal>
      <AddAccountModal
        open={showAddAccount}
        changeShowAddAccount={changeShowAddAccount}
        onSearch={onSearch}
      ></AddAccountModal>
    </div>
  );
};
export default AccountManagement;
