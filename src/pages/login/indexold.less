.login_warp {
    height: 100%;
    background: url('../../assets/img/login_bg.jpg') no-repeat;
    background-size: 100% 100%;

    .ant-spin-nested-loading {
        height: 100%;

        .ant-spin-container {
            height: 100%;
        }
    }
   

    .login {
        display: flex;
        height: 100%;
        position: relative;

        .login_con {
            height: 570px;
            width: 880px;
            border-radius: 18px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            display: flex;

            .login_con_left {
                width: 388px;
                background: url('../../assets/img/login_logo_left.png') no-repeat;
                background-size: cover;
                border-top-left-radius: 18px;
                border-bottom-left-radius: 18px;
            }

            .login_con_right {
                flex: 1;

                .login_con_right_logo {
                    width: 296px;
                    height: 40px;
                    margin: 64px auto 38px auto;

                    // margin-top: 40px;
                    // margin-bottom: 22px;
                    img {
                        width: 100%;
                    }
                }

                .login_form {
                    width: 380px;
                    margin: 0 auto;

                    .ant-input-affix-wrapper {
                        border: 0;
                        border-bottom: solid 1px #D9D9D9;
                        border-radius: 0;

                        input {
                            box-shadow: 0 0 0 1000px white inset
                        }
                    }

                    .ant-input-affix-wrapper-focused {
                        border: 0;
                        border-bottom: solid 1px #D9D9D9;
                        box-shadow: none;
                    }

                    .login_bts {
                        display: flex;
                        justify-content: space-between;

                        .remember {
                            color: black;
                        }

                        .forget {
                            color: #1890FF;
                            cursor: pointer;
                        }
                    }

                    .submit {
                        width: 100%;
                        height: 36px;
                        border-radius: 4px;
                        background: #1890ff;
                    }

                    .ant-form-item {
                        margin-bottom: 16px;
                    }

                    .group_8 {
                        width: 368px;
                        height: 17px;
                        display: flex;
                        margin-top: 15px;
                        .image_3 {
                            width: 140px;
                            height: 1px;
                            margin-top: 8px;
                        }

                        .text_12 {
                            width: 14px;
                            height: 17px;
                            overflow-wrap: break-word;
                            color: rgba(0, 0, 0, 0.65);
                            font-size: 14px;
                            font-family: PingFangSC-Regular;
                            font-weight: NaN;
                            text-align: left;
                            white-space: nowrap;
                            line-height: 17px;
                            margin-left: 37px;
                        }

                        .image_4 {
                            width: 140px;
                            height: 1px;
                            margin: 8px 0 0 37px;
                        }
                    }

                    .group_9 {
                        background-color: rgba(12, 176, 217, 1);
                        border-radius: 4px;
                        width: 100%;
                        height: 40px;
                        margin-top: 16px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        cursor: pointer;
                            .thumbnail_5 {
                                width: 20px;
                                height: 20px;
                                margin-top: -2px;
                                margin-right: 10px;
                            }

                            .text-group_6 {
                                width: 267px;
                                height: 24px;
                                overflow-wrap: break-word;
                                color: rgba(255, 255, 255, 1);
                                font-size: 15px;
                                font-family: PingFangSC-Medium;
                                font-weight: 500;
                                text-align: left;
                                white-space: nowrap;
                                line-height: 24px;
                            }
                    }
                }

            }
        }

        .copyright_info {
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.85);
            position: absolute;
            bottom: 40px;
            width: 100%;
            text-align: center;
        }
    }
}
.ant-spin-spinning{ 
    left: 48%;
    top: 40%;
    position: fixed;
}