/*通用接口环境处理*/

const allUrl = {
  Authority: {
    phoneLogin: '/user/v1/phoneLogin', //登录
    logout: '/user/v1/logout', //登出
    kaptchaimageCode: '/user/v1/kaptcha/imageCode', //获取登陆验证码
    ticketLogin: '/user/v1/ticketLogin', // 根据票据(ticket)SSO跳转登录
    sso_logout: '/user/v1/sso/logout', //子系统注销登录,返回SSO
    MenuTreeList: '/user/v1/menu/treeList', //获取菜单树
    MenuUserList: '/user/v1/menu/userList', //获取菜单树
    saveMenu: '/user/v1/menu/save', //添加菜单
    updateMenu: '/user/v1/menu/update', //更新菜单
    deleteMenu: '/user/v1/menu/delete', //删除菜单
    updateFunction: '/user/v1/menu/updateFunction', //删除菜单
    sendSSO: '/user/v1/sso-login-auth/multi-add', // 新增sso权限
    rmSSO: '/user/v1/sso-login-auth/multi-rm', // 删除sso权限
    userInfo: '/user/v1/userInfo', //获取用户信息

    userList: '/user/v1/userList', //获取用户列表
    nonPagedList: '/user/v1/user/nonPagedList', //获取不分页当前组织用户列表
    userCreate: '/user/v1/userCreate', //新增用户
    userUpdate: '/user/v1/userUpdate', //更新用户
    userDelete: '/user/v1/userDelete', //删除用户
    userDetail: '/user/v1/userDetail', //获取用户详情
    resetPsd: '/user/v1/password/reset', //重置用户密码
    modifyPsd: '/user/v1/modify/password', //修改密码
    userStatus: '/user/v1/modify/userStatus', //启用/禁用账号
    getUserInfo: '/clue/v1/userAndOrg/getUserInfo', //获取用户扩展信息
    updPartTimePosition: '/clue/v1/userAndOrg/updPartTimePosition', //修改兼职岗位
    bindQywxUser: '/clue/v1/userAndOrg/bindQywxUser', //绑定/解绑企业微信
    userUnlock: '/user/v1/userUnlock', //登录解锁
    rePushQwUser: '/clue/v1/userAndOrg/rePushQwUser', //企业微信用户重推送
    getStoreKeyPostInterviewDetail: '/clue/v1/userAndOrg/getStoreKeyPostInterviewDetail', //获取面试详情
    setStoreKeyPostInterview: '/clue/v1/userAndOrg/setStoreKeyPostInterview', //维护门店关键岗位面试信息
    delStoreKeyPostInterview: '/clue/v1/userAndOrg/delStoreKeyPostInterview', //删除面试信息

    importExcel: '/clue/v1/userAndOrg/importExcel', //面试结果导入

    rootList: '/user/v1/organization/list', //获取组织列表
    orgPageList: '/user/v1/organization/pageList', //获取个人权限下的全部组织
    childList: '/user/v1/organization/childList', //获取组织子节点信息
    parentsList: '/user/v1/organization/list', //根据组织名称查询父子组织信息
    updateOrg: '/user/v1/organization/update', //编辑组织
    saveOrg: '/user/v1/organization/save', //添加组织
    deleteOrg: '/user/v1/organization/delete', //删除组织
    syncOrg: '/clue/v1/userAndOrg/syncOrg', //组织同步到企业微信
    transfer: '/user/v1/organization/transfer', //组织迁移
    isStore: '/store/v1/ports/isStore', //判断是否是组织是否为门店
    messageSendOrgList: '/user/v1/organization/allList', // 消息推送-获取所有组织(和个人权限无关)
    orgDetailByID: '/user/v1/organization/detail/', //获取角色id获取角色详情
    isExistOrgName: '/user/v1/organization/isExistName', //校验组织名称是否重复
    changeOrg: '/user/v1/organizationLogin', // 切换组织
    headerOrgList: '/user/v1/userOrganization', // 右上角下拉切换组织加载的组织列表
    orgTypeList: '/user/v1/organization/typeList', // 获取组织类型

    roleList: '/user/v1/role/list', //获取角色列表
    organizationRoleList: '/user/v1/role/organizationRoleList', //查询登录人组织下角色
    createRole: '/user/v1/role/create', //新增角色
    updateRole: '/user/v1/role/update', //修改角色
    deleteRole: '/user/v1/role/delete', //删除角色
    detailRole: '/user/v1/role/detail', //获取角色详情
    copyRole: '/user/v1/role/copy', //角色拷贝
    typeRoles: '/user/v1/role/typeRoles', //角色拷贝
    rolesByOrganizationIds: '/user/v1/role/rolesByOrganizationIds', //查询组织集合下角色集合

    listUrlPermission: '/v1/gateUrl/listUrlPermission', //接口权限配置查询
    addUrlPermission: '/v1/gateUrl/addUrlPermission', //接口权限配置新建
    updateUrlPermission: '/v1/gateUrl/updateUrlPermission', //接口权限配置更新
    deleteUrlPermission: '/v1/gateUrl/deleteUrlPermission', //接口权限配置删除

    getCeoUserList: '/clue/v1/userManage/getCeoUserList', //CEO账号查询
    bindUser: '/clue/v1/userManage/bindUser', //绑定CEO账号
    unBindUser: '/clue/v1/userManage/unBindUser', //解绑CEO账号
    getAllCeoCode: '/clue/v1/userManage/getAllCeoCode', //获取所有CEO账号
    importUserRelation: '/clue/v1/userManage/importUserRelation', //CEO映射关系导入
    importClueExcel: '/clue/api/ceo/importClueExcel', //CEO线索批量导入
    importPlanExcel: '/clue/api/ceo/importPlanExcel', //CEO计划批量导入
    importRecordExcel: '/clue/api/ceo/importRecordExcel', //CEO跟进记录批量导入
    getFileList: '/clue/v1/userManage/getFileList', //文件导入结果
    getQwUserList: '/clue/v1/userAndOrg/getQwUserList', //企业微信列表
    uploadUserFile: '/user/v1/common/uploadUserFile', // 上传用户相关附件通用接口
    modifyIDNum: '/user/v1/modify/idNum', // 修改用户身份证号
    storeuserList: '/store/v1/storeUser/getStoreUserList', // 获取门店列表
    exportStoreUser: '/store/v1/storeUser/exportStoreUser', // 导出门店员工
    exportQwClockDaily: '/store/v1/qwClock/exportQwClockDaily', //导出员工打卡昨日数据
    exportQwClockMonth: '/store/v1/qwClock/exportQwClockMonth', //导出员工打卡月度数据
  },
  orderTimeOut: {
    OvertimeOrderList: '/scrms/orderTimeOut/query', //获取超时返利订单
    OvertimeOrderGetDetail: '/scrms/orderTimeOut/getDetail', //获取超时返利订单明细
    OvertimeOrderAdd: '/scrms/orderTimeOut/add', //增加返利订单
    OvertimeOrderEditor: '/scrms/orderTimeOut/editor', //编辑返利订单
    OvertimeOrderDownLoad: '/scrms/orderTimeOut/downLoad', //模板下载
    OvertimeOrderAdds: '/scrms/orderTimeOut/adds', //批量增加返利和返利设置弹框确定按钮
    OvertimeOrderGetCheck: '/scrms/orderTimeOut/getCheck', //点击增加流水信息的该订单当天是否 成功增加过流水信息
    OvertimeOrderexcelImport: '/scrms/orderTimeOut/excelImport', //返利补贴导入
    excelImportOther: '/scrms/orderTimeOut/excelImportOther', //其他订单导入
    deleteOrder: '/scrms/orderTimeOut/deleteOrder', //删除
    exportAllOrder: '/scrms/orderTimeOut/exportAllOrder', //全部导出
    exportDetail: '/scrms/orderTimeOut/exportDetail', //返利明细导出
  },
  ClueManagement: {
    getDict: '/clue/dict/v1/getDict', //获取字典接口
    addClue: '/clue/v1/addClue', //新增线索
    updateClue: '/clue/v1/plan/updateClue', //销售线索编辑
    updateManagerClueInfo: '/clue/v1/clueManage/updateManagerClueInfo', //店长线索编辑
    getClueDetail: '/clue/v1/plan/getClueDetail', //线索详情
    assignBatch: '/clue/v1/assignBatch', //线索分配
    moveBatch: '/clue/v1/moveBatch', //线索转移
    getFollowRecordList: '/clue/v1/plan/getFollowRecordList', //跟进记录列表
    getManagerClueList: '/clue/v1/clueManage/getManagerClueList', //店长-线索列表
    getSaleClueList: '/clue/v1/clueManage/getSaleClueList', //销售-线索列表
    exportClue: '/clue/v1/clueManage/exportClue', //店长-导出
    // 异地线索
    saveClueObj: '/clue/v1/distribution/saveClueObj', //添加线索
    getClueObj: '/clue/v1/distribution/getClueObj', //详情查看
    queryClueList: '/clue/v1/distribution/queryClueList', //列表查询
    updateClueObj: '/clue/v1/distribution/updateClueObj', //线索编辑
    deleteClueList: '/clue/v1/distribution/deleteClueList', //批量/单个删除
    distributionList: '/clue/v1/distribution/distributionList', //批量/单个分配
    exportClueExcel: '/clue/v1/distribution/exportClueExcel', //excel导出
    importClueDistributionExcel: '/clue/v1/distribution/importClueDistributionExcel', //线索导入
    getRegionList: '/clue/v1/distribution/getRegionList', //获取省市数据
    getCityDealer: '/clue/v1/distribution/getCityDealer', //根据意向省份/城市查询门店
    refreshClueOrderStatus: '/clue/v1/distribution/refreshClueOrderStatus', //订单状态刷新
    getCarConfig: '/lead/v1/carSku/getCarConfig', //车辆配置信息查询
    invalidStoreClue: '/clue/v1/distribution/invalidStoreClue', //线索失效
    //原始线索
    queryLeadList: '/lead/background/queryLeadList', //原始线索列表
    carTypes: '/lead/v1/commonConfig/carTypes', //获取意向车型下拉框
    channels: '/lead/v1/commonConfig/channels', //获取线索渠道下拉框
    // 唯一线索
    queryOnlyLeadList: '/lead/background/queryOnlyLeadList', //唯一线索列表（厂端）
    queryLeadDealerList: '/lead/background/queryLeadDealerList', //唯一线索列表（店端）
    findDealersByKeywords: '/lead/background/findDealersByKeywords', //根据关键词查询门店信息
    findSalesmanByKeywords: '/lead/background/findSalesmanByKeywords', //根据关键词查询销售信息
    batchDistributionToDealer: '/lead/background/batchDistributionToDealer', //线索批量分配到店
    batchDistributionLead: '/lead/manager/batchDistributionLead', //批量线索分配销售
    updateLead: '/lead/background/updateLead', //线索编辑
    getLeadInfo: '/lead/background/getLeadInfo', //线索详情
    batchtTansferLeadToSale: '/lead/manager/batchtTansferLeadToSale', //转移
    findSalesmanList: '/lead/background/findSalesmanList', //查询当前登录人所在门店所有的在职销售
    queryLeadPlanList: '/lead/background/queryLeadPlanList', //跟进计划列表
    reviewVictoryDetails: '/lead/background/plan/reviewVictoryDetails', //战赢审核详情
    approveVictory: '/lead/background/plan/approveVictory', //战赢确认
  },
  StoreManage: {
    list: '/store/v1/ports/list', //门店列表
    syncAllStore: '/store/v1/ports/syncAllStore', // 手动异步更新门店列表
    detail: '/store/v1/ports/detail', //获取门店详情
    updateOrganization: '/store/v1/ports/updateOrganization', //门店添加组织
    updateOrganizationInfo: '/store/v1/ports/updateOrganizationInfo', //编辑门店信息
    cityList: '/store/v1/city/list', //获取城市列表
    statistics: '/store/v1/ports/statistics', //获取门店统计
    listByOrganizationIds: '/store/v1/ports/listByOrganizationIds', //根据组织ID查询门店信息
    template: '/store/v1/ports/download/template', //模版下载
    getStoreUserList: '/clue/v1/userAndOrg/getStoreUserList', //获取门店用户列表
    exportStoreQuery: '/store/v1/ports/exportStoreQuery', //获取门店导出条数信息
    exportStore: '/store/v1/ports/exportStore', //导出门店信息
    getStoreInfo: '/store/v1/ports/getStoreInfo/', //根据门店简称/门店编号关键字查询门店数据
    getUserTrainingAndCertification: '/store/v1/userTraining/get', //根据用户id获取配信和认证信息
    addUserTrainingAndCertification: '/store/v1/userTraining/save', //维护人员认证信息和培训记录
    importExcel: '/clue/v1/storeUser/importExcel', //门店人员认证等级和培训记录导入
    getFileList: '/clue/v1/storeUser/getFileList', //门店导入结果查询
    exprotStoreUser: '/store/v1/storeProject/exprotStoreUser', //门店人员认证等级导出
    ptpConfigList: '/store/v1/position/ptpConfigList', // 兼岗规则 list
    ptpConfig: '/store/v1/position/ptpConfig', // 编辑兼岗规则
    getAllowPTP: '/store/v1/position/getAllowPTP', // 获得允许的兼岗列表
    updPTP: '/store/v1/position/updPTP', // 更新兼岗
    /**门店项目管理 */
    getProjectManagerStoreList: '/store/v1/storeProject/getProjectManagerStoreList', //项目经理获取门店项目管理列表
    getStoreProjectList: '/store/v1/storeProject/getStoreProjectList', //网络部获取门店项目管理列表
    getFunnelFigure: '/store/v1/storeProject/getFunnelFigure', //获取漏斗图
    getPieChart: '/store/v1/storeProject/getPieChart', //获取饼图
    uploadFile: '/store/v1/common/uploadFile', //附件上传
    getStoreUserAndLevel: '/store/v1/storeProject/getStoreUserAndLevel', //获取门店人员以及认证等级
    getStoreUserAndLevel1: '/store/v1/storeProject/getStoreUserAndLevel_copy', //获取门店人员以及认证等级新
    savePorjectDetails: '/store/v1/storeProject/savePorjectDetails', //保存门店项目（存草稿，更新）
    submitPorjectDetails: '/store/v1/storeProject/submitPorjectDetails', //提交门店项目
    getProjectDetails: '/store/v1/storeProject/getProjectDetails', //门店项目详情
    getProjectManagerList: '/store/v1/storeProject/getProjectManagerList', //获取项目经理
    getBindableStoreList: '/store/v1/storeProject/getBindableStoreList', //获取项目经理可绑定的门店列表
    getManagerBindStoreList: '/store/v1/storeProject/getManagerBindStoreList', //获取某一个项目经理绑定的门店列表
    bindStoreManager: '/store/v1/storeProject/bindStoreManager', //维护项目经理和门店绑定关系
    getProjectProgress: '/store/v1/storeProject/getProjectProgress', //获取门店项目进度
    acceptanceProject: '/store/v1/storeProject/acceptanceProject', //项目验收
    updateSubmitCreateTime: '/store/v1/storeProject/updateSubmitCreateTime', //修改完成时间
    getProjectProgressBar: '/store/v1/storeProject/getProjectProgressBar', // 项目进度条
    getCertifiedUserInfo: '/store/v1/storeProject/getCertifiedUserInfo', // 培训认证通过信息查看
    getTrainStandard: '/store//v1/storeProject/getUserTrainInfo', // 培训认证通过人员培训情况查询
    setTrainStandard: '/store/v1/storeProject/setTrainPassRequire', // 培训认证通过要求设置
    createStoreUser: '/store/v1/storeUser/createUser', // 门店员工管理-新建用户
    updateStoreUser: '/store/v1/storeUser/updateUser', // 门店员工管理-更新用户
    getBigAreaName: '/store/v1/storeUser/getBigAreaName', // 获取大区
    getMiddleAreaName: '/store/v1/storeUser/getMiddleAreaName', // 获取省区
    getStoreUserInfo: '/store/v1/storeUser/getStoreUserInfo', // 门店员工管理-获取门店人员详情
    tryGeneratorStaffNumber: '/store/v1/storeUser/tryGeneratorStaffNumber', // 尝试生成该用户的工号
    staffExit: '/store/v1/storeUser/staffExit', // 员工离职
    updatePhone: '/store/v1/storeUser/updatePhone', // 更改手机号
    sendSms2UpdatePhone: '/store/v1/storeUser/sendSms2UpdatePhone', // 发送短信更改手机号
    lmsStaffLevel: 'store/v1/storeUser/lmsStaffLevel', // 从lms获取员工培训等级
  },
  truckLogistics: {
    getCarriageList: '/dray/admin/carriageManage/getCarriageList', //待发运列表/历史承运单列表
    getCarriageObj: '/dray/admin/carriageManage/getCarriageObj', //待发运列表/历史承运单列表 -- 详情
    export: '/dray/admin/carriageManage/export', //导出
    searchDriver: '/dray/admin/v1/transfer/searchDriver', //后台拆单转派时模糊搜索司机
    searchTrailer: '/dray/admin/v1/transfer/searchTrailer', //后台拆单转派时模糊搜索板车
    transfer: '/dray/admin/v1/transfer/transfer', //拆单转派
    getTransferLog: '/dray/admin/v1/transfer/getTransferLog', //后台拆单转派日志
    getClockListByVinAndCarriageId: '/dray/admin/carriageManage/getClockListByVinAndCarriageId', //后台打卡记录
    // 物流/板车/服务商管理
    carriageDriverPage: '/dray/admin/carriageDriver/page', //司机列表分页查询
    carriageDriverAdd: '/dray/admin/carriageDriver/add', //司机列表新增
    carriageDriverUpdate: '/dray/admin/carriageDriver/update', //司机列表更新
    carriageDriverDelete: '/dray/admin/carriageDriver/delete', //删除司机
    carriageDriverImportExcel: '/dray/admin/carriageDriver/importExcel', //导入司机
    CarriageTrailerPage: '/dray/admin/carriageTrailer/page', //板车列表分页查询
    carriageTrailerAdd: '/dray/admin/carriageTrailer/add', //板车列表新增
    carriageTrailerUpdate: '/dray/admin/carriageTrailer/update', //板车列   表更新
    carriageTrailerDelete: '/dray/admin/carriageTrailer/delete', //删除板车
    carriageTrailerImportExcel: '/dray/admin/carriageTrailer/importExcel', //导入板车
    getAllLogisticsProvider: '/dray/admin/v1/transfer/getAllLogisticsProvider', //转派查询服务商
    getDefaultLogisticsProvider: '/dray/admin/v1/transfer/getDefaultLogisticsProvider', //获得当前登录人对应的默认物流商
    forceFinishQuery: '/dray/admin/v1/forceFinish/forceFinishQuery', //后台操作强制送达时查询对应车辆
    forceFinish: '/dray/admin/v1/forceFinish/forceFinish', //后台操作强制送达
    importArchiveVin: '/dray/admin/v1/forceFinish/importArchiveVin', // 强制送达导入归档 vin
    // 服务商
    logisticsProviderPage: '/dray/admin/CarriageLogisticsProvider/page', //服务物流商分页查询
    logisticsProviderAdd: '/dray/admin/CarriageLogisticsProvider/add', //服务物流商新增
    logisticsProviderUpdate: '/dray/admin/CarriageLogisticsProvider/update', //服务物流商更新
    logisticsProviderList: '/dray/admin/CarriageLogisticsProvider/list', //查询服务商List
    processSave: '/dray/admin/carriageFenceConfig/save', //设置电子围栏
    getScope: '/dray/admin/carriageFenceConfig/getScope', //查询电子围栏
    carriageEntryList: '/dray/admin/CarriageEntryAdmin/page', // 进场单列表
    updateEntryTimeById: '/dray/admin/CarriageEntryAdmin/updateEntryTimeById', // 进场单更新
    setLimitEntry: '/dray/admin/CarriageEntryAdmin/setLimitEntry', // 进场单-设置限制进场时间状态 0:不限制 1：限制
    getLimitEntry: '/dray/admin/CarriageEntryAdmin/getLimitEntry', // 进场单-回显限制进场时间状态 0:不限制 1：限制
    carriageBoardList: '/dray/admin/CarriageBoardAdmin/page', // 组板单列表
    getOSSurl: '/dray/admin/carriageManage/getOssUrl', // 查看url文件
    getLog: '/dray/admin/log/page', // 日志
    forceBoardById: '/dray/admin/CarriageBoardAdmin/forceBoardById', // 组组单-强制组组By组组单Id
    carriageBoardAdminExport: '/dray/admin/CarriageBoardAdmin/export', // 组板单导出
    carriageEntryAdminExport: '/dray/admin/CarriageEntryAdmin/export', // 进场单导出
    carriageManagePeShow: '/dray/admin/carriageManage/peShow', // 运单查看手机号
    carriageBoardAdminPeShow: '/dray/admin/CarriageBoardAdmin/peShow', // 组板单查看手机号
    carriageEntryAdminPeShow: '/dray/admin/CarriageEntryAdmin/peShow', // 进场单查看手机号
    carriageDriverPeShow: '/dray/admin/carriageDriver/peShow', // 用户查看手机号
    carriageLogisticsProviderPeShow: '/dray/admin/CarriageLogisticsProvider/peShow', // 查看司机手机号
    confirmLogPeShow: '/dray/admin/log/peShow', // 补偿送达日志查看手机号
    transferPeShow: '/dray/admin/v1/transfer/peShow', // 转派日志查看手机号
    unlock: '/dray/admin/carriageDriver/unlock', // 解锁用户登录
  },
  common: {
    dictUpdateCode: '/common/v1/dict/updateCode', //更新字典表
    dictList: '/common/v1/dict/list', //数据字典分页查询
    dictDetail: '/common/v1/dict/detail', //获取详情
    entryList: '/common/v1/dict/entry/list', //根据字典类型查询字典值列表
    entryLists: '/common/v1/dict/entry/listByCodes', //根据字典类型查询多个字典值列表
    powerModelList: '/equity/v1/carConfig/getCarPowerModelConfig', //动力方式字典
    uploadFile: '/equity/v1/uploadFile', //无需额外参数文件上传
  },
  OrderManage: {
    getOrderList: '/clue/v1/order/backend/getOrderList', //大定订单列表查询
    getOrderDetail: '/clue/v1/order/backend/getOrderDetail', //大定订单详情
    getOrderStat: '/clue/v1/order/backend/getOrderStat', //订单列表页统计
  },
  MessageManage: {
    getMessageList: '/clue/v1/portalMessage/getMessageList', //消息列表
    getUser: '/store/v1/user/getUser', //获取消息列表账号
    uploadMedia: '/clue/v1/portalMessage/uploadAttach', //附件上传
    uploadCoverImg: '/clue/v1/portalMessage/uploadMedia', //上传封面图
    saveMessage: '/clue/v1/portalMessage/saveMessage', //保存消息
    delMessage: '/clue/v1/portalMessage/delMessage', //删除消息
    getMessageDetail: '/clue/v1/portalMessage/getMessageDetail', //消息详情
    sendMessage: '/clue/v1/portalMessage/sendMessage', //发送消息
    withdrawMessage: '/clue/v1/portalMessage/recallMessage', //撤回消息
    getReceiptInfo: '/clue/v1/portalMessage/getReadList', //查看回执名单
    exportReadList: '/clue/v1/portalMessage/exportReadList/', // 导出回执
    getAlluser: '/store/v1/user/getAllUser', // 获取所有用户
    exportReceipt: '/clue/v1/portalMessage/exportReadList', //已读和未读名单导出
    userExportTemplate: '/store/v1/portalMessage/userExportTemplate', // 导出人员信息模版
    userExportFromData: '/store/v1/portalMessage/userExportFromData', // 导出人员信息（目前用于导出失败数据）
    userImportFile: '/store/v1/portalMessage/userImportFile', // 导入人员信息
    dealerExportTemplate: '/store/v1/portalMessage/dealerExportTemplate', // 导出门店信息模版
    dealerExportFromData: '/store/v1/portalMessage/dealerExportFromData', // 导出门店信息（目前用于导出失败数据）
    dealerImportFile: '/store/v1/portalMessage/dealerImportFile', // 导入门店信息
    // 老接口(还有部分在用)
    exportReceipt2: '/clue/v1/qwAgentMessage/exportReceipt', //已读和未读名单导出
    getMessageList2: '/clue/v1/qwAgentMessage/getMessageList', //消息列表
    uploadMedia2: '/clue/v1/qwAgentMessage/uploadMedia', //附件上传
    uploadCoverImg2: '/clue/v1/qwAgentMessage/uploadCoverImg', //上传封面图
    saveMessage2: '/clue/v1/qwAgentMessage/saveMessage', //保存消息
    delMessage2: '/clue/v1/qwAgentMessage/delMessage', //删除消息
    getMessageDetail2: '/clue/v1/qwAgentMessage/getMessageDetail', //消息详情
    sendMessage2: '/clue/v1/qwAgentMessage/sendMessage', //发送消息
    withdrawMessage2: '/clue/v1/qwAgentMessage/withdrawMessage', //撤回消息
    getReceiptInfo2: '/clue/v1/qwAgentMessage/getReceiptInfo', //查看回执名单
  },
  // 企业微信
  WeCom: {
    getPageCustomer: '/clue/v1/qw/customer/pageCustomer', //获取客户列表
    updateSalesOrder: '/clue/v1/salesOrder/update', //更新交付单状态
    // getCustomerSaleOrder:'/clue/v1/qw/customer/getCustomerSaleOrder',    //获取客户交付信息
    getCustomerSaleOrder: '/qw/v1/qwCustomer/getCustomerSaleOrder', //获取客户交付信息
    exprotNoGroup: '/qw/v1/salesOrder/exprotNoGroup', //未建群明细导出
    // exportGroup:'/qw/v1/salesOrder/exportGroup',    //群明细导出
    exportReceipt: '/clue/v1/qwAgentMessage/exportReceipt', //已读和未读名单导出
    qwGroupList: '/clue/v1/qwGroup/page', //管家群查询
    qwGroupMemberList: '/clue/v1/qwGroupMember/page', //管家群成员查询
    checkDeliveryOrderNoForBackend: '/clue/v1/qw/customer/checkDeliveryOrderNoForBackend', //后台-校验交付单号
    saveAffirmForBackend: '/clue/v1/qw/customer/saveAffirmForBackend', //后台-保存客户详情信息
    getAffirmForBackend: '/clue/v1/qw/customer/getAffirmForBackend', //后台-获取客户认证信息
    checkPhoneAndCustomerTypeForBackend: '/clue/v1/qw/customer/checkPhoneAndCustomerTypeForBackend', // 后台-校验手机号和用户类型,返回值为错误提示信息,为 null 时表示无错误
    storeStatistical: '/clue/v1/qw/noGroup/storeStatistical', //昨日用户中心未建群排行榜
    userStatistical: '/clue/v1/qw/noGroup/userStatistical', //昨日交付专员未建群排行榜
    pageUserStatistical: '/qw/v1/noGroup/pageUserStatistical', //昨日交付专员未建群列表
    pageStoreStatistical: '/clue/v1/qw/noGroup/pageStoreStatistical', //昨日用户中心未建群列表
    qwStoreGroupStatisticPage: '/qw/v1/qwStoreGroupStatistic/page', //昨日用户中心建群排行榜
    qwGroupStatisticPage: '/qw/v1/qwGroupStatistic/page', //建群统计、交付建群趋势图
    pageStoreDeliverOrder: '/clue/v1/qw/noGroup/pageStoreDeliverOrder', //用户中心未建群排行榜-交付单 废弃
    pageUserDeliverOrder: '/clue/v1/qw/noGroup/pageUserDeliverOrder', //交付专员未建群排行榜-交付单 废弃
    pageDeliverOrder: '/qw/v1/noGroup/pageDeliverOrder', //未建群排行榜(个人,中心)-查询交付单
    queryNotIncludeLog: '/clue/v1/qwOperationLog/queryNotIncludeLog', //查询不纳入操作日志
    queryAffirmLog: '/clue/v1/qwOperationLog/queryAffirmLog', //查询客户认定日志
    qwOvertimeNoticeSave: '/qw/v1/qwOvertimeNotice/batchSave', //保存超时提醒规则
    qwOvertimeNoticePage: '/qw/v1/qwOvertimeNotice/page', //查询超时提醒规则

    qwSpecialOvertimeNoticePage: '/qw/v1/qwSpecialOvertimeNotice/pageRule', //查询特殊超时提醒规则
    updateRule: '/qw/v1/qwSpecialOvertimeNotice/updateRule', //更新特殊超时规则
    addRule: '/qw/v1/qwSpecialOvertimeNotice/addRule', //增加特殊超时规则
    deleteRule: '/qw/v1/qwSpecialOvertimeNotice/deleteRule', //删除特殊超时规则
    exportArchiveStaff: '/qw/v1/archive/exportArchiveStaff', //导出会话存档权限人员列表

    queryGroupNameChangeLog: '/clue/v1/qwOperationLog/queryGroupNameChangeLog', //查询群名变更日志
    queryOwnerChangeLog: '/clue/v1/qwOperationLog/queryOwnerChangeLog', //查询群主变更日志
    dealerStatisticDetail: '/qw/v1/qwChatStatistic/dealerStatisticDetail', // 用户中心详情统计 默认7天
    exportYesterdayDealer: '/qw/v1/qwChatStatistic/exportYesterdayDealer', // 昨日门店统计excel下载
    yesterdayDealerStatistic: '/qw/v1/qwChatStatistic/yesterdayDealerStatistic', // 昨日用户中心统计
    nationTable: '/qw/v1/qwChatStatistic/nationTable', // 全国指标表
    getAllMiddleArea: '/qw/v1/qwChatStatistic/getAllMiddleArea', // 获得当前所选区域
    mainChart: '/qw/v1/qwChatStatistic/mainChart', // 回话轮统计大图
    getAreaRankDataByDate: '/qw/v1/qwChatStatistic/getAreaRankDataByDate', // 根据日期查询省区排名列表和数据
    getBigAreaRankDataByDate: '/qw/v1/qwChatStatistic/getBigAreaRankDataByDate', // 根据日期查询大区排名列表和数据
    qwChatMsgStaffPage: '/qw/v1/chatMsg/staffPage', // 聊天记录后台员工分页搜索
    qwChatMsgStaffLatestList: '/qw/v1/chatMsg/staffLatestList', // 聊天记录获取员工对应同事列表
    qwChatMsgCustomerLatestList: '/qw/v1/chatMsg/customerLatestList', // 聊天记录获取员工对应客户列表
    qwChatMsgGroupLatestList: '/qw/v1/chatMsg/groupLatestList', // 聊天记录获取员工对应群聊列表
    qwChatMsgSearchGroup: '/qw/v1/chatMsg/groupList', // 关键字模糊搜索群聊的聊天记录
    qwChatMsgSearchStaff: '/qw/v1/chatMsg/staffList', // 关键字精确搜索与同事聊天记录
    qwChatMsgCustomer: '/qw//v1/chatMsg/customerList', // 关键字精确搜索与客户聊天记录
    qwChatMsgSlideChat: '/qw/v1/chatMsg/slideChat', // 单聊会话聊天记录
    qwChatMsgGroupChat: '/qw/v1/chatMsg/slideGroupChat', // 群聊会话聊天记录
    qwCheckChatAgree: '/qw//v1/chatMsg/checkChatAgree', // 查询回话存档状态
    qwChatMsgSildeChatByTime: '/qw/v1/chatMsg/slideChatByTimeRange', // 单聊会话聊天记录按时间筛选
    qwChatMsgGroupChatByTime: '/qw/v1/chatMsg/slideGroupChatByTimeRange', // 群聊会话聊天记录按时间筛选
    qwGetOssUrlByFileId: '/qw/v1/chatMsg/getOssUrlByFileId', // 获取 fileId 对应的 ossUrl
    qwBigAreaStc: '/qw/v1/qwStoreGroupStatistic/pageBigArea', // 交付单-大区维度统计
    qwProvinceStc: '/qw/v1/qwStoreGroupStatistic/pageProvince', // 交付单-省区维度统计
    qwGroupExport: '/qw/v1/qwGroupStatistic/exportPage', // 建群统计导出
    qwsalesOrderExportGroup: '/qw/v1/salesOrder/exportGroup', // 客户建群明细导出
    importSpecialFilingExcel: '/clue/v1/salesOrder/importSpecialFilingExcel', //客户特殊备案导入
    getSpecialFilingResult: '/clue/v1/salesOrder/getSpecialFilingResult', //客户特殊备案导入结果查询
    exportPageBigArea: '/qw/v1/qwStoreGroupStatistic/exportPageBigArea', // 建群统计大区维度导出
    exportPageProvince: '/qw/v1/qwStoreGroupStatistic/exportPageProvince', // 建群统计省区维度导出
    yesterDaystoreCenterExport: '/qw/v1/qwStoreGroupStatistic/exportPage', // 昨日用户中心建群排行榜导出
    exportNationTableData: '/qw/v1/qwChatStatistic/exportNationTableData', // 聊天记录导出全国数据
    exportAreaRankDataByDate: '/qw/v1/qwChatStatistic/exportAreaRankDataByDate', // 聊天记录导出省区数据
    exportBigAreaRankDataByDate: '/qw/v1/qwChatStatistic/exportBigAreaRankDataByDate', // 聊天记录导出大区数据
    exportDealerStc: '/qw/v1/qwChatStatistic/exportDealerStatisticDetail', // -用户中心详情统计 默认7天_导出
    updateSalesOrder: '/qw/v1/salesOrder/update', // 更新交付单信息
    exportStoreChatData: '/qw/v1/qwChatStatistic/exportStoreChatData', // 导出用户中心响应数据

    searchOvertimeChatRound: '/qw/v1/chatMsg/searchOvertimeChatRound', // 超时会话检索
    slideChatRoundChat: '/qw/v1/chatMsg/slideChatRoundChat', // 会话轮聊天消息检索
    exportOvertimeDetail: '/qw/v1/chatMsg/exportOvertimeDetail', // 超时检索超时明细导出
    getMediaByMsgId: '/qw/v1/qwChatDataMedia/getByMsgId', // 根据 msgId 获取多媒体文件 url
    getMediaUrlByFileId: '/qw/v1/qwChatDataMedia/getMediaUrlByFileId', // 根据 FileId 获取多媒体文件 url

    NightSearchOvertimeChatRound: 'qw/v1/chatKFMsg/searchOvertimeChatRound',
    NightSlideChatRoundChat: 'qw/v1/chatKFMsg/slideChatRoundChat',
    NightgetMediaUrlByFileId: 'qw/v1/chatKFMsg/getMediaUrlByFileId',

    queryAccountList: '/qw/v1/qwCustomerService/queryAccountList', //查询账号列表
    uploadCoverImg: '/clue/v1/qwAgentMessage/uploadCoverImg', //上传封面
    servicerTree: '/qw/v1/qwCustomerService/servicerTree', //获取所有的接待人员树
    addAccount: '/qw/v1/qwCustomerService/addAccount', //添加客服账号
    queryAccountInfo: '/qw/v1/qwCustomerService/queryAccountInfo', //查询客服账号详情
    updateAccountInfo: '/qw/v1/qwCustomerService/updateAccountInfo', //更改客服账号
    updateReception: '/qw/v1/qwCustomerService/updateReception', //更改客服账号（接待人员）
    updateTime: '/qw/v1/qwCustomerService/updateTime', //更改客服账号（接待时间）
    updateAccountOther: '/qw/v1/qwCustomerService/updateAccountOther', //更改客服账号（其他）
    delAccount: '/qw/v1/qwCustomerService/delAccount', //删除客服账号

    qwKfSync: '/qw/v1/qwCustomerService/qwKfSync', // 同步客服账号
    queryCustomerQuestion: '/qw/v1/qwKfIndex/queryCustomerQuestion', // 查询问题
    exportCustomerQuestion: '/qw/v1/qwKfIndex/exportCustomerQuestion', // 导出问题
    sessionKpi: '/qw/v1/qwKfIndex/sessionKpi', // 会话kpi
    sessionKpiHours: '/qw/v1/qwKfIndex/sessionKpiHours', // 会话kpi根据小时分
    sessionKpiDay: '/qw/v1/qwKfIndex/sessionKpiDay', // 会话kpi根据日期分组
    servicerPerfor: '/qw/v1/qwKfIndex/servicerPerfor', // 客服绩效
    exportServicerPerfor: '/qw/v1/qwKfIndex/exportServicerPerfor', // 客服绩效导出
    querySession: '/qw/v1/qwKfSession/querySession', // 查询接待记录
    queryMessage: '/qw/v1/qwKfSession/queryMessage', // 查询对话消息
    queryMessageMedia: '/qw/v1/qwKfSession/queryMessageMedia', // 查询媒体文件的oss地址

    saleQuestion: '/store/ai/saleQuestion/page',
    allLabel: '/store/ai/saleQuestion/allLabel',
    ownerList: '/store/ai/saleQuestion/ownerList',
    saveMark: '/store/ai/saleQuestion/mark',
  },
  // 权益包管理
  EquityPackageManage: {
    queryEquityPack: '/equity/v1/packManage/queryEquityPack', // 获取权益包列表
    downEquityPack: '/equity/v1/packManage/downEquityPack', // 下架
    publishEquityPack: '/equity/v1/packManage/publishEquityPack', // 权益包发布
    updateEquityPack: '/equity/v1/packManage/updateEquityPack', //更新权益包
    draftEquityPack: '/equity/v1/packManage/draftEquityPack', //权益包保存草稿
    getEquityPack: '/equity/v1/packManage/getEquityPack', // 获取权益包
    queryDetailRelation: '/equity/v1/detailManage/queryDetailRelation', // 权益包明细关联查询
    createAndPublish: '/equity/v1/packManage/createAndPublish', // 编辑发布权益包
    getCarType: '/equity/v1/packManage/getCarType', //获取全部车型

    getCarConfig: '/equity/v1/packManage/getCarConfigNew', //获取全部车型配置
    // getCarConfig:'/equity/v1/packManage/getCarConfig',//获取全部车型配置

    getCarMarketTypeAll: '/equity/v1/carMarketType/getCarMarketTypeAll', //获取车辆市场类型下拉菜单
    packageExport: '/equity/v1/excel/packExcelExport', // 导出
    ableToDelete: '/equity/v1/packManage/ableToDelete', //查询权益包能否删除
    delete: '/equity/v1/packManage/delete', //权益包删除
    getDetailLadderInfo: '/equity/v1/detailManage/getDetailLadderInfo', //根据权益明细编码查询阶梯明细
    detailVersionCheck: '/equity/v1/packManage/detailVersionCheck', //检查权益包内权益明细是否是最新版本，如果不是则返回最新版本明细
    getObjByDetailNoAndVersion: '/equity/v1/detailManage/getObjByDetailNoAndVersion', //根据权益明细编码和版本查找明细详情
    queryEffectBasePack: '/equity/v1/packManage/queryEffectBasePack', // 根据车型配置和市场类型查询生效的基础权益包
  },
  // 权益明细管理
  DetailedManageInterests: {
    queryDetailList: '/equity/v1/detailManage/queryDetailList', // 列表
    getSortList: '/equity/v1/packManage/getSortList', //分类
    getTypeList: '/equity/v1/packManage/getTypeList', // 权益类别接口是
    getDetailObj: '/equity/v1/detailManage/getDetailObj', // 获取权益明细详情
    getDetailInfo: '/equity/v1/detailManage/getDetailInfo', // 获取前权益明细详情（延保）
    saveEquityDetail: '/equity/v1/detailManage/saveEquityDetail', // 权益明细保存
    updateEquityDetail: '/equity/v1/detailManage/updateEquityDetail', // 权益明细修改
    createCode: '/equity/v1/packManage/createCode', // 生成编码
    getDetailName: '/equity/v1/detailManage/getDetailName', // 获取所有权益明细名称
    addSort: '/equity/v1/packManage/addSort', // 添加权益分类
    getAllDealerList: '/equity/v1/packManage/getAllDealerList', // 获取全部门店
    getRegionList: '/equity/v1/region/getRegionList', // 获取省市区
    detailExcelExport: '/equity/v1/excel/detailExcelExport', // 导出
    queryDetailPartList: '/equity/v1/detailManage/queryDetailPartList', // 权益明细配件查询
    queryExtendDetailPartList: '/equity/v1/detailManage/queryExtendDetailPartList', // 延保权益明细配件查询
    getSnapshotDetailObj: '/equity/v1/detailManage/getSnapshotDetailObj', // 权益账户跳转明细

    //删除一下

    ableToDelete: '/equity/v1/detailManage/ableToDelete',
    canDelete: '/equity/v1/detailManage/delete/',
    // 根据业务编码code查询业务编码
    queryByBusinessCode: '/equity/v1/businessCode/queryByBusinessCode',
    getAllBusinessCode: '/equity/v1/businessCode/getAll', // 查询全部业务编码
  },
  //权益账户管理
  AccountManagement: {
    getAccountList: '/equity/v1/account/getAccountList', //权益账户列表
    getExactAccountList: '/equity/v1/account/getExactAccountList', //根据条件精准查询用户列表信息 -400专用
    getAccountObj: '/equity/v1/account/getAccountObj', //权益账号详情
    getAccountPackObj: '/equity/v1/account/getAccountPackObj', //权益包基础信息
    // getAccountLogList:'/equity/v1/account/getAccountLogList',//履约记录
    getSnapshotPackName: '/equity/v1/packManage/getSnapshotPackName', //获取快照权益包名称
    getAccountPurchasedList: '/equity/v1/account/getAccountPurchasedList', //核销信息查询
    getPurchasedObj: '/equity/v1/account/getPurchasedObj', //获取核销详情
    updateAccountObj: '/equity/v1/account/updateAccountObj', //权益账户变更
    getSnapshotPackObj: '/equity/v1/account/getSnapshotPackObj', //获取快照权益包权益明细
    relationEquityPack: '/equity/v1/account/relationEquityPack', //权益包关联
    getAccountPartList: '/equity/v1/account/getAccountPartList', //配件列表
    getLog: '/equity/v1/equityLog/getLog', //操作历史记录
    getAccountOwnerList: '/equity/v1/account/getAccountOwnerList', //车主任期标签叶查询
    accountUploadFile: '/equity/v1/account/uploadFile', // 权益账户赠送权益信息上传附件
    deleteUploadFile: '/equity/v1/account/deleteFile', // 权益账户赠送权益信息删除已上传附件
    accountExport: '/equity/v1/excel/accountExcelExport', //导出
    batchRelationEquityPack: '/equity/v1/account/batchRelationEquityPack', //批量关联保存
    getAccountListForRel: '/equity/v1/account/getAccountListForRel', //批量关联查询权益账户
    updateDeliverData: params => {
      //修改订单交付时间
      return `/equity/v1/account/${params.accountNo}/orders`;
    },
    updateRightDetail: params => {
      //变更账户权益明细接口
      return `/equity/v1/account/${params.accountNo}/benefits/${params.detailNo}/details`;
    },
    unbindRightPackage: params => {
      //权益账户解除权益包接口
      return `/equity/v1/account/${params.accountNo}/benefits/packs`;
    },
    getBenefits: params => {
      //账户权益统计接口
      return `/equity/v1/account/statistics/${params.accountNo}/benefits`;
    },
    deactivation: params => {
      //账户注销接口
      return `/equity/v1/account/${params.accountNo}/deactivation`;
    },
    rollback: params => {
      //账户还原接口
      return `/equity/v1/account/${params.accountNo}/rollback`;
    },
    addByExcel: '/equity/v1/account/addByExcel', //新增账户第二步校验接口
    addAccount: '/equity/v1/account/add', //新增账户第三步执行
    getAccountPartListCount: '/equity/v1/account/getAccountPartListCount', //权益账户配件总数
    getAccountHaveDetailObj: '/equity/v1/account/getAccountHaveDetailObj', //账户绑定权益原账户查询接口
    getAccountEquityLadderDetailList: '/equity/v1/account/getAccountEquityLadderDetailList', //账户阶段保养详细信息
    selectAccountPreEquityExtended: '/equity/v1/account/selectAccountPreEquityExtended', //取延保的前权益信息
    getAccountPartDetail: '/equity/v1/account/getAccountPartDetail', //查询账户配件详情
    getExternalBenefits: '/equity/v1/account/getExternalBenefits', //外埠权益查询接口
  },
  //车辆配置维护
  CarConfigMaintain: {
    getCarConfigs: '/equity/v1/carConfig/getCarConfigs', //车辆配置列表
    getCarConfigObj: '/equity/v1/carConfig/getCarConfigObj', // 获取车辆配置详情
    saveCarConfig: '/equity/v1/carConfig/saveCarConfig', // 车辆配置保存
    updateCarConfig: '/equity/v1/carConfig/updateCarConfig', // 车辆配置修改
  },
  //业务编码维护
  BusinessCodeMaintain: {
    queryBusinessCodeList: '/equity/v1/businessCode/queryBusinessCodeList', //业务编码列表
    getBusinessCodeDetail: '/equity/v1/businessCode/getBusinessCodeDetail', // 获取业务编码详情
    saveBusinessCode: '/equity/v1/businessCode/saveBusinessCode', // 业务编码保存
    updateBusinessCode: '/equity/v1/businessCode/updateBusinessCode', // 业务编码修改
    generateCode: '/equity/v1/businessCode/generateCode', //生成业务编码
  },
  //车主活动
  OwnerActivity: {
    securitList: '/clue/v1/coupon/pageCoupon',
  },
  //配件管理
  PartsManagement: {
    getPartInfoList: '/equity/v1/partsInfo/getPartInfoList', //配件列表
    getPartInfoDetail: '/equity/v1/partsInfo/getPartInfoDetail', // 配件详情
    getCustomerPartList: '/equity/v1/partsInfo/customer-paid-part-info', //保外配件列表
  },
  //车辆市场类型
  CarMarketType: {
    saveCarMarketType: '/equity/v1/carMarketType/saveCarMarketType', //创建保存
    updateCarMarketType: '/equity/v1/carMarketType/updateCarMarketType', //编辑保存
    getCarMarketTypeDetail: '/equity/v1/carMarketType/getCarMarketTypeDetail', //编辑带的数据
    getCarMarketTypeList: '/equity/v1/carMarketType/getCarMarketTypeList', //列表
  },
  //精品物料
  BoutiqueMaterial: {
    save: '/equity/v1/boutiqueMaterial/save', //保存精品物料
    queryList: '/equity/v1/boutiqueMaterial/queryList', //查询精品物料列表
    publish: '/equity/v1/boutiqueMaterial/publish', //上架
    down: '/equity/v1/boutiqueMaterial/down', //下架
    selectList: '/equity/v1/boutiqueMaterial/selectList', //获取创建精品权益时需要的下拉列表
  },
  //下载中心
  DownloadCenter: {
    queryExcelList: '/equity/v1/excel/queryExcelList',
  },
  //数据导入
  ExcelImport: {
    queryExcelTable: '/equity/v1/importExcelForTableDetail', // 查询
    uploadAppendix: '/equity/v1/importVinPayAccountExcelForTable', // 上传附件
  },
  // 闪电交付
  Lightning: {
    flashSkuConfig: '/clue/flash/flashSkuConfig/query', // sku配置信息查询
    getLockCarList: '/clue/flash/lock-car/getLockCarList', // 锁车列表
    getLockCarHistoryList: '/clue/flash/lock-car/getLockCarHistoryList', // 锁车变更历史记录
    unlockCar: '/clue/flash/lock-car/unlockCar', // 解锁
  },
  // 合作伙伴
  Partner: {
    // 合作伙伴
    partnerList: '/partner/v1/partner/getPartnerList', // 合作伙伴列表带分页
    getAllPartner: '/partner/v1/partner/getAllPartner', // 全量返回合作伙伴
    showPlaintextInfo: '/partner/v1/partner/showPlaintextInfo', // 查看明文详情
    getPartnerInfo: '/partner/v1/partner/getPartnerInfo', // 合作伙伴详情
    setPartner: '/partner/v1/partner/setPartner', // 添加/编辑合作伙伴
    // 合作伙伴员工
    partnerStaffList: '/partner/v1/staff/page', // 合作伙伴员工列表
    addPartnerStaff: '/partner/v1/staff/add', //新增合作伙伴员工
    getPartnerStaffDetail: '/partner/v1/staff/getDetail', // 获取合作伙伴员工详情
    getPlaintextDetailById: '/partner/v1/staff/getPlaintextDetailById', // 查看合作伙伴员工手机号
    importPartnerStaffExcel: '/partner/v1/staff/importExcel', // 导入合作伙伴员工
    exportPartnerStaffExcel: '/partner/v1/staff/exportStaff', // 导出合作伙伴员工
    updatePartnerStaff: '/partner/v1/staff/update', // 更新合作伙伴员工信息
  },
  // LBS
  LBS: {
    orgTree: '/store/v1/lbs/organizationTree', // 赛力斯组织树
    searchStoreByName: '/store/v1/lbs/getStoreList', // 查询门店
    queryAddress: '/lbs/v1/lbsAddress/query', // 查询门店经纬度
    updateAddress: '/lbs/v1/lbsAddress/update', // 更新门店经纬度
  },
};

export default allUrl;
