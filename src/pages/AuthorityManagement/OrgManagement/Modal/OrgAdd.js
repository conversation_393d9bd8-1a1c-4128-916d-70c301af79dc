import React from 'react'
import { Modal, message, Form, Input, Button, Spin, Steps, Tree, Result, Select, Row, Col, Checkbox } from 'antd'
import allUrl from '../../../../utils/url'
import { post, get } from '../../../../utils/request'
import { connect } from 'react-redux'
import { GetMenuList } from '../../../../actions/async'
import DebounceSelect from '@/components/Public/DebounceSelect'
import './OrgAdd.less'

const { Step } = Steps;
const { Option } = Select;

class OrgAdd extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {
            MentData: {},
            loading: false,
            btnLoading: false,
            current: 0,
            treeData: [

            ],
            treeselectedKeys: [],
            treeSelectedRows: [],
            OrgName: '',
            nextNum: 0,
            checkedList: [],
            indeterminate: false,
            checkAll: false,
            plainOptions: [],
            type: '2',
            storeOptions: [],
            typeList:[]
        }
        this.MenuID = ''
        this.ParentID = ''
    }
    formRef = React.createRef();
    handleOk = () => {
        const { onCancel } = this.props
        this.formRef.current.validateFields().then(values => {
            if (values) {
                let data = {
                    MenuID: this.MenuID,
                    ParentID: this.ParentID,
                    ParentName: values.ParentName,
                    MenuName: values.MenuName,
                    Url: values.Url,
                    Icon: values.Icon,
                    ComponentUrl: values.ComponentUrl,
                    Remark: values.Remark,
                    OrderNo: values.OrderNo,
                }
                post(allUrl.common.SaveMenu, data).then(res => {
                    if (res.code === 200) {
                        message.success(res.message)
                        onCancel()
                        this.props.dispatch(GetMenuList())
                    } else {
                        // message.error(res.message)
                    }
                })
            }
        })
    }

    handleCancel = () => {
        const { current } = this.state
        if (current === 2) {
            this.props.onCancel()
        } else {
            this.props.onCancel()
        }
        this.props.cb()
    }
    Del = () => {

    }

    ininForm = () => {
        const { treeSelectedRows } = this.props
        setTimeout(() => {
            this.formRef.current.setFieldsValue({
                ParentName: treeSelectedRows[0].title
            })
        }, 200)

    }
    next = async (num) => {
        const { treeSelectedRows } = this.state
        if (num === 2) {
            this.formRef.current.validateFields()
                .then(values => {
                    console.log(values)
                    let data = {
                        name: values.type == '1' || values.type == '4' ? values.name.label : values.name,
                        parentId: treeSelectedRows[0].key,
                        levelId: treeSelectedRows[0].levelId,
                        type: values.type,
                        code: values.code,
                        roleIds: values.roleIds
                    }
                    this.setState({
                        btnLoading: true
                    })
                    post(allUrl.Authority.isExistOrgName, { name: values.type == '1' || values.type == '4' ?values.name.label : values.name,parentId: treeSelectedRows[0].key }).then(result=>{
                        if(result.success){
                            if(result.resp[0]){
                                return message.error('组织名称重复！')
                            }
                            post(allUrl.Authority.saveOrg, data).then(res => {
                                if (res.success) {
                                    message.success(res.msg)
                                    this.setState((prevState) => ({
                                        current: prevState.current + 1,
                                    }))
                                } else {
                                    // message.error(res.msg)
                                }
                            })
                        }else{
                            // message.error(result.msg)
                        }
                        this.setState(({
                            btnLoading: false
                        }))
                    })
                })
                .catch(error => {
                    console.log(error, '错误信息')
                })
        } else {
            this.setState((prevState) => ({
                current: prevState.current + 1,
            }))
        }
    }

    prev = () => {
        this.setState((prevState) => ({
            current: prevState.current - 1,
            treeSelectedRows: [], treeselectedKeys: [],
            type: '2'
        }))
    }
    updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }

            if (node.children) {
                return { ...node, children: this.updateTreeData(node.children, key, children) };
            }

            return node;
        });
    }
    treeSelect = (selectedKeys, selectedNodes, event) => {
        this.setState({ treeselectedKeys: selectedKeys, treeSelectedRows: selectedNodes.selectedNodes })
    }
    OrgNameChange = (e) => {
        this.setState({ OrgName: e.target.value })
    }
    lookAt = () => {
        const { treeselectedKeys } = this.state
        this.props.onCancel()
        this.props.cb()
    }

    orgTypeChange = (value) => {
        this.setState({ type: value }, () => {
            this.formRef.current.resetFields(['name', 'code'])
        })
    }

    onCheckAllChange = (e) => {
        const { plainOptions } = this.state
        const checkedList = plainOptions.map(item => item.id)

        this.setState({
            indeterminate: false,
            checkAll: e.target.checked
        }, () => {
            this.formRef.current.setFieldsValue({
                roleIds: e.target.checked ? checkedList : []
            })
        })
    }

    CheckboxGroupChange = (list) => {
        const { plainOptions } = this.state
        const checkedList = plainOptions.map(item => item.id)
        this.setState({
            indeterminate: !!list.length && list.length < checkedList.length,
            checkAll: list.length === checkedList.length
        })
    }

    handleChange = (option) => {
        if (option && option.value) {
            this.formRef.current.setFieldsValue({
                code: option.value
            })
        } else {
            this.formRef.current.resetFields(['code'])
        }
    }
    handleChangePartner = (option) => {
        if (option && option.value) {
            this.formRef.current.setFieldsValue({
                code: option.value
            })
        } else {
            this.formRef.current.resetFields(['code'])
        }
    }
    render() {
        const { visible, title } = this.props
        const { loading, current, treeData, treeSelectedRows, checkAll, indeterminate, plainOptions, type, storeOptions,typeList } = this.state
        const layout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 18 },
        };
        const steps = [
            {
                title: '选择上级组织',
                content: treeData && treeData.length ? <Tree defaultExpandedKeys={[1]} treeData={treeData} onSelect={this.treeSelect} /> : <div></div>,
            },
            {
                title: '添加下级组织',
                content: <Form
                    {...layout}
                    name="vertical"
                    onFinish={this.onFinish}
                    ref={this.formRef}
                >
                    <Row>
                        <Col span={12}>
                            <Form.Item
                                label="上级组织名称"
                                name="parentName"
                                initialValue={treeSelectedRows.length ? treeSelectedRows[0].title :null}
                            >
                                <Input placeholder='自动带出' disabled />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="组织类型"
                                name="type"
                                rules={[{ required: true, message: '请选择组织类型！' }]}
                                initialValue={'2'}
                            >
                                <Select placeholder='请选择' onChange={this.orgTypeChange}>
                                    {/* <Option value={'1'} disabled={treeSelectedRows.length && treeSelectedRows[0].type === 1 ? true : false}>门店</Option>
                                    <Option value={'2'}>其他</Option> */}
                                    {
                                        typeList.map((item,index)=><Option key={index} value={String(item.type)} disabled={item.type ==1 &&  treeSelectedRows.length && treeSelectedRows[0].type === 1 ? true : false}>{item.name}</Option>)
                                    }
                                </Select>
                            </Form.Item>
                        </Col>
                        {
                            type == '1' ?
                                <Col span={12}>
                                    <Form.Item
                                        label="组织名称"
                                        name="name"
                                        rules={[{ required: true, message: '请填写组织名称！' }]}
                                    >
                                        <DebounceSelect
                                            showSearch
                                            labelInValue
                                            allowClear
                                            placeholder={'请输入门店简称/门店编号后选择'}
                                            fetchOptions={this.getStoreList}
                                            onChange={this.handleChange}
                                        />
                                    </Form.Item>
                                </Col>
                                : type == '4' ?  <Col span={12}>
                                <Form.Item
                                    label="组织名称"
                                    name="name"
                                    rules={[{ required: true, message: '请填写组织名称！' }]}
                                >
                                    <DebounceSelect
                                        showSearch
                                        labelInValue
                                        allowClear
                                        placeholder={'请输入合作伙伴简称/合作伙伴编号后选择'}
                                        fetchOptions={this.getPartnerList}
                                        onChange={this.handleChangePartner}
                                    />
                                </Form.Item>
                            </Col>
                                 : <Col span={12}>
                                 <Form.Item
                                     label="组织名称"
                                     name="name"
                                     rules={[{ required: true, message: '请填写组织名称！' }]}
                                 >
                                     <Input autoComplete="off" allowClear onChange={this.OrgNameChange} placeholder='请输入' />
                                 </Form.Item>
                             </Col>
                                
                        }
                        {
                            type === '1' ?
                                <>
                                    <Col span={12}>
                                        <Form.Item
                                            label="门店编号"
                                            name="code"
                                            rules={[{ required: false, message: '请填写门店编号！' }]}
                                        >
                                            <Input autoComplete="off" allowClear disabled placeholder='自动带出' />
                                        </Form.Item>
                                    </Col>
                                    <Col span={24}>
                                        <Col span={24} style={{ position: 'absolute', left: 104 }}>
                                            <Checkbox indeterminate={indeterminate} onChange={this.onCheckAllChange} checked={checkAll}>
                                                全选
                                            </Checkbox>
                                        </Col>
                                        <Form.Item
                                            label="配置角色"
                                            name="roleIds"
                                            rules={[{ required: true, message: '请选择角色名称！' }]}
                                            labelCol={{ span: 3 }}
                                            wrapperCol={{ span: 21 }}
                                            className='roles'
                                        >
                                            <Checkbox.Group style={{ width: '100%' }} onChange={this.CheckboxGroupChange}>

                                                <Row>
                                                    {
                                                        plainOptions.map((item, index) => {
                                                            return <Col key={item.id} span={6}>
                                                                <Checkbox value={item.id}>{item.roleName}</Checkbox>
                                                            </Col>
                                                        })
                                                    }
                                                </Row>
                                            </Checkbox.Group>
                                        </Form.Item>
                                    </Col>
                                </> 
                                : type === '4' ? 
                                <>
                                <Col span={12}>
                                    <Form.Item
                                        label="合作伙伴编号"
                                        name="code"
                                        rules={[{ required: false, message: '请填写门店编号！' }]}
                                    >
                                        <Input autoComplete="off" allowClear disabled placeholder='自动带出' />
                                    </Form.Item>
                                </Col>
                            </> 
                                : null
                        }
                    </Row>
                </Form>,
            },
            {
                title: '完成',
                content: <Result
                    status="success"
                    title="操作成功"
                    subTitle="您已完成组织添加"
                    extra={[
                        <Button key='1' type="primary" onClick={() => this.lookAt()}>
                            查看
                        </Button>,
                    ]}
                />
            },
        ];

        return (
            <Modal
                visible={visible}
                title={title}
                onOk={this.handleOk}
                onCancel={this.handleCancel}
                footer={false}
                width={900}
                maskClosable={false}
                centered={true}
                keyboard={false}
                className='OrgAdd'
            // bodyStyle={{ padding: '50px' }}

            >
                <Spin spinning={loading}>
                    <Steps current={current}>
                        {steps.map(item => (
                            <Step key={item.title} title={item.title} />
                        ))}
                    </Steps>
                    <div className="steps-content">{steps[current].content}</div>
                    <div className="steps-action">
                        {(current === 0) && (
                            <Button type="primary" onClick={() => this.next(1)} disabled={!treeSelectedRows.length} >
                                下一步
                            </Button>
                        )}
                        {(current === 1) && (
                            <Button type="primary" loading={this.state.btnLoading} onClick={() => this.next(2)} >
                                下一步
                            </Button>
                        )}
                        {/* {current === steps.length - 1 && (
                                    <Button type="primary" onClick={() => message.success('Processing complete!')}>
                                        完成
                                    </Button>
                                )} */}
                        {current === 1 && (
                            <Button style={{ margin: '0 8px' }} onClick={() => this.prev()}>
                                上一步
                            </Button>
                        )}
                    </div>
                </Spin>
            </Modal>
        )
    }
    componentDidMount() {
        this.getOrgData()
        this.getRoleList()
        this.getOrgType()
    }
    getOrgData = () => {
        post(allUrl.Authority.orgPageList).then(res => {
            if (res.success) {
                this.setState({ treeData: res.resp })
            } else {
                // message.error(res.msg)
            }
        })
    }

    getRoleList = () => {
        get(allUrl.Authority.typeRoles, { type: 1 }).then(res => {
            if (res.success) {
                this.setState({ plainOptions: res.resp })
            }
        })
    }

    getStoreList = (value) => {
        return get(allUrl.StoreManage.getStoreInfo + value).then(res => {
            if (res.success) {
                return res.resp
            } else {
                return []
            }
        })
    }
    getPartnerList = (value) => {
        return post(allUrl.Partner.getAllPartner, {input: value}).then(res => {
            if (res.success && res.resp.length) {
                const data = res.resp.map((item, index) => ({
                    dealerCode: item.partnerCode,
                    name: item.partnerName,
                    id: index
                }));
                return data
            } else {
                return []
            }
        })
    }
    getOrgType = () =>{
        get(allUrl.Authority.orgTypeList).then(res=>{
            if(res.success){
                let Dt = res.resp
                this.setState({typeList:Dt})
            }
        })
    }

}
export default connect()(OrgAdd)