import React from 'react'
import {<PERSON>,message,<PERSON><PERSON>,<PERSON>con<PERSON>rm,Input,Row,Col,Divider ,Toolt<PERSON>} from 'antd'
import {connect} from 'react-redux'
import {get,post} from '../../../utils/request'
import allUrl from '../../../utils/url'
import {PlusOutlined} from '@ant-design/icons';
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
import {roleJudgment} from '@/utils/authority'
import './index.less'
import '../index.less'
const {Search} = Input
class RoleManagement extends React.Component{
    constructor(props){
        super(props)
        this.state = {
            dataSource:[],
            title:'',
            TabHeight:0,
            selectedRowKeys:[],
            selectedRows:[],
            pageSize:10,
            pageIndex:0,
            current:1,
            total:0,
            defaultQuery:{
                params:{}
            }
        }
        this.UserID = ''
    }

    GetData = () =>{
        const {defaultQuery,pageSize,current} = this.state
        post(allUrl.Authority.listUrlPermission,{...defaultQuery,pageSize,current}).then(res=>{
            if(res.success){
                this.setState({
                    dataSource:res.resp.length ? res.resp[0].list :[],
                    total:res.resp.length ? res.resp[0].pagination.total :0,
                })
            }else{
                // message.error(res.msg || '获取列表信息失败！')
            }
        })
    }
    PageChange=(current,pageSize)=>{ this.setState({current:current,pageSize},()=>{
        this.GetData()
    }); }

    InitPage = () =>{
        let WinHeight = document.documentElement.clientHeight;
        this.setState({TabHeight:WinHeight-410}); 
    }
    RowAdd = () =>{
        let data = {
            id:0,
            type:'Add',
            title:'添加接口'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/InterfaceAuthority',data,history:this.props.history
        })
    }
    RowEdit = (record) =>{
        let data = {
            id:record.id,
            type:'Edit',
            data:record,
            title:'编辑接口'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/InterfaceAuthority',data,history:this.props.history
        })
    }
    RowDetail = (record) =>{
        let data = {
            id:record.id,
            type:'LookAt',
            title:'接口详情'
        }
        UniversalOpenWindow({
            JumpUrl:'/AuthorityManagement/InterfaceAuthority',data,history:this.props.history
        })
    }
    RowDel = (record) =>{
        get(allUrl.Authority.deleteUrlPermission,{id:record.id}).then(res=>{
            if(res.success ){
                message.success(res.msg || '删除成功！')
                this.GetData()
            }else{
                // message.error(res.msg || '删除失败！')
            }
        })
    }
    renderOperation = (text,record)  =>{
        const {userInfo} = this.props
        return <div style={{color:'#1890ff'}}>
            {
                roleJudgment(userInfo,'PERM_URL_PERMISSION_EDIT') ?
                [
                    <span key={1} style={{cursor:'pointer'}} onClick={()=>this.RowEdit(record)}>编辑</span>,
                    <Divider key={2} type="vertical" />
                ]:null
            }
            {
                roleJudgment(userInfo,'PERM_URL_PERMISSION_DELETE') ?
                <Popconfirm onConfirm={()=>this.RowDel(record)} title='确定删除吗？' okText='确定' cancelText='取消'>
                    <span style={{cursor:'pointer'}}>删除</span>
                </Popconfirm>
                :null
            }
        </div>
    }
    ModalInfo = (data) =>{
        console.log(data)
    }
    onSearch = (value) =>{
        const {defaultQuery} = this.state
        defaultQuery.params.url = value
        this.setState({defaultQuery},()=>{
            this.GetData()
        })
    }
    
    render(){
        const {dataSource,TabHeight,pageSize,total,current} = this.state
        const {userInfo} = this.props
        const InforData= {
            rowKey:record => record.id,
            bordered:true,
            dataSource,
            scroll: { x:980},
            columns:[
                // { title: '序号', dataIndex: 'index',width:80,render:(text,record,index)=><div style={{textAlign:'center'}}>{ index+1}</div>},
                { title: '接口ID',dataIndex: 'id',width:100},
                { title: '接口地址', dataIndex: 'url', width: 300},
                // { title: '接口描述', dataIndex: 'description', width: 80},
                { title: '请求方法', dataIndex: 'requestMethod', width: 100},
                { title: '所属角色', dataIndex: 'role', width: 300,render:text=><Tooltip placement="topLeft" title={text}>{text}</Tooltip>},
                { title: '操作', width: 80,fixed:'right',dataIndex:'Operation',render:(text,record)=>this.renderOperation(text,record)}
            ],
            pagination:{
                pageSize:pageSize,
                onChange:this.PageChange,
                current:current,
                total:total,
                showTotal:()=>`共${total}条，${pageSize}条/页`,
                showSizeChanger:true,
                showQuickJumper:true,
                onShowSizeChange:this.PageChange,
            }
        };
        return(
            <div className='UserManagement PublicList'>
                <Row className='queryArea'>
                    {
                        roleJudgment(userInfo,'PERM_URL_PERMISSION_ADD') ?
                            <Col className='add' style={{marginRight:'16px'}}><Button onClick={()=>this.RowAdd()} icon={<PlusOutlined />}>添加接口</Button></Col>
                        :''
                    }
                    <Col className='search'><Search placeholder="搜接口地址关键字" allowClear onSearch={this.onSearch} /></Col>
                </Row>
                <Row className='tableTiele'>接口权限列表</Row>
                <div className='tableData'>

                <Table {...InforData} />
                </div>
            </div>
        )
    }
    componentDidMount(){
        this.InitPage()
        this.GetData()
    }
}

export default connect(({ common }) => {
    const { userInfo } = common
    return { userInfo}
})(RoleManagement)
