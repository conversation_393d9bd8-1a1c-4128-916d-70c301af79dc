
import React, { useEffect, useState, useRef, forwardRef } from 'react'
import { useSelector } from 'react-redux'
import { Table, message, Tabs ,Divider,Popconfirm, Button, Modal, Tooltip, Input, Tree, Checkbox, TimePicker, Select, Radio, Space, Upload, Row, Col} from 'antd'
import { EditOutlined, UserOutlined, LoadingOutlined, PlusOutlined, DoubleLeftOutlined } from '@ant-design/icons';
import moment from 'moment'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import PublicTableQuery from '@/components/Public/PublicTableQuery'

import Trees from './components/treeDepart'
import {roleJudgment} from '@/utils/authority'
const { TabPane } = Tabs;
const { Search, TextArea } = Input;
const CustomerService = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading, setLoading] = useState(false)
    const [dataSource, changeDataSource] = useState([])
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)
    const [total, changeTotal] = useState(0)
    const [tableHeight,setTableHeight] = useState(0)
    const [tabsKey,setTabsKey] = useState('1')
    const [isList, setIsList] = useState(true)
    const [isCreate, setIsCreate] = useState(true)
    const [isEditUnit, setIsEditUnit] = useState(false)
    const [defaultQuery, setDefaultQuery] = useState({})
    const [isModalVisible, setIsModalVisible] = useState(false)
    const [modalTitle,setModalTitle] = useState('选择部门或成员')
    const [modalType, setModalType] = useState(1)
    const [openKfid, setOpenKfid] = useState('')
    const [customerInfo, setCustomerInfo] = useState({})
    const [treeData, setTreeData] = useState([]);
    const [isEditWelcome, setIsEditWelcome] = useState(false);
    const childRef = useRef();

    const options = [
        { label: '一', value: '1', },
        { label: '二', value: '2', },
        { label: '三', value: '3', },
        { label: '四', value: '4', },
        { label: '五', value: '5', },
        { label: '六', value: '6', },
        { label: '日', value: '7', },
    ];
    const options_3 = [
        { label: '优先上次接待人员', value: 1, },
        { label: '轮流接待', value: 2, },
        { label: '空闲接待', value: 3, },
    ]
    const options_33 = [
        { label: '轮流接待', value: 2, },
        { label: '空闲接待', value: 3, },
    ]
    const options_4 = [
        { label: '1位', value: 1, },
        { label: '2位', value: 2, },
        { label: '3位', value: 3, },
        { label: '4位', value: 4, },
        { label: '5位', value: 5, },
        { label: '10位', value: 10, },
        { label: '15位', value: 15, },
        { label: '20位', value: 20, },
        { label: '25位', value: 25, },
        { label: '30位', value: 30, },
        { label: '50位', value: 50, },
    ]
    const options_5 = [
        { label: '10分钟', value: 10, },
        { label: '30分钟', value: 30, },
        { label: '1小时', value: 60, },
        { label: '12小时', value: 720, },
        { label: '48小时', value: 2880, },
    ]
    
    let searchList = [
        { label: '账号名称', name: 'name', type: 'Input', placeholder: '请输入', colSpan: 6},
    ]
    const showModal = (type) => {
        setIsModalVisible(true);
        setModalType(type)
        if(type == 1) {
            servicerTree()
            setModalTitle("选择部门或成员")
        }else if(type == 2) {
            setModalTitle("接待时间")
        }else if(type == 3) {
            setModalTitle("接待分流")
        }else if(type == 4) {
            setModalTitle("单人同时最多接待")
        }else if(type == 5) {
            setModalTitle("自动结束聊天")
        }else if(type == 6) {
            setModalTitle("欢迎语")
        }else if(type == 7) {
            setModalTitle("自动发送满意度评价")
        }
    };
    // 更改客服账号（接待人员）
    const updateReception = (params) => {
        post(allUrl.WeCom.updateReception, { receptions: params, openKfid: openKfid }).then(res => {
            if (res.success) {
                // 处理回执情况字段，把两个字段包到一个字段中
                customerInfo.receptions = params
                setCustomerInfo(customerInfo)
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }
    // 更改客服账号（接待时间）
    const updateTime = (params) => {
        post(allUrl.WeCom.updateTime, { ...params }).then(res => {
            if (res.success) {
                // 处理回执情况字段，把两个字段包到一个字段中
                message.success("修改成功")
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }
    // 更改客服账号（其他）
    const updateAccountOther = (params) => {
        post(allUrl.WeCom.updateAccountOther, { ...params }).then(res => {
            if (res.success) {
                // 处理回执情况字段，把两个字段包到一个字段中
                message.success("修改成功")
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }

    const handleOk = () => {
        setIsModalVisible(false);
        if(modalType == 1) {
            console.log(childRef.current.receptions, 444444)
            let receptions = childRef.current.receptions
            customerInfo.receptions = receptions
            if(!isCreate) {
                updateReception(receptions)
            }
        }else if(modalType == 2){
            let params = {
                openKfid: openKfid, 
                gooffwork: customerInfo.gooffwork, 
                gotowork: customerInfo.gotowork, 
                autoReply: customerInfo.autoReply, 
                receptionWeeks: customerInfo.receptionWeeks
            }
            if(!isCreate) {
                updateTime(params)
            }
        }else {
            let params = {
                openKfid: openKfid, 
                receptionShunt: customerInfo.receptionShunt, 
                receptionNoLast: customerInfo.receptionNoLast, 
                receptionMaxNum: customerInfo.receptionMaxNum, 
                overtimeEnd: customerInfo.overtimeEnd,
                welcome: customerInfo.welcome,
                satisSwitch: customerInfo.satisSwitch
            }
            if(!isCreate) {
                updateAccountOther(params)
            }
        }
        setCustomerInfo(customerInfo)
        
    };
    const handleCancel = () => {
        setIsModalVisible(false);
        if(!isCreate) {
            queryAccountInfo(openKfid)
        }
    };
    
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const renderName = (text, record) => {
        return <div className='avatar-name'>
                <img className="photo" src={ record.avatar } />
                <span className='unit-text'>{ record.name }</span>
            </div>
    }
   
    const renderWeeks = (text, record) => {
        let receptionWeeks = record.receptionWeeks.split(',')
        return receptionWeeks.map((item, index) => {
            return index+1 < receptionWeeks.length ? 
            <span key={index}>周{ options[item-1].label }、</span> :
            <span key={index}>周{ options[item-1].label }</span>
        }) 
        
    }
    const renderDateTime = (text, record) => {
        return <span>{ record.gotowork} - { record.gooffwork == '00:00' ?  '次日' + record.gooffwork : record.gooffwork }</span>
        
    }
    const cancel = () => {
        console.log(8888)
    }
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo, 'CUSTOMER_MANAGE_DETAIL') ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => CustomeDetail(record)}>详情</span>,
                        <Divider key={1} type="vertical" />,
                    ] 
                    : null
            }
            {
                roleJudgment(userInfo, 'CUSTOMER_MANAGE_DEl') ?
                    [
                        <Popconfirm
                            title="是否确定删除这条数据？"
                            onConfirm={() => DelCustomer(record)}
                            onCancel={cancel}
                            okText="确定"
                            cancelText="取消"
                        >
                            <span key={2} style={{ cursor: 'pointer' }}>删除</span>
                        </Popconfirm>
                        
                        
                    ] 
                    : null
            }
        </div>
    }
    const renderReceptions = (text, record) => {
        return record.receptions.map((item, index) => {
               return <div key={index}>{ item.recepName }</div>
            }) 
    }
    const CustomeDetail = (record) => {
        setOpenKfid(record.openKfid)
        setIsList(false)
        setIsCreate(false)
        setIsEditUnit(false)
        queryAccountInfo(record.openKfid)
    }
    const queryAccountInfo = (openKfid) => {
        let params = new URLSearchParams(); 
        params.append('openKfid', openKfid)
        post(allUrl.WeCom.queryAccountInfo, params, {isForm: true}).then(res => {
            if (res.success) {
                setCustomerInfo(res.resp[0])
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }
    const DelCustomer = (record) => {
        let params = new URLSearchParams(); 
        params.append('openKfid', record.openKfid)
        post(allUrl.WeCom.delAccount, params, {isForm: true}).then(res => {
            if (res.success) {
                getData()
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
    }
    
    const CustomerData = {
        rowKey: record => record.openKfid,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content', y: tableHeight},
        columns: [
            { title: '账号名称', dataIndex: 'name', width: 100,render: (text, record) => renderName(text, record)  },
            { title: '星期', dataIndex: 'receptionWeeks', width: 140, render: (text, record) => renderWeeks(text, record)  },
            { title: '时间段', dataIndex: 'gooffwork', width: 100, render: (text, record) => renderDateTime(text, record)  },
            { title: '是否有管理权限', dataIndex: 'managePrivilege', width: 100, render: (text, record) => text ? "是" : "否"  },
            { title: '接待人员', dataIndex: 'receptions', width: 80, render: (text, record) => renderReceptions(text, record) },
            { title: '操作', width: 80, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: null,
    };
    const getData = () => {
        setLoading(true)
        let query = { ...defaultQuery }
        let params = { pageIndex: current, pageSize, ...query }
        post(allUrl.WeCom.queryAccountList, { ...params }).then(res => {
            if (res.success) {
                // 处理回执情况字段，把两个字段包到一个字段中
                changeDataSource(res.resp[0].list)
                changeTotal(res.resp[0].total)
            } else {
                message.error(res.msg)
            }
            setLoading(false)
        })
        
    }
    const onChangeInput = (type, e) => {
        if(type == 1) {
            customerInfo.name = e.target.value
        }else if(type == 2) {
            customerInfo.autoReply = e.target.value
        }else if(type == 3) {
            customerInfo.welcome = e.target.value
        }
        setCustomerInfo(customerInfo)
        
    }
    const sureWelcome = () => {
        let params = {
            openKfid: openKfid, 
            welcome: customerInfo.welcome,
        }
        if(!isCreate) {
            updateAccountOther(params)
        }
        setIsEditWelcome(false)
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }

    useEffect(()=>{
        initPage(true)
    },[])
    useEffect(() => {
        if (userInfo) {
            getData()
            servicerTree()
        }
    }, [defaultQuery, userInfo, current, pageSize,tabsKey])

    const qwKfSync = () => {
        post(allUrl.WeCom.qwKfSync, { }, { isForm: true }).then(res => {
            if (res.success) {
                getData()
            } else {
                message.error(res.msg)
            }
        })
    }
    const createAccount = () => {
        setIsList(false)
        setIsCreate(true)
        setIsEditUnit(true)
        let customerInfo = {
            avatar: "",
            gooffwork: "00:00",
            gotowork: "00:00",
            name: "",
            overtimeEnd: 10,
            receptionMaxNum: 20,
            receptionNoLast: 2,
            receptionShunt: 1,
            receptionWeeks: "1,2,3,4,5,6,7",
            receptions: [],
            satisSwitch: 1,
            welcome: "你好，很高兴为你服务，请问有什么可以帮到你？"
        }
        setCustomerInfo(customerInfo)
    }
    const createEvent = () => {
        post(allUrl.WeCom.addAccount, { ...customerInfo }).then(res => {
            if (res.success) {
                setIsList(true)
                getData()
            } else {
                message.error(res.msg)
            }
        })
    }
    const servicerTree = (openKfid) => {
        post(allUrl.WeCom.servicerTree, { openKfid: openKfid }).then(res => {
            if (res.success) {
                setTreeData(res.resp[0])
            } else {
                message.error(res.msg)
            }
        })
    }
    const cancelEvent = () => {
        setIsList(true)
    }
    const onSearch = (values) => {
        setDefaultQuery(values)
    }

    const onChangeCheck = (checkedValues) => {
        console.log('checked = ', checkedValues);
        customerInfo.receptionWeeks = checkedValues.join(',')
        setCustomerInfo(customerInfo)
    };
    const onChangeTime1 = (time, timeString) => {
        customerInfo['gotowork'] = timeString
        setCustomerInfo(customerInfo)
    }
    const onChangeTime2 = (time, timeString) => {
        customerInfo['gooffwork'] = timeString
        setCustomerInfo(customerInfo)
    }
    
    const handleChangeSelect = (typeName, value) => {
        let info = JSON.parse(JSON.stringify(customerInfo))
        info[typeName] = value
        setCustomerInfo(info)
    };
    const onChangeCheck7 = (e) => {
        console.log(`checked = ${e.target.checked}`);
        if(e.target.checked){
            customerInfo.satisSwitch = 1
        }else {
            customerInfo.satisSwitch = 0
        }
        setCustomerInfo(customerInfo)
    };
    const addWel = () => {
        setModalType(101)
    }
    const saveName = () => {
        let params = {
            openKfid: openKfid, 
            name: customerInfo.name, 
            avatarMediaId: customerInfo.avatarMediaId, 
            avatar: customerInfo.avatar
        }
        post(allUrl.WeCom.updateAccountInfo, { ...params },).then(res => {
            if (res.success) {
                setIsEditUnit(false)
            } else {
                message.error(res.msg)
            }
        })
    }
    const cancelName = () => {
        setIsEditUnit(false)
    }
    const editUnit = () => {
        setIsEditUnit(true)
    }
    const goBack = () => {
        setIsList(true)
        getData()
    }
    const customRequest = (file) => {
        const fmData = new FormData();
        fmData.append("file", file.file);
        post(allUrl.WeCom.uploadCoverImg, fmData, { isForm: true }).then(res => {
            if (res.success) {
                customerInfo.avatar = res.resp[0].url
                customerInfo.avatarMediaId = res.resp[0].mediaId
                setCustomerInfo(customerInfo)
                message.success("上传成功")
            } else {
                message.error(res.msg)
            }
        })
    }
    return (
        <div className='CustomerManage'>
            {isList ? <div>
                        <PublicTableQuery tabsKey={tabsKey} onSearch={onSearch} searchList={searchList} defaultQuery={defaultQuery} />
                        <div className='tableData'>
                            <Row className='tableTitle'>
                                <Col className='text'>客服管理列表</Col>
                                <div className='btn-box' style={{textAlign: 'right'}}>
                                
                                {
                                    roleJudgment(userInfo, 'CUSTOMER_MANAGE_CREATE') ? 
                                    <Button type='primary' onClick={createAccount} style={{marginRight: '24px'}}>创建账号</Button>
                                    : null
                                }
                                {
                                    roleJudgment(userInfo, 'CUSTOMER_MANAGE_SYNCH') ? 
                                    <Button type='primary' onClick={qwKfSync}>同步客服账号</Button>
                                    : null
                                }   
                                </div>
                            </Row>
                            <Table {...CustomerData} />
                        </div> 
                    </div> :
                    <div className='tableData'>
                        <div className='customer-manage'>
                            <Button size="small" onClick={ goBack } style={{marginBottom: '16px'}} icon={<DoubleLeftOutlined />}>返回</Button>
                            { !isEditUnit ? <div className='info'>
                                <div className="photo-box">
                                    <img className="photo" src={ customerInfo.avatar } />
                                </div>
                                <div className='unit' onClick={ editUnit }>
                                    <span className='unit-text'>{ customerInfo.name }</span>
                                    <EditOutlined/>
                                </div>
                                {/* <span className="name">{ customerInfo.name }</span>
                                <EditOutlined onClick={ editUnit } /> */}
                                {/* { !isEditUnit ? <div className='unit' onClick={ editUnit }>
                                    <span className='unit-text'>所属单位</span>
                                    <EditOutlined/>
                                </div> :
                                <div className="unit">
                                    <Input.Group compact>
                                        <Input style={{ width: '200px'}} defaultValue="所属单位" value={ name } onChange={ onChangeInput.bind(this, 1) }/>
                                        <Button type="primary" onClick={ sureEditUnit }>确定</Button>
                                    </Input.Group>
                                </div>} */}
                            </div> : 
                            <div className='info-create'>
                                <div className='form-item'>
                                    <label className='label'>头像</label>
                                    <div className="photo-box">
                                        {/* <img className="photo" src={ customerInfo.avatar } />
                                        <Button type="link">上传</Button> */}
                                        <Upload
                                            accept="image/gif,image/jpeg,image/jpg,image/png"
                                            name="file"
                                            listType="picture-card"
                                            className="avatar-uploader"
                                            showUploadList={false}
                                            customRequest={customRequest}
                                            >
                                            {customerInfo.avatar ? (
                                                <img src={customerInfo.avatar} alt="avatar" style={{ width: '100%', }} />
                                            ) : (
                                                <div>
                                                    { loading ? <LoadingOutlined /> : <PlusOutlined />}
                                                    <div style={{ marginTop: 8, }} >  上传 </div>
                                                </div>
                                            )}
                                        </Upload>
                                    </div>
                                </div>
                                <div className='form-item'>
                                    <label className='label'>名称</label>
                                    <Input style={{ width: '200px'}} placeholder="请输入" defaultValue={ customerInfo.name } onChange={ onChangeInput.bind(this, 1) }/>
                                </div>
                                {
                                    !isCreate && <div className='btn-box'>
                                    <Button type="primary" size="small" style={{ marginRight: '16px' }} onClick={ saveName }>保存</Button>
                                    <Button size="small" onClick={ cancelName }>取消</Button>
                                </div>
                                }
                                
                            </div>}
                            <div className='list-box'>
                                <div className='item'>
                                    <label className='label'>接待人员</label>
                                    <div className='values'>
                                        {
                                            customerInfo.receptions && customerInfo.receptions.map((item, index) => {
                                                return <div className='name' key={index}>
                                                        <UserOutlined />
                                                        <span>{ item.recepName }</span>
                                                    </div>
                                            })
                                        }
                                        <Button className='edit' type="link" size="small" onClick={() => showModal(1)}> 修改 </Button>
                                    </div>
                                </div>
                                <div className='item'>
                                    <label className='label'>接待时间</label>
                                    <div className='values'>
                                        {
                                            customerInfo.receptionWeeks && customerInfo.receptionWeeks.split(',').map((item, index) => {
                                                return (index+1) < customerInfo.receptionWeeks.split(',').length ? 
                                                <span key={index}>周{options[item-1].label}、</span> :
                                                <span key={index}>周{options[item-1].label} </span>
                                            })
                                        }
                                        <span style={{ marginLeft: '16px' }}>
                                            { customerInfo.gotowork }  - 
                                            { customerInfo.gooffwork == '00:00' ?  '次日' + customerInfo.gooffwork : customerInfo.gooffwork }</span>
                                        <Button className='edit' type="link" size="small" onClick={() => showModal(2)}> 修改 </Button>
                                    </div>
                                </div>
                                <div className='item'>
                                    <label className='label'>接待分流</label>
                                    <div className='values'>
                                        <span>{ customerInfo.receptionShunt && options_3[customerInfo.receptionShunt-1].label }</span>
                                        <Button className='edit' type="link" size="small" onClick={() => showModal(3)}> 修改 </Button>
                                    </div>
                                </div>
                                <div className='item'>
                                    <label className='label'>单人同时最多接待</label>
                                    <div className='values'>
                                        <span>{ customerInfo.receptionMaxNum }位客户</span>
                                        <Button className='edit' type="link" size="small" onClick={() => showModal(4)}> 修改 </Button>
                                    </div>
                                </div>
                                <div className='item'>
                                    <label className='label'>自动结束聊天</label>
                                    <div className='values'>
                                        <span>客户{ customerInfo.overtimeEnd < 60 ? customerInfo.overtimeEnd + '分钟' : customerInfo.overtimeEnd/60 + '小时' }未回复</span>
                                        <Button className='edit' type="link" size="small" onClick={() => showModal(5)}> 修改 </Button>
                                    </div>
                                </div>
                                <div className='item'>
                                    <label className='label'>欢迎语</label>
                                    {/* { !isEditWelcome ?  */}
                                    <div className='values wel-val'>
                                        <span>{ customerInfo.welcome }</span>
                                        {/* <Button className='edit' type="link" size="small" onClick={() => { setIsEditWelcome(true) }}> 修改 </Button> */}
                                        <Button className='edit' type="link" size="small" onClick={() => showModal(6)}> 修改 </Button>
                                    </div>
                                    {/* : <div className='values'>
                                        <Input style={{ width: '600px', height: '50px' }}  defaultValue={ customerInfo.welcome } onChange={ onChangeInput.bind(this, 3) }/>
                                        <Button type='primary' style={{ marginLeft: '16px' }}  onClick={ sureWelcome }> 确定 </Button>
                                        <Button style={{ marginLeft: '16px' }}  onClick={() => { setIsEditWelcome(false) }}> 取消 </Button>
                                    </div>} */
                                    }
                                </div>
                                <div className='item'>
                                    <label className='label'>自动发送满意度评价</label>
                                    <div className='values'>
                                        <span>{ customerInfo.satisSwitch ? '已开启' : '未开启'}</span>
                                        <Button className='edit' type="link" size="small" onClick={() => showModal(7)}> 修改 </Button>
                                    </div>
                                </div>
                            </div>
                            {
                                isCreate && <div className='btn-box' style={{ marginTop: '16px' }}>
                                <Button type="primary" style={{ marginRight: '16px' }} onClick={ createEvent }>创建</Button>
                                <Button onClick={ cancelEvent }>取消</Button>
                            </div>
                            }
                            
                        </div>
                    </div>}

            <Modal title={modalTitle} className="modalContainer" open={isModalVisible} closable={true}  width={800} maskClosable={true} onOk={handleOk} onCancel={handleCancel}>
                {modalType == 1 && <Trees ref={childRef} treeDepartData={treeData} receptions={ customerInfo.receptions }></Trees> }
                {
                    modalType == 2 && <div className="time-container">
                        <div className="form-item">
                            <label className='label'>星期</label>
                            <div className="values">
                                <Checkbox.Group options={options} defaultValue={customerInfo.receptionWeeks.split(',')} onChange={onChangeCheck} />
                            </div>
                        </div>
                        <div className="form-item">
                            <label className='label'>时段</label>
                            <div className="values">
                                <span className='time-text'>上班</span>
                                <TimePicker defaultValue={moment(customerInfo.gotowork || '00:00', 'HH:mm')} format='HH:mm' onChange={onChangeTime1}/>
                                <span className='line'>——</span>
                                <span className='time-text'>下班</span>
                                <TimePicker defaultValue={moment(customerInfo.gooffwork || '00:00', 'HH:mm')} format='HH:mm' onChange={onChangeTime2}/>
                            </div>
                        </div>
                        <div className='label'>非接待时间自动回复的内容</div>
                        <TextArea rows={4} showCount maxLength={1000} defaultValue={ customerInfo.autoReply } onChange={ onChangeInput.bind(this, 2) }/>
                    </div>
                }
                {
                   modalType == 3 && <div className='time-container'>
                        <div className="form-item">
                            <label className='label'>接待分流：</label>
                            <div className="values">
                            <Select
                                defaultValue={ customerInfo.receptionShunt }
                                style={{ width: 188 }}
                                onChange={ handleChangeSelect.bind(this, 'receptionShunt') }
                                options={options_3}
                                />
                            </div>
                        </div>
                        {customerInfo.receptionShunt == 1 && <div className="form-item">
                            <label className='label'>无接待人员时：</label>
                            <div className="values">
                            <Select
                                defaultValue={ customerInfo.receptionNoLast }
                                style={{ width: 160 }}
                                onChange={ handleChangeSelect.bind(this, 'receptionNoLast') }
                                options={options_33}
                                />
                            </div>
                        </div>}

                   </div>
                }
                {
                   modalType == 4 && <div className='time-container'>
                        <div className="form-item">
                            <label>单个接待人员最多同时接待</label>
                            <div className="values">
                            <Select
                                defaultValue={ customerInfo.receptionMaxNum }
                                style={{ width: 160 }}
                                onChange={ handleChangeSelect.bind(this, 'receptionMaxNum') }
                                options={options_4}
                                />客户
                            </div>
                        </div>
                   </div>
                }
                { modalType == 5 && <div className='time-container'>
                    客户超过
                    <Select
                        defaultValue={ customerInfo.overtimeEnd }
                        style={{ width: 160 }}
                        onChange={ handleChangeSelect.bind(this, 'overtimeEnd') }
                        options={ options_5 }
                    /> 
                    未回复消息时自动结束聊天
                </div> }
                { modalType == 6 && <div className='time-container'>
                    {/* <div className='tips'>您好，这里是AITO客服。关于用户中心任何问题、建议、投诉您都可以在这里留言反馈。我们将会及时收集并跟进。</div>
                    <div className='wel-list'>
                        <div className='wel-item'>
                            <div className='wel-content'>你好，很高兴为你服务，请问有什么可以帮到你？</div>
                            <img className="select-icon" src={ Groupselect }/>
                        </div>
                    </div>
                    <span className='add-wel' onClick={ addWel } >添加欢迎语</span> */}
                    <div className='label'>欢迎语</div>
                    <TextArea rows={4} showCount style={{ marginTop: 8 }} maxLength={1000} defaultValue={ customerInfo.welcome } onChange={ onChangeInput.bind(this, 3) }/>
                </div> }

                { modalType == 101 && <div className='wel-container'>
                    <div className='left'>
                        <div className='textarea-box'>
                            <TextArea rows={8} bordered={false} placeholder="请输入欢迎语，如：你好，很高兴为你服务" />
                            <div className='list'></div>
                            <div className='tip-btn'>
                                {/* <div className='btn'>添加客户咨询较多的问题</div> */}
                               
                                <span className='tip'>客户点击问题后，机器人将自动回复答案</span>
                            </div>
                        </div>
                        <div className='form-item'>
                            <label className='label'>使用该欢迎语的客服帐号：</label>
                            <Select
                                defaultValue="1"
                                style={{ width: 160 }}
                                onChange={handleChangeSelect}
                                options={options_5}
                            /> 
                        </div>
                        
                    </div>
                    <div className='right'>

                    </div>
                </div>}
                {
                   modalType == 7 && <div className='time-container'>
                        <div className="form-item">
                            <label className='label'>自动发送满意度评价</label>
                            <div className="values">
                                <Checkbox onChange={onChangeCheck7} defaultChecked={ customerInfo.satisSwitch }>开启</Checkbox>
                            </div>
                        </div>
                        <div className='eval-content'>
                            <div className="title">满意度内容展示</div>
                            <div className='item'>
                                <div className="i-label">请您为本次服务打分</div>
                                <Radio.Group>
                                    <Space direction="vertical">
                                        <Radio value={5}>非常满意</Radio>
                                        <Radio value={4}>满意</Radio>
                                        <Radio value={3}>一般</Radio>
                                        <Radio value={2}>不满意</Radio>
                                        <Radio value={1}>非常不满意</Radio>
                                    </Space>
                                </Radio.Group>
                            </div>
                            {/* <div className='item'>
                                <div className="i-label">您的问题是否得到解决</div>
                                <Radio.Group>
                                    <Space direction="vertical">
                                        <Radio value={1}>是</Radio>
                                        <Radio value={0}>否</Radio>
                                    </Space>
                                </Radio.Group>
                            </div> */}

                        </div>
                   </div>
                }

            </Modal>


        </div>
    )
}
export default CustomerService
