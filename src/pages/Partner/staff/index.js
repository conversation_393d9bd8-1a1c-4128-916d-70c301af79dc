import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Timeline, message, Popconfirm, Tabs, Row, Col, Button, Modal } from "antd";
import moment from "moment";
import { UniversalOpenWindow } from "@/components/Public/PublicOpenWindow";
import PublicTableQuery from "@/components/Public/PublicTableQuery";
import PublicTable from "@/components/Public/PublicTable";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
import { roleJudgment } from "@/utils/authority";
import { setStorage, getStorage } from "../../../utils/Storage";
import { getDict } from "../../../actions/async";
import _ from "lodash";
import "./index.less";
import PartnerForm from "./detail";
import UploadFile from "@/components/Public/UploadFile";
import baseURL from "@/baseURL";

const { TabPane } = Tabs;

const PartnerStaffList = (props) => {
  //创建ref节点
  const childRef1 = useRef(null);
  const tableRef1 = useRef(null);
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.common);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [current, setCurrent] = useState(1);
  const [tableHeight, setTableHeight] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [importLoading, setImportLoading] = useState(false);
  const [defaultQuery, setDefaultQuery] = useState({});
  const [treeOrgData, setTreeOrgData] = useState([]);
  const [dictLockType, setDictLockType] = useState([]);
  const [jobStatus, setJobStatus] = useState([]);
  const [carDict, setCarDict] = useState([]);
  const [selectedPartner, setSelectedPartner] = useState({});
  const [showForm, setShowForm] = useState(false);
  const [isViewMode, setIsViewMode] = useState(false);
  const [showPhone, setShowPhone] = useState("");
  const [phoneOpen, setPhoneOpen] = useState(false);
  const onSearch = (values) => {
    setDefaultQuery(values);
  };
  const openForm = (partner) => {
    console.log("partner", partner, partner !== {});
    if (partner) {
      post(allUrl.Partner.getPlaintextDetailById, { id: partner.id }).then((res) => {
        if (res.success) {
          console.log("详情", res.resp[0]);
          // 处理多选数据
          let arr = []
          if (res.resp[0].partnerCodes) {
            const codesArr = res.resp[0].partnerCodes.split(',')
            const namesArr = res.resp[0].partnerNames.split(',')
            codesArr.map((item, index) => {
              arr.push({
                value: item,
                label: namesArr[index]
              })
            })
          }
          setSelectedPartner({
            ...partner,
            // partnerCodes: res.resp[0].partnerCodes ? res.resp[0].partnerCodes.split(",") : [],
            partnerCodes: arr,
            phone: res.resp[0].phone,
            emailAddress: res.resp[0].emailAddress,
          });
          setIsViewMode(false);
          setShowForm(true);
        } else {
          message.error(res.msg);
        }
      });
    } else {
      setSelectedPartner({});
      setIsViewMode(false);
      setShowForm(true);
    }
  };
  const exportStaffList = () => {
    post(
      allUrl.Partner.exportPartnerStaffExcel,
      {},
      {
        responseType: "blob",
      }
    ).then((res) => {
      if (res) {
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容ie
          window.navigator.msSaveBlob(blob, "合作伙伴员工列表.xlsx");
        } else {
          let downloadElement = document.createElement("a");
          let href = window.URL.createObjectURL(blob); //创建下载的链接
          downloadElement.href = href;
          let date = new Date();
          downloadElement.download = `合作伙伴员工列表${date.toLocaleDateString()}` + ".xlsx"; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放掉blob对象
        }
        message.success("导出成功！");
      } else {
        // message.error(res.msg)
      }
    });
  }

  const openViewForm = (partner) => {
    setSelectedPartner(partner);
    setIsViewMode(true);
    setShowForm(true);
  };

  const closeForm = () => {
    setSelectedPartner({});
    setIsViewMode(false);
    setShowForm(false);
    setLoading(false);
  };
  const lookPhoneMumber = (record) => {
    post(allUrl.Partner.getPlaintextDetailById, { id: record.id }).then((res) => {
      if (res.success) {
        console.log("详情", res.resp[0]);
        setShowPhone(res.resp[0].phone);
        setPhoneOpen(true);
      } else {
        message.error(res.msg);
      }
    });
  };
  const savePartner = (partner) => {
    setLoading(true);
    if (!partner.emailAddress && !partner.phone) {
      message.error("请填写手机号或者邮箱")
      setLoading(false);
      return
    }
    if (partner.id) {
      post(allUrl.Partner.updatePartnerStaff, { ...partner }).then((res) => {
        if (res.success) {
          message.success("操作成功");
          tableRef1.current.getTableData();
          closeForm();
        }
      });
    } else {
      post(allUrl.Partner.addPartnerStaff, { ...partner }).then((res) => {
        if (res.success) {
          message.success("操作成功");
          tableRef1.current.getTableData();
          closeForm();
        }
      });
    }
    setLoading(false);

  };
  const renderOperation = (text, record) => {
    return (
      <div style={{ color: "#1890ff" }}>
        {roleJudgment(userInfo, "EDIT_PARTNER_STAFF")
          ? [
            <span
              key={1}
              style={{ cursor: "pointer", marginRight: 10 }}
              onClick={() => openForm(record)}
            >
              编辑
            </span>,
          ]
          : null}
        {roleJudgment(userInfo, "PARTNER_STAFF_DETAIL")
          ? [
            <span
              key={2}
              style={{ cursor: "pointer", marginRight: 10 }}
              onClick={() => openViewForm(record)}
            >
              详情
            </span>,
          ]
          : null}
        {roleJudgment(userInfo, "PARTNER_STAFF_PHONE")
          ? [
            <span key={3} style={{ cursor: "pointer" }} onClick={() => lookPhoneMumber(record)}>
              查看手机号
            </span>,
          ]
          : null}
      </div>
    );
  };
  const downloadDataFailed = (response) => {
    let url = response.resp[0].errorUrl
    window.open(url)
  }
  const UploadChange = ({ file, fileList }, type) => {
    console.log(file, type, "file的数据");
    const { response } = file;
    if (file.status === "uploading") {
      setImportLoading(true);
    }
    if (file.status === "done") {
      if (response.success) {
        let key = 2;
        message.loading({ content: "正在导入中，请稍后", key });
        setTimeout(() => {
          message.success({ content: "导入成功!", key, duration: 2 });
          tableRef1.current.getTableData();
        }, 1000);
      } else if (response.resp && response.resp[0].errorUrl) {
        let key = 2;
        message.loading({ content: "正在导入中，请稍后", key });
        setTimeout(() => {
          message.error({
            content: (
              <span>
                导入列表失败，点击"<a onClick={() => downloadDataFailed(response)}>确定</a>
                "下载导入失败数据
              </span>
            ),
            key,
            duration: 10,
          });
        }, 1000);
      } else {
        message.error(response.msg || response.msgCode);
      }
      setImportLoading(false);
    }
    if (file.status === "error") {
      message.error(response.msg || response.msgCode);
      setImportLoading(false);
    }
  };
  const download = () => {
    let url =
      "https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/seres-partner/%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E5%91%98%E5%B7%A5%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx";
    window.open(url);
  };
  const initPage = () => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let FormQueryH = document.getElementsByClassName("PublicList_FormQuery")[0].clientHeight;
    let h = winH - FormQueryH - 47 - 54 - 305;
    setTableHeight(h);
  };
  useEffect(() => {
    initPage();
  }, []);

  // 获取字典数据
  const getDictData = () => {
    get(allUrl.common.entryLists, { codes: "scrm_parter_staff_status" }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        Dt["scrm_parter_staff_status"].forEach((item) => {
          item.name = item.entryMeaning;
          item.value = Number(item.entryValue);
        });
        setJobStatus(Dt["scrm_parter_staff_status"]);
      }
    });
  };
  /**
   * 根据字典和id返回状态名称
   * @param {Array} dict
   * @param {string} id
   * @returns
   */
  const renderStatusName = (dict, id) => {
    let str = "";
    dict.map((item) => {
      if (item.value == id) {
        str = item.name;
      }
    });
    return str;
  };
  useEffect(() => {
    getDictData();
  }, []);
  const columns1 = [
    { title: "统一登录账号", dataIndex: "staffNumber", width: 120, fixed: "left" },
    { title: "姓名", dataIndex: "staffName", width: 100 },
    { title: "手机号", dataIndex: "phone", width: 100 },
    { title: "邮箱", dataIndex: "emailAddress", width: 100 },
    { title: "岗位", dataIndex: "position", width: 100 },
    { title: "所属企业", dataIndex: "partnerName", width: 100 },
    {
      title: "在职状态",
      dataIndex: "isOnJob",
      width: 100,
      render: (text) => renderStatusName(jobStatus, text),
    },
    // { title: "企业编码", dataIndex: "partnerCode", width: 100 },
    // { title: "邮箱地址", dataIndex: "emailAddress", width: 180 },
    {
      title: "操作",
      fixed: "right",
      width: 100,
      dataIndex: "Operation",
      render: (text, record) => renderOperation(text, record),
    },
  ];

  let searchList1 = [
    { label: "统一登录账号", name: "staffNumber", type: "Input", placeholder: "请完整输入", colSpan: 6 },
    { label: "姓名", name: "staffName", type: "Input", placeholder: "请完整输入", colSpan: 6 },
    { label: "手机号", name: "phone", type: "Input", placeholder: "请完整输入", colSpan: 6 },
    { label: "所属企业", name: "partnerName", type: "Input", colSpan: 6 },
    // { label: "岗位", name: "position", type: "Input", colSpan: 6 },
    {
      label: "在职状态",
      name: "isOnJob",
      type: "Select",
      placeholder: "请选择",
      colSpan: 6,
      data: jobStatus,
    },
  ];

  return (
    <div className="PartnerStaffList">
      <PublicTableQuery
        onSearch={onSearch}
        searchList={searchList1}
        isCatch={false}
        isFormDown={false}
        ref={childRef1}
      />
      <div className="tableData">
        <Row className="tableTitle">
          <Col className="text">合作伙伴员工列表</Col>
          <Col style={{ textAlign: "right" }}>
            {roleJudgment(userInfo, "ADD_PAETNER_STAFF") ? (
              <Button
                type="primary"
                style={{ cursor: "pointer", marginRight: 10 }}
                onClick={() => openForm()}
              >
                新增合作伙伴员工
              </Button>
            ) : null}
            {roleJudgment(userInfo, "EXPORT_PARTNER_STAFF") ? (
              <Button
                type="primary"
                style={{ cursor: "pointer", marginRight: 10 }}
                onClick={() => exportStaffList()}
              >
                导出
              </Button>
            ) : null}
            {roleJudgment(userInfo, "IMPORT_PARTNER_STAFF") ? (
              <>
                {" "}
                <UploadFile
                  style={{ display: "inline-block" }}
                  extension={["xls", "xlsx"]}
                  showUploadList={false}
                  size={10}
                  action={baseURL.Host + allUrl.Partner.importPartnerStaffExcel}
                  UploadChange={UploadChange}
                >
                  <Button loading={importLoading}>导入</Button>
                </UploadFile>
                <a style={{ fontSize: "14px", margin: "6px 6px 0 10px" }} onClick={download}>
                  模版下载
                </a>
              </>
            ) : null}
          </Col>
        </Row>
        <PublicTable
          url={allUrl.Partner.partnerStaffList}
          scroll={{ x: "max-content", y: tableHeight }}
          isCatch={true}
          columns={columns1}
          type={2}
          ref={tableRef1}
          rowSelection={false}
          setCurrent={setCurrent}
          setPageSize={setPageSize}
          defaultQuery={{ ...defaultQuery }}
        />
      </div>
      {showForm && (
        <PartnerForm
          visible={showForm}
          onCancel={closeForm}
          jobStatus={jobStatus}
          loading={loading}
          onSave={savePartner}
          renderStatusName={renderStatusName}
          initialValues={selectedPartner}
          isViewMode={isViewMode}
        />
      )}
      {phoneOpen && (
        <Modal
          centered
          open={phoneOpen}
          onOk={() => setPhoneOpen(false)}
          onCancel={() => setPhoneOpen(false)}
          width={400}
        >
          <p>{showPhone}</p>
        </Modal>
      )}
    </div>
  );
};
export default PartnerStaffList;
