import React from 'react'

/**
 * @description: 设置延保结束时间
 * @param {*} value 相对开始时间的value值（下拉框选项）
 * @param {*} originDetail 前权益对象（包括权益限制年限和生效时间）
 * @param {*} currentFormData 当前对象（包括权益限制年限和生效时间）
 * @param {*} entryList 字典值（包括equity_relativeTime）
 * @return {*} 返回相对结束时间字符串或者dom值
 */
export const endTimeRender = ({ value, originDetail, currentFormData, entryList, showTips }) => {
  if (originDetail) {
    if (originDetail.limitTimeFrame != 0) {
      function currentLimitTime() {
        switch (currentFormData.limitTimeFrame) {
          case -1:
            return ` + <div class='box' style='display:inline-block'><span>延保限制年限：</span>不限年数（延保为终身）</div>`
          case 1:
            return ` + <div class='box' style='display:inline-block'><span>延保限制年限：</span><b>${Number(currentFormData.limitYear)}</b>年<b>${Number(currentFormData.limitMonth)}</b>月<b>${Number(currentFormData.limitDay)}</b>日</div>`
          default:
            return ''
        }
      }
      const strComputed = {
        // 选择前权益相对结束时间
        12: () => {
          let str = ``
          const relativeTime = entryList['equity_relativeTime'].filter(i => i.value == originDetail.relativeTime)[0]
          if (relativeTime.name) {
            str += `<div class='box' style='display:inline-block'><span>前权益开始时间：</span> ${relativeTime.name}</div>`
          }
          switch (originDetail.limitTimeFrame) {
            case -1:
              str += ` + <div class='box' style='display:inline-block'><span>前权益限制年限</span>：不限年数</div>`
              break
            case 1:
              str += ` + <div class='box' style='display:inline-block'><span>前权益限制年限：</span><b>${Number(originDetail.limitYear)}</b>年<b>${Number(originDetail.limitMonth)}</b>月<b>${Number(originDetail.limitDay)}</b>日</div>`
              break
          }

          str += currentLimitTime()
          str += showTips ? `<div class='red-tips'>该结束时间只做展示，如需修改请修改以上时间相关内容，如延保限制年限</div>` : ''
          return <div className='public-end-time' style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: str }}></div>
        },
        11: () => {
          let str = `<div class='box' style='display:inline-block'>前权益的实际结束时间</div>`
          str += currentLimitTime()
          str += showTips ? `<div class='red-tips'>该结束时间只做展示，如需修改请修改以上时间相关内容，如延保限制年限</div>` : ''
          return <div className='public-end-time' style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: str }}></div>
        }
      }

      if (Object.prototype.toString.call(strComputed[value]) === "[object Function]") {
        return strComputed[value]()
      }
      return ''
    }
    return ''
  }
  return ''
}

/**
 * @description: 设置延保聚合结束时间
 * @param {*} value 相对开始时间的value值（下拉框选项）
 * @param {*} originDetail 前权益对象（包括权益限制年限和生效时间）
 * @param {*} currentFormData 聚合对象（包括权益限制年限和生效时间）
 * @param {*} entryList 字典值（包括equity_relativeTime）
 * @return {*} 返回相对结束时间字符串或者dom值
 */
export const endTimeRenderPolymerization = ({ value, originDetail, currentFormData, entryList }) => {

  if (originDetail) {
    if (originDetail.limitTimeFrame != 0) {
      function currentLimitTime() {
        switch (currentFormData.limitTimeFrame) {
          case -1:
            return ` + <div class='box' style='display:inline-block'><span>延保限制年限：</span>不限年数（延保为终身）</div>`
          case 1:
            return ` + <div class='box' style='display:inline-block'><span>聚合限制年限：</span><b>${Number(currentFormData.limitYear)}</b>年<b>${Number(currentFormData.limitMonth)}</b>月<b>${Number(currentFormData.limitDay)}</b>日</div>`
          default:
            return ''
        }
      }

      const strComputed = {
        // 选择前权益相对结束时间
        12: () => {
          let str = ''
          const relativeTime = entryList['equity_relativeTime'].filter(i => i.entryValue == originDetail.relativeTime)[0]
          if (relativeTime.entryMeaning) {
            str += `<div class='box' style='display:inline-block'><span>前权益开始时间：</span> ${relativeTime.entryMeaning}</div>`
          }
          switch (originDetail.limitTimeFrame) {
            case -1:
              str += ` + <div class='box' style='display:inline-block'><span>前权益限制年限：</span>不限年数</div>`
              break
            case 1:
              str += ` + <div class='box' style='display:inline-block'><span>前权益限制年限：</span><b>${Number(originDetail.limitYear)}</b>年<b>${Number(originDetail.limitMonth)}</b>月<b>${Number(originDetail.limitDay)}</b>日</div>`
              break
          }
          str += currentLimitTime()
          return <div class='public-end-time' style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: str }}></div>
        },
        11: () => {
          let str = `<div class='box' style='display:inline-block'>前权益的实际结束时间</div> ${currentLimitTime()}`
          return <div className='public-end-time' style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: str }}></div>
        },
        other: () => {
          let str = ''
          const r = entryList['equity_relativeTime'].filter(i => i.entryValue == value)
          if (r.length) {
            str += `<div class='box' style='display:inline-block'><span>前权益开始时间：</span> ${r[0].entryMeaning}</div>`
            str += currentLimitTime()
          } else {
            return ''
          }
          return <div className='public-end-time' style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: str }}></div>
        }
      }
      switch (value) {
        case 12:
        case 11:
          if (Object.prototype.toString.call(strComputed[value]) === "[object Function]") {
            return strComputed[value]()
          }
          break
        default:
          return strComputed.other()
      }

      return ''
    }
    return ''
  }
  return ''
}

/**
 * @description: 设置延保生效里程-结束里程
 * @param {*} originDetail 前权益对象（包括权益限制里程和生效里程）
 * @param {*} currentFormData 当前对象（包括权益限制里程和生效里程）
 * @param {*} entryList 字典值（包括equity_relative_begin_mileage_strategy）
 * @return {*} 返回相对结束里程字符串或者dom值
 */
export const mileageEndRender = ({ originDetail = null, currentFormData = null, entryList }) => {
  if (originDetail) {
    if (currentFormData) {
      const relativeBeginMileageStrategy = Number(currentFormData.relativeBeginMileageStrategy)
      const relativeEndMileageStrategy = Number(currentFormData.relativeEndMileageStrategy)
      let str = ''
      const extendMileage = () => {
        let _str = ''
        // 延保限制里程
        switch (currentFormData.limitLegendFrame) {
          case 1:
            // 行驶里程
            _str += `<div class='box' style='display:inline-block'><span>延保限制里程：</span> <b>${currentFormData.limitLegend || 0}</b>公里</div>`
            break
          case 2:
            // 固定里程
            _str += `<div class='box' style='display:inline-block'><span>延保限制里程：</span> <b>${currentFormData.extenderMileage || 0}</b>公里</div>`
            break
        }
        return _str
      }

      // 生效里程切换单选时判断
      switch (currentFormData.effectMileageFrame) {
        // 无此策略
        case 0:
          return ''
        default:
          // 前权益的相对结束里程，结束里程选择计算到里程和
          if (relativeBeginMileageStrategy === 12 && relativeEndMileageStrategy === 1) {
            // 如果前权益为无此策略，开始里程为0
            let startMileage = 0
            if (Number(originDetail.effectMileageFrame) === 2) {
              // 如果前权益为相对里程
              startMileage = entryList['equity_relative_begin_mileage_strategy'].filter(i => i.entryValue == originDetail.relativeBeginMileageStrategy)[0]

            } else if (Number(originDetail.effectMileageFrame) === 1) {
              // 如果前权益为固定里程
              startMileage = originDetail.fixedBeginMileage
            }
            str += `<div class='box' style='display:inline-block'><span>前权益开始里程：</span> ${startMileage?.entryMeaning ?? 0}</div>`

            // 判断前权益限制里程
            switch (originDetail.limitLegendFrame) {
              case 1:
                // 行驶里程
                str += ` + <div class='box' style='display:inline-block'><span>前权益的限制里程：</span> <b>${originDetail.limitLegend || 0}</b>公里</div>`
                break
              case 2:
                // 固定里程
                str += ` + <div class='box' style='display:inline-block'><span>前权益的限制里程：</span> <b>${originDetail.extenderMileage || 0}</b>公里</div>`
                break
            }

            // 延保限制里程
            str += ` + ${extendMileage()}`


          }
          // 前权益的相对结束里程，结束里程选择计算到里程策略
          if (relativeBeginMileageStrategy === 12 && relativeEndMileageStrategy === 2) {
            // 延保限制里程
            str += extendMileage()
          }

          // 前权益的实际结束里程，结束里程选择计算到里程和
          if (relativeBeginMileageStrategy === 11 && relativeEndMileageStrategy === 1) {
            str += `<div class='box' style='display:inline-block'><span>前权益的实际结束里程</span></div>`
            // 延保限制里程
            str += ` + ${extendMileage()}`
          }

          // 前权益的实际结束里程，结束里程选择计算到里程策略
          if (relativeBeginMileageStrategy === 11 && relativeEndMileageStrategy === 2) {
            // 延保限制里程
            str += extendMileage()
          }
          break
      }


      return <div className='public-end-mileage' style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: str }}></div>
    }
    return ''
  }
  return ''
}


/**
 * @description: 设置延保生效里程-结束里程
 * @param {*} originDetail 前权益对象（包括权益限制里程和生效里程）
 * @param {*} currentFormData 聚合对象（包括权益限制里程和生效里程）
 * @param {*} entryList 字典值（包括equity_relative_begin_mileage_strategy）
 * @return {*} 返回相对结束里程字符串或者dom值
 */
export const mileageEndRenderPolymerization = ({ originDetail = null, currentFormData = null, entryList }) => {
  if (originDetail) {
    if (currentFormData) {
      const relativeBeginMileageStrategy = Number(currentFormData.relativeBeginMileageStrategy) //相对里程-开始里程
      const relativeEndMileageStrategy = Number(currentFormData.relativeEndMileageStrategy) //相对里程-结束里程
      const fixedEndMileageStrategy = Number(currentFormData.fixedEndMileageStrategy) //固定里程-结束里程
      console.log('relativeBeginMileageStrategy', relativeBeginMileageStrategy);

      let str = ''

      // 前权益的开始里程
      const originStartMileage = () => {
        // 如果前权益为无此策略，开始里程为0
        let startMileage = 0
        let _str = ''
        if (Number(originDetail.effectMileageFrame) === 2) {
          // 如果前权益为相对里程
          startMileage = entryList['equity_relative_begin_mileage_strategy'].filter(i => i.entryValue == originDetail.relativeBeginMileageStrategy)[0]

        } else if (Number(originDetail.effectMileageFrame) === 1) {
          // 如果前权益为固定里程
          startMileage = originDetail.fixedBeginMileage
        }
        _str = `<div class='box' style='display:inline-block'><span>前权益开始里程：</span> ${startMileage?.entryMeaning ?? 0}</div>`
        return _str
      }


      // 聚合限制里程
      const extendMileage = () => {
        let _str = ''
        // 延保限制里程
        switch (currentFormData.limitLegendFrame) {
          case 1:
            // 行驶里程
            _str += `<div class='box' style='display:inline-block'><span>聚合限制里程：</span> <b>${currentFormData.limitLegend || 0}</b>公里</div>`
            break
          case 2:
            // 固定里程
            _str += `<div class='box' style='display:inline-block'><span>聚合限制里程：</span> <b>${currentFormData.extenderMileage || 0}</b>公里</div>`
            break
        }
        return _str
      }

      console.log('currentFormData.effectMileageFrame', currentFormData.effectMileageFrame);
      // 生效里程切换单选时判断
      switch (currentFormData.effectMileageFrame) {
        // 无此策略
        case 0:
          return ''
        // 相对里程
        case 2:
          // 前权益的相对结束里程，结束里程选择计算到里程和
          if (relativeBeginMileageStrategy === 12 && relativeEndMileageStrategy === 1) {
            str += originStartMileage()

            // 判断前权益限制里程
            switch (originDetail.limitLegendFrame) {
              case 1:
                // 行驶里程
                str += ` + <div class='box' style='display:inline-block'><span>前权益的限制里程：</span> <b>${originDetail.limitLegend || 0}</b>公里</div>`
                break
              case 2:
                // 固定里程
                str += ` + <div class='box' style='display:inline-block'><span>前权益的限制里程：</span> <b>${originDetail.extenderMileage || 0}</b>公里</div>`
                break
            }

            // 聚合限制里程
            str += ` + ${extendMileage()}`


          }
          // 前权益的相对结束里程，结束里程选择计算到里程策略
          if (relativeBeginMileageStrategy === 12 && relativeEndMileageStrategy === 2) {
            // 聚合限制里程
            str += extendMileage()
          }

          // 前权益的实际结束里程，结束里程选择计算到里程和
          if (relativeBeginMileageStrategy === 11 && relativeEndMileageStrategy === 1) {
            str += `<div class='box' style='display:inline-block'><span>前权益的实际结束里程</span></div>`
            // 聚合限制里程
            str += ` + ${extendMileage()}`
          }

          // 前权益的实际结束里程，结束里程选择计算到里程策略
          if (relativeBeginMileageStrategy === 11 && relativeEndMileageStrategy === 2) {
            // 聚合限制里程
            str += extendMileage()
          }
          // 前权益的开始里程，计算到里程策略
          if (relativeBeginMileageStrategy === 13 && relativeEndMileageStrategy === 1) {
            // 前权益的开始里程
            str += originStartMileage()
            // 聚合限制里程
            str += ` + ${extendMileage()}`
          }
          // 前权益的开始里程，计算到里程和
          if (relativeBeginMileageStrategy === 13 && relativeEndMileageStrategy === 2) {
            str += extendMileage()
          }

          break

        // 固定里程
        case 1:
          switch (fixedEndMileageStrategy) {
            // 里程和
            case 1:
              // 固定里程
              str += `<div class='box' style='display:inline-block'><span>固定里程：</span> <b>${currentFormData.fixedBeginMileage || 0}</b>公里</div>`
              // 聚合限制里程
              str += ` + ${extendMileage()}`
              break
            // 里程策略
            case 2:
              str += extendMileage()
              break
          }
          break
      }

      return <div className='public-end-mileage' style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: str }}></div>

    }
    return ''
  }
  return ''
}