import React from 'react'
import { message, Image, Button, Upload, Row, Col,Input, Form, Modal, Tree, Radio ,Select,Spin ,DatePicker,Popconfirm} from 'antd'
import { connect } from 'react-redux'
import { EditOutlined, PlusOutlined, CheckOutlined } from '@ant-design/icons';
import RoleModal from '../Common/RoleModal'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get, post } from '@/utils/request'
import allUrl from '@/utils/url'
import OrgAdd from '../OrgManagement/Modal/OrgAdd'
import OrgEdit from '../OrgManagement/Modal/OrgEdit'
import '../index.less'
import moment from 'moment';
import {roleJudgment} from '@/utils/authority'
import {trim} from '@/utils'
import SelectOrgModal from './Modal/SelectOrg'
import baseURL from '@/baseURL'
import Cookies from 'js-cookie'
import PreviewImg from '@/components/Public/PreviewImg'
import { MOTOR_MECHANIC_CODE, TM_CODE, QYJSJL_CODE, FJSZJ } from './constant'
const {Option} = Select
class Detail extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            loading: false,
            locationParmas: {},
            roleVisible: false,
            OrgAddVisible: false,
            OrgEditVisible: false,
            Type: '',
            treeData: [],
            treeselectedKeys: [],
            treeSelectedRows: [],
            SelectedOrg: [],
            currentOrg: null,
            orgType:'',
            positionOptions:[],
            EditNodeData:[],
            orgInputText:'',
            SelectOrgVisible:false,
            typeList:[],
            extendJson: null,
            isMotorMechanic: false,// 是否是机电技师
            isTM: false,// 是否技术专家
            isQYJSJL: false, // 是否区域技术经理
            previewVisible: false,
            currentUrl: '',
            elecCertUrls: [],
            fileList: [],
            previewFile: {},
            isLoweLectric: false,
            hasStoreOrg: false, // 是否配置了门店组织
            editIDVisible: false, // 设置身份证号弹框
            IDNum: '', // 身份证号
        }
        this.id = 0
    }
    formRef = React.createRef();
    IDFormRef = React.createRef();

    getDict = () =>{
        const {typeList} = this.state
        const {userInfo} = this.props
        // 管理员，厂端可以看所有岗位；门店只能看门店的岗位
        let loginOrganizationTypeArr = typeList.filter(item=>item.type === userInfo.loginOrganizationType)
        get(allUrl.common.entryLists, {codes:'scrm_position'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                let arr = []
                if(loginOrganizationTypeArr.length){
                    if(loginOrganizationTypeArr[0].name == '厂端'){
                        arr = Dt['scrm_position']
                    }else{
                        arr = Dt['scrm_position'].filter(item=>item.extendField1 === loginOrganizationTypeArr[0].name)
                    }
                }else{
                    arr = Dt['scrm_position']
                }
                this.setState({positionOptions:arr})
                if (this.id) {
                    this.initForm()
                }
            } else {
                // message.error(res.message)
            }
        })
    }
    Save = () => {
        const { SelectedOrg, elecCertUrls } = this.state
        console.log('this.formRef.current', this.formRef.current.getFieldValue('idNum'))
        // 如果岗位是机电技师，技术专家，副技术专家，区域技术经理，必选是否持有低压电工证，如果选是，必上传证明文件
        if(this.formRef.current.getFieldValue('position') == MOTOR_MECHANIC_CODE
        || this.formRef.current.getFieldValue('position') == TM_CODE
        || this.formRef.current.getFieldValue('position') == QYJSJL_CODE
         || this.formRef.current.getFieldValue('position') == FJSZJ
        ) {
            if(this.formRef.current.getFieldValue('loweLectric') == 1 && this.state.elecCertUrls.length === 0) {
                message.error('请上传证明文件！')
                return;
            }
        }
        this.formRef.current.validateFields()
            .then(values => {

                let arr = []
                let extendJson = {
                    hasElecCert: values.loweLectric,
                    elecCertUrls: this.state.elecCertUrls
                }
                SelectedOrg.forEach(item => {
                    arr.push({
                        organizationId:item.organizationId,
                        roleIds:[...new Set(item.roleIds)]
                    })
                })
                if(!arr.length) return message.error('请选择所属组织！')
                console.log(arr)
                let data = {
                    ...values,
                    extendJson:extendJson,
                    organizationRoles: arr
                }
                if(this.id) data.id = this.id
                this.setState({loading:true},()=>{
                    if (this.id) {
                        post(allUrl.Authority.userUpdate, data).then(res => {
                            this.setState({loading:false})
                            if (res.success) {
                                message.success(res.msg || '修改用户成功！')
                                history.goBack()
                            } else {
                                // message.error(res.msg || '修改用户失败！')
                            }
                        })
                    } else {
                        post(allUrl.Authority.userCreate, data).then(res => {
                            this.setState({loading:false})
                            if (res.success) {
                                message.success(res.msg || '新增用户成功！')
                                history.goBack()
                            } else {
                                // message.error(res.msg || '新增用户失败！')
                            }
                        })
                    }
                })
                console.log('参数为：', values)
            })
            .catch(error => {
                console.log(error)
            })
    }
    onCancel = () => {
        history.goBack()
    }

    getRoleCB = (data) => {

    }
    updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }

            if (node.children) {
                return { ...node, children: this.updateTreeData(node.children, key, children) };
            }

            return node;
        });
    }
    treeSelect = (selectedKeys, selectedNodes, event) => {
        this.setState({ treeselectedKeys: selectedKeys, treeSelectedRows: selectedNodes.selectedNodes })
    }
    treeTitleRender = (nodeData) => {
        let AddTreeData =  sessionStorage.getItem('AddOrgTreeData') ? JSON.parse(sessionStorage.getItem('AddOrgTreeData')):[]
        return <>
            <div style={{ marginRight: '40px', flex: 1 }}>{nodeData.title}</div>
            {
                AddTreeData.indexOf(nodeData.key) !== -1 &&
                <div style={{ marginRight: '10px' }} onClick={() => this.setState({ OrgEditVisible: true ,EditNodeData:[nodeData] })} title='编辑'><EditOutlined /></div>
            }
        </>
    }
    deWeight = (arr) => {
        for (var i = 0; i < arr.length - 1; i++) {
            for (var j = i + 1; j < arr.length; j++) {
                if (Number(arr[i].key) === Number(arr[j].key)) {
                    arr.splice(j, 1);
                    //因为数组长度减小1，所以直接 j++ 会漏掉一个元素，所以要 j--
                    j--;
                }
            }
        }
        return arr;
    }
    selectOrg = () => {
        const {treeSelectedRows,SelectedOrg} = this.state
        let temp = SelectedOrg.filter(item=>item.organizationId === treeSelectedRows[0].key)
        if(temp.length) return message.error('所选组织不能重复！')
        this.setState({SelectOrgVisible:true,orgType:'ADD'})
    }
    SelectOrgCB = (data,type, hasStoreOrg) =>{
        console.log('数据为：',data, type, hasStoreOrg)
        let temp = [...this.state.SelectedOrg]
        if(type === 'ADD'){
            temp.push(data)
        }else if(type === 'EDIT'){
            let editTemp = temp.filter(item=>item.organizationId === data.organizationId)[0]
            // editTemp.dataAuthority = data.dataAuthority
            editTemp.roleArr = data.roleArr
            editTemp.roleIds = data.roleIds
            editTemp.roleNames = data.roleNames
        }
        this.setState({SelectedOrg:temp, hasStoreOrg})
    }
    SelectedOrgDel = (row) => {
        let { SelectedOrg } = this.state
        let arr = SelectedOrg.filter(item => item.organizationId !== row.organizationId)
        let storeOrgs = arr.filter(item => item.type == 1)
        console.log('storeOrgs', storeOrgs)
        this.setState({ SelectedOrg: arr, hasStoreOrg: storeOrgs.length > 0 })
    }
    SelectedOrgEdit = (row) => {
        this.setState({
            currentOrg:row,
            SelectOrgVisible:true,
            orgType:'EDIT'
        })
    }
    getOrg = (data) => {
        let AddTreeData =  sessionStorage.getItem('AddOrgTreeData') ? JSON.parse(sessionStorage.getItem('AddOrgTreeData')):[]
        if(data){
            let arr = []
            data.forEach(item=>{
                arr.push(item.id)
            })
            AddTreeData = [...AddTreeData, ...arr]
            sessionStorage.setItem('AddOrgTreeData',JSON.stringify(AddTreeData))
        }
        this.GetOrgData()
    }
    getNodeRoute =(tree1, targetId)=> {
        let nodePathArray = []
        let fn = function(tree,targetId) {
            for (let index = 0; index < tree.length; index++) {
                if (tree[index].children) {
                    let endRecursiveLoop = fn(tree[index].children, targetId)
                    if (endRecursiveLoop) {
                        console.log(nodePathArray)
                        nodePathArray.push(tree[index].title)
                        return true
                    }
                }
                if (tree[index].key === targetId) {
                    nodePathArray.push(tree[index].title)
                    return true
                }
            }
        }
        fn(tree1,targetId)
        return nodePathArray
    }
    orgSearch = () =>{
        this.GetOrgData()
    }
    orgReset = () =>{
        this.setState({orgInputText:''},()=>{
            this.GetOrgData()
        })
    }

    isOnJobChange = (value) =>{
        // if(!value){
        //     Modal.confirm({
        //         title:'提示！',
        //         content:`是否确认变更为“离职”，如确定将删除对应企微账号。`,
        //         onOk:()=>{

        //         },
        //         onCancel:()=>{
        //             this.formRef.current.resetFields(['isOnJob'])
        //         }
        //     })
        // }
    }
    setLoweLectric = (value) => {
        this.setState({isLoweLectric: value == 1})

    }
    selectPosition = (value) => {
        const arr = [MOTOR_MECHANIC_CODE, TM_CODE, QYJSJL_CODE]
        if((this.state.isMotorMechanic || this.state.isTM || this.state.isQYJSJL) && arr.indexOf(value) > -1 ) {
        } else {
            this.setState({elecCertUrls: [],fileList: []})
        }
        this.setState({isMotorMechanic: value == MOTOR_MECHANIC_CODE, isTM: value == TM_CODE, isQYJSJL: value == QYJSJL_CODE})
    }
    getFileExtendingName = (filename)=> {
        // 文件扩展名匹配正则
        var reg = /\.[^\.]+$/;
        var matches = reg.exec(filename);
        if (matches) {
          return matches[0];
        }
        return '';
    }

    onPreview = (file) =>{
        console.log('onPreview-file', file)
        let imgSuffix = ['.png','.gif','.jpeg','.jpg','.svg','.bmp']
        let extend = this.getFileExtendingName(file.name)

        if(imgSuffix.indexOf(extend)>-1){
            if(this.state.Type == 'Edit') {
                this.setState({
                    previewFile: file,
                    currentUrl: file.url,
                    previewVisible: true
                })
            } else {
                this.setState({
                    previewFile: file,
                    currentUrl: file.response.resp[0].url,
                    previewVisible: true
                })
            }
        }else{
            window.open(file.url || file.response.resp[0].url)
        }
    }
    // 设置身份证号
    handleSetIDNumber = () => {
        console.log('设置身份证号', this.id)
        this.IDFormRef.current.validateFields()
            .then(values => {
                console.log('修改身份证号',values)
                post(allUrl.Authority.modifyIDNum, {
                    id: this.id,
                    idNum: values.IDNumber
                }).then(res => {
                    if (res.success) {
                        message.success(res.msg || '修改身份证号成功！')
                        const v = this.desenText(values.IDNumber)
                        this.setState({ editIDVisible: false, IDNum: v}, () => {
                        })
                        this.formRef.current.setFieldsValue({idNum: v})
                        // this.initForm()
                    }
                })

            }).catch(error => {
                console.log(error)
            })
    }
    // 身份证号中间8位变成*号
    desenText = (str) => {
        const len = str.length
        let pre6 = ''
        let last4 = ''
        pre6 = str.slice(0,6)
        last4 = str.slice(Math.max(len - 4,4))
        console.log(pre6,last4)
        return pre6 + '********' + last4
    }
    render() {
        const { title ,userInfo, } = this.props
        const {handleDisabled, handleSetIDNumber} = this
        const { locationParmas, fileList, roleVisible,loading, treeData, treeSelectedRows,currentOrg,orgType,
            SelectedOrg, OrgAddVisible, OrgEditVisible ,positionOptions,EditNodeData ,SelectOrgVisible,
            currentUrl, previewVisible, elecCertUrls, editIDVisible,IDNumber} = this.state
        const formItemLayout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 24 },
        }

        const uploadProps = {
            name: 'file',
            multiple: false,
            action: baseURL.Host + allUrl.Authority.uploadUserFile,
            fileList,
            headers: {
                appid: 1,
                authorization: Cookies.get('scrm_token')
            },
            onChange:(info) => {
                console.log('info',info)
                this.setState({
                    fileList: [...info.fileList]
                })
                if (info.file.status !== 'uploading') {
                }
                if (info.file.status === 'done') {
                    message.success(`${info.file.name} 上传成功！`);
                    console.log('res', elecCertUrls,info.file.response.resp[0])
                    elecCertUrls.push(info.file.response.resp[0].url)
                    this.setState({
                        elecCertUrls
                    }, () => {
                        console.log('elecCertUrls111', elecCertUrls)
                    })

                } else if (info.file.status === 'error') {
                    message.error(`${info.file.name} 上传失败！`);
                }
            },
            onPreview:(file)=>this.onPreview(file),
            onRemove:(info) => {
                elecCertUrls.length && elecCertUrls.map((item,index) => {
                    if(item === info.url){
                        elecCertUrls.splice(index, 1)
                    }
                })
                this.setState({
                    elecCertUrls
                })
            }
        }
        return (
            <div className='PublicDetail'>
                <div className='DetailBox'>
                    <div className='DetailTitle'>{locationParmas.title}</div>
                    <div className='DetailCon'>
                        <Spin spinning={loading}>
                            <Form
                                {...formItemLayout}
                                layout={'vertical'}
                                ref={this.formRef}
                                initialValues={{ remember: true, }}
                            >
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>账号信息</p>
                                    <Row className='FormRowCon'>
                                    <Col span={8}>
                                         <Form.Item wrapperCol={{ span: 14 }} label="工号" name="employeeNumber" rules={[{ required: true, message: '请输入工号！', },
                                            ]}>
                                            <Input disabled={this.id} placeholder="请输入..." allowClear onChange={(e)=>trim(e.target.value,'employeeNumber',this.formRef?.current)} />
                                        </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item wrapperCol={{ span: 14 }} label="姓名" name="userName" rules={[{ required: true, message: '请输入姓名！', },
                                        {
                                            validator(_, value) {
                                                if(value && value.length >16){
                                                    return Promise.reject(new Error('姓名最多16个字符'));
                                                }else{
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}>
                                                <Input placeholder="请输入..." allowClear onChange={(e)=>trim(e.target.value,'userName',this.formRef?.current)} />
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item
                                                wrapperCol={{ span: 14 }}
                                                label="手机号"
                                                name="mobilePhone"
                                                rules={[
                                                    { required: true, message: '请输入手机号！', },
                                                    {
                                                        validator(_, value) {
                                                            if (value && !/^[1][1,2,3,4,5,6,7,8,9][0-9]{9}$/.test(value)) {
                                                                return Promise.reject(new Error('请输入正确的手机号！'));
                                                            } else {
                                                                return Promise.resolve();
                                                            }
                                                        },
                                                    }
                                                ]}>
                                                <Input placeholder="请输入..." onChange={(e)=>trim(e.target.value,'mobilePhone',this.formRef?.current)} allowClear disabled={this.id?true:false} />
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item
                                            wrapperCol={{ span: 14 }}
                                            label="公司岗位"
                                            name="position"
                                            rules={[{ required: true, message: '请选择公司岗位！', }]}>
                                                <Select
                                                    onChange={this.selectPosition}
                                                    allowClear
                                                    showSearch
                                                    optionFilterProp="children"
                                                    placeholder='请选择'
                                                    filterOption={(input, option) =>option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                                >
                                                    {
                                                        positionOptions.map((item,index)=><Option key={index} value={item.entryValue}>{item.entryMeaning}</Option>)

                                                    }
                                                </Select>
                                            </Form.Item>
                                        </Col>
                                        {
                                            this.state.isMotorMechanic || this.state.isTM || this.state.isQYJSJL? <>
                                            <Col span={8}>
                                                <Form.Item
                                                        wrapperCol={{ span: 14 }}
                                                        label="是否持有低压电工证"
                                                        name="loweLectric"
                                                        rules={[{ required: true, message: '请选择是否持有低压电工证！', }]}
                                                    >
                                                    <Select placeholder='请选择' onChange={this.setLoweLectric}>
                                                            <Option value={1}>是</Option>
                                                        <Option value={0}>否</Option>
                                                    </Select>
                                                </Form.Item>
                                            </Col>
                                            <Col span={8} >
                                                <Form.Item
                                                        wrapperCol={{ span: 14 }}
                                                        label="证明文件"
                                                        name="extendJson"
                                                        rules={[{ required: this.state.isLoweLectric, message: '请上传证明文件！', }]}
                                                        >
                                                    <Upload {...uploadProps}>
                                                        <Button style={{ border: '1px solid #1890ff', color: '#1890ff', width: 78, padding: 0 }}>文件上传</Button>
                                                    </Upload>
                                                </Form.Item>
                                            </Col>
                                            </> : null
                                        }
                                        <Col span={8}>
                                            <Form.Item
                                                wrapperCol={{ span: 14 }}
                                                label="入职时间"
                                                name="inductionTime"
                                                rules={[{ required: false, message: '请选择入职时间！', }]}
                                            >
                                                <DatePicker placeholder='请选择' style={{width:'100%'}}/>
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item
                                                wrapperCol={{ span: 14 }}
                                                label="是否在职"
                                                name="isOnJob"
                                                rules={[{ required: false, message: '请选择是否在职！', }]}
                                            >
                                                <Select placeholder='请选择' onChange={this.isOnJobChange}>
                                                    <Option value={1}>在职</Option>
                                                    <Option value={0}>离职</Option>
                                                </Select>
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item
                                                wrapperCol={{ span: 14 }}
                                                label="邮箱"
                                                name="email"
                                                rules={[
                                                    { required: false, message: '请输入邮箱！'},
                                                    {
                                                        validator(_, value) {
                                                            if (value && !/^([a-zA-Z0-9]+[_|_|\-|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,6}$/.test(value)) {
                                                                return Promise.reject(new Error('请输入正确的邮箱！'));
                                                            } else {
                                                                return Promise.resolve();
                                                            }
                                                        },
                                                    }
                                                ]}
                                            >
                                                <Input placeholder='请输入' allowClear/>
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                        {
                                                // 编辑账号
                                                 this.id ?

                                                <><Form.Item
                                                shouldUpdate

                                                style={{ position: 'relative' }}
                                                wrapperCol={{ span: 14 }}
                                                    label="身份证号"
                                                    name="idNum"
                                                    rules={[
                                                    { required: this.state.hasStoreOrg, message: '请填写身份证号！'},
                                                    ]}>
                                                            {/* <Input placeholder='请输入'  disabled={this.state.IDNum} value={this.state.IDNum}/> */}
                                                            { this.state.IDNum ? <Input placeholder='请输入'  disabled={true}/> : <Input placeholder='请输入' />}

                                                </Form.Item>
                                                        {
                                                            roleJudgment(userInfo,'PERM_USER_RESET_ID_NUMBER') && (this.state.IDNum.length !== 0) ?
                                                            <>
                                                             <Button type="link" style={{position:'absolute',top:'27px',left:'260px'}} onClick={() => this.setState({ editIDVisible: true })}>编辑</Button>
                                                            </>
                                                                : null
                                                        }
                                                </>
                                                :
                                                // 新增账号
                                                <Form.Item
                                                    wrapperCol={{ span: 14 }}
                                                    label="身份证号"
                                                    name="idNum"
                                                    rules={[
                                                        { required: this.state.hasStoreOrg, message: '请输入身份证号！'},
                                                        {    validator(_, value) {
                                                                if (value && !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/.test(value)) {
                                                                    return Promise.reject(new Error('请输入正确的身份证号！'));
                                                                } else {
                                                                    return Promise.resolve();
                                                                }
                                                        }},
                                                    ]}
                                                >
                                                     <Input placeholder='请输入' disabled={this.state.IDNum !== ''} allowClear/>
                                                </Form.Item>

                                            }

                                        </Col>
                                        <Col span={8}>
                                            <Form.Item
                                                wrapperCol={{ span: 14 }}
                                                label="开户银行"
                                                name="bankName"
                                                rules={[{ required: false, message: '请输入开户银行！', }]}
                                            >
                                                <Input placeholder='请输入' allowClear onChange={(e)=>trim(e.target.value,'bankName',this.formRef?.current)}/>
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item
                                                wrapperCol={{ span: 14 }}
                                                label="银行卡"
                                                name="bankAccount"
                                                rules={[
                                                    { required: false, message: '请输入银行卡！', },
                                                    {
                                                        validator(_, value) {
                                                            // if (value && !/^([1-9]{1})(\d{14}|\d{18})$/.test(value)) {
                                                            if (value && !/^[0-9]*$/.test(value)) {
                                                                return Promise.reject(new Error('请输入正确的银行卡号！'));
                                                            } else {
                                                                return Promise.resolve();
                                                            }
                                                        },
                                                    }
                                            ]}
                                            >
                                                <Input placeholder='请输入' allowClear/>
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </div>
                                <div className='FormRow'>
                                    <p className='FormRowTitle'>所属组织</p>
                                    <Row style={{margin:'20px 0 0 30px'}}>
                                        <Col>
                                            <span>关键字：</span><Input placeholder='搜组织名称关键字！' style={{width:300}} allowClear onChange={e=>this.setState({orgInputText:e.target.value})} onPressEnter={this.orgSearch} />
                                        </Col>
                                        <Col style={{marginLeft:20}}>
                                            <Button type='primary' onClick={this.orgSearch}>查询</Button>
                                            <Button style={{marginLeft:20}} onClick={this.orgReset}>重置</Button>
                                        </Col>
                                    </Row>
                                    <Row className='FormRowCon'>

                                        <Col span={10} className='orgTreeBox'>
                                            {
                                                treeData && treeData.length ?
                                                <Tree defaultExpandedKeys={[1]} treeData={treeData} titleRender={this.treeTitleRender} onSelect={this.treeSelect} />
                                                :'暂无数据'
                                            }
                                            <div className='orgTreeBoxBtns'>
                                                <Button icon={<CheckOutlined />} disabled={!treeSelectedRows.length} onClick={() => this.selectOrg()}>选择组织</Button>
                                                {
                                                    roleJudgment(userInfo,'PERM_ORGANIZATION_ADD') ?
                                                    <Button icon={<PlusOutlined />} type="primary" onClick={() => this.setState({ OrgAddVisible: true })}>添加组织</Button>
                                                    :null
                                                }
                                            </div>
                                        </Col>
                                        <Col span={14} className='orgTreeSelected'>
                                            <p className='orgTreeSelectedTitle'>该账号所属组织</p>
                                            <div className='orgTreeSelectedCon' style={{overflowY:'auto',height:'400px'}}>
                                                {
                                                    SelectedOrg.map((item, index) => {
                                                        return <div className='orgTreeSelectedConItem' key={index}>
                                                            <div className='orgTreeSelectedConItemTitle'>
                                                                <div style={{color:'#000000'}}>{index+1}. {item.organizationName}</div>
                                                                <div>已配置角色：
                                                                    {
                                                                        item.roleNames ? item.roleNames.join('/') :''
                                                                    }
                                                                </div>
                                                            </div>
                                                            <div className='orgTreeSelectedConItem_Clear'>
                                                                <Popconfirm title={`确定删除${item.organizationName}吗？`} onConfirm={()=>this.SelectedOrgDel(item)} okText="确定" cancelText="取消" >
                                                                    <a href="#" style={{color:'#ff4d4f'}}>删除</a>
                                                                </Popconfirm>
                                                            </div>
                                                            <div className='orgTreeSelectedConItem_Clear' onClick={() => this.SelectedOrgEdit(item)}>修改角色配置</div>
                                                        </div>
                                                    })
                                                }
                                            </div>
                                        </Col>
                                    </Row>
                                    <Row className='KJFW' style={{paddingBottom: '80px'}} >
                                        <Col>
                                        <Form.Item  name="dataAuthority" label="可见范围"  rules={[{ required: true, message: '请选择可见范围！', }]}>
                                            <Radio.Group>
                                                <Radio value={1}>个人数据</Radio>
                                                <Radio value={2}>组织数据</Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                        </Col>
                                    </Row>
                                </div>
                            </Form>
                        </Spin>
                    </div>
                </div>
                <div className='DetailBtns'>
                    <Button type="primary" onClick={this.Save} style={{ marginRight: '10px' }}
                    disabled={!this.state.isLoweLectric && (this.state.isMotorMechanic || this.state.isTM || this.state.isQYJSJL)}
                    loading={loading}>确定</Button>
                    <Button onClick={this.onCancel} loading={loading}>取消</Button>
                </div>
                {
                    <RoleModal visible={roleVisible} data={treeSelectedRows} title={'添加组织'} getRoleCB={this.getRoleCB} handleOk={() => this.setState({ roleVisible: false })} handleCancel={() => this.setState({ roleVisible: false })} />
                }
                {
                    OrgAddVisible &&
                    <OrgAdd visible={OrgAddVisible} title='添加组织' GetData={this.GetOrgData} cb={this.getOrg} onCancel={() => this.setState({ OrgAddVisible: false })} onOk={() => this.setState({ OrgAddVisible: false })} />
                }
                {
                    OrgEditVisible &&
                    <OrgEdit title={title} visible={OrgEditVisible} treeSelectedRows={EditNodeData} GetData={this.GetOrgData} onCancel={() => this.setState({ OrgEditVisible: false })}
                    />
                }
                {
                    SelectOrgVisible &&
                    <SelectOrgModal SelectedOrg={SelectedOrg} visible={SelectOrgVisible} currentOrg={currentOrg} orgType={orgType} parentSelectOrg={treeSelectedRows} cb={this.SelectOrgCB} onCancel={()=>this.setState({SelectOrgVisible:false})} />
                }
                {
                    previewVisible &&
                    <Image
                        src={currentUrl}
                        preview={{
                        visible: previewVisible,
                        onVisibleChange: value => {
                            this.setState({
                                previewVisible: value
                            })
                        },
                        }}
                    />
                    // <PreviewImg data={previewFile} visible={previewVisible} onCancel={ this.setState({ previewVisible: false })}/>
                }
                {
                    <Modal visible={editIDVisible} title='设置身份证号' onOk={handleSetIDNumber} onCancel={() => this.setState({ editIDVisible: false})} maskClosable={false}>
                       <Form
                            layout={'horizontal'}
                            ref={this.IDFormRef}
                        >
                            <Form.Item label="身份证号" name="IDNumber" rules={[
                                 { required: true, message: '请输入身份证号！'},
                                 {
                                     validator(_, value) {
                                         if (value && !/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)) {
                                             return Promise.reject(new Error('请输入正确的身份证号！'));
                                         } else {
                                             return Promise.resolve();
                                         }
                                     },
                                 }
                            ]}>
                                <Input allowClear />
                            </Form.Item>
                        </Form>
                    </Modal>
                }
            </div>
        )
    }
    componentDidMount() {
        let locationParmas = this.props.match.params.data ? JSON.parse(DecryptByAES(this.props.match.params.data)) : {}
        const { Type } = locationParmas
        this.id = locationParmas.id
        this.setState({
            locationParmas, Type
        }, () => {
            this.GetOrgData()
        })
        this.getOrgType()
    }
    initForm = () => {
        this.setState({loading:true},()=>{
            get(allUrl.Authority.userDetail + '/' + this.id).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    // if (Dt.organizations && Dt.organizations.length) {
                    //     Dt.organizations.forEach(item => {
                    //         item.title = item.name
                    //         item.key = item.id
                    //     })
                    // }
                    // 判断该账号是否有门店类型的组织，门店类型组织判断条件为type是1
                    if(Dt.organizations.length) {
                        const storeOrgs = Dt.organizations.filter(i => i.type === 1)
                        this.setState({
                            hasStoreOrg: storeOrgs.length > 0
                        })
                    }
                    let orgArr = []
                    if(Dt.organizationRoles && Dt.organizationRoles.length){
                        Dt.organizationRoles.forEach(item=>{
                            let obj = []
                            if(item.roles && item.roles.length){
                                obj.organizationId = item.organizationId
                                obj.organizationName = item.organizationName
                                obj.roleNames = item.roles.map(item=>item.roleName)
                                obj.roleIds = item.roles.map(item=>item.id)
                                obj.roleArr = item.roles.map(item=>{
                                    return {name:item.roleName,id:item.id}
                                })
                            }
                            orgArr.push(obj)
                    })
                }
                    let fileList = []
                    if((Dt.position === MOTOR_MECHANIC_CODE || TM_CODE || QYJSJL_CODE) && Dt.extendJson && Dt.extendJson.hasElecCert == 1){
                        Dt.extendJson.elecCertUrls.length && Dt.extendJson.elecCertUrls.forEach((i, index) => {
                            fileList.push({
                                uid: index,
                                name: i.substr(i.lastIndexOf('/') + 1),
                                status:'done',
                                url: i
                            })
                        })
                    }
                    console.log('fileList',fileList)
                    this.setState({
                        SelectedOrg: orgArr || [],
                        elecCertUrls: Dt.extendJson ? Dt.extendJson.elecCertUrls : [],
                        fileList,
                        isMotorMechanic: Dt.position === MOTOR_MECHANIC_CODE,
                        isTM: Dt.position === TM_CODE,
                        isQYJSJL: Dt.position === QYJSJL_CODE,
                        isLoweLectric: Dt.extendJson? Dt.extendJson['hasElecCert'] == 1 : false,
                        IDNum: Dt.idNum || '' // 单独存储身份证号
                    }, () => {
                        this.formRef.current.setFieldsValue({
                            userName: Dt.userName,
                            mobilePhone: Dt.mobilePhone,
                            position: Dt.position,
                            dataAuthority: Dt.dataAuthority,
                            // roleIds: Dt.roleIds,
                            inductionTime: Dt.inductionTime ? moment(Dt.inductionTime):null,
                            isOnJob: Dt.isOnJob,
                            email: Dt.email || null,
                            idNum: Dt.idNum || null,
                            bankName: Dt.bankName || null,
                            bankAccount: Dt.bankAccount || null,
                            extendJson: Dt.extendJson,
                            loweLectric: Dt.extendJson ? Dt.extendJson['hasElecCert'] : '',
                            employeeNumber: Dt.employeeNumber
                        })
                    })
                    console.log('this.state.Type',this.formRef.current)
                } else {
                    // message.error(res.msg || '获取用户详情失败！')
                }
                this.setState({loading:false})
            })
        })

    }
    GetOrgData = () => {
        const {orgInputText} = this.state
        post(allUrl.Authority.orgPageList,{name:orgInputText}).then(res => {
            if (res.success) {
                this.setState({ treeData: res.resp })
            } else {
                // message.error(res.msg)
            }
        })
    }
    getOrgType = () =>{
        get(allUrl.Authority.orgTypeList).then(res=>{
            if(res.success){
                let Dt = res.resp
                this.setState({typeList:Dt},()=>{
                    this.getDict()
                })
            }
        })
    }
}
export default connect(({ common }) => {
    const {  userInfo } = common
    return {
         userInfo
    }
})(Detail)
