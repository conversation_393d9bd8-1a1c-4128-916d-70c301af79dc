/*
 * @Author: geyulan <EMAIL>
 * @Date: 2024-09-26 14:52:24
 * @LastEditors: geyulan <EMAIL>
 * @LastEditTime: 2025-03-18 15:25:53
 * @FilePath: /scrm-web/src/pages/FileManage/FileView/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getSeresFileItemDetail, getSeresFileOperateGenerateUrl } from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import PageLoading from '@/components/page-loading';
import { useRequest } from '@/hooks/request';
import React, { FC, useCallback, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { OnDocumentLoadSuccess } from 'react-pdf/dist/cjs/shared/types';
import { useParams } from 'react-router';
import './index.less';

pdfjs.GlobalWorkerOptions.workerSrc = new URL('pdfjs-dist/build/pdf.worker.min.js', import.meta.url).toString();

const FileView: FC = () => {
  const { id } = useParams<{ id: string }>();

  const [pages, setPages] = useState(0);

  const [fileName, setFileName] = useState('');

  const { data: url, loading } = useRequest(
    async () => {
      const [file] = await getSeresFileItemDetail({
        id: id as unknown as number,
      });
      setFileName(file.name as string);
      const [url] = await getSeresFileOperateGenerateUrl({
        objectName: file.ossObjectName as string,
      });
      return url;
    },
    {
      deps: [id],
    }
  );

  const handlePdfLoadSuccess = useCallback<OnDocumentLoadSuccess>((document) => {
    // console.log(document);
    setPages(document.numPages);
  }, []);

  return (
    <PageContainer ghost={false} loading={loading} backable fixedHeader>
      <Document
        loading={<PageLoading />}
        options={{
          cMapUrl: '/cmaps/',
          cMapPacked: true,
        }}
        file={url}
        onLoadSuccess={handlePdfLoadSuccess}
      >
        <h3 style={{ textAlign: 'center', margin: '10px 0' }}>{fileName}</h3>
        {Array.from({ length: pages }).map((_, index) => (
          <Page
            key={index}
            pageIndex={index}
            renderAnnotationLayer={false}
            renderTextLayer={false}
            className={'pdf-page'}
            width={800}
            loading={<PageLoading />}
          />
        ))}
      </Document>
    </PageContainer>
  );
};

export default FileView;
