.car-config-content {
    .top {
        height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 36px;
        background-color: #DAEDFF;
        padding-left: 16px;
    }

    .bottom {
        padding-left: 16px;
        padding-right: 16px;
        padding-bottom: 24px;
        padding-top: 24px;

        .ant-checkbox+span {
            word-wrap: break-word;
            word-break: break-all;
        }

        .ant-row.border {
            border-bottom: 1px dashed #B2BDC6;
        }

        .ant-row:last-child {
            border-bottom: none;
        }

        .custom-divider {
            margin-top: 0;

            &.ant-divider-horizontal.ant-divider-with-text {
                color: rgba(0, 0, 0, 0.65);
                font-size: 14px;

                .ant-divider-inner-text {
                    padding: 0 18px;
                }
            }

            &.ant-divider-horizontal.ant-divider-with-text::before,
            &.ant-divider-horizontal.ant-divider-with-text::after {
                border-top-color: #B8C7D6;
            }
        }

        .divider-blue {
            &.ant-divider-horizontal.ant-divider-with-text {
                color: #1890FF;
            }
        }

        .custom-vertical-divider {
            top: -1px;
            height: 26px;
            margin: 0 8px;
            border-left-color: #B8C7D6;
            margin-right: 5em;
            margin-left: 0;

            &:last-child {
                border: 0
            }
        }

    }
}

.car-config-tabs {
    .ant-tabs-nav {
        margin-bottom: 0;

        .ant-tabs-tab {
            min-width: 80px;
            height: 40px;
            box-sizing: border-box;
            padding: 0 !important;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top-color: rgba(0, 0, 0, 0.15);
            border-left-color: rgba(0, 0, 0, 0.15);
            border-right-color: rgba(0, 0, 0, 0.15);
        }

        .ant-tabs-tab-active {
            background: #1890FF;
            border-radius: 2px 2px 0px 0px;
            border: 1px solid #1890FF !important;

            .ant-tabs-tab-btn {
                color: #ffffff;

                .car-config-tabs-empty {
                    color: #ffffff;
                }
            }
        }
    }

    .ant-tabs-content-holder {
        border: 1px solid #1890FF;
        padding-top: 16px;
    }

    .car-config-tabs-empty {
        color: red;
    }
}