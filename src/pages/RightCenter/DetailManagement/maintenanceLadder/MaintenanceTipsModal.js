import React from 'react'
import { Modal, Tag, Row, Col } from 'antd';
import { getWidth, offsetLeft } from '@/utils/index'
import style from './tipsModal.module.less'

const tagColor = '#1A69E9'

const MaintenanceTipsModal = (props) => {
    const { maintenanceModalVisible, allLadderShow } = props
    return (
        <Modal
            title="全保养阶梯策略说明"
            visible={maintenanceModalVisible}
            maskClosable={false}
            onCancel={allLadderShow}
            width={800}
            // style={{ left: offsetLeft() }}
            style={{ top: 10 }}
            footer={null}
        >
            <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={12}><img src={require('@/assets/img/maintenance/modal-left.png')} style={{ width: '100%' }} /></Col>
                <Col span={12}><img src={require('@/assets/img/maintenance/modal-right.png')} style={{ width: '100%' }} /></Col>
            </Row>
            <div className={style['m-box']}>
                <p className={style['m-box-title']}>每个阶梯计算规则</p>
                <div className={style['m-content']}>
                    <p className={style['m-content-title']}>(1) 第一阶梯：</p>
                    <p>权益生效时间=权益开始时间～权益开始时间<Tag color={tagColor}>+ 第一阶梯限制年限</Tag></p>
                    <p>权益生效里程=权益开始里程～权益开始里程<Tag color={tagColor}>+ 第一阶梯限制里程</Tag></p>
                </div>
                <div className={style['m-content']}>
                    <p className={style['m-content-title']}>(2) 第二阶梯：</p>
                    <p>权益生效时间=第一阶梯权益结束时间+1天～第一阶梯权益结束时间<Tag color={tagColor}>+ 第二阶梯限制年限</Tag></p>
                    <p>权益生效里程=第一阶梯权益结束里程+1～第一阶梯权益结束里程<Tag color={tagColor}>+ 第二阶梯限制里程</Tag></p>
                </div>
                <div className={style['m-content']}>
                    <p className={style['m-content-title']}>(3) 第三阶梯：</p>
                    <p>权益生效时间=第二阶梯权益结束时间+1天～第三阶梯权益结束时间<Tag color={tagColor}>+ 第三阶梯限制年限</Tag></p>
                    <p>权益生效里程=第二阶梯权益结束里程+1～第二阶梯权益结束里程<Tag color={tagColor}>+ 第三阶梯限制里程</Tag></p>
                </div>

                <div className={style['m-content']}>
                    <p className={style['m-content-title']}>以此类推</p>
                    <div className={style['m-content-tips']}>
                        <p><Tag color="warning">例如</Tag>若配置阶梯限制年限为 1 年，阶梯限制里程为 5000 km，则某一账户激活该权益时，权益开始时间为 2021年1月1日，权益开始里程为 0 km，则：</p>
                        <p>（1）第一阶梯：</p>
                        <p className={style['m-content-indent']}>权益生效时间=2021年1月1日～2021年12月31日；</p>
                        <p className={style['m-content-indent']}>权益生效里程=0km～5000km；</p>
                        <p>（2）第二阶梯：</p>
                        <p className={style['m-content-indent']}>权益生效时间=2022年1月1日～2022年12月31日；</p>
                        <p className={style['m-content-indent']}>权益生效里程=5001km～10000（5000+5000）km；</p>
                    </div>
                </div>

            </div >
        </Modal >

    )
}

export default MaintenanceTipsModal