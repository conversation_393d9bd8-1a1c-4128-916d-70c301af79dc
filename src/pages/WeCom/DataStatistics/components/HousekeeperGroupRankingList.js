import React ,{useEffect, useState} from 'react'
import { Modal ,Table ,message} from 'antd'
import allUrl from '@/utils/url'
import {post} from '@/utils/request'
import PublicTooltip from '@/components/Public/PublicTooltip'
import '../index.less'
import moment from 'moment'

export default (props) =>{
    const {visible,title,onCancel} = props
    const [dataSource1,setDataSource1] = useState([])
    const [dataSource2,setDataSource2] = useState([])
    const [total1,setTotal1] = useState(0)
    const [total2,setTotal2] = useState(0)
    const [pageSize1,setPageSize1] = useState(10)
    const [pageSize2,setPageSize2] = useState(10)
    const [current1,setCurrent1] = useState(1)
    const [current2,setCurrent2] = useState(1)
    const [visible1,setVisible1] = useState(false)
    const columns1 = [
        { title: '排名', dataIndex: 'index',width:100, render: (text, record, index) => serialNumber1(current1,pageSize1,index+1) },
        { title: '门店编码', dataIndex: 'index',width:140},
        { title: '用户中心', dataIndex: 'storeFullName1',width:120,render:text=><PublicTooltip title={text}>{text}</PublicTooltip>},
        { title: '群名', dataIndex: 'storeFullName1',width:120},
        { title: '群主', dataIndex: 'storeFullName1',width:120},
        { title: '被回复对话量', dataIndex: 'storeFullName1',width:140},
        { title: '及时响应量', dataIndex: 'storeFullName1',width:140},
        { title: '超10分钟响应量', dataIndex: 'storeFullName1',width:120},
        { title: '超30分钟响应量', dataIndex: 'storeFullName1',width:120},
        { title: '平均响应时长', dataIndex: 'storeFullNam33e1',width:140},
        { title: '平均及时回复率', dataIndex: 'storeFullName1',width:140},
        { title: '历史数据', dataIndex: 'storeFullName1',width:140},
    ]

    const columns2 = [
        { title: '统计时间', dataIndex: 'storeFullNam1e1',width:120},
        { title: '被回复对话量', dataIndex: 'storeFullName1',width:140},
        { title: '及时响应量', dataIndex: 'storeFullName1',width:140},
        { title: '超10分钟响应量', dataIndex: 'storeFullName1',width:120},
        { title: '超30分钟响应量', dataIndex: 'storeFullName1',width:120},
        { title: '平均响应时长', dataIndex: 'storeFullNam33e1',width:140},
        { title: '平均响应时长', dataIndex: 'storeFullNam33e1',children:[
            { title: '当日全国排名', dataIndex: 'storeFullName1',width:140},
            { title: '当日区域排名', dataIndex: 'storeFullName1',width:140},
            { title: '当日门店排名', dataIndex: 'storeFullName1',width:140},
        ]},
        { title: '平均及时回复率', dataIndex: 'storeFullNam33e1',children:[
            { title: '当日全国排名', dataIndex: 'storeFullName1',width:140},
            { title: '当日区域排名', dataIndex: 'storeFullName1',width:140},
            { title: '当日门店排名', dataIndex: 'storeFullName1',width:140},
        ]},

        
    ]

    const serialNumber1 =(pageIndex, pageSize, index)=>{
        return (current1-1) * pageSize + index;
    }

    const PageChange1 = (current, pageSize) => {
        setCurrent1(current)
        setPageSize1(pageSize)
    }

    const PageChange2 = (current, pageSize) => {
        setCurrent2(current)
        setPageSize2(pageSize)
    }

    useEffect(()=>{
        post(allUrl.WeCom.qwStoreGroupStatisticPage,{pageIndex:current1,pageSize:pageSize2}).then(res=>{
            if(res.success){
                setDataSource1(res.resp[0].list)
                setTotal1(res.resp[0].total)
            }else{
                // message.error(res.msg)
            }
        })
    },[current1,pageSize2])

    const InforData1 = {
        columns:columns1,
        dataSource:dataSource1,
        bordered:true,
        rowKey:'id',
        size:'small',
        scroll:{y:'max-content'},
        pagination: {
            pageSize: pageSize1,
            onChange: PageChange1,
            current: current1,
            total: total1,
            showTotal: () => `共${total1}条，${pageSize1}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions:[10,20,50],
            onShowSizeChange: PageChange1,
        },
    }

    const InforData2 = {
        columns:columns2,
        dataSource:dataSource2,
        bordered:true,
        rowKey:'id',
        size:'small',
        scroll:{y:'max-content'},
        pagination: {
            pageSize: pageSize2,
            onChange: PageChange2,
            current: current2,
            total: total2,
            showTotal: () => `共${total2}条，${pageSize2}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions:[10,20,50],
            onShowSizeChange: PageChange1,
        },
    }
    return <Modal title={<div>{title}<span style={{marginLeft:'20px',fontSize:'14px',color: 'rgba(0,0,0,0.45)',fontWeight: 400}}>{`统计截止${moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD 23:59:59')}`}</span></div>} visible={visible} onCancel={onCancel} maskClosable={false} footer={null} width={1300}>
                <Table {...InforData1} />
                <Modal visible={visible1} title={`区域排名明细（${moment().subtract(1, 'day').format('YYYY-MM-DD')}）`} footer={null} onCancel={()=>setVisible1(false)} width={1000}>
                    <Table {...InforData2} />
                </Modal>
            </Modal>
}