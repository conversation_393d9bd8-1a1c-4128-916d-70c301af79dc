/*
 * @Author: geyulan <EMAIL>
 * @Date: 2024-09-26 14:52:24
 * @LastEditors: geyulan <EMAIL>
 * @LastEditTime: 2024-09-29 16:00:33
 * @FilePath: /scrm-web/src/pages/FileManage/FileDetail/undonwload-modal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { postSeresFileStatisticInCompleteListExport } from '@/apis/file-manage';
import ProTable from '@/components/pro-table';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { downloadBlob } from '@/utils/file';
import { Modal, ModalProps } from 'antd';
import React, { FC, useCallback, useState } from 'react';
import { useParams } from 'react-router';
import { StatisticQueryType } from '../constants';
import { useFileIncompleteApi, useStatisticDetailTableColumns } from './hooks';

export interface UnDownloadModalProps extends ModalProps {
  orgCode?: string;
  file?: FileManage.FileItemDetailVO;
}

const UnDownloadModal: FC<UnDownloadModalProps> = ({ file, orgCode, ...props }) => {
  const [downloading, setDownloading] = useState(false);
  const { id } = useParams<{ id: string }>();

  const columns = useStatisticDetailTableColumns(file);
  const handleUnDownloadExportClick = useCallback(async () => {
    try {
      setDownloading(true);
      const blob = await postSeresFileStatisticInCompleteListExport(
        { fileId: id as unknown as number, queryType: StatisticQueryType.NOT_DOWNLOADED, organizeCode: orgCode },
        {
          responseType: 'blob',
        }
      );
      const filename = file?.name?.split('.')[0] ?? '';
      downloadBlob(blob as unknown as Blob, `${filename}未下载人员明细数据.xlsx`);
    } catch (error) {
    } finally {
      setDownloading(false);
    }
  }, [file, id, orgCode]);

  const inCompleteListRequest = useFileIncompleteApi(file);
  const unDownloadTableRequest = usePaginationTableListStyleRequest(async ({ current, pageSize, params }) =>
    inCompleteListRequest({
      fileId: id as unknown as number,
      pageNum: current,
      pageSize,
      queryType: StatisticQueryType.NOT_DOWNLOADED,
      ...params,
    })
  );
  return (
    <Modal maskClosable={false} width={860} footer={null} title="文件未下载人员明细" {...props}>
      <ProTable
        columns={columns}
        ghost
        rowKey={'serialNo'}
        request={unDownloadTableRequest}
        params={{ organizeCode: orgCode }}
        actions={[
          {
            children: '导出数据',
            type: 'primary',
            onClick: handleUnDownloadExportClick,
            loading: downloading,
          },
        ]}
      />
    </Modal>
  );
};

export default UnDownloadModal;
