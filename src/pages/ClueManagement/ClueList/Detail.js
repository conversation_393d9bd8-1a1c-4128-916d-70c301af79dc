import React, { useEffect, useState } from 'react'
import { message, Button, Input, Row, Col, Form, Card, Tag, Radio, Select, Collapse, Popconfirm, Divider,Timeline } from 'antd'
import { connect } from 'react-redux'
import { PlusOutlined, DownOutlined } from '@ant-design/icons';
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get, post } from '@/utils/request'
import allUrl from '@/utils/url'
import { useSelector, useDispatch } from 'react-redux'
import moment from 'moment';
import '../index.less'
import './Detail.less'
const { Option } = Select
const { TextArea } = Input
const { Panel } = Collapse

const FollowUpRecords = (props) => {
    console.log(props)
    const {FollowUpRecordData} = props
    const [pageSize, setPageSize] = useState(10)
    const [pageNum, setPageNum] = useState(10)
    const [total, setTotal] = useState(0)
    const [isExpand, setIsExpand] = useState(true)
    // const [FollowUpRecordData, setFollowUpRecordData] = useState([])
    // useEffect(() => {
    //     if(planId){
    //         let params = { planId, pageSize, pageNum: 1 }
    //         get(allUrl.ClueManagement.getFollowRecordList, { ...params }).then(res => {
    //             console.log(res)
    //             if (res.success) {
    //                 setFollowUpRecordData(res.resp[0].list)
    //                 setTotal(res.resp[0].total)
    //             } else {
    //                 // message.error(res.msg)
    //             }
    //         })
    //     }
    // }, [pageSize,planId])
    return <>
        {/* {
            FollowUpRecordData.length ? FollowUpRecordData.map((item, index) => {
                return <div className='FollowUpRecordItem' key={index}>
                            <div className='FollowUpRecordTime'><span style={{ background: index === 0 ? '#1890FF' : '#BCBCBC' }}></span>{item.createTime?moment(item.createTime).format('YYYY-MM-DD HH:mm:ss'):''} 更新</div>
                            <div className='FollowUpRecordCon'>
                                {item.content}
                            </div>
                            <p className='FollowUpRecordName'>记录人：{item.createByName}</p>
                        </div>
            }) : null

        }
        {
            FollowUpRecordData.length !== total ?
                <div className='more'><span style={{ cursor: 'pointer' }} onClick={() => {
                    setPageSize(pageSize + 5)
                }}>更多跟进记录 <DownOutlined /></span></div>
                : <div className='more'><span style={{ cursor: 'pointer' }}>没有更多记录了～ </span></div>
        } */}
                {<div className='PunchInRecord'>
                    <Timeline>
                        {   FollowUpRecordData&&   
                            FollowUpRecordData.map((item, index) => {
                                return (isExpand ? index < 5 : index >= 0)
                                    && <Timeline.Item key={index} color={index === 0 ? 'blue' : 'gray'}>
                                        <div className='FollowUpRecordItem'>
                                            <span className='FollowUpRecordTime' style={{marginRight:'20px'}}>{item.createTime} 更新</span>
                                            {item.content?
                                            item.content.split('\n').map((ele,elindex)=>{
                                                return <div className='FollowUpRecordCon' key ={elindex}>{ele}</div>
                                            }):null
                                            }
                                            <p className='FollowUpRecordName'>记录人：{item.createByName}</p>
                                        </div>
                                    </Timeline.Item>
                            })
                        }
                    </Timeline>
                        <p className='more'>
                            {isExpand ?
                                <span onClick={() => {
                                    setIsExpand(false)
                                }}>更多跟进记录<DownOutlined /></span>
                                : <span>没有更多记录了～</span>
                            }
                        </p>
                </div>}

    </>
}

const IntentInformation = (props) => {
    let data = JSON.parse(JSON.stringify(props.data))
    const [visible,setVisible] = useState(true)
    if(visible && data.length>1){
        data = [data[0]]
    }
    return <div>
        {
            data.length ? data.map((ele, index) => {
                return <div key={index}>
                    <Divider />
                    <div className='IntentionInfo'>
                        <div className='IntentionInfoTitle'>
                            <span className='IntentionInfoLeft'>
                                <span className='IntentionInfoIndex'>{index + 1}</span>意向信息
                            </span>
                            {/* <span className='IntentionInfoRight'>
                                2021年5月17日 07:36 转入
                            </span> */}
                        </div>
                        <div className='IntentionInfoCon'>
                            <div className='IntentionInfoConItem'><span className='IntentionInfoConTitle'>意向车型：</span>{ele.vehicleModelName}</div>
                            {/* <div className='IntentionInfoConItem'>
                                {ele.carColorImg ? <img src={ele.carColorImg} alt='颜色' /> : null}
                                {ele.InteriorImg ? <img src={ele.InteriorImg} alt='车型' /> : null}
                            </div> */}
                            <div className='IntentionInfoConItem'><span className='IntentionInfoConTitle'>意向颜色：</span>{ele.exteriorColorName}</div>
                            <div className='IntentionInfoConItem'><span className='IntentionInfoConTitle'>意向内饰：</span>{ele.interiorColorName}</div>
                            {/* {ele.buyWay?<div className='IntentionInfoConItem'><span className='IntentionInfoConTitle'>购买方式倾向：</span>{ele.buyWay === 1?'全款':'贷款'}</div>:''} 
                            {ele.intentionBuyTimeMeaning ?<div className='IntentionInfoConItem'><span className='IntentionInfoConTitle'>购买时间：</span>{ele.intentionBuyTimeMeaning}</div>:''}
                            {ele.remark?<div className='IntentionInfoConItem'><span className='IntentionInfoConTitle'>其他意向备注：</span>{ele.remark}</div>:''} */}
                            
                        </div>
                    </div>
                </div>
            }):null
        }
        {
            visible && props.data.length >1?
            <div className='more'><span style={{ cursor: 'pointer' }} onClick={() => {
                setVisible(false)
            }}>更多意向信息 <DownOutlined /></span></div>
            :<div className='more'><span style={{ cursor: 'pointer' }}>没有更多意向了～</span></div>
        }
    </div>


}



const Detail = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [form] = Form.useForm()
    const [id, setId] = useState(null)
    const [locationParmas, setLocationParmas] = useState({})
    const [Type, setType] = useState('')
    const [intentionData, setIntentionData] = useState([])
    const [DictArr, setDictArr] = useState({})
    const [planList, setPlanList] = useState([])
    const [clueBaseInfo,setclueBaseInfo] = useState({})
    const [defaultActiveKey, setdefaultActiveKey] = useState([])
    const [recordVisibled, setRecordVisibled] = useState({})

    const Save = () => {
        form.validateFields().then(values => {
            console.log(values)
            intentionData.forEach(item => {
                item.id = item.id || 0
                delete item.key
            })
            if (id == '0') {
                values.userId = userInfo.id
                values.userName = userInfo.userName
                values.intention = intentionData
                if (intentionData.length > 5) {
                    message.error('线索意向不能超过5条！')
                    return
                }
                post(allUrl.ClueManagement.addClue, { ...values }).then(res => {
                    console.log(res)
                    if (res.success) {
                        message.success(res.msg)
                        history.goBack()
                    } else {
                        // message.error(res.msg)
                    }
                })
            } else {
                values.planId = locationParmas.planId
                post(allUrl.ClueManagement.updateClue, { planInfo: values, intentionList: intentionData }).then(res => {
                    console.log(res)
                    if (res.success) {
                        message.success(res.msg)
                        history.goBack()
                    } else {
                        // message.error(res.msg)
                    }
                })
            }
        })
    }
    const onCancel = () => {
        history.goBack()
    }
    const getData = (params) => {
        let query = {onlyLeadId:params.onlyLeadId,dealerCode:params.dealerCode}
        // if(userInfo && userInfo.position.indexOf('POST_SHOP_MANAGER') >-1){
        //     query.onlyLeadId = params.onlyLeadId
        // }else{
        //     query.planId = params.planId
        // }
        get(allUrl.ClueManagement.getLeadInfo, { ...query}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setclueBaseInfo(Dt)
                if(Dt.leadSaleInfoVO){
                    setPlanList(Dt.leadSaleInfoVO)
                }
                // if (Dt.planList && Dt.planList.length) {
                //     let arr = []
                //     let visObj = { ...recordVisibled }
                //     if (params.Type === 'EDIT') {
                //         let data = Dt.planList[0]
                //         form.setFieldsValue({
                //             customerName: data.planInfo.userName,
                //             mobilePhone: data.planInfo.mobilePhone,
                //             gender:data.planInfo.gender? String(data.planInfo.gender):'',
                //             planStatus: data.planInfo.planStatus?String(data.planInfo.planStatus):'',
                //             planTag: data.planInfo.planTag ? String(data.planInfo.planTag):'',
                //             intentionLevel: data.planInfo.intentionLevel?String(data.planInfo.intentionLevel):'',
                //             clueRemark: data.planInfo.remark,
                //         })
                //         data.intentionList.forEach((item, index) => {
                //             item.intentionCarType = String(item.intentionCarType)
                //             item.intentionCarColor = String(item.intentionCarColor)
                //             item.intentionInterior = String(item.intentionInterior)
                //             item.buyWay = String(item.buyWay)
                //             item.intentionBuyTime = String(item.intentionBuyTime)
                //             item.intentionRemark = String(item.remark)
                //             item.key = index
                //         })
                //         setIntentionData(data.intentionList)
                //         setPlanList(Dt.planList)
                //     } else {
                //         console.log(Dt)
                //         setPlanList(Dt.planList)
                //         setclueBaseInfo(Dt.clueBaseInfo)
                //         setRecordVisibled(visObj)
                //         setdefaultActiveKey(arr)
                //     }
                // }
            } else {
                // message.error(res.msg)
            }
        })
    }
    const Add = () => {
        let temp = [...intentionData]
        temp.push({
            intentionCarType: null,
            intentionCarColor: null,
            intentionInterior: null,
            buyWay: null,
            intentionBuyTime: null,
            intentionRemark: '',
            id: 0,
            key: temp.length
        })
        setIntentionData(temp)
    }
    const Del = (index) => {
        let temp = [...intentionData]
        temp = temp.filter(item => item.key !== index)
        setIntentionData(temp)
    }
    const fieldsChange = (fields, value, key) => {
        let temp = [...intentionData]
        let target = temp.filter(item => item.key === key)[0]
        target[fields] = value
        setIntentionData(temp)
    }
    const getDict = () => {
        let getDict1 = get(allUrl.ClueManagement.getDict, { dicCode: 'scrm_clue_planStatus' }) //计划状态
        let getDict2 = get(allUrl.ClueManagement.getDict, { dicCode: 'scrm_clue_planTag' }) //计划标签
        let getDict3 = get(allUrl.ClueManagement.getDict, { dicCode: 'scrm_clue_intentionLevel' }) //意向登记
        let getDict4 = get(allUrl.ClueManagement.getDict, { dicCode: 'scrm_clue_carType' }) //车辆类型
        let getDict5 = get(allUrl.ClueManagement.getDict, { dicCode: 'scrm_clue_carColor' }) //车辆颜色
        let getDict6 = get(allUrl.ClueManagement.getDict, { dicCode: 'scrm_clue_carInterior' }) //车内饰
        let getDict7 = get(allUrl.ClueManagement.getDict, { dicCode: 'scrm_clue_buyTime' }) //购买时间
        const DictArray = [
            'scrm_clue_planStatus', 'scrm_clue_planTag', 'scrm_clue_intentionLevel', 'scrm_clue_carType',
            'scrm_clue_carColor', 'scrm_clue_carInterior', 'scrm_clue_buyTime'
        ]
        Promise.all([getDict1, getDict2, getDict3, getDict4, getDict5, getDict6, getDict7]).then(res => {
            let obj = {}
            res.forEach((item, index) => {
                if (item.success) {
                    obj[DictArray[index]] = item.resp[0]
                } else {
                    // message.error(item.msg)
                }
            })
            setDictArr(obj)
        })
    }
    useEffect(() => {
        // getDict()
    }, [])
    useEffect(() => {
        const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}
        setLocationParmas(locationParmas)
        setId(locationParmas.onlyLeadId)
        setType(locationParmas.Type)
        console.log(locationParmas)
        if (locationParmas.onlyLeadId) {
            getData(locationParmas)
        } else {
            form.setFieldsValue({
                planStatus:'1'
            })
            // let temp = [...intentionData]
            // temp.push(
            //     {
            //         intentionCarType: null,
            //         intentionCarColor: null,
            //         intentionInterior: null,
            //         buyWay: null,
            //         intentionBuyTime: null,
            //         intentionRemark: '',
            //         id: 0,
            //         key: temp.length
            //     }
            // )
            // setIntentionData(temp)
        }
    }, [userInfo])
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 },
    }
    // let SalesInfo = planList.length ? planList[0]:null
    // if(JSON.stringify(DictArr) !=='{}' && planList.length){
    //     console.log(DictArr)
    //     console.log(planList)
    //     let formList = planList[0].planInfo
    //     if(formList.planStatus === 1){
    //         DictArr.scrm_clue_planStatus = DictArr.scrm_clue_planStatus.filter(item=>item.entryValue !=='2')
    //     }else if(formList.planStatus === 2){
    //         DictArr.scrm_clue_planStatus = DictArr.scrm_clue_planStatus.filter(item=>item.entryValue !=='1')
    //     }
    // }
    return <div className='PublicDetail ClueDetail'>
        <div className='DetailBox'>
            <div className='DetailTitle'>{locationParmas.title}</div>
            <div className='DetailCon'>
                {
                    Type === 'LookAt' ?
                        <div className='ClueDetailLookAt'>
                            {
                                // userInfo && userInfo.position.indexOf('POST_SHOP_MANAGER') > -1 ?
                                    <div>
                                        <Card title='线索初始信息' bordered={false}>
                                            <div className='InitialInfo'>
                                                <div className='InitialInfo_Personal'>
                                                    <span className='InitialInfo_Name'>{clueBaseInfo.customerName ?clueBaseInfo.customerName :''}</span>
                                                    <span className='InitialInfo_Gender'>{clueBaseInfo.gender ?clueBaseInfo.gender === 1?'先生':'女士' :''}</span>
                                                    <span>
                                                    {clueBaseInfo.gender ?clueBaseInfo.gender === 1?
                                                        <img src={require('@/assets/img/gender_male.png')} alt='男' />
                                                    : <img src={require('@/assets/img/gender_female.png')} alt='女' />
                                                    :''}
                                                    </span>
                                                    <div className='InitialInfo_PhoneBox'>
                                                        <span>
                                                            <img src={require('@/assets/img/phone.png')} style={{ width: '18px' }} alt='电话号码' />
                                                            <span className='InitialInfo_Phone'>{clueBaseInfo.mobilePhone?clueBaseInfo.mobilePhone	 :''}</span>
                                                            <span> 
                                                            {clueBaseInfo.leadStatus && clueBaseInfo.leadStatus === 1 &&
                                                                <Tag color="processing">待分配门店</Tag>   //计划状态
                                                            }
                                                            {clueBaseInfo.leadStatus && clueBaseInfo.leadStatus === 2 &&
                                                                <Tag color="processing">待分配销售</Tag>   //计划状态
                                                            }
                                                            {clueBaseInfo.leadStatus && clueBaseInfo.leadStatus === 3 &&
                                                                <Tag color="processing">待跟进</Tag>   //计划状态
                                                            }
                                                            {clueBaseInfo.leadStatus && clueBaseInfo.leadStatus === 4  &&
                                                                <Tag color="processing">跟进中</Tag>   //计划状态
                                                            }
                                                            {clueBaseInfo.leadStatus && clueBaseInfo.leadStatus === 5  &&
                                                                <Tag color="processing">战败</Tag>   //计划状态
                                                            }
                                                            {clueBaseInfo.leadStatus && clueBaseInfo.leadStatus === 6  &&
                                                                <Tag color="processing">战赢待确认</Tag>   //计划状态
                                                            }
                                                            {clueBaseInfo.leadStatus && clueBaseInfo.leadStatus === 7  &&
                                                                <Tag color="processing">战赢</Tag>   //计划状态
                                                            }
                                                            </span>
                                                           
                                                            <span className='InitialInfo_Status'>{clueBaseInfo.isInvitation? <Tag >已邀约</Tag> :<Tag >未邀约</Tag>}</span>
                                                            <span className='InitialInfo_Status'>{clueBaseInfo.isTestDriver?<Tag >已试驾</Tag> :<Tag >未试驾</Tag>}</span>
                                                            <span className='InitialInfo_Status'>{clueBaseInfo.isSettled?<Tag >已大定</Tag> :<Tag >未大定</Tag>}</span>

                                                        </span>
                                                        <span className='InitialInfo_CreateInfo'>
                                                            {clueBaseInfo.createTime?clueBaseInfo.createTime	 :''} 新增
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </Card>
                                        <div style={{ width: '100%', height: '24px', background: '#f0f2f5' }}></div>
                                        <Card title='跟进销售顾问'>
                                            <Collapse defaultActiveKey={['0', '1','2']}>
                                                {
                                                    planList.map((item, index) => {
                                                        return <Panel header={item.saleConsultantName+'@'+item.dealerName} key={`${index}`}>
                                                            <div className='MaintenanceInfo'>
                                                                <p className='MaintenanceInfo_title'>顾问维护信息</p>
                                                                <div className='InitialInfo'>
                                                                    <div className='InitialInfo_Personal'>
                                                                        <span className='InitialInfo_NameBox'>
                                                                            <span >
                                                                                <span className='InitialInfo_Name'>{clueBaseInfo.customerName}</span>
                                                                                <span className='InitialInfo_Gender'>{clueBaseInfo.gender === 1 ? '先生' : '女士'}</span>
                                                                                <span>
                                                                                    {
                                                                                        clueBaseInfo.gender === 1 ?
                                                                                            <img src={require('@/assets/img/gender_male.png')} alt='男' />
                                                                                            : <img src={require('@/assets/img/gender_female.png')} alt='女' />
                                                                                    }
                                                                                </span>
                                                                            </span>
                                                                            <span className='InitialInfo_CreateInfo'>{clueBaseInfo.createTime} 新增</span>
                                                                        </span>
                                                                        <div className='InitialInfo_PhoneBox'>
                                                                            <span>
                                                                                <img src={require('@/assets/img/phone.png')} alt='电话号码' />
                                                                                <span className='InitialInfo_Phone'>{clueBaseInfo.mobilePhone}</span>
                                                                            </span>
                                                                            <span >
                                                                                {item.planStatus && item.planStatus === 1 &&
                                                                                    <Tag color="processing">待分配门店</Tag>   //计划状态
                                                                                }
                                                                                {item.planStatus && item.planStatus === 2 &&
                                                                                    <Tag color="processing">待分配销售</Tag>   //计划状态
                                                                                }
                                                                                {item.planStatus && item.planStatus === 3 &&
                                                                                    <Tag color="processing">待跟进</Tag>   //计划状态
                                                                                }
                                                                                {item.planStatus && item.planStatus === 4  &&
                                                                                    <Tag color="processing">跟进中</Tag>   //计划状态
                                                                                }
                                                                                {item.planStatus && item.planStatus === 5 &&
                                                                                    <Tag color="processing">战败</Tag>   //计划状态
                                                                                }
                                                                                {item.planStatus && item.planStatus === 6  &&
                                                                                    <Tag color="processing">战赢待确认</Tag>   //计划状态
                                                                                }
                                                                                {item.planStatus && item.planStatus === 7  &&
                                                                                    <Tag color="processing">战赢</Tag>   //计划状态
                                                                                }
                                                                                {
                                                                                    item.intentionLevel ===1 &&
                                                                                    <Tag>高意向</Tag>
                                                                                }
                                                                                {
                                                                                    item.intentionLevel ===2 &&
                                                                                    <Tag>中意向</Tag>
                                                                                }
                                                                                {
                                                                                    item.intentionLevel ===3 &&
                                                                                    <Tag>低意向</Tag>
                                                                                }
                                                                                {
                                                                                    item.isTestDriver ===1 ?
                                                                                    <Tag>已试驾</Tag>:<Tag>未试驾</Tag>
                                                                                }
                                                                            </span>
                                                                        </div>
                                                                        <div className='InitialInfo_Remarks'>
                                                                            备注：{item.remark ? item.remark:''}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <IntentInformation data={item.intentionInfo?item.intentionInfo:[]} />
                                                            </div>
                                                            <Divider />

                                                            <div className='FollowUpRecord'>
                                                                <p className='FollowUpRecordTitle'>顾问跟进记录</p>
                                                                <FollowUpRecords FollowUpRecordData={item.followRecordList} />
                                                            </div>
                                                        </Panel>
                                                    })
                                                }
                                            </Collapse>
                                        </Card>
                                    </div>
                        //             : <div className='xiaoshouinfo'>
                        //                 <Card>
                        //                     <div className='MaintenanceInfo'>
                        //                         <div className='InitialInfo'>
                        //                             <div className='InitialInfo_Personal'>
                        //                                 <span className='InitialInfo_NameBox'>
                        //                                     <span >
                        //                                         <span className='InitialInfo_Name'>{SalesInfo ? SalesInfo.planInfo.userName :''}</span>
                        //                                         <span className='InitialInfo_Gender'>{SalesInfo ? SalesInfo.planInfo.gender === 1?'先生':'女士' :''}</span>
                        //                                         <span>
                        //                                             {
                        //                                                 SalesInfo ? SalesInfo.planInfo.gender === 1 ?
                        //                                                 <img src={require('@/assets/img/gender_male.png')} alt='男' />
                        //                                                 :<img src={require('@/assets/img/gender_female.png')} alt='女' />
                        //                                                 :''
                        //                                             }
                        //                                         </span>
                        //                                     </span>
                        //                                     <span className='InitialInfo_CreateInfo'>{SalesInfo ? SalesInfo.planInfo.createTime :''} 新增</span>
                        //                                 </span>
                        //                                 <div className='InitialInfo_PhoneBox'>
                        //                                     <span>
                        //                                         <img src={require('@/assets/img/phone.png')} alt='电话号码' />
                        //                                         <span className='InitialInfo_Phone'>{SalesInfo ? SalesInfo.planInfo.mobilePhone :''}</span>
                        //                                     </span>
                        //                                     <span className='InitialInfo_CreateInfo'>
                        //                                         {SalesInfo && SalesInfo.planInfo.planStatusMeaning && SalesInfo.planInfo.planStatusMeaning === '跟进中' &&
                        //                                             <Tag color="blue">{SalesInfo.planInfo.planStatusMeaning}</Tag>   //计划状态
                        //                                         }
                        //                                         {SalesInfo && SalesInfo.planInfo.planStatusMeaning && SalesInfo.planInfo.planStatusMeaning === '待跟进' &&
                        //                                             <Tag color="gold">{SalesInfo.planInfo.planStatusMeaning}</Tag>   //计划状态
                        //                                         }
                        //                                         {SalesInfo && SalesInfo.planInfo.planStatusMeaning && SalesInfo.planInfo.planStatusMeaning === '战败' &&
                        //                                             <Tag color="red">{SalesInfo.planInfo.planStatusMeaning}</Tag>   //计划状态
                        //                                         }
                        //                                         {SalesInfo && SalesInfo.planInfo.planStatusMeaning && SalesInfo.planInfo.planStatusMeaning === '战赢' &&
                        //                                             <Tag color="green">{SalesInfo.planInfo.planStatusMeaning}</Tag>   //计划状态
                        //                                         }
                        //                                         {
                        //                                             SalesInfo && SalesInfo.planInfo.planTagMeaning &&
                        //                                             <Tag>{SalesInfo.planInfo.planTagMeaning}</Tag>
                        //                                         }
                        //                                         {
                        //                                             SalesInfo && SalesInfo.planInfo.intentionLevelMeaning &&
                        //                                             <Tag>{SalesInfo.planInfo.intentionLevelMeaning}</Tag>
                        //                                         }
                        //                                     </span>
                        //                                 </div>
                        //                                 <div className='InitialInfo_Remarks'>
                        //                                     备注：{SalesInfo && SalesInfo.planInfo ? SalesInfo.planInfo.remark:''}
                        //                                 </div>
                        //                             </div>
                        //                         </div>
                        //                         <IntentInformation data={SalesInfo && SalesInfo.intentionList ? SalesInfo.intentionList:[]} />
                        //                     </div>

                        //                 </Card>
                        //                 <div style={{ width: '100%', height: '24px', background: '#f0f2f5' }}></div>
                        //                 <Card title='跟进记录'>
                        //                     <div className='FollowUpRecord'>
                        //                         <p className='FollowUpRecordTitle'>顾问跟进记录</p>
                        //                         <FollowUpRecords planId={SalesInfo && SalesInfo.planInfo ? SalesInfo.planInfo.planId :''} />
                        //                     </div>
                        //                 </Card>
                        //             </div>
                            }
                        </div>
                        : <Form
                            {...formItemLayout}
                            layout={'vertical'}
                            form={form}
                            initialValues={{ remember: true, }}
                        >
                            <div className='FormRow'>
                                <p className='FormRowTitle'>账号信息</p>
                                <Row className='FormRowCon'>
                                    <Col span={8}>
                                        <Form.Item label="姓名" name="customerName" rules={[{ required: true, message: '请输入姓名！', }]}>
                                            <Input placeholder="请输入..." allowClear />
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item
                                            label="手机号"
                                            name="mobilePhone"
                                            rules={[
                                                { required: true, message: '请输入手机号！', },
                                                {
                                                    validator(_, value) {
                                                        if (value && !/^[1][1,2,3,4,5,6,7,8,9][0-9]{9}$/.test(value)) {
                                                            return Promise.reject(new Error('请输入正确的手机号！'));
                                                        } else {
                                                            return Promise.resolve();
                                                        }
                                                    },
                                                }
                                            ]}>
                                            <Input placeholder="请输入..." allowClear />
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="性别" name="gender" rules={[{ required: false, message: '请选择性别！', }]}>
                                            <Select allowClear placeholder='请选择...'>
                                                <Option value={'1'}>男</Option>
                                                <Option value={'2'}>女</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="线索状态" name="planStatus" rules={[{ required: false, message: '请选择线索状态！', }]}>
                                            <Select allowClear placeholder='请选择...' disabled={Type==='Add'?true:false}>
                                                {
                                                    DictArr.scrm_clue_planStatus ? DictArr.scrm_clue_planStatus.map(item => <Option key={item.entryValue} value={item.entryValue}>{item.entryMeaning}</Option>) : null
                                                }
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="线索标签" name="planTag" rules={[{ required: false, message: '请选择线索标签！', }]}>
                                            <Select allowClear placeholder='请选择...'>
                                                {
                                                    DictArr.scrm_clue_planTag ? DictArr.scrm_clue_planTag.map(item => <Option key={item.entryValue} value={item.entryValue}>{item.entryMeaning}</Option>) : null
                                                }
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="意向等级" name="intentionLevel" rules={[{ required: false, message: '请选择意向等级！', }]}>
                                            <Select allowClear placeholder='请选择...'>
                                                {
                                                    DictArr.scrm_clue_intentionLevel ? DictArr.scrm_clue_intentionLevel.map(item => <Option key={item.entryValue} value={item.entryValue}>{item.entryMeaning}</Option>) : null
                                                }
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="备注" name="clueRemark" rules={[{ required: false, message: '请填写备注！', }]}>
                                            <TextArea allowClear placeholder='请输入...' autoSize={{ minRows: 2, maxRows: 4 }} />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </div>
                            {
                                intentionData.map((item, index) => {
                                    return <div className='FormRow' key={index}>
                                        <div className='FormRowTitle' style={{ display: 'flex', justifyContent: 'space-between', marginRight: '32px' }}>
                                            <div>
                                                <span className='FormRowTitleIndex'>{index + 1}</span>
                                                线索意向信息
                                            </div>
                                            <div>
                                                <Popconfirm
                                                    title={`确定删除线索意向信息 ${index + 1} 吗？`}
                                                    onConfirm={() => Del(index)}
                                                    okText="确定"
                                                    cancelText="取消"
                                                >
                                                    <div className='del'>删除</div>
                                                </Popconfirm>
                                            </div>
                                        </div>
                                        <Row className='FormRowCon'>
                                            <Col span={8}>
                                                <Form.Item label="意向车型">
                                                    <Select allowClear placeholder='请选择...' onChange={(e) => fieldsChange('intentionCarType', e, item.key)} value={item.intentionCarType}>
                                                        {
                                                            DictArr.scrm_clue_carType ? DictArr.scrm_clue_carType.map(item => <Option key={item.entryValue} value={item.entryValue}>{item.entryMeaning}</Option>) : null
                                                        }
                                                    </Select>
                                                </Form.Item>
                                            </Col>
                                            <Col span={24} style={{ display: 'flex' }}>
                                                <Col span={8}>
                                                    <Form.Item label="意向颜色">
                                                        <Radio.Group value={item.intentionCarColor} onChange={(e) => fieldsChange('intentionCarColor', e.target.value, item.key)}>
                                                            {
                                                                DictArr.scrm_clue_carColor ? DictArr.scrm_clue_carColor.map(item =>
                                                                    <Radio key={item.entryValue} value={item.entryValue}>
                                                                        <span className={`yxys`}>{<img src={item.extendFiled2} style={{ width: '30px', height: '30px' }} />}</span>
                                                                        {item.entryMeaning}
                                                                    </Radio>
                                                                ) : null
                                                            }
                                                        </Radio.Group>
                                                    </Form.Item>
                                                </Col>
                                                <Col span={8}>
                                                    <Form.Item label="意向内饰">
                                                        <Radio.Group value={item.intentionInterior} onChange={(e) => fieldsChange('intentionInterior', e.target.value, item.key)}>
                                                            {
                                                                DictArr.scrm_clue_carInterior ? DictArr.scrm_clue_carInterior.map(item =>
                                                                    <Radio key={item.entryValue} value={item.entryValue}>
                                                                        <span className={`yxys`}>{<img src={item.extendFiled2} style={{ width: '30px', height: '30px' }} />}</span>
                                                                        {item.entryMeaning}
                                                                    </Radio>
                                                                ) : null
                                                            }
                                                        </Radio.Group>
                                                    </Form.Item>
                                                </Col>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label="购买方式倾向">
                                                    <Select allowClear placeholder='请选择...' onChange={(e) => fieldsChange('buyWay', e, item.key)} value={item.buyWay}>
                                                        <Option value={'1'}>全款</Option>
                                                        <Option value={'2'}>贷款</Option>
                                                    </Select>
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label="意向购买时间">
                                                    <Select allowClear placeholder='请选择...' onChange={(e) => fieldsChange('intentionBuyTime', e, item.key)} value={item.intentionBuyTime}>
                                                        {
                                                            DictArr.scrm_clue_buyTime ? DictArr.scrm_clue_buyTime.map(item => <Option key={item.entryValue} value={item.entryValue}>{item.entryMeaning}</Option>) : null
                                                        }
                                                    </Select>
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label="其他意向备注">
                                                    <TextArea allowClear placeholder='请输入...' autoSize={{ minRows: 1, maxRows: 3 }} value={item.intentionRemark} onChange={(e) => fieldsChange('intentionRemark', e.target.value, item.key)} />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </div>
                                })
                            }
                            <div className='FormRow' style={{ height: '90px', padding: '0 32px' }}>
                                <div className={`Add ${intentionData.length===5?'AddDis':''}`} onClick={intentionData.length<5?Add:console.log()}>
                                    <PlusOutlined /> 添加更多意向
                                </div>
                            </div>
                        </Form>
                }
            </div>
        </div>
        <div className='DetailBtns'>
            {
                Type === 'LookAt' ? null :
                    <Button type="primary" onClick={Save} style={{ marginRight: '10px' }}>确定</Button>
            }
            <Button onClick={onCancel}>取消</Button>
        </div>

    </div>
}
export default Detail