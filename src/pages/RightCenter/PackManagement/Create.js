import React, { useEffect, useState, useRef, createContext } from 'react';
import { useSelector } from 'react-redux';
import {
  Table,
  message,
  Button,
  Descriptions,
  Row,
  Col,
  Popconfirm,
  Modal,
  Input,
  Form,
  Select,
  Spin,
  Radio,
} from 'antd';
import '../index.less';
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { UpOutlined, DownOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import moment from 'moment';
import PublicTooltip from '@/components/Public/PublicTooltip';
import { DecryptByAES } from '@/components/Public/Decrypt';
import CreateForm from './CreateForm';
import RelativeModal from './RelativeModal';
import UpgradeModal from './upgrade/UpgradeModal';
import CombineModal from './combine/CombineModal';
import styles from '../antd.module.less';
import PublicTable from './PublicTable';
import Logs from '../components/Logs';
import upgradeStyle from './upgrade/upgrade.module.less';
import ExpandedRowComponent from './upgrade/ExpandedRowComponent';
import { detailFormConfig, outsideStrategyConfig, strategyConfig } from './upgrade/upgradeFormConfig';
import { UPGRADE_DETAILAPI } from './upgradeSetting';
import { roleJudgment } from '@/utils/authority';
import _ from 'lodash';
export const InjectData = createContext(null);
const { confirm } = Modal;

const PackManagementDetail = props => {
  const [form] = Form.useForm();
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [id, setId] = useState(null);
  const [locationParmas, setLocationParmas] = useState({});
  const [Type, setType] = useState('');
  const [title, setTitle] = useState('');
  const [isItemLabel, setIsItemLabel] = useState(false);
  const [loading, setLoading] = useState(false);

  const [details, setDetails] = useState([]); // 权益明细
  const [packNo, setPackNo] = useState(''); // 权益明细编码
  const [dataDetail, setDataDetail] = useState(); // 权益包详情
  const [tableList, setTableList] = useState([]); // 关联列表
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isUpgradeModalVisible, setIsUpgradeModalVisible] = useState(false);
  const [isCombineModalVisible, setIsCombineModalVisible] = useState(false);
  const [groupDetails, setGroupDetails] = useState([]); //驾行安心包
  const [allGroupDetailNos, setAllGroupDetailNos] = useState([]); //过滤出来驾行安心包中所有的detailNos
  const [carTypeHasValue, setCarTypeHasValue] = useState(false);
  const [carConfigData, setCarConfigData] = useState([]);
  const [typeValue, setTypeValue] = useState('1');
  const [newStrategyConfig, setNewStrategyConfig] = useState();
  const [modalUpgradeLoading, setModalUpgradeLoading] = useState(false);
  const [modalUpgradeDetail, setModalUpgradeDetail] = useState({}); // 传递给升级弹窗的数据
  const [reciveModalDetail, setReciveModalDetail] = useState(); // 升级弹窗返回的数据
  const [expandedRowKeys, setExpandedRowKeys] = useState([]); // table需要展开的行
  const [expandedDisplays, setExpandedDisplays] = useState({}); // 子组件修改的聚合参数
  const [updateDetailNo, setUpdateDetailNo] = useState([]); //有更新的明细编码
  const [combineRecord, setCombineRecord] = useState({});
  const expandedDisplayHash = useRef({});
  const bodyRefs = useRef({});

  useEffect(() => {
    const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {};

    setLocationParmas(locationParmas);
    setId(locationParmas.id);
    setType(locationParmas.Type);
    setTitle(locationParmas.title);
  }, [props.match.params.data, userInfo]);

  // 权益明细编码

  useEffect(() => {
    if (id || Type !== 'add') {
      return;
    }
    const params = { operate: 'pack' };
    // if (sessionStorage.getItem('PackManagementCode')) {
    //     setPackNo(sessionStorage.getItem('PackManagementCode'))
    // } else {
    //     post(allUrl.DetailedManageInterests.createCode, { ...params }).then(res => {
    //         if (res.success) {
    //             setPackNo(res.resp[0])
    //             sessionStorage.setItem('PackManagementCode', res.resp[0])
    //         } else {
    //             // message.error(res.msg)
    //         }
    //     })
    // }
    // v15去掉权益包编码
    // post(allUrl.DetailedManageInterests.createCode, { ...params }).then(res => {
    //     if (res.success) {
    //         setPackNo(res.resp[0])
    //     } else {
    //         // message.error(res.msg)
    //     }
    // })
  }, [Type]);

  // 编辑状态 通过id调用权益包详情接口
  useEffect(() => {
    if (id && Type === 'edit') {
      let query = { id: id };
      setLoading(true);
      get(allUrl.EquityPackageManage.getEquityPack, { ...query }).then(res => {
        if (res.success) {
          let Dt = res.resp[0];
          // setDetails(Dt.details)

          //处理权益车型
          Dt.carType = !!Dt.packCarConfigs.length
            ? Array.from(new Set(Dt.packCarConfigs.map(item => item.carType))).map(String)
            : [];
          if (!!Dt.packCarConfigs.length) {
            Object.assign(Dt, {
              // temporaryCarConfiguration: [
              //     Dt.carOfYear,
              //     Dt.powerModelCode,
              //     Dt.carConfig
              // ]
              // tmplCarConfig: [{ carOfYear: '2023', powerModelCode: "EVR", carConfig: "9999" }, { carOfYear: '2023', powerModelCode: "EVR", carConfig: "31" }]
              tmplCarConfig: Dt.packCarConfigs,
            });
          }
          let temp = [],
            collectDetailNo = [];
          const { updateDetails } = locationParmas;
          Dt.relationDetails.map((item, index) => {
            item[UPGRADE_DETAILAPI.ORIGIN].key = index + 1;
            temp.push({ detailNo: item[UPGRADE_DETAILAPI.ORIGIN].detailNo });
            // if (item[UPGRADE_DETAILAPI.UPGRADE]) {
            //     setExpandedDetailNos([...expandedDetailNos, item[UPGRADE_DETAILAPI.ORIGIN].detailNo])
            // }
            //处理更新的权益明细
            updateDetails &&
              updateDetails.map((item2, index2) => {
                if (item[UPGRADE_DETAILAPI.ORIGIN].detailNo === item2.detailNo) {
                  if (item[UPGRADE_DETAILAPI.UPGRADE]) {
                    //收集包含升级的且已更新的明细编码
                    collectDetailNo.push(item[UPGRADE_DETAILAPI.ORIGIN].detailNo);
                  }
                  item[UPGRADE_DETAILAPI.ORIGIN] = item2;
                  item[UPGRADE_DETAILAPI.UPGRADE] = null;
                  item[UPGRADE_DETAILAPI.UPGRADEVIEW] = null;
                  item[UPGRADE_DETAILAPI.DISPLAY] = null;
                }
              });
          });
          if (updateDetails) {
            //列表页从发布/重新发布按钮跳转进来的
            setUpdateDetailNo(updateDetails.map(item => item.detailNo));
            !!collectDetailNo.length && showUpgradeNo(collectDetailNo);
          }
          setDataDetail(Dt);
          setTableList(Dt.relationDetails);
          // 所有已组合选项
          setGroupDetails(Dt.groupDetails || []);
          setTypeValue(Dt.type);
          setDetails(temp);
          if (Dt.carType && Dt.carType.length > 0) {
            setCarTypeHasValue(true);
            carConfigChange(Dt.carType.join(','));
          }
        } else {
          // message.error(res.msg)
        }
        setLoading(false);
      });
    }
  }, [Type, id]);

  const carConfigChange = key => {
    get(allUrl.EquityPackageManage.getCarConfig, { carTypeList: key || '' }).then(res => {
      if (res.success) {
        setCarConfigData(res.resp);
      }
    });
  };
  const BasePayTableCB = data => {
    /**  mock  */
    data = data.map((item, index) => {
      return { originDetail: item };
    });

    /** mock end */
    if (typeValue === 6) {
      //精品权益，一个包只能绑一个明细
      setTableList(data);
    } else {
      let temp = [...tableList, ...data];

      temp = unique(temp);
      setTableList(temp);
    }
  };

  //数组去重
  const unique = arr => {
    const arr1 = [],
      arr2 = [];
    for (var i = 0, len = arr.length; i < len; i++) {
      if (arr2.indexOf(arr[i][UPGRADE_DETAILAPI.ORIGIN].detailNo) === -1) {
        arr2.push(arr[i][UPGRADE_DETAILAPI.ORIGIN].detailNo);
        arr1.push(arr[i]);
      }
    }
    return arr1;
  };

  const isHas = (obj, name) => {
    return Object.prototype.hasOwnProperty.call(obj, name);
  };
  // 提交时的参数校验
  const submitBefore = query => {
    // console.log(query);
    if (query === undefined) return {};

    if (isHas(query, 'limitTimeFrame') && query.limitTimeFrame == '1') {
      if (Number(query.limitYear) == 0 && Number(query.limitMonth) == 0 && Number(query.limitDay) == 0) {
        message.warn('权益限制年限不能为0年0月0日');
        return false;
      }
      if (
        isHas(query, 'limitYear') &&
        query.limitYear == undefined &&
        query.limitMonth == undefined &&
        query.limitDay == undefined
      ) {
        message.warn('权益限制年限不能为0年0月0日');
        return false;
      }
    }
    //权益金额抵扣
    if (isHas(query, 'deductionFrame') && query.deductionFrame !== 1) {
      query.payAmount = query.deduction = undefined;
    }

    //权益限制里程
    if (isHas(query, 'limitLegendFrame') && query.limitLegendFrame !== 1) {
      query.limitLegend = undefined;
    }
    if (isHas(query, 'limitLegendFrame') && query.limitLegendFrame !== 2) {
      query.extenderMileage = undefined;
    }

    //权益限制年限
    if (isHas(query, 'limitTimeFrame') && query.limitTimeFrame !== 1) {
      query.limitYear = undefined;
      query.limitMonth = undefined;
      query.limitDay = undefined;
    }

    //全保养阶梯策略
    if (isHas(query, 'maintenanceLadderFrame') && query.maintenanceLadderFrame !== 1) {
      query.maintenanceLadder = undefined;
    }

    //权益生效里程
    if (isHas(query, 'effectMileageFrame') && query.effectMileageFrame !== 1) {
      query.fixedBeginMileage = undefined;
      query.fixedEndMileage = undefined;
    }

    // 享有权益频次
    if (isHas(query, 'frequencyRadio') && (query.frequencyRadio === undefined || query.frequencyRadio == '0')) {
      query.frequency = 0;
    }
    if (isHas(query, 'frequencyRadio') && query.frequencyRadio == '-1') {
      query.frequency = -1;
    }

    // 车联网流量
    if (isHas(query, 'trafficRadio') && (query.trafficRadio == '0' || query.trafficRadio === undefined)) {
      query.traffic = 0;
      query.trafficUnit = '';
    }
    if (isHas(query, 'trafficRadio') && query.trafficRadio == '-1') {
      query.traffic = -1;
      query.trafficUnit = undefined;
    }

    // 权益生效时间
    if (isHas(query, 'timeRadio') && query.timeRadio == '1') {
      query.relativeTime = '';
      query.fixedRelativeTime = query.fixedRelativeTime || [];
      query.fixedBeginTime = query.fixedRelativeTime.length
        ? moment(query.fixedRelativeTime[0]).format('YYYY-MM-DD HH:mm:ss')
        : '';
      query.fixedEndTime = query.fixedRelativeTime.length
        ? moment(query.fixedRelativeTime[1]).format('YYYY-MM-DD HH:mm:ss')
        : '';
      delete query.fixedRelativeTime;
    }
    if (isHas(query, 'timeRadio') && query.timeRadio == '0') {
      query.fixedBeginTime = '';
      query.fixedEndTime = '';
      delete query.fixedRelativeTime;
    }

    // 补漆面选项
    if (isHas(query, 'paintFrame') && query.paintFrame !== 1) {
      query.partRelationList = [];
      delete query.paintList;
    } else {
      const filter = query?.paintList?.filter(i => i.num > 0) || [];
      let sum = 0;
      const str =
        filter.map(item => {
          sum += item.num;
          return `${item.name}*${item.num} `;
        }) || [];
      query.paintDesc = str.join(',') + `, 共${sum}面`;
    }
    //权益生效时间和权益生效里程，不能同时选择固定时间/固定里程那一项
    if (isHas(query, 'timeRadio') && query.timeRadio === 1 && query.effectMileageFrame === 1) {
      message.warn('由于策略冲突，同一条权益明细不允许同时存在固定生效里程和固定生效开始时间');
      return false;
    }
    return query;
  };
  // 保存或者发布时校验聚合表单
  const check = fn => {
    const promiseArr = [];
    Reflect.ownKeys(bodyRefs.current).map(item => {
      // console.log(bodyRefs.current[item].getFieldsValue());
      if (bodyRefs.current[item]) {
        const _display = bodyRefs.current[item].getFieldsValue();
        expandedDisplayHash.current = {
          ...expandedDisplayHash.current,
          [_display.originDetailNo]: { ...expandedDisplayHash.current[_display.originDetailNo], ..._display },
        };
        promiseArr.push(bodyRefs.current[item].validateFields());
      }
    });
    Promise.all(promiseArr)
      .then(res => {
        // setTableList(prev => {
        //     const _prev = _.cloneDeep(prev)
        //     _prev.map((item, index) => {
        //         let _display
        //         if (expandedDisplays.hasOwnProperty(item.originDetail.detailNo)) {
        //             const _submit = submitBefore(expandedDisplays[item.originDetail.detailNo])
        //             if (_submit) {
        //                 _display = { ...item[UPGRADE_DETAILAPI.DISPLAY], ..._submit }
        //             }
        //         }

        //         item.originDetail = {
        //             detailNo: item[UPGRADE_DETAILAPI.ORIGIN].detailNo
        //         }
        //         item.upgradeDetail = item[UPGRADE_DETAILAPI.UPGRADE]
        //         item.detailDisplay = _display || item[UPGRADE_DETAILAPI.DISPLAY]
        //     })
        //     console.log(_prev);

        //     return _prev
        // })

        let relationDetails = [];
        let flag = true;
        tableList.map((item, index) => {
          let _display;
          if (expandedDisplayHash.current.hasOwnProperty(item.originDetail.detailNo)) {
            const _submit = submitBefore(expandedDisplayHash.current[item.originDetail.detailNo]);
            if (!_submit) {
              flag = false;
            } else {
              _display = { ...item[UPGRADE_DETAILAPI.DISPLAY], ..._submit };
            }
          }

          relationDetails[index] = {
            originDetail: {
              detailNo: item[UPGRADE_DETAILAPI.ORIGIN].detailNo,
              detailVersion: item[UPGRADE_DETAILAPI.ORIGIN].detailVersion,
              goodsCategory: item[UPGRADE_DETAILAPI.ORIGIN].goodsCategory,
            },
            upgradeDetail: item[UPGRADE_DETAILAPI.UPGRADE],
            detailDisplay: _display || item[UPGRADE_DETAILAPI.DISPLAY],
          };
        });
        if (!flag) return;
        // console.log(expandedDisplayHash, relationDetails);

        if (Object.prototype.toString.call(fn) === '[object Function]') {
          fn({ relationDetails });
        }
        // console.log(res, expandedDisplays);

        // _submit()
      })
      .catch(err => {
        console.log(err);
        err?.errorFields &&
          err?.errorFields.length &&
          err.errorFields.map(item => {
            item?.errors.length &&
              item.errors.map(item2 => {
                // console.log(item2);

                message.error(item2);
              });
          });
      });
  };

  //提交前校验所选的车型是否都选了车型配置
  const validateCarConfig = query => {
    const k = query.tmplCarConfig?.map(item => item?.split('/')[0]) || [];
    if ([...new Set(k)].sort().join('') !== query.carType_alias.sort().join('')) {
      const carName = [];
      // 创建一个哈希表来存储carType和carTypeName的对应关系
      const carTypeMap = new Map();
      carConfigData.forEach(item => {
        carTypeMap.set(item.carType, item.carTypeName);
      });

      query.carType_alias.forEach(i => {
        if (!k.includes(i) && carTypeMap.has(Number(i))) {
          carName.push(carTypeMap.get(Number(i)));
        }
      });
      message.error(`${carName.join('、')}车型的车型配置未选，请选择`);
      return true;
    }
  };

  const handleCreate = value => {
    const fn = ({ relationDetails }) => {
      let query = { ...value, relationDetails, groupDetails };
      if (validateCarConfig(query)) return;
      // 更新权益包接口
      setLoading(true);
      if (id && Type === 'edit') {
        const params = {
          id,
          packNo: dataDetail.packNo,
          ...query,
        };
        post(allUrl.EquityPackageManage.updateEquityPack, { ...params }).then(res => {
          if (res.success) {
            message.success('更新成功！');
            // 保存成功跳转到列表
            props.history.push('/RightCenter/PackManagement');
          } else {
            // message.error('更新失败！')
          }
          setLoading(false);
        });
      } else {
        // 保存草稿接口
        post(allUrl.EquityPackageManage.draftEquityPack, { ...query }).then(res => {
          if (res.success) {
            message.success('保存成功！');
            // 保存成功跳转到列表
            // sessionStorage.removeItem('PackManagementCode')
            props.history.push('/RightCenter/PackManagement');
          } else {
            // message.error('保存失败！')
          }
          setLoading(false);
        });
      }
    };
    // 基础权益包走check
    if (typeValue == 1) {
      check(fn);
    } else {
      let relationDetails = [];
      tableList.map((item, index) => {
        relationDetails[index] = {
          originDetail: {
            detailNo: item[UPGRADE_DETAILAPI.ORIGIN].detailNo,
            detailVersion: item[UPGRADE_DETAILAPI.ORIGIN].detailVersion,
            goodsCategory: item[UPGRADE_DETAILAPI.ORIGIN].goodsCategory,
          },
          upgradeDetail: null,
          detailDisplay: null,
        };
      });
      fn({ relationDetails });
    }
  };
  // 打开关联弹窗
  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    form.setFieldsValue({ adddetailNo: '' });
    setIsModalVisible(false);
  };
  // 添加升级编辑逻辑
  const editDefault = detail => {
    const _config = _.cloneDeep(strategyConfig);

    // 限制年限升级前为无此策略和不限年数，不可修改
    if (Number(detail.limitTimeFrame) === 0 || Number(detail.limitTimeFrame) === -1) {
      _config.limitTimeFrame.c.limitTimeFrame.disabled = true;
    } else if (Number(detail.limitTimeFrame) === 1) {
      _config.limitTimeFrame.c.limitTimeFrame.childrenDisabled1 = true;
    }
    // 权益生效时间不可修改，相对时间不可以改，固定时间可以改数值
    if (detail.fixedBeginTime && detail.fixedEndTime) {
      _config.timeRadio.c.timeRadio.disabled = true;
      _config.timeRadio.c.relativeTime.disabled = true;
    } else {
      _config.timeRadio.c.timeRadio.disabled = true;
      _config.timeRadio.c.relativeTime.disabled = true;
      _config.timeRadio.c.fixedRelativeTime.disabled = true;
    }

    // 权益限制里程（基准）不可修改选项，且隐藏不可修改选项，但是可以修改数值
    // _config.limitLegendFrame.c.limitLegendFrame.disabled = true
    if (detail.limitLegendFrame === 0) {
      _config.limitLegendFrame.c.limitLegendFrame.childrenDisabled2 = true;
      _config.limitLegendFrame.c.limitLegendFrame.childrenDisabled3 = true;
      _config.limitLegendFrame.c.limitLegendFrame.childrenTips1 =
        '限制里程（基准）基于基础权益生成，基础权益配置无此策略，则升级策略默认为无此策略，无法修改';
    } else if (detail.limitLegendFrame === 1) {
      _config.limitLegendFrame.c.limitLegendFrame.childrenDisabled1 = true;
      _config.limitLegendFrame.c.limitLegendFrame.childrenDisabled3 = true;
      _config.limitLegendFrame.c.limitLegendFrame.childrenTips2 =
        '限制里程（基准）基于基础权益生成，基础权益配置行驶里程，则升级策略默认为行驶里程，无法修改';
    } else if (detail.limitLegendFrame === 2) {
      _config.limitLegendFrame.c.limitLegendFrame.childrenDisabled1 = true;
      _config.limitLegendFrame.c.limitLegendFrame.childrenDisabled2 = true;
      _config.limitLegendFrame.c.limitLegendFrame.childrenTips3 =
        '限制里程（基准）基于基础权益生成，基础权益配置增程器里程，则升级策略默认为增程器里程，无法修改';
    }

    // 权益生效里程 不可修改 相对里程不可以改，固定里程可以改数值
    if (Number(detail.effectMileageFrame) === 1) {
      _config.effectMileageFrame.c.effectMileageFrame.disabled = true;
      _config.effectMileageFrame.c.relativeBeginMileageStrategy.disabled = true;
      _config.effectMileageFrame.c.relativeEndMileageStrategy.disabled = true;
    } else {
      _config.effectMileageFrame.c.effectMileageFrame.disabled = true;
      _config.effectMileageFrame.c.relativeBeginMileageStrategy.disabled = true;
      _config.effectMileageFrame.c.relativeEndMileageStrategy.disabled = true;
      _config.effectMileageFrame.c.fixedBeginMileage.disabled = true;
      _config.effectMileageFrame.c.fixedEndMileageStrategy.disabled = true;
    }

    // 享受权益频次。无此策略和不限年限不可修改。其他可以选择不限次数，其他可以修改
    if (Number(detail.frequency) > 0) {
      _config.frequencyRadio.c.frequencyRadio.childrenDisabled1 = true;
    } else {
      _config.frequencyRadio.c.frequencyRadio.disabled = true;
    }

    // 补漆面部位
    if (Number(detail.paintFrame) === 0 || Number(detail.paintFrame) === -1) {
      _config.paintFrame.c.paintFrame.disabled = true;
    } else {
      _config.paintFrame.c.paintFrame.childrenDisabled1 = true;
    }
    // 车联网流量 无此策略和不限次数不可修改，其他可选不限次数
    if (Number(detail.traffic) > 0) {
      _config.trafficRadio.c.trafficRadio.childrenDisabled1 = true;
    } else {
      _config.trafficRadio.c.trafficRadio.disabled = true;
    }
    // 权益金额抵扣 为无此策略不可修改
    if (Number(detail.deductionFrame) === 0) {
      _config.deductionFrame.c.deductionFrame.disabled = true;
    }

    setNewStrategyConfig(_config);
  };
  // 打开升级弹窗-包内权益明细升级
  const showUpgradeModal = record => {
    if (typeValue != '1') return;
    const _list = tableList.filter(item => item[UPGRADE_DETAILAPI.ORIGIN].detailNo === record.detailNo)[0];
    if (_list?.[UPGRADE_DETAILAPI.UPGRADE]) {
      // 取消升级
      setTableList(prev => {
        const _prev = _.cloneDeep(prev);
        _prev.map(item => {
          if (item[UPGRADE_DETAILAPI.ORIGIN].detailNo === record.detailNo) {
            item[UPGRADE_DETAILAPI.UPGRADE] = null;
            item[UPGRADE_DETAILAPI.UPGRADEVIEW] = null;
            item[UPGRADE_DETAILAPI.DISPLAY] = null;
          }
        });
        return _prev;
      });
      if (expandedDisplayHash.current[record.detailNo]) {
        // 删除保存聚合信息的对象 和 form对象
        delete expandedDisplayHash.current[record.detailNo];
        delete bodyRefs.current[record.detailNo];
        console.log(expandedDisplayHash.current, bodyRefs.current);
      }
      setExpandedRowKeys(prev => {
        const _prev = [...prev];
        return _prev.filter(item => item !== record.detailNo);
      });
    } else {
      // 升级

      // const promiseArr = []
      // Reflect.ownKeys(bodyRefs.current).map(item => {
      //     const _display = bodyRefs.current[item].getFieldsValue()
      //     expandedDisplayHash.current = { ...expandedDisplayHash.current, [_display.originDetailNo]: { ...expandedDisplayHash.current[_display.originDetailNo], ..._display } }
      //     // promiseArr.push(bodyRefs.current[item].validateFields())
      // })
      // console.log('anniu', expandedDisplayHash.current);
      // setTableList(prev => {
      //     const _prev = _.cloneDeep(prev)
      //     _prev.map((item, index) => {
      //         let _display
      //         if (expandedDisplayHash.current.hasOwnProperty(item.originDetail.detailNo)) {
      //             const _newDisplay = expandedDisplayHash.current[item.originDetail.detailNo]
      //             _display = { ...item[UPGRADE_DETAILAPI.DISPLAY], ..._newDisplay }
      //         }

      //         return {
      //             originDetail: {
      //                 detailNo: item[UPGRADE_DETAILAPI.ORIGIN].detailNo
      //             },
      //             upgradeDetail: item[UPGRADE_DETAILAPI.UPGRADE],
      //             detailDisplay: _display || item[UPGRADE_DETAILAPI.DISPLAY]
      //         }
      //     })
      //     return _prev
      // })

      setIsUpgradeModalVisible(true);
      setModalUpgradeLoading(true);
      // 点击升级的时候获取保内权益明细的详情接口
      get(allUrl.EquityPackageManage.getObjByDetailNoAndVersion, {
        detailNo: record.detailNo,
        detailVersion: record.detailVersion,
      }).then(res => {
        if (res.success) {
          let data = res.resp[0];
          editDefault(data);
          setModalUpgradeDetail({ ...modalUpgradeDetail, type: 'add', detail: data });
        } else {
          message.error(res.msg);
        }
        setModalUpgradeLoading(false);
      });
    }
  };

  // 关闭升级弹窗
  const handleUpgradeCancel = () => {
    setIsUpgradeModalVisible(false);
  };
  // 编辑升级权益明细
  const editUpgradeModal = record => {
    const _detail = tableList.filter(item => item[UPGRADE_DETAILAPI.ORIGIN].detailNo === record.originDetailNo)[0];

    if (_detail) {
      setIsUpgradeModalVisible(true);
      // 这里要使用原来的明细判断置灰选项
      editDefault(_detail[UPGRADE_DETAILAPI.ORIGIN]);
      setModalUpgradeDetail({ ...modalUpgradeDetail, type: 'edit', detail: _detail[UPGRADE_DETAILAPI.UPGRADE] });
    }
  };

  // 删除权益包内权益明细关联
  const handleDelete = record => {
    const fn = () => {
      const filterDetailNos = details.filter(item => item.detailNo !== record.detailNo);
      const filterTables = tableList.filter(item => item[UPGRADE_DETAILAPI.ORIGIN].detailNo !== record.detailNo);
      filterTables.map((item, index) => (item[UPGRADE_DETAILAPI.ORIGIN].key = index + 1));
      setDetails(filterDetailNos);
      setTableList(filterTables);
      setExpandedRowKeys(prev => {
        const _prev = [...prev];
        return _prev.filter(item => item !== record.detailNo);
      });
    };
    if (allGroupDetailNos.includes(record.detailNo)) {
      /** 组合相关 */
      confirm({
        title: '该明细已进行组合，请确认删除该明细？',
        icon: <ExclamationCircleFilled />,
        content: '删除后，明细与权益包关联关系删除，组合内的明细信息同步删除。',
        onOk() {
          console.log('OK');
          fn();
          // 删除时如果是否删除了已组合的选项，这里重置一下
          const _groupDetails = groupDetails
            .map((item, index) => {
              if (item?.detailNoList?.includes(record.detailNo)) {
                // 如果组合内只有两个，则解除这个组合
                if (item?.detailNoList.length <= 2) {
                  return null;
                } else {
                  item.detailNoList = item.detailNoList.filter(i => i != record.detailNo);
                  return item;
                }
              }
              return item;
            })
            .filter(i => i !== null);
          setGroupDetails(_groupDetails);

          // console.log(_groupDetails);
        },
        onCancel() {
          console.log('Cancel');
        },
      });
    } else {
      confirm({
        title: '删除操作不可逆转，确认删除？',
        icon: <ExclamationCircleFilled />,
        onOk() {
          fn();
        },
        onCancel() {
          console.log('Cancel');
        },
      });
    }
  };

  useEffect(() => {
    // 所有已组合的选项detaiNo数组

    setAllGroupDetailNos(() => {
      console.log(11111, groupDetails);
      if (groupDetails.length > 1) {
        return groupDetails?.reduce((i, j) => {
          i = i.detailNoList ? i.detailNoList : [...i];
          return i.concat(j.detailNoList);
        });
      } else {
        return groupDetails[0]?.detailNoList || [];
      }
    });
  }, [groupDetails]);
  // 升级按钮
  const filterUpgradeBtn = record => {
    if (roleJudgment(userInfo, 'PACK_MANAGEMENT_UPGRADE')) {
      const _detail = tableList.filter(item => item[UPGRADE_DETAILAPI.ORIGIN].detailNo === record.detailNo)[0];
      if (_detail?.[UPGRADE_DETAILAPI.UPGRADE]) {
        return (
          <Popconfirm
            title="取消升级操作不可逆转，确认取消升级？"
            disabled={typeValue != '1'}
            onConfirm={() => showUpgradeModal(record)}
          >
            <a disabled={typeValue != 1 || record.businessScene != 1} className={upgradeStyle.upgradeBtn}>
              取消升级
            </a>
          </Popconfirm>
        );
      } else {
        return (
          <a
            className={upgradeStyle.upgradeBtn}
            disabled={typeValue != 1 || record.businessScene != 1}
            onClick={() => showUpgradeModal(record)}
          >
            升级
          </a>
        );
      }
    }
  };
  // 展开按钮
  const filterOpenBtn = record => {
    const _detail = tableList.filter(item => item[UPGRADE_DETAILAPI.ORIGIN].detailNo === record.detailNo)[0];
    if (_detail?.[UPGRADE_DETAILAPI.UPGRADE] && typeValue == '1') {
      // 当前是展开的
      if (expandedRowKeys.includes(record.detailNo)) {
        return (
          <a className={upgradeStyle.openBtn} onClick={() => onExpand(false, record)}>
            收起
            <UpOutlined />
          </a>
        );
      } else {
        // 当前是收起的
        return (
          <a className={upgradeStyle.openBtn} onClick={() => onExpand(true, record)}>
            展开
            <DownOutlined />
          </a>
        );
      }
    } else {
      return '';
    }
  };

  // 组合按钮
  const filterCombineBtn = record => {
    let disableBtn = false;
    if (allGroupDetailNos?.includes(record.detailNo)) {
      return (
        <a className={upgradeStyle.openBtn} disabled={typeValue != 7} onClick={() => showCombineModal(true, record)}>
          编辑组合
        </a>
      );
    } else {
      if (tableList.length - allGroupDetailNos.length <= 1) {
        // 当只剩一个未组合时，组合按钮置灰
        disableBtn = true;
      }
      const isDisabled = typeValue != 7 || disableBtn;
      return (
        <a
          className={upgradeStyle.openBtn}
          disabled={isDisabled}
          onClick={() => !isDisabled && showCombineModal(true, record)}
        >
          组合
        </a>
      );
    }
  };

  // table默认展开（expandedRowKeys设置之后就无法收起），需要配合此函数进行展开和收起
  const onExpand = (expanded, record) => {
    if (expanded) {
      setExpandedRowKeys([record.detailNo]);
    } else {
      setExpandedRowKeys([]);
    }
  };

  const showCombineModal = (val, record) => {
    setIsCombineModalVisible(val);
    setCombineRecord(record);
  };

  const handleCombineCancel = () => {
    setIsCombineModalVisible(false);
  };

  // 升级之后的明细集合，用来判断是否展示加号+
  useEffect(() => {
    if (reciveModalDetail) {
      const _detail = reciveModalDetail;
      // 升级之后的明细集合，用来判断是否展示加号+
      // const Nos = [...expandedDetailNos]
      // Nos.push(_detail?.originDetailNo)
      // setExpandedDetailNos(Nos)

      //默认展开此行
      setExpandedRowKeys([_detail?.originDetailNo]);

      // 在原来的基础上新增一条升级明细表格 + 添加聚合逻辑

      setTableList(prev => {
        const _prev = _.cloneDeep(prev);
        _prev.map(item => {
          if (item[UPGRADE_DETAILAPI.ORIGIN].detailNo === _detail.originDetailNo) {
            // 新增一条升级明细表格
            item[UPGRADE_DETAILAPI.UPGRADE] = _detail; //编辑用
            item[UPGRADE_DETAILAPI.UPGRADEVIEW] = _detail; //展示用
            item[UPGRADE_DETAILAPI.DISPLAY] = _detail; //聚合用
            // 添加聚合逻辑
            // editDefault(_detail)
          }
        });
        return _prev;
      });
    }
  }, [reciveModalDetail]);

  // table展开内容
  const expandableProps = {
    expandedRowRender: (oriRecord, index, indent, expanded) => {
      const _list = tableList.filter(item => item[UPGRADE_DETAILAPI.ORIGIN].detailNo === oriRecord.detailNo);
      const _detailNo = oriRecord.detailNo;
      const _props = {
        tableList,
        currentList: _list,
        typeValue,
        oriRecord,
        setTableList, //在展开项中给tablelist填充聚合信息
        editUpgradeModal,
        expandedDisplayHash,
        // expandedDisplays,
        // setExpandedDisplays
      };
      const _detailDisplay = _list[0]?.[UPGRADE_DETAILAPI.DISPLAY];
      // if (expandedDisplayHash.current.hasOwnProperty(_detailNo)) {
      //     // const _display = bodyRefs.current[_detailNo].getFieldsValue()
      //     // console.log(expandedDisplayHash.current[_detailNo]);

      //     const a = _.cloneDeep(expandedDisplayHash.current[_detailNo])
      //     _props.detailDisplay = a
      //     // expandedDisplayHash.current = { ...expandedDisplayHash.current, [_detailNo]:  } }
      // } else {
      if (_detailDisplay) {
        // console.log(
        //   'create-expandedRowRender-yes',
        //   _detailDisplay.detailNo,
        //   oriRecord.detailNo,
        //   index,
        //   indent,
        //   expanded
        // );
        _props.detailDisplay = _detailDisplay;
      } else {
        // console.log('create-expandedRowRender-no', _detailDisplay, oriRecord.detailNo, index, indent, expanded);
        _props.detailDisplay = _list[0]?.[UPGRADE_DETAILAPI.UPGRADE];
      }
      // }

      if (!_detailDisplay && !expanded) {
        /**
         * !_detailDisplay 取消升级后置为null !expanded当前为关闭状态
         * 这里设为null是为了当dom元素去掉，否则会一直存在，在保存时会获取到
         */
        return null;
      }
      return <ExpandedRowComponent {..._props} bodyRefs={bodyRefs} refIndex={oriRecord.detailNo} />;
    },
    showExpandColumn: false,
    onExpand,
    expandedRowKeys: expandedRowKeys, //展开的行
    // rowExpandable: (record) => expandedDetailNos.includes(record.detailNo), // 本行是否展示+
  };

  //取消升级的明细编码二次提醒
  const showUpgradeNo = detailNo => {
    Modal.info({
      title: '提示',
      okText: '我知道了',
      bodyStyle: {
        wordBreak: 'break-all',
      },
      content: (
        <>
          包内明细<span>{detailNo.join(',')}</span>已取消升级，请重新升级！
          <br />
        </>
      ),
      onOk: () => {},
    });
  };

  //保存或发布时校验包内权益明细是否更新弹窗
  const checkDetailUpdate = () => {
    return new Promise(async (resolve, reject) => {
      // const { success, resp } = await get(allUrl.EquityPackageManage.getEquityPack, { id: 351 })
      const detailCheckList = tableList.map(item => item.originDetail);
      const { success, resp } = await post(allUrl.EquityPackageManage.detailVersionCheck, {
        packNo: dataDetail?.packNo,
        detailCheckList,
      });
      if (success) {
        if (resp.length > 0) {
          const _updateDetailNo = resp.map(item => item.detailNo);
          //弹出有更新的弹窗
          Modal.confirm({
            title: (
              <div style={{ wordBreak: 'break-all' }}>
                包内明细{_updateDetailNo.join(',')}的版本有更新，是否更新为最新的版本？
              </div>
            ),
            cancelText: '不更新',
            okText: '更新',
            width: 500,
            icon: <ExclamationCircleFilled />,
            bodyStyle: {
              lineHeight: '30px',
              paddingTop: '45px',
            },
            content: (
              <>
                {/* 包内明细<span>{_updateDetailNo.join(',')}</span>的版本有更新，是否更新为最新的版本？<br /> */}
                若选择不更新，则权益包直接保存该权益明细当前所选的版本；
                <br />
                若选择更新，则该明细更新为最新的版本。
              </>
            ),
            closable: true,
            onOk: () => {
              //更新关联的权益数据
              setUpdateDetailNo(_updateDetailNo);
              let tmpl = [...tableList];
              let collectDetailNo = []; //收集包含升级的且已更新的明细编码
              tmpl.map((item, index) => {
                resp.map((item2, index2) => {
                  if (item[UPGRADE_DETAILAPI.ORIGIN].detailNo === item2.detailNo) {
                    if (item[UPGRADE_DETAILAPI.UPGRADE]) {
                      collectDetailNo.push(item[UPGRADE_DETAILAPI.ORIGIN].detailNo);
                    }
                    item[UPGRADE_DETAILAPI.ORIGIN] = item2;
                    item[UPGRADE_DETAILAPI.UPGRADE] = null;
                    item[UPGRADE_DETAILAPI.UPGRADEVIEW] = null;
                    item[UPGRADE_DETAILAPI.DISPLAY] = null;
                  }
                });
              });
              setTableList(tmpl);
              setExpandedRowKeys([]);
              !!collectDetailNo.length && showUpgradeNo(collectDetailNo);
              resolve(true);
            },
            onCancel: close => {
              if (typeof close.name !== 'undefined' && close.name !== '') {
                //点击了"不更新"按钮，直接保存
                close();
                resolve(false);
              }
            },
          });
        } else {
          //直接保存
          resolve(false);
        }
      }
    });
  };
  //V21版本：基础权益包=》保存或发布，校验同一车型配置、同一车辆市场类型的基础权益包是否存在，如存在，则提示用户
  const checkSameConfigPack = async data => {
    return new Promise(async (resolve, reject) => {
      const { success, resp } = await post(allUrl.EquityPackageManage.queryEffectBasePack, {
        // carType: data.carType, //权益包车型
        carMarketType: data.carMarketType, //车辆市场类型
        carConfigList: data.carConfigList, //车型配置
      });
      if (success) {
        if (resp[0].alreadyExist) {
          //弹出有更新的弹窗
          Modal.confirm({
            title: '提示',
            cancelText: '取消',
            okText: '确认',
            width: 500,
            icon: <ExclamationCircleFilled />,
            bodyStyle: {
              lineHeight: '30px',
              paddingTop: '45px',
            },
            content: (
              <>
                当前发布的权益包，与已发布的
                <span style={{ color: 'red' }}>相同车型配置、“{resp[0].basePackageList?.[0].carMarketTypeName}”</span>
                的基础权益包存在重复。请确认是否继续发布？
                <br />
                若确认发布，系统将自动以本次新发布的基础权益包为准，进行基础权益关联。
              </>
            ),
            closable: true,
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              resolve(false);
            },
          });
        } else {
          resolve(true);
        }
      } else {
        resolve(false);
      }
    });
  };

  //精品权益包=》发布时校验包内权益明细是否已被其他包绑定
  // const checkDetailBinded = () => {
  //     return new Promise(async (resolve, reject) => {
  //         const detailCheckList = tableList.map(item => item.originDetail)
  //         const { success, resp } = await post(allUrl.EquityPackageManage.detailVersionCheck, { packNo: dataDetail?.packNo, detailCheckList })
  //         if (success) {
  //             if (resp.length > 0) {
  //                 //弹出明细已关联的弹窗
  //                 Modal.warning({
  //                     title: '提示',
  //                     okText: '我知道了',
  //                     width: 500,
  //                     // icon: <ExclamationCircleFilled />,
  //                     bodyStyle: {
  //                         lineHeight: '30px',
  //                         paddingTop: '45px',
  //                     },
  //                     content: (
  //                         <div style={{ wordBreak: 'break-all' }}>包内权益明细已被权益包PGXXX关联，无法发布，请知悉！</div>
  //                     ),
  //                     closable: true,
  //                     onOk: () => {
  //                         resolve(true)
  //                     }
  //                 })
  //             } else {
  //                 //直接保存
  //                 resolve(false)
  //             }
  //         }
  //     })
  // }

  const time = new Date();
  return (
    <div className={`PublicList ${upgradeStyle.upgradeDetail}`}>
      <Spin spinning={loading}>
        <Row className="tableTitle" style={{ paddingBottom: 15, paddingTop: 0 }}>
          <Col className="text">{title}</Col>
        </Row>
        <div className="tableData">
          <div
            style={{
              backgroundColor: '#D6EBFF',
              height: 50,
              marginTop: 0,
              paddingLeft: 24,
              paddingTop: 13,
              fontSize: 16,
              color: 'rgba(0,0,0,0.85)',
              fontWeight: 500,
            }}
          >
            <div className="img-open-close">
              {isItemLabel ? (
                <img
                  className="icon-box"
                  src={require('@/assets/img/up-icon.png')}
                  onClick={() => {
                    setIsItemLabel(false);
                  }}
                  alt=""
                />
              ) : (
                <img
                  className="icon-box"
                  src={require('@/assets/img/down-icon.png')}
                  onClick={() => {
                    setIsItemLabel(true);
                  }}
                  alt=""
                />
              )}
              <span>操作信息</span>
            </div>
          </div>
          {
            id && Type === 'edit' && dataDetail && (
              <div className="detailPanel">
                <div className="log-wrap">
                  <Logs type="pack" dataCode={locationParmas.packNo} showAll={isItemLabel}>
                    {item => (
                      <div>
                        {item.changeContent &&
                          typeof item.changeContent === 'object' &&
                          Object.entries(item.changeContent).map(k => (
                            <>
                              {k[0]}：{k[1]}
                            </>
                          ))}
                      </div>
                    )}
                  </Logs>
                </div>
              </div>
            )
            // <div className='info-box  border-bottom'>
            //     <Descriptions title="" >
            //         <Descriptions.Item label='创建人'>{dataDetail.createName}</Descriptions.Item>
            //         <Descriptions.Item label='创建时间'>{dataDetail.createTime}</Descriptions.Item>
            //         <Descriptions.Item label='权益包状态'>{dataDetail.statusName}</Descriptions.Item>
            //     </Descriptions>
            //     <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            //         <div style={{ height: isItemLabel ? 'auto' : 0, overflow: 'hidden' }} >
            //             {dataDetail.updateNames &&
            //                 dataDetail.updateNames.map((item, index) => (
            //                     <Descriptions key={index}>
            //                         <Descriptions.Item key={item.id + index} label='修改人'>{item.operName}</Descriptions.Item>
            //                         <Descriptions.Item key={item.id + item.operTime + index} label='修改时间' span={2}>{item.operTime}</Descriptions.Item>
            //                     </Descriptions>
            //                 ))
            //             }
            //         </div>
            //     </div>
            // </div>
          }
          {Type === 'add' && userInfo && (
            <div className="info-box  border-bottom">
              <Descriptions title="">
                <Descriptions.Item label="操作人">{userInfo.userName}</Descriptions.Item>
                <Descriptions.Item label="操作动作">创建</Descriptions.Item>
                <Descriptions.Item label="操作时间">{time.toLocaleString()}</Descriptions.Item>
              </Descriptions>
            </div>
          )}
          <div
            style={{
              backgroundColor: '#D6EBFF',
              height: 50,
              marginTop: 24,
              paddingLeft: 24,
              paddingTop: 13,
              fontSize: 16,
              color: 'rgba(0,0,0,0.85)',
              fontWeight: 500,
            }}
          >
            权益包内容
          </div>
          <div className="detailPanel" style={{ padding: '24px 0' }}>
            <CreateForm
              setData={{
                setCarConfigData,
                setCarTypeHasValue,
                setLoading,
                setTypeValue,
                check,
                setTableList,
                setGroupDetails,
              }}
              getData={{ carConfigData, carTypeHasValue, typeValue, groupDetails }}
              dataSource={dataDetail}
              tableList={tableList}
              onCreate={handleCreate}
              checkDetailUpdate={checkDetailUpdate}
              checkSameConfigPack={checkSameConfigPack}
              validateCarConfig={validateCarConfig}
              id={id}
              details={details}
              Type={Type}
            >
              <div style={{ backgroundColor: '#F0F2F5', height: 20, paddingLeft: 24, paddingTop: 13 }}></div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  backgroundColor: '#D6EBFF',
                  height: 50,
                  paddingLeft: 24,
                  paddingTop: 13,
                  fontSize: 16,
                  color: 'rgba(0,0,0,0.85)',
                  fontWeight: 500,
                }}
              >
                <div>
                  <span style={{ color: '#F5222D', marginRight: 5 }}>*</span>权益包内权益明细关联
                </div>
                <Button
                  className="btn"
                  style={{ marginRight: '20px', marginTop: '-3px' }}
                  onClick={showModal}
                  type="primary"
                >
                  添加关联
                </Button>
              </div>
              {/* 基础权益明细 */}
              <div className="detail-table">
                {typeValue == '1' ? (
                  <p className="tips-title">可在当前基础包上做权益升级，如无需升级则不需要进行升级操作</p>
                ) : (
                  ''
                )}
                <InjectData.Provider value={{ equityType: typeValue }}>
                  <PublicTable
                    className={`TablePanel ${styles['gray-table']}`}
                    dataSource={tableList}
                    rowKey={record => record.detailNo}
                    type="originDetail"
                    pagination={false}
                    expandable={typeValue == '1' ? expandableProps : ''}
                    rowClassName={(record, index) => {
                      if (updateDetailNo.includes(record.detailNo)) return `${styles['table-row-cyan']}`;
                    }}
                  >
                    <Table.Column
                      title="操作"
                      dataIndex=""
                      width={250}
                      fixed="right"
                      render={record => (
                        <div className="btn-wrapper">
                          {/* <Popconfirm title="删除操作不可逆转，确认删除？" onConfirm={() => handleDelete(record)}>
                            <a className={upgradeStyle.btn}>删除</a>
                          </Popconfirm> */}
                          <a className={upgradeStyle.btn} onClick={() => handleDelete(record)}>
                            删除
                          </a>

                          {/* 组合按钮 */}
                          {filterCombineBtn(record)}
                          {/* 升级按钮 */}
                          {filterUpgradeBtn(record)}
                          {/* 展开按钮 */}
                          {filterOpenBtn(record)}
                        </div>
                      )}
                    />
                  </PublicTable>
                </InjectData.Provider>
              </div>
            </CreateForm>
          </div>
        </div>
        {isModalVisible && (
          <InjectData.Provider value={{ equityType: typeValue }}>
            <RelativeModal
              handleCancel={handleCancel}
              visible={isModalVisible}
              cb={BasePayTableCB}
              tableList={tableList}
            />
          </InjectData.Provider>
        )}
        {isUpgradeModalVisible && (
          <UpgradeModal
            handleCancel={handleUpgradeCancel}
            visible={isUpgradeModalVisible}
            strategyConfig={newStrategyConfig}
            detailFormConfig={detailFormConfig}
            getData={{ modalUpgradeDetail, modalUpgradeLoading }}
            setData={{ setReciveModalDetail, setModalUpgradeLoading }}
          />
        )}
        {isCombineModalVisible && (
          <CombineModal
            visible={isCombineModalVisible}
            handleCancel={handleCombineCancel}
            tableList={tableList}
            setGroupDetails={setGroupDetails}
            groupDetails={groupDetails}
            combineRecord={combineRecord}
            allGroupDetailNos={allGroupDetailNos}
          />
        )}
      </Spin>
    </div>
  );
};
export default PackManagementDetail;
