import React, { useEffect, useState, useRef } from 'react';

import { useSelector } from 'react-redux';
import { Table, message, Button, Descriptions, Row, Col, Popconfirm, Modal, Input, Form, Spin } from 'antd';
import '../index.less';
import history from '@/utils/history';
import { post, get } from '@/utils/request';
import allUrl from '../../../utils/url';
import moment from 'moment';
import ResultNotify from './ResultNotify';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import { DecryptByAES } from '@/components/Public/Decrypt';
import CreateForm from './CreateForm';
import SERES from '@/assets/img/seres.jpg';

const PackManagementDetail = props => {
  const [form] = Form.useForm();
  const formRef = useRef(null);

  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [id, setId] = useState(null);
  const [locationParmas, setLocationParmas] = useState({});
  const [tableHeight, setTableHeight] = useState(0);
  const [Type, setType] = useState('');
  const [title, setTitle] = useState('');
  const [isItemLabel, setIsItemLabel] = useState(false);

  // const [detailNo, setDetailNo] = useState([]) // 生成权益明细编码
  const [sortOptions, setSortOptions] = useState([]); // 权益分类
  const [typeOptions, setTypeOptions] = useState([]); // 权益类别
  const [detailNameOptions, setDetailNameOptions] = useState([]); // 获取所有权益明细名称

  const [dataDetail, setDataDetail] = useState(); // 权益包详情

  const [isModalVisibleSample, setIsModalVisibleSample] = useState(false); // 示例展示modal
  const [loading, setLoading] = useState(false);
  const [showModalValue, setShowModalValue] = useState(false); // code 999 弹窗
  const [modalMessage, setModalMessage] = useState(null);

  useEffect(() => {
    const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {};
    setLocationParmas(locationParmas);
    setId(locationParmas.id);
    setType(locationParmas.Type);
    setTitle(locationParmas.title);
    console.log(locationParmas);
    let createType = locationParmas.type;
    console.log('createType', locationParmas.Type);
    sessionStorage.setItem('createType', locationParmas.Type);
  }, [props.match.params.data, userInfo]);

  // 权益明细编码
  useEffect(() => {
    if (id || Type !== 'Add') {
      return;
    }
    const params = { operate: 'detail' };
    // V15版本去掉
    // post(allUrl.DetailedManageInterests.createCode, { ...params }).then(res => {
    //     if (res.success) {
    //         setDetailNo(res.resp[0])
    //     } else {
    //         // message.error(res.msg)
    //     }
    // })
  }, [Type]);

  //权益分类
  useEffect(() => {
    getSortList();
  }, []);

  //权益类别
  useEffect(() => {
    get(allUrl.DetailedManageInterests.getTypeList).then(res => {
      if (res.success) {
        let Dt = res.resp;
        setTypeOptions(Dt);
      } else {
        // message.error(res.msg)
      }
    });
  }, []);
  //获取所有权益明细名称
  useEffect(() => {
    get(allUrl.DetailedManageInterests.getDetailName).then(res => {
      if (res.success) {
        let Dt = res.resp;
        setDetailNameOptions(Dt);
      } else {
        // message.error(res.msg)
      }
    });
  }, []);
  // 车辆属性

  // 编辑状态 通过id调用权益明细详情接口
  useEffect(() => {
    if (id && Type === 'edit') {
      let query = { id: id };
      setLoading(true);

      get(allUrl.DetailedManageInterests.getDetailObj, { ...query }).then(res => {
        if (res.success) {
          let data = res.resp[0];
          // 数据处理
          // const data = data1 // 假数据
          // if (data.limitYear && data.yearUnit) {
          //     data.limitYearRadio = 1
          // }
          // // 享有权益频次
          // data.frequencyRadio = data.frequency > 0 ? 1 : data.frequency
          // data.frequency = data.frequency < 1 ? null : data.frequency    //解决未选其他选项时，其他选项关联的题有值

          // // 车联网流量
          // data.trafficRadio = data.traffic > 0 ? 1 : data.traffic
          // //解决未选其他选项时，其他选项关联的题有值
          // data.traffic = data.traffic < 1 ? null : data.traffic
          // data.trafficUnit = data.trafficUnit < 1 ? null : data.trafficUnit

          // if (data.relativeTime) {
          //     data.timeRadio = 0
          // }
          // if (data.fixedBeginTime && data.fixedEndTime) {
          //     data.timeRadio = 1

          // }
          // //行驶里程
          // if (data.limitLegend) {
          //     data.mileageRadio = 0
          // }
          // if (data.extenderMileage) {
          //     data.mileageRadio = 1
          // }

          // // 享有权益频次
          // data.outsideFrequencyRadio = data.outsideFrequency > 0 ? 1 : data.outsideFrequency
          // data.outsideFrequency = data.outsideFrequency < 1 ? null : data.outsideFrequency  //解决未选其他选项时，其他选项关联的题有值
          // // 车联网流量
          // data.outsideTrafficRadio = (data.outsideTraffic > 0 ? 1 : data.outsideTraffic)
          // //解决未选其他选项时，其他选项关联的题有值
          // data.outsideTraffic = data.outsideTraffic < 1 ? null : data.outsideTraffic
          // data.outsideTrafficUnit = data.outsideTrafficUnit < 1 ? null : data.outsideTrafficUnit

          setDataDetail(data);
        } else {
          // message.error(res.msg)
        }
        setLoading(false);
      });
    }
  }, [Type, id]);

  const getSortList = () => {
    return new Promise((resolve, reject) => {
      get(allUrl.DetailedManageInterests.getSortList).then(res => {
        if (res.success) {
          let Dt = res.resp;
          setSortOptions(Dt);
          resolve(Dt);
        } else {
          // message.error(res.msg)
        }
      });
    });
  };

  //其他输入框为disabled状态时，删除其他输入框里面的数据
  const resetFieldsByOther = query => {
    //权益金额抵扣
    if (query.deductionFrame !== 1) {
      query.payAmount = query.deduction = undefined;
    }
    if (query.outsideDeductionFrame !== 1) {
      query.outsidePayAmount = query.outsideDeduction = undefined;
    }
    //权益限制里程
    if (query.limitLegendFrame !== 1) {
      query.limitLegend = undefined;
    }
    if (query.limitLegendFrame !== 2) {
      query.extenderMileage = undefined;
    }
    if (query.outsideLimitLegendFrame !== 1) {
      query.outsideLimitLegend = undefined;
    }
    if (query.outsideLimitLegendFrame !== 2) {
      query.outsideExtenderMileage = undefined;
    }
    //权益限制年限
    if (query.limitTimeFrame !== 1) {
      query.limitYear = undefined;
      query.limitMonth = undefined;
      query.limitDay = undefined;
    }
    if (query.outsideLimitTimeFrame !== 1) {
      query.outsideLimitYear = undefined;
      query.outsideLimitMonth = undefined;
      query.outsideLimitDay = undefined;
    }
    //全保养阶梯策略
    if (query.maintenanceLadderFrame !== 1) {
      query.maintenanceLadder = undefined;
    }
    if (query.outsideMaintenanceLadderFrame !== 1) {
      query.outsideMaintenanceLadder = undefined;
    }
    //补漆面部位
    if (query.paintFrame !== 1) {
      query.paintList = [];
      // query.partRelationList = []
    }
    if (query.outsidePaintFrame !== 1) {
      query.outsidePaintList = [];
      // query.outsidePartRelationList = []
    }
    //权益生效里程
    if (query.effectMileageFrame !== 1) {
      query.fixedBeginMileage = undefined;
      query.fixedEndMileage = undefined;
    }
    if (query.outsideEffectMileageFrame !== 1) {
      query.outsideFixedBeginMileage = undefined;
      query.outsideFixedEndMileage = undefined;
    }
  };
  // 延保聚合判断
  const checkExtend = query => {
    const { detailDisplay } = query;
    if (detailDisplay && Object.keys(detailDisplay).length) {
      if (detailDisplay.limitTimeFrame == '1') {
        if (
          Number(detailDisplay.limitYear) == 0 &&
          Number(detailDisplay.limitMonth) == 0 &&
          Number(detailDisplay.limitDay) == 0
        ) {
          message.warn('聚合权益限制年限不能为0年0月0日');
          return false;
        }
        if (
          detailDisplay.limitYear == undefined &&
          detailDisplay.limitMonth == undefined &&
          detailDisplay.limitDay == undefined
        ) {
          message.warn('聚合权益限制年限不能为0年0月0日');
          return false;
        }
      }

      // 权益生效时间
      if (detailDisplay.timeRadio == '1') {
        detailDisplay.relativeTime = '';
        query.detailDisplay.fixedBeginTime = moment(detailDisplay.fixedRelativeTime[0]).format('YYYY-MM-DD HH:mm:ss');
        query.detailDisplay.fixedEndTime = moment(detailDisplay.fixedRelativeTime[1]).format('YYYY-MM-DD HH:mm:ss');
        delete detailDisplay.fixedRelativeTime;
      }
      if (detailDisplay.timeRadio == '0') {
        query.detailDisplay.fixedBeginTime = '';
        query.detailDisplay.fixedEndTime = '';
        delete detailDisplay.fixedRelativeTime;
      }
    }
    return true;
  };

  const handleCreate = value => {
    console.log('handleCreate', value);

    // 数据处理
    let query = { ...value };
    if (Number(query.goodsCategory) === 1) {
      // 实物类
      // query.name = query.materialName
      const nameAndId = query.materialName?.split('$id$');
      query.name = nameAndId[0];
      query.boutiqueMaterialId = nameAndId[1] ?? null;
    }

    if (query.limitTimeFrame == '1') {
      if (Number(query.limitYear) == 0 && Number(query.limitMonth) == 0 && Number(query.limitDay) == 0) {
        message.warn('权益限制年限不能为0年0月0日');
        return;
      }
      if (query.limitYear == undefined && query.limitMonth == undefined && query.limitDay == undefined) {
        message.warn('权益限制年限不能为0年0月0日');
        return;
      }
    }

    // 享有权益频次
    if (query.frequencyRadio === undefined || query.frequencyRadio == '0') {
      query.frequency = 0;
    }
    if (query.frequencyRadio == '-1') {
      query.frequency = -1;
    }

    // 车联网流量
    if (query.trafficRadio == '0' || query.trafficRadio === undefined) {
      query.traffic = 0;
      query.trafficUnit = '';
    }
    if (query.trafficRadio == '-1') {
      query.traffic = -1;
      query.trafficUnit = undefined;
    }

    // 权益生效时间
    if (query.timeRadio == '1') {
      query.relativeTime = '';
      query.fixedBeginTime = moment(query.fixedRelativeTime[0]).format('YYYY-MM-DD HH:mm:ss');
      query.fixedEndTime = moment(query.fixedRelativeTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete query.fixedRelativeTime;
    }
    if (query.timeRadio == '0') {
      query.fixedBeginTime = '';
      query.fixedEndTime = '';
      delete query.fixedRelativeTime;
    }

    //权益生效时间和权益生效里程，不能同时选择固定时间/固定里程那一项
    if (query.timeRadio === 1 && query.effectMileageFrame === 1) {
      message.warn('由于策略冲突，同一条权益明细不允许同时存在固定生效里程和固定生效开始时间');
      return;
    }
    // 内外不一致
    if (query.consumptionStrategy === 2) {
      // 享有权益频次
      if (query.outsideFrequencyRadio === undefined || query.outsideFrequencyRadio == '0') {
        query.outsideFrequency = 0;
      }
      if (query.outsideFrequencyRadio == '-1') {
        query.outsideFrequency = -1;
      }

      // 车联网流量
      if (query.outsideTrafficRadio == '0' || query.outsideTrafficRadio === undefined) {
        query.outsideTraffic = 0;
        query.outsideTrafficUnit = '';
      }
      if (query.outsideTrafficRadio == '-1') {
        query.outsideTraffic = -1;
        query.outsideTrafficUnit = undefined;
      }
      // 权益生效时间
      if (query.outsideTimeRadio == '1') {
        query.outsideRelativeTime = '';
        query.outsideFixedBeginTime = moment(query.outsideFixedRelativeTime[0]).format('YYYY-MM-DD HH:mm:ss');
        query.outsideFixedEndTime = moment(query.outsideFixedRelativeTime[1]).format('YYYY-MM-DD HH:mm:ss');
        delete query.outsideFixedRelativeTime;
      }
      if (query.outsideTimeRadio == '0') {
        query.outsideFixedBeginTime = '';
        query.outsideFixedEndTime = '';
        delete query.outsideFixedRelativeTime;
      }

      //权益生效时间和权益生效里程，不能同时选择固定时间/固定里程那一项
      if (query.outsideTimeRadio === 1 && query.outsideEffectMileageFrame === 1) {
        message.warn('由于策略冲突，同一条权益明细不允许同时存在固定生效里程和固定生效开始时间');
        return;
      }
    }

    if (query.settlementPriceFrame == 2 && !query.settlementPrice) {
      query.settlementPrice = 0;
    }
    // 延保的聚合明细校验
    const res = checkExtend(query);
    if (!res) {
      return;
    }

    resetFieldsByOther(query);

    // 更新权益明细接口
    setLoading(true);
    if (id && Type === 'edit') {
      const params = {
        id,
        detailNo: dataDetail.detailNo,
        ...query,
      };
      post(allUrl.DetailedManageInterests.updateEquityDetail, { ...params }, { notificationConfig: true }).then(res => {
        if (res.success) {
          message.success('更新成功！');
          // 保存成功跳转到列表
          props.history.push('/RightCenter/DetailManagement');
        } else if (res.msgCode === '9999') {
          setShowModalValue(true);
          setModalMessage(res.msg || '对外展示策略、对内核销策略配置的存在数值不匹配，辛苦重新修改');
          // message.error('更新失败！')
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      });
    } else {
      // 保存接口
      post(allUrl.DetailedManageInterests.saveEquityDetail, { ...query }, { notificationConfig: true }).then(res => {
        if (res.success) {
          message.success('保存成功！');
          // 保存成功跳转到列表
          props.history.push('/RightCenter/DetailManagement');
          // sessionStorage.removeItem('DetailedManageInterestsCode')
        } else if (res.msgCode === '9999') {
          setShowModalValue(true);
          setModalMessage(res.msg || '对外展示策略、对内核销策略配置的存在数值不匹配，辛苦重新修改');
          // message.error('保存失败！')
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      });
    }
  };
  const showModalHandler = () => {
    setShowModalValue(false);
  };

  const onFinishFailed = ({ values, errorFields, outOfDate }) => {
    // if (!values.businessCode) {
    //     return message.warn('业务编码不能为空')
    // }
    console.log(errorFields);

    if (errorFields.length) {
      return message.warn('有必填项未填写');
    }
  };

  /*-----------示例展示start-----------*/

  const showSampleModal = () => {
    setIsModalVisibleSample(true);
  };

  const handleSampleOk = () => {
    setIsModalVisibleSample(false);
  };

  const handleSampleCancel = () => {
    setIsModalVisibleSample(false);
  };

  const time = new Date();
  /*-----------示例展示end-----------*/

  useEffect(() => {
    initPage();
  }, []);
  const initPage = flag => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let h = 0;
    if (!flag) {
      h = winH - 47 - 54 - 345;
    } else {
      h = winH - 47 - 54 - 400;
    }
    setTableHeight(h);
  };

  return (
    <div className="PublicList">
      <Spin spinning={loading}>
        <Row className="tableTitle" style={{ paddingTop: 0, marginBottom: 15 }}>
          <Col className="text">{title}</Col>
          {/* <Col className='text'><Button onClick={() => {
						history.goBack()
					}}>
						返回
					</Button></Col> */}
        </Row>
        <div className="tableData">
          <div
            style={{
              backgroundColor: '#D6EBFF',
              height: 50,
              paddingLeft: 32,
              paddingTop: 13,
              fontSize: 16,
              color: 'rgba(0,0,0,0.85)',
              fontWeight: 500,
            }}
          >
            权益明细信息
          </div>

          <div className="detailPanel" style={{ padding: '24px 0' }}>
            {/* <div className='tableTitle flex' style={{ paddingBottom: 0 }}>
                            <Descriptions title="" style={{ marginTop: -20 }} >
                                <Descriptions.Item label=''></Descriptions.Item>
                            </Descriptions>
                        </div> */}
            <CreateForm
              dataSource={dataDetail}
              // detailNo={detailNo}
              sortOptions={sortOptions}
              typeOptions={typeOptions}
              detailNameOptions={detailNameOptions}
              onFinishFailed={onFinishFailed}
              pageType={Type}
              onCreate={handleCreate}
              showSampleModal={showSampleModal}
              getSortList={getSortList}
            ></CreateForm>
          </div>
        </div>

        <ResultNotify showModal={showModalValue} onOk={showModalHandler} modalMessage={modalMessage} />
        <Modal
          title="示例展示"
          width={800}
          footer={null}
          visible={isModalVisibleSample}
          maskClosable={false}
          onOk={handleSampleOk}
          onCancel={handleSampleCancel}
        >
          <div style={{ textAlign: 'center', width: '100%' }}>
            <div>权益明细配置后的展示（PC端，仅供参考）</div>
            <img style={{ width: 500 }} alt="示例展示" src={SERES} />
          </div>
        </Modal>
      </Spin>
    </div>
  );
};
export default PackManagementDetail;
