import React ,{useEffect,useState} from 'react'
import { Modal, Form ,Input ,DatePicker, message,Spin,Radio,InputNumber} from 'antd'
import moment from 'moment'
import {post} from '@/utils/request'
import allUrl from '@/utils/url'
const EditFlowInfo = (props) => {
    const { visible, title, onCancel ,rowData,GetData} = props
    const [form] = Form.useForm();
    const [loading,setLoading] = useState(false)
    const [carToStoreTime,setCarToStoreTime] = useState(null)
    const limitDecimals = value=> {
        if(!value) return null
        if (typeof value === 'string') {
            return !isNaN(Number(value)) ? parseInt(value || 0) : 0
        } else if (typeof value === 'number') {
            return !isNaN(value) ? String(parseInt(value || 0)) : 0
        } else {
            return 0
        }
    };
    const handleOK = () => {
        console.log(form)
        form.validateFields().then(values=>{
            console.log(values)
            let data = {}
            if(rowData.subsidyType == '1'){
                data = {
                    orderNumber:rowData.orderNumber,
                    subsidyType:rowData.subsidyType,
                    payTime:values.payTime?moment(values.payTime).format('YYYY-MM-DD'):null,
                    deliverTime:values.deliverTime?moment(values.deliverTime).format('YYYY-MM-DD'):'',
                    carToStoreTime:values.carToStoreTime?moment(values.carToStoreTime).format('YYYY-MM-DD'):'',
                    userTakeCarTime:values.userTakeCarTime?moment(values.userTakeCarTime).format('YYYY-MM-DD'):'',
                }
            }else{
                data = {
                    orderNumber:rowData.orderNumber,
                    subsidyType:rowData.subsidyType,
                    typeName:values.typeName || '',
                    rebateAmount:values.rebateAmount || '',
                    orderState:values.orderState || '',
                }
            }
            setLoading(true)
            post(allUrl.orderTimeOut.OvertimeOrderEditor,{...data}).then(res=>{
                if(res && res.success){
                    message.success('编辑成功！')
                    onCancel()
                    GetData()
                }else{
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        })
    }
    const carToStoreTimeChange = (value) =>{
        setCarToStoreTime(value)
        if(!value) form.resetFields(['userTakeCarTime'])
    }
    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };
    useEffect(()=>{
        form.setFieldsValue({
            orderNumber:rowData.orderNumber,
            subsidyType:rowData.subsidyType,
            payTime:rowData.payTime?moment(rowData.payTime):null,
            deliverTime:rowData.deliverTime?moment(rowData.deliverTime):null,
            carToStoreTime:rowData.carToStoreTime?moment(rowData.carToStoreTime):null,
            userTakeCarTime:rowData.userTakeCarTime?moment(rowData.userTakeCarTime):null,
        })
        setCarToStoreTime(rowData.carToStoreTime)
        if(rowData.subsidyType === '2'){
            console.log(rowData.orderState)
            form.setFieldsValue({
                typeName:rowData.typeName,
                rebateAmount:rowData.rebateAmount,
                orderState:rowData.orderState === 8 ? 8 :4,
            })
        }
    },[rowData])
    return (
        <>
            <Modal
                visible={visible}
                title={title}
                onOk={()=>handleOK()}
                onCancel={onCancel}
                maskClosable={false}
                centered={true}
                keyboard={false}
                confirmLoading={loading}
            >
                <Spin spinning={loading}>
                    <Form
                        {...layout}
                        // name="vertical"
                        form={form}
                    >
                        <Form.Item
                            label='订单号'
                            name="orderNumber"
                            rules={[{ required: false, message: '请填写订单号！' }]}
                        >
                            <Input disabled allowClear />
                        </Form.Item>
                        <Form.Item
                            label='订单类型'
                            name="subsidyType"
                            initialValue={1}
                        >
                            <Radio.Group disabled>
                                <Radio value={'1'}>返利补贴</Radio>
                                <Radio value={'2'}>其他</Radio>
                            </Radio.Group>
                        </Form.Item>
                        {
                            rowData.subsidyType === '1' ?
                            <>
                                <Form.Item
                                    label='大定支付时间	'
                                    name="payTime"
                                    rules={[{ required: true, message: '请选择大定支付时间！' }]}
                                >
                                    <DatePicker format='YYYY-MM-DD' style={{width:'100%'}} />
                                </Form.Item>
                                <Form.Item
                                    label='预计交付时间'
                                    name="deliverTime"
                                    rules={[{ required: true, message: '请选择预计交付时间！' }]}
                                >
                                    <DatePicker format='YYYY-MM-DD' style={{width:'100%'}} />
                                </Form.Item>
                                <Form.Item
                                    label='车辆到店时间'
                                    name="carToStoreTime"
                                    rules={[{ required: rowData.carToStoreTime ?true:false, message: '请选择车辆到店时间！' }]}
                                >
                                    <DatePicker format='YYYY-MM-DD' style={{width:'100%'}} onChange={carToStoreTimeChange} />
                                </Form.Item>
                                <Form.Item
                                    label='用户提车时间'
                                    name="userTakeCarTime"
                                    rules={[{ required: rowData.userTakeCarTime ?true:false, message: '请选择用户提车时间！' }]}
                                >
                                    <DatePicker format='YYYY-MM-DD' style={{width:'100%'}} disabled={carToStoreTime?false:true} />
                                </Form.Item>
                            </>
                            :<>
                                <Form.Item
                                    label='类型名称'
                                    name="typeName"
                                    rules={[
                                        { required: true, message: '请填写类型名称！' },
                                        {
                                            validator(_, value) {
                                                if(value && value.length>20){
                                                    return Promise.reject(new Error('类型名称最多20个字符！'));
                                                }else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}
                                >
                                    <Input allowClear />
                                </Form.Item>
                                <Form.Item
                                    label='补贴金额(元)'
                                    name="rebateAmount"
                                    rules={[
                                        { required: true, message: '请填写补贴金额！' },
                                    ]}
                                >
                                    <InputNumber 
                                        allowClear 
                                        step={0.01} 
                                        stringMode style={{width:'100%'}}
                                        formatter={limitDecimals}
                                        parser={limitDecimals}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label='是否可提现'
                                    name="orderState"
                                    rules={[
                                        { required: true, message: '请选择是否可提现！' },
                                    ]}
                                >
                                    <Radio.Group>
                                        <Radio value={4}>可提现</Radio>
                                        <Radio value={8}>不可提现</Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </>
                        }
                    </Form>
                </Spin>
            </Modal>
        </>
    )
}
export default EditFlowInfo