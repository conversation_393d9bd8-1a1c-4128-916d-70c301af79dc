import React, { useState, useEffect } from 'react'
import { Modal, Tree, message, Steps, Button, Result } from 'antd'
import allUrl from '../../../../utils/url'
import { post,get} from '../../../../utils/request'
import _ from 'lodash'
// import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import './OrgTransfer.less'
const { Step } = Steps;
const OrgTransfer = (props) => {
    const { cb, title, visible, onCancel, parentSelectRows } = props
    console.log(parentSelectRows[0])
    const [treeData, setTreeData] = useState([])
    const [treeselectedKeys, setTreeselectedKeys] = useState([])
    const [treeSelectedRows, setTreeRows] = useState([])
    const [current, setCurrent] = React.useState(0);
    const handleOk = () => {
        
    }
    const treeSelect = (selectedKeys, selectedNodes, event) => {
        setTreeselectedKeys(selectedKeys)
        setTreeRows(selectedNodes.selectedNodes)
    }
    const updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }

            if (node.children) {
                return { ...node, children: updateTreeData(node.children, key, children) };
            }

            return node;
        });
    }
    const lookAt = () =>{
        onCancel()
    }
    useEffect(() => {
        post(allUrl.Authority.orgPageList).then(res => {
            if (res.success && res.resp.length) {
                let Dt = res.resp
                for(let i =0 ;i <Dt.length;i++){
                    if(Dt[i].key === parentSelectRows[0].key){
                        Dt.splice(i,1)
                        i--
                        break
                    }
                    if(Dt[i].children && Dt[i].children.length){
                        for(let y=0;y<Dt[i].children.length;y++){
                            if(Dt[i].children[y].key === parentSelectRows[0].key){
                                Dt[i].children.splice(y,1)
                                y--
                            }
                        }
                    }
                }
                setTreeData(res.resp)
            } else {
                // message.error(res.msg)
            }
        })
    }, [parentSelectRows])
    const next = () => {
        console.log(treeSelectedRows,parentSelectRows)
        // setCurrent(current + 1);
        post(allUrl.Authority.transfer,{fromId:parentSelectRows[0].key,toId:treeSelectedRows[0].key}).then(res=>{
            if(res.success){
                message.success(res.msg)
                onCancel()
                cb()
            }else{
                // message.error(res.msg)
            }
        })
    };

    const prev = () => {
        setTreeRows([])
        setTreeselectedKeys([])
        setCurrent(current - 1);
    };
    const steps = [
        {
            title: '选择转移的上级组织',
            content: treeData && treeData.length ? <Tree defaultExpandedKeys={[1]} treeData={treeData} onSelect={treeSelect} /> : null
        },
        {
            title: '完成',
            content: <Result
                status="success"
                title="操作成功"
                subTitle="您已完成组织转移"
                extra={[
                    <Button key='1' type="primary" onClick={() => lookAt()}>
                        查看
                    </Button>,
                ]}
            />
        },
    ];
    console.log(current)
    return <Modal width={700} visible={visible} wrapClassName='OrgTransfer' title={title} onOk={handleOk} onCancel={onCancel} maskClosable={false} footer={false}>
        <p style={{ marginBottom: '10px', fontSize: '16px' }}>已选组织：{parentSelectRows[0].title}</p>
        <Steps current={current}>
            {steps.map(item => (
                <Step key={item.title} title={item.title} />
            ))}
        </Steps>
        <div className="steps-content">
            {steps[current].content}
        </div>
        <div className="steps-action">
            {current < 1 && (
                <Button type="primary" onClick={() => next()} disabled={!treeselectedKeys.length}>
                    完成
                </Button>
            )}
            {/* {current === steps.length - 1 && (
                <Button type="primary" onClick={() => message.success('Processing complete!')}>
                    完成
                </Button>
            )} */}
            {/* {current > 0 && (
                <Button style={{ margin: '0 8px' }} onClick={() => prev()}>
                    上一步
                </Button>
            )} */}
        </div>
    </Modal>
}
export default OrgTransfer