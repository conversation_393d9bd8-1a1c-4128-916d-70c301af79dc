import React, { useEffect, useState,useRef } from 'react'
import { useSelector,useDispatch } from 'react-redux'
import { Timeline,message, Popconfirm, Tabs ,Row,Col,Tag,Modal} from 'antd'
import moment from 'moment'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import { post, get } from '@/utils/request';
import allUrl from '@/utils/url';
import {roleJudgment} from '@/utils/authority'
import {setStorage,getStorage} from '../../../utils/Storage'
import {getDict} from '../../../actions/async'
import _ from "lodash";
import './index.less'

const { TabPane } = Tabs;

const LockList = (props) => {
    //创建ref节点
    const childRef1 = useRef(null);
    const tableRef1 = useRef(null);
    const dispatch = useDispatch()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const [current, setCurrent] = useState(1)
    const [tableHeight, setTableHeight] = useState(0)
    const [pageSize, setPageSize] = useState(10)
    const [historyVisible, setHistoryVisible] = useState(false)
    const [dictSellStatus,setDictSellStatus] = useState([])
    const [historyData, setHistoryData] = useState([])
    const [defaultQuery, setDefaultQuery] = useState({
        exteriorColorCode:'',
        interiorColorCode:'',
        optionalPackageCode:'',
        configCode:'',
        lockStatusList: [],
        scrmModelCode: '',
        modelCode: ''
    })
    const [treeOrgData, setTreeOrgData] = useState([])
    const [dictLockType, setDictLockType] = useState([])
    const [dictSalesOrderStatus,setDictSalesOrderStatus] = useState([])
    const [carDict,setCarDict] = useState([])
    const [carType, setCarType] = useState([])
    const [carConfig, setCarConfig] = useState([])
    const [carExColor, setCarExColor] = useState([])
    const [carInColor, setCarInColor] = useState([])
    const [carOpPackage, setCarOpPackage] = useState([])
    const onSearch = (values) => {
        console.log('values', values)
        // values = Object.assign(defaultQuexry, values)
        console.log('values', values)
        if(!values.scrmModelCode && values.configCode) {
            message.error('必须选择车型')
            return
        }
        if((!values.configCode || !values.scrmModelCode) 
        && ((values.exteriorColorCode && values.exteriorColorCode.length > 0) || (values.interiorColorCode && values.interiorColorCode.length > 0) || (values.optionalPackageCode && values.optionalPackageCode.length > 0))) {
            message.error('必须选择配置和车型')
            return
        }
        if (values.lockTime && values.lockTime.length) {
            values.lockTimeStart = moment(values.lockTime[0]).format('YYYY-MM-DD')
            values.lockTimeEnd = moment(values.lockTime[1]).format('YYYY-MM-DD')
            delete values.lockTime
        }
        if(values.scrmModelCode){
            carDict.map((i) => {
                if(i.scrmModelCode == values.scrmModelCode) {
                    values.modelCode = i.modelCode
                }
            })
        }
        if(values.exteriorColorCode && values.exteriorColorCode.length){
             values.exteriorColorCode = values.exteriorColorCode.toString()
        } else {
            values.exteriorColorCode = ''
        }
        if(values.interiorColorCode && values.interiorColorCode.length){
            values.interiorColorCode = values.interiorColorCode.toString()
        } else {
            values.interiorColorCode = ''
        }
        if(values.optionalPackageCode && values.optionalPackageCode.length){
            values.optionalPackageCode = values.optionalPackageCode.toString()
        } else {
            values.optionalPackageCode = ''
        }
        setDefaultQuery(values)
      
    }
    // 获取车型及所有车辆配置
    const getCarDict = () => {
        post(allUrl.Lightning.flashSkuConfig).then((res) => {
          if (res.success) {
            const resp = res.resp;
            console.log('配置', resp)
            setCarDict(resp);
            let arr = []
            //提取数据
            resp.map((i) => {
                    arr.push({name: i.scrmModelName, value: i.scrmModelCode})
                })
                setCarType(arr)
            }
        })
    };
    const LookAtHistory = (record) => {
        getLockCarHistoryList(record.id)
       
    }
    // 查看锁车历史记录
    const getLockCarHistoryList = (id = "") => {
        get(allUrl.Lightning.getLockCarHistoryList, { id }).then((res) => {
          if (res.success) {
            const arr = res.resp[0];
            console.log('历史记录', arr)
            setHistoryData(arr);
            setHistoryVisible(true)
          } else {
            message.error(res.msg);
          }
        });
    };
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'LIGHTNING_LOCK_HISTORY') ?
                    [
                        <span key={1} style={{ cursor: 'pointer', marginRight: 10 }} onClick={() => LookAtHistory(record)}>查看历史记录</span>,
                    ] : null
            }
            {
                // 有权限且需要锁车状态是待关联批售单时才展示
                roleJudgment(userInfo,'LIGHTNING_LOCK_BTN') && record.lockStatus == 1 ?
                    [
                        <Popconfirm onConfirm={() => unlock(record)} title='解锁后将释放该车辆资源，是否确认解锁？' okText='确定' cancelText='取消'>
                            <span key={2} style={{ cursor: 'pointer' }}>解锁</span>
                        </Popconfirm>
                    ] : null
            }
        </div>
    }
    const initPage = () => {
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let FormQueryH = document.getElementsByClassName('PublicList_FormQuery')[0].clientHeight
        let h = winH - FormQueryH - 47 - 54 - 305
        setTableHeight(h)
    }
    useEffect(() => {
        initPage()
    }, [])

    // 过滤数据
    const getShopData = (arr1, type) => {
        let temp = []
        let forFn1 = function (arr, type) {
            for (let i = 0; i < arr.length; i++) {
                let item = arr[i]
                if (item.type === type && item.code) {
                    temp.push(item)
                } else {
                    if (item.children) {
                        forFn1(item.children, 1)
                    }
                }
            }
        }
        if (arr1 && arr1.length) {
            forFn1(arr1, 1)
        }
        console.log(temp.length)

        return temp
    }
    // 获取组织列表
    const getOrgData = (name = "") => {
        post(allUrl.Authority.orgPageList, { name, type: 1 }).then((res) => {
        if (res.success) {
            const arr = res.resp.slice(0);
            setTreeOrgData(getShopData(arr));
        } else {
            message.error(res.msg);
        }
        });
    };
    // 组织搜索
    const searchOrg = _.debounce((value) => {
        if (value) {
            getOrgData(value);
        }
    }, 500);
    // 获取字典数据
    const getDictData = () => {
        get(allUrl.common.entryLists, {codes:'scrm_flash_lock_type,sales_order_deliver_status,scrm_flash_sell_status'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt['scrm_flash_lock_type'].forEach(item=>{
                    item.name = item.entryMeaning
                    item.value = Number(item.entryValue)
                })
                Dt['sales_order_deliver_status'].forEach(item=>{
                    item.name = item.entryMeaning
                    item.value = Number(item.entryValue)
                })
                Dt['scrm_flash_sell_status'].forEach(item=>{
                    item.name = item.entryMeaning
                    item.value = Number(item.entryValue)
                })
                setDictLockType(Dt['scrm_flash_lock_type'])
                setDictSalesOrderStatus(Dt['sales_order_deliver_status'])
                console.log('123', Dt['sales_order_deliver_status'])
                setDictSellStatus(Dt['scrm_flash_sell_status'])
            } 
        })
        
    }
    /**
     * 根据字典和id返回状态名称
     * @param {Array} dict 
     * @param {string} id 
     * @returns 
     */
    const renderStatusName = (dict, id) => {
        console.log('dict', dict, id)
        let str = ''
        dict.map(item => {
            if(item.value == id) {
                str = item.name
            }
        })
        return str
    }
    const selectCarType = (code, option) => {
        console.log('选择车型' ,option)
        childRef1.current.setFieldValue('configCode', '')
        childRef1.current.setFieldValue('exteriorColorCode', [])
        childRef1.current.setFieldValue('interiorColorCode', [])
        childRef1.current.setFieldValue('optionalPackageCode', [])
        carDict.map((i) => {
            if(i.scrmModelCode == code) {
                setCarConfig(i.config)
            }
        })
    }
    const selectCarConfig = (code) => {
        console.log('选择配置' ,code)
        childRef1.current.setFieldValue('exteriorColorCode', [])
        childRef1.current.setFieldValue('interiorColorCode', [])
        childRef1.current.setFieldValue('optionalPackageCode', [])
        carConfig.map((i) => {
            if(i.configCode == code) {
                setCarInColor(i.interiorColorSet)
                setCarExColor(i.exteriorColorSet)
                setCarOpPackage(i.optionalPackageSet)
            }
        })
    }
    const selectShop = (value, node) => {
        console.log('select选择组织', value, node)
    }
    const ChangeOrg = (value) => {
        if(value == undefined) {
            getOrgData()
        }
    }
    // 解锁
    const unlock = (record) => {
        console.log('解锁操作',record)
        post(allUrl.Lightning.unlockCar, { lockInfoId:record.id }).then((res) => {
            if (res.success) {
                tableRef1.current.getTableData()
            } else {
                message.error(res.msg);
            }
            });
    }
    useEffect(()=>{
        getCarDict()
        getDictData()
        getOrgData()
    },[])
    const columns1 = [
        { title: '车辆VIN', dataIndex: 'vin', width: 120, fixed: 'left' },
        { title: '批售单号', dataIndex: 'wholesaleOrderNo', width: 100 },
        { title: '交付单号', dataIndex: 'deliverOrderNo',  width: 100 },
        { title: '车型名称', dataIndex: 'modelName', width: 100 },
        { title: '配置', dataIndex: 'configCodeName', width: 100 },
        { title: '外饰', dataIndex: 'exteriorColorName', width: 100 },
        { title: '内饰', dataIndex: 'interiorColorName', width: 150 },
        { title: '选装包', dataIndex: 'optionalPackageName', width: 200 }, 
        { title: '客户名称', dataIndex: 'customerName', width: 100 },
        { title: '客户手机号', dataIndex: 'customerPhone', width: 120 },
        { title: '锁定人', dataIndex: 'lockUserName' },
        { title: '锁定人岗位', dataIndex: 'lockUserPositionName' },
        { title: '锁定人手机号', dataIndex: 'lockUserPhone', width: 120 },
        { title: '门店简称', dataIndex: 'name' },
        { title: '用户中心', dataIndex: 'userCenter'},
        { title: '锁车状态', dataIndex: 'lockStatus', width: 110 ,render:text=> renderStatusName(dictSellStatus, text)},
        { title: '变更原因', dataIndex: 'lockTypeName'},
        { title: '交付状态', dataIndex: 'deliverOrderNoStatus', width: 100 ,render:text=> renderStatusName(dictSalesOrderStatus, text)},
        { title: '锁定时间', dataIndex: 'lockTime', width: 180 },
        { title: '操作', fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
    ]
    
    let searchList1 = [    
        { label: '车辆VIN', name: 'vin', type: 'Input', placeholder: '请输入关键字', colSpan: 6 },
        { label: '批售单号', name: 'wholesaleOrderNo', type: 'Input',  colSpan: 6 },
        { label: '交付单号', name: 'deliverOrderNo', type: 'Input', colSpan: 6 },
        { label: '客户姓名', name: 'customerName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '锁定时间', name: 'lockTime', type: 'RangePicker', placeholder: ['开始时间', '截止时间'], colSpan: 6 },
        {
            label: "用户中心",
            name: "dealerCode",
            initialValue: '',
            type: "TreeSelect",
            data: treeOrgData,
            onSearch: searchOrg,
            onChange: ChangeOrg,
            colSpan: 6,
            fieldNames:{
               label: 'title', value: 'code'
            },
            onSelect: selectShop,
            placeholder: "选择组织",
        },
        { label: '锁车状态', name: 'lockStatusList', initialValue: [], type: 'Select', mode: 'multiple', placeholder: '请选择', colSpan: 6, data: dictSellStatus },
        { label: '变更原因', name: 'lockType', type: 'Select', placeholder: '请选择', colSpan: 6, data: dictLockType },
        { label: '交付状态', name: 'deliverOrderNoStatus', type: 'Select', placeholder: '请选择', colSpan: 6, data: dictSalesOrderStatus },
        { label: '车型', name: 'scrmModelCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: carDict, 
        onChange: selectCarType, fieldNames: {label: 'scrmModelName', value: 'scrmModelCode'}  },
        { label: '配置', name: 'configCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: carConfig, 
        onChange: selectCarConfig, fieldNames: {label: 'configCodeName', value: 'configCode'} },
        { label: '外饰', name: 'exteriorColorCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: carExColor,
        initialValue: [], fieldNames: {label:'codeName', value: 'code'}, mode: 'multiple' },
        { label: '内饰', name: 'interiorColorCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: carInColor,
        initialValue: [], fieldNames: {label:'codeName', value: 'code'}, mode: 'multiple' },
        { label: '选装包', name: 'optionalPackageCode', type: 'Select', placeholder: '请选择', colSpan: 6, data: carOpPackage,
        initialValue: [], fieldNames: {label:'codeName', value: 'code'}, mode: 'multiple' },
        
    ]

    return (
        <div className='LightningLock'>
            <PublicTableQuery onSearch={onSearch} searchList={searchList1}  isCatch={false} isFormDown={false} ref={childRef1}/>
            <div className='tableData'>
                <Row className='tableTitle'>
                    <Col span={24} style={{textAlign:'right'}}>
                        {/* {
                            roleJudgment(userInfo,'TRUCK_LOGISTICS_CARRIAGETYPE1_EXPORT') ?
                                <Button type='primary' onClick={()=>ExportExcel(1)} >导出</Button>
                                : null
                        } */}
                    </Col>
                </Row>
                    <PublicTable
                        url={allUrl.Lightning.getLockCarList}
                        scroll={{ x: "max-content", y: tableHeight }}
                        isCatch={true}
                        columns={columns1}
                        type={2}
                        ref={tableRef1}
                        rowSelection={false}
                        setCurrent={setCurrent}
                        setPageSize={setPageSize}
                        defaultQuery={{...defaultQuery}}
                    />
            </div>
            {
                 historyVisible && 
            <Modal width={600} footer={null} visible={historyVisible} title='历史记录' onOk={() => setHistoryVisible(false)}  onCancel={()=>setHistoryVisible(false)}>
               <div className='Record'>
                    <Timeline>
                        {   historyData &&   
                            historyData.map((item, index) => {
                                return <Timeline.Item key={index} color={index === 0 ? 'blue' : 'gray'}>
                                        <div className='TimelineItem_Con'>
                                            <span className='time' style={{marginRight:'20px'}}>{item.createTime}</span>
                                            <span className='statusCn'>
                                                { item.lockStatus ==1?
                                                    <Tag color="gold">{renderStatusName(dictSellStatus,item.lockStatus)}</Tag> : 
                                                    item.lockStatus ==2?
                                                    <Tag color="gray">{renderStatusName(dictSellStatus,item.lockStatus)}</Tag> : 
                                                    <Tag color="orange">{renderStatusName(dictSellStatus,item.lockStatus)}</Tag> 
                                                }
                                            </span>
                                            <p className='address'><span className="bold">VIN：</span>{item.vin || '-'}</p>
                                            <p className='address'><span className="bold">批售单号：</span>{item.wholesaleOrderNo || '-'}</p>
                                            {
                                                // 已解锁状态才展示以下信息
                                                item.lockStatus == 3 ? <>
                                                    <p className='address'><span className="bold">解锁人ID：</span>{item.unlockUserId || '-'}</p>
                                                    <p className='address'><span className="bold">解锁人：</span>{item.unlockUserName || '-'}</p>
                                                    <p className='address'><span className="bold">解锁人岗位：</span>{item.unlockUserPosition || '-'}</p>
                                                    <p className='address'><span className="bold">解锁人组织：</span>{item.unlockUserOrganizationName || '-'}</p>
                                                </> : null
                                            }
                                           
                                        </div>
                                    </Timeline.Item>
                            })
                        }
                    </Timeline>
                </div>
            </Modal>
            }
        </div>
    )
}
export default LockList
