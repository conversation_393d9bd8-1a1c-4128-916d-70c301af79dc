let Host = '',
  SSO_Host = '';
let EXSSO_HOST = '',
  SSO_REDIRECT = '',
  SSO_SDK = '';
let FILE_PSA_URL = ''; // 文件上传下载域名
const mode = process.env.mode;

if (process.env.REACT_APP_ENV === 'dev') {
  Host = 'https://api-dev.seres.cn';
  SSO_Host = 'https://seres-sso-dev.seres.cn/';
  EXSSO_HOST = 'https://exsso-dev.seres.cn';
  SSO_REDIRECT = 'https://scrm-dev.seres.cn';
  SSO_SDK = 'https://exsso-dev.seres.cn/seres-sso-sdk-1.0.0.js';
  FILE_PSA_URL = 'https://file-m-dev.seres.cn';
} else if (process.env.REACT_APP_ENV === 'test') {
  Host = 'https://api-test.seres.cn';
  SSO_Host = 'https://seres-sso-test.seres.cn/';
  EXSSO_HOST = 'https://exsso-test.seres.cn';
  SSO_REDIRECT = 'https://scrm-test.seres.cn';
  SSO_SDK = 'https://exsso-test.seres.cn/seres-sso-sdk-1.0.0.js';
  FILE_PSA_URL = 'https://file-m-test.seres.cn';
} else if (process.env.REACT_APP_ENV === 'uat') {
  Host = 'https://api-uat.seres.cn';
  SSO_Host = 'https://seres-sso-uat.seres.cn/';
  EXSSO_HOST = 'https://exsso-uat.seres.cn';
  SSO_REDIRECT = 'https://scrm-uat.seres.cn';
  SSO_SDK = 'https://exsso-uat.seres.cn/seres-sso-sdk-1.0.0.js';
  FILE_PSA_URL = 'https://file-m-uat.seres.cn';
} else if (process.env.REACT_APP_ENV === 'prod') {
  Host = 'https://api.seres.cn';
  SSO_Host = 'https://seres-sso.seres.cn/';
  EXSSO_HOST = 'https://exsso.seres.cn';
  SSO_REDIRECT = 'https://scrm.seres.cn';
  SSO_SDK = 'https://exsso.seres.cn/seres-sso-sdk-1.0.0.js';
  FILE_PSA_URL = 'https://file-m.seres.cn';
}
export default { Host, SSO_Host, EXSSO_HOST, SSO_REDIRECT, SSO_SDK, FILE_PSA_URL };
