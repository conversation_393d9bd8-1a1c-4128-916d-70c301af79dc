import React, { useMemo, useEffect, useRef, useState, useReducer, createContext } from 'react'
import _ from 'lodash'
import './CreateForm.less'
import CreateStrategy from './CreateStrategy'
import { Form, Input, InputNumber, Select, Button, Radio, Space, Modal, message, Table, Row, Col } from 'antd';
import moment from 'moment'
import BatchAdd from './BatchAdd'
import CreateFormDetail from './CreateFormDetail';
import history from '@/utils/history'
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { detailFormConfig } from './formConfig'
import ExtendForm from './extend/Index'

import { get, post, axiosDelete } from '@/utils/request';
import { columnsPart } from '@/utils/columns'
import allUrl from '@/utils/url';
import styles from '../antd.module.less'
const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;
const layout = {
	labelCol: { span: 3 },
	wrapperCol: { span: 15 },
};

export const InjectContext = React.createContext(null)

function reduder(state, action) {
	switch (action.type) {
		case 'update': {
			let { idChecked, rows, checked } = action.payload

			return {
				...state,
				...{
					idChecked,
					rows,
					checked
				}
			}
		}
		case 'delete': {
			let { row } = action.payload

			let rows = _.cloneDeep(state.rows)
			let idChecked = _.cloneDeep(state.idChecked)
			let checked = _.cloneDeep(state.checked)

			let deleteIndex = checked.indexOf(row.key)
			checked.splice(deleteIndex, 1)
			idChecked.splice(deleteIndex, 1)
			rows.splice(deleteIndex, 1)

			return {
				...state,
				...{
					rows,
					idChecked,
					checked
				}
			}
		}
		case 'search': {
			let { searchObj } = action.payload

			return {
				...state,
				...{
					searchObj
				}
			}
		}
		default:
			return state
	}
}
const Component = (props) => {
	const [state, dispatch] = useReducer(reduder, { idChecked: [], rows: [], checked: [] })
	const { getSortList } = props
	const [form] = Form.useForm()
	const [consumptionStrategy, setConsumptionStrategy] = useState('1')// 策略一致 or 不一致
	const [performingListObj, setPerformingListObj] = useState([]) // 权益履约方
	const [limitYearRadioDisplay, setLimitYearRadioDisplay] = useState('')
	const [frequencyRadioDisplay, setFrequencyRadioDisplay] = useState('')
	const [trafficRadioDisplay, setTrafficRadioDisplay] = useState('')
	const [timeRadioChangeDisplay, setTimeRadioChangeDisplay] = useState('0')
	const [mileageRadioChangeDisplay, setMileageRadioChangeDisplay] = useState('0')
	const [settlementPriceFrameDisplay, setSettlementPriceFrameDisplay] = useState('1') // 结算策略显示
	const [backWidth, setBackWidth] = useState({})
	const [showBatchAdd, setShowBatchAdd] = useState(false) // 关联配件
	const [existPartValue, setExistPartValue] = useState(1) // 关联配件值
	const [tableColumns, setTableColumns] = useState([]) // 配件关联列表
	const [scene, setScene] = useState(1)   //权益业务场景
	const [usable, setUsable] = useState(false) //前权益是否可用
	// const [formStrategyConfig, setFormStartegyConfig] = useState({}) // 根据前权益设置默认置灰策略以及默认选项
	const [formData, setFormData] = useState({}) // 根据前权益设置默认置灰策略以及默认选项

	const [goodsCategory, setGoodsCategory] = useState(0) //权益商品分类(子组件传过来)

	// useEffect(() => {
	//   console.log('formStrategyConfig', formStrategyConfig);

	//   },[formStrategyConfig])
	const formRef = useRef(null)

	const dataSourceForm = useMemo(() => props.dataSource, [props.dataSource])

	const [entryList, setEntryList] = useState({})
	const { sortOptions, typeOptions, detailNameOptions, pageType } = props



	const onDelete = (row) => {
		dispatch({ type: 'delete', payload: { row } })
	}

	const filterValue = (v) => {
		let a = ''
		if (v) {
			a = v < 0 ? '-1' : '1';
		} else if (a == 0) {
			a = '0'
		} else {
			a = ''
		}
		return a
	}

	const onFinish = values => {
		if (performingListObj) {
			values.performingList = performingListObj
		} else {
			values.performingList = []
		}
		// 补漆面选项
		if (values.paintFrame !== 1) {
			delete values.paintList
		}
		// 策略不一致时
		if (consumptionStrategy == 2 && values.outsidePaintFrame !== 1) {
			delete values.outsidePaintList
		}
		// 权益明细版本(创建没有版本)
		values.detailVersion = dataSourceForm?.detailVersion || ''
		// 延保场景
		if (scene === 2) {
			// 策略固定为1
			values.consumptionStrategy = 1
			// 原权益明细版本
			values.originDetailVersion = formData.originDetail.detailVersion
			// 商品分类默认为虚拟类
			values.goodsCategory = 0
			// 权益配件关联
			if (state.rows.length) {
				values.partRelationList = _.cloneDeep(state.rows)
			} else {
				message.warn('未添加延保配件，影响履约核销，请添加后再保存')
				return
			}
		} else {
			// 权益配件关联
			if (existPartValue == 1 && state.rows) {
				values.partRelationList = _.cloneDeep(state.rows)
			}
		}
		props.onCreate && props.onCreate(values)
	};

	const onFinishFailed = params => {
		props.onFinishFailed && props.onFinishFailed(params)
	}

	// 内外是否一致
	const consumptionStrategyChange = value => {
		setConsumptionStrategy(value)
	}

	// 结算策略
	const settlementPriceFrameChange = value => {
		setSettlementPriceFrameDisplay(value)
	}

	// 关联配件显示
	const batchAddShow = value => {
		setShowBatchAdd(value)
	}
	// 配件关联提交
	const batchAddSubmit = (state) => {
		let { idChecked, rows, checked } = state

		dispatch({ type: 'update', payload: { idChecked, rows, checked } })
		batchAddShow(false)
	}
	// 配件关联按钮
	const existPartChange = e => {
		setExistPartValue(e.target.value)
	}


	// 删除
	const DeteAt = () => {
		const { detailNo } = dataSourceForm
		get(allUrl.DetailedManageInterests.ableToDelete, { detailNo }).then(res => {
			if (res.success) {
				confirm({
					title: '确认删除该权益明细吗？删除后不可恢复！',
					onOk() {
						axiosDelete(`${allUrl.DetailedManageInterests.canDelete}${detailNo}`).then(res => {
							if (res.success) {
								message.success('删除成功！')
								history.goBack()
							}
						})
					},
					onCancel() {
						console.log('Cancel');
					},
				});
			} else {
				// message.error(res.msg)
			}
		})


	}

	//业务场景切换
	const sceneChange = (value) => {
		setScene(value)
		if (Number(value) === 1) {
			setUsable(false)
		}
	}
	useEffect(() => {
		let newColumns = _.cloneDeep(columnsPart)
		newColumns.push({ title: '操作', width: 40, fixed: 'right', dataIndex: 'Operation', render: (text, record) => <Button type='link' size='small' onClick={() => { onDelete(record) }}>删除</Button> })
		setTableColumns(newColumns)
	}, [])
	useEffect(() => {
		let item = document.getElementsByClassName('Content-con')[0]
		setBackWidth({ width: `${item.clientWidth}px`, height: '60px' })
	}, [])
	useEffect(() => {
		if (dataSourceForm) {
			formRef.current &&
				form.setFieldsValue({
					consumptionStrategy: consumptionStrategy ? dataSourceForm.consumptionStrategy : '1',
				})
			// 配件关联值
			setExistPartValue(dataSourceForm.existPart)

			// 策略是否一致
			if (dataSourceForm.consumptionStrategy) {
				setConsumptionStrategy(dataSourceForm.consumptionStrategy)
			}

			// 配件关联
			if (dataSourceForm.existPart == 1 && dataSourceForm.partRelationList) {
				let arr = []
				dataSourceForm.partRelationList.map(item => {
					item.key = item.carType + item.partNo + item.partVersion
					// arr.push(item.id)
					arr.push(item.key)
				})
				dispatch({ type: 'update', payload: { idChecked: arr, rows: dataSourceForm.partRelationList, checked: arr } })
			}
			// 结算价格
			settlementPriceFrameChange(dataSourceForm.settlementPriceFrame)
			// 设置场景
			sceneChange(dataSourceForm.businessScene)

		}
	}, [dataSourceForm, form])

	useEffect(() => {
		get(allUrl.common.entryLists, { codes: 'equity_business_scene,equity_car_attr,equity_belong,equity_identity,equity_car_identity,equity_detail_refund,equity_detail_performance,equity_business_code, equity_relativeTime, equity_performing,equity_extend_warranty_active_time,equity_relative_begin_mileage_strategy ' }).then(res => {
			if (res.success) {
				let Dt = res.resp[0]
				for (let i in Dt) {
					Dt[i].forEach(item => {
						item.name = item.entryMeaning
						item.value = item.entryValue
						item.businessScene = item.extendField1
						item.description = item.extendField2
					})
				}
				setEntryList(Dt || {})
			} else {
				//   message.error(res.message)
			}
		})
	}, [])
	return (
		<div className='detail-create'>
			<Form {...layout} name="add" ref={formRef} form={form} onFinish={onFinish} onFinishFailed={onFinishFailed}>
				<Form.Item label="权益业务场景" name="businessScene" initialValue={1}>
					<Radio.Group onChange={e => sceneChange(e.target.value)} disabled={sessionStorage.getItem('createType') === 'edit'}>
						<Space size={20} >
							{entryList['equity_business_scene'] && entryList['equity_business_scene'].length ?
								entryList['equity_business_scene'].filter(i => i.value < 3).map((item, index) => <Radio value={Number(item.value)}>{item.name}
									{item.value == 2 ? <span className='custom-item-help custom-item-help-mt6'>(当前仅支持整车质保、三电和单个配件的延保)</span> : ''}
								</Radio>) : null}
						</Space>
					</Radio.Group>
				</Form.Item>

				{
					scene === 1 ?
						<>
							{/* 基本场景 */}
							<CreateFormDetail
								{...props}
								scene={scene}
								goodsCategory={goodsCategory}
								setGoodsCategory={setGoodsCategory}
								formRef={formRef}
								getSortList={getSortList}
								dataSource={dataSourceForm}
								formConfig={detailFormConfig}
								setPerformingListObj={setPerformingListObj}
							/>

							<div style={{ height: 20, backgroundColor: '#F0F2F5', marginTop: 60 }}></div>
							<div style={{ backgroundColor: "#D6EBFF", height: 50, marginBottom: 24, paddingLeft: 32, paddingTop: 13, fontSize: 16, color: "rgba(0,0,0,0.85)", fontWeight: 500 }}>权益明细策略</div>
							{/* {对内核销策略与对外展示策略是否一致 - 实物类权益隐藏} */}
							<Form.Item
								className={goodsCategory === 1 ? styles.globalHide : ''}
								labelCol={4}
								style={{ marginLeft: '30px' }}
								label="对内核销策略与对外展示策略是否一致"
								name="consumptionStrategy"
								initialValue={1}>
								<Radio.Group onChange={e => consumptionStrategyChange(e.target.value)}>
									<Space size={20} >
										<Radio value={1}>一致</Radio>
										<Radio value={2}>不一致</Radio>
									</Space>
								</Radio.Group>
							</Form.Item>
							{/* 这里有个坑：无论场景是几，默认都会走到这里，目的是用这里的一大堆判断 （◐ˍ◑） */}
							<CreateStrategy
								consumptionStrategy={consumptionStrategy}
								dataSource={dataSourceForm}
								formObj={form}
								goodsCategory={goodsCategory}
							/>
						</> :
						<>
							{/* 延保场景 */}
							<InjectContext.Provider value={{ form: form, formRef: formRef, dataSource: dataSourceForm, ...props, entryList, getSortList, formConfig: detailFormConfig, setPerformingListObj, usable, setUsable, formData, setFormData, goodsCategory, setGoodsCategory }}>
								<ExtendForm />
							</InjectContext.Provider>
						</>
				}

				{/* 履约信息-实物类权益隐藏 */}
				{(scene === 2 && !usable) ? '' : <div className={goodsCategory === 1 ? styles.globalHide : ''}>
					<div style={{ height: 20, backgroundColor: '#F0F2F5' }}></div>
					<div style={{ backgroundColor: "#D6EBFF", height: 50, paddingLeft: 32, paddingTop: 13, fontSize: 16, color: "rgba(0,0,0,0.85)", fontWeight: 500 }}>履约信息</div>
					<div style={{ paddingTop: '20px' }}>
						<Form.Item label="履约结算价格" style={{ marginBottom: 0 }}>
							<Form.Item name="settlementPriceFrame" style={{ display: 'inline-block' }} initialValue={0}>
								<Radio.Group onChange={e => settlementPriceFrameChange(e.target.value)}>
									<Space align='center' size={20} >
										<Radio value={0}>无此策略</Radio>
										<Radio value={1}>实际结算</Radio>
										<Radio value={2}>单价</Radio>
									</Space>
								</Radio.Group>
							</Form.Item>
							{settlementPriceFrameDisplay == 2 ? <><Form.Item
								name="settlementPrice"
								noStyle
							>
								<InputNumber min={0} style={{ display: 'inline-block', width: '100px' }} allowClear />
							</Form.Item><span style={{ marginLeft: '5px' }}>元</span></> : null}
						</Form.Item>

						<Form.Item label="延保承保方" name="extendWarrantyAssurer">
							<Radio.Group defaultValue={1}>
								<Space align='center' size={20} >
									{/* <Radio value={0}>无此策略</Radio> */}
									<Radio value={1}>保险</Radio>
									<Radio value={2}>车厂</Radio>
								</Space>
							</Radio.Group>
						</Form.Item>
					</div>
				</div>}
				<div style={{ height: 20, backgroundColor: '#F0F2F5' }}></div>
				{/* {权益配件关联}-实物类权益隐藏 */}
				{(scene === 2 && !usable) ? '' : <div className={goodsCategory === 1 ? styles.globalHide : ''}>
					{scene === 2 ? <>
						{/* 延保场景 */}
						<div className='batch-top'>
							<div className='left-box'>
								<span className='title'>权益配件关联</span>
								<span className='sub-title'>需选上所有车型的相关配件</span>
							</div>
							<Button className='batch-btn' type="primary" onClick={e => batchAddShow(true)}>关联</Button>
						</div>
						<div style={{ paddingTop: '20px' }}>

							<Table
								rowKey={'key'}
								dataSource={state.rows}
								scroll={{ y: 300 }}
								columns={tableColumns}
								className={styles['gray-table']}
								bordered
								size="small"
							>
							</Table>
						</div>
					</> : <>
						{/* 普通场景 */}
						<div className='batch-top'>
							<div className='left-box'>
								<span className='title'>权益配件关联</span>
								<span className='sub-title'>整车、三包、增程器无需额外配置，除此之外其他配件与权益相关在此进行关联，如无此配件请及时联系管理员进行维护。</span>
							</div>
							<Button className='batch-btn' type="primary" onClick={e => batchAddShow(true)} disabled={existPartValue !== 1}>关联</Button>
						</div>
						<div style={{ paddingTop: '20px' }}>
							<Form.Item label="是否存在配件" style={{ marginBottom: 0 }}>
								<Form.Item name="existPart" style={{ display: 'inline-block', marginBottom: 0 }}>
									<Radio.Group defaultValue={1} onChange={e => existPartChange(e)}>
										<Space align='center' size={20} >
											<Radio value={1}>是</Radio>
											<Radio value={2}>否</Radio>
										</Space>
									</Radio.Group>
								</Form.Item>
							</Form.Item>
							<Table
								rowKey={'key'}
								dataSource={state.rows}
								scroll={{ y: 300 }}
								columns={tableColumns}
								className={styles['gray-table']}
								bordered
								size="small"
							>
							</Table>
						</div>
					</>}
				</div>}
				<div style={{ height: 20, backgroundColor: '#F0F2F5' }}></div>

				<div className='bottom-back' style={backWidth}>
					<Button style={{ marginRight: '8px' }} onClick={() => {
						history.goBack()
					}}>
						返回
					</Button>
					{
						pageType === 'edit' ? <Button type='danger' style={{ marginRight: '8px' }} onClick={DeteAt}>删除</Button> : null
					}

					<Button style={{ marginRight: '8px' }} className='btn-box' type="primary" htmlType="submit" disabled={scene === 2 && !usable}>
						保存
					</Button>
				</div>
			</Form>
			<BatchAdd showBatchAdd={showBatchAdd} batchAddHandle={batchAddShow} batchAddSubmit={batchAddSubmit} state={state} scene={scene} />
		</div>
	)
}
Component.displayName = 'DatabaseForm'
export default Component
