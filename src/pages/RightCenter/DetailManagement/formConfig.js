export const detailFormConfig = {
    'name': {
        label: '权益明细名称',
        name: 'name',
    },
    'businessCode': {
        label: '业务编码',
        name: 'businessCode',
    },
    'businessCodeType': {
        label: '业务分类',
        name: 'businessCodeType',
    },
    'performingList': {
        label: '权益履约方',
        name: 'performingList',
    },
    'sortCode': {
        label: '权益分类',
        name: 'sortCode',
    },
    'typeCode': {
        label: '权益类别',
        name: 'typeCode',
    },
    'carAttr': {
        label: '车辆属性',
        name: 'carAttr',
    },
    'belong': {
        label: '权益归属',
        name: 'belong',
    },
    'statement': {
        label: '权益声明',
        name: 'statement',
    },
    'remark': {
        label: '权益备注',
        name: 'remark',
    },
    'refund': {
        label: '权益支持退款',
        name: 'refund',
    },
    'performance': {
        label: '是否三方履约',
        name: 'performance',
    },
    'isRepeatablePurchase': {
        label: '能否重复购买',
        name: 'isRepeatablePurchase'
    }
}

export const strategyConfig = {
    limitTimeFrame: {
        l: '权益限制年限',
        c: {
            limitTimeFrame: {
                value: 'limitTimeFrame'
            },
            limitYear: {
                value: 'limitYear'
            },
            limitMonth: {
                value: 'limitMonth'
            },
            limitDay: {
                value: 'limitDay'
            }
        }
    },
    timeRadio: {
        l: '权益生效时间',
        c: {
            timeRadio: {
                value: 'timeRadio'
            },
            relativeTime: {
                value: 'relativeTime'
            },
            fixedRelativeTime: {
                value: 'fixedRelativeTime',
                fixedBeginTime: 'fixedBeginTime',
                fixedEndTime: 'fixedEndTime'
            }
        }
    },
    limitLegendFrame: {
        l: '权益限制里程（基准）',
        c: {
            limitLegendFrame: {
                value: 'limitLegendFrame'
            },
            limitLegend: {
                value: 'limitLegend'
            },
            extenderMileage: {
                value: 'extenderMileage'
            }
        }
    },
    'effectMileageFrame': {
        l: '权益生效里程',
        c: {
            effectMileageFrame: {
                value: 'effectMileageFrame'
            },
            relativeBeginMileageStrategy: {
                value: 'relativeBeginMileageStrategy'
            },
            relativeBeginMileageStrategyName: {
                value: 'relativeBeginMileageStrategyName'
            },
            relativeEndMileageStrategy: {
                value: 'relativeEndMileageStrategy'
            },
            fixedBeginMileage: {
                value: 'fixedBeginMileage'
            },
            fixedEndMileageStrategy: {
                value: 'fixedEndMileageStrategy'
            }
        }
    },
    'frequencyRadio': {
        l: '享有权益频次',
        c: {
            frequencyRadio: {
                value: 'frequencyRadio'
            },
            frequency: {
                value: 'frequency'
            }
        }

    },
    'identity': {
        l: '车辆权益变更属性',
        c: {
            identity: {
                value: 'identity'
            },
        }
    },
    'carIdentity': {
        l: '享有车主身份',
        c: {
            carIdentity: {
                value: 'carIdentity'
            },
        }
    },
    'goodsType': {
        l: '权益所属',
        c: {
            goodsType: {
                value: 'goodsType'
            },
        }
    },
    'maintenanceLadderFrame': {
        l: '全保养阶梯策略',
        c: {
            maintenanceLadderFrame: {
                value: 'maintenanceLadderFrame'
            },
            maintenanceLadder: {
                value: 'maintenanceLadder'
            },
            maintenanceLadderLimitFrame: {
                value: 'maintenanceLadderLimitFrame'
            },
            maintenanceLadderDetail: {
                value: 'maintenanceLadderDetail'
            },
            maintenanceLadderDetailItem: {
                // ladderTimeFrame: {
                //     value: 'ladderTimeFrame',
                //     disabled: true
                // },
                ladderLimitYear: {
                    value: 'ladderLimitYear',
                    // disabled: true
                },
                ladderLimitMonth: {
                    value: 'ladderLimitMonth',
                    // disabled: true
                },
                // ladderLimitDay: {
                //     value: 'ladderLimitDay',
                //     disabled: true
                // },
                // ladderMileageFrame: {
                //     value: 'ladderMileageFrame',
                //     disabled: true
                // },
                ladderLimitMileage: {
                    value: 'ladderLimitMileage',
                    // disabled: true
                },
                ladderNo: {
                    value: 'ladderNo',
                }
            }
        }
    },
    'paintFrame': {
        l: '补漆面部位',
        c: {
            paintFrame: {
                value: 'paintFrame'
            },
            paintList: {
                value: 'paintList'
            }
        }
    },
    'trafficRadio': {
        l: '车联网流量',
        c: {
            trafficRadio: {
                value: 'trafficRadio'
            },
            traffic: {
                value: 'traffic'
            },
            trafficUnit: {
                value: 'trafficUnit'
            }
        }
    },
    'deductionFrame': {
        l: '权益金额抵扣',
        c: {
            deductionFrame: {
                value: 'deductionFrame'
            },
            payAmount: {
                value: 'payAmount'
            },
            deduction: {
                value: 'deduction'
            }
        }
    },
}

export const outsideStrategyConfig = {
    limitTimeFrame: {
        l: '权益限制年限',
        c: {
            limitTimeFrame: {
                value: 'outsideLimitTimeFrame'
            },
            limitYear: {
                value: 'outsideLimitYear'
            },
            limitMonth: {
                value: 'outsideLimitMonth'
            },
            limitDay: {
                value: 'outsideLimitDay'
            }
        }
    },
    timeRadio: {
        l: '权益生效时间',
        c: {
            timeRadio: {
                value: 'outsideTimeRadio'
            },
            relativeTime: {
                value: 'outsideRelativeTime'
            },
            fixedRelativeTime: {
                value: 'outsideFixedRelativeTime',
                fixedBeginTime: 'outsideFixedBeginTime',
                fixedEndTime: 'outsideFixedEndTime'
            }
        }
    },
    limitLegendFrame: {
        l: '权益限制里程（基准）',
        c: {
            limitLegendFrame: {
                value: 'outsideLimitLegendFrame'
            },
            limitLegend: {
                value: 'outsideLimitLegend'
            },
            extenderMileage: {
                value: 'outsideExtenderMileage'
            }
        }
    },
    'effectMileageFrame': {
        l: '权益生效里程',
        c: {
            effectMileageFrame: {
                value: 'outsideEffectMileageFrame'
            },
            relativeBeginMileageStrategy: {
                value: 'outsideRelativeBeginMileageStrategy'
            },
            relativeBeginMileageStrategyName: {
                value: 'outsideRelativeBeginMileageStrategyName'
            },
            relativeEndMileageStrategy: {
                value: 'outsideRelativeEndMileageStrategy'
            },
            fixedBeginMileage: {
                value: 'outsideFixedBeginMileage'
            },
            fixedEndMileageStrategy: {
                value: 'outsideFixedEndMileageStrategy'
            }
        }
    },
    'frequencyRadio': {
        l: '享有权益频次',
        c: {
            frequencyRadio: {
                value: 'outsideFrequencyRadio'
            },
            frequency: {
                value: 'outsideFrequency'
            }
        }
    },
    'identity': {
        l: '车辆权益变更属性',
        c: {
            identity: {
                value: 'outsideIdentity'
            },
        }
    },
    'carIdentity': {
        l: '享有车主身份',
        c: {
            carIdentity: {
                value: 'outsideCarIdentity'
            },
        }
    },
    'goodsType': {
        l: '权益所属',
        c: {
            goodsType: {
                value: 'outsideGoodsType'
            },
        }
    },
    'maintenanceLadderFrame': {
        l: '全保养阶梯策略',
        c: {
            maintenanceLadderFrame: {
                value: 'outsideMaintenanceLadderFrame'
            },
            maintenanceLadder: {
                value: 'outsideMaintenanceLadder'
            },
            maintenanceLadderLimitFrame: {
                value: 'outsideMaintenanceLadderLimitFrame'
            },
            maintenanceLadderDetail: {
                value: 'outsideMaintenanceLadderDetail'
            },
            maintenanceLadderDetailItem: {
                // ladderTimeFrame: {
                //     value: 'outsideLadderTimeFrame',
                //     disabled: true
                // },
                ladderLimitYear: {
                    value: 'outsideLadderLimitYear',
                    // disabled: true
                },
                ladderLimitMonth: {
                    value: 'outsideLadderLimitMonth',
                    // disabled: true
                },
                // ladderLimitDay: {
                //     value: 'outsideLadderLimitDay',
                //     disabled: true
                // },
                // ladderMileageFrame: {
                //     value: 'outsideLadderMileageFrame',
                //     disabled: true
                // },
                ladderLimitMileage: {
                    value: 'outsideLadderLimitMileage',
                    // disabled: true
                },
                ladderNo: {
                    value: 'ladderNo',
                }
            }
        }
    },
    'paintFrame': {
        l: '补漆面部位',
        c: {
            paintFrame: {
                value: 'outsidePaintFrame'
            },
            paintList: {
                value: 'outsidePaintList'
            }
        }
    },
    'trafficRadio': {
        l: '车联网流量',
        c: {
            trafficRadio: {
                value: 'outsideTrafficRadio'
            },
            traffic: {
                value: 'outsideTraffic'
            },
            trafficUnit: {
                value: 'outsideTrafficUnit'
            }
        }
    },
    'deductionFrame': {
        l: '权益金额抵扣',
        c: {
            deductionFrame: {
                value: 'outsideDeductionFrame'
            },
            payAmount: {
                value: 'outsidePayAmount'
            },
            deduction: {
                value: 'outsideDeduction'
            }
        }
    },
}