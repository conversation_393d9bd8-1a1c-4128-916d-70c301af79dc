import './index.less'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { Table,message, Tabs ,Divider,Popconfirm,Alert} from 'antd'
import moment from 'moment'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTooltip from '@/components/Public/PublicTooltip'
import { post,get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import SendMessage from './components/SendMessage'
import {roleJudgment} from '@/utils/authority'
import { Button, Modal, Tooltip } from 'antd';
const { TabPane } = Tabs;

const ClueList = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading, setLoading] = useState(false)
    const [entryList, setEntryList] = useState({})
    const [dataSource, changeDataSource] = useState([])
    const [receiverDataSource, changeReceiverDataSource] = useState({})
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)
    const [total, changeTotal] = useState(0)
    const [tableHeight,setTableHeight] = useState(0)
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('CarrierListTabsKey') || '1')
    const [Type,setType] = useState('Add')
    const [ID,setID] = useState('')
    const [defaultQuery, setDefaultQuery] = useState({
        // lastFollowTime:[moment().subtract(1,'months'),moment()],
        // createTime: [moment().subtract(1, 'months'), moment()],
    })
    const [isModalVisible, setIsModalVisible] = useState(false)
    const [exportLoading, setExportLoading] = useState(false)
    const [exportVisible,setExportVisible] = useState(false)
    const [confirmLoading,setConfirmLoading] = useState(false)
    const showModal = () => {
        setIsModalVisible(true);
    };
    const handleOk = () => {
        setIsModalVisible(false);
    };
    const handleCancel = () => {
        setIsModalVisible(false);
    };
    // 导出回执名单
    const ExportExcel = () => {
        setConfirmLoading(true)
        setExportLoading(true)

        get(allUrl.WeCom.exportReceipt + '/' + ID, {}).then(res => {
            if(res.success){
                window.location.href = res.resp[0].url
                message.success('导出成功！')
            }else{
                // message.error(res.msg)
            }
            
            setExportLoading(false)
            setExportVisible(false)
            setConfirmLoading(false)
        })
    };
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const onSearch = (values) => {
        // 处理更新时间格式
        if (values.groupCreateTime && values.groupCreateTime.length) {
            values.beginTime = moment(values.groupCreateTime[0]).format('YYYY-MM-DD HH:mm:ss')
            values.endTime = moment(values.groupCreateTime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete values.groupCreateTime
        }

        setDefaultQuery(values)
    }
    // 获取回执名单数据
    const CheckReceiptDetail = (ID) => {
        setLoading(true)
        setID(ID)

        get(allUrl.MessageManage.getReceiptInfo + '/' + ID).then(res => {
            if (res.success) {
                res.resp.map((item, index) => item.key = index + 1)
                changeReceiverDataSource(res.resp[0])
            } else {
                message.error(res.msg)
            }

            showModal(true)
            setLoading(false)
        })
    }
    const LookAt = (record) => {
        setID(record.id)
        setTabsKey('1')
        setType('EDIT')
    }
    const CheckDetail = (record) => {
        setID(record.id)
        setTabsKey('1')
        setType('Check')
    }
    const Del = (record) =>{
        post(allUrl.MessageManage.delMessage+'/'+ record.id).then(res=>{
            if(res.success){
                message.success(res.msg)
                getData()
            }else{
                // message.error(res.msg)
            }
        })
    }
    const RecallMessage = (record) =>{
        post(allUrl.MessageManage.withdrawMessage+'/'+ record.id).then(res=>{
            if(res.success){
                message.success(res.msg)
                getData()
            }else{
                // message.error(res.msg)
            }
        })
    }
    const calDiffHours=(StartDate, EndDate)=> {
        let temp = moment(EndDate).diff(moment(StartDate), 'hours')
        return temp
    }
    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'WECOM_MESSAGE_PUSH_SENDOUT_EDIT') && (record.status === 1 || record.status === 3 || record.status === 4) ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => LookAt(record)}>编辑</span>,
                        <Divider type="vertical" key={3}/>
                    ] : null
            }
            {
                roleJudgment(userInfo, 'WECOM_MESSAGE_PUSH_SENDOUT_EDIT') && (record.status === 2) ?
                    [
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => CheckDetail(record)}>查看</span>,
                        <Divider type="vertical" key={3}/>
                    ] : null
            }
            {
                roleJudgment(userInfo,'WECOM_MESSAGE_PUSH_SENDOUT_DEL') && (record.status === 1 || record.status === 3 || record.status === 4) ?
                    <Popconfirm title='确定删除此条消息吗？' onConfirm={() => Del(record)}>
                        <span key={2} style={{ cursor: 'pointer',color:'red' }}>删除</span>
                    </Popconfirm>
                    : null
            }
            {
                roleJudgment(userInfo,'WECOM_MESSAGE_PUSH_SENDOUT_WITHDRAW') && record.status === 2 ?
                calDiffHours(record.sendTime,new Date()) <=24?
                    <Popconfirm title='确定撤回此条消息吗？' onConfirm={() => RecallMessage(record)}>
                        <span key={2} style={{ cursor: 'pointer' }}>撤回消息</span>
                    </Popconfirm>
                    : null
                    :null
            }
        </div>
    }
    const getData = () => {
        setLoading(true)
        let query = { ...defaultQuery }
        let params = { pageNum: current, pageSize, ...query }
        if(tabsKey === '1'){
        }else if(tabsKey === '2'){
            get(allUrl.MessageManage.getMessageList, { ...params }).then(res => {
                if (res.success) {
                    // 处理回执情况字段，把两个字段包到一个字段中
                    res.resp[0].list.map((item,index) => {
                        return res.resp[0].list[index].isReceived = [item.alreadyRead, item.unRead, item.noReceipt]
                    })

                    res.resp[0].list.map((item, index) => item.key = index + 1)
                    changeDataSource(res.resp[0].list)
                    changeTotal(res.resp[0].total)
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }
    const tabsCallback = (key) => {
        console.log(key)
        setTabsKey(key)
        changeCurrent(1)
        changePageSize(10)
        sessionStorage.setItem('CarrierListTabsKey',key)
        setID('')
        setType('Add')
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }
    const getSaveSuccess = () =>{
        console.log('111111')
        setType('Add')
        setID('')
        setTabsKey('2')
    }
    useEffect(() => {
        get(allUrl.common.entryLists, { codes: 'qw-send-status' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setEntryList(Dt || {})
            } else {
                // message.error(res.message)
            }
        })
    }, [])
    useEffect(()=>{
        initPage()
    },[])
    useEffect(() => {
        if (userInfo) {
            getData()
        }
    }, [defaultQuery, userInfo, current, pageSize,tabsKey])

    const renderStatusName = (record) =>{
        const obj ={
            1:require('@/assets/img/message_draft.png'), //草稿
            2:require('@/assets/img/message_hasBeenSent.png'),//已发送
            3:require('@/assets/img/message_FailnSend.png'),//发送失败
            4:require('@/assets/img/message_Withdrawn.png'),//撤回
        }
        return <div>
            <img src={obj[record.status]} style={{width:'18px'}} />
            <span style={{marginLeft:'6px'}}>{record.statusName}</span>
            </div>
    }
    const renderIsReceipt = (isReceived, id)=>{
        return (
            isReceived ?
                isReceived[2] ?
                    <div>
                        <span>{ isReceived[2] }</span>
                    </div> :
                    <div className='isReceivedWrapper' onClick={() => CheckReceiptDetail(id)}>
                        <Tooltip title='点击查看更多信息'>
                            <span>已读({ isReceived?isReceived[0]:'' })</span>&nbsp;&nbsp;
                            <span style={{color:'#1890FF'}}>未读({ isReceived?isReceived[1]:'' })</span>
                        </Tooltip>
                    </div> : ''
        )
    }
    
    const InforData = {
        rowKey: record => record.id,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content'},
        columns: [
            { title: '状态', dataIndex: 'statusName', width: 100, render:(text,record)=>renderStatusName(record) },
            { title: '消息', dataIndex: 'title', width: 100 },
            { title: '应用', dataIndex: 'agentIdName', width: 100 },
            { title: '回执情况', dataIndex: 'isReceived', width: 120, render: (isReceived, record)=>renderIsReceipt(isReceived, record.id) },
            { title: '创建时间', dataIndex: 'createTime', width: 160, render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss'):'' },
            { title: '创建人', dataIndex: 'createName', width: 100 },
            { title: '创建人所属组织', dataIndex: 'organizationName', width: 140 },
            { title: '更新时间', dataIndex: 'updateTime', width: 160, render:text=>text?moment(text).format('YYYY-MM-DD HH:mm:ss'):'' },
            { title: '最后更新人', dataIndex: 'updateName', width: 90 },
            { title: '操作', width: 150, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: null,
    };

    let searchList2 = [
        { label: '消息', name: 'title', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '状态', name: 'status', type: 'Select', placeholder: '请选择', colSpan: 6, data: entryList['qw-send-status'] || [] },
        { label: '创建人', name: 'createName', type: 'Input', placeholder: '请输入', colSpan: 6 },
        { label: '更新时间', name: 'groupCreateTime', type: 'RangePicker', colSpan: 6, showTime: true },
    ]

    return (
        <div className='CarrierList'>
            <Tabs onChange={tabsCallback} activeKey={tabsKey} defaultActiveKey={tabsKey} >
                <TabPane tab="发消息" key="1">
                    <div className='tableData'>
                        <SendMessage Type={Type} ID={ID} setID={setID} setType={setType} getSaveSuccess={getSaveSuccess} />
                    </div>
                </TabPane>
                <TabPane tab="已发送" key="2">
                    <div className='tableData'>
                        <Alert message="仅发送时间在24小时内的消息支持撤回。" type="error" showIcon closable style={{width:500,marginLeft:'24px',marginTop:'24px'}} />
                        <PublicTableQuery tabsKey={tabsKey} onSearch={onSearch} searchList={searchList2} defaultQuery={defaultQuery} />
                        <Table {...InforData} />
                        <Modal title="回执名单" visible={isModalVisible} onOk={ExportExcel} onCancel={handleCancel} okText='导出记录' confirmLoading={confirmLoading}>
                            <Tabs>
                                {/* tabsKey替换为已读名单.length */}
                                <TabPane tab={`已读人数（${receiverDataSource.alreadyReadUserNames ? receiverDataSource.alreadyReadUserNames.length : 0}）`} key="1">
                                    {
                                        receiverDataSource.alreadyReadUserNames ?
                                        <div className='already-read-wrapper'>
                                            {
                                            receiverDataSource.alreadyReadUserNames.length?
                                                receiverDataSource.alreadyReadUserNames.length > 20?
                                                    <div className='already-read-list'>
                                                        {
                                                            receiverDataSource.alreadyReadUserNames.map((item, index) => {
                                                                return (
                                                                    <div key={index+1}>{ item }</div>
                                                                )
                                                            })
                                                        }
                                                        <Divider/>
                                                        <div className='more-info'>更多已读/未读名单可导出查看</div>
                                                    </div>:<div className='already-read-list'>
                                                        {
                                                            receiverDataSource.alreadyReadUserNames.map((item, index) => {
                                                                return (
                                                                    <div key={index+1}>{ item }</div>
                                                                )
                                                            })
                                                        }
                                                    </div> :null
                                            }
                                        </div>:null
                                    }
                                </TabPane>
                                {/* tabsKey替换为未读名单.length */}
                                <TabPane tab={`未读人数（${receiverDataSource.unReadUserNames ? receiverDataSource.unReadUserNames.length : 0}）`} key="2">
                                {
                                    receiverDataSource.unReadUserNames ?
                                    <div className='already-read-wrapper'>
                                    {
                                        receiverDataSource.unReadUserNames.length ?
                                            receiverDataSource.unReadUserNames.length > 20?
                                            <div className='already-read-list'>
                                                {
                                                    receiverDataSource.unReadUserNames.slice(0,20).map((item, index) => {
                                                        return (
                                                            <div className='already-read-item' key={index+1}>{ item }</div>
                                                        )
                                                    })
                                                }
                                                <Divider/>
                                                <div className='more-info'>更多已读/未读名单可导出查看</div>
                                                </div>:<div className='already-read-list'>
                                                    {
                                                        receiverDataSource.unReadUserNames.map((item, index) => {
                                                            return (
                                                                <div className='already-read-item' key={index+1}>{ item }</div>
                                                            )
                                                        })
                                                    }
                                                </div>:null
                                    }
                                    </div>:null
                                }
                                </TabPane>
                            </Tabs>
                        </Modal>
                    </div>
                </TabPane>
            </Tabs>
        </div>
    )
}
export default ClueList
