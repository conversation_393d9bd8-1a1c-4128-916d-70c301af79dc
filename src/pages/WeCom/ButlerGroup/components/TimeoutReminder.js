import React,{useEffect, useState} from 'react'
import { Modal,Row,Col ,Select ,Checkbox ,Popconfirm, message ,Spin} from 'antd'
import { CloseOutlined} from '@ant-design/icons';
import SendingRange from './SendingRange'
import _ from 'lodash'
import {nanoid} from 'nanoid'
import {get,post} from '@/utils/request'
import allUrl from '@/utils/url';
import './TimeoutReminder.less'

const {Option} = Select
const TimeoutReminder = props =>{
    const {visible,onCancel} = props
    const [Type,setType] = useState('')
    const [tabsType,setTabsType] = useState([])
    const [sendingRangeVisible,setSendingRangeVisible] = useState(false)
    const [loading,setLoading] = useState(false)
    const [targetList,setTargetList] = useState([])
    const [remindPeople,setRemindPeople] = useState([])
    const [timeOption,setTimeOption] = useState([
        {name:'5',value:'5'},
        {name:'10',value:'10'},
        {name:'15',value:'15'},
        {name:'20',value:'20'},
        {name:'30',value:'30'},
        {name:'60',value:'60'},
    ])
    const [ruleList,setRuleList] = useState([
        {timeout:null,groupOwnerStatus:true,qwUserIds:[],uid:nanoid(),noticedPositionCodeList:[]}
    ])

    const handleOk = () =>{

        if(!targetList.length) return message.error('请选择规则适用范围！')

        for(let i=0 ;i<ruleList.length;i++){
            if(!ruleList[i].timeout || (!ruleList[i].groupOwnerStatus && !ruleList[i].qwUserIds.length)){
                message.error('请录入完整的提醒规则！')
                return
            }
        }

        ruleList.forEach(item=>{
            item.groupOwnerStatus = item.groupOwnerStatus?'1':'2'
        })

        let ruleTimes = [...new Set(ruleList.map(item=>item.timeout))]
        if(ruleTimes.length!==ruleList.length){
            return message.error('提醒规则时间不允许重复！')
        }

        const orgIds = targetList.map(item=>String(item.orgId))
        const data = {
            records:ruleList,
            orgIds
        }
        console.log('保存的数据为：',data)
        setLoading(true)
        post(allUrl.WeCom.qwOvertimeNoticeSave,{...data}).then(res=>{
            if(res.success){
                message.success('保存成功！')
                onCancel()
            }
            setLoading(false)
        })

    }

    const handleModal = () =>{
        setTabsType(['org'])
        setSendingRangeVisible(true)
    }

    const SendingRangeCB = (data)=>{
        setTargetList(data)
    }

    const targetListDel = (row) =>{
        let newTargetList = _.cloneDeep({obj:targetList}).obj
        newTargetList = newTargetList.filter(item=>item.orgId!==row.orgId)
        setTargetList(newTargetList)
    }

    const addRule = () =>{
        let temp = [...ruleList]
        temp.push({
            timeout:null,groupOwnerStatus:true,qwUserIds:[],uid:nanoid(),noticedPositionCodeList: []
        })
        setRuleList(temp)
    }
    const delRule = (row) =>{
        let temp = [...ruleList]
        temp = temp.filter(item=>item.uid !== row.uid)
        setRuleList(temp)
    }

    const fieldChange = (row,value,field) =>{
        let arr = [...ruleList]
        let temp = arr.filter(item=>item.uid === row.uid)[0]
        temp[field] = value
        console.log(arr)
        setRuleList(arr)
    }

    useEffect(()=>{
        get(allUrl.common.entryLists,{codes:'qw_user_id'}).then(res=>{
            console.log(res)
            if(res.success){
                setRemindPeople(res.resp[0].qw_user_id)
                // res.resp[0].forEach(item=>{
                //     item.name = item.entryMeaning
                //     item.value = item.entryValue
                // })
            }
        })
    },[])

    useEffect(()=>{
        setLoading(true)
        post(allUrl.WeCom.qwOvertimeNoticePage).then(res=>{
            setLoading(false)
            if(res.success){
                let Dt = res.resp[0]
                const ruleList = Dt.records.map(item=>{
                    return {
                        groupOwnerStatus:item.groupOwnerStatus == 1 ?true:false,
                        timeout:String(item.timeout),
                        qwUserIds:item.qwUserList.map(item1=>item1.qwUserId),
                        uid:nanoid(),
                        noticedPositionCodeList: item.noticedPositionCodeList.map(item2=>item2.noticedPositionCode),
                    }
                })
                const targetList = Dt.organizationList
                if(ruleList.length)setRuleList(ruleList)
                setTargetList(targetList)
            }
        })
    },[])

    return <Modal title='超时提醒规则设置' visible={visible} onOk={handleOk} onCancel={onCancel} confirmLoading={loading} maskClosable={false} width={1200} wrapClassName='TimeoutReminder'>
        <div className='TimeoutReminder-wrap'>
            <Spin spinning={loading}>
                <div className='title'>
                    <span>1、规则适用范围：</span>
                    <a onClick={handleModal}>选择适用范围</a>
                </div>
                <div className='select'>
                {
                    targetList.length ?
                    <Row>
                        <Col span={24} style={{display:'flex'}}>
                            <span style={{marginTop:'6px',marginLeft:'25px'}}>已选择的范围：</span><Row style={{lineHeight:'34px',marginBottom:'20px',flex:1,maxHeight:'450px',overflow: 'hidden',overflowY: 'auto'}} gutter={[16,16]}>
                                {
                                    targetList.map((item,index)=>{
                                        return <Col key={index} span={3}>
                                                <div style={{background:'#F4FAFF',padding:'2px 22px 2px 10px',overflow:'hidden',textOverflow:'ellipsis',whiteSpace:'nowrap'}} title={item.orgName}>
                                                    {item.orgName}
                                                    <span style={{position:'absolute',right:'14px',color:'#b7b7b7',cursor:'pointer'}} onClick={()=>targetListDel(item)}>
                                                        <CloseOutlined />
                                                    </span>
                                                </div>
                                        </Col>
                                    })
                                }
                                {/* <span><a style={{verticalAlign:'sub',marginLeft:'10px'}}>修改</a></span> */}
                            </Row>
                        </Col>
                    </Row>:null
                }
                </div>
                <div className='title'>
                    <span>2、提醒规则：</span>
                </div>
                <div className='ruleList'>
                    {
                        ruleList.map((item,index)=>{
                            return <div className='ruleList-item' key={item.uid}>
                                超过
                                <Select allowClear style={{margin:'0 10px',width:'60px'}} value={item.timeout} onChange={value=>fieldChange(item,value,'timeout')}>
                                    {
                                        timeOption.map((item,index)=><Option key={index} value={item.value}>{item.name}</Option>)
                                    }
                                </Select>
                                分钟未回复客户的群聊，提醒
                                <Checkbox style={{marginLeft:10}} defaultChecked={true} checked={item.groupOwnerStatus}  onChange={e=>fieldChange(item,e.target.checked,'groupOwnerStatus')}>群主和群内：</Checkbox>
                                <Select mode='multiple' style={{width:'200px', marginRight: 10}} allowClear placeholder='请选择岗位' value={item.noticedPositionCodeList} onChange={e=>fieldChange(item,e,'noticedPositionCodeList')}>
                                    <Option value={'POST_DELIVERY_SPECIALIST'}>交付专员A</Option>
                                    <Option value={'POST_DSB'}>交付专员B</Option>
                                    <Option value={'POST_DELIVERY_MANAGER'}>交付经理</Option>
                                    <Option value={'POST_USERCARE_MANAGER'}>用户关爱专员</Option>
                                    <Option value={'POST_MANAGER'}>用户关爱经理</Option>
                                    <Option value={'POST_WM'}>车间主管</Option>
                                    <Option value={'POST_TM'}>技术专家</Option>
                                    <Option value={'POST_SERVICE_CONSULTANT'}>服务顾问</Option>
                                </Select>
                                {/* <a>添加群聊人员</a> */}
                                提醒人员：
                                <Select mode='multiple' style={{width:'30%'}} allowClear placeholder='请选择' value={item.qwUserIds} onChange={e=>fieldChange(item,e,'qwUserIds')}>
                                    {
                                        remindPeople.map((item,index)=><Option key={index} value={item.entryValue}>{item.entryMeaning}</Option>)
                                    }
                                </Select>
                                {
                                    index!==0?<Popconfirm title="确定删除这条规则吗？" onConfirm={()=>delRule(item)}>
                                        <CloseOutlined style={{marginLeft: '13px',cursor: 'pointer'}} />
                                    </Popconfirm>:null
                                }
                            </div>
                        })
                    }
                </div>
                {
                    ruleList.length<5?
                    <div className='add-rule'><a onClick={addRule}>添加更多规则</a></div>:null
                }
            </Spin>
        </div>
        {
            sendingRangeVisible &&
            <SendingRange tabsType={tabsType}  Type={Type} targetList={targetList} visible={sendingRangeVisible} onCancel={() => setSendingRangeVisible(false)} cb={SendingRangeCB} />
        }
    </Modal>
}
export default TimeoutReminder