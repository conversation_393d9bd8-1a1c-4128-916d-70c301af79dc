import React, { useEffect, useState, useRef } from 'react'
import _ from 'lodash'
import { postForm } from '@/utils/request'
import allUrl from '@/utils/url'
import { useDispatch } from 'react-redux'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import baseURL from '@/baseURL'
import ResultModal from './ResultModal'
import moment from 'moment'
import './style.less'
import { Button, Upload, message } from 'antd'
import { UploadOutlined } from '@ant-design/icons';
import styles from '../antd.module.less'

const statusMAP = {
    0: '未处理',
    1: '成功',
    2: '校验错误',
    3: '处理异常'
}

const ExcelImport = (props) => {
    const tableRef = useRef()
    const [defaultQuery, setDefaultQuery] = useState({})
    const [importLoading, setImportLoading] = useState(false)
    const [showResult, setShowResult] = useState(false)
    const [resultData, setResultData] = useState('')

    const download = () => {
        let url = 'https://scrm-equity-prod-oss.oss-cn-shanghai.aliyuncs.com/equity-file/%E6%9D%83%E7%9B%8A%E4%B8%AD%E5%BF%83_%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E6%9F%A5%E8%AF%A2%E6%A8%A1%E6%9D%BF.xlsx'
        window.open(url)
    }
    const columns = [{
        title: '导入批次',
        dataIndex: 'importDateBatch',
        key: 'importDateBatch',
        render: (text) => text
    }, {
        title: '车辆VIN',
        dataIndex: 'vin',
        key: 'vin',
        render: (text) => text
    }, {
        title: '导入时间',
        dataIndex: 'createTime',
        key: 'createTime',
        render: (text) => {
            return text ? text : '-'
        }
    }, {
        title: '导入结果',
        dataIndex: 'syncStatus',
        key: 'syncStatus',
        render: (text) => {
            if (Number(text) > 0) {
                if (text == 1) {
                    return <span><span className='success-dot'></span>{statusMAP[text]}</span>
                } else {
                    return <span><span className='fail-dot'></span>{statusMAP[text]}</span>
                }
            } else {
                return '-'
            }
        }
    }, {
        title: '原因',
        dataIndex: 'errorMsg',
        key: 'errorMsg',
        render: (text) => text || '-'
    }, {
        title: '权益包编码',
        dataIndex: 'equityPackageNo',
        key: 'equityPackageNo',
        render: (text) => text
    }, {
        title: '支付时间',
        dataIndex: 'payTime',
        key: 'payTime',
        render: (text) => text ? text : '-'
    }, {
        title: '付费权益生效时间',
        dataIndex: 'effectTime',
        key: 'effectTime',
        render: (text) => text ? text : '-'
    }, {
        title: '权益结束时间',
        dataIndex: 'endTime',
        key: 'endTime',
        render: (text) => text ? text : '-'
    }, {
        title: '操作人',
        dataIndex: 'createBy',
        key: 'createBy',
    }
    ]
    let searchList = [
        { label: '车辆VIN', name: 'vin', type: 'Input', placeholder: '请输入车辆VIN', colSpan: 6 },
        { label: '导入时间', name: 'importDate', type: 'DatePicker', placeholder: '请选择', colSpan: 6, help: <span className='help-text'>可根据导入时间，查看以天为维度的数据</span> },
        {
            label: '导入结果', name: 'syncStatus', type: 'Select', placeholder: '请选择', colSpan: 6, data: [
                { name: '未处理', value: 0 },
                { name: '成功', value: 1 },
                { name: '校验错误', value: 2 },
                { name: '处理异常', value: 3 },
            ]
        },
        { label: '权益包编码', name: 'packNo', type: 'Input', placeholder: '请输入权益包编码', colSpan: 6 },
    ]
    const onSearch = (values) => {
        const obj = {
            ...values,
        }
        if (values && values.importDate) {
            obj.importDate = moment(values.importDate).format('YYYYMMDD')
        }
        setDefaultQuery(obj)
    }
    const handleCancel = () => {
        setShowResult(false)
    }

    // 文件上传处理
    const beforeUpload = (file, fileList) => {
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
            message.warn('附件大小限制10M内')
        }
        return isLt10M
    }
    return (<div>
        <div className='import-container'>
            <div className='import-flow'>
                <div className='title'>数据导入流程</div>
                <div className='number'>
                    <div className='number-item'>
                        <div className='number-common one'>1</div>
                        <div className='number-text'>下载数据导入模版</div>
                        <div className='number-string'></div>
                    </div>
                    <div className='number-item'>
                        <div className='number-common two'>2</div>
                        <div className='number-text'>上传数据模版</div>
                        <div className='number-string'></div>
                    </div>
                    <div className='number-item'>
                        <div className='number-common three'>3</div>
                        <div className='number-text'>数据校验</div>
                        <div className='number-string'></div>
                    </div>
                    <div className='number-item'>
                        <div className='number-common four'>4</div>
                        <div className='number-text'>上传审批材料</div>
                        <div className='number-string'></div>
                    </div>
                    <div className='number-item'>
                        <div className='number-common five'>5</div>
                        <div className='number-text'>数据导入完成</div>
                    </div>
                </div>
            </div>
            <div className='import-excel'>
                <div className='top'>
                    <span className='name'>下载数据导入模版：</span>
                    <a className='download' onClick={download}><img style={{ width: '14px', height: '14px', marginRight: '4px' }} src={require('../../../assets/img/excel-download.png')}></img>模版文件下载</a>
                    <span className='red-text'>一个文件最多同时支持2000个VIN的批量导入，每次都需要按照模版下载，否则无法正常绑定</span>
                </div>
                <div className='bottom'>
                    <span className='name'>上传数据模版：</span>

                    <Upload
                        style={{ display: 'inline-block' }}
                        extension={['xls', 'xlsx']}
                        showUploadList={false}
                        size={10}
                        action={() => {
                            return new Promise(async (resolve) => {
                                resolve(baseURL.Host + allUrl.ExcelImport.uploadAppendix)
                            })
                        }}
                        beforeUpload={beforeUpload}
                        customRequest={(e) => {
                            const { action, file } = e
                            setImportLoading(true)
                            let data = new FormData()
                            data.append('file', file)
                            postForm(action, data, {
                                headers: {
                                    'Content-Type': "multipart/form-data"
                                }
                            }).then(res => {
                                if (res.success) {
                                    setImportLoading(false)
                                    setShowResult(true)
                                    setResultData(res.resp[0])
                                }
                                setImportLoading(false)
                            })
                        }}
                    >

                        <Button icon={<UploadOutlined />} type="primary" loading={importLoading} disabled={importLoading} className='
                        upload-btn'>点击上传数据</Button>
                    </Upload>
                    <span className='red-text'>加密文件无法识别</span>
                </div>
            </div>
            <div className='import-search'>
                <PublicTableQuery isCatch={false} isFormDown={false} onSearch={onSearch} searchList={searchList} />
            </div >
            <div className='import-list'>
                <div className='list-top'>
                    <span className='name'>数据导入列表</span>
                    <span className='red-text'>全部导入的数据，成功和失败的列表</span>
                </div>
                <div>
                    <PublicTable isCatch={false} ref={tableRef} type={2} rowSelection={false} defaultQuery={defaultQuery} url={allUrl.ExcelImport.queryExcelTable} columns={columns} className={styles['blue-table']} />
                </div>
            </div>
        </div>
        <ResultModal showResult={showResult} resultData={resultData} defaultQuery={defaultQuery} handleCancel={handleCancel} onSearch={onSearch} />
    </div>)
}
export default ExcelImport