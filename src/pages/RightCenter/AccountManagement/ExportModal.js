import React, { useState, useReducer } from 'react'
import { Modal, Checkbox, Row, Col } from 'antd'
import './style.less'
import { accountInfoOptions, accountRightOptions, accountTypeOptions, accountOrderOptions } from '@/utils/checkOptions'

const allCheckedInfoOpt = ['accountNo', 'carBuyerPhone', 'carBuyerName', 'carType', 'vin', 'baseConfig', 'transferType', 'carOwnerName', 'carOwnerPhone']
const allCheckedRightOpt = ['packNo', 'packName', 'attribute', 'packSalesType', 'detailNo', 'name', 'effectTime']
const allCheckedTypeOpt = ['baseNo', 'payNo', 'giveNo']
const allCheckedOrderOpt = ['bookOrderNo', 'formalOrderNo', 'hwOrderNo', 'saleOrderNo', 'deliverOrderNo']

const ExportModal = (props) => {
    const [checkedInfoList, setInfoCheckedList] = useState(allCheckedInfoOpt)
    const [checkedOrderList, setCheckedOrderList] = useState(allCheckedOrderOpt)
    const [checkedTypeList, setCheckedTypeList] = useState(allCheckedTypeOpt)  //订单信息三个权益包
    const [checkedRightList, setCheckedRightList] = useState(allCheckedRightOpt)

    const [showOrder, setShowOrder] = useState(true)
    const [showRight, setShowRight] = useState(true)
    const [showDetail, setShowDetail] = useState(true)

    const [exportDisabled, setExportDisabled] = useState(false)
    const [checkAllInfo, setCheckAllInfo] = useState(true)
    const [indeterminateInfo, setIndeterminateInfo] = useState(false)
    const [checkAllOrder, setCheckAllOrder] = useState(true)
    const [indeterminateOrder, setIndeterminateOrder] = useState(false)
    const [checkAllRight, setCheckAllRight] = useState(true)
    const [indeterminateRight, setIndeterminateRight] = useState(false)

    //账户信息全选
    const onCheckAllInfoChange = (e) => {
        const { checked } = e.target
        setInfoCheckedList(checked ? allCheckedInfoOpt : []);
        setIndeterminateInfo(false);
        setCheckAllInfo(checked);
        setShowOrder(checked);
        setShowRight(checked);
        setShowDetail(checked);
        setExportDisabled(!checked);
    }

    //订单信息全选
    const onCheckAllOrderChange = (e) => {
        const { checked } = e.target
        setCheckedOrderList(checked ? allCheckedOrderOpt : []);
        setIndeterminateOrder(false);
        setCheckAllOrder(checked);
    }

    //权益信息全选
    const onCheckAllRightChange = (e) => {
        const { checked } = e.target
        setCheckedRightList(checked ? allCheckedRightOpt : []);
        setCheckedTypeList(checked ? allCheckedTypeOpt : []);
        setIndeterminateRight(false);
        setCheckAllRight(checked);
    }

    //单选
    const onInfoChange = (type, list) => {
        switch (type) {
            case 'info':
                setInfoCheckedList(list);
                setIndeterminateInfo(!!list.length && list.length < allCheckedInfoOpt.length);
                setCheckAllInfo(list.length === allCheckedInfoOpt.length);
                setShowOrder(!!list.length);
                setShowRight(!!list.length);
                setShowDetail(!!list.length);
                setExportDisabled(!list.length);
                break;
            case 'order':
                setCheckedOrderList(list);
                setIndeterminateOrder(!!list.length && list.length < allCheckedOrderOpt.length);
                setCheckAllOrder(list.length === allCheckedOrderOpt.length);
                break;
            case 'type':
                setCheckedTypeList(list);
                if (list.length === 0) {
                    setIndeterminateRight(false)
                } else if (list.length === allCheckedTypeOpt.length) {
                    if (checkedRightList.length === allCheckedRightOpt.length) {
                        setIndeterminateRight(false)
                    } else {
                        setIndeterminateRight(true)
                    }
                } else {
                    setIndeterminateRight(true)
                }
                setCheckAllRight(list.length === allCheckedTypeOpt.length);
                setShowDetail(!!list.length);
                break;
            case 'right':
                setCheckedRightList(list);
                if (list.length === allCheckedRightOpt.length && checkedTypeList.length === allCheckedTypeOpt.length) {
                    setIndeterminateRight(false)
                } else {
                    setIndeterminateRight(true)
                }
                setCheckAllRight(list.length === allCheckedRightOpt.length);
                break;
            default:
                break;
        }

    }

    const onOk = () => {
        let arr = checkedInfoList.concat(checkedOrderList).concat(checkedTypeList).concat(checkedRightList)
        props.submitHandle(arr)
        onCancel()
    }
    const onsetOpt = () => {
        setInfoCheckedList(allCheckedInfoOpt)
        setCheckedOrderList(allCheckedOrderOpt)
        setCheckedTypeList(allCheckedTypeOpt)
        setCheckedRightList(allCheckedRightOpt)

        setIndeterminateInfo(false)
        setCheckAllInfo(true)
        setIndeterminateOrder(false)
        setCheckAllOrder(true)
        setIndeterminateRight(false)
        setCheckAllRight(true)

        setShowOrder(true)
        setShowRight(true)
        setShowDetail(true)
    }
    const onCancel = () => {
        props.handleCancel()
        onsetOpt()
    }
    return (
        <Modal
            width={900}
            visible={props.showExport}
            onCancel={onCancel}
            title={<div><span>权益账户导出</span></div>}
            onOk={onOk}
            okText="导出"
            okButtonProps={exportDisabled ? { disabled: true } : { disabled: false }}
        >
            <div className='export-content'>
                <div className='export-top'><span style={{ display: 'inline-block', marginRight: '20px' }}>账户信息</span><Checkbox indeterminate={indeterminateInfo} checked={checkAllInfo} onChange={onCheckAllInfoChange}>全选</Checkbox></div>
                <div className='export-bottom'>
                    <Checkbox.Group style={{ width: '100%' }} onChange={(list) => onInfoChange('info', list)} value={checkedInfoList}>
                        <Row >
                            {accountInfoOptions.map(item => {
                                return (
                                    <Col span={6} style={{ marginBottom: '20px' }} key={item.value}>
                                        <Checkbox value={item.value}>{item.label}</Checkbox>
                                    </Col>
                                )
                            })}
                        </Row>
                    </Checkbox.Group>
                </div>
            </div>
            {showOrder && <div className='export-content'>
                <div className='export-top'><span style={{ display: 'inline-block', marginRight: '20px' }}>订单信息</span><Checkbox indeterminate={indeterminateOrder} checked={checkAllOrder} onChange={onCheckAllOrderChange}>全选</Checkbox></div>
                <div className='export-bottom'>
                    <Checkbox.Group style={{ width: '100%' }} onChange={(list) => onInfoChange('order', list)} value={checkedOrderList}>
                        <Row >
                            {accountOrderOptions.map(item => {
                                return (
                                    <Col span={6} style={{ marginBottom: '20px' }} key={item.value}>
                                        <Checkbox value={item.value}>{item.label}</Checkbox>
                                    </Col>
                                )
                            })}
                        </Row>
                    </Checkbox.Group>
                </div>
            </div>}
            {showRight && <div className='export-content'>
                <div className='export-top'><span style={{ display: 'inline-block', marginRight: '20px' }}>权益信息</span><Checkbox indeterminate={indeterminateRight} checked={checkAllRight} onChange={onCheckAllRightChange}>全选</Checkbox></div>
                <div className='export-add'>
                    <Checkbox.Group style={{ width: '100%' }} onChange={(list) => onInfoChange('type', list)} value={checkedTypeList}>
                        <Row >
                            {accountTypeOptions.map(item => {
                                return (
                                    <Col span={6} style={{ marginBottom: '20px' }} key={item.value}>
                                        <Checkbox value={item.value}>{item.label}</Checkbox>
                                    </Col>
                                )
                            })}
                        </Row>
                    </Checkbox.Group>
                </div>
                {showDetail && <div className='export-last'>
                    <Checkbox.Group style={{ width: '100%' }} onChange={(list) => onInfoChange('right', list)} value={checkedRightList}>
                        <Row >
                            {accountRightOptions.map(item => {
                                return (
                                    <Col span={6} style={{ marginBottom: '20px' }} key={item.value}>
                                        <Checkbox value={item.value}>{item.label}</Checkbox>
                                    </Col>
                                )
                            })}
                        </Row>
                    </Checkbox.Group>
                </div>}
            </div>}
        </Modal>
    )
}
export default ExportModal