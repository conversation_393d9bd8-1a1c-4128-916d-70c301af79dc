import React,{useState} from 'react'
import {Modal} from 'antd'
import PreviewImg from './previewImg'
import './viewAttachments.less'

export default (props) =>{
    const {visible,data,onCancel} = props
    const [previewVisible,setPreviewVisible] = useState(false)
    const [previewFile,setPreviewFile] = useState({})
    const getFileExtendingName =(filename)=> {
        // 文件扩展名匹配正则
        var reg = /\.[^\.]+$/;
        var matches = reg.exec(filename);
        if (matches) {
          return matches[0];
        }
        return '';
    }

    const onPreview = (file) =>{
        let imgSuffix = ['.png','.gif','.jpeg','.jpg','.svg','.bmp']
            let extend = getFileExtendingName(file.attachmentName)
            if(imgSuffix.indexOf(extend)>-1){
                setPreviewFile(file)
                setPreviewVisible(true)
            }else{
                window.open(file.attachmentUrl)
            }
    }
    return <Modal title={`${data.institutionName}证明文件`} visible={visible} footer={null} onCancel={onCancel} wrapClassName='viewAttachments'>
        {
            data && data.attachments && data.attachments.length ?data.attachments.map((item,index)=>{
                return <div key={index} className='viewAttachments_item'>
                    <span><a onClick={()=>onPreview(item)}>{index+1}、{item.attachmentName}</a></span>
                    </div>
            }):null
        }
        {
            previewVisible && 
            <PreviewImg data={previewFile} visible={previewVisible} onCancel={()=>setPreviewVisible(false)}/>
        }
    </Modal>
}