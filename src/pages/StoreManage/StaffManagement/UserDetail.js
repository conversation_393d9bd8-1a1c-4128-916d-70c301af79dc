import React, { useEffect, useState } from 'react'
import { Modal, Form, Row, Col, Spin, Button, message,Table  } from 'antd'
import moment from 'moment'
import history from '@/utils/history'
import { get } from '@/utils/request'
import { DecryptByAES } from '@/components/Public/Decrypt'
import PersonnelTrainingInfo  from './Modal/PersonnelTrainingInfo'
import allUrl from '@/utils/url'
import './UserDetail.less'
import {utilsDict} from '@/utils/utilsDict'
import {
    MOTOR_MECHANIC_CODE,
    TM_CODE,
    QYJSJL_CODE,
    SHOP_MANAGER_CODE,
    DELIVERY_MANAGER_CODE,
    SERVICESTATION_MASTER_CODE,
    USERCARE_MANAGER_CODE,
    JRJL,
    POST_MM_CODE,
    POST_BAM,
    FJSZJ,
    XMTZG
  } from "../../AuthorityManagement/UserManagement/constant";
const status_map = {
    0: '未启用',
    1: '启用'
}
const UserDetail = (props) => {
    const { rowData } = props
    const [data, setData] = useState(JSON.parse(DecryptByAES(props.match.params.data)))
    const [currentUrl, setCurrentUrl ] = useState('')
    const [previewVisible, setPreviewVisible] = useState(false)
    const [interviewResult,setInterviewResult]=useState()
    const [dataSource,setDataSource] = useState([])
    const [accountSource, setAccountSource] = useState([])
    const [loading, setLoading] = useState(false)
    const [detail, setDetail] = useState({})
    const [showinterview, setShowinterview] = useState(false) // 是否显示面试信息

    const getFileExtendingName = (filename)=> {
        // 文件扩展名匹配正则
        var reg = /\.[^\.]+$/;
        var matches = reg.exec(filename);
        if (matches) {
          return matches[0];
        }
        return '';
    }
    const isImage = (url) => {
        let imgSuffix = ['.png','.gif','.jpeg','.jpg','.svg','.bmp']
        const extend = getFileExtendingName(url)
        console.log('e',imgSuffix.indexOf(extend) > -1)
        return imgSuffix.indexOf(extend) > -1
    }
     // 表格
     const InforData = {
        rowKey: record => record.id,
        bordered: true,
        pagination:false,
        dataSource,
        columns: [
            { title: '面试时间', dataIndex: 'interviewTime', width: 100 ,render:text=> text?moment(text).format('YYYY-MM-DD'):''},
            { title: '面试官账号ID', dataIndex: 'interviewerId', width: 100 },
            { title: '面试官', dataIndex: 'interviewer', width: 100 },
            { title: '面试官所属组织', dataIndex: 'interviewerOrgName', width: 150 },
            { title: '面试成绩', dataIndex: 'score', width: 100 },
        ],
    };
    const accountData = {
        rowKey: record => record.id,
        bordered: true,
        pagination:false,
        dataSource: accountSource,
        columns: [
            { title: '系统名称', dataIndex: 'name', width: 300 },
            { title: '账号ID', dataIndex: 'accountID', width: 300 },
            { title: '账号状态', dataIndex: 'status', width: 300 },
        ],
    }
    // 获取门店人员详情
    const getStoreUserInfo = () => {
        let params = {
            userId: data.userId,
            dealerCode: data.dealerCode,
            qywxStatus: data.qywxStatus
        }
        get(allUrl.StoreManage.getStoreUserInfo, params).then(res=>{
            if(res.success){
                setDetail(res.resp[0])
                let source1 = {
                    id: 0,
                    key: 0,
                    name: 'SERES协同系统',
                    accountID: res.resp[0] ? res.resp[0].userId : '-',
                    status: res.resp[0] ? status_map[res.resp[0].status] : '-'
                }
                let source2 = {
                    id: 1,
                    key: 1,
                    name: '企业微信',
                    accountID: res.resp[0] ? res.resp[0].qywxUserId : '-',
                    status:  res.resp[0] ? utilsDict('qywxStatus', res.resp[0].qywxStatus) : '-',
                }
                setAccountSource([source1, source2])
                // 总经理 交付经理 技术专家 服务经理 用户关爱经理 金融保险专员 市场经理 显示面试信息
                let arr = [SHOP_MANAGER_CODE, DELIVERY_MANAGER_CODE, TM_CODE, SERVICESTATION_MASTER_CODE, USERCARE_MANAGER_CODE, JRJL, POST_MM_CODE, POST_BAM, FJSZJ, XMTZG]
                if(res.resp[0] &&  arr.indexOf(res.resp[0].position) > -1){
                    setShowinterview(true)
                } else {
                    setShowinterview(false)
                }
            }
        })
    }
    // 获取面试信息
    const getTableData = () =>{
        setLoading(true)
        get(allUrl.Authority.getStoreKeyPostInterviewDetail,{userId:data.userId}).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                // Dt.list.map((item,index)=>item.key = index)

                setDataSource(Dt.list)
                setInterviewResult(Dt.interviewResult)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }

    const getFileName = (str) => {
        return str.substr(str.lastIndexOf('/') + 1)
    }
    const lookAt = (url) => {

        if(isImage(url)){
            setCurrentUrl(url)
        } else {
            window.open(url)
        }
    }

    useEffect(() => {
        getStoreUserInfo()
        getTableData()
    },[])

    const onContextMenuHanlder = (e) => {
        e.preventDefault()
        return false
    }
    useEffect(() => {
        if(currentUrl) {
            setPreviewVisible(true)
        }
    }, [currentUrl]);

    return <div>
        <div className="title-first">门店员工详情</div>
        <div className='user-detail'>
            <div className='info-box'>
                <div className='bottom-box'>
                    <Row>
                        <Col span={24}><Form.Item>
                            <div className='user-info'>
                                <span className="name-span">{detail.userName ? detail.userName :'-'}</span>
                                <span className='middle-span'>
                                    {`（${detail.organizationsDescription ? detail.organizationsDescription : '-'}）`}
                                </span>
                                <span className={`${detail.isOnJob == 1 ? 'on-job' : 'out-job'}`}>{detail.isOnJob == 1 ? '在职' : '离职'}</span>
                            </div>


                        </Form.Item></Col>
                        <Col span={8}><Form.Item label='岗位'>{detail.positionName ? detail.positionName :'-'}</Form.Item></Col>
                        {
                            (data.position == MOTOR_MECHANIC_CODE
                            || data.position == TM_CODE
                            || data.position == QYJSJL_CODE
                            || data.position == FJSZJ)
                            ?
                                <Col span={8}><Form.Item label='是否持有低压电工作证'>{detail.hasElecCert == 1 ? '是' :'否'}</Form.Item></Col>
                            : null
                        }
                        <Col span={8}><Form.Item label='兼职岗位'>{detail.partTimePositionsName || '-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='统一登录账号'>{detail.staffNumber || '-'}</Form.Item></Col>
                        {
                            detail.position == MOTOR_MECHANIC_CODE
                            || detail.position == TM_CODE
                            || detail.position == QYJSJL_CODE
                            || detail.position == FJSZJ
                            ?
                            <Col span={8}>
                                <Form.Item label='查看证书'>{
                                    (detail.position == MOTOR_MECHANIC_CODE
                                    || detail.position == TM_CODE
                                    || detail.position == QYJSJL_CODE
                                    || detail.position == FJSZJ)
                                    && detail.hasElecCert == 1 ? detail.elecCertUrlsArray.map((item) => {
                                    return <><Button type='link' onClick={() => lookAt(item)}><span className="ellipsis">{getFileName(item)}</span></Button></>
                                    }) : null
                                }</Form.Item>
                            </Col>
                            : null
                        }


                        <Col span={8}><Form.Item label='身份证号'>{detail.idNum ? detail.idNum :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='手机号'>{detail.mobilePhone ? detail.mobilePhone :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='邮箱'>{detail.email ? detail.email :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='入职时间'>{detail.inductionTime ? moment(detail.inductionTime).format('YYYY-MM-DD') : '-'}</Form.Item></Col>
                        {detail.isOnJob == 0 ? <>
                            <Col span={8}><Form.Item label='离职操作时间'>{detail.exitTime || '-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='离职操作者'>{detail.exitOperator || '-'}</Form.Item></Col></> : null}
                    </Row>
                </div>
            </div>
            <div className='user-container'>
                {
                    <div className='box'>
                        <div className='top-title'>员工业务信息</div>
                        <div className='bottom-box'>
                            { showinterview ? <div style={{paddingBottom:'20px'}} className='box-item'>
                                <div className='item-title'>
                                    <span className='blue-dot'></span>
                                    <span>面试信息</span>
                                    {
                                    // interviewResult  ? [ interviewResult =='通过' ? <span className='pass-result'>{`面试${interviewResult}`}</span> :
                                    // <span className='unpass-result'>{interviewResult}</span>]
                                    // : [<span className='no-result'>无面试记录</span>]
                                    (function fn1(){
                                        // 自执行函数
                                        if(interviewResult =='面试通过'){
                                            return <span className='pass-result'>{interviewResult}</span>
                                        }if(interviewResult =='面试不通过'){
                                            return <span className='unpass-result'>{interviewResult}</span>
                                        }if(interviewResult =='免面试'){
                                            return <span className='pass-result'>{interviewResult}</span>
                                        }else{
                                            return <span className='no-result'>无面试记录</span>
                                        }
                                    })()
                                    }
                                </div>
                                <div className='interview'>
                                    <Table {...InforData}/>
                                </div>
                            </div>:null
                            }
                            <div className='box-item'>
                                <div className='item-title'><span className='blue-dot'></span><span>培训信息</span></div>
                                <div className='interview'>
                                    <PersonnelTrainingInfo type={2} rowData={rowData} recordObj={data}/>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <div className='box'>
                    <div className='top-title'>员工各系统账号信息</div>
                    <div className='bottom-box'>
                        <div className='account-info'>
                            <Table {...accountData}/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div className='user-operation'>
            <Button onClick={() => { history.goBack() }}>返回</Button>
        </div>
        {
            <Modal visible={previewVisible} width={'90%'} footer={null} onCancel={() => {
              setCurrentUrl('')
              setPreviewVisible(false)
            }}>
                <div onContextMenu={onContextMenuHanlder}>
                    <img src={currentUrl} alt="" style={{ width: '100%' }}/>
                </div>

            </Modal>
        }
    </div>
}
export default UserDetail
