import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Button, message, Popconfirm, Input, Table, Row, Col, Select, Modal, Checkbox } from "antd";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
const BatchImportModal = (props) => {
  const { title, visible, onCancel, cb, 
    successImportData, errorImportData, 
    importDataUrl ,exportErrorDataUrl,
    importSuccessColumns,
    importErrorColumns
  } = props;

  const addImportExcel = () => {
    cb(successImportData) 
    onCancel()
  };
  const exportErrorData = () => {
    post(
      exportErrorDataUrl,
      { data: errorImportData },
      {
        responseType: "blob",
      }
    ).then((res) => {
      if (res) {
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容ie
          window.navigator.msSaveBlob(blob, title + "导入失败数据.xlsx");
        } else {
          let downloadElement = document.createElement("a");
          let href = window.URL.createObjectURL(blob); //创建下载的链接
          downloadElement.href = href;
          downloadElement.download = title + "导入失败数据" + ".xlsx"; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放掉blob对象
          message.success("下载成功！");
        }
      } else {
        message.error(res.msg);
      }
    });
  };
  const closeMoDal = () => {
    onCancel();
    
  };
  return (
    <Modal open={visible} title="导入确认" width={800} onOk={addImportExcel} onCancel={closeMoDal}>
      <div style={{ marginBottom: 20, fontSize: 15 }}>
        数据解析结果如下所示，点击“导出失败数据”可下载失败数据，点击“确定”将导入成功数据，点击“取消”将取消此次导入，请您仔细确认后执行对应操作。
      </div>
      <Row gutter={30}>
        <Col span={12}>
          <Row style={{ height: 50 }} align="middle">
            <p>成功数据（{successImportData.length}）</p>
          </Row>
          <Table rowKey={(record) => record.index} columns={importSuccessColumns} dataSource={successImportData} />
        </Col>
        <Col span={12}>
          <Row style={{ height: 50 }} align="middle">
            <p>失败数据（{errorImportData.length}）</p>
            {errorImportData.length ? (
              <Button onClick={exportErrorData} type="primary">
                导出失败数据
              </Button>
            ) : null}
          </Row>
          <Table rowKey={(record) => record.index} columns={importErrorColumns} dataSource={errorImportData} />
        </Col>
      </Row>
    </Modal>
  );
};
export default BatchImportModal;
