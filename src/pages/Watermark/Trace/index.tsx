import { postSeresFileOperateRecognition } from '@/apis/file-manage';
import PageContainer from '@/components/page-container';
import ProTable from '@/components/pro-table';
import { modalFormLayout } from '@/constants/form';
import { watermarkTracePermissions } from '@/permissions/file';
import { Button, Form, message, Modal, Upload, UploadFile } from 'antd';
import React, { FC, useCallback, useState } from 'react';

const WatermarkTrace: FC = () => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<{ file: UploadFile }>();
  const [pending, setPending] = useState(false);
  const [dataSource, setDataSource] = useState<FileManage.FileRecognitionDTO[]>([]);

  const handleModalOk = useCallback(async () => {
    try {
      const { file } = await form.validateFields();
      setDataSource([]);
      const formData = new FormData();
      formData.append('file', file.originFileObj as File);
      const res = await postSeresFileOperateRecognition(formData);
      setDataSource(res);
      message.success('识别成功');
      setOpen(false);
    } catch (error) {
    } finally {
      setPending(false);
    }
  }, [form]);

  return (
    <PageContainer>
      <ProTable
        loading={pending}
        dataSource={dataSource}
        columns={[
          {
            dataIndex: 'bizCategory',
            title: '业务类型',
          },
          {
            dataIndex: 'fileName',
            title: '文件名称',
          },
          {
            dataIndex: 'sendTime',
            title: '下发时间',
            valueType: 'dateTime',
          },
          {
            dataIndex: 'sendUser',
            title: ' 下发人',
          },
          {
            dataIndex: 'fromUser',
            title: '文件来源人',
          },
          {
            dataIndex: 'fromOrg',
            title: '来源人所属',
          },
          {
            dataIndex: 'fromUserNo',
            title: '编号',
          },
          {
            dataIndex: 'createTime',
            title: '生成时间',
            valueType: 'dateTime',
          },
        ]}
        actions={[
          {
            children: '上传材料',
            type: 'primary',
            onClick: () => setOpen(true),
            permission: watermarkTracePermissions.uploadMaterial,
          },
        ]}
        actionPosition="toolbar"
      />
      <Modal
        maskClosable={false}
        title="上传材料"
        open={open}
        onCancel={() => setOpen(false)}
        forceRender
        afterClose={form.resetFields}
        onOk={handleModalOk}
        confirmLoading={pending}
      >
        <Form {...modalFormLayout} form={form}>
          <Form.Item
            label="上传材料文件"
            name="file"
            rules={[{ required: true, type: 'object', message: '请上传材料文件' }]}
            valuePropName="fileList"
            normalize={(value) => value?.fileList?.[0]}
            getValueProps={(value) => ({
              fileList: value ? [value] : [],
            })}
          >
            <Upload accept=".pdf,.jpeg,.jpg,.png" showUploadList maxCount={1} beforeUpload={() => false}>
              <Button>上传文件</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default WatermarkTrace;
