import { Select, SelectProps } from 'antd';
import React, { FC } from 'react';
import { useStoreJobs } from '../../hooks';

const StoreJobSelect: FC<Omit<SelectProps<string, FileManage.StorePost>, 'options'>> = (props) => {
  const { data, loading } = useStoreJobs();
  return <Select loading={loading} {...props} options={data} fieldNames={{ value: 'code', label: 'name' }} />;
};

export default StoreJobSelect;
