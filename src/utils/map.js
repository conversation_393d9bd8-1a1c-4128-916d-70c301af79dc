/* 
定位
点标记
比例尺插件
信息窗体
经纬度附近搜索
关键字搜索
交通路径规划（经纬度或地点名）
步行路径规划（经纬度或地点名）
经纬度获取地址

*/
import initMap from './initMap.js';
export const init = (container, props) => {
  const config = {
    /* 公司jsapi */
    key: 'a2ee3768857dd150202833d44ea987a2',
  };
  return new Promise((resolve, reject) => {
    initMap(config)
      .then((AMap) => {
        // debugger

        let arr = [];
        if (Array.isArray(container) && Array.isArray(props)) {
          container.forEach((element, index) => {
            let prop = props[index] ? props[index] : props[0];
            arr.push(new window.AMap.Map(element, prop));
          });
          resolve(arr);
        } else {
          if (container) resolve(new window.AMap.Map(container, { ...props }));
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

/**
 *
 * @method {*} getCurrentPosition 定位
 * @param {*} map 地图实例
 * @param {Function} success 定位成功的回调
 * @param {Function} fail 定位失败的回调
 * @param {Object} otherProps 其他参数，如有需要的话 具体api见https://lbs.window.AMap.com/api/javascript-api/reference/location#m_window.AMap.Geolocation
 */
export const getCurrentPosition = (map, success, fail, otherProps) => {
  window.AMap.plugin('AMap.Geolocation', function () {
    var geolocation = new window.AMap.Geolocation({
      enableHighAccuracy: false, //是否使用高精度定位，默认:true
      timeout: 10000,
      maximumAge: 0,
      convert: true,
      showButton: true,
      buttonPosition: 'RB',
      showMarker: true,
      showCircle: true,
      panToLocation: true,
      zoomToAccuracy: false,
      ...otherProps,
    });
    map.addControl(geolocation);
    geolocation.getCurrentPosition();
    window.AMap.event.addListener(geolocation, 'complete', onComplete);
    window.AMap.event.addListener(geolocation, 'error', onError);
    function onComplete(data) {
      success(data);
    }
    function onError(err) {
      fail(err);
    }
  });
};

/**
 * @method {*} addMarker 添加点标记
 * @param {*} map 地图实例
 * @param {Array} marker 需要定位的点经纬度集合，结构如 [{ lng: 116.39, lat: 39.9 ，otherProps:{title:'广州'}}]
 * @param {Function} callback 点标记点击事件回调
 * @param {Object} otherProps 定位其他属性，止于marker对象数组里面 具体api见https://lbs.window.AMap.com/api/javascript-api/reference/overlay#marker
 * @returns 点标记集合markers
 */
//  必须mmp： 高德的这个点标记必须传入的是字符串的经纬度。
//  position: [lnglats[i].lng+’’, lnglats[i].lat+’’]
export const addMarker = (map, marker, callback) => {
  let markers = marker.map((e) => {
    let mark = new window.AMap.Marker({
      position: e.position || [e.lng, e.lat], // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
      ...e.otherProps,
    });
    if (callback) mark.on('click', callback);
    return mark;
  });
  map.add(markers);
  return markers;
};
/**
 * @method {*} addDistrictSearch 添加区域路线
 * @param {*} map 地图实例
 * @param {string} city 区域code
 * @returns 点标记集合
 */
export const addDistrictSearch = (map, city) => {
  window.AMap.plugin(['AMap.DistrictSearch'], () => {
    new window.AMap.DistrictSearch({
      extensions: 'all',
      subdistrict: 0,
    }).search(city, (status, result) => {
      if (status == 'complete') {
        let polygons = [];
        let bounds = result.districtList[0].boundaries;
        if (bounds) {
          for (let i = 0, l = bounds.length; i < l; i++) {
            let polygon = new window.AMap.Polygon({
              strokeWeight: 1,
              strokeColor: '#6F92DF',
              fillColor: '#B0E3C0',
              fillOpacity: 0.7,
              path: bounds[i],
              strokeStyle: 'dashed',
            });
            polygons.push(polygon);
          }
          map.add(polygons);
          map.setFitView(); //地图自适应
        }
      } else {
        console.warn(result.info);
      }
    });
  });
};

/**
 * @method {*} removeMarker 移除点标记
 * @param {*} map 地图实例
 * @param {Array} marker 由removeMarker返回的点标记集合
 */
export const removeMarker = (map, marker) => {
  map.remove(marker);
};
/**
 * @param {*} map 地图实例
 * @param {Boolean} noScale 不需要比例尺  true表示不需要
 * @param {Boolean} noToolBar 不需要工具栏 true表示不需要
 */
export const initScaleTools = (map, noScale, noToolBar) => {
  if (!noScale) {
    map.plugin(['AMap.Scale'], function () {
      var scale = new window.AMap.Scale();
      map.addControl(scale);
    });
  }
  if (!noToolBar) {
    map.plugin(['AMap.ToolBar'], function () {
      var tool = new window.AMap.ToolBar();
      map.addControl(tool);
    });
  }
};
/**
 * 信息窗体
 * @param {*} map 地图实例
 * @param {Array} center 经纬度
 * @param {String} content 信息窗体内容
 */
export const showInfoWindow = (map, center, content) => {
  var infoWindow = new window.AMap.InfoWindow({
    content: content,
  });
  infoWindow.open(map, center);
};
/**
 *
 * @param {*} map 地图实例
 * @param {Array} center 经纬度
 * @param {Number} radius 取值范围：0-50000 范围
 * @param {Object} otherProps  city 页码等其他参数
 * @param {Function} callback 回调
 * @param {String} keyword 搜索关键字
 * https://lbs.window.AMap.com/api/javascript-api/reference/search#m_window.AMap.PlaceSearch
 */
export const searchNearBy = (map, center, callback, otherProps, keyword, radius) => {
  map.clearMap();
  window.AMap.plugin(['AMap.PlaceSearch'], function () {
    var placeSearch = new window.AMap.PlaceSearch({
      map: map,
      ...otherProps,
    });
    placeSearch.searchNearBy(keyword || '', center, radius || 1000, callback);
  });
};

/**
 * @param {*} map 地图实例
 * @param {Function} callback 回调函数
 * @param {Object} otherProps 其他属性
 * @param {String} keyword 关键字
 * <AUTHOR> 2021-07-09 <EMAIL>
 */
export const searchByKeyword = (map, callback, otherProps, keyword) => {
  map.clearMap();
  window.AMap.plugin(['AMap.PlaceSearch'], function () {
    var placeSearch = new window.AMap.PlaceSearch({
      map: map,
      ...otherProps,
    });
    placeSearch.search(keyword, callback);
  });
};
/**
 *
 * @param {*} map 地图实例
 * @param {Number} startLngLat  起点经纬度
 * @param {Number} endLngLat 终点经纬度
 * @param {String} city 城市名 默认广州
 * @param {Function} callback 回调函数
 * @param {String} panel 容器id
 * @param {Boolean} isLngLat 是否经纬度查询，如传false 则为名称查询
 * @param {String} startName 起点名称
 * @param {String} endName 终点名称
 * @param {Object}  otherProps 构造函数其他参数，详见https://lbs.window.AMap.com/api/javascript-api/reference/route-search#m_TransferResult
 */
export const transfer = (
  map,
  startLngLat,
  endLngLat,
  city,
  callback,
  panel,
  isLngLat = true,
  startName,
  endName,
  otherProps
) => {
  window.AMap.plugin('AMap.Transfer', function () {
    var transOptions = {
      map,
      city: city || '广州市',
      panel: panel || 'panel',
      policy: window.AMap.TransferPolicy.LEAST_TIME,
      ...otherProps,
    };
    var transfer = new window.AMap.Transfer(transOptions);
    if (isLngLat) {
      transfer.search(startLngLat, endLngLat, function (status, result) {
        callback && callback(status, result, transfer);
      });
    } else {
      transfer.search([{ keyword: startName }, { keyword: endName }], function (status, result) {
        callback && callback(status, result, transfer);
      });
    }
  });
};
/**
 *
 * @param {*} map 地图实例
 * @param {Number} startLngLat 起点经纬度
 * @param {Number} endLngLat 终点经纬度
 * @param {String} city 城市
 * @param {Function} callback 回调函数
 * @param {String} panel 容器id
 * @param {Number} isLngLat 是否经纬度查询 默认是
 * @param {String} startName 当isLngLat传false时，为起点名称
 * @param {String} endName 当isLngLat传false时，为终点名称
 * @param {Object} otherProps 其他属性
 */
export const walking = (
  map,
  startLngLat,
  endLngLat,
  city,
  callback,
  panel,
  isLngLat = true,
  startName,
  endName,
  otherProps
) => {
  window.AMap.plugin('AMap.Walking', function () {
    var transOptions = {
      map,
      city: city || '广州市',
      panel: panel || 'panel',
      ...otherProps,
    };
    var warking = new window.AMap.Walking(transOptions);
    if (isLngLat) {
      warking.search(startLngLat, endLngLat, function (status, result) {
        callback && callback(status, result, warking);
      });
    } else {
      warking.search([{ keyword: startName }, { keyword: endName }], function (status, result) {
        callback && callback(status, result, warking);
      });
    }
  });
};
// 清除线路图
/**
 *
 * @param {*} lineStrory window.AMap.Walking/transfer 创建的实例对象
 */
export const clearLine = (lineStrory) => {
  lineStrory.clear();
};
// 经纬度获取地址
/**
 *
 * @param {String} LngLat 经纬度
 * @param {Function} callback 回调函数
 * @param {Object} otherProps 其他参数
 */
export const getAddressByLngLat = (LngLat, callback, otherProps) => {
  window.AMap.plugin('AMap.Geocoder', function () {
    var geocoder = new window.AMap.Geocoder({
      ...otherProps,
    });

    geocoder.getAddress(LngLat, function (status, result) {
      callback(status, result);
    });
  });
};
const mapJS = {
  init,
  getCurrentPosition,
  addMarker,
  removeMarker,
  initScaleTools,
  showInfoWindow,
  searchNearBy,
  searchByKeyword,
  transfer,
  walking,
  clearLine,
  getAddressByLngLat,
};
export default mapJS;

/* //
高德两个api搜索
// 关键字搜索
window.open(`https://m.window.AMap.com/search/view/keywords=${keywords}`)
// 出行路径规划
window.open(`https://gaode.com/dir?from[name]=起点&from[lnglat]=${'出发经度'},${'出发维度'}&to[name]=终点&to[lnglat]=${目标经度},${目标维度}&policy=1&type=car`) */
