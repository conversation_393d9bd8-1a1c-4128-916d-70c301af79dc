import './index.less'
import React, { useEffect, useState, forwardRef, useRef } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Row, Col,message, Tabs } from 'antd'
// import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import AddOrEditStore from './components/AddOrEditStore'
import SelectOrg from './components/SelectOrg'
// import { post } from '@/utils/request'
import allUrl from '@/utils/url'
import PublicTable from '@/components/Public/PublicTable'
import { get } from '../../../utils/request';
// import TotalNumberOfStores from '../../../assets/img/TotalNumberOfStores.png'
// import StorePersonnel from '../../../assets/img/StorePersonnel.png'
// import NoAccountStore from '../../../assets/img/NoAccountStore.png'
// import { utilsDict } from '@/utils/utilsDict'
import {roleJudgment} from '@/utils/authority'
import moment from 'moment';
const { TabPane } = Tabs
const OrderList = () => {
    const tableRef = useRef()
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [defaultQuery, setDefaultQuery] = useState({isAdmin:false})
    const [AddOrEditStoreVisible, setAddOrEditStoreVisible] = useState(false)
    const [SelectOrgVisible, setSelectOrgVisible] = useState(false)
    const [AddOrEditStoreTitle, setAddOrEditStoreTitle] = useState('')
    const [entryList, setEntryList] = useState({})
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [selectedRows, setSelectedRows] = useState([])
    const [tabsKey, setTabsKey] = useState('1')
    const [rowData, setRowData] = useState({})
    const [orderStat, setOrderStat] = useState({})
    const [tableHeight,setTableHeight] = useState(0)
    const onSearch = (values = {}) => {
        if (values.Time && values.Time.length) {
            values.startDate = moment(values.Time[0]).format('YYYY-MM-DD')
            values.endDate = moment(values.Time[1]).format('YYYY-MM-DD')
            delete values.Time
        }
        if (tabsKey === '2') {
            values.isAdmin = true
        } else {
            values.isAdmin = false
        }
          
        setDefaultQuery(values)
        rowSelectionChange({ selectedRowKeys: [], selectedRows: [] })
    }
    const RowDetail = (record) => {
        setRowData(record)
        setAddOrEditStoreVisible(true)
        setAddOrEditStoreTitle('订单详情')
    }
    const rowSelectionChange = ({ selectedRowKeys, selectedRows }) => {
        console.log(selectedRowKeys, selectedRows)
        setSelectedRowKeys(selectedRowKeys)
        setSelectedRows(selectedRows)
    }

    const tabsCallback = key => {
        let obj = JSON.parse(JSON.stringify(defaultQuery))
        if (key === '2') {
            obj.isAdmin = true
        } else {
            obj.isAdmin = false
        }
        setDefaultQuery(obj)
        setTabsKey(key)
    }

    const renderOperation = (text, record) => {
        return <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'ORDERMANAGE_LIST_DETAIL') ?
                    
                        <span key={2} style={{ cursor: 'pointer' }} onClick={() => RowDetail(record)}>详情</span>
                     : null
            }
        </div>
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 475
        }else{
            h = winH   - 47 - 54 - 400
        }
        setTableHeight(h)
    }
    useEffect(()=>{
        initPage()
    },[])

    useEffect(() => {
        get(allUrl.common.entryLists, { codes: 'scrm-mtcloud-order-status' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setEntryList({ ...Dt })
            } else {
                // message.error(res.message)
            }
        })
    }, [])
    useEffect(() => {
        let obj = {}
        if (tabsKey === '2') {
            obj.isAdmin = true
        }else{
            obj.isAdmin = false
        }
        get(allUrl.OrderManage.getOrderStat, { ...obj }).then(res => {
            if (res.success) {
                setOrderStat(res.resp[0])
            } else {
                // message.error(res.msg)
            }
        })
    }, [tabsKey])
    const columns = [
        { title: '订单编号', dataIndex: 'orderCode', width: 80 },
        { title: '下单手机号', dataIndex: 'userMobile', width: 120 },
        { title: '购车人姓名', dataIndex: 'carBuyerName', width: 120 },
        { title: '购车人手机号', dataIndex: 'carBuyerPhone', width: 140 },
        { title: '成交车型', dataIndex: 'productName', width: 200 },
        { title: '推荐人', dataIndex: 'recommendUserName',width:140 },
        { title: '推荐人手机号', dataIndex: 'recommendUserPhone',width:140 },
        { title: '销售门店', dataIndex: 'dealerName',width:200 },
        { title: '订单状态', dataIndex: 'orderStatusName',width:140 },
        { title: '下单时间', dataIndex: 'orderTime'},
        { title: '操作', width: 80, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
    ]
    let searchList = [
        { label: '购车人名称', name: 'carBuyerName', type: 'Input', colSpan: 6 },
        { label: '下单手机号', name: 'userMobile', type: 'Input', colSpan: 6 },
        { label: '推荐人姓名', name: 'recommendName', type: 'Input', colSpan: 6 },
        { label: '推荐人手机号', name: 'recommendMobile', type: 'Input', colSpan: 6 },
        { label: '销售门店', name: 'dealerName', type: 'Input', colSpan: 6 },
        { label: '下单时间', name: 'Time', type: 'RangePicker', colSpan: 6 },
        { label: '订单状态', name: 'orderStatus', type: 'Select', colSpan: 6, data: entryList['scrm-mtcloud-order-status'] || [] },
    ]

    const getColor = (num) => {
        if (parseInt(num) > 0) {
            return '#52C41A'
        } else if (parseInt(num) < 0) {
            return '#FF4D4F'
        } else {
            return '#ABABAB'
        }
    }
    const unitData = [
        { title: '日', field: 'dayPercentage', num: orderStat.dayPercentage, color: getColor(orderStat.dayPercentage) },
        { title: '周', field: 'weekPercentage', num: orderStat.weekPercentage, color: getColor(orderStat.weekPercentage) },
        { title: '月', field: 'monthPercentage', num: orderStat.monthPercentage, color: getColor(orderStat.monthPercentage) },
    ]
    return (
        <div className='OrderList'>
            <Tabs onChange={tabsCallback} activeKey={tabsKey} type="card">
                {
                    roleJudgment(userInfo,'ORDERMANAGE_USER_LIST') ?
                    <TabPane tab="用户列表" key="1">
                        {
                        <div key={1}>
                                <Row className='TurnoverInfo'>
                                    <Col className='Overall'>
                                        <p>昨日大定成交量</p>
                                        <p className='num'>{orderStat.orderNum}</p>
                                    </Col>

                                    {
                                        unitData.map((item, index) => {
                                            return <Col className='unitData' key={index}>
                                                <p>{item.title}</p>
                                                <p className='num' style={{ color: item.color }}>{item.num}</p>
                                            </Col>
                                        })
                                    }
                                </Row>
                                <PublicTableQuery onSearch={onSearch} searchList={searchList} />
                                <div className='tableData'>
                                    <Row className='tableTitle'>
                                        <Col className='text'>订单列表</Col>
                                    </Row>
                                    <PublicTable ref={tableRef} method='get' columns={columns} rowSelection={false} defaultQuery={defaultQuery} type={2} url={allUrl.OrderManage.getOrderList} rowSelectionChange={rowSelectionChange} />
                                </div>
                            </div>
                        }
                    </TabPane>:null
                }
                {
                    roleJudgment(userInfo,'ORDERMANAGE_ADMIN_LIST') ?
                        <TabPane tab="管理员列表" key="2">
                            <div key={2}>
                                <Row className='TurnoverInfo'>
                                    <Col className='Overall'>
                                        <p>昨日大定成交量</p>
                                        <p className='num'>{orderStat.orderNum}</p>
                                    </Col>
                                    {
                                        unitData.map((item, index) => {
                                            return <Col className='unitData' key={index}>
                                                <p>{item.title}</p>
                                                <p className='num' style={{ color: item.color }}>{item.num}</p>
                                            </Col>
                                        })
                                    }
                                </Row>
                                <PublicTableQuery onSearch={onSearch} searchList={searchList} />
                                <div className='tableData'>
                                    <Row className='tableTitle'>
                                        <Col className='text'>订单列表</Col>
                                    </Row>
                                    <PublicTable ref={tableRef} method='get' columns={columns} rowSelection={false} defaultQuery={defaultQuery} type={2} url={allUrl.OrderManage.getOrderList} rowSelectionChange={rowSelectionChange} />
                                </div>

                            </div>
                        </TabPane> : null
                }
            </Tabs>

            {
                AddOrEditStoreVisible &&
                <AddOrEditStore rowData={rowData} title={AddOrEditStoreTitle} visible={AddOrEditStoreVisible} onCancel={() => setAddOrEditStoreVisible(false)} />
            }
            {
                SelectOrgVisible &&
                <SelectOrg getTableData={tableRef ? tableRef.current.getTableData : null} selectedRows={selectedRows} title={'选择组织'} visible={SelectOrgVisible} onCancel={() => setSelectOrgVisible(false)} />
            }
        </div>
    )
}
export default OrderList
