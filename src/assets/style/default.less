//初始化样式
*::-webkit-scrollbar {
    /*滚动条整体样式*/
    width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px;
}
*::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background   : #ededed;
}
*::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background   : rgba(0,0,0,0.15);
}
::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.25);
}
::-webkit-scrollbar-thumb:active {
    background: rgba(0,0,0,0.3);
}

.RequireAsterisk::before{
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: Sim<PERSON><PERSON>, sans-serif;
    line-height: 1;
    content: '*';
}