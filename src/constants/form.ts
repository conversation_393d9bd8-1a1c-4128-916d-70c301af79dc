/**
 * form主体间距
 */
export const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 8 },
  style: {
    maxWidth: 1440,
  },
};

/**
 * 超长的FormItem间距
 */
export const doubleFormItemLayout = {
  wrapperCol: { span: 16 },
};

/**
 * Modal Form 布局配置
 */
export const modalFormLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

/**
 * form一体的按钮间距
 */
export const formButtonLayout = {
  wrapperCol: { ...formLayout.wrapperCol, offset: formLayout.labelCol.span },
};
