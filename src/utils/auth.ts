/**
 *
 * @param {Object} userInfo 当前用户信息
 * @param {String} code 按钮权限编码
 * @returns Boolean
 */
export const checkUserPermission = (userInfo: Record<string, any>, code: string) => {
  const authorized = userInfo && userInfo.permissionCodeList?.includes(code);
  if (!authorized && process.env.NODE_ENV === 'development') {
    console.log('checkUserPermission', `当前用户无权限：${code}，请检查权限配置，开发环境已放开检查。`);
    return true;
  }
  return authorized;
};
