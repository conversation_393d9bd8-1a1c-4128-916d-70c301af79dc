
.detail{
    background: white;
    .DetailHeader{
        display: flex;
        .DetailHeaderTitle{
            padding: 0px 0px 8px 0px;
            font-size: 20px;
            font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
            font-weight: 500;
            color: rgba(0,0,0,0.85);
        }
        .DetailHeaderButton{
            height: 60px;
            line-height: 60px;
            .ant-btn{
                margin: 0 10px;
            }
        }
    }

    .detail_warp{
        background: #f0f2f5;
        overflow-y: auto;
        height: calc(100vh - 48px - 54px - 36px - 20px);
        .detail_title{
            // position: fixed;
            font-size: 20px;
            font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
            font-weight: 500;
            text-align: left;
            color: rgba(0,0,0,0.85);
            padding-bottom: 16px;
            padding-left: 24px;
            background: white;
        }
        .detail_con{
            margin: 24px;
            background: white;
            .custom-Collapse{
                .custom-Panel{
                    .ant-table-tbody{
                        cursor: pointer;
                    }
                    .custom-Panel-content{
                        display: flex;
                        overflow-x: auto;
                        overflow-y: hidden;
                        .ant-card{
                            width: 374px;
                            height: 280px;
                            margin-right: 14px;
                            flex-shrink: 0;
                            .ant-card-body{
                                padding-right: 12px;
                                .ant-form-item{
                                    margin-bottom: 14px;
                                    .ant-upload-list{
                                        height: 70px !important;
                                        overflow-y: auto !important;
                                    }
                                    .ant-form-item-explain{
                                        display: none;
                                    }
                                }
                                .file{
                                    .promptText{
                                        font-size: 11px;
                                        font-family: PingFangSC, PingFangSC-Regular;
                                        font-weight: 400;
                                        text-align: left;
                                        color: rgba(0,0,0,0.65);
                                        position: absolute;
                                        top: 6px;
                                        left: 88px;
                                    }
                                    .ant-upload-disabled{
                                        display: none;
                                    }
                                }
                            }
                        }
                        .ant-card:last-child{
                            margin-right: 0;
                        }
                        .ant-table-wrapper{
                            width: 100%;
                        }
                    }
                }
            }
            
        }
    }
    .detail_btns{
        height: 56px;
        line-height: 56px;
        text-align: right;
        margin-right: 24px;
        border-top: solid 1px #e8e8e8;
    }
}