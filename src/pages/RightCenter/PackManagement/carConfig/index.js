import React, { useState, useContext, useRef } from 'react';
import { Modal, Tabs, Checkbox, message } from 'antd';
import { getWidth, offsetLeft } from '@/utils';
import CarConfigModal from './CarConfigModal';
import './carConfigModal.less';

const CarConfigIndex = props => {
  const {
    showCarConfig,
    carConfigData,
    setShowCarConfig,
    getModalData,
    allSelectedConfigObj,
    setAllSelectedConfigObj,
  } = props;
  const [indeterminate, setIndeterminate] = useState(null);
  const [checkAll, setCheckAll] = useState(false);
  const [btnDisabled, setBtnDisabled] = useState(true);
  const [selectedData, setSelectedData] = useState({}); //切换具体配置时，保存的所有车型的配置数据
  const [allCheckedListConfig, setAllCheckedListConfig] = useState({}); //保存选择的所有车型配置数据
  const [clickSave, setClickSave] = useState(false); //点击保存按钮

  const modelRef = {};
  carConfigData.forEach(_ => {
    modelRef[_.carType] = useRef(null);
  });

  const handleCancel = () => {
    setShowCarConfig(false);
  };

  const handleSubmit = () => {
    const _allData = getCheckedListConfig();
    //车型配置必选校验
    {
      const carStr = [];
      carConfigData.forEach(_ => {
        if (!_allData[_.carType].length) {
          carStr.push(_.carTypeName);
        }
      });
      if (!!carStr.length) {
        setAllCheckedListConfig(_allData);
        setClickSave(true);
        message.error(`${carStr.join('、')}车型的车型配置是必选项，请选择`);
        return;
      }
    }
    getModalData(_allData);
    handleCancel();
  };

  //获取弹窗内所有选中的配置
  const getCheckedListConfig = () => {
    let arr = {};
    carConfigData.map((_, i) => {
      const carType = _.carType;
      arr[carType] = modelRef[carType].current?.checkedListConfig;
    });
    return arr;
  };

  const onCheckAllChange = e => {
    const { checked } = e.target;
    setIndeterminate(false);
    setCheckAll(checked);
    carConfigData.forEach(_ => {
      modelRef[_.carType].current?.handleChangeCheckAllConfig(e);
    });
  };

  //组装数据,获取所有车型的所有配置{["1/2023/EVR/9999","1/2023/EVR/31"]}
  const handleData = () => {
    const ALL_CAR_CONFIG = [];
    carConfigData.forEach(i => {
      i.configList.forEach(j => {
        j.carOfYearConfig.forEach(k => {
          k.configList.forEach(l => {
            ALL_CAR_CONFIG.push(i.carType + '/' + j.carOfYear + '/' + k.powerModelCode + '/' + l.configKey);
          });
        });
      });
    });
    return ALL_CAR_CONFIG;
  };

  const handleDataChange = data => {
    setSelectedData(prev => {
      const COMBINE_DATA = Object.assign(prev, data);
      //设置确认按钮disabled状态
      setBtnDisabled(!Object.values(COMBINE_DATA).flat().length);

      //设置全部车型配置按钮的选中状态
      {
        const ALL_CAR_CONFIG_LEN = handleData().length;
        const SELECTED_CAR_CONFIG_LEN = Object.values(COMBINE_DATA).flat().length;
        setIndeterminate(!!SELECTED_CAR_CONFIG_LEN && SELECTED_CAR_CONFIG_LEN < ALL_CAR_CONFIG_LEN);
        if (ALL_CAR_CONFIG_LEN > 0 && SELECTED_CAR_CONFIG_LEN === ALL_CAR_CONFIG_LEN) {
          setCheckAll(true);
        } else {
          setCheckAll(false);
        }
      }

      return COMBINE_DATA;
    });
  };
  //处理所有车型的数据
  const filterCarConfigData = ({ carType, carConfigDTO }) => {
    return carConfigDTO.map(item => {
      return {
        label: item.carOfYear,
        value: carType + '/' + item.carOfYear,
        children: item.carOfYearConfig.map(configItem => {
          return {
            parent: carType + '/' + item.carOfYear,
            parentLabel: item.carOfYear,
            label: item.carOfYear + '/' + configItem.powerModelName,
            value: carType + '/' + item.carOfYear + '/' + configItem.powerModelCode,
            children: configItem.configList.map(listItem => {
              return {
                parent: carType + '/' + item.carOfYear + '/' + configItem.powerModelCode,
                label: item.carOfYear + '/' + configItem.powerModelName + '/' + listItem.configValue,
                value: carType + '/' + item.carOfYear + '/' + configItem.powerModelCode + '/' + listItem.configKey,
              };
            }),
          };
        }),
      };
    });
  };

  const title = (
    <>
      <span style={{ marginRight: 20 }}>权益车型配置</span>
      <Checkbox indeterminate={indeterminate} checked={checkAll} onChange={onCheckAllChange}>
        全部车型配置
      </Checkbox>
    </>
  );
  return (
    <Modal
      open={showCarConfig}
      onCancel={handleCancel}
      title={title}
      onOk={handleSubmit}
      okText="确认"
      cancelText="取消"
      width={getWidth()}
      style={{ left: offsetLeft() }}
      okButtonProps={{ disabled: btnDisabled }}
    >
      <Tabs
        type="card"
        size="small"
        className="car-config-tabs"
        items={carConfigData.map(_ => {
          return {
            label: (
              <span className={clickSave && !allCheckedListConfig[_.carType]?.length ? 'car-config-tabs-empty' : ''}>
                {_.carTypeName}
              </span>
            ),
            key: _.carType,
            forceRender: true,
            children: (
              <CarConfigModal
                ref={modelRef[_.carType]}
                carConfigData={filterCarConfigData({ carType: _.carType, carConfigDTO: _.configList })}
                carConfigInfo={allSelectedConfigObj[_.carType]}
                carType={_.carType}
                onDataChange={handleDataChange}
              />
            ),
          };
        })}
      />
    </Modal>
  );
};
export default CarConfigIndex;
