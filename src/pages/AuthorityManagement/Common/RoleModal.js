import React from 'react'
import { Drawer } from 'antd'
import RoleManagementDetail from '../RoleManagement/Detail'
class RoleModal extends React.Component {
    constructor(props) {
        super(props)
        this.state = {

        }
    }
    render() {
        const { visible, handleCancel,getRoleCB } = this.props

        return (
            <>
                <Drawer
                    title="添加角色"
                    placement="right"
                    closable={false}
                    onClose={handleCancel}
                    visible={visible}
                    contentWrapperStyle={{width:'calc(100vw - 208px)'}}
                >
                    {
                        visible && 
                        <RoleManagementDetail isDrawer={true} handleDrawerCancel={handleCancel} handleDrawerCB={getRoleCB} />
                    }
                </Drawer>
            </>
        )
    }
}
export default RoleModal