.detail-create {
    // .ant-form-item-with-help{
    //     // margin-bottom:0;
    // }
    .ant-select-multiple .ant-select-selection-item-remove svg {
        display: none;
    }

    .strategy-title {
        margin-left: 32px;
        margin-right: 17px;
        height: 30px;
        border-bottom: 2px solid #5095D5;

        .left-title {
            width: 122px;
            height: 30px;
            background: #5095D5;
            font-size: 15px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 30px;
            text-align: center;
        }

        margin-bottom: 20px;
    }

    .prefix-red {
        label:not(.ant-radio-wrapper)::before {
            display: inline-block;
            margin-right: 4px;
            color: #ff4d4f;
            font-size: 14px;
            line-height: 1;
            content: '*';
        }
    }

    .custom-item-help {
        color: red;
        font-size: 12px;
        margin-bottom: 15px;
    }

    .custom-item-help-mt6 {
        margin-top: 6px;
    }

}

.bussiness-options {
    display: flex;
    align-items: center;
}

.bussiness-description {
    padding-left: 30px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 400px;
}

.select-dropdown {
    height: 200px;
    width: 300px;
    overflow-y: auto;
    color: 'black';

    .select-item {
        display: flex;
        justify-content: space-between;
        height: 35px;
        padding-left: 10px;
        padding-right: 10px;

        .item-text {
            line-height: 35px;
            color: #000;
        }

        .input-number {
            height: 30px;
        }
    }

    .checked {
        // background-color: aquamarine;
    }
}

.batch-top {
    background-color: #D6EBFF;
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .left-box {
        line-height: 50px;

        .title {
            padding-left: 32px;
            padding-top: 13px;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
        }

        .sub-title {
            margin-left: 17px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #F5222D;
            line-height: 17px;
        }
    }

    .batch-btn {
        margin-right: 32px;
    }

}

.space-compact-block {

    .site-input-split {
        background-color: #fff;
    }

    .site-input-right:not(.ant-input-rtl) {
        border-left-width: 0;
    }

    .site-input-right:not(.ant-input-rtl):hover,
    .site-input-right:not(.ant-input-rtl):focus {
        border-left-width: 1px;
    }

    .dw {
        display: inline-block;
        line-height: 30px;
        margin-left: 10px;
    }

    .tips {
        color: red;
        font-size: 12px;
        margin-left: 10px;
        white-space: nowrap;
        display: inline-block;
        line-height: 30px;
    }

    .ant-form-item-has-error+.site-input-split {
        border: 1px solid #ff4d4f;
    }

}

.effectMileageFrameWrap {
    margin-bottom: 0;

    .effectMileageFrameCls {
        display: inline-block;
        width: 200px;
        margin-top: 5px;
    }

    .w250 {
        width: 250px;
    }

    .mr15 {
        margin-right: 15px;
    }

    .mb0 {
        margin-bottom: 0px;
    }

    .tips {
        color: red;
        font-size: 12px;
        display: inline-block;
        margin-left: 6px
    }

    .ant-form-item-label>label::before {
        display: inline-block;
        margin-right: 4px;
        color: transparent;
        font-size: 14px;
        line-height: 1;
        content: '*';
    }
}

.create-detail-radiogroup{
    width: 100%;
    .ant-space{
        width: 100%;
    }
    .ant-radio-wrapper-in-form-item{
        white-space: nowrap;
        line-height: 32px;
    }
}
.create-detail-name{
    display: flex;
}
.detail-name-select .ant-select-disabled .ant-select-selection-item{
    color: transparent;
}