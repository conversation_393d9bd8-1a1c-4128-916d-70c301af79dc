.m-box {
    background: #F7FBFF;
    border: 1px solid #9BCEFF;
    padding-bottom: 32px;
}

.m-box-title {
    height: 40px;
    background: #D6EBFF;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    line-height: 40px;
    padding-left: 24px;
}

.m-content {
    padding-top: 10px;
    padding-left: 24px;
    padding-right: 24px;
    color: #2A5781;

    .m-content-title {
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: #2A5781;
        line-height: 20px;
    }

    p {
        margin-bottom: 6px;

        :global {
            .ant-tag {
                margin-left: 10px;
                border-radius: 3px;
            }
        }


    }

    .m-content-tips {
        background: #FFFFFF;
        border-radius: 8px;
        border: 1px solid #C9E4FC;
        padding: 16px 20px;
        color: #3D4348;

        :global {
            .ant-tag {
                margin-left: 0
            }
        }

        .m-content-indent {
            text-indent: 36px;
        }
    }
}

.m-imgs {
    margin-bottom: 24px;
    display: flex;

    img {
        width: 50%;
    }
}