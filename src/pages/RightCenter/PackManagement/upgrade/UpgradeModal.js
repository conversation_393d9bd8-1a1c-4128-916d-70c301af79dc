import React, { useState, memo, useEffect, useRef } from 'react'
import { Modal, Form, message, Row, Col, Descriptions, Spin, Input } from 'antd'
import { getWidth, offsetLeft } from '@/utils/index'
import allUrl from '../../../../utils/url';
import { get } from '@/utils/request';
import moment from 'moment'
import CreateStrategyCom from '../../DetailManagement/CreateStrategyCom';
import style from './upgrade.module.less'
const { TextArea } = Input;
const UpgrdeModal = (props) => {
  const { handleCancel, visible, getData, setData, strategyConfig, detailFormConfig } = props
  // const [performingListObj, setPerformingListObj] = useState([]) // 权益履约方
  // const [detailNameOptions, setDetailNameOptions] = useState([]) // 获取所有权益明细名称
  // const [sortOptions, setSortOptions] = useState([]) // 权益分类
  // const [typeOptions, setTypeOptions] = useState([]) // 权益类别
  // const [existPartValue, setExistPartValue] = useState(1) // 关联配件值
  const [entryList, setEntryList] = useState({})

  const [form] = Form.useForm()
  const formRef = useRef(null)

  const { modalUpgradeDetail, modalUpgradeLoading } = getData
  const { detail: dataSourceForm, type } = modalUpgradeDetail

  const { setReciveModalDetail, setModalUpgradeLoading } = setData


  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 16 },
  };
  const BUSINESS_CODE_TYPE = [
    {
      name: '工时',
      value: 1
    },
    {
      name: '配件',
      value: 2
    }
  ]
  const performance_MAP = {
    1: '是',
    2: '否'
  }
  const refund_MAP = {
    1: '支持',
    2: '不支持'
  }

  const getEntryLists = (setPaint) => {
    // 'equity_car_attr,equity_belong,equity_identity,equity_car_identity,equity_detail_refund,equity_detail_performance,equity_business_code, equity_relativeTime, equity_performing, equity_paint, equity_paint_frame'
    get(allUrl.common.entryLists, { codes: 'equity_identity, equity_car_identity, equity_relativeTime' }).then(res => {
      if (res.success) {
        let Dt = res.resp[0]
        for (let i in Dt) {
          Dt[i].forEach(item => {
            item.name = item.entryMeaning
            item.value = item.entryValue
          })
        }
        setEntryList(Dt || {})
      } else {
        //   message.error(res.message)
      }
    })
  }

  const handleOk = () => {
    if (modalUpgradeLoading) return
    const query = form.getFieldsValue()

    // 权益履约方
    // if (performingListObj) {
    //   query.performingList = performingListObj
    // } else {
    //   query.performingList = []
    // }
    if (query.limitTimeFrame == '1') {
      if (Number(query.limitYear) == 0 && Number(query.limitMonth) == 0 && Number(query.limitDay) == 0) {
        message.warn('权益限制年限不能为0年0月0日')
        return
      }
      if (query.limitYear == undefined && query.limitMonth == undefined && query.limitDay == undefined) {
        message.warn('权益限制年限不能为0年0月0日')
        return
      }
    }
    //权益金额抵扣
    if (query.deductionFrame !== 1) {
      query.payAmount = query.deduction = undefined
    }

    //权益限制里程
    if (query.limitLegendFrame !== 1) {
      query.limitLegend = undefined
    }
    if (query.limitLegendFrame !== 2) {
      query.extenderMileage = undefined
    }

    //权益限制年限
    if (query.limitTimeFrame !== 1) {
      query.limitYear = undefined
      query.limitMonth = undefined
      query.limitDay = undefined
    }

    //全保养阶梯策略
    if (query.maintenanceLadderFrame !== 1) {
      query.maintenanceLadder = undefined
    }

    //权益生效里程
    if (query.effectMileageFrame !== 1) {
      query.fixedBeginMileage = undefined
      query.fixedEndMileage = undefined
    }

    // 享有权益频次
    if (query.frequencyRadio === undefined || query.frequencyRadio == '0') {
      query.frequency = 0
    }
    if (query.frequencyRadio == '-1') {
      query.frequency = -1
    }

    // 车联网流量
    if (query.trafficRadio == '0' || query.trafficRadio === undefined) {
      query.traffic = 0
      query.trafficUnit = ''
    }
    if (query.trafficRadio == '-1') {
      query.traffic = -1
      query.trafficUnit = undefined
    }

    // 权益生效时间
    if (query.timeRadio == '1') {
      query.relativeTime = ''
      query.fixedBeginTime = moment(query.fixedRelativeTime[0]).format('YYYY-MM-DD HH:mm:ss')
      query.fixedEndTime = moment(query.fixedRelativeTime[1]).format('YYYY-MM-DD HH:mm:ss')
      delete query.fixedRelativeTime
    }
    if (query.timeRadio == '0') {
      query.fixedBeginTime = ''
      query.fixedEndTime = ''
      delete query.fixedRelativeTime
    }


    // 补漆面选项
    if (query.paintFrame !== 1) {
      query.partRelationList = []
      delete query.paintList
    }
    // else {
    //   const filter = query?.paintList?.filter(i => i.num > 0) || []
    //   let sum = 0
    //   const str = filter.map(item => {
    //     sum += item.num
    //     return `${item.name}*${item.num} `
    //   }) || []
    //   query.paintDesc = str.join(',') + `, 共${sum}面`
    // }
    //权益生效时间和权益生效里程，不能同时选择固定时间/固定里程那一项
    if (query.timeRadio === 1 && query.effectMileageFrame === 1) {
      message.warn('由于策略冲突，同一条权益明细不允许同时存在固定生效里程和固定生效开始时间')
      return
    }
    // 权益业务场景（1-基本场景，2-延保场景，3-升级场景）
    query.businessScene = 3
    query.businessSceneName = '升级场景'
    query.detailVersion = null  //明细编码
    // 权益属性identityName,享有车主身份carIdentityName name字段添加
    entryList['equity_identity'] && entryList['equity_identity'].map(i => {
      if (i.entryValue == query.identity) {
        query.identityName = i.entryMeaning
      }
    })
    entryList['equity_car_identity'] && entryList['equity_car_identity'].map(i => {
      if (i.entryValue == query.carIdentity) {
        query.carIdentityName = i.entryMeaning
      }
    })


    // 原权益明细编码

    if (type === 'add') {
      query.originDetailNo = dataSourceForm.detailNo
      query.originDetailVersion = dataSourceForm.detailVersion
      query.detailNo = ''
      query.id = ''
      query.consumptionStrategy = 1
    } else {
      query.originDetailNo = dataSourceForm.originDetailNo
      query.originDetailVersion = dataSourceForm.originDetailVersion
      query.detailNo = dataSourceForm.detailNo
    }
    handleCancel()

    setReciveModalDetail({ ...dataSourceForm, ...query })
  }
  // const getSortList = () => {
  //   return new Promise((resolve, reject) => {
  //     get(allUrl.DetailedManageInterests.getSortList).then(res => {
  //       if (res.success) {
  //         let Dt = res.resp
  //         setSortOptions(Dt)
  //         resolve(Dt)
  //       } else {
  //         // message.error(res.msg)
  //       }
  //     })
  //   })
  // }
  //查询业务分类编码枚举值
  const getbusinessCodeTypeName = (value) => {
    return BUSINESS_CODE_TYPE.filter(item => item.value === value)[0]?.name
  }
  const filterperformingList = (list) => {
    let arr = []
    if (list) {
      list.map(item => {
        arr.push(item.name)
      })
    }
    if (arr.length) {
      return arr.join(',')
    } else {
      return '-'
    }
  }
  useEffect(() => {
    getEntryLists()
  }, [])

  useEffect(() => {
    if (dataSourceForm) {
      const { name, statement, remark } = dataSourceForm
      formRef.current.setFieldsValue({
        name, statement, remark
      })

    }
  }, [dataSourceForm])
  const titleRender = () => {
    return <div>
      <b>升级明细</b>
      <span style={{ fontSize: '12px', color: 'red', marginLeft: '25px', fontWeight: 'normal' }}>权益明细升级后，权益以升级后的明细策略为准</span>
    </div>
  }
  return (
    <Modal open={visible} onOk={handleOk} onCancel={handleCancel} maskClosable={false} width={getWidth()} style={{ left: offsetLeft() }} title={titleRender()} className='detail-create'>
      <Spin spinning={modalUpgradeLoading}>
        <Form {...layout} name="upgrade" ref={formRef} form={form} >
          <Row>
            <Col span={3}></Col>
          </Row>
          <div className={style.titleWrapper}><h4>基本信息</h4></div>
          {/* 名称 */}
          <Form.Item
            label={detailFormConfig['name'].label}
            className='prefix-red'
            name={detailFormConfig['name'].name}
            rules={[{ max: 100, message: '请输入填写标题，最多100个字，建议50字以内，不影响展示' }]}>
            <Input placeholder="请输入填写标题，最多100个字，建议50字以内，不影响展示" allowClear maxLength={100} />
          </Form.Item>
          {/* 其他：声明 备注 */}
          <Form.Item name='statement' label='权益声明' rules={[]}
            help={<div className='custom-item-help custom-item-help-mt6'>如配置在声明里的权益策略，例如：一次补胎、两次补漆等 不作为权益策略，均无法进行核销</div>}>
            <TextArea rows={4} placeholder='此处需要对外展示，建议描述时，谨慎，例如：赛力斯为您所购买的赛力斯汽车提供整车及增程器包修期限为4年不限里程（以先到者为准，运营车辆除外），详见《保修及保养手册》质量担保明细' allowClear />
          </Form.Item>
          <Form.Item name='remark' label='权益备注' rules={[]} >
            <TextArea rows={4} placeholder='例如：如需备注其他内容，在此描述，此描述不对外展示' allowClear />
          </Form.Item>
         
          <div className={style.descriptions}>
            {!modalUpgradeLoading ? <Descriptions title="" >
              <Descriptions.Item label='业务编码'>{dataSourceForm.businessCode || '-'}</Descriptions.Item>
              <Descriptions.Item label='业务分类' >{getbusinessCodeTypeName(dataSourceForm.businessCodeType) || '-'}</Descriptions.Item>
              <Descriptions.Item label='权益履约方' >{filterperformingList(dataSourceForm.performingList)}</Descriptions.Item>
              <Descriptions.Item label='权益分类'>{dataSourceForm.sortCodeName || '-'}</Descriptions.Item>
              <Descriptions.Item label='权益类别'>{dataSourceForm.typeCodeName || '-'}</Descriptions.Item>
              <Descriptions.Item label='车辆属性'>{dataSourceForm.carAttrName || '-'}</Descriptions.Item>
              <Descriptions.Item label='权益归属'>{dataSourceForm.belongName || '-'}</Descriptions.Item>
              <Descriptions.Item label='是否三方履约'>{performance_MAP[dataSourceForm.performance] || '-'}</Descriptions.Item>

              <Descriptions.Item label='权益支持退款'>{refund_MAP[dataSourceForm.refund] || '-'}</Descriptions.Item>
            </Descriptions> : null}
          </div>


          <div className={style.titleWrapper}><h4>升级策略</h4><span style={{}}>该升级策略基于基础明细生成，如基础明细配置无此策略，则升级策略默认为无此策略，无法修改</span></div>

          {strategyConfig ? <CreateStrategyCom formObj={form} dataSource={dataSourceForm} strategyConfig={strategyConfig} type='upgrade' {...props} /> : ''}
        </Form>
      </Spin>
    </Modal>
  )
}
export default memo(UpgrdeModal)
