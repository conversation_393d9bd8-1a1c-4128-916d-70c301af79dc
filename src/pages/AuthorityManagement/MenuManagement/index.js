import React from 'react'
import { Tree, message, Row, Col, Button, Input, Form, Space } from 'antd'
import { get, post } from '../../../utils/request'
import allUrl from '../../../utils/url'
import MenuEdit from './Modal/MenuEdit'
import { connect } from 'react-redux'
import { GetMenuList , GetUserInfo} from '../../../actions/async'
import { PlusOutlined, EditFilled, MinusCircleOutlined, CheckOutlined } from '@ant-design/icons';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import './index.less'
import '../index.less'
import {roleJudgment} from '@/utils/authority'
class OrgManagement extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            selectedKeys: ['0001'],
            TabHeight: 0,
            dataSource: [],
            rowData: {},
            pageSize: 10,
            pageIndex: 0,
            total: 0,
            selectedRowKeys: [],
            selectedRows: [],
            EditVisible: false,
            treeData: [],
            treeselectedKeys: [],
            treeSelectedRows: [],
            JurisdictionData: [],
        }
    }
    formRef = React.createRef();
    componentDidMount() {
        this.InitPage()
        this.GetData()
    }
    componentWillUnmount() {
        this.setState = () => {
            return;
        };
    }
    GetData = () => {
        get(allUrl.Authority.MenuTreeList).then(res => {
            if (res.success) {
                let arr = [{title:'根节点',key:0,code:'RootNode',children:res.resp}]
                this.setState({
                    treeData: arr,
                    treeselectedKeys: [],
                    treeSelectedRows: [],
                })
            } else {
                // message.error(res.msg || '系统繁忙请稍后再试！')
            }
        })
    }

    InitPage = () => {
        let WinHeight = document.documentElement.clientHeight;
        this.setState({ TabHeight: WinHeight - 200 });
    }
    PageChange = (current, pageSize) => { this.setState({ pageIndex: current - 1, pageSize }); }

    RowAdd = () => {
        let data = {
            id: 0,
            type: 'Add',
            title:'添加菜单'
        }
        UniversalOpenWindow({
            JumpUrl: '/AuthorityManagement/MenuManagementDetail', data,history:this.props.history
        })
    }
    RowEdit = () => {
        const { treeselectedKeys } = this.state
        if (!treeselectedKeys.length) {
            message.error('请先选择组织！')
            return
        }
        this.setState({ visible: true })
    }
    RowDel = (row) => {
        post(allUrl.common.DelMenu, { MenuID: row.MenuID }).then(res => {
            if (res.code === 200) {
                this.GetChildData(row.ParentID)
                this.props.dispatch(GetMenuList())
            } else {
                // message.error(res.message)
            }
        })
    }
    handleModal = (visible) => {
        this.setState({ visible })
    }
    ModalInfo = (data) => {
    }

    updateTreeData = (list, key, children) => {
        return list.map((node) => {
            if (node.key === key) {
                return { ...node, children };
            }

            if (node.children) {
                return { ...node, children: this.updateTreeData(node.children, key, children) };
            }

            return node;
        });
    }
    treeSelect = (selectedKeys, selectedNodes, event) => {
        this.setState({ treeselectedKeys: selectedKeys, treeSelectedRows: selectedNodes.selectedNodes }, () => {
            let newData = selectedNodes.selectedNodes.length ? selectedNodes.selectedNodes[0].functionChildren : []
            this.formRef.current.setFieldsValue({
                functionAuthoritys: newData
            })
        })
    }
    onFinish = values => {
        let { functionAuthoritys, } = values
        const { treeSelectedRows } = this.state
        functionAuthoritys.forEach(item => {
            item.key = item.key || 0
        })
        let data = {
            functionAuthoritys,
            parent: treeSelectedRows[0].key,
            level: treeSelectedRows[0].level,
        }
        post(allUrl.Authority.updateFunction, data).then(res => {
            if (res.success) {
                message.success(res.msg)
                this.GetData()
                this.props.dispatch(GetMenuList())
                this.props.dispatch(GetUserInfo())
            } else {
                // message.error(res.msg)
            }
        })
    };
    render() {
        const { treeselectedKeys, treeSelectedRows, treeData, visible, title} = this.state
        const {userInfo} = this.props
        return (
            <div className='MenuManagement PublicList'>
                <Row className='queryArea'>
                    {
                        roleJudgment(userInfo,'PERM_MENU_ADD') ?
                        <Col style={{ marginRight: '16px' }}><Button onClick={() => this.RowAdd()} icon={<PlusOutlined />}>添加菜单</Button></Col>
                        :null
                    }
                    {
                        roleJudgment(userInfo,'PERM_MENU_EDIT') ?
                            <Col style={{ marginRight: '16px' }}><Button onClick={() => this.RowEdit()} icon={<EditFilled />} disabled={!treeselectedKeys.length}>编辑菜单</Button></Col>
                        :null
                    }
                </Row>
                <div className='MenuManagement_Con'>
                    <Row className='MenuManagement_ConBox'>
                        <Col span={8} className='MenuManagementLeft'>
                            {
                                treeData.length ? 
                                <Tree
                                    defaultExpandedKeys={[0]}
                                    treeData={treeData}
                                    onSelect={this.treeSelect}
                                />:null
                            }
                        </Col>
                        <Col span={16} className='MenuManagementRight'>
                            <Row className='tableTiele'>功能权限</Row>
                            <Form onFinish={this.onFinish} autoComplete="off" ref={this.formRef} style={{ height: 'calc(100vh - 290px)', overflow: 'auto',paddingRight:'10px' }}>
                                <Form.Item>
                                    {
                                    roleJudgment(userInfo,'PERM_MENU_EDIT') ?
                                        <Button type="primary" htmlType="submit" icon={<CheckOutlined />} disabled={!treeSelectedRows.length}>
                                            保存
                                        </Button>
                                    :null
                                    }
                                </Form.Item>
                                <Form.List name="functionAuthoritys">
                                    {(fields, { add, remove }) => (
                                        <>
                                            {fields.map(({ key, name, fieldKey, ...restField }) => (
                                                <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'title']}
                                                        fieldKey={[fieldKey, 'title']}
                                                        rules={[{ required: true, message: '请输入名称' }]}
                                                        style={{width:'220px'}}
                                                    >
                                                        <Input placeholder="名称" />
                                                    </Form.Item>
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'code']}
                                                        fieldKey={[fieldKey, 'code']}
                                                        rules={[{ required: true, message: '请输入Code' }]}
                                                        style={{width:'300px'}}
                                                    >
                                                        <Input placeholder="Code" />
                                                    </Form.Item>
                                                    <MinusCircleOutlined onClick={() => remove(name)} />
                                                </Space>
                                            ))}
                                            <Form.Item>
                                                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                                                    新增
                                                </Button>
                                            </Form.Item>
                                        </>
                                    )}
                                </Form.List>

                            </Form>
                        </Col>
                    </Row>

                </div>
                {
                    visible &&
                    <MenuEdit
                        title={'编辑菜单'}
                        visible={visible}
                        treeSelectedRows={treeSelectedRows}
                        cb={this.ModalInfo}
                        onCancel={() => this.handleModal(false)}
                        GetData={this.GetData}
                        userInfo={userInfo}
                    />
                }
            </div>
        )
    }
}
export default connect(({ common }) => {
    const { MenuList ,userInfo } = common
    return { MenuList ,userInfo}
})(OrgManagement)