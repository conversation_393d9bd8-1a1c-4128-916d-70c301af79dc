import React,{useState,useEffect} from 'react'
import { Modal, Form ,Row,Col, message,Tooltip,Spin} from 'antd'
import PublicForm from '@/components/Public/PublicForm'
import { get ,post} from '@/utils/request'
import allUrl from '@/utils/url'
import './AddOrEditStore.less'
import {utilsDict} from '@/utils/utilsDict'
import moment from 'moment'

const AddOrEditStore = (props) => {
    const { visible, onCancel,title ,rowData,type,getTableData} = props
    const [form] = Form.useForm();
    const [data,setData] = useState({})
    const[loading,setLoading] = useState(false)
    const formList = [
        {label:'门店编码',name:'dealerCode',type:'Input',disabled:true},
        {label:'门店简称',name:'name',type:'Input',disabled:true},
        {label:'开始建设日期',name:'constructionStartDate',type:'DatePicker',required:false,disabled:false},
        {label:'完工日期',name:'completionDate',type:'DatePicker',required:false,disabled:false},
        {label:'一验日期',name:'firstInspectionDate',type:'DatePicker',required:false,disabled:false},
        {label:'一验合格日期',name:'firstAcceptanceDate',type:'DatePicker',required:false,disabled:false},
        {label:'综合验收日期',name:'comprehensiveInspectionDate',type:'DatePicker',required:false,disabled:false},
        {label:'综合验收合格日期',name:'comprehensiveAcceptanceDate',type:'DatePicker',required:false,disabled:false},
        {label:'是否有监控设备',name:'isMonitoringEquipment',type:'Select',required:false,data:[{name:'是',value:'Y'},{name:'否',value:'N'}],disabled:false},
    ]
    const handleOk = () => {
        form.validateFields().then(values=>{
            console.log(values)
            values.constructionStartDate  = values.constructionStartDate? moment(values.constructionStartDate).format('YYYY-MM-DD') :''
            values.completionDate  = values.completionDate? moment(values.completionDate).format('YYYY-MM-DD') :''
            values.firstInspectionDate  = values.firstInspectionDate? moment(values.firstInspectionDate).format('YYYY-MM-DD') :''
            values.firstAcceptanceDate  = values.firstAcceptanceDate? moment(values.firstAcceptanceDate).format('YYYY-MM-DD') :''
            values.comprehensiveInspectionDate  = values.comprehensiveInspectionDate? moment(values.comprehensiveInspectionDate).format('YYYY-MM-DD') :''
            values.comprehensiveAcceptanceDate  = values.comprehensiveAcceptanceDate? moment(values.comprehensiveAcceptanceDate).format('YYYY-MM-DD') :''
            delete values.name
            setLoading(true)
            post(allUrl.StoreManage.updateOrganizationInfo,{...values}).then(res=>{
                setLoading(false)
                if(res?.success){
                    message.success(res.msg)
                    onCancel()
                    getTableData()
                }
            })
        })
    }
    useEffect(()=>{
        setLoading(true)
        get(allUrl.StoreManage.detail + '/' + rowData.id).then(res=>{
            if(res.success){
                let Dt = res.resp[0]
                form.setFieldsValue({
                    dealerCode:Dt.dealerCode,
                    name:rowData.name,
                    constructionStartDate:rowData.constructionStartDate ? moment(rowData.constructionStartDate) :null,
                    completionDate:rowData.completionDate ? moment(rowData.completionDate) :null,
                    firstInspectionDate:rowData.firstInspectionDate ? moment(rowData.firstInspectionDate) :null,
                    firstAcceptanceDate:rowData.firstAcceptanceDate ? moment(rowData.firstAcceptanceDate) :null,
                    comprehensiveInspectionDate:rowData.comprehensiveInspectionDate ? moment(rowData.comprehensiveInspectionDate) :null,
                    comprehensiveAcceptanceDate:rowData.comprehensiveAcceptanceDate ? moment(rowData.comprehensiveAcceptanceDate) :null,
                    isMonitoringEquipment:rowData.isMonitoringEquipment ? rowData.isMonitoringEquipment :null,
                })
                setData(Dt)
            }else{
                // message.error(res.msg)
            }
            setLoading(false)
        })
    },[])
    const formItemLayout1 = {
        labelCol: {
            span: 24,
        },
        wrapperCol: {
            span: 15,
        },
    }
    const formItemLayout2 = {
        labelCol: {
            span: 8,
        },
        wrapperCol: {
            span: 15,
        },
    }
    const ModalProps = {
        width:1200,
        wrapClassName:'AddOrEditStore',
        title:title,
        visible,
        onOk:handleOk,
        onCancel,
        maskClosable:false,
    }

    if(type === 'LookAt') ModalProps.footer = null
    return <Modal {...ModalProps} confirmLoading={loading}>
        {
            type !=='LookAt' ?
            <Spin spinning={loading}>
                <PublicForm formItemLayout={formItemLayout1} formList={formList} Form={Form} form={form} />
            </Spin>
            :<Form {...formItemLayout2}>
                <Spin spinning={loading}>
                    <Row>
                        <Col span={8}><Form.Item label='门店编码'>{data?data.dealerCode || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='门店简称'>{data?data.name || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='门店名称'>{data?data.name || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='所属大区'>{data?data.bigAreaName || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='所属区域'>{data?data.middleAreaName || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='所属省'>{data?data.provinceName || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='所属市'>{data?data.displayName || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='所属区'>{data?data.districtName || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='地址'><Tooltip title={data?data.address || '-' :'-'}>{data?data.address || '-' :'-'}</Tooltip></Form.Item></Col>
                        <Col span={8}><Form.Item label='经度'>{data?data.lat || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='纬度'>{data?data.lng || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='所属母公司'>{data?data.parentCompany || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='是否母公司'>{data?data.isG ? '是':'否'  || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='门店类型'>{data?utilsDict('dealerType',data.dealerType) || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='渠道类型'>{data?utilsDict('channel',data.channel) || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='合作类型'>{data?utilsDict('dealerisDirect',data.dealerisDirect) || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='门店状态'>{data?utilsDict('dealerStates',data.dealerStates) || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='营业状态'>{data?utilsDict('businessStatus',data.businessStatus) || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='是否可销售'>{data?data.isSales?'是':'否' || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='是否有展车'>{data?data.exhibitionCars?'是':'否' || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='是否可试驾'>{data?data.ifTestDrive?'是':'否' || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='是否可交付'>{data?data.isDeliverables?'是':'否' || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='是否可服务'>{data?data.isService?'是':'否' || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='创建时间'>{data?data.createTime?moment(data.createTime).format('YYYY-MM-DD') :'-' || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='是否对外展示'>{data?data.isExternalDisplay?'是':'否' || '-' :'-'}</Form.Item></Col>
                        {/* <Col span={8}><Form.Item label='服务热线'>{data?data.phone || '-' :'-'}</Form.Item></Col> */}
                        {/* <Col span={8}><Form.Item label='销售热线'>{data?data.phone || '-' :'-'}</Form.Item></Col> */}
                        <Col span={8}><Form.Item label='开户账号'>{data?data.bankAccount || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='开户银行'>{data?data.bankName || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='银行户主'>{data?data.accountOwner || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='开票名称'>{data?data.invoiceName || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='开票地址'>{data?data.invoiceAddress || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='税号'>{data?data.taxNumber || '-' :'-'}</Form.Item></Col>
                        {/* <Col span={8}><Form.Item label='负责人'>{data?data.generalManager || '-' :'-'}</Form.Item></Col> */}
                        {/* <Col span={8}><Form.Item label='类型'>{data?data.phone || '-' :'-'}</Form.Item></Col> */}
                        {/* <Col span={8}><Form.Item label='申请等级'>{data?data.phone || '-' :'-'}</Form.Item></Col> */}
                        {/* <Col span={8}><Form.Item label='认定等级'>{data?data.phone || '-' :'-'}</Form.Item></Col> */}
                        <Col span={8}><Form.Item label='开始建设日期'>{data?data.constructionStartDate || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='完工日期'>{data?data.completionDate || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='一验日期'>{data?data.firstInspectionDate || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='一验合格日期'>{data?data.firstAcceptanceDate || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='综合验收日期'>{data?data.comprehensiveInspectionDate || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='综合验收合格日期'>{data?data.comprehensiveAcceptanceDate || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='监控设备'>{data?data.isMonitoringEquipment === 'Y' ?'有':'无' || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='总经理'>{data?data.generalManager || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='电话'>{data?data.phone || '-' :'-'}</Form.Item></Col>
                        <Col span={8}><Form.Item label='门店备案邮箱'>{data?data.email || '-' :'-'}</Form.Item></Col>
                    </Row>
                </Spin>
            </Form>
        }
    </Modal>


}
export default AddOrEditStore