import React, { useState, memo, useEffect, useRef, useMemo } from 'react'
import { Modal, Form, Row, Col, Select, Input, message } from 'antd'
import { post } from '../../../utils/request';
import allUrl from '../../../utils/url';
const AddCodeInfo = (props) => {
    const { handleCancel, visible, reloadList, flag, BUSINESS_CODE_TYPE } = props
    const [form] = Form.useForm()
    const formRef = useRef(null)
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [DataDetailNameObj, setDataDetailNameObj] = useState({})
    const [businessUseComment, setBusinessUseComments] = useState('')
    const dataSourceForm = useMemo(() => props.codeInfoSource, [props.codeInfoSource])
    const onChangeCommentsStatus = () => {
        const { businessUseComment } = form.getFieldsValue()
        if (businessUseComment) {
            setBusinessUseComments('')
            return true
        } else {
            setBusinessUseComments('error')
            return false
        }
    }
    const handleOk = () => {
        onChangeCommentsStatus()
        form.validateFields().then(values => {
            if (!onChangeCommentsStatus()) return
            setConfirmLoading(true)
            if (flag === 'add') {
                createBussinessCode(values);
            } else {
                editBussinessCode(values);
            }
        })
    }

    // 创建业务编码
    const createBussinessCode = (values) => {
        post(allUrl.BusinessCodeMaintain.saveBusinessCode, values).then(res => {
            setConfirmLoading(false)
            if (res.success) {
                handleCancel()
                reloadList()
                return message.success('保存成功！')
            }
        })
    }

    //编辑业务编码
    const editBussinessCode = (values) => {
        const params = {
            dictEntryId: dataSourceForm?.dictEntryId,
            id: dataSourceForm?.id,
            businessCode: dataSourceForm?.businessCode,
            ...values
        }
        post(allUrl.BusinessCodeMaintain.updateBusinessCode, { ...params }).then(res => {
            setConfirmLoading(false)
            if (res.success) {
                message.success('更新成功！')
                handleCancel()
                reloadList()
            }
        })
    }


    const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 15 },
    };


    useEffect(() => {
        if (dataSourceForm) {
            formRef.current && form.setFieldsValue({
                ...dataSourceForm
            })
        }
    }, [])

    return (
        <Modal open={visible} onOk={handleOk} okText={'保存'} onCancel={handleCancel} maskClosable={false} confirmLoading={confirmLoading} width='650px' title='业务编码信息'>
            <Form name='change-codeInfo' ref={formRef} form={form} {...layout} >
                <Row>
                    {/* <Col span={24}>
                        <Form.Item label='业务编码编号' rules={[{ required: true, message: '请输入业务编码编号!' }]} name='businessCode'>
                            <Input allowClear placeholder='请输入业务编码编号' disabled />
                        </Form.Item>
                    </Col> */}
                    <Col span={24}>
                        <Form.Item label='业务编码名称' rules={[{ required: true, message: '请输入业务编码名称!' }]} name='businessCodeName'>
                            <Input allowClear placeholder='请输入业务编码名称' />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='业务编码分类' rules={[{ required: true, message: '请选择业务编码分类!' }]} name='businessCodeType'>
                            <Select
                                placeholder='请选择业务编码分类'
                                fieldNames={{ label: 'name', value: 'value' }}
                                options={BUSINESS_CODE_TYPE}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='业务应用' validateStatus={businessUseComment} required={true} onChange={onChangeCommentsStatus} name='businessUseComment'>
                            <Input allowClear maxLength={50} placeholder='请填写该业务编码应用领域，建议字数在20字以内' />
                        </Form.Item>
                        <p style={{ color: 'red', fontSize: '12px', position: 'absolute', top: '38px', left: '151px' }}>不支持其它表情和特殊符号，如配置则无法正常展示</p>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}
export default memo(AddCodeInfo)