import { Spin, SpinProps } from 'antd';
import React, { FC } from 'react';

export interface PageLoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  spin?: SpinProps;
}

const PageLoading: FC<PageLoadingProps> = ({ spin, style, ...props }) => {
  return (
    <div
      style={{
        paddingTop: 100,
        textAlign: 'center',
        ...style,
      }}
      {...props}
    >
      <Spin size="large" spinning {...spin} />
    </div>
  );
};

export default PageLoading;
