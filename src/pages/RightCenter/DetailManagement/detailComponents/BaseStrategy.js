import React, { useState } from 'react'
import { Descriptions, Tag, Button } from 'antd'
import moment from 'moment'
import { utilsDict } from '@/utils/utilsDict'
import LadderPartDetail from '../maintenanceLadder/LadderPartDetail'
import { strategyConfig, outsideStrategyConfig } from '../formConfig';

const deductionFrame_MaP = {
    0: '-',
    1: '具体金额'
}

const DetailInfo = (props) => {
    const { detail, styles } = props
    const [ladderPartDetailVisible, setLadderPartDetailVisible] = useState(false)
    const [ladderFormConfig, setLadderFormConfig] = useState(null)

    //阶梯保养弹窗相关---start
    const handleLadderVisible = () => {
        setLadderPartDetailVisible(!ladderPartDetailVisible)
    }

    const handleLaddderDetail = (data) => {
        handleLadderVisible()
        setLadderFormConfig(data)
    }
    //阶梯保养弹窗相关---end
    /**
     * 计算权益限制里程
     * @param {*} limitLegendFrame 权益限制里程（基准）下拉选的值
     * @param {*} limitLegend 行驶里程输入框的值
     * @param {*} extenderMileage 增程器里程输入框的值
     * @returns DOM 
     */
    const computedMileage = ({ limitLegendFrame, limitLegend, extenderMileage }) => {
        const _mileageFrame = {
            //无此策略
            0: () => '-',
            //行驶里程
            1: () => {
                return <>
                    {limitLegend}公里
                    <Tag className={styles['custom-ant-tag-green']} style={{ marginLeft: 5 }}>行驶</Tag>
                </>
            },
            //增程器里程
            2: () => {
                return <>
                    {extenderMileage}公里
                    <Tag className={styles['custom-ant-tag-yellow']} style={{ marginLeft: 5 }}>增程器</Tag>
                </>
            },
            null: () => '-'
        }
        return _mileageFrame[limitLegendFrame]?.()
    }

    return (

        <div>
            {detail.consumptionStrategy == 2 ?
                <Descriptions title="对外展示策略" className='strategy-box'>
                    <Descriptions.Item label='权益限制年限'>
                        {detail.outsideLimitTimeFrame == 0 && <span>-</span>}
                        {detail.outsideLimitTimeFrame == -1 && <span>永久有效</span>}
                        {detail.outsideLimitTimeFrame == 1 && <span>{`${Number(detail.outsideLimitYear)}年${Number(detail.outsideLimitMonth)}月${Number(detail.outsideLimitDay)}日`}</span>}
                    </Descriptions.Item>
                    <Descriptions.Item label='权益生效时间'><span>
                        {
                            detail.outsideRelativeTime ?
                                detail.outsideRelativeTimeName
                                : <>
                                    {detail.outsideFixedBeginTime ? moment(detail.outsideFixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') : ''} ~ {detail.outsideFixedEndTime ? moment(detail.outsideFixedEndTime).format('YYYY年MM月DD日 HH:mm:ss') : ''}
                                </>
                        }
                    </span></Descriptions.Item>
                    <Descriptions.Item label='权益限制里程(基准)'>
                        {
                            computedMileage({
                                limitLegendFrame: detail.outsideLimitLegendFrame,
                                limitLegend: detail.outsideLimitLegend,
                                extenderMileage: detail.outsideExtenderMileage
                            })
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label='权益生效里程'>
                        {detail.outsideEffectMileageFrame === 0 && <span>-</span>}
                        {/* {detail.outsideEffectMileageFrame === 1 && <span>{detail.outsideFixedBeginMileage}公里 ~ {detail.outsideFixedEndMileage}公里</span>} */}
                        {detail.outsideEffectMileageFrame === 1 && <span>
                            <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detail.outsideFixedBeginMileage}公里
                            <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detail.outsideFixedEndMileageStrategy)}</span>}
                        {detail.outsideEffectMileageFrame === 2 && <span>
                            <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detail.outsideRelativeBeginMileageStrategyName}
                            <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detail.outsideRelativeEndMileageStrategy)}</span>}
                    </Descriptions.Item>
                    <Descriptions.Item label='享有权益频次'>{detail.frequency ?
                        <span>{detail.outsideFrequency && detail.outsideFrequency !== -1 ? detail.outsideFrequency + ' 次' : '不限频次'}</span> : <><span>-</span></>
                    }
                    </Descriptions.Item>

                    <Descriptions.Item label='车辆权益变更属性'>{detail.outsideIdentityName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='享有车主身份'>{detail.outsideCarIdentityName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='权益所属'>{detail.outsideGoodsTypeName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='全保养阶梯策略'>
                        {
                            detail.outsideMaintenanceLadderFrame == 0 ?
                                '-' :
                                // <>{detail.outsideMaintenanceLadder}次<Button type='link' size='small' onClick={() => handleLaddderDetail(outsideStrategyConfig)}>详情</Button></>
                                <>{detail.outsideMaintenanceLadder}次</>
                        }
                    </Descriptions.Item>

                    <Descriptions.Item label='补漆面部位'>
                        {
                            detail.outsidePaintFrame === -1 ?
                                '不限面数'
                                : detail.outsidePaintListDisplay || '-'
                        }
                        {/* <span>{detail.outsidePaintListDisplay || '-'}</span> */}
                    </Descriptions.Item>
                    <Descriptions.Item label='车联网流量'>
                        {detail.outsideTraffic ? <span>{
                            detail.outsideTraffic !== -1 && detail.outsideTrafficUnit ?
                                <span>{detail.outsideTraffic}  {detail.outsideTrafficUnit}</span>
                                : '不限流量'
                        }</span> : <><span>-</span></>}
                    </Descriptions.Item>
                    <Descriptions.Item label='权益金额抵扣'>
                        {detail.outsideDeductionFrame == 0 && <span>{deductionFrame_MaP[detail.outsideDeductionFrame] || '-'}</span>}
                        {detail.outsideDeductionFrame == 1 && <span>{`${detail.outsidePayAmount}-${detail.outsideDeduction}`}</span>}
                    </Descriptions.Item>
                </Descriptions>
                : null}

            <Descriptions title="对内核销策略" className='strategy-box'>
                <Descriptions.Item label='权益限制年限'>
                    {detail.limitTimeFrame == 0 && <span>-</span>}
                    {detail.limitTimeFrame == -1 && <span>永久有效</span>}
                    {detail.limitTimeFrame == 1 && <span>{`${Number(detail.limitYear)}年${Number(detail.limitMonth)}月${Number(detail.limitDay)}日`}</span>}
                </Descriptions.Item>
                <Descriptions.Item label='权益生效时间'><span>
                    {
                        detail.relativeTime ?
                            detail.relativeTimeName
                            : <>
                                {detail.fixedBeginTime ? moment(detail.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') : ''} ~ {detail.fixedEndTime ? moment(detail.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss') : ''}
                            </>
                    }
                </span></Descriptions.Item>
                <Descriptions.Item label='权益限制里程(基准)'>
                    {
                        computedMileage({
                            limitLegendFrame: detail.limitLegendFrame,
                            limitLegend: detail.limitLegend,
                            extenderMileage: detail.extenderMileage
                        })
                    }
                </Descriptions.Item>
                <Descriptions.Item label='权益生效里程'>
                    {detail.effectMileageFrame === 0 && <span>-</span>}
                    {detail.effectMileageFrame === 1 && <span>
                        <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detail.fixedBeginMileage}公里
                        <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detail.fixedEndMileageStrategy)}</span>}
                    {detail.effectMileageFrame === 2 && <span>
                        <Tag className={styles['custom-ant-tag-blue']}>开始里程</Tag>{detail.relativeBeginMileageStrategyName}
                        <Tag className={styles['custom-ant-tag-blue']} style={{ marginLeft: 10 }}>结束里程</Tag>{utilsDict('endMileageMap', detail.relativeEndMileageStrategy)}</span>}
                </Descriptions.Item>
                <Descriptions.Item label='享有权益频次'>{detail.frequency ?
                    <span>{detail.frequency && detail.frequency !== -1 ? detail.frequency + ' 次' : '不限频次'}</span> : <><span>-</span></>
                }
                </Descriptions.Item>

                <Descriptions.Item label='车辆权益变更属性'>{detail.identityName || '-'}</Descriptions.Item>
                <Descriptions.Item label='享有车主身份'>{detail.carIdentityName || '-'}</Descriptions.Item>
                <Descriptions.Item label='权益所属'>{detail.goodsTypeName || '-'}</Descriptions.Item>
                <Descriptions.Item label='全保养阶梯策略'>
                    {
                        detail.maintenanceLadderFrame == 0 ?
                            '-' :
                            // <>{detail.maintenanceLadder}次<Button type='link' size='small' onClick={() => handleLaddderDetail(strategyConfig)}>详情</Button></>
                            <>{detail.maintenanceLadder}次</>
                    }
                </Descriptions.Item>

                <Descriptions.Item label='补漆面部位'>
                    {
                        detail.paintFrame === -1 ?
                            '不限面数'
                            : detail.paintListDisplay || '-'
                    }
                    {/* <span>{detail.paintListDisplay || '-'}</span> */}
                </Descriptions.Item>
                <Descriptions.Item label='车联网流量'>
                    {detail.traffic ? <span>{
                        detail.traffic !== -1 && detail.trafficUnit ?
                            <span>{detail.traffic}  {detail.trafficUnit}</span>
                            : '不限流量'
                    }</span> : <><span>-</span></>}
                </Descriptions.Item>

                <Descriptions.Item label='权益金额抵扣'>
                    {detail.deductionFrame == 0 && <span>{deductionFrame_MaP[detail.deductionFrame] || '-'}</span>}
                    {detail.deductionFrame == 1 && <span>{`${detail.payAmount}-${detail.deduction}`}</span>}
                </Descriptions.Item>
            </Descriptions>

            {
                ladderPartDetailVisible &&
                <LadderPartDetail
                    handleLadderVisible={handleLadderVisible}
                    ladderPartDetailVisible={ladderPartDetailVisible}
                    data={{
                        ...detail,
                        rTimeName: detail.relativeTimeName,
                        rBeginMileagegName: detail[ladderFormConfig['effectMileageFrame'].c.relativeBeginMileageStrategyName.value]
                    }}
                    strategyConfig={ladderFormConfig}
                />
            }
        </div>
    )
}

export default DetailInfo