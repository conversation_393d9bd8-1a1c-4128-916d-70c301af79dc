/**
 * 下载文件
 * @param blob  文件的二进制数据
 * @param filename  文件名
 */
export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
}

export enum FileType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  PDF = 'pdf',
  WORD = 'word',
  EXCEL = 'excel',
  PPT = 'ppt',
  TXT = 'txt',
  ZIP = 'zip',
  OTHER = 'other',
}
/**
 * 根据文件名获取文件类型
 */
export function getFileType(filename: string): FileType {
  const ext = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
  if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(ext)) {
    return FileType.IMAGE;
  }
  if (['mp4', 'avi', 'rmvb', 'rm', 'flv', '3gp', 'wmv', 'mkv', 'mov'].includes(ext)) {
    return FileType.VIDEO;
  }
  if (['mp3', 'wav', 'wma', 'ogg', 'ape', 'flac', 'aac'].includes(ext)) {
    return FileType.AUDIO;
  }
  if (['pdf'].includes(ext)) {
    return FileType.PDF;
  }
  if (['doc', 'docx'].includes(ext)) {
    return FileType.WORD;
  }
  if (['xls', 'xlsx'].includes(ext)) {
    return FileType.EXCEL;
  }
  if (['ppt', 'pptx'].includes(ext)) {
    return FileType.PPT;
  }
  if (['txt'].includes(ext)) {
    return FileType.TXT;
  }
  if (['zip', 'rar', '7z'].includes(ext)) {
    return FileType.ZIP;
  }
  return FileType.OTHER;
}
