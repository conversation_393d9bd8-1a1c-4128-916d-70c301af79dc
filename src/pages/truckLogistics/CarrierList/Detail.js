import React, { useEffect, useState, useRef  } from 'react'
import { message, But<PERSON>, Card, Table, Divider, Form, Row, Col, Timeline, Spin,Popconfirm,Modal,Select,Tag,Image} from 'antd'
import history from '@/utils/history'
import { DecryptByAES } from '@/components/Public/Decrypt'
import { get,post } from '@/utils/request'
import allUrl from '@/utils/url'
import { useSelector } from 'react-redux'
import { DownOutlined } from '@ant-design/icons';
import {roleJudgment} from '@/utils/authority'
import {trim} from '@/utils'
import _ from "lodash";
import Cookies from 'js-cookie'
import '../index.less'
import './Detail.less'
const {Option} = Select
const CarrierDetail = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [id, setId] = useState(null)
    const [locationParmas, setLocationParmas] = useState({})
    const [Type, setType] = useState('')
    const [dataSource, setDataSource] = useState([])
    const [dataSource3, setDataSource3] = useState([])
    const [formData, setFormData] = useState({})
    const [clockList, setClockList] = useState([])
    const [isExpand, setIsExpand] = useState(false)
    const [loading, setLoading] = useState(false)
    const [title,setTitle]=useState({})
    const [operationVisible1,setoperationVisible1] = useState(false)
    const [operationVisible2,setoperationVisible2] = useState(false)
    const [operationVisible3,setoperationVisible3] = useState(false)
    const [driverOptions,setdriverOptions] = useState([])
    const [carOptions,setcarOptions] = useState([])
    const [recordObj,setRecordObj] = useState({})
    const [driverName,setDriveName] = useState('')
    const [driverPhone,setdriverPhone] = useState('')
    const [toTrailerLicensePlate,settoTrailerLicensePlate] = useState('')
    const [turnform] = Form.useForm();
    const turnformRef = useRef(null);
    const [imageVisibleArr, setImageVisibleArr] = useState([]) 
    const [imageUrlArr, setImageUrlArr] = useState([]) 
    const [phoneVisible, setPhoneVisible] = useState(false)
    const [pePhone, setPePhone] = useState('')

    const onCancel = () => {
        history.push('/truckLogistics/CarrierList')
    }
     // 获取资源
    /**
     * 
     * @param {*} url 后台返回的请求地址
     * @param {*} fn 回调函数
     */
    const getOssBuffer = (url,fn) => {
        var xhr = new XMLHttpRequest();
        xhr.responseType = "blob";
        xhr.open('get',url,true);
        xhr.setRequestHeader("x-oss-meta-authorization", Cookies.get('scrm_token'));
        xhr.setRequestHeader("authorization", Cookies.get('scrm_token'));
        xhr.onreadystatechange = function(){
            if(xhr.readyState == 4 && xhr.status == 200){
            console.log('xhr', xhr)
            fn(xhr.response)
            } else {
                return false;
            }
        };
        xhr.send();

    
    }
    const getData = (params) => {
        let query = { id: params.id }
        setLoading(true)
        get(allUrl.truckLogistics.getCarriageObj, { ...query }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt.driverCheckUrls = Dt.driverCheckUrls || []
                setFormData(Dt)
                setDataSource(Dt.carList)
                setClockList(Dt.clockList?Dt.clockList:[])
                if (Dt.clockList && Dt.clockList.length >= 5) {
                    setIsExpand(true)
                } else {
                    setIsExpand(false)
                }
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }
    const openModal = (type,record) => {
        setRecordObj(record)
        if(type == 1){
            // 转派
            setoperationVisible1(true)
            setTitle('转派给')
        }
        if (type == 2){
            // 打卡记录
            setoperationVisible2(true)
             setTitle('打卡记录')
        }
        if(type == 3){
            //转派日志
            setoperationVisible3(true)
            post(allUrl.truckLogistics.getTransferLog,{vin:record.vin,carriageNo:formData.carriageNo}).then(res => {
                if (res.success) {
                let Dt = res.resp[0]
                setDataSource3(Dt)
                } else {
                    // message.error(res.msg)
                }
            })
            setTitle('转派日志')
        }
    }
    const renderOperation = (text, record) => {
        // 详情页操作按钮
        return <div style={{ color: '#1890ff' }}>
        {
            roleJudgment(userInfo,'TRUCK_LOGISTICS_TRANSFER') && record.carriageStatus == 1 || roleJudgment(userInfo,'TRUCK_LOGISTICS_TRANSFER') && record.carriageStatus == 0 ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openModal(1,record)}>转派</span>,
                    <Divider key={2} type="vertical" />
                ] : null
        }
        {
            roleJudgment(userInfo,'TRUCK_LOGISTICS_CLOCKRERECORD') ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openModal(2,record)}>打卡记录</span>,
                    <Divider key={2} type="vertical" />
                ] : null
        }
        {
            roleJudgment(userInfo,'TRUCK_LOGISTICS_TRANRERECORD') ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openModal(3,record)}>转派日志</span>,
                ] : null
        }
    </div>
    }
    const renderOperationPhone = (text, record) => {
        // 详情页操作按钮
        return <div style={{ color: '#1890ff' }}>
        {
            roleJudgment(userInfo,'TRUCK_LOGISTICS_DETAIL_PHONE') ?
                [
                    <span key={1} style={{ cursor: 'pointer' }} onClick={() => openPhoneModal(record)}>查看手机号</span>,
                ] : null
        }
    </div>
    }
    const openPhoneModal = record => {
        post(allUrl.truckLogistics.carriageManagePeShow,{pe:record.pe}).then(res => {
            if (res.success) {
                // settransferClockList(res.resp[0])
                setPePhone(res.resp[0])
                console.log(res.resp[0])
                setPhoneVisible(true)
            } else {
                message.error(res.msg || '获取手机号码失败')
            }
        }) 
    }
    //转派搜索
    const  onSearch1 =_.debounce((value)=>{
        value =trim(value)
        get(allUrl.truckLogistics.searchDriver, {keywords:value}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setdriverOptions(Dt)
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })

    }, 500);
    const onChange1 =(value,key)=>{
     setDriveName(driverOptions[key.key].driverName)
     setdriverPhone(value)
    }
    const  onSearch2 =_.debounce((value)=>{
        value =trim(value)
        get(allUrl.truckLogistics.searchTrailer, {keywords:value}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                setcarOptions(Dt)
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })

    }, 500);
    const onChange2 =(value)=>{
        settoTrailerLicensePlate(value)
    }
    const handleOk1 = () => {
        // 确定转派
        turnform.validateFields().then(values=>{
            
            let params ={
                carriageNo:formData.carriageNo ||'',
                vin:recordObj.vin,
                toDriverName:driverName || '',
                toDriverPhone:driverPhone || '',
                toTrailerLicensePlate:values.toTrailerLicensePlate || '',
            }
            post(allUrl.truckLogistics.transfer,{...params}).then(res => {
                if (res.success) {
                    getData(JSON.parse(DecryptByAES(props.match.params.data)) || {})
                    setoperationVisible1(false)
                    message.success(res.msg || '转派成功')
                } else {
                    message.error(res.msg || '转派失败')
                }
            })
        })
    }
    const handleOk2= () => {
        setoperationVisible2(false)
    }
    const handleOk3 = () => {
        setoperationVisible3(false)
    }
    // 获取解密图片
    const getImage = (str,index) => {

        get(allUrl.truckLogistics.getOSSurl,{id: formData.id, index}).then(res => {
            if (res.success && res.resp[0]) {
                getOssBuffer(res.resp[0].url,(data) => {
                    // let blob = new Blob([data],{type: data.type});
                    console.log('图片', data)
                   
                    const updatedImageUrlArr = [...imageUrlArr]; 
                    const updatedImageVisibleArr = [...imageVisibleArr]; 
                    updatedImageUrlArr[index] = window.URL.createObjectURL(data)
                    updatedImageVisibleArr[index] = true;
                    setImageUrlArr(updatedImageUrlArr);
                    setImageVisibleArr(updatedImageVisibleArr)
                  })
            } 
        })
    }
    useEffect(() => {
        const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {}
        setLocationParmas(locationParmas)
        setId(locationParmas.id)
        setType(locationParmas.Type)
        console.log(locationParmas)
        if (locationParmas.id && userInfo) {
            getData(locationParmas)
        } else {


        }
    }, [userInfo])

    const InforData = {
        rowKey: record => record.key,
        bordered: true,
        dataSource,
        scroll: { x: 'max-content' },
        columns: [
            { title: '序号', dataIndex: 'serialNumber', width: 80, render: (text, record, index) => index + 1 },
            { title: '车架号(VIN)', dataIndex: 'vin', width: 180 },
            // { title: '子运单编号', dataIndex: 'childCarriageNo', width: 180 },
            { title: '承运司机', dataIndex: 'driverName', width: 100 },
            { title: '承运司机手机号', dataIndex: 'driverPhone', width: 200, render: (text, record) => renderOperationPhone(text, record) },
            { title: '承运板车', dataIndex: 'licensePlate', width: 180 },
            { title: '是否被转派', dataIndex: 'transferFlag', width: 120,render:text=>text==1 ? '是':'否'},
            { title: '物流状态', dataIndex: 'carriageStatusDesc', width: 100},
            { title: '车辆配置信息', dataIndex: 'description', width: 300 },
            { title: '验收结果', dataIndex: 'checkResult', width: 120 },
            { title: '验收人', dataIndex: 'checkUserName', width: 120 },
            { title: '验收时间', dataIndex: 'checkTime', width: 300 },
            // { title: '操作', width: 220, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation(text, record) }
        ],
        pagination: false,
        rowSelection: null,
    };
    const InforData3 = {
        rowKey: record => record.key,
        bordered: true,
        dataSource:dataSource3,
        scroll: { x: 'max-content' },
        columns: [
            { title: '时间', dataIndex: 'transferTime', width: 180},
            { title: '司机姓名', dataIndex: 'driverName', width: 100 },
            { title: '手机号', dataIndex: 'driverPhone', width: 140 },
            { title: '物流服务商', dataIndex: 'driverLogisticsProviderName', width: 150 },
            { title: '承运板车', dataIndex: 'trailerLicensePlate', width: 120 },
            { title: '操作者', dataIndex: 'transferByName', width: 100},
            { title: '操作者ID', dataIndex: 'transferBy', width: 100 },
            { title: '操作者所属组织', dataIndex: 'transferByOrg', width: 180 }
        ],
        pagination: false,
        rowSelection: null,
    };
    return <div className='PublicDetail CarrierDetail'>
        <div className='DetailBox'>
            <div className='DetailTitle'>{locationParmas.title}</div>
            <div className='DetailCon'>
                <div className='CarrierDetail_Box'>
                    <Card bordered={false}>
                        <div className='CarrierDetail_Con'>
                            <Spin spinning={loading}>
                                <Form>
                                    <div className='CarrierDetail_Info_Title'>承运信息</div>
                                    <div className='CarrierDetail_Info_Con'>
                                            <Form.Item className='CarrierDetail_Info_Con_text1' label='承运单编号'>{formData.carriageNo ? formData.carriageNo : '-'}</Form.Item>
                                            <div style={{display:'flex'}}>
                                            <Form.Item className='CarrierDetail_Info_Con_text2' style={{marginRight:'30px'}} label='订单类型'>{formData.carriageType  ? (formData.carriageType == 1 ? '正常' : '中转库') : '-'}</Form.Item>
                                            <Form.Item className='CarrierDetail_Info_Con_text2' label='承运台数'>{formData.carriageNum ? formData.carriageNum : '-'}</Form.Item>
                                            </div>
                                            <div>
                                                <Timeline>
                                                    <Timeline.Item key={1} color={'blue'}>
                                                    <Form.Item className='CarrierDetail_Info_Con_title1' label='出发时间'>{formData.beginTime ? formData.beginTime : '-'}</Form.Item>
                                                    <Form.Item className='CarrierDetail_Info_Con_title1' label='出发地'>{formData.warehouseAddress ? formData.warehouseAddress : '-'}</Form.Item>
                                                        <div style={{display:'flex'}}>
                                                            <Form.Item className='CarrierDetail_Info_Con_text2'style={{marginRight:'30px'}} label='出库名称'>{formData.warehouseName ? formData.warehouseName : '-'}</Form.Item>
                                                            <Form.Item className='CarrierDetail_Info_Con_text2' label='仓库编号'>{formData.warehouseCode ? formData.warehouseCode : '-'}</Form.Item>
                                                        </div>
                                                    </Timeline.Item>
                                                    <Timeline.Item key={2} color={'blue'}>
                                                    <Form.Item className='CarrierDetail_Info_Con_title1' label='到达时间'>{formData.endTime ? formData.endTime : '-'}</Form.Item>
                                                    <Form.Item className='CarrierDetail_Info_Con_title1' label='目的地'>{formData.dealerReceiveAddress ? formData.dealerReceiveAddress : '-'}</Form.Item>
                                                        <div style={{display:'flex'}}>
                                                           <Form.Item className='CarrierDetail_Info_Con_text2'style={{marginRight:'30px'}} label={formData.carriageType == 1 ? '目的地门店' : '入库名称'}>{formData.dealerName  ? formData.dealerName : '-'}</Form.Item>
                                                           <Form.Item className='CarrierDetail_Info_Con_text2' label={formData.carriageType == 1 ? '门店编号' : '仓库编号'}>{formData.dealerCode ? formData.dealerCode : '-'}</Form.Item>
                                                       </div>
                                                    <Form.Item className='rDetail_Info_Con_title1' label='司机附件'>
                                                    <div style={{display:'flex'}}>
                                                        {

                                                            formData.driverCheckUrls && formData.driverCheckUrls.length ? formData.driverCheckUrls.map((item, index) => {
                                                               return (
                                                                  <>
                                                                     <div><Button type='link' onClick={()=>getImage(item,index)} key={index+1} >查看图片{index+1}</Button></div>
                                                                     <Image
                                                                        visible={imageVisibleArr[index]}
                                                                        width={100}
                                                                        src={imageUrlArr[index]}
                                                                    />
                                                                  </>
                                                               )
                                                            }) : '暂无'
                                                        }
                                                    </div>
                                                    </Form.Item>
                                                    </Timeline.Item>
                                                </Timeline>
                                           </div>
                                
                                    </div>
                                    <Divider />
                                    <div className='CarrierDetail_Info_Title'>车辆信息</div>
                                    <Table {...InforData} />
                                    {/* <div className='CarrierDetail_Info_Title' style={{ margin: '32px 0 20px 0' }}>位置打卡记录</div>
                                    <div className='PunchInRecord'>
                                        <Timeline>
                                            {
                                                clockList.map((item, index) => {
                                                    return (isExpand ? index < 5 : index >= 0)
                                                        && <Timeline.Item key={index} color={index === 0 ? 'blue' : 'gray'}>
                                                            <div className='TimelineItem_Con'>
                                                                <span className='time' style={{marginRight:'20px'}}>{item.time}</span>
                                                                <span className='statusCn'>
                                                                    { item.status =='1'?
                                                                    <Tag color="gold">{item.statusCn}</Tag> : 
                                                                    [item.status =='2'?
                                                                    <Tag color="blue">{item.statusCn}</Tag> :
                                                                    <Tag color="green">{item.statusCn}</Tag>
                                                                    ]
                                                                    }
                                                                </span>
                                                                <p className='address'>位置：{item.address}</p>
                                                                <p className='address'>承运板车：{formData.carrierPallet}</p>
                                                                <p className='address'>打卡人：{item.recorder}</p>
                                                            </div>
                                                        </Timeline.Item>
                                                })
                                            }
                                        </Timeline>
                                            <p className='more'>
                                                {isExpand ?
                                                    <span onClick={() => {
                                                        setIsExpand(false)
                                                    }}>点击查看更多<DownOutlined /></span>
                                                    : <span>没有更多打卡记录啦～</span>
                                                }
                                            </p>
                                    </div> */}
                                </Form>
                            </Spin>
                        </div>
                    </Card>
                </div>
            </div>
        </div>
        <div className='DetailBtns'>
            <Button onClick={onCancel}>取消</Button>
        </div>
        
        {
            operationVisible1 && 
            <Modal  width={520}
            footer={[
                        <Button onClick={()=>setoperationVisible1(false)} key={1}>取消</Button>,
                        <Popconfirm key={2} onConfirm={handleOk1} className='popconIcon' title={
                        <>
                        <h3 style={{marginBottom:'10px'}}>转派确认</h3>
                        <div style={{marginBottom:'10px'}}>是否确认将车辆{recordObj.vin}转给</div>
                        <div style={{marginBottom:'10px'}}>司机姓名：{driverName || ''}     ({driverPhone || ''})</div>
                        <div>板车车牌号：{toTrailerLicensePlate || ''}</div>
                        </>
                    } okText='确定' cancelText='取消'>
                        <Button type="primary">确定</Button>
                        </Popconfirm>
                    ]}
            visible={operationVisible1} title={title} recordObj={recordObj} onOk={handleOk1}  onCancel={()=>setoperationVisible1(false)}>
                <Form  form={turnform} ref={turnformRef}>
                    <Row className='FormRowCon'>
                        <Col span={4}></Col>
                        <Col span={15}>
                        <Form.Item wrapperCol={{ span: 20 }}label="司机：" name="toDriverName" rules={[{ required: true, message: '请选择要转派的司机', }]}>
                        <Select
                            placeholder='请输入姓名/手机号后选择'
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            onSearch={onSearch1}
                            onChange={onChange1}
                            filterOption={false}>
                            {  driverOptions&&
                                driverOptions.map((item, index) =>  <Option key={index} value={item.driverPhone}>{item.driverName}({item.driverPhone})</Option>)

                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row className='FormRowCon'>
                        <Col span={4}></Col>
                        <Col span={15}>
                        <Form.Item wrapperCol={{ span: 20 }}label="板车：" name="toTrailerLicensePlate" rules={[{ required: true, message: '请选择要转派的司机', }]}>
                        <Select
                            placeholder='请输入车牌号进行搜索'
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            onSearch={onSearch2}
                            onChange={onChange2}
                            filterOption={false}>
                            {   carOptions&&
                                carOptions.map((item, index) =>  <Option key={index} value={item.licensePlate}>{item.licensePlate}</Option>)

                            }
                        </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            }
            {
            operationVisible2 && 
            <Modal  width={600} footer={null} visible={operationVisible2} title={title} recordObj={recordObj} onOk={handleOk2}  onCancel={()=>setoperationVisible2(false)}>
               <div className='PunchInRecord'>
                    <Timeline>
                        {   recordObj.transferClockList&&   
                            recordObj.transferClockList.map((item, index) => {
                                return (isExpand ? index < 5 : index >= 0)
                                    && <Timeline.Item key={index} color={index === 0 ? 'blue' : 'gray'}>
                                        <div className='TimelineItem_Con'>
                                            <span className='time' style={{marginRight:'20px'}}>{item.time}</span>
                                            <span className='statusCn'>
                                                { item.status =='1'?
                                                <Tag color="gold">{item.statusCn}</Tag> : 
                                                [item.status =='2'?
                                                <Tag color="blue">{item.statusCn}</Tag> :
                                                <Tag color="green">{item.statusCn}</Tag>
                                                ]
                                                }
                                            </span>
                                            <p className='address'>位置：{item.address}</p>
                                            <p className='address'>承运板车：{item.carrierPallet}</p>
                                            <p className='address'>打卡人：{item.recorder}</p>
                                        </div>
                                    </Timeline.Item>
                            })
                        }
                    </Timeline>
                        <p className='more'>
                            {isExpand ?
                                <span onClick={() => {
                                    setIsExpand(false)
                                }}>点击查看更多<DownOutlined /></span>
                                : <span>没有更多打卡记录啦～</span>
                            }
                        </p>
                </div>
            </Modal>
            }
             {
            operationVisible3 && 
            <Modal  width={1200}  footer={null} visible={operationVisible3} title={title} recordObj={recordObj} onOk={handleOk3}  onCancel={()=>setoperationVisible3(false)}>
                <Table {...InforData3} />
            </Modal>
            }
            {
            phoneVisible && 
            <Modal  width={600}  open={phoneVisible} title='查看手机号' recordObj={recordObj} onOk={() => setPhoneVisible(false)}  onCancel={()=>setPhoneVisible(false)}>
                {pePhone}
            </Modal>
            }

    </div>
}
export default CarrierDetail