import React, { useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import { Table, message, Button, Row, Col,Input, Spin, Menu, Dropdown, Select } from 'antd'
import './index.less'
import { post,get } from '@/utils/request';
import allUrl from '@/utils/url';
import {roleJudgment} from '@/utils/authority'
import moment from 'moment'
import { PlusOutlined } from '@ant-design/icons';
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import PublicTable from '@/components/Public/PublicTable'
import ExtendedColumnNew from '@/components/Public/ExtendedColumnNew'
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
import PublicTooltip from '@/components/Public/PublicTooltip'
import {setStorage,getStorage} from '@/utils/Storage'
import UploadFile from '@/components/Public/UploadFile'
import Interview from './Modal/Interview'
import {utilsDict} from '@/utils/utilsDict'
import baseURL from '@/baseURL'
import _ from 'lodash'
import PostStandardUp from './Modal/PostStandardUp'
import {EncryptByAES} from '@/components/Public/Encryption'
import PostStandardImg from './Modal/PostStandardImg'
import { Modal } from 'antd';
import {
    MOTOR_MECHANIC_CODE,
    TM_CODE,
    QYJSJL_CODE,
    SHOP_MANAGER_CODE,
    DELIVERY_MANAGER_CODE,
    SERVICESTATION_MASTER_CODE,
    USERCARE_MANAGER_CODE,
    JRJL,
    POST_MM_CODE,
    POST_BAM,
    FJSZJ,
    XMTZG,
    YHZZJL,
    YHFZZG,
    ESCJL
  } from "../../AuthorityManagement/UserManagement/constant";

const getPageStorage = (key) =>{
    let res = getStorage(window.location.hash,true)
    return res && res[key] ? res[key]:null
}

const isOnJobOpt = [
    {name: '离职', value: 0},
    {name: '在职', value: 1},
]
let countDownTime = 60
const StaffManagement = (props) => {
    const tableRef = useRef()
    const rowDataRef = useRef()
    const positionRef = useRef()
    const queryRef = useRef();

    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const [loading, setLoading] = useState(false)
    const [isCatch, setisCatch] = useState(true)
    const [current, changeCurrent] = useState(isCatch && getPageStorage('pagination')?getPageStorage('pagination').current:1)
    const [pageSize, changePageSize] = useState(isCatch &&getPageStorage('pagination')?getPageStorage('pagination').pageSize:10)
    const [tableHeight,setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({
        staffNumber: '',
        bigAreaName: '',
        dealerCode: '',
        dealerName: '',
        isOnJob: '',
        middleAreaName: '',
        mobilePhone: '',
        name: '',
        partTimePosition: '',
        position: '',
        userName: ''
    })
    const [sortCodeList,setSortCode]=useState([])
    const [ManageInterests,setManageInterests] = useState([])
    const [interviewVisible, setInterviewVisible] = useState(false)
    const [positionOptions, setPositionOptions] = useState([])
    const [bigAreaName, setBigAreaName] = useState([])
    const [middleAreaName, setMiddleAreaName] = useState([])
    const [importLoading, setImportLoading] = useState(false)
    const [isOnJobVisible, setIsOnJobVisible] = useState(false)
    const [editPhoneVisible, setEditPhoneVisible] = useState(false)
    const [PostStandardVisible, setPostStandardVisible] = useState(false)
    const [sendCodeButtonVal, setSendCodeButtonVal] = useState('发送验证码')
    const [verifySuccess, setVerifySuccess] = useState(false)
    const [smsCode, setSmsCode] = useState('')
    const [authSmsCode, setAuthSmsCode] = useState('')
    const [newPhone, setNewPhone] = useState('')
    const [columns, setColums] = useState([
        { title: '统一登录账号', dataIndex: 'staffNumber', width: 120, fixed: 'left', render: text => text || '-'},
        { title: '身份证号', dataIndex: 'idNum', width: 80, fixed: 'left', render: text => text || '-'},
        { title: '姓名', dataIndex: 'userName', width: 140, fixed: 'left', render: text => text || '-'},
        { title: '手机号', dataIndex: 'mobilePhone', width: 140, render: text => text || '-'},
        { title: '岗位', dataIndex: 'position', width: 100, render: text => {
            return positionRef.current ? [positionRef.current[text] || '-' ] : '-'
        }},
        { title: '兼职岗位', dataIndex: 'partTimePositionsName', width: 120 },
        { title: '入职时间', dataIndex: 'inductionTime', width: 120, render:text=> text?moment(text).format('YYYY-MM-DD'):'-'},
        { title: '是否在职', dataIndex: 'isOnJob', width: 100, render:text=>text && text == 1 ?'在职':'离职' },
        { title: '当前等级', dataIndex: 'currentLevel', width: 100},
        { title: '目标等级', dataIndex: 'targetLevel', width: 100},
        { title: '兼职岗位当前等级', dataIndex: 'partCurrentLevel', width: 160},
        { title: '兼职岗位目标等级', dataIndex: 'partTargetLevel', width: 160},
        { title: '企微激活状态', dataIndex: 'qywxStatus', width: 120, render: text => utilsDict('qywxStatus',text)},
        { title: '门店编号', dataIndex: 'dealerCode', width: 120, render: text => text || '-' },
        { title: '门店名称', dataIndex: 'fullName', width: 280, render: text => text ? <PublicTooltip title={text}>{text}</PublicTooltip>:'-'},
        { title: '门店简称', dataIndex: 'name', width: 280, render: text => text ? <PublicTooltip title={text}>{text}</PublicTooltip>:'-'},
        { title: '所属大区', dataIndex: 'bigAreaName', width: 120, render: text => text || '-'},
        { title: '所属省区', dataIndex: 'middleAreaName', width: 120, render: text => text || '-'},
        { title: '省', dataIndex: 'provinceName', isExtend: true, width: 120, render: text => text || '-'},
        { title: '市', dataIndex: 'cityName', width: 120, isExtend: true,  render: text => text || '-'},
        { title: '区', dataIndex: 'districtName', width: 120, isExtend: true,  render: text => text || '-'},
        { title: '门店类型', dataIndex: 'dealerType', width: 120, isExtend: true,  render: text => arrayHandle(text, 'dealerType') || '-'},
        { title: '渠道类型', dataIndex: 'channel', width: 120, isExtend: true,  render: text => arrayHandle(text, 'channel') || '-'},
        { title: '合作类型', dataIndex: 'dealerisDirect', width: 120, isExtend: true,  render: text => arrayHandle(text, 'dealerisDirect') || '-'},
        { title: '门店状态', dataIndex: 'dealerStates', width: 120, isExtend: true,  render: text => arrayHandle(text, 'dealerStates') || '-'},
        { title: '营业状态', dataIndex: 'businessStatus', width: 120, isExtend: true,  render: text => arrayHandle(text, 'businessStatus') || '-'},
        { title: '操作', width: 100,dataIndex: 'Operation',fixed:'right', render: (text, record) => renderOperation(text, record) }
    ])
    const [partTimeJobRuleVisible, setPartTimeJobRuleVisible] = useState(false) // 兼职规则设置
    const [partTimeJobRuleLoading, setPartTimeJobRuleLoading] = useState(false) // 规则设置loading
    const [partTimeJobRuleKey, setPartTimeJobRuleKey] = useState(0)
    const [partTimeJobVisible, setPartTimeJobVisible] = useState(false) // 兼职岗位设置
    const [changeJobList, setChangeJobList] = useState([]) // 所选兼职岗位
    const [allowPTP, setAllowPTP] = useState([]) // 获得允许的所有兼职岗位
    const [partTimeJobList, setPartTimeJobList] = useState([]) // 所有兼职规则
    const [allTableJob, setAllTableJob] = useState([]) // 所有兼职规则可选岗位
    const [checkPartTimeRuleId, setCheckPartTimeRuleId] = useState([]) // 当前所选规则ID
    const [checkPartTimeRow, setCheckPartTimeRow] = useState({}) // 当前所选兼职岗位

    // 更新表格可兼职数量
    const changePartTimeJobNum = (value, record) => {
        const updatedList = partTimeJobList.map(item =>
            item.id === record.id ? { ...item, ptpConfig: { ...item.ptpConfig, numberLimit: value } } : item
        );
        setPartTimeJobList(updatedList);
    };
    // 更新表格可兼职岗位
    const changePartTimeJobPost = (values, record) => {
        const updatedConfig = values.map(value => {
            const matchedItem = allTableJob.find(item => item.code === value);
            return matchedItem ? { name: matchedItem.value, value: matchedItem.code } : null;
        }).filter(Boolean);
        const updatedList = partTimeJobList.map(item =>
            item.id === record.id ? { ...item, ptpConfig: { ...item.ptpConfig, config: updatedConfig } } : item
        );
        setPartTimeJobList(updatedList);  // 更新状态
    };



    const columns1 = [
        { title: '序号', dataIndex: 'index', width: 80,fixed:'left',render:(text,record,index)=>index+1 },
        { title: '主岗名称', dataIndex: 'value',fixed:'left', width: 200},
        { title: '可兼职数量', dataIndex: 'ptpConfig', fixed:'right', width: 120, editable: true, render: (ptpConfig, record) => (
            <Select
                disabled={record.id !== checkPartTimeRuleId}
                defaultValue={ptpConfig && ptpConfig.numberLimit}
                placeholder='请选择'
                onChange={v => changePartTimeJobNum(v, record)}
            >
                <Option value={0}>0</Option>
                <Option value={1}>1</Option>
                <Option value={2}>2</Option>
                <Option value={3}>3</Option>
                <Option value={4}>4</Option>
                <Option value={5}>5</Option>
            </Select>
        )},
        { title: '可兼职岗位', dataIndex: 'ptpConfig', fixed:'right', width: 220, editable: true, render: (ptpConfig, record) => (
            <Select
                disabled={record.id !== checkPartTimeRuleId}
                style={{ width: '180px' }}
                placeholder='请选择'
                mode="multiple"
                allowClear
                showSearch
                optionFilterProp="children"
                defaultActiveFirstOption={false}
                showArrow={false}
                filterOption={(input, option) =>{
                    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.value.toLowerCase().indexOf(input.toLowerCase()) >=0
                }}
                defaultValue={ptpConfig && ptpConfig.config.map(item => item.value)} // 获取config数组的value值
                onChange={v => changePartTimeJobPost(v, record)}  // 更新岗位
            >
                {allTableJob.map((item, index) => (
                    <Option key={index} value={item.code}>{item.value}</Option>
                ))}
            </Select>
        )},
        { title: '操作', width: 100, dataIndex: 'Operation', render: (text, record) => {
            return <div style={{ color: '#1890ff' }}>
                {
                        [
                            checkPartTimeRuleId !== record.id && <span key={1} style={{ cursor: 'pointer' }} onClick={() => setCheckPartTimeRuleId(record.id)}>编辑</span>,
                            checkPartTimeRuleId === record.id && <span key={2} style={{ cursor: 'pointer', marginRight:'10px' }} onClick={() => {editPtpConfig(record)}}>确定</span>,
                            checkPartTimeRuleId === record.id && <span key={3} style={{ cursor: 'pointer' }} onClick={() => {cancelPtpConfig(record)}}>取消</span>,
                        ]
                }
            </div>
        } }
    ]


    // todo 权益分类
    useEffect(()=>{
        getList()
        getDict()
        getBigAreaName()
        getMiddleAreaName()
    },[])
    let newColumns = _.cloneDeep({ columns }).columns
    for (let i = 0; i < newColumns.length; i++) {
        if (JSON.stringify(newColumns[i]['checked']) !== undefined && !newColumns[i].checked) {
            newColumns.splice(i, 1)
            i--
        }
    }
    useEffect(()=>{
        if (positionRef.current) {
            // 当positionRef.current有值时，更新columns
            newColumns.map((item) => {
               if(item.title == '岗位') {
                item =  { title: '岗位', dataIndex: 'position', width: 100, render: text => positionRef.current[text] || '-' }
               }
            //    if(item.title == '兼职岗位') {
            //     item =  { title: '兼职岗位', dataIndex: 'partTimePosition', width: 100, render: text => positionRef.current[text] || '-' }
            //    }
            })


        }
    },[positionRef.current])

    // 兼职规则设置
    const editStaffPartTimeJobRule = async () => {
        setPartTimeJobRuleVisible(true)
        getAllRuleList()
    }

    // 获取所有兼职规则
    const getAllRuleList = async () => {
        setPartTimeJobRuleLoading(true)
        post(allUrl.StoreManage.ptpConfigList).then(res => {
            if (res.success) {
                setPartTimeJobList(res.resp[0])
                setAllTableJob(res.resp[0])
                setPartTimeJobRuleKey(Math.random()*10)
            }
        }).finally(() => {
            setPartTimeJobRuleLoading(false)
        })
    }

    // 编辑兼职岗位规则
    const editPtpConfig = async (record) => {
        post(allUrl.StoreManage.ptpConfig, record).then(res => {
            if (res.success) {
                message.success('编辑成功')
                setCheckPartTimeRuleId(null)
                queryRef.current.handleSearch()
            }
        })
    }

    // 取消设置规则
    const cancelPtpConfig = row => {
        setCheckPartTimeRuleId(null)
        getAllRuleList()
    }

    // 获取所有允许兼职岗位
    const getAllAllowPTP = async (record) => {
        setChangeJobList(record.partTimePositions ? JSON.parse(record.partTimePositions) : [])
        get(allUrl.StoreManage.getAllowPTP, {position: record.position}).then(res => {
            if (res.success) {
                setAllowPTP(res.resp[0])
                setCheckPartTimeRow(record)
                setPartTimeJobVisible(true)
            }
        })
    }

    // 设置岗位
    const onfirmPartTimeJob = async () => {
        if (allowPTP && allowPTP.numberLimit < changeJobList.length) {
            message.error(`当前主岗可兼职岗位不可超过${allowPTP.numberLimit}个`)
            return
        }
        post(allUrl.StoreManage.updPTP, {id: checkPartTimeRow.userId, ptpCodes: changeJobList}).then(res => {
            if (res.success) {
                message.success('设置成功')
                setPartTimeJobVisible(false)
                setCheckPartTimeRow({})
                setChangeJobList([])
                queryRef.current.handleSearch()
            }
        })
    }

    const changePartTimeJob = (value) => {
        setChangeJobList(value)
    }

    const getList = () => {
        get(allUrl.DetailedManageInterests.getSortList).then(res => {
            if (res.success) {
                res.resp.forEach((item, index) => {
                    item.key = index + 1
                    item.value = item.code
                })
                setSortCode(res.resp)
            } else {
                // message.error(res.msg)
            }
        })
    }
    const getBigAreaName = () => {
        get(allUrl.StoreManage.getBigAreaName).then(res => {
            let arr = []
            res.resp[0].map(item => {
                arr.push({
                    name: item,
                    value: item,
                    children: item
                })
            })
            setBigAreaName(arr)
        })
    }
    const getMiddleAreaName = () => {
        get(allUrl.StoreManage.getMiddleAreaName).then(res => {
            let arr = []
            res.resp[0].map(item => {
                arr.push({
                    name: item,
                    value: item,
                    children: item
                })
            })
            setMiddleAreaName(arr)
        })
    }
    const getDict = () => {
        get(allUrl.common.entryLists, {codes:'scrm_position'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                let arr = []
                let newArr = []
                let positionMap = {}

                arr = Dt['scrm_position']
                arr.map(item=>{
                    newArr.push({
                        value:item.entryValue,
                        name: item.entryMeaning,
                        children: item.entryMeaning,
                    })
                    positionMap[item.entryValue] = item.entryMeaning
                })
                setPositionOptions(newArr)
                console.log('岗位', positionMap)
                positionRef.current = positionMap
            } else {
                // message.error(res.message)
            }
        })
    }
    // 生成统一登录账号
    const generatorStaffNumber = (userId) => {
        get(allUrl.StoreManage.tryGeneratorStaffNumber, {userId}).then(res => {
         if(res.success){
            message.success('操作成功！')
         }
        })
    }
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
        if(isCatch){
            setStorage({
                Type:window.location.hash,
                pagination:{current,pageSize}
            })
        }
    }
    const resetPage = () => {
        changeCurrent(1)
        changePageSize(10)
    }
    const onSearch = (values) => {
        // 所属大区选- 传参为null
        if(values.bigAreaName === '-'){
            values.bigAreaName = null
        }
        if(values.middleAreaName === '-'){
            values.middleAreaName = null
        }

        if(values.bigAreaName === undefined){
            values.bigAreaName = ""
        }
        if(values.dealerCode === undefined){
            values.dealerCode = ""
        }
        if(values.dealerName === undefined){
            values.dealerName = ""
        }
        if(values.middleAreaName === undefined){
            values.middleAreaName = ""
        }
        if(values.isOnJob === undefined){
            values.isOnJob = ""
        }
        if(values.partTimePosition === undefined){
            values.partTimePosition = ""
        }
        setDefaultQuery(values)
        resetPage()
    }
    const initPage = (flag) =>{
        let winH = document.documentElement.clientHeight || document.body.clientHeight;
        let h = 0
        if(!flag){
            h = winH   - 47 - 54 - 345
        }else{
            h = winH   - 47 - 54 - 345 - 112
        }
        setTableHeight(h)
    }
    useEffect(()=>{
        initPage()
    },[])


    let searchList = [
        { label: "统一登录账号", name: "staffNumber", type: "Input", placeholder: "请完整输入统一登录账号", colSpan: 6 },
        { label: '姓名', name: 'userName', type: 'Input', placeholder: '请完整输入姓名', colSpan: 6 },
        { label: '手机号', name: 'mobilePhone', type: 'Input', placeholder: '请完整输入手机号', colSpan: 6 },
        {
            label: '岗位', name: 'position', type: 'Select', placeholder: '请选择', colSpan: 6,showSearch:true, data: positionOptions || []
        },
        {
            label: '兼职岗位', name: 'partTimePosition', type: 'Select', placeholder: '请选择', colSpan: 6,showSearch:true, data: positionOptions || []
        },
        {
            label: '是否在职', name: 'isOnJob', type: 'Select', placeholder: '请选择', colSpan: 6,showSearch:false, data:  isOnJobOpt || []
        },
        { label: '门店编号', name: 'dealerCode', type: 'Input', placeholder: '请输入门店编号', colSpan: 6 },
        { label: '门店简称', name: 'name', type: 'Input', placeholder: '请输入门店名称', colSpan: 6 },
        {
            label: '所属大区', name: 'bigAreaName', type: 'Select', placeholder: '请选择', colSpan: 6,showSearch:true, data: bigAreaName || []
        },
        {
            label: '所属省区', name: 'middleAreaName', type: 'Select', placeholder: '请选择', colSpan: 6,showSearch:true, data: middleAreaName || []
        },
    ]
    const arrayHandle = (text, type) => {
        let result = '-'
        if( text && text.indexOf(',') > -1){
            let arr = text.split(',')
            let res = []
            arr.map(item => {
                res.push(utilsDict(type, item))
            })
            result = res.join(',')
        } else {
            result = utilsDict(type, text)
        }
        return result
    }
    const onMenuClick  = ({key},record) =>{

        switch (key){
            case '5':
                RowEdit(record);
            break;
            case '7':
                let data = {
                    ...record
                }
                rowDataRef.current = record
                UniversalOpenWindow({
                    JumpUrl:'/StoreManage/StaffManagementDetail', data, history:props.history
                })
            break
            case '8':
                rowDataRef.current = record
                setNewPhone('')
                setEditPhoneVisible(true)
            break;
            case '9':
                rowDataRef.current = record
                setIsOnJobVisible(true)
            break;
            case '10':
                rowDataRef.current = record
                setInterviewVisible(true)
            break;
            case '11':
                rowDataRef.current = record
                generatorStaffNumber(record.userId)
            case '12':
                getAllAllowPTP(record)
            break;
        }
    }
    const renderOperation = (text, record) => {
        const menu = (
            <Menu onClick={(e)=> onMenuClick(e,record)}>
                {
                    record.position == SHOP_MANAGER_CODE //总经理
                    || record.position == DELIVERY_MANAGER_CODE //交付经理
                    || record.position == TM_CODE   //技术专家
                    || record.position == SERVICESTATION_MASTER_CODE  //服务经理
                    || record.position == USERCARE_MANAGER_CODE //用户关爱经理
                    || record.position == JRJL //金融保险专员
                    || record.position == POST_MM_CODE //市场经理
                    || record.position == POST_BAM //精品附件经理
                    || record.position == FJSZJ //副技术专家
                    || record.position == XMTZG // 新媒体主管
                    || record.position == YHZZJL // 用户增长经理
                    || record.position == YHFZZG // 用户发展经理
                    || record.position == ESCJL // 二手车经理
                    ?
                   ( roleJudgment(userInfo,'STAFF_USER_RESET_INTERVIEW') ?
                    <Menu.Item key='10'>面试信息维护</Menu.Item>:null):null
                }
                {
                    roleJudgment(userInfo,'STAFF_USER_EDIT') ?
                    <Menu.Item key='5'>编辑</Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'STAFF_USER_DETAIL') ?
                    <Menu.Item key='7'>详情</Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'STAFF_PART_TIME_JOB_EDIT') ?
                    <Menu.Item key='12'>设置兼职岗位</Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'STAFF_USER_PHONE') ?
                    <Menu.Item key='8'>变更手机号</Menu.Item>:null
                }
                {
                    roleJudgment(userInfo,'STAFF_USER_EXIT') && record.isOnJob == 1 ?
                    <Menu.Item key='9'>员工离职</Menu.Item>:null
                }
                 {
                    roleJudgment(userInfo,'STAFF_USER_CREATE_STAFF_NUMBER') && !record.staffNumber ?
                    <Menu.Item key='11'>生成统一登录账号</Menu.Item>:null
                }
            </Menu>
        )
        return <Dropdown overlay={menu} placement="bottomCenter" trigger={['click']} >
            <a>更多操作</a>
        </Dropdown>
    }
    const RowEdit = (record) => {
        let data = {
            userId: record.userId,
            dealerCode: record.dealerCode,
            qywxStatus: record.qywxStatus,
            Type: 'Edit',
            title:'编辑员工'
        }
        UniversalOpenWindow({
            JumpUrl:'/StoreManage/StaffManagementCreate',data,history:props.history
        })

    }

    const downloadDataFailed=(response)=>{
        let url = response.resp[0]
        window.open(url)
    }

    const UploadChange = ({ file, fileList }, type) => {
        console.log(file,type,'file的数据')
        const { response } = file
        if (file.status === 'uploading') {
            setImportLoading(true)
        }
        if (file.status === 'done') {
            if (response.success) {
                let key =2;
                message.loading({ content: '正在导入中，请稍后', key });
                setTimeout(() => {
                    message.success({ content: '导入成功!', key, duration: 2 });
                }, 1000);
            } else {
                let key =2;
                message.loading({ content: '正在导入中，请稍后', key });
                setTimeout(() => {
                    message.error({content:<span>未找到部分面试者/面试官手机号，点击"<a onClick={() => downloadDataFailed(response)}>确定</a>"下载导入失败数据</span>, key, duration: 10 });
                }, 1000);
            }
            setImportLoading(false)
        }
        if (file.status === 'error') {
            message.error(response.msg || response.msgCode)
            setImportLoading(false)
        }
    }

    const download = () => {
        let url = 'https://scrm-prod-oss.oss-cn-shanghai.aliyuncs.com/template/%E9%9D%A2%E8%AF%95%E6%88%90%E7%BB%A9%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
        window.open(url)
    }
    const exportStaffUser = () => {

        post(
            allUrl.Authority.exportStoreUser,
            {},
            {
              responseType: "blob",
            }
          ).then((res) => {
            if (res) {
              let blob = new Blob([res], { type: "application/vnd.ms-excel" });
              if (window.navigator.msSaveOrOpenBlob) {
                //兼容ie
                window.navigator.msSaveBlob(blob, "门店员工列表.xlsx");
              } else {
                let downloadElement = document.createElement("a");
                let href = window.URL.createObjectURL(blob); //创建下载的链接
                downloadElement.href = href;
                let date = new Date();
                downloadElement.download = `门店员工列表${date.toLocaleDateString()}` + ".xlsx"; //下载后文件名
                document.body.appendChild(downloadElement);
                downloadElement.click(); //点击下载
                document.body.removeChild(downloadElement); //下载完成移除元素
                window.URL.revokeObjectURL(href); //释放掉blob对象
              }
              message.success("导出成功！");
            } else {
              // message.error(res.msg)
            }
          });
    }
    const staffExit = () => {

        get(allUrl.StoreManage.staffExit, {
            userId: rowDataRef.current.userId,
        }).then(res=>{

            if(res.success){
                message.success('变更成功！')
                queryRef.current.handleSearch()
                setIsOnJobVisible(false)
            }else{
                // message.error(res.msg)
            }
        })
    }
    const sendCode = () => {
        countDownTime = 60
        const intervalId = setInterval(() => {
          if (countDownTime > 0) {
            countDownTime--;
          } else {
            clearInterval(intervalId);
            countDownTime = 0
          }
        }, 1000);
        countDownTime > 0 ? setSendCodeButtonVal(`${countDownTime}s`) : setSendCodeButtonVal('发送验证码')
      };
    const verifyPhoneExist = () => {
        get(allUrl.StoreManage.sendSms2UpdatePhone, { userId: rowDataRef.current.userId, phone: newPhone}).then(res=>{
            if(res.success){
                setEditPhoneVisible(false)
                setVerifySuccess(true)
            }
        })
    }
    const updatePhone = () => {
        let code = EncryptByAES(smsCode,'qOOM8aQpd3TprmR7', 'YZgUv8tD3nW3uXuJ')
        let authCode = EncryptByAES(authSmsCode,'qOOM8aQpd3TprmR7', 'YZgUv8tD3nW3uXuJ')
        get(allUrl.StoreManage.updatePhone, { userId: rowDataRef.current.userId, phone: newPhone, code, authCode}).then(res=>{
            if(res.success){
                message.success('修改成功！')
                setEditPhoneVisible(false)
                setVerifySuccess(false)
                queryRef.current.handleSearch()
                setAuthSmsCode('')
                setSmsCode('')
            }
        })
    }
    useEffect(()=>{
        get(allUrl.DetailedManageInterests.getTypeList).then(res=>{
            if(res.success){
                res.resp.forEach(item=>{
                    // item.name = item.name
                    item.value = item.code
                })
                setManageInterests(res.resp)
            }else{
                // message.error(res.msg)
            }
        })
    },[])



    const handleClockInExport=({key})=>{
        //console.log(key)
        let date=moment().startOf('month')   //本月
        if(key==='yesterday'){
            date=moment().add(-1,'day')   //昨天
        }else if(key==='lastMonth'){
            date=moment().add(-1,'month').startOf('month')   //上个月1号
        }
        //console.log(date)
        //date=date.format('YYYY-MM-DD')
        let url=[allUrl.Authority.exportQwClockDaily, allUrl.Authority.exportQwClockMonth]
        post(
            key==='yesterday'?url[0]:url[1],
            {date:date.format('YYYY-MM-DD')},
            {
              responseType: "blob",
            }
          ).then((res) => {
            //console.log('打卡报告导出中1')
            if (res) {
              let blob = new Blob([res], { type: "application/vnd.ms-excel" });
              let fileName=(key==='yesterday'?date.format('YYYY年MM月DD日')+"打卡数据.xlsx":(date.format('YYYY年MM月')+"打卡数据.xlsx"))
              if (window.navigator.msSaveOrOpenBlob) {
                //兼容ie
                //console.log('打卡报告导出中3')
                window.navigator.msSaveBlob(blob, fileName);
              } else {
                let downloadElement = document.createElement("a");
                let href = window.URL.createObjectURL(blob); //创建下载的链接
                downloadElement.href = href;
                //let date = new Date();
                downloadElement.download = fileName; //下载后文件名
                //downloadElement.download = keywordDict[key]+`-${date.toLocaleDateString()}.xlsx`; //下载后文件名
                document.body.appendChild(downloadElement);
                downloadElement.click(); //点击下载
                document.body.removeChild(downloadElement); //下载完成移除元素
                window.URL.revokeObjectURL(href); //释放掉blob对象
                //console.log('打卡报告导出中4')
              }
            message.success("导出成功！");
            } else {
              // message.error(res.msg)
            }
          });
    }

    const clockInMenuDay = (
        <Menu onClick={(key)=>handleClockInExport(key)}>
          <Menu.Item key='yesterday'>昨日打卡数据</Menu.Item>
        </Menu>
      );
    const clockInMenuMonth = (
        <Menu onClick={(key)=>handleClockInExport(key)}>
          <Menu.Item key='lastMonth'>上月打卡数据</Menu.Item>
          <Menu.Item key='thisMonth'>本月打卡数据</Menu.Item>
        </Menu>
      );
    return (
        <Spin spinning={loading}>
            <div className='detail-PublicList'>
                <PublicTableQuery ref={queryRef}  initPage={initPage} isCatch={true} isFormDown={true} onSearch={onSearch} searchList={searchList} />
                <div className="tableData">
                    <Row className='tableTitle'>
                        <Col className='text' style={{color:'black',fontSize:20}}>
                            <span style={{marginRight:'20px'}}>门店员工管理</span>
                            <a style={{fontSize:'14px',margin:'6px 0 0 0px'}} onClick={()=> setPostStandardVisible(true)}>查看岗位和人员标准</a>
                        </Col>
                        <Col className='bts'>
                        {
                            roleJudgment(userInfo,'STAFF_MANAGEMENT_BUILD') ?
                                <Button type='primary' icon={<PlusOutlined />} onClick={()=>{
                                    let data = {
                                        clueId: 0,
                                        Type: 'Add',
                                        title:'添加员工'
                                    }
                                    UniversalOpenWindow({
                                        JumpUrl:'/StoreManage/StaffManagementCreate',data,history:props.history
                                    })
                                }}>添加员工</Button>:null
                        }
                        {roleJudgment(userInfo, "STAFF_USER_ PUNCH_CARD_DAY_EXPORT") ? (
                            <Dropdown
                                overlay={clockInMenuDay}
                                placement="bottom"
                            >
                            <Button type="primary">日打卡数据导出</Button>
                          </Dropdown>
                            ) : null}

                        {roleJudgment(userInfo, "STAFF_USER_ PUNCH_CARD_MONTH_EXPORT") ? (
                            <Dropdown
                                overlay={clockInMenuMonth}
                                placement="bottom"
                            >
                            <Button type="primary">月度打卡数据导出</Button>
                          </Dropdown>
                            ) : null}
                          {roleJudgment(userInfo, "EXPORT_STAFF_USER") ? (
                            <Button
                            type="primary"
                            style={{ cursor: "pointer", marginRight: 10 }}
                            onClick={() => exportStaffUser()}
                            >
                                导出
                            </Button>
                            ) : null}
                            {roleJudgment(userInfo, "STAFF_PART_TIME_JOB_RULE_EDIT") ? (
                            <Button
                            type="primary"
                            style={{ cursor: "pointer", marginRight: 10 }}
                            onClick={() => editStaffPartTimeJobRule()}
                            >
                                兼职规则设置
                            </Button>
                            ) : null}
                        {
                            roleJudgment(userInfo, 'STAFF_USER_INTERVIEW_IMPORT_RESULT') ?
                            <>
                                <UploadFile
                                    style={{ display: 'inline-block' }}
                                    extension={['xls', 'xlsx']}
                                    showUploadList={false}
                                    size={10}
                                    action={baseURL.Host + allUrl.Authority.importExcel}
                                    UploadChange={UploadChange}
                                >
                                    <Button loading={importLoading}>面试信息导入</Button>
                                </UploadFile>
                                <a style={{fontSize:'14px',margin:'6px 6px 0 10px'}} onClick={download}>模版下载</a>
                            </>:null
                        }
                        <ExtendedColumnNew setColums={setColums} columns={columns} />
                        </Col>
                    </Row>
                    <PublicTable
                        scroll={{ x: "max-content", y: tableHeight }}
                        sticky={true}
                        isCatch={true}
                        ref={tableRef}
                        type={2}
                        rowSelection={false}
                        defaultQuery={defaultQuery}
                        url={allUrl.Authority.storeuserList}
                        columns={newColumns}/>
                </div>

                { interviewVisible &&
                    <Interview visible={interviewVisible} onCancel={()=> setInterviewVisible(false)} rowData={rowDataRef.current}/>
                }
                {/* { 表格形式的岗位
                    PostStandardVisible &&
                    <PostStandardUp visible={PostStandardVisible} onCancel={()=>setPostStandardVisible(false)} />
                } */}
                { // 图片形式的岗位
                    PostStandardVisible &&
                    <PostStandardImg visible={PostStandardVisible} onCancel={()=>setPostStandardVisible(false)} />
                }
                { // 变更在职状态
                    isOnJobVisible &&
                    <Modal open={isOnJobVisible} onOk={staffExit} onCancel={()=>setIsOnJobVisible(false)} >
                        <p>是否要将{rowDataRef.current.userName}由在职变更为离职 ？点击确定后即变更完成。</p>
                    </Modal>
                }
                { // 变更手机号
                    editPhoneVisible &&
                    <Modal open={editPhoneVisible} title="更换手机号" okText="前往验证" onOk={verifyPhoneExist} onCancel={()=>setEditPhoneVisible(false)} >

                            <div>
                                 <Input style={{ width: 200}}  placeholder="请输入新手机号" value={newPhone} onChange={(e) => setNewPhone(e.target.value)}/>
                                 <p style={{color: 'red', marginTop: 10}}>注意：请先核对新手机号在系统内是否重复，若重复将无法修改成功。</p>
                             </div>
                    </Modal>
}
{
                     verifySuccess &&
                     <Modal open={verifySuccess} title="更换手机号" onOk={updatePhone} onCancel={()=>setVerifySuccess(false)} >
                              <div>
                                <h4>第一步：请输入当前登录账号手机号收到的验证码以验证你的身份。</h4>
                                <Input  style={{marginTop: 10, width: 150}}  placeholder="本人验证码" value={authSmsCode}  onChange={(e) => setAuthSmsCode(e.target.value)} />
                                <h4  style={{marginTop: 10}}>第二步：请输入员工新手机号收到的验证码以完成更换。</h4>
                                <div style={{marginTop: 10}}>账号：{rowDataRef.current.staffNumber} 姓名：{rowDataRef.current.userName} 新手机号：{newPhone}</div>
                                <Input style={{marginTop: 10, width: 150}}  placeholder="请输入新手机号验证码" value={smsCode}  onChange={(e) => setSmsCode(e.target.value)} />
                            </div>
                     </Modal>
                }
                {/* 兼职规则设置 */}
                {
                    partTimeJobRuleVisible &&
                    <Modal title="兼职规则设置" visible={partTimeJobRuleVisible} onCancel={()=>setPartTimeJobRuleVisible(false)} footer={null} width={800}>
                        <Table key={partTimeJobRuleKey} rowKey='id' loading={partTimeJobRuleLoading} columns={columns1} dataSource={partTimeJobList} scroll={{y:'max-content'}} pagination={false} />
                    </Modal>
                }
                {/* 兼职岗位设置 */}
                {
                    partTimeJobVisible &&
                    <Modal title="兼职岗位设置" visible={partTimeJobVisible} onOk={onfirmPartTimeJob} onCancel={()=>{setPartTimeJobVisible(false);setCheckPartTimeRow({});setChangeJobList([])}} width={800}>
                        <Select
                            style={{ width: '180px' }}
                            placeholder='请选择'
                            mode="multiple"
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            defaultValue={checkPartTimeRow.partTimePositions ? JSON.parse(checkPartTimeRow.partTimePositions) : []} // 获取config数组的value值
                            showArrow={false}
                            filterOption={false}
                            onChange={v => changePartTimeJob(v)}
                            filterOption={(input, option) =>{
                                return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.value.toLowerCase().indexOf(input.toLowerCase()) >=0
                            }}
                            >
                            {allowPTP && allowPTP.config.map((item, index) => (
                                <Option key={index} value={item.value}>{item.name}</Option>
                            ))}
                        </Select>
                    </Modal>
                }
            </div>
        </Spin>
    )
}
export default StaffManagement
