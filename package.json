{"name": "test", "version": "0.1.0", "private": true, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons": "^5.4.0", "@babel/core": "7.23.3", "@svgr/webpack": "4.3.3", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "@typescript-eslint/eslint-plugin": "^2.34.0", "@typescript-eslint/parser": "^2.34.0", "@uiw/react-amap": "^1.8.8", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "animate.css": "^4.1.1", "antd": "^4.24.15", "axios": "^1.7.7", "babel-eslint": "10.1.0", "babel-jest": "^24.9.0", "babel-loader": "8.1.0", "babel-plugin-import": "^1.13.8", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "10.0.1", "benz-amr-recorder": "^1.1.5", "braft-editor": "^2.3.9", "browserslist": "4.22.1", "camelcase": "^5.3.1", "case-sensitive-paths-webpack-plugin": "2.4.0", "crypto-js": "^4.2.0", "css-loader": "3.4.2", "cssnano": "^6.0.1", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-loader": "3.0.3", "eslint-plugin-flowtype": "4.6.0", "eslint-plugin-import": "2.20.1", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.19.0", "eslint-plugin-react-hooks": "^1.7.0", "file-loader": "6.2.0", "file-saver": "^2.0.5", "fs-extra": "^8.1.0", "html-webpack-plugin": "5.5.3", "html2canvas": "^1.4.1", "http-proxy-middleware": "^2.0.6", "identity-obj-proxy": "3.0.0", "immutability-helper": "^3.1.1", "jest": "24.9.0", "jest-environment-jsdom-fourteen": "1.0.1", "jest-resolve": "24.9.0", "jest-watch-typeahead": "0.4.2", "js-cookie": "^2.2.1", "less": "^3.13.1", "less-loader": "11.1.3", "lodash": "^4.17.21", "mini-css-extract-plugin": "2.7.6", "moment": "^2.29.4", "nanoid": "^4.0.2", "node-libs-browser": "^2.2.1", "postcss-flexbugs-fixes": "5.0.2", "postcss-loader": "7.3.3", "postcss-normalize": "10.0.1", "postcss-preset-env": "9.3.0", "postcss-safe-parser": "6.0.0", "prop-types": "^15.8.1", "query-string": "^6.14.1", "rc-field-form": "^1.40.0", "rc-input": "^0.1.4", "rc-virtual-list": "^3.11.3", "react": "^16.14.0", "react-app-polyfill": "^1.0.6", "react-copy-to-clipboard": "^5.1.0", "react-dev-utils": "12.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^16.14.0", "react-error-overlay": "^6.0.11", "react-fast-compare": "^3.2.2", "react-pdf": "^8.0.2", "react-redux": "^7.2.9", "react-resizable": "^3.0.5", "react-router-cache-route": "^1.12.11", "react-router-dom": "^5.3.4", "redux": "^4.2.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-promise": "^0.6.0", "redux-thunk": "^2.4.2", "resolve": "1.15.0", "resolve-url-loader": "3.1.1", "save-dev": "0.0.1-security", "semver": "6.3.0", "style-loader": "0.23.1", "ts-pnp": "1.1.6", "url-loader": "4.1.1", "webpack-dev-server": "4.15.1", "webpack-manifest-plugin": "5.0.0", "workbox-webpack-plugin": "7.0.0"}, "resolutions": {"ajv": "6.12.5", "ajv-keywords": "3.5.2"}, "scripts": {"start": "cross-env BIZ_ENV=dev node scripts/start.js", "start:test": "cross-env BIZ_ENV=test REACT_APP_ENV=test mode=test node scripts/start.js", "start:uat": "cross-env BIZ_ENV=uat mode=uat node scripts/start.js", "start:prod": "cross-env BIZ_ENV=prod mode=prod node scripts/start.js", "build": "cross-env BIZ_ENV=prod REACT_APP_ENV=prod node scripts/build.js", "test": "node scripts/test.js", "build:dev": "cross-env BIZ_ENV=dev REACT_APP_ENV=dev node scripts/build.js", "build:test": "cross-env BIZ_ENV=test REACT_APP_ENV=test mode=test node scripts/build.js", "build:uat": "cross-env BIZ_ENV=uat REACT_APP_ENV=uat node scripts/build.js", "build:prod": "cross-env BIZ_ENV=prod REACT_APP_ENV=prod node scripts/build.js", "preinstall": "npx npm-force-resolutions", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,less}\""}, "eslintConfig": {"extends": ["react-app", "prettier", "plugin:react/recommended"], "plugins": ["prettier"], "rules": {"prettier/prettier": "error", "react/display-name": "off", "react/prop-types": "off", "jsx-a11y/anchor-is-valid": "off", "react/jsx-no-target-blank": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "overrides": [{"files": ["**/*.ts?(x)"], "extends": ["react-app", "prettier", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["prettier", "@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module", "project": "./tsconfig.json"}, "rules": {"prettier/prettier": "error", "react/display-name": "off", "react/prop-types": "off", "jsx-a11y/anchor-is-valid": "off", "react/jsx-no-target-blank": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}}]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jest-environment-jsdom-fourteen", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "babel": {"presets": ["react-app"], "env": {"development": {"plugins": ["react-refresh/babel"]}}}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.25.4", "@opas/cli": "^0.12.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/react": "^18.3.5", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.0", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "css-minimizer-webpack-plugin": "^5.0.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.4.1", "postcss": "8.4.31", "prettier": "^3.3.3", "process": "0.11.10", "react-hot-loader": "^4.13.1", "react-refresh": "^0.14.2", "terser-webpack-plugin": "^5.3.10", "typescript": "^5.5.4", "webpack": "^5.94.0", "webpack-cli": "^5.1.4"}, "homepage": "."}