import React, { useState, memo, useEffect, useCallback, useContext } from 'react'

import { useSelector } from 'react-redux'
import { Table, message, Button, Descriptions, Row, Col, Spin, Tabs, Divider } from 'antd'
import history from '@/utils/history'
import { get, post } from '@/utils/request';
import allUrl from '@/utils/url';
import { utilsDict } from '@/utils/utilsDict'

import moment from 'moment'
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow'
import { DecryptByAES } from '@/components/Public/Decrypt'
import PublicTooltip from '@/components/Public/PublicTooltip'
import AgreementDetail from './AgreementDetail'
import styles from '../antd.module.less'
import { KeepTableInject } from '@/pages/RightCenter/AccountManagement/Detail'

const KeepTable = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {}
    const { dataSource } = props
    const [agreementDetail, setAgreementDetail] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [loading, setLoading] = useState(false)
    const { type } = useContext(KeepTableInject) ?? {}
    const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
    };
    const getYearUnitName = (key) => {
        return {
            'year': '年',
            'month': '月',
            'day': '日'
        }[key] || ''
    }
    const handleCancel = () => {
        setIsModalVisible(false);
    };
    const LookAt = (record) => {
        setIsModalVisible(true);
        let params = {
            vin: record.vin,
            detailNo: record.detailNo,
            accountRelationId: record.accountPackRelationId
        }
        get(allUrl.AccountManagement.getPurchasedObj, { ...params }).then(res => {
            setAgreementDetail([])
            if (res.success) {
                if (res.resp) {
                    res.resp.map((item, index) => item.key = index + 1)
                    setAgreementDetail(res.resp)
                }
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
        console.log(record, '99999', params.vin)

    }
    return (
        <>
            <Table className={`${styles['gray-table']} TablePanele`}
                dataSource={dataSource}
                rowKey={record => record.id.toString()}
                bordered={true}
                size="small"
                pagination={false}
                scroll={{ x: 'max-content' }}
            >
                <Table.Column title='权益明细编码' key='detailNo' dataIndex='detailNo' width={100} fixed='left' render={text => text || '-'} />
                <Table.Column title='权益名称' key='' dataIndex='name' width={160} render={text => text || '-'} fixed={'left'} />
                {/* <Table.Column title='权益有效期' key='relativeTime' dataIndex='relativeTime' width={190} render={(text,record)=>{
                            return record.effect===2 || record.effect===3 || record.effect===4?
                            <>{!record.endTime ? '永久有效':moment(record.effectTime).format('YYYY年MM月DD日 HH:mm:ss') + ' ~ '+ moment(record.endTime).format('YYYY年MM月DD日 HH:mm:ss')}</>
                            :<>{record.relativeTimeName?record.relativeTimeName:moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') + ' ~ '+ moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')}</>
                        }}/>     */}
                <Table.Column title='权益业务场景' key='businessSceneName' dataIndex='businessSceneName' width={120} render={text => text || '-'} />
                <Table.Column title='权益有效期' key='relativeTime' dataIndex='relativeTime' width={190} render={(text, record) => {
                    return record.effect === 2 || record.effect === 3 || record.effect === 4 ?
                        <><div>{!record.endTime ? '永久有效' : moment(record.effectTime).format('YYYY年MM月DD日 HH:mm:ss') + ' ~ '}</div><div>{!record.endTime ? '' : moment(record.endTime).format('YYYY年MM月DD日 HH:mm:ss')}</div></>
                        : <><div>{record.relativeTimeName ? record.relativeTimeName : moment(record.fixedBeginTime).format('YYYY年MM月DD日 HH:mm:ss') + ' ~ '}</div><div>{record.relativeTimeName ? '' : moment(record.fixedEndTime).format('YYYY年MM月DD日 HH:mm:ss')}</div></>
                }} />
                <Table.Column title='车辆总里程（公里）' key='totalMileage' dataIndex='totalMileage' width={160} render={text => text || '-'} />
                <Table.Column title='车辆权益变更属性' key='identityName' dataIndex='identityName' width={140} render={text => text || '-'} />
                <Table.Column title='商品分类' key='goodsCategoryName' dataIndex='goodsCategoryName' width={140} render={text => text || '-'} />
                {
                    // 精品权益
                    type === 'boutique' ?
                        <>
                            <Table.Column title='权益所属' key='goodsTypeName' dataIndex='goodsTypeName' width={140} render={text => text || '-'} />
                            <Table.Column title='华为权益活动编码' key='hwActivityCode' dataIndex='hwActivityCode' width={140} render={text => text || '-'} />
                            <Table.Column title='华为权益活动名称' key='hwActivityName' dataIndex='hwActivityName' width={140} render={text => text || '-'} />
                            <Table.Column title='华为权益明细编码' key='hwDetailNo' dataIndex='hwDetailNo' width={140} render={text => text || '-'} />
                            <Table.Column title='权益销售备注' key='hwRemark' dataIndex='hwRemark' width={140} render={text => text || '-'} />
                        </> : null
                }
                <Table.Column title='剩余情况' key='remain' dataIndex='remain' width={120} render={text => {
                    let a = ''
                    if (text) {
                        a = text < 0 ? '不限次数' : text + '次';
                    } else {
                        a = '无次数'
                    }
                    return a
                }} />
                <Table.Column title='权益生效状态' key='effectName' dataIndex='effectName' width={140} render={text => text || '-'} />
                <Table.Column title='权益使用状态' key='statusName' dataIndex='statusName' width={120} render={text => text} />
                <Table.Column title='操作' key='' dataIndex='' width={80} fixed='right'
                    render={(text, record) => (
                        <Button type='link' size='small' onClick={() => LookAt(record)}>详情</Button>
                    )
                    } />
            </Table>

            {
                isModalVisible &&
                <AgreementDetail visible={isModalVisible} handleCancel={handleCancel} dataSource={agreementDetail} />
            }
        </>
    )
}
export default memo(KeepTable)