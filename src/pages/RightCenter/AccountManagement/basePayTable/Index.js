import React, { useState, memo, useEffect, useCallback, useContext } from 'react';
import { useSelector } from 'react-redux';
import { Table, Button, Tag, Descriptions, Row, Col, Spin, Tabs, Divider, Tooltip } from 'antd';
import ChangeRightDetail from '../ChangeRightDetail';
import { get } from '@/utils/request';
import allUrl from '@/utils/url';
import styles from '../../antd.module.less';
import PublicTable from './PublicTable';
import { InjectMessage } from '@/pages/RightCenter/AccountManagement/Detail';
import { roleJudgment } from '@/utils/authority';

const Index = props => {
  const { strategy, packType } = props;
  const [expandedRowKeys, setExpandedRowKeys] = useState([]); // table需要展开的行
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState({});
  const [insideTableData, setInsideTableData] = useState([]);
  const { isCurrent, type } = useContext(InjectMessage);
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};

  const onExpand = (expanded, record) => {
    if (expanded) {
      setExpandedRowKeys([record.detailNo]);
      getAccountPreEquityExtended(record);
    } else {
      setExpandedRowKeys([]);
      setInsideTableData([]);
    }
  };

  //取延保的前权益信息
  const getAccountPreEquityExtended = record => {
    setInsideTableData([]);
    get(allUrl.AccountManagement.selectAccountPreEquityExtended, {
      accountNo: record.accountNo,
      originDetailNo: record.originDetailNo,
      displayFrame: record.displayFrame, //聚合还是并列
      type: packType, //权益包类型
    }).then(res => {
      const { success, resp } = res;
      if (success) {
        if (!!resp.length) {
          setInsideTableData(resp);
        }
      }
    });
  };

  //table展开收起
  const expandableProps = {
    showExpandColumn: false,
    expandedRowRender: record => (
      <PublicTable
        strategy={strategy}
        insideTable={true}
        className={styles['blue-Table-nest']}
        dataSource={insideTableData}
      />
    ),
    rowExpandable: record => record.businessScene === 2,
    expandedRowKeys: expandedRowKeys, //展开的行
    onExpand,
  };
  //编辑相关---- start
  const showModal = () => {
    setIsModalVisible(true);
  };
  const onEdit = record => {
    showModal();
    setCurrentRecord(record);
  };
  const handleCancel = () => {
    setIsModalVisible(false);
  };
  //编辑相关---- end

  return (
    <>
      <PublicTable
        {...props}
        expandableProps={expandableProps}
        expandedRowKeys={expandedRowKeys}
        className={`${styles['gray-table']} basePayTable`}
      >
        {/* V20.0精品权益没有编辑按钮 */}
        {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_DETAILEDIT') && type !== 'boutique' && type !== 'driver' && (
          <Table.Column
            fixed="right"
            title="操作"
            width={120}
            ellipsis
            render={(text, record) => {
              // return <Button type='link' size='small' onClick={() => { onDetail(text, record) }}>详情</Button>
              // v12.0
              return (
                <Button
                  disabled={record.deactivationStatus || !isCurrent}
                  type="link"
                  size="small"
                  onClick={() => {
                    onEdit(record);
                  }}
                >
                  编辑
                </Button>
              );
            }}
          />
        )}
      </PublicTable>
      {isModalVisible && (
        <ChangeRightDetail
          handleCancel={handleCancel}
          visible={isModalVisible}
          dataSource={currentRecord}
          strategy={strategy}
        />
      )}
    </>
  );
};
export default memo(Index);
