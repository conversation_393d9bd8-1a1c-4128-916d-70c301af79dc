import React from 'react'
import { Modal, Form, Input, message ,Spin } from 'antd'
import { post } from '../../../utils/request'
import allUrl from '../../../utils/url'
import {EncryptByAES} from '../../Public/Encryption'
import history from '@/utils/history'
import Cookies from 'js-cookie'
class ChangePsd extends React.Component {
    state = {
        loading:false
    }
    formRef = React.createRef()
    handleOk = () => {
        this.formRef.current.validateFields().then(values=>{
            if(values.password === values.newPassword){
                message.error('新密码与旧密码不能一致！')
                return
            }
            let password = EncryptByAES(values.password,'5566778899ABCDEF', 'ABCDEF5566778899')
            let newPassword = EncryptByAES(values.newPassword,'5566778899ABCDEF', 'ABCDEF5566778899')
            this.setState({loading:true},()=>{
                post(allUrl.Authority.modifyPsd,{password,newPassword}).then(res=>{
                    if(res.success){
                        message.success('密码修改成功，请重新登录！')
                        this.props.onOk()
                        setTimeout(()=>{
                            localStorage.removeItem('remember')
                            Cookies.remove('scrm_token')
                            history.push('/')
                        },1000)
                    }else{
                        // message.error(res.msg || '密码修改失败！')
                    }
                    this.setState({loading:false})
                })
            })
        })
        .catch(error=>{
            console.log(error)
        })
    }
    componentDidMount(){
        const {userInfo} = this.props
        this.formRef.current.setFieldsValue({
            mobilePhone:userInfo.mobilePhone,
            userName:userInfo.userName
        })
    }

    render() {
        const { visible, onCancel } = this.props
        const layout = {
            labelCol: { span: 5 },
            wrapperCol: { span: 24 },
        };
        return (
            <Modal title='修改密码' visible={visible} onCancel={onCancel} onOk={this.handleOk} bodyStyle={{ padding: '54px' }} maskClosable={false}>
                <Spin spinning={this.state.loading}>
                    <Form
                        {...layout}
                        className='loginBox'
                        name="basic"
                        initialValues={{ remember: true, }}
                        ref={this.formRef}
                    >
                        <Form.Item name="userName" label="姓名">
                            <Input disabled allowClear autoComplete={'off'} placeholder="请输入..." />
                        </Form.Item>
                        <Form.Item
                            name="mobilePhone"
                            label='手机号'
                            rules={[
                                {
                                    required: true,
                                    message: '请输入手机号！',
                                },
                                // {
                                //     validator(_, value) {
                                //         if (value && !/^[1][3,4,5,7,8,9][0-9]{9}$/.test(value)) {
                                //             return Promise.reject(new Error('请输入正确的手机号！'));
                                //         } else {
                                //             return Promise.resolve();
                                //         }
                                //     },
                                // }
                            ]}
                        >
                            <Input disabled allowClear autoComplete={'off'} placeholder='请输入...' />
                        </Form.Item>

                        <Form.Item
                            name="password"
                            label='旧密码'
                            rules={[
                                {
                                    required: true,
                                    message: '请输入旧密码！',
                                },
                            ]}
                        >
                            <Input.Password allowClear placeholder='请输入...' />
                        </Form.Item>
                        <Form.Item
                            name="newPassword"
                            label='新密码'
                            rules={[
                                {
                                    required: true,
                                    message: '请输入新密码！',
                                },
                                ({ getFieldValue }) => ({
                                    validator(_, value) {
                                      if (!value) {
                                        return Promise.resolve();
                                      } else {
                                        if (
                                          /^(?![\d]+$)(?![a-zA-Z]+$)(?![@$!%*#_~?&^]+$)[\da-zA-Z@$!%*#_~?&^]{8,16}$/.test(
                                            value,
                                          )
                                        ) {
                                          if (getFieldValue('password') !== value) {
                                            return Promise.resolve();
                                          } else {
                                            return Promise.reject(new Error('新密码不能与旧密码一致！'));
                                          }
                                        } else {
                                          return Promise.reject(
                                            new Error(
                                              '密码必须是8-16位英文字母、数字、字符组合，且字符仅包含@$!%*#_~?&^',
                                            ),
                                          );
                                        }
                                      }
                                    },
                                  }),
                                ]}
                        >
                            <Input.Password allowClear placeholder="密码必须是8-16位英文字母、数字、字符组合" />
                        </Form.Item>
                        <Form.Item
                            name="checkPassword"
                            label="确认密码"
                            rules={[
                                {
                                required: true,
                                message: '请输入新密码！',
                                },
                                ({ getFieldValue }) => ({
                                validator(_, value) {
                                    if (!value) {
                                    return Promise.resolve();
                                    } else {
                                    if (getFieldValue('newPassword') !== value) {
                                        return Promise.reject(new Error('两次密码输入的不一致'));
                                    } else {
                                        return Promise.resolve();
                                    }
                                    }
                                },
                                }),
                            ]}
                            // validateTrigger={['onBlur', 'onChange']}
                            >
                            <Input.Password allowClear placeholder="请输入" />
                            </Form.Item>
                    </Form>
                </Spin>
            </Modal>
        )
    }
}
export default ChangePsd