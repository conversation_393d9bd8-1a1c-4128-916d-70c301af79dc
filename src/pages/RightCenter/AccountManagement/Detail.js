import React, { useEffect, useState, createContext } from 'react';

import { useSelector } from 'react-redux';
import { Table, message, Button, Descriptions, Row, Col, Spin, Tabs, Divider, Radio, Image, Modal } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import './Detail.less';
import { getDataType } from '@/utils';
import history from '@/utils/history';
import { get, post } from '../../../utils/request';
import allUrl from '../../../utils/url';
import moment from 'moment';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import { DecryptByAES } from '@/components/Public/Decrypt';
import PublicTooltip from '@/components/Public/PublicTooltip';
import KeepTable from './KeepTable';
import NoData from '../../../assets/img/noData.png';
import { PlusOutlined } from '@ant-design/icons';
import ChangeAccountInfo from './ChangeAccountInfo';
import LO_REDModal from './LO_REDModal';
import ChangeDeliveryTime from './ChangeDeliveryTime';
import RelevancePayGiveTable from './RelevancePayGiveTable';
import PackageDetail from './PackageDetail';
import UnbindRight from './UnbindRight';
import PublicTableQuery from '@/components/Public/PublicTableQuery';
import PartsList from './PartsList';
import styles from '../antd.module.less';
import Logs from '../components/Logs';
// import { saveAs } from 'file-saver'
import { roleJudgment } from '@/utils/authority';
import LoadTipsModal from './loadTipsModal/Index';

export const InjectMessage = createContext(null);
export const KeepTableInject = createContext(null);

const { TabPane } = Tabs;
const callback = key => {
  console.log(key);
};

const orderSourceMap = {
  1: 'DMS',
  2: '手动创建',
};
const orderCreateSourceMap = {
  1: 'DMS(VMall)',
  2: 'DMS(PMall)',
  3: '营销云(JKSales)',
  4: '人工创建权益账户',
  5: '未知',
  null: '-',
};
const displayMap = {
  0: '展示',
  1: '不展示',
};
const AccountManagementDetail = props => {
  const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {};
  const [accountNo, setAccountNo] = useState(null);
  const [locationParmas, setLocationParmas] = useState({});
  const [Type, setType] = useState('');
  const [isItemLabel, setIsItemLabel] = useState(false);
  const [loading, setLoading] = useState(false);
  const [listLoading, setListLoading] = useState(false);
  const [areasArr, setAreasArr] = useState([]);
  const [dealersArr, setdealersArr] = useState([]);
  const [carDown, ChangeCarDown] = useState(true);
  const [tabType, setTabType] = useState(1);
  const [orderDown, ChangeOrderDown] = useState(true);
  const [baseDown, ChangeBaseDown] = useState(false);
  const [payDown, ChangePayDown] = useState(false);
  const [giveDown, ChangeGiveDown] = useState(false);
  const [gainDown, ChangeGainDown] = useState(false);
  const [driverDown, ChangeDriverDown] = useState(false); //驾车行权益
  const [boutiqueDown, ChangeBoutiqueDown] = useState(false); //精品权益
  const [huaWeiDown, ChangeHuaWeiDown] = useState(false); //华为权益
  const [traceDown, ChangeTraceDown] = useState(false); //溯源权益
  const [reportDown1, ChangeReportDown1] = useState(false);
  const [reportDown2, ChangeReportDown2] = useState(false);
  const [reportDown3, ChangeReportDown3] = useState(false); //精品核销信息
  const [dataSource2, changeDataSource2] = useState([]);
  const [dataSource3, changeDataSource3] = useState([]);
  const [dataSource4, changeDataSource4] = useState([]);
  const [dataSource5, changeDataSource5] = useState([]);
  const [dataSource6, changeDataSource6] = useState([]);
  const [dataSource7, changeDataSource7] = useState([]);
  const [dataSource8, changeDataSource8] = useState([]); //增值权益工时核销
  const [dataSource9, changeDataSource9] = useState([]); //增值权益配件核销
  const [dataSource10, changeDataSource10] = useState([]); //精品权益配件核销
  const [dataSourceDriver1, changeDataSourceDriver1] = useState([]); //驾行权益工时核销
  const [dataSourceDriver2, changeDataSourceDriver2] = useState([]); //驾行权益配件核销
  const [baseDetail, setBaseDetail] = useState([]);
  const [payDetail, setPayDetail] = useState([]);
  const [giveDetail, setGiveDetail] = useState([]);
  const [gainDetail, setGainDetail] = useState([]);
  const [driverDetail, setDriverDetail] = useState([]); //驾车行权益
  const [boutiqueDetail, setBoutiqueDetail] = useState([]); //精品权益
  const [huaWeiDetail, setHuaWeiDetail] = useState([]); //华为权益
  const [traceDetail, setTraceDetail] = useState([]); //溯源权益
  const [detail, setDetail] = useState({});
  const [title, setTitle] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isModalVisible2, setIsModalVisible2] = useState(false);
  const [isLO_REDModalVisible, setIsLO_REDModalVisible] = useState(false); //注销还原弹窗
  // const [isReductionModalVisible, setIsReductionModalVisible] = useState(false);//弹窗
  const [isTimeModalVisible, setIsTimeModalVisible] = useState(false); //修改订单支付时间
  const [isUnbindModalVisible, setIsUnbindModalVisible] = useState(false); //解绑弹窗
  const [unbindList, setUnbindList] = useState([]); //解绑弹窗-弹窗内容
  const [unbindType, setUnbindType] = useState('');
  const [relevanDisable, setRelevanDisable] = useState(true);
  const [partsDown, ChangePartsDown] = useState(false);
  const [partsDataSource, changePartsDataSource] = useState([]);
  const [operDown, ChangeOperDown] = useState(false);
  const [operDataSource, changeOperDataSource] = useState([]);
  const [buttonType, setButtonType] = useState(null);
  const [hasVin, setHasVin] = useState(true);
  const [tabArr, setTabArr] = useState([]); //车主tab标签
  const [activeIndex, setActiveIndex] = useState(0);
  const [changeButton, setChangeButton] = useState(true);
  const [defaultpartQuery, setDefaultpartQuery] = useState({});

  const [current, setCurrent] = useState(true);
  const [ownerNum, setOwnerNum] = useState('');
  const [logImgVisible, setLogImgVisible] = useState({}); //V13操作日志图片show
  const [partListCount, setPartListCount] = useState(0);

  const [isLoadTipsModalOpen, setIsLoadTipsModalOpen] = useState(false); //下载弹窗提示
  //创建context 给初始值
  const getYearUnitName = key => {
    return (
      {
        year: '年',
        month: '月',
        day: '日',
      }[key] || ''
    );
  };
  useEffect(() => {
    if (partsDown) {
      console.log(detail.vin);
      get(allUrl.AccountManagement.getAccountPartListCount, { vin: detail.vin }).then(res => {
        const { success, total } = res;
        if (success) {
          setPartListCount(total);
        }
      });
    }
  }, [partsDown]);
  useEffect(() => {
    const locationParmas = props.match.params.data ? JSON.parse(DecryptByAES(props.match.params.data)) : {};
    setLocationParmas(locationParmas);
    setAccountNo(locationParmas.accountNo);
    setType(locationParmas.Type);
  }, [props.match.params.data, userInfo]);
  //车主任期标签页查询
  useEffect(() => {
    if (!accountNo) {
      return;
    }
    let query = { accountNo: accountNo };
    setLoading(true);
    get(allUrl.AccountManagement.getAccountOwnerList, { ...query }).then(res => {
      if (res.success) {
        let Dt = res.resp;
        setTabArr(Dt);
        setOwnerNum(Dt[0]?.ownerNum || 1);
      } else {
        // message.error(res.msg)
      }
      setLoading(false);
    });
  }, [accountNo]);

  const promiseApi = params => {
    return new Promise((resolve, reject) => {
      get(allUrl.AccountManagement.getAccountPurchasedList, { ...params }).then(res => {
        if (res.success) {
          resolve(res.resp);
        }
      });
    });
  };
  //精品权益核销
  const keepBoutiqueDetailContent = sort => {
    setLoading(true);
    const params = { type: '6', accountNo: detail.accountNo, sort };
    promiseApi(params).then(res => {
      setLoading(false);
      changeDataSource10(res);
    });
  };
  // 权益核销信息
  const keepDetailContent = sort => {
    setLoading(true);
    if (userInfo) {
      let params = { type: '1', accountNo: detail.accountNo, sort };
      let params2 = { type: '2', accountNo: detail.accountNo, sort };
      let params3 = { type: '4', accountNo: detail.accountNo, sort };
      let params4 = { type: '5', accountNo: detail.accountNo, sort }; //增值权益
      let params5 = { type: '7', accountNo: detail.accountNo, sort }; //驾行权益
      Promise.allSettled([
        promiseApi(params),
        promiseApi(params2),
        promiseApi(params3),
        promiseApi(params4),
        promiseApi(params5),
      ]).then(res => {
        setLoading(false);
        res.map((item, index) => {
          if (item.status === 'fulfilled') {
            if (index === 0) {
              if (sort === 'order') {
                changeDataSource2(item.value);
              } else {
                changeDataSource5(item.value);
              }
            } else if (index === 1) {
              if (sort === 'order') {
                changeDataSource3(item.value);
              } else {
                changeDataSource6(item.value);
              }
            } else if (index === 2) {
              if (sort === 'order') {
                changeDataSource4(item.value);
              } else {
                changeDataSource7(item.value);
              }
            } else if (index === 3) {
              if (sort === 'order') {
                changeDataSource8(item.value);
              } else {
                changeDataSource9(item.value);
              }
            } else if (index === 4) {
              if (sort === 'order') {
                changeDataSourceDriver1(item.value);
              } else {
                changeDataSourceDriver2(item.value);
              }
            }
          }
        });
      });
    }
  };
  //基础权益信息
  const baseDetailContent = async () => {
    let params = { type: '1', accountNo: detail.accountNo, ownerNum: ownerNum, current: current };
    const res = await get(allUrl.AccountManagement.getAccountPackObj, { ...params });
    if (res.success) {
      // if (res.resp.length) {
      res.resp.map((item, index) => (item.key = index + 1));
      setBaseDetail(res.resp);
      // }
      return Promise.resolve(res.resp);
    } else {
      // message.error(res.msg)
    }
    setLoading(false);
    return Promise.resolve(false);
  };

  const vinHandle = detail => {
    setRelevanDisable(false);
    // 判断是否有VIN
    if (!detail.vin) {
      setRelevanDisable(true);
    }
    // else {
    //     setRelevanDisable(false)
    // }
    // 判断当前状态住否是注销
    if (detail.deactivateStatus) {
      setRelevanDisable(true);
    }
    // else {
    //     setRelevanDisable(false)
    // }
  };

  //付费权益信息
  const payDetailContent = async reload => {
    vinHandle(detail);

    // 如果先点击了解绑按钮，则先请求接口,返回false为了下面判断
    if (!reload && payDetail.length) {
      return Promise.resolve(false);
    }
    let params = { type: '2', accountNo: detail.accountNo, ownerNum: ownerNum, current: current };
    const res = await get(allUrl.AccountManagement.getAccountPackObj, { ...params });
    if (res.success) {
      // if (res.resp.length) {
      res.resp.map((item, index) => (item.key = index + 1));
      setPayDetail(res.resp);
      // }
      return Promise.resolve(res.resp);
    } else {
      // message.error(res.msg)
    }
    setLoading(false);
    return Promise.resolve(false);
  };
  //赠送权益信息  630
  const giveDetailContent = async reload => {
    vinHandle(detail);
    // 如果先点击了解绑按钮，则先请求接口,返回false为了下面判断
    if (!reload && giveDetail.length) {
      return Promise.resolve(false);
    }
    let params = { type: '4', accountNo: detail.accountNo, ownerNum: ownerNum, current: current };
    const res = await get(allUrl.AccountManagement.getAccountPackObj, { ...params });
    // .then(res => {
    if (res.success) {
      // if (res.resp.length) {
      res.resp.map((item, index) => (item.key = index + 1));
      setGiveDetail(res.resp);
      // }
      return Promise.resolve(res.resp);
    } else {
      // message.error(res.msg)
    }
    setLoading(false);
    return Promise.resolve(false);
    // })
  };

  //增值权益信息  V16
  const gainDetailContent = async reload => {
    vinHandle(detail);
    // 如果先点击了解绑按钮，则先请求接口,返回false为了下面判断
    if (!reload && gainDetail.length) {
      return Promise.resolve(false);
    }
    let params = { type: '5', accountNo: detail.accountNo, ownerNum: ownerNum, current: current };
    const res = await get(allUrl.AccountManagement.getAccountPackObj, { ...params });
    // .then(res => {
    if (res.success) {
      // if (res.resp.length) {
      res.resp.map((item, index) => (item.key = index + 1));
      setGainDetail(res.resp);
      // }
      return Promise.resolve(res.resp);
    } else {
      // message.error(res.msg)
    }
    setLoading(false);
    return Promise.resolve(false);
    // })
  };

  //精品权益信息  V20.0
  const boutiqueDetailContent = async reload => {
    let params = { type: '6', accountNo: detail.accountNo, ownerNum: ownerNum, current: current };
    const res = await get(allUrl.AccountManagement.getAccountPackObj, { ...params });
    if (res.success) {
      res.resp.map((item, index) => (item.key = index + 1));
      setBoutiqueDetail(res.resp);
      return Promise.resolve(res.resp);
    }
    setLoading(false);
    return Promise.resolve(false);
  };

  //华为权益信息  V28.0.2
  const huaWeiDetailContent = async reload => {
    let params = { accountNo: detail.accountNo, ownerNum: ownerNum, source: 1 };
    const res = await get(allUrl.AccountManagement.getExternalBenefits, { ...params });
    if (res.success) {
      res.resp.map((item, index) => (item.key = index + 1));
      setHuaWeiDetail(res.resp);
      return Promise.resolve(res.resp);
    }
    setLoading(false);
    return Promise.resolve(false);
  };

  //溯源权益信息  V28.0.5
  const traceDetailContent = async reload => {
    let params = { type: '8', accountNo: detail.accountNo, ownerNum: ownerNum, current: current };
    const res = await get(allUrl.AccountManagement.getAccountPackObj, { ...params });
    if (res.success) {
      res.resp.map((item, index) => (item.key = index + 1));
      setTraceDetail(res.resp);
      return Promise.resolve(res.resp);
    }
    setLoading(false);
    return Promise.resolve(false);
  };

  //驾行权益信息  V22
  const driverDetailContent = async reload => {
    vinHandle(detail);
    // 如果先点击了解绑按钮，则先请求接口,返回false为了下面判断
    if (!reload && gainDetail.length) {
      return Promise.resolve(false);
    }
    let params = { type: '7', accountNo: detail.accountNo, ownerNum: ownerNum, current: current };
    const res = await get(allUrl.AccountManagement.getAccountPackObj, { ...params });
    // .then(res => {
    if (res.success) {
      // if (res.resp.length) {
      res.resp.map((item, index) => (item.key = index + 1));
      setDriverDetail(res.resp);
      // }
      return Promise.resolve(res.resp);
    } else {
      // message.error(res.msg)
    }
    setLoading(false);
    return Promise.resolve(false);
    // })
  };
  //  获取详情接口
  const getDetail = () => {
    if (!accountNo) {
      return;
    }
    /** 请求时候重置权益内容，方便注销之后重新请求接口*/
    ChangeBaseDown(false);
    ChangePayDown(false);
    ChangeGiveDown(false);
    ChangeGainDown(false);
    ChangeBoutiqueDown(false);
    ChangeDriverDown(false);
    ChangeHuaWeiDown(false);
    ChangeTraceDown(false);
    /** end  */
    let query = { accountNo: accountNo, ownerNum: ownerNum, current: current };
    // setLoading(true)
    setListLoading(true);
    get(allUrl.AccountManagement.getAccountObj, { ...query }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        vinHandle(Dt);
        setDetail(Dt);
        //  如果没有vin值， 配件列表展开按钮禁止点击 start
        const vin = Dt.vin;
        console.log(vin, 'vin111');
        if (!vin) {
          setHasVin(false);
        }
        //如果没有vin  配件列表展开按钮禁止点击 end
      } else {
        // message.error(res.msg)
      }
      setListLoading(false);
    });
  };
  useEffect(() => {
    getDetail();
  }, [ownerNum]);
  const reloadChangeInfo = () => {
    let query = { accountNo: accountNo, current: current };
    setLoading(true);
    setListLoading(true);
    // 将操作信息收回来
    ChangeOperDown(false);

    get(allUrl.AccountManagement.getAccountObj, { ...query }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        vinHandle(Dt);
        setDetail(Dt);
      } else {
        // message.error(res.msg)
      }
      setLoading(false);
      setListLoading(false);
    });
  };

  // 解绑之后刷新权益详情（付费权益和赠送权益、增值权益）、更新权益详情
  const reloadChangeRightInfo = async type => {
    // 将操作信息收回来
    ChangeOperDown(false);
    const res = type || unbindType;
    switch (res) {
      case 'base':
        // 基础权益不需要解绑按钮
        baseDetailContent();
        break;
      case 'pay':
        const res1 = await payDetailContent('reload');
        setUnbindList(res1 || payDetail || []);
        break;
      case 'give':
        const res2 = await giveDetailContent('reload');
        setUnbindList(res2 || giveDetail || []);
        break;
      case 'gain':
        const res3 = await gainDetailContent('reload');
        setUnbindList(res3 || gainDetail || []);
        break;
      case 'driver':
        const res4 = await driverDetailContent('reload');
        setUnbindList(res4 || driverDetail || []);
        break;
    }
  };
  // 关联成功后刷新权益详情（付费权益和赠送权益）
  const reloadRelevanceInfo = async val => {
    // 将操作信息收回来
    ChangeOperDown(false);
    switch (title.title) {
      case '付费权益包关联':
        const res1 = await payDetailContent('reload');
        setUnbindList(res1 || payDetail || []);
        break;
      case '赠送权益包关联':
        const res2 = await giveDetailContent('reload');
        setUnbindList(res2 || giveDetail || []);
        break;
      case '增值权益包关联':
        const res3 = await gainDetailContent('reload');
        setUnbindList(res3 || gainDetail || []);
        break;
      case '驾行安心包关联':
        const res4 = await driverDetailContent('reload');
        setUnbindList(res4 || driverDetail || []);
        break;
    }
  };

  const onCancel = () => {
    // history.push('/RightCenter/AccountManagement')
    history.goBack();
  };

  // 关闭弹窗
  const handleCancel = () => {
    const modalCollections = [
      'setIsModalVisible',
      'setIsModalVisible2',
      'setIsTimeModalVisible',
      'setIsUnbindModalVisible',
      'setIsLO_REDModalVisible',
    ];
    modalCollections.map(i => eval(i)(false));
  };
  // 弹窗
  const showModal = (type, params) => {
    switch (type) {
      case 'setIsLO_REDModalVisible':
        getBenefits(params);
        break;
      default:
        getDataType(type) === 'String' && eval(type)(true);
        break;
    }
    console.log('leicj-------------', params);
    if (getDataType(params) === 'Object') {
      // console.log(params);
      setTitle(params);
    }
  };
  const [examine, setExamine] = useState(false); //是否需要证明文件
  // 判断该账户内权益明细是否已经核销过或正在核销
  const getBenefits = params => {
    // console.log(dataSourceForm);

    const tipsText =
      params.type === 'LO'
        ? '因涉及到权益核销的费用问题，无法注销，需联系权益中心管理员处理'
        : '因涉及到权益核销的费用问题，无法还原至待交付状态，需联系权益中心管理员处理';
    get(allUrl.AccountManagement.getBenefits({ accountNo: detail.accountNo }), '').then(res => {
      const { success, resp } = res;
      console.log(resp);

      if (success) {
        /**
         * hasUsed: 是否有已使用或者正在使用的权益
         * hasPaidOrGiftBenefits: 是否有付费或者赠送权益
         */
        const { hasUsed, hasPaidOrGiftBenefits } = resp[0];

        if (hasUsed) {
          Modal.warning({
            title: tipsText,
            okText: '我知道了',
          });
          return;
        }
        if (hasPaidOrGiftBenefits) {
          // 已关联
          setExamine(true);
        }
        setIsLO_REDModalVisible(true);
      } else {
        message.warn(res.msg);
      }
    });
  };

  // 解绑权益包弹窗
  const showUnbindModal = async type => {
    switch (type) {
      case 'pay':
        const res1 = await payDetailContent();
        setUnbindList(res1 || payDetail || []);
        break;
      case 'give':
        const res2 = await giveDetailContent();
        setUnbindList(res2 || giveDetail || []);
        break;
      case 'gain':
        const res3 = await gainDetailContent();
        setUnbindList(res3 || gainDetail || []);
        break;
      case 'driver':
        const res4 = await driverDetailContent();
        setUnbindList(res4 || driverDetail || []);
        break;
    }
    setUnbindType(type);
    setIsUnbindModalVisible(true);
  };
  //830  配件列表接口
  const partsContent = params => {
    changePartsDataSource([]);

    get(allUrl.AccountManagement.getAccountPartList, { ...params }).then(res => {
      if (res.success) {
        if (res.resp.length) {
          res.resp.map((item, index) => (item.key = index + 1));
          changePartsDataSource(res.resp);
        }
      } else {
        // message.error(res.msg)
      }
      setLoading(false);
    });
  };

  const getFileObj = url => {
    const imgSuffixArr = ['png', 'jpg', 'jpeg', 'bmp', 'webp'];
    const fileSuffixPre = url.split('.').pop();
    const fileSuffix = fileSuffixPre.indexOf('?') > 0 ? fileSuffixPre.split('?')[0] : fileSuffixPre;
    const fileNamePre = url.split('/').pop();
    const fileName = fileNamePre.indexOf('?') > 0 ? fileNamePre.split('?')[0] : fileNamePre;
    return {
      imgSuffixArr,
      fileSuffix,
      fileName,
    };
  };

  const outputFile = (name, urls, index) => {
    return (
      <Col span={12} key={index} className="log-block">
        <div style={{ width: 70, whiteSpace: 'nowrap' }}>{name}：</div>
        <div>
          {urls.map((url, i) => {
            if (!url) return null;
            // console.log('leicj-------------', url);
            const { imgSuffixArr, fileSuffix, fileName } = getFileObj(url);
            if (imgSuffixArr.includes(fileSuffix)) {
              return (
                <div key={i} style={{ marginBottom: 5 }}>
                  <span className="log-span" onClick={() => setLogImgVisible({ [fileName]: true })}>
                    {fileName}
                  </span>
                  <Image
                    // width={20}
                    // style={{ display: 'none' }}
                    src={url}
                    preview={{
                      visible: logImgVisible[fileName] ?? false,
                      src: url,
                      onVisibleChange: value => {
                        setLogImgVisible({ [fileName]: value });
                      },
                    }}
                  />
                </div>
              );
            }
            if (fileSuffix === 'pdf') {
              return (
                <span className="log-span" key={i}>
                  <a href={url} target="_blank">
                    {fileName}
                  </a>
                </span>
              );
            }
            return (
              <span className="log-span" key={i} onClick={() => setIsLoadTipsModalOpen(true)}>
                <a href={url}>{fileName}</a>
              </span>
            );
          })}
        </div>
      </Col>
    );
  };

  //V13 操作信息操作内容展示
  const outputContent = content => {
    if (typeof content === 'object') {
      const arr = ['证明文件', '审核文件', '附件'];
      return Object.entries(content).map((k, index) => {
        //文件展示
        if (arr.includes(k[0])) {
          if (Array.isArray(k[1])) {
            return outputFile(k[0], k[1], index);
          }

          //单文件
          return outputFile(k[0], [k[1]], index);
        }

        //原因因字数过长，单独换行展示
        if (k[0].endsWith('原因')) {
          return (
            <Col span={23} key={index} className="log-block">
              <div style={{ width: 70 }}>{k[0]}：</div>
              <span className="log-word-break log-flex-1">{k[1]}</span>
            </Col>
          );
        }

        //权益包信息特殊处理，	"权益包信息": [{"关联权益包编码": "<packNo>","关联权益包版本号": "<packVersion>"}, {"关联权益包编码": "<packNo>","关联权益包版本号": "<packVersion>"}]
        if (['权益包信息'].includes(k[0])) {
          if (Array.isArray(k[1])) {
            return k[1].map(item => {
              return outputContent(item);
            });
          }
        }
        // if (k[0].endsWith('时间')) {
        //     return <Col span={8} key={index}>{k[0]}：<span>{k[1] ? moment(k[1]).format('YYYY-MM-DD HH:mm:ss') : '-'}</span></Col>
        // }
        //其他
        return (
          <Col span={12} key={index}>
            {k[0]}
            {k[1] !== '' ? '：' : ''}
            <span>{k[1]}</span>
          </Col>
        );
      });
    }

    return <Col span={24}>{content}</Col>;
  };
  const TabContent = (item, index) => {
    console.log(item, index);

    setActiveIndex(index);
    setCurrent(item.current);
    setOwnerNum(item.ownerNum);
    ChangeBaseDown(false);
    ChangePayDown(false);
    ChangeGiveDown(false);
    ChangeGainDown(false);
    ChangeBoutiqueDown(false);
    ChangeDriverDown(false);
    ChangeHuaWeiDown(false);
    ChangeTraceDown(false);

    setPayDetail([]);
    setGiveDetail([]);
    setGainDetail([]);
    setBoutiqueDetail([]);
    setDriverDetail([]);
    setHuaWeiDetail([]);
    setTraceDetail([]);

    if (item.current == false) {
      setChangeButton(false);
    } else {
      setChangeButton(true);
    }
    let query = { accountNo: accountNo, ownerNum: item.ownerNum, current: item.current };
    setLoading(true);
    setListLoading(true);
    get(allUrl.AccountManagement.getAccountObj, { ...query }).then(res => {
      if (res.success) {
        let Dt = res.resp[0];
        vinHandle(Dt);
        setDetail(Dt);
        //  如果没有vin值， 配件列表展开按钮禁止点击 start
        const vin = Dt.vin;
        console.log(vin, 'vin111');
        if (!vin) {
          setHasVin(false);
        }
        //如果没有vin  配件列表展开按钮禁止点击 end
      } else {
        // message.error(res.msg)
      }
      setLoading(false);
      setListLoading(false);
    });
  };
  const onTabChange = () => {};
  const onPartSearch = values => {
    setLoading(true);
    let { partNo, partName, partBelongs, partMode, partType } = values;
    let params = {
      vin: detail.vin,
      partNo,
      partName,
      partType,
      partBelongs,
      partMode,
    };
    setDefaultpartQuery(values);
    partsContent(params);
  };
  const partsearchList = [
    // {
    //     label: '配件总数:', name: 'partNo', type: 'Text', colSpan: 3, data: () => {
    //         return <>
    //             <span style={{color:'#5095D5'}}>{partListCount}</span>个
    //         </>
    // } },
    { label: '配件编码', name: 'partNo', type: 'Input', placeholder: '请输入配件编码', colSpan: 6 },
    { label: '配件名称', name: 'partName', type: 'Input', placeholder: '请输入配件名称', colSpan: 6 },
    {
      label: '配件包修类型',
      name: 'partType',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: [
        { name: '整车件', value: '1' },
        { name: '核心零配件', value: '2' },
        { name: '易损易耗件', value: '3' },
        { name: '不保件', value: '4' },
        // { name: 'R特性配件', value: '5' },
      ],
    },
    {
      label: '配件包修所属',
      name: 'partBelongs',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: [
        { name: '原车件', value: '1' },
        { name: '配件', value: '2' },
        { name: '延保件', value: '3' },
      ],
    },
    {
      label: '保修情况',
      name: 'partMode',
      type: 'Select',
      placeholder: '请选择',
      colSpan: 6,
      data: [
        { name: '保内', value: '1' },
        { name: '保外', value: '2' },
      ],
    },
  ];
  const createMethods = [
    { name: '系统创建', value: 1 },
    { name: '手动创建', value: 2 },
    { name: '邮件创建', value: 3 },
  ];

  return detail ? (
    <>
      <Row className="detail-btn-box">
        <Col className="text" style={{ marginRight: -10 }}>
          {tabArr.map((item, index) => (
            <span
              className={`car-btn ${index == activeIndex ? 'active' : null}`}
              onClick={() => TabContent(item, index)}
              key={index}
            >
              {item.value}
            </span>
          ))}
        </Col>
      </Row>
      <div className="detail-PublicList">
        <Spin spinning={listLoading}>
          <div className="detail-overflow">
            <div className="detail-box">
              <div className="tow-tab">
                <div className={`tab-item ${tabType === 1 ? 'active' : null}`} onClick={() => setTabType(1)}>
                  权益账户信息
                </div>
                <div className={`tab-item ${tabType === 2 ? 'active' : null}`} onClick={() => setTabType(2)}>
                  权益核销信息
                </div>
              </div>
              <div className="tab-box">
                {tabType === 1 && (
                  <>
                    <div className="tableData" style={{ marginTop: 0 }}>
                      <div className="top-box">
                        <div className="left-item margin-left">账户信息</div>
                        <div className="btns">
                          <div className="right-item">
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_LOGOUT') &&
                              !relevanDisable &&
                              tabArr.length === 1 && (
                                <Col>
                                  {' '}
                                  <Button
                                    className="border-btn"
                                    onClick={() =>
                                      showModal('setIsLO_REDModalVisible', { type: 'LO', title: '权益账户注销' })
                                    }
                                  >
                                    注销
                                  </Button>
                                </Col>
                              )}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_REDUCTION') &&
                              !relevanDisable &&
                              tabArr.length === 1 && (
                                <Col>
                                  {' '}
                                  <Button
                                    className="border-btn"
                                    onClick={() =>
                                      showModal('setIsLO_REDModalVisible', {
                                        type: 'RED',
                                        title: '权益账户还原至待交付状态',
                                      })
                                    }
                                  >
                                    还原
                                  </Button>
                                </Col>
                              )}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_ACCOUNTCHANGE') && changeButton ? (
                              <Button
                                disabled={detail.deactivateStatus}
                                type="primary"
                                onClick={() => showModal('setIsModalVisible')}
                              >
                                更改
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      <div className="detailPanel">
                        {/* <Row className='tableTitle1' style={{ paddingBottom: 15, paddingTop: 0, justifyContent: 'space-between' }}>
                                                <Col className='text'>权益账户编码 : &nbsp;&nbsp;<Descriptions.Item label=''>{detail.accountNo || '-'}</Descriptions.Item></Col>
                                                <Col className='text'>权益账户编码 : &nbsp;&nbsp;<Descriptions.Item label=''>{detail.accountNo || '-'}</Descriptions.Item></Col>
                                            </Row> */}
                        <Descriptions>
                          <Descriptions.Item label="权益账户编码">{detail.accountNo || '-'}</Descriptions.Item>
                          <Descriptions.Item label="账户创建时间">
                            {detail.createTime ? moment(detail.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
                          </Descriptions.Item>
                          <Descriptions.Item label="账户创建类型">
                            {detail.orderSource
                              ? createMethods.filter(i => i.value == detail.orderSource)[0]['name']
                              : '-'}
                          </Descriptions.Item>
                        </Descriptions>
                        <Descriptions>
                          <Descriptions.Item label="购车人手机号">{detail.carBuyerPhone || '-'}</Descriptions.Item>
                          <Descriptions.Item label="购车人姓名">{detail.carBuyerName || '-'}</Descriptions.Item>
                        </Descriptions>
                        <Descriptions>
                          <Descriptions.Item label="车主手机号">{detail.carOwnerPhone || '-'}</Descriptions.Item>
                          <Descriptions.Item label="车主姓名">{detail.carOwnerName || '-'}</Descriptions.Item>
                        </Descriptions>
                        <Descriptions>
                          <Descriptions.Item label="车联网手机号">{detail.certificationPhone || '-'}</Descriptions.Item>
                          <Descriptions.Item label="实名认证姓名">{detail.certificationName || '-'}</Descriptions.Item>
                        </Descriptions>
                        <Descriptions>
                          <Descriptions.Item label="溯源手机号">{detail.tracePhone || '-'}</Descriptions.Item>
                        </Descriptions>
                      </div>
                    </div>
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {carDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeCarDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeCarDown(true);
                                }}
                                alt=""
                              />
                            )}
                            <span>车辆信息</span>
                          </div>
                        </div>
                      </div>

                      {carDown ? (
                        <>
                          {detail.carObj ? (
                            <>
                              {
                                <div>
                                  <div
                                    style={{
                                      paddingTop: 20,
                                      paddingLeft: 20,
                                      paddingBottom: 20,
                                      backgroundColor: '#F1F8FF',
                                    }}
                                  >
                                    <div>
                                      <Descriptions>
                                        <Descriptions.Item label="车辆VIN">
                                          {detail.carObj.vin || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="车型">
                                          {detail.carObj.carTypeName || '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="车型市场名称">
                                          {detail.carObj.carMarketName || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="车辆市场类型">
                                          {detail.carObj.carMarketTypeName || '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="内饰颜色">
                                          {detail.carObj.interiorColor || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="车身颜色">
                                          {detail.carObj.bodyColor || '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="基础配置">
                                          {detail.carObj.basicConfig || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="选配">
                                          {detail.carObj.optionalConfig || '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="车辆销售类型">
                                          {detail.carObj.transferTypeName || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="销售门店">
                                          {detail.carObj.salesStoreName || '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="动力方式">
                                          {detail.carObj.powerModelCode === 'EV' ? '纯电式电车' : '增程式电动'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="备注">
                                          {detail.carObj.remark || '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                    </div>
                                  </div>
                                </div>
                              }
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {orderDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeOrderDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeOrderDown(true);
                                }}
                                alt=""
                              />
                            )}
                            <span>订单信息</span>
                          </div>
                          {/* 首任车主tabArr.length===1 已交付!relevanDisable*/}
                          {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_ORDERCHANGE') && (
                            <div className="right-item">
                              <Button
                                type="primary"
                                disabled={!(!relevanDisable && tabArr.length === 1)}
                                onClick={() => showModal('setIsTimeModalVisible')}
                              >
                                更改
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                      {orderDown ? (
                        <div className="tab-content">
                          <Tabs
                            defaultActiveKey="2"
                            onChange={callback}
                            type="card"
                            className={styles['custom-ant-tabs']}
                          >
                            <TabPane tab="意向订单" key="1">
                              {detail.bookOrderList && detail.bookOrderList.length ? (
                                <>
                                  {detail.bookOrderList &&
                                    detail.bookOrderList.length &&
                                    detail.bookOrderList.map((item, index) => (
                                      <div key={index} className="content-div">
                                        <Descriptions>
                                          <Descriptions.Item label="意向订单编号">
                                            {item.orderNo || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="订单金额">{item.price || '-'}元</Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="支付时间">
                                            {item.deliverDate || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="同步时间">{item.syncDate || '-'}</Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="订单来源">
                                            {orderCreateSourceMap[detail.orderCreateSource]}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        {index !== detail.bookOrderList.length - 1 && (
                                          <div style={{ paddingRight: 25 }}>
                                            <Divider style={{ marginTop: 0, backgroundColor: '#EFEFEF' }} />
                                          </div>
                                        )}
                                      </div>
                                    ))}
                                </>
                              ) : (
                                <div className="content-div content-div-no">
                                  {' '}
                                  <img style={{ width: 124, height: 104 }} src={NoData} />
                                  <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                                </div>
                              )}
                            </TabPane>
                            <TabPane tab="正式订单" key="2">
                              {detail.formalOrderList && detail.formalOrderList.length ? (
                                <>
                                  {detail.formalOrderList &&
                                    detail.formalOrderList.length &&
                                    detail.formalOrderList.map((item, index) => (
                                      <div key={index} className="content-div">
                                        <Descriptions>
                                          <Descriptions.Item label="金康订单编号">
                                            {item.formalOrderNo || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="华为订单编号">
                                            {item.hwOrderNo || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="订单金额">{item.price || '-'}元</Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="支付时间">
                                            {item.deliverDate || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="同步时间">{item.syncDate || '-'}</Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="订单来源">
                                            {orderCreateSourceMap[detail.orderCreateSource]}
                                          </Descriptions.Item>
                                        </Descriptions>

                                        {index !== detail.formalOrderList.length - 1 && (
                                          <div style={{ paddingRight: 25 }}>
                                            <Divider style={{ marginTop: 0, backgroundColor: '#EFEFEF' }} />
                                          </div>
                                        )}
                                      </div>
                                    ))}
                                </>
                              ) : (
                                <div className="content-div content-div-no">
                                  {' '}
                                  <img style={{ width: 124, height: 104 }} src={NoData} />
                                  <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                                </div>
                              )}
                            </TabPane>
                            <TabPane tab="交付订单" key="3">
                              {detail.deliverOrderList && detail.deliverOrderList.length ? (
                                <>
                                  {detail.deliverOrderList &&
                                    detail.deliverOrderList.length &&
                                    detail.deliverOrderList.map((item, index) => (
                                      <div key={index} className="content-div">
                                        <Descriptions>
                                          <Descriptions.Item label="交付订单编号">
                                            {item.orderNo || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        {/* <Descriptions>
                                                                    <Descriptions.Item label='订单金额'>{item.price || '-'}元</Descriptions.Item>
                                                                </Descriptions> */}
                                        <Descriptions>
                                          <Descriptions.Item label="批售订单编号">
                                            {item.purchaseOderNo || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="交付时间">
                                            {item.deliverDate || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="同步时间">{item.syncDate || '-'}</Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="订单来源">
                                            {orderCreateSourceMap[detail.orderCreateSource]}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        {index !== detail.deliverOrderList.length - 1 && (
                                          <div style={{ paddingRight: 25 }}>
                                            <Divider style={{ marginTop: 0, backgroundColor: '#EFEFEF' }} />
                                          </div>
                                        )}
                                      </div>
                                    ))}
                                </>
                              ) : (
                                <div className="content-div content-div-no">
                                  {' '}
                                  <img style={{ width: 124, height: 104 }} src={NoData} />
                                  <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                                </div>
                              )}
                            </TabPane>
                            <TabPane tab="销售订单" key="4">
                              {detail.saleOrderList && detail.saleOrderList.length ? (
                                <>
                                  {detail.saleOrderList &&
                                    detail.saleOrderList.length &&
                                    detail.saleOrderList.map((item, index) => (
                                      <div key={index} className="content-div">
                                        <Descriptions>
                                          <Descriptions.Item label="销售订单编号">
                                            {item.saleOrderNo || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="订单金额">{item.price || '-'}元</Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="支付时间">
                                            {item.deliverDate || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="同步时间">{item.syncDate || '-'}</Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="订单来源">
                                            {orderCreateSourceMap[detail.orderCreateSource]}
                                          </Descriptions.Item>
                                        </Descriptions>
                                        <Descriptions>
                                          <Descriptions.Item label="销售渠道">
                                            {item.saleChannel || '-'}
                                          </Descriptions.Item>
                                        </Descriptions>

                                        {index !== detail.saleOrderList.length - 1 && (
                                          <div style={{ paddingRight: 25 }}>
                                            <Divider style={{ marginTop: 0, backgroundColor: '#EFEFEF' }} />
                                          </div>
                                        )}
                                      </div>
                                    ))}
                                </>
                              ) : (
                                <div className="content-div content-div-no">
                                  {' '}
                                  <img style={{ width: 124, height: 104 }} src={NoData} />
                                  <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                                </div>
                              )}
                            </TabPane>
                            {detail.secondCarInfoDTO && detail.secondCarInfoDTO.length
                              ? detail.secondCarInfoDTO.length
                              : null}
                            {/* 二手车订单页签展示：（1）当权益账户只有首任车主时，不展示；（2）当权益账户有首任车主、第二任以上车主时，除首任车主外其余任数车主展示相应的二手车订单信息； */}
                            {tabArr.length === 1 || ownerNum === 1 ? null : (
                              <TabPane tab="二手车订单" key="5">
                                {detail.secondCarInfoDTO ? (
                                  <>
                                    <div className="content-div">
                                      <Descriptions>
                                        <Descriptions.Item label="过户时间">
                                          {detail.secondCarInfoDTO ? detail.secondCarInfoDTO.transferDate : '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="同步时间">
                                          {detail.secondCarInfoDTO ? detail.secondCarInfoDTO.syncDate : '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="过户类型">
                                          {detail.secondCarInfoDTO ? detail.secondCarInfoDTO.transferType : '-'}
                                        </Descriptions.Item>
                                      </Descriptions>
                                      <Descriptions>
                                        <Descriptions.Item label="订单来源">
                                          {orderCreateSourceMap[detail.orderCreateSource]}
                                        </Descriptions.Item>
                                      </Descriptions>
                                    </div>
                                  </>
                                ) : (
                                  <div className="content-div content-div-no">
                                    {' '}
                                    <img style={{ width: 124, height: 104 }} src={NoData} />
                                    <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                                  </div>
                                )}
                              </TabPane>
                            )}
                          </Tabs>
                        </div>
                      ) : null}
                    </div>
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {baseDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeBaseDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeBaseDown(true);
                                  baseDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>基础权益</span>
                          </div>
                        </div>
                      </div>
                      {baseDown ? (
                        <>
                          {baseDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {baseDetail &&
                                    baseDetail.length &&
                                    baseDetail.map((item, i) => (
                                      <>
                                        <TabPane tab={`权益包${i + 1}`} key={i}>
                                          <InjectMessage.Provider
                                            value={{
                                              accountNo,
                                              type: 'base',
                                              reloadChangeRightInfo,
                                              isCurrent: changeButton,
                                            }}
                                          >
                                            <PackageDetail detail={item} callbackFn={callback} />
                                          </InjectMessage.Provider>
                                        </TabPane>
                                      </>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>

                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {payDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangePayDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangePayDown(true);
                                  payDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>付费权益</span>
                          </div>
                          <div className="right-item">
                            {/* 未交付并且当前不是已注销状态 */}
                            {relevanDisable && !detail.deactivateStatus ? (
                              <p style={{ fontSize: 15, marginLeft: '20px', color: 'red' }}>未交付车辆无法关联权益包</p>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_RELEVANCE') && changeButton ? (
                              <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                disabled={relevanDisable}
                                style={{ marginLeft: 20, zIndex: 100 }}
                                onClick={() => {
                                  showModal('setIsModalVisible2', { type: 'PayRelation', title: '付费权益包关联' });
                                }}
                              >
                                关联
                              </Button>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_UNBIND') && changeButton ? (
                              <Button
                                disabled={detail.deactivateStatus}
                                type="primary"
                                danger
                                onClick={() => showUnbindModal('pay')}
                                style={{ marginLeft: 20, zIndex: 100 }}
                              >
                                解绑
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      {payDown ? (
                        <>
                          {payDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {payDetail &&
                                    payDetail.length &&
                                    payDetail.map((item, i) => (
                                      <TabPane tab={`权益包${i + 1}`} key={i}>
                                        <InjectMessage.Provider
                                          value={{
                                            accountNo,
                                            type: 'pay',
                                            reloadChangeRightInfo,
                                            isCurrent: changeButton,
                                          }}
                                        >
                                          {' '}
                                          <PackageDetail detail={item} callbackFn={callback} price={true} />
                                        </InjectMessage.Provider>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>
                    {/* 赠送权益信息 630 */}
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {giveDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeGiveDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeGiveDown(true);
                                  giveDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>赠送权益</span>
                          </div>
                          <div className="right-item">
                            {/* 未交付并且当前不是已注销状态 */}
                            {relevanDisable && !detail.deactivateStatus ? (
                              <p style={{ marginLeft: '20px', fontSize: 15, color: 'red' }}>未交付车辆无法关联权益包</p>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_RELEVANCE') && changeButton ? (
                              <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                style={{ marginLeft: 20, zIndex: 100 }}
                                disabled={relevanDisable}
                                onClick={() => {
                                  showModal('setIsModalVisible2', { type: 'GiveRelation', title: '赠送权益包关联' });
                                }}
                              >
                                关联
                              </Button>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_UNBIND') && changeButton ? (
                              <Button
                                disabled={detail.deactivateStatus}
                                type="primary"
                                danger
                                onClick={() => showUnbindModal('give')}
                                style={{ marginLeft: 20, zIndex: 100 }}
                              >
                                解绑
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      {giveDown ? (
                        <>
                          {giveDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {giveDetail &&
                                    giveDetail.length &&
                                    giveDetail.map((item, i) => (
                                      <TabPane tab={`权益包${i + 1}`} key={i}>
                                        <InjectMessage.Provider
                                          value={{
                                            accountNo,
                                            type: 'give',
                                            reloadChangeRightInfo,
                                            isCurrent: changeButton,
                                          }}
                                        >
                                          <PackageDetail detail={item} callbackFn={callback} price={true} />
                                        </InjectMessage.Provider>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>

                    {/* 精品权益信息 V20.0 */}
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {boutiqueDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeBoutiqueDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeBoutiqueDown(true);
                                  boutiqueDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>精品权益</span>
                          </div>
                        </div>
                      </div>
                      {boutiqueDown ? (
                        <>
                          {boutiqueDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {boutiqueDetail &&
                                    boutiqueDetail.length &&
                                    boutiqueDetail.map((item, i) => (
                                      <TabPane tab={`权益包${i + 1}`} key={i}>
                                        <InjectMessage.Provider
                                          value={{ accountNo, type: 'boutique', isCurrent: changeButton }}
                                        >
                                          <PackageDetail detail={item} callbackFn={callback} />
                                        </InjectMessage.Provider>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>

                    {/* 华为权益信息 V28.0.2 */}
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {huaWeiDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeHuaWeiDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeHuaWeiDown(true);
                                  huaWeiDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>华为权益</span>
                          </div>
                        </div>
                      </div>
                      {huaWeiDown ? (
                        <>
                          {huaWeiDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {huaWeiDetail &&
                                    huaWeiDetail.length &&
                                    huaWeiDetail.map((item, i) => (
                                      <TabPane tab={`权益包${i + 1}`} key={i}>
                                        <InjectMessage.Provider
                                          value={{ accountNo, type: 'huawei', isCurrent: changeButton }}
                                        >
                                          <PackageDetail detail={item} callbackFn={callback} />
                                        </InjectMessage.Provider>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>

                    {/* 增值权益信息 630 */}
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {gainDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeGainDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeGainDown(true);
                                  gainDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>增值权益</span>
                          </div>
                          <div className="right-item">
                            {/* 未交付并且当前不是已注销状态 */}
                            {relevanDisable && !detail.deactivateStatus ? (
                              <p style={{ marginLeft: '20px', fontSize: 15, color: 'red' }}>未交付车辆无法关联权益包</p>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_RELEVANCE') && changeButton ? (
                              <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                style={{ marginLeft: 20, zIndex: 100 }}
                                disabled={relevanDisable}
                                onClick={() => {
                                  showModal('setIsModalVisible2', { type: 'GaineRelation', title: '增值权益包关联' });
                                }}
                              >
                                关联
                              </Button>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_UNBIND') && changeButton ? (
                              <Button
                                disabled={detail.deactivateStatus}
                                type="primary"
                                danger
                                onClick={() => showUnbindModal('gain')}
                                style={{ marginLeft: 20, zIndex: 100 }}
                              >
                                解绑
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      {gainDown ? (
                        <>
                          {gainDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {gainDetail &&
                                    gainDetail.length &&
                                    gainDetail.map((item, i) => (
                                      <TabPane tab={`权益包${i + 1}`} key={i}>
                                        <InjectMessage.Provider
                                          value={{
                                            accountNo,
                                            type: 'gain',
                                            reloadChangeRightInfo,
                                            isCurrent: changeButton,
                                          }}
                                        >
                                          <PackageDetail detail={item} callbackFn={callback} price={true} />
                                        </InjectMessage.Provider>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>
                    {/* V22驾行权益信息 */}
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {driverDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeDriverDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeDriverDown(true);
                                  driverDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>驾行权益</span>
                          </div>
                          <div className="right-item">
                            {/* 未交付并且当前不是已注销状态 */}
                            {relevanDisable && !detail.deactivateStatus ? (
                              <p style={{ marginLeft: '20px', fontSize: 15, color: 'red' }}>未交付车辆无法关联权益包</p>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_RELEVANCE') && changeButton ? (
                              <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                style={{ marginLeft: 20, zIndex: 100 }}
                                disabled={relevanDisable}
                                onClick={() => {
                                  showModal('setIsModalVisible2', { type: 'DriverRelation', title: '驾行安心包关联' });
                                }}
                              >
                                关联
                              </Button>
                            ) : null}
                            {roleJudgment(userInfo, 'ACCOUNT_MANAGEMENT_UNBIND') && changeButton ? (
                              <Button
                                disabled={detail.deactivateStatus}
                                type="primary"
                                danger
                                onClick={() => showUnbindModal('driver')}
                                style={{ marginLeft: 20, zIndex: 100 }}
                              >
                                解绑
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      {driverDown ? (
                        <>
                          {driverDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {driverDetail &&
                                    driverDetail.length &&
                                    driverDetail.map((item, i) => (
                                      <TabPane tab={`权益包${i + 1}`} key={i}>
                                        <InjectMessage.Provider
                                          value={{
                                            accountNo,
                                            type: 'driver',
                                            reloadChangeRightInfo,
                                            isCurrent: changeButton,
                                          }}
                                        >
                                          <PackageDetail detail={item} callbackFn={callback} price={true} />
                                        </InjectMessage.Provider>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>

                    {/* V28.0.5溯源权益信息 */}
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {traceDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeTraceDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeTraceDown(true);
                                  traceDetailContent();
                                }}
                                alt=""
                              />
                            )}
                            <span>溯源权益</span>
                          </div>
                        </div>
                      </div>
                      {traceDown ? (
                        <>
                          {traceDetail.length ? (
                            <>
                              <div className="tab-content">
                                <div className="title">权益包信息</div>
                                <Tabs onChange={callback} type="card" className={styles['custom-ant-tabs']}>
                                  {traceDetail &&
                                    traceDetail.length &&
                                    traceDetail.map((item, i) => (
                                      <TabPane tab={`权益包${i + 1}`} key={i}>
                                        <InjectMessage.Provider
                                          value={{
                                            accountNo,
                                            type: 'trace',
                                            isCurrent: changeButton,
                                          }}
                                        >
                                          <PackageDetail detail={item} callbackFn={callback} />
                                        </InjectMessage.Provider>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>

                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {!detail.vin ? (
                              <img className="icon-box" src={require('@/assets/img/gray-icon.png')} alt="" />
                            ) : partsDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangePartsDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangePartsDown(true);
                                  if (detail.vin) {
                                    partsContent({ vin: detail.vin });
                                  }
                                }}
                                alt=""
                              />
                            )}

                            <span>配件列表</span>
                          </div>
                        </div>
                      </div>

                      {partsDown ? (
                        <>
                          <>
                            <div className="part-box" style={{ backgroundColor: '#F1F8FF' }}>
                              <div className="part-content">
                                <div style={{ display: 'flex', width: '100%' }}>
                                  <div style={{ lineHeight: '32px', fontWeight: 'bold', paddingRight: '10px' }}>
                                    配件列表: <span style={{ color: '#5095D5' }}>{partListCount}</span>个
                                  </div>
                                  <PublicTableQuery
                                    isCatch={true}
                                    defaultQuery={defaultpartQuery}
                                    isFormDown={false}
                                    onSearch={onPartSearch}
                                    searchList={partsearchList}
                                  />
                                </div>
                                <PartsList loading={loading} partsDataSource={partsDataSource} />
                              </div>
                            </div>
                          </>
                        </>
                      ) : null}
                    </div>
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {operDown ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeOperDown(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeOperDown(true);
                                  // operContent()
                                }}
                                alt=""
                              />
                            )}
                            <span>操作信息</span>
                          </div>
                        </div>
                      </div>

                      {/* {operDown ? <>
                                            {
                                                operDataSource.length ?
                                                    <>
                                                        <div style={{ backgroundColor: '#F1F8FF', paddingLeft: 22, paddingTop: 25, paddingBottom: 15, position: 'relative' }}>
                                                            <div className='log-wrap'>
                                                                {
                                                                    operDataSource && operDataSource.map((item, index) => {
                                                                        return <LogItem
                                                                            key={index}
                                                                            logTime={moment(item.operTime).format('YYYY-MM-DD HH:mm:ss')}
                                                                            operName={item.operName}
                                                                            operType={item.operType ? OPERATE[item.operType] : '-'}>
                                                                            <div className='changeContent'>
                                                                                <span>操作内容：</span>
                                                                                <Row className='content'>{outputContent(item.changeContent)}</Row>
                                                                            </div>
                                                                        </LogItem>
                                                                    })
                                                                }
                                                            </div>
                                                        </div>
                                                    </> : <div style={{ textAlign: 'center', paddingTop: 20, paddingBottom: 20, backgroundColor: '#F1F8FF' }}> <img style={{ width: 124, height: 104 }} src={NoData} /><p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p></div>
                                            }
                                        </> : null} */}

                      {operDown ? (
                        <div style={{ backgroundColor: '#F1F8FF', padding: '25px 22px 15px', position: 'relative' }}>
                          <Logs type="account" dataCode={detail.accountNo} showAll={operDown}>
                            {item => {
                              return (
                                <div className="changeContent">
                                  <span>操作内容：</span>
                                  <Row className="content">{outputContent(item.changeContent)}</Row>
                                </div>
                              );
                            }}
                          </Logs>
                        </div>
                      ) : null}
                    </div>
                  </>
                )}
                {tabType === 2 && (
                  <>
                    <div className="tableData">
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {reportDown1 ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeReportDown1(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeReportDown1(true);
                                  keepDetailContent('order');
                                }}
                                alt=""
                              />
                            )}
                            <span>工时核销信息</span>
                          </div>
                        </div>
                      </div>
                      {reportDown1 ? (
                        <>
                          {dataSource2.length ||
                          dataSource3.length ||
                          dataSource4.length ||
                          dataSource8.length ||
                          dataSourceDriver1.length ? (
                            <>
                              {dataSource2.length ? (
                                <div>
                                  <div className="tab-content">
                                    <div className="title">基础权益</div>
                                    <Tabs
                                      defaultActiveKey="1"
                                      onChange={callback}
                                      type="card"
                                      className={styles['custom-ant-tabs']}
                                    >
                                      {dataSource2.length &&
                                        dataSource2.map(item => (
                                          <TabPane tab={item.name} key={item.name}>
                                            <div className="content-div content-div-nopad">
                                              <KeepTable dataSource={item.content} />
                                            </div>
                                          </TabPane>
                                        ))}
                                    </Tabs>
                                  </div>
                                  {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                </div>
                              ) : null}
                              {dataSource3.length ? (
                                <div className="tab-content">
                                  <div className="title">付费权益</div>
                                  <Tabs
                                    defaultActiveKey="1"
                                    onChange={callback}
                                    type="card"
                                    className={styles['custom-ant-tabs']}
                                  >
                                    {dataSource3.length &&
                                      dataSource3.map(item => (
                                        <TabPane tab={item.name} key={item.name}>
                                          <div className="content-div content-div-nopad">
                                            <KeepTable dataSource={item.content} />
                                          </div>
                                        </TabPane>
                                      ))}
                                  </Tabs>
                                  {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                </div>
                              ) : null}
                              {dataSource4.length ? (
                                <div className="tab-content">
                                  <div className="title">赠送权益</div>
                                  <Tabs
                                    defaultActiveKey="1"
                                    onChange={callback}
                                    type="card"
                                    className={styles['custom-ant-tabs']}
                                  >
                                    {dataSource4.length &&
                                      dataSource4.map(item => (
                                        <TabPane tab={item.name} key={item.name}>
                                          <div className="content-div content-div-nopad">
                                            <KeepTable dataSource={item.content} />
                                          </div>
                                        </TabPane>
                                      ))}
                                  </Tabs>
                                  {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                </div>
                              ) : null}
                              {dataSource8.length ? (
                                <div className="tab-content">
                                  <div className="title">增值权益</div>
                                  <Tabs
                                    defaultActiveKey="1"
                                    onChange={callback}
                                    type="card"
                                    className={styles['custom-ant-tabs']}
                                  >
                                    {dataSource8.length &&
                                      dataSource8.map(item => (
                                        <TabPane tab={item.name} key={item.name}>
                                          <div className="content-div content-div-nopad">
                                            <KeepTable dataSource={item.content} />
                                          </div>
                                        </TabPane>
                                      ))}
                                  </Tabs>
                                  {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                </div>
                              ) : null}
                              {!!dataSourceDriver1.length && (
                                <div className="tab-content">
                                  <div className="title">驾行权益</div>
                                  <Tabs
                                    defaultActiveKey="1"
                                    onChange={callback}
                                    type="card"
                                    className={styles['custom-ant-tabs']}
                                  >
                                    {dataSourceDriver1.map(item => (
                                      <TabPane tab={item.name} key={item.name}>
                                        <div className="content-div content-div-nopad">
                                          <KeepTable dataSource={item.content} />
                                        </div>
                                      </TabPane>
                                    ))}
                                  </Tabs>
                                  {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                </div>
                              )}
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {reportDown2 ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeReportDown2(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeReportDown2(true);
                                  keepDetailContent('part');
                                }}
                                alt=""
                              />
                            )}
                            <span>配件核销信息</span>
                          </div>
                        </div>
                      </div>
                      {reportDown2 ? (
                        <>
                          {dataSource5.length ||
                          dataSource6.length ||
                          dataSource7.length ||
                          dataSource9.length ||
                          dataSourceDriver2 ? (
                            <>
                              {dataSource5.length ? (
                                <>
                                  <div>
                                    <div className="tab-content">
                                      <div className="title">基础权益</div>
                                      <Tabs
                                        defaultActiveKey="1"
                                        onChange={callback}
                                        type="card"
                                        className={styles['custom-ant-tabs']}
                                      >
                                        {dataSource5.length &&
                                          dataSource5.map(item => (
                                            <TabPane tab={item.name} key={item.name}>
                                              <div className="content-div content-div-nopad">
                                                <KeepTable dataSource={item.content} />
                                              </div>
                                            </TabPane>
                                          ))}
                                      </Tabs>
                                    </div>
                                    {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                  </div>
                                </>
                              ) : null}
                              {dataSource6.length ? (
                                <>
                                  <div>
                                    <div className="tab-content">
                                      <div className="title">付费权益</div>
                                      <Tabs
                                        defaultActiveKey="1"
                                        onChange={callback}
                                        type="card"
                                        className={styles['custom-ant-tabs']}
                                      >
                                        {dataSource6.length &&
                                          dataSource6.map(item => (
                                            <TabPane tab={item.name} key={item.name}>
                                              <div className="content-div content-div-nopad">
                                                <KeepTable dataSource={item.content} />
                                              </div>
                                            </TabPane>
                                          ))}
                                      </Tabs>
                                    </div>
                                    {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                  </div>
                                </>
                              ) : null}
                              {dataSource7.length ? (
                                <>
                                  <div>
                                    <div className="tab-content">
                                      <div className="title">赠送权益</div>
                                      <Tabs
                                        defaultActiveKey="1"
                                        onChange={callback}
                                        type="card"
                                        className={styles['custom-ant-tabs']}
                                      >
                                        {dataSource7.length &&
                                          dataSource7.map(item => (
                                            <TabPane tab={item.name} key={item.name}>
                                              <div className="content-div content-div-nopad">
                                                <KeepTable dataSource={item.content} />
                                              </div>
                                            </TabPane>
                                          ))}
                                      </Tabs>
                                    </div>
                                    {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                  </div>
                                </>
                              ) : null}
                              {dataSource9.length ? (
                                <>
                                  <div>
                                    <div className="tab-content">
                                      <div className="title">增值权益</div>
                                      <Tabs
                                        defaultActiveKey="1"
                                        onChange={callback}
                                        type="card"
                                        className={styles['custom-ant-tabs']}
                                      >
                                        {dataSource9.length &&
                                          dataSource9.map(item => (
                                            <TabPane tab={item.name} key={item.name}>
                                              <div className="content-div content-div-nopad">
                                                <KeepTable dataSource={item.content} />
                                              </div>
                                            </TabPane>
                                          ))}
                                      </Tabs>
                                    </div>
                                    {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                  </div>
                                </>
                              ) : null}
                              {!!dataSourceDriver2.length && (
                                <>
                                  <div>
                                    <div className="tab-content">
                                      <div className="title">驾行权益</div>
                                      <Tabs
                                        defaultActiveKey="1"
                                        onChange={callback}
                                        type="card"
                                        className={styles['custom-ant-tabs']}
                                      >
                                        {dataSourceDriver2.map(item => (
                                          <TabPane tab={item.name} key={item.name}>
                                            <div className="content-div content-div-nopad">
                                              <KeepTable dataSource={item.content} />
                                            </div>
                                          </TabPane>
                                        ))}
                                      </Tabs>
                                    </div>
                                    {/* <div style={{paddingRight:25}}><Divider style={{marginTop:0}} /></div> */}
                                  </div>
                                </>
                              )}
                            </>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>
                    {/* 精品核销V20.0 */}
                    <div className="tableData" style={{ marginTop: -24 }}>
                      <div style={{ backgroundColor: '#d6ebff' }}>
                        <div className="top-box">
                          <div className="left-item margin-left">
                            {reportDown3 ? (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/up-icon.png')}
                                onClick={() => {
                                  ChangeReportDown3(false);
                                }}
                                alt=""
                              />
                            ) : (
                              <img
                                className="icon-box"
                                src={require('@/assets/img/down-icon.png')}
                                onClick={() => {
                                  ChangeReportDown3(true);
                                  keepBoutiqueDetailContent('boutique');
                                }}
                                alt=""
                              />
                            )}
                            <span>精品核销信息</span>
                          </div>
                        </div>
                      </div>
                      {reportDown3 ? (
                        <>
                          {dataSource10.length ? (
                            <div>
                              <div className="tab-content">
                                <div className="title">精品权益</div>
                                <Tabs
                                  defaultActiveKey="1"
                                  onChange={callback}
                                  type="card"
                                  className={styles['custom-ant-tabs']}
                                >
                                  {dataSource10.length &&
                                    dataSource10.map(item => (
                                      <TabPane tab={item.name} key={item.name}>
                                        <div className="content-div content-div-nopad">
                                          <KeepTableInject.Provider value={{ type: 'boutique' }}>
                                            <KeepTable dataSource={item.content} />
                                          </KeepTableInject.Provider>
                                        </div>
                                      </TabPane>
                                    ))}
                                </Tabs>
                              </div>
                            </div>
                          ) : (
                            <div
                              style={{
                                textAlign: 'center',
                                paddingTop: 20,
                                paddingBottom: 20,
                                backgroundColor: '#F1F8FF',
                              }}
                            >
                              {' '}
                              <img style={{ width: 124, height: 104 }} src={NoData} />
                              <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>暂无数据</p>
                            </div>
                          )}
                        </>
                      ) : null}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="bottom-btn">
            <Col className="text"></Col>
            <Col className="bts">
              <Button onClick={onCancel}>返回</Button>
            </Col>
          </div>
          {isModalVisible && (
            <ChangeAccountInfo
              handleCancel={handleCancel}
              visible={isModalVisible}
              dataSource={detail}
              reloadChangeInfo={reloadChangeInfo}
            />
          )}
          {isLO_REDModalVisible && (
            <InjectMessage.Provider value={{ getDetail }}>
              <LO_REDModal
                handleCancel={handleCancel}
                title={title}
                visible={isLO_REDModalVisible}
                dataSource={detail}
                reloadChangeInfo={reloadChangeInfo}
                examine={examine}
              />
            </InjectMessage.Provider>
          )}
          {isModalVisible2 && (
            <RelevancePayGiveTable
              handleCancel={handleCancel}
              title={title}
              visible={isModalVisible2}
              dataSourceDetail={detail}
              payDetailContent={payDetailContent}
              giveDetailContent={giveDetailContent}
              gainDetailContent={gainDetailContent}
              reloadChangeInfo={reloadRelevanceInfo}
            />
          )}
          {isTimeModalVisible && (
            <ChangeDeliveryTime
              handleCancel={handleCancel}
              visible={isTimeModalVisible}
              dataSource={detail}
              reloadChangeInfo={reloadChangeInfo}
            />
          )}
          {isUnbindModalVisible && (
            <UnbindRight
              handleCancel={handleCancel}
              accountNo={accountNo}
              visible={isUnbindModalVisible}
              dataSource={unbindList}
              reloadChangeInfo={reloadChangeRightInfo}
            />
          )}

          {isLoadTipsModalOpen && (
            <LoadTipsModal isLoadTipsModalOpen={isLoadTipsModalOpen} setIsLoadTipsModalOpen={setIsLoadTipsModalOpen} />
          )}
        </Spin>
      </div>
    </>
  ) : null;
};
export default AccountManagementDetail;
