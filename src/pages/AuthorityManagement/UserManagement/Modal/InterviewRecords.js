import React,{useEffect,useState} from 'react'
import { Modal, Select, Form,Row,Col,Input ,message,DatePicker } from 'antd'
import { post,get } from '@/utils/request'
import allUrl from '@/utils/url'
import {trim} from '@/utils'
import {DictList} from '@/utils/utilsDict'
import moment from 'moment';
const {Option} = Select
const InterviewRecords = (props) => {
    const { visible, onCancel,rowData,getTableData,title,recordObj } = props
    const [form] = Form.useForm();
    const [interviewOptions,setinterviewOptions] = useState([])

    const [defaultQuery,setDefaultQuery]=useState({})

    const handleOk = () => {
        form.validateFields().then(values=>{
            console.log(recordObj)
            let params ={
                id:recordObj.id,
                interviewTime:values.interviewTime,
                userId:rowData.id,
                interviewerId:values.interviewerId,
                score:parseInt(values.score)
            }
            post(allUrl.Authority.setStoreKeyPostInterview,{...params}).then(res => {
                if (res.success) {
                    message.success(res.msg || '修改面试记录成功')
                    onCancel()
                    getTableData()
                } else {
                    message.error(res.msg || '修改面试记录失败')
                }
            })
        })
    }
    // 数据接口
    const GetData = () => {
            post(allUrl.Authority.userList, {...defaultQuery,pageSize:10,current:1}).then(res => {
                if (res && res.success) {
                    let Dt = res.resp[0]
                 setinterviewOptions(Dt.list)
                } else {
                    // message.error(res.msg)
                }
            }) 
    }
    // 搜索
    const onSearch = (value) => {
        value = trim(value)
        defaultQuery.params = {}
        defaultQuery.params.text = value
        setDefaultQuery(defaultQuery)
        GetData()
    }
    const onChange =(value,key)=>{
       // key是数组，数组中有key值
       if(value){
        form.setFieldsValue({
            interviewerOrgName:interviewOptions[key.key].organizationsDescription,
            interviewerId:interviewOptions[key.key].id
        })
        }
    }


    useEffect(()=>{
        form.setFieldsValue({
            interviewTime:recordObj.interviewTime ? moment(recordObj.interviewTime):moment(today),
            interviewerOrgName:recordObj.interviewerOrgName,
            interviewer:recordObj.interviewer,
            score:recordObj.score,
            interviewerId:recordObj.interviewerId
        })
    },[recordObj])
    const today = moment();
    return <Modal  width={800} visible={visible} recordObj={recordObj} title={title} onOk={handleOk} onCancel={onCancel} maskClosable={false} getTableData={getTableData}>
        <Form
        layout={'vertical'}
            form={form}
        >
            <Row className='FormRowCon'>
                <Col span={8}>
                    <Form.Item wrapperCol={{ span: 20 }}label="面试时间" name="interviewTime" rules={[{ required: false, message: '请选择面试时间！', }]}>
                    <DatePicker  style={{width:'100%'}}/>
                    </Form.Item>
                    <Form.Item wrapperCol={{ span: 20 }}label="面试官所属组织" name="interviewerOrgName" rules={[{ required: false, message: '请输入', }]}>
                        <Input placeholder="请输入"disabled />
                    </Form.Item>
                    
                </Col>
                <Col span={8}>
                    <Form.Item wrapperCol={{ span: 20 }}label="面试官姓名" name="interviewer" rules={[{ required: true, message: '请选择面试官！', },
                                        {
                                            validator(_, value) {
                                                if (value ==rowData.userName) {
                                                    return Promise.reject(new Error('面试官不能为本人'));
                                                } else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}>
                        <Select
                            placeholder='请输入姓名/手机号后选择'
                            allowClear
                            showSearch
                            optionFilterProp="children"
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            onSearch={onSearch}
                            onChange={onChange}
                            filterOption={false}>
                            {
                                interviewOptions.map((item, index) =>  <Option key={index} value={item.userName}>{item.userName}</Option>)

                            }
                        </Select>
                    </Form.Item>
                    <Form.Item wrapperCol={{ span: 20 }}label="面试成绩" name="score"
                                rules={[{ required: true, message: '请输入面试成绩', },
                                        {
                                            validator(_, value) {
                                                if (value && !/^(?:[1-9]?\d|100)$/.test(value)) {
                                                    return Promise.reject(new Error('请输入0-100的自然数'));
                                                } else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}>
                        <Input placeholder="请输入"/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                <Form.Item wrapperCol={{ span: 20 }}label="面试官账号ID" name="interviewerId" rules={[{ required: false, message: '请输入面试官账号ID', }]}>
                    <Input placeholder="请输入"disabled />
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    </Modal>
}
export default InterviewRecords