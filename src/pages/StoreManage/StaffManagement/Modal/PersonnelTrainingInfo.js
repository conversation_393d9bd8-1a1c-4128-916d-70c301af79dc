import React, { useState ,useEffect } from 'react'
import { Modal, Form, Select, Row, Col, Table, Button, Input, Popconfirm ,message ,Collapse} from 'antd'
import {useDispatch} from 'react-redux'
import { QuestionCircleOutlined } from '@ant-design/icons';
import {getDict} from '@/actions/async'
import {post,get} from '@/utils/request'
import allUrl from '@/utils/url';
const { Item } = Form
const { Option } = Select
const { Panel } = Collapse;

export default (props) => {
    const dispatch = useDispatch()
    const { type, visible, onCancel ,recordObj ,rowData ,getTableData } = props
    const [form] = Form.useForm()
    const [dataSource, setDataSource] = useState([])
    const [loading, setLoading] = useState(false)
    const [curriculum, setCurriculum] = useState([])
    const [deleteTrainingIds,setDeleteTrainingIds] = useState([])
    const [trainingInfo,setTrainingInfo] = useState({})
    const [lmsStaffLevelData, setLmsStaffLevelData] = useState([])
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
    }

    const fieldChange = (value, record, field) => {
        let data = [...dataSource]
        let target = data.filter(item => item.key === record.key)[0]
        target[field] = value
        setDataSource(data)
    }

    const selectSearchChange = (value, option,record, field) =>{
        console.log(value, option)
        let data = [...dataSource]
        let target = data.filter(item => item.key === record.key)[0]
        target[field] = value
        if(field === 'trainCourseCodeName'){
            target.trainCourseCode = option.code
        }
        setDataSource(data)

    }

    const renderSelect = (text, record, field) => {
        const textStr = ['未通过', '通过', '免训']
        return type === 1 ? <Select placeholder='请选择' style={{ width: '100%' }} value={text} onChange={(e) => fieldChange(e, record, field)}>
            <Option value={'1'}>通过</Option>
            <Option value={'0'}>未通过</Option>
            <Option value={'2'}>免训</Option>
        </Select> : textStr[text]


    }

    const renderSelectSearch = (text, record, field) => {
        return type === 1 ?
        <Select
            showSearch
            placeholder="请选择"
            optionFilterProp="children"
            onChange={(value, option) => selectSearchChange(value, option, record, field)}
            filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            style={{width:'100%'}}
            value={text}
        >
            {
                curriculum.map(item=><Option key={item.entryValue} value={item.entryMeaning} code={item.entryValue}>{item.entryMeaning}</Option>)
            }
        </Select>
         : text
    }

    const columns = [
        { title: '序号', dataIndex: 'index', width: 70, fixed: 'left', render: (text, record, index) => index + 1 },
        { title: '培训课程', dataIndex: 'trainCourseCodeName',width:300, render: (text, record) => renderSelectSearch(text, record, 'trainCourseCodeName') },
        { title: '培训课程编码', dataIndex: 'trainCourseCode',width:120},
        {
            title: '培训结果', children: [
                { title: '线上学习', dataIndex: 'onlineResult', width: 130, render: (text, record) => renderSelect(text, record, 'onlineResult') },
                { title: '线下培训', dataIndex: 'offlineResult', width: 130, render: (text, record) => renderSelect(text, record, 'offlineResult') },
                { title: '等级认证', dataIndex: 'levelResult', width: 130, render: (text, record) => renderSelect(text, record, 'levelResult') },
            ]
        },
    ]

    if(type == 2){
        columns.splice(4,1)
    }

    const tableInfo = {
        rowKey: record => record.key,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content' },
        columns,
        pagination: false,
    };

    const renderCertificationLevel = () => {
        return type === 2?
        <Form form={form} {...formItemLayout}>
            <Row>
                <Col span={12}>
                    <Item label='当前等级' name='currentLevel'>{trainingInfo.currentLevel}</Item>
                </Col>
                <Col span={12}>
                    <Item label='目标等级' name='targetLevel'>{trainingInfo.targetLevel}</Item>
                </Col>
                <Col span={12}>
                    <Item label='兼职岗位当前等级' name='partCurrentLevel'>{trainingInfo.partCurrentLevel}</Item>
                </Col>
                <Col span={12}>
                    <Item label='兼职岗位目标等级' name='partTargetLevel'>{trainingInfo.partTargetLevel}</Item>
                </Col>
                 <Col span={12}>
                   <Item label='历史岗位/培训等级' name='partTargetLevel'>
                    {lmsStaffLevelData && lmsStaffLevelData.map((item, index) => (
                      <span key={index}>
                        <span>{item.postName ? `${item.postName} (L${item.level})；` : ''}</span>
                      </span>
                    ))}
                  </Item>
                </Col>
            </Row>
        </Form>
        :<Form form={form} {...formItemLayout}>
            <Row>
                <Col span={12}>
                    <Item label='当前等级' name='currentLevel'>
                        <Select allowClear placeholder='请选择'>
                            <Option value='L1'>L1</Option>
                            <Option value='L2'>L2</Option>
                            <Option value='L3'>L3</Option>
                        </Select>
                    </Item>
                </Col>
                <Col span={12}>
                    <Item label='目标等级' name='targetLevel' initialValue={'L1'}>
                        <Select placeholder='请选择'>
                            <Option value='L1'>L1</Option>
                            <Option value='L2'>L2</Option>
                            <Option value='L3'>L3</Option>
                        </Select>
                    </Item>
                </Col>
            </Row>
        </Form>
    }

    const renderTrainingRecords = () => {
        return <div>
            <Table {...tableInfo} />
        </div>
    }

    const initForm = (Dt) =>{
        form.setFieldsValue({
            currentLevel:Dt.currentLevel || null,
            targetLevel:Dt.targetLevel || 'L1',
        })
    }

    useEffect(()=>{
        dispatch(getDict({codes:'user_train_course'})).then(({payload})=>{
            setCurriculum(payload['user_train_course'])
        })
    },[])

    useEffect(()=>{
        setLoading(true)
        get(allUrl.StoreManage.getUserTrainingAndCertification + '/' + recordObj.userId).then(res=>{
            setLoading(false)
            if(res.success){
                let Dt = res.resp[0]
                Dt.trainingList.forEach((item,index)=>{
                    item.key=index+1
                    item.levelResult = String(item.levelResult)
                    item.offlineResult = String(item.offlineResult)
                    item.onlineResult = String(item.onlineResult)
                })
                setTrainingInfo(Dt)
                setDataSource(Dt.trainingList)
                initForm(Dt)
            }else{
                // message.error(res.msg)
            }
        })
        post(allUrl.StoreManage.lmsStaffLevel, {staffNumber: recordObj.staffNumber }).then(res=> {
          if (res.success) {
            console.log(res.resp[0])
            setLmsStaffLevelData(res.resp[0])
          }
        })
    },[type,recordObj])

    // if(type === 2){
    //     modeProps.footer = null
    // }

    return <div>
        <Collapse defaultActiveKey={['1','2']}>
            <Panel header="认证等级" key="1">
                {renderCertificationLevel()}
            </Panel>
            <Panel header="培训记录" key="2">
                {renderTrainingRecords()}
            </Panel>
        </Collapse>
    </div>
}
