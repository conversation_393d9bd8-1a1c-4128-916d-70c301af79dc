import "./Staff.less";
import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Select,
  Input,
  Row,
  Col,
  Button,
  Avatar,
  Spin,
  DatePicker,
  Tooltip,
  Tabs,
  Image,
  Tag,
  message,
  Anchor,
  TreeSelect,
  TimePicker,
} from "antd";
import { mock1 } from "./mock";
import { get, post } from "../../../utils/request";
import allUrl from "../../../utils/url";
import _, { countBy, range } from "lodash";
import ChatRow from "./components/ChatRow";
import { UserOutlined } from "@ant-design/icons";
import { useCallbackState } from "../../../hooks";
import moment from "moment";
const { TabPane } = Tabs;
const { TreeNode } = TreeSelect;
const { Option } = Select;
const { Link } = Anchor;
const { Search } = Input;
const { RangePicker } = DatePicker;

const Staff = (props) => {
  const [pageSpin, setPageSpin] = useCallbackState(false);
  const [treeOrgData, setTreeOrgData] = useState([]);
  const [staffList, setStaffList] = useState([]);
  const [groupList, setGroupList] = useState([]);
  const [customerList, setCustomerList] = useState([]);
  const [staffLastestList, setStaffLastestList] = useState([]);
  const [staffPageKeywords, setStaffPageKeywords] = useState(undefined);
  const [staffPageOrgId, setStaffPageOrgId] = useState(undefined);
  const [staffPagePosition, setStaffPagePosition] = useState(undefined);
  const [positionOptions, setPositionOptions] = useState([]);
  const [staffPageCurrent, setStaffPageCurrent] = useState(1);
  const [staffPageIsLastPage, setStaffPageIsLastPage] = useState(false);
  const [staffPagePageSize, setStaffPagePageSize] = useState(10);
  const [staffPageTotal, setStaffPageTotal] = useState(0);
  const [currentStaff, setCurrentStaff] = useState({
    qwUserId: "",
  });
  const [staffSpinning, setStaffSpinning] = useState(false);
  const [groupSpining, setGroupSpining] = useState(false);
  const [customerSpining, setCustomerSpining] = useState(false);
  const [colleagueSpining, setColleagueSpining] = useState(false);
  const [recordInfo, setRecordInfo] = useState({
    avatarUrl: "",
    name: "",
    groupId: "",
    tagNames: [],
    orgNames: [],
    memberSign: "",
  });
  const [chatList, setChatList] = useState([]);
  const chatListRef = useRef(chatList);
  const [chatListHasMore, setChatListHasMore] = useCallbackState(false);
  const [firstGetChatRecord, setFirstGetChatRecord] = useState(true);
  const [beforeScrollHeight, setBeforeScrollHeight] = useCallbackState(0);
  const [chatSpining, setChatSpining] = useState(false);
  const [rangeTime, setRangeTime] = useState([
    moment("00:00:00", "HH:mm:ss"),
    moment("11:59:59", "HH:mm:ss"),
  ]);

  const [searchByTimeOptions, setSearchByTimeOptions] = useState({
    lastId: "",
    startTime: "",
    endTime: "",
  });
  const staffListRef = React.createRef();
  const recordRef = React.createRef();
  const chatRowRef = useRef();
  useEffect(() => {
    chatListRef.current = chatList; // 把它写入 ref
  }, [chatList]); // 取得chatList最新值，方便赋值后直接使用

  const selectOrg = (value) => {
    setStaffPageOrgId(value);
  };

  const searchKeywords = (value) => {
    setStaffPageKeywords(value);
    getStaffList(true, value);
  };

  const selectPosition = (value) => {
    setStaffPagePosition(value);
  };
  // 初始化
  useEffect(() => {
    getStaffList();
    GetOrgData();
    getDict();
  }, []);
  // 获取组织列表
  const GetOrgData = (name = "") => {
    post(allUrl.Authority.orgPageList, { name }).then((res) => {
      if (res.success) {
        const arr = res.resp.slice(0);
        setTreeOrgData(arr);
      } else {
        message.error(res.msg);
      }
    });
  };
  // 组织搜索
  const searchOrg = _.debounce((value) => {
    if (value) {
      setStaffPageOrgId(value);
      GetOrgData(value);
    }
  }, 500);
  // 点击员工
  const selectStaff = (staff) => {
    setCurrentStaff(staff);
    getStafftList(staff.qwUserId);
    getCustomerList(staff.qwUserId);
    getGroupList(staff.qwUserId);
    setChatList([]);
    setRecordInfo({
      avatarUrl: "",
      name: "",
      groupId: "",
      tagNames: [],
      orgNames: [],
      memberSign: "",
    });
  };
  // 获取岗位列表
  const getDict = () => {
    get(allUrl.common.entryLists, { codes: "scrm_position" }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        setPositionOptions(Dt["scrm_position"]);
      } else {
        
      }
    });
  };
  /**
   * 获取群聊列表
   * @param {企微id} qwUserId
   */
  const getGroupList = (qwUserId) => {
    setGroupSpining(true);
    post(allUrl.WeCom.qwChatMsgGroupLatestList, {
      qwUserId,
    }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        if (Dt.length) {
          Dt.push({
            isLastPage: true,
          });
        }
        setGroupList(Dt);
      } else {
        
      }
      setGroupSpining(false);
    });
  };
  /**
   * 获取客户列表
   * @param {企微id} qwUserId
   */
  const getCustomerList = (qwUserId) => {
    setCustomerSpining(true);
    post(allUrl.WeCom.qwChatMsgCustomerLatestList, {
      qwUserId,
    }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0].slice(0);
        if (Dt.length) {
          Dt.push({
            isLastPage: true,
          });
        }
        // 目前一次返回了所有客户，后续有需要再拉后端做分页
        setCustomerList(Dt);
        // 提取客户列表中所有的exteranalopenid，和当前用户的userid，一次性批量查询存档状态
        // 一次性最多查100条，所以使用二维数组每一百条存一次。
        let customerInfos = [];
        let arr = [];
        if (res.resp[0].length) {
          res.resp[0].forEach((item, index) => {
            arr.push({
              userid: qwUserId,
              exteranalopenid: item.externalUserId,
            });
            if ((index + 1) % 2 === 0 && index !== 0) {
              customerInfos.push(arr);
              arr = [];
            }
          });
          // 要等所有存档返回以后再渲染
          let promises = [];
          promises = customerInfos.map((info) => {
            return new Promise((resolve, eject) => {
              resolve(getCheckChatAgree(Dt, info));
            });
          });
          Promise.all(promises).then((v) => {});
        }
      } else {
        
      }
      setCustomerSpining(false);
    });
  };
  /**
   * 获取客户存档情况
   * @param {*} qwUserId
   */
  const getCheckChatAgree = (customerList, info) => {
    post(allUrl.WeCom.qwCheckChatAgree, {
      info,
    }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        if (Dt.length) {
          Dt.forEach((i) => {
            customerList.map((j) => {
              if (i === j.externalUserId) {
                j.tagNames.push("不同意存档");
              }
            });
            const list = _.cloneDeep({ list: customerList }).list;
            setCustomerList(list);
          });
        }
      } else {
        
      }
      setColleagueSpining(false);
    });
  };
  /**
   * 获取同事列表
   * @param {企微id} qwUserId
   */
  const getStafftList = (qwUserId) => {
    setColleagueSpining(true);
    post(allUrl.WeCom.qwChatMsgStaffLatestList, {
      qwUserId,
    }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        if (Dt.length) {
          Dt.push({
            isLastPage: true,
          });
        }
        setStaffLastestList(Dt);
      } else {
        
      }
      setColleagueSpining(false);
    });
  };
  /**
   * 查询员工列表
   * @param {bool} bool 是否是搜索
   * @param {string} key 关键字搜索
   */
  const getStaffList = (bool, key) => {
    const isSearch = bool;
    setStaffSpinning(true);
    post(allUrl.WeCom.qwChatMsgStaffPage, {
      current: isSearch ? 1 : staffPageCurrent,
      keywords: key,
      orgId: staffPageOrgId,
      position: staffPagePosition,
    }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        let list = [];
        if (isSearch) {
          list = Dt.list;
        } else {
          list = staffList.concat(Dt.list);
        }
        if (Dt.isLastPage) {
          list.push({
            isLastPage: true,
          });
        }
        setStaffList(list);
        setStaffPageCurrent(Dt.nextPage);
        setStaffPageTotal(Dt.Total);
        setStaffPageIsLastPage(Dt.isLastPage);
      } else {
        
      }
      setStaffSpinning(false);
    });
  };
  const changeCustomerTab = () => {};
  // 同事精准查询
  const searchStaffRecord = (keywords) => {
    if (!currentStaff.qwUserId) return false;
    if (currentStaff.qwUserId && keywords) {
      setColleagueSpining(true);
      post(allUrl.WeCom.qwChatMsgSearchStaff, {
        qwUserId: currentStaff.qwUserId,
        keywords,
      }).then((res) => {
        if (res.success) {
          let Dt = res.resp[0];
          setStaffLastestList(Dt);
        } else {
        }
        setColleagueSpining(false);
      });
    } else {
      getStafftList(currentStaff.qwUserId);
    }
  };
  // 群聊模糊查询
  const searchGroupRecord = (keywords) => {
    if (!currentStaff.qwUserId) return false;
    if (currentStaff.qwUserId && keywords) {
      setGroupSpining(true);
      post(allUrl.WeCom.qwChatMsgSearchGroup, {
        qwUserId: currentStaff.qwUserId,
        keywords,
      }).then((res) => {
        if (res.success) {
          let Dt = res.resp[0];
          setGroupList(Dt);
        } else {
        }
        setGroupSpining(false);
      });
    } else {
      getGroupList(currentStaff.qwUserId);
    }
  };
  // 客户精准查询
  const searchCusTomerRecord = (keywords) => {
    if (!currentStaff.qwUserId) return false;
    if (currentStaff.qwUserId && keywords) {
      setCustomerSpining(true);
      post(allUrl.WeCom.qwChatMsgCustomer, {
        qwUserId: currentStaff.qwUserId,
        keywords,
      }).then((res) => {
        if (res.success) {
          let Dt = res.resp[0];
          setCustomerList(Dt);
        } else {
        }
        setCustomerSpining(false);
      });
    } else {
      getCustomerList(currentStaff.qwUserId);
    }
  };
  // 首次请求单聊记录
  const firstGetSildeChat = () => {
    if (recordInfo.memberSign || recordInfo.groupId) {
      setFirstGetChatRecord(true);
      setChatList([]);
      setChatListHasMore(false);
      getSildeChat();
    }
  };
  // 获取聊天记录
  const getSildeChat = (lastId, beforePosition) => {
    if (chatSpining) return; // 节流，防止多次请求
    const listDom = document.getElementsByClassName("record-list")[0];
    setChatSpining(true);
    // 有lastid则是二次请求，首次请求标识置否
    lastId && setFirstGetChatRecord(false);
    // 有传上次滚动条位置则保存
    beforePosition && setBeforeScrollHeight(beforePosition);
    post(
      recordInfo.memberSign ? allUrl.WeCom.qwChatMsgSlideChat : allUrl.WeCom.qwChatMsgGroupChat,
      {
        lastId,
        sign: recordInfo.memberSign || recordInfo.groupId,
        startTime: searchByTimeOptions.startTime,
        endTime: searchByTimeOptions.endTime,
      }
    ).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        searchByTimeOptions.startTime &&
          Dt.list.length &&
          setSearchByTimeOptions((options) => ({
            ...options,
            lastId: Dt.list[Dt.list.length - 1].id,
          }));
        if (lastId) {
          // 再次加载
          searchByTimeOptions.startTime
            ? setChatList((chatList) => chatList.concat(Dt.list))
            : setChatList((chatList) => Dt.list.reverse().concat(chatList));
        } else {
          // 首次加载
          searchByTimeOptions.startTime
            ? setChatList(Dt.list)
            : setChatList((chatList) => Dt.list.reverse());
        }
        setChatListHasMore(Dt.hasMore);
        if (!searchByTimeOptions.startTime) {
          listDom.scrollTop = beforePosition
            ? listDom.scrollHeight - beforePosition
            : listDom.scrollTop;
        } else {
          listDom.scrollTop = lastId ? beforePosition : 0;
        }
      } else {
        
      }
      setChatSpining(false);
    });
  };
  // 滚动条设置
  useEffect(() => {
    setChatSpining(true);
    if (chatList.length && firstGetChatRecord && searchByTimeOptions.startTime === "") {
      // 首次加载从滚动条底部开始，从最近当前时间的一条开始
      const listDom = document.getElementsByClassName("record-list")[0];
      if (listDom.clientHeight < listDom.scrollHeight) {
        listDom.scrollTop = listDom.scrollHeight - listDom.clientHeight;
      }
      setBeforeScrollHeight(listDom.scrollHeight);
    }
    setTimeout(() => {
      setChatSpining(false);
    }, 500);
  }, [chatList, firstGetChatRecord, searchByTimeOptions]);
  // 选中客户或群聊
  const selectCustomer = (item) => {
    setRecordInfo(item);
  };
  // 更新客户或者群聊信息
  useEffect(() => {
    setSearchByTimeOptions({
      startTime: "",
      endTime: "",
      lastId: "",
    });
    setRangeTime(["", ""]);
  }, [recordInfo]);
  // 更新聊天记录查询时间
  useEffect(() => {
    firstGetSildeChat();
  }, [rangeTime]);
  // 选择时间
  const searchRecordRange = (dates, dateStrings) => {
    setSearchByTimeOptions({
      startTime: dateStrings[0],
      endTime: dateStrings[1],
      lastId: "",
    });
    setRangeTime([dates[0], dates[1]]);
  };
  // 聊天记录滚动条监听
  const handleOnScrollCapture = (event) => {
    if (recordRef) {
      if (
        !searchByTimeOptions.startTime &&
        recordRef.current.scrollHeight > recordRef.current.clientHeight &&
        recordRef.current.scrollTop === 0
      ) {
        getSildeChat(chatListRef.current[0].id, recordRef.current.scrollHeight);
      }
      if (
        recordRef.current.scrollHeight - recordRef.current.clientHeight >
        recordRef.current.scrollTop + 100
      ) {
      } else {
        if (chatListHasMore && searchByTimeOptions.startTime) {
          getSildeChat(searchByTimeOptions.lastId, recordRef.current.scrollTop - 100);
        }
      }
    }
  };
  // 员工列表滚动条监听
  const handleStaffOnScroll = _.debounce(() => {
    if (
      staffListRef.current.scrollHeight - staffListRef.current.clientHeight >
      staffListRef.current.scrollTop + 30
    ) {
    } else {
      if (!staffPageIsLastPage) {
        console.log("加载下一页");
        getStaffList(false, staffPageKeywords);
      }
    }
  }, 500);
  // 关闭加载蒙层，并调用子组件方法清空数据防止加载完弹框。
  const closeSpin = () => {
    chatRowRef.current.closeSpin();
  }
  return (
    <>
      {
     pageSpin ?  <Button type="primary" onClick={closeSpin} className="spin-btn">关闭</Button>
      : null
      }
    <Spin 
      spinning={pageSpin}  //是否开启加载中
      tip='资源加载中，请稍候...'          //loading文本，这里也可以设置为动态的文本
      >
    <Row gutter={0} className="staff">
      <Col span={6} className="staff-left">
        <div className="search-wrapper">
          <TreeSelect
            showSearch
            showArrow={false}
            style={{ width: "100%", marginBottom: "14px" }}
            value={staffPageOrgId}
            dropdownStyle={{
              maxHeight: 800,
              overflow: "auto",
            }}
            fieldNames={{
              label: 'title', value: 'key', children: 'children' 
            }}
            filterTreeNode={() => true}
            onSearch={searchOrg}
            dropdownMatchSelectWidth={false}
            treeData={treeOrgData}
            placeholder="选择组织"
            allowClear
            onChange={selectOrg}
          />
          <Select
            style={{ width: "100%", marginBottom: "14px" }}
            allowClear
            showSearch
            onChange={selectPosition}
            optionFilterProp="children"
            placeholder="选择岗位"
            value={staffPagePosition}
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {positionOptions.map((item, index) => (
              <Option key={index} value={item.entryValue}>
                {item.entryMeaning}
              </Option>
            ))}
          </Select>
          <Search
            onSearch={searchKeywords}
            style={{ width: "100%" }}
            placeholder="搜索成员/手机号"
            allowClear
          />
        </div>
        <Spin className="spin" spinning={staffSpinning}>
          {staffList.length ? (
            <div
              className="staff-left-list"
              ref={staffListRef}
              onScrollCapture={handleStaffOnScroll}
            >
              {staffList.map((item, index) => {
                if (item.isLastPage) {
                  return <div className="divider">没有更多了</div>;
                }
                return (
                  <div
                    className={`staff-row_wrapper ${
                      item.qwUserId === currentStaff.qwUserId ? "staff-row_wrapper-active" : ""
                    }`}
                  >
                    <div
                      className="staff-row flex"
                      key={item.userId}
                      onClick={() => selectStaff(item)}
                    >
                      <Avatar
                        className="staff-row-avatar"
                        src={item.avatarUrl}
                        icon={<UserOutlined />}
                      ></Avatar>
                      <div className="staff-row-info">
                        <div className="floot1 flex">
                          <div className="name ellipsis">{item.name || "-"}</div>
                        </div>
                        <div className="floot2">{item.phone || "-"}</div>
                        {item.positionName && (
                          <Tag color="volcano" className="position ellipsis">
                            {item.positionName || "-"}
                          </Tag>
                        )}
                        {item.orgNames.length
                          ? item.orgNames.map((org) => {
                              return (
                                <Tag key={org} color="orange" className="floot3 ellipsis">
                                  <Tooltip title={org}>{org}</Tooltip>
                                </Tag>
                              );
                            })
                          : null}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="empty">暂无数据</div>
          )}
        </Spin>
      </Col>
      <Col span={18} className="staff-right">
        <Row gutter={28}>
          <Col span={8} className="customer">
            <Tabs defaultActiveKey="1" onChange={changeCustomerTab} centered>
              <TabPane tab="群聊" key="1">
                <Search
                  placeholder="输入群名关键字"
                  allowClear
                  onSearch={searchGroupRecord}
                  className="customer-search"
                  style={{}}
                />
                <Spin className="spin" spinning={groupSpining}>
                  <div className="customer-list">
                    {groupList.length ? (
                      groupList.map((item, index) => {
                        if (item.isLastPage) {
                          return <div className="divider">没有更多了</div>;
                        }
                        return (
                          <div
                            className={`staff-row_wrapper ${
                              item.groupId === recordInfo.groupId ? "staff-row_wrapper-active" : ""
                            }`}
                          >
                            <div
                              className="staff-row flex"
                              key={index}
                              onClick={() => selectCustomer(item)}
                            >
                              {/* <Avatar className="staff-row-avatar" src={item.avatarUrl} icon={<UserOutlined />} ></Avatar> */}
                              <div className="staff-row-info">
                                <div className="floot1 flex">
                                  <div className="name ellipsis">
                                    <Tooltip title={item.name}>{item.name || "-"}</Tooltip>
                                  </div>
                                  <div className="flex">
                                    {item.tagNames && item.tagNames.length
                                      ? item.tagNames.map((org) => {
                                          return (
                                            <Tag
                                              key={org}
                                              color="orange"
                                              className="floot3 ellipsis"
                                            >
                                              {org}
                                            </Tag>
                                          );
                                        })
                                      : null}
                                    <div className="lastest-time">{item.latestMsgStr || "-"}</div>
                                  </div>
                                </div>
                                <div className="floot2 flex flex-align">
                                  <div className="owner-name ellipsis">
                                    群主:{item.ownerName || "-"}
                                  </div>
                                  <div className="group-tags flex">
                                    {item.ownerPositionName && (
                                      <Tooltip title={item.ownerPositionName}>
                                        <Tag color="volcano" className="position ellipsis">
                                          {item.ownerPositionName}
                                        </Tag>
                                      </Tooltip>
                                    )}
                                  </div>
                                </div>
                                <div className="flex flex-align space-between">
                                  <div className="owner-phone">
                                    群主手机号:{item.ownerPhone || "-"}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="empty">
                        <img
                          className="empty-img"
                          src={require("@/assets/img/empty2.png")}
                          style={{ width: 70, height: 70 }}
                        />
                        <div className="empty-content">暂无数据</div>
                      </div>
                    )}
                  </div>
                </Spin>
              </TabPane>
              <TabPane tab="客户" key="2">
                <Search
                  placeholder="输入昵称/真实姓名/手机号"
                  className="customer-search"
                  allowClear
                  onSearch={searchCusTomerRecord}
                  style={{}}
                />
                <Spin className="spin" spinning={customerSpining}>
                  <div className="customer-list">
                    {customerList.length ? (
                      customerList.map((item, index) => {
                        if (item.isLastPage) {
                          return <div className="divider">没有更多了</div>;
                        }
                        return (
                          <div
                            className={`staff-row_wrapper ${
                              item.memberSign === recordInfo.memberSign
                                ? "staff-row_wrapper-active"
                                : ""
                            }`}
                          >
                            <div
                              className="staff-row flex"
                              key={index}
                              onClick={() => selectCustomer(item)}
                            >
                              <Avatar
                                className="staff-row-avatar"
                                src={item.avatarUrl}
                                icon={<UserOutlined />}
                              ></Avatar>
                              <div className="staff-row-info">
                                <div className="floot1 flex">
                                  <div className="name ellipsis">{item.name}</div>
                                  <div className="flex">
                                    {item.positionName && (
                                      <Tag color="volcano" className="position ellipsis">
                                        {item.positionName}
                                      </Tag>
                                    )}
                                    <div className="lastest-time">{item.latestMsgStr}</div>
                                  </div>
                                </div>
                                {item.tagNames && item.tagNames.length
                                  ? item.tagNames.map((org) => {
                                      return (
                                        <Tag key={org} color="orange" className="floot3 ellipsis">
                                          <Tooltip title={org}>{org}</Tooltip>
                                        </Tag>
                                      );
                                    })
                                  : null}
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="empty">
                        <img
                          className="empty-img"
                          src={require("@/assets/img/empty2.png")}
                          style={{ width: 70, height: 70 }}
                        />
                        <div className="empty-content">暂无数据</div>
                      </div>
                    )}
                  </div>
                </Spin>
              </TabPane>
              <TabPane tab="同事" key="3">
                <Search
                  placeholder="输入姓名/手机号"
                  className="customer-search"
                  allowClear
                  onSearch={searchStaffRecord}
                  style={{}}
                />
                <Spin className="spin" spinning={colleagueSpining}>
                  <div className="customer-list">
                    {staffLastestList.length ? (
                      staffLastestList.map((item, index) => {
                        if (item.isLastPage) {
                          return <div className="divider">没有更多了</div>;
                        }
                        return (
                          <div
                            className={`staff-row_wrapper ${
                              item.memberSign === recordInfo.memberSign
                                ? "staff-row_wrapper-active"
                                : ""
                            }`}
                          >
                            <div
                              className="staff-row flex"
                              key={index}
                              onClick={() => selectCustomer(item)}
                            >
                              <Avatar
                                className="staff-row-avatar"
                                src={item.avatarUrl}
                                icon={<UserOutlined />}
                              ></Avatar>
                              <div className="staff-row-info">
                                <div className="floot1 flex">
                                  <div className="name ellipsis">{item.name || "-"}</div>
                                  <div className="flex">
                                    {item.positionName && (
                                      <Tag color="volcano" className="position ellipsis">
                                        {item.positionName || "-"}
                                      </Tag>
                                    )}
                                    <div className="lastest-time">{item.latestMsgStr || "-"}</div>
                                  </div>
                                </div>
                                <div className="floot2 flex flex-align space-between">
                                  <div className="mr10">{item.phone || "-"}</div>
                                </div>
                                {item.orgNames.length
                                  ? item.orgNames.map((org) => {
                                      return (
                                        <Tag key={org} color="orange" className="floot3 ellipsis">
                                          <Tooltip title={org}>{org}</Tooltip>
                                        </Tag>
                                      );
                                    })
                                  : null}
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="empty">
                        <img
                          className="empty-img"
                          src={require("@/assets/img/empty2.png")}
                          style={{ width: 70, height: 70 }}
                        />
                        <div className="empty-content">暂无数据</div>
                      </div>
                    )}
                  </div>
                </Spin>
              </TabPane>
            </Tabs>
          </Col>
          <Col span={16} className="record">
            <Spin className="spin" spinning={chatSpining}>
              <div className="record-top">
                <div className="record-info flex">
                  <div className="flex">
                    {recordInfo.memberSign ? (
                      <Avatar
                        className="staff-row-avatar"
                        src={recordInfo.avatarUrl}
                        icon={<UserOutlined />}
                      ></Avatar>
                    ) : null}
                    <div className="staff-row-info">
                      <div className="floot1 flex">
                        <div className="name ellipsis">{recordInfo.name}</div>
                      </div>
                      <div className="floot2 tags flex">
                        {recordInfo.tagNames && recordInfo.tagNames.length
                          ? recordInfo.tagNames.map((item, index) => {
                              return (
                                <Tag key={index} className="position ellipsis" color="orange">
                                  <Tooltip title={item}>{item}</Tooltip>
                                </Tag>
                              );
                            })
                          : recordInfo.orgNames && recordInfo.orgNames.length
                          ? recordInfo.orgNames.map((item, index) => {
                              return (
                                <Tag key={index} className="position ellipsis" color="orange">
                                  <Tooltip title={item}>{item}</Tooltip>
                                </Tag>
                              );
                            })
                          : null}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="search-filter">
                  <RangePicker
                    format="YYYY-MM-DD HH:mm:ss"
                    value={rangeTime}
                    showTime={{
                      hideDisabledOptions: true,
                      // defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('11:59:59', 'HH:mm:ss')],
                    }}
                    onChange={searchRecordRange}
                  />
                </div>
              </div>
              <div className="record-list" ref={recordRef} onScrollCapture={handleOnScrollCapture}>
                {chatList.length ? (
                  <div>
                    {!chatListHasMore ? (
                      <div className="divider" style={{ marginBottom: 20 }}>
                        没有更多了
                      </div>
                    ) : null}
                    <ChatRow
                      ref={chatRowRef}
                      pageSpin={pageSpin}
                      setPageSpin={setPageSpin}
                      hasMore={chatListHasMore}
                      list={chatList}
                      qwUserId={currentStaff.qwUserId}
                    ></ChatRow>
                  </div>
                ) : (
                  <div className="empty" style={{ marginTop: 175 }}>
                    <img
                      className="empty-img"
                      src={require("@/assets/img/empty1.png")}
                      style={{ width: 70, height: 70 }}
                    />
                    <div className="empty-content">暂无聊天数据</div>
                  </div>
                )}
              </div>
              {/* <input type="file" id="amr-file" accept=".amr"></input> */}
            </Spin>
          </Col>
        </Row>
      </Col>
    </Row>
    </Spin>
    </>
  );
};

export default Staff;
