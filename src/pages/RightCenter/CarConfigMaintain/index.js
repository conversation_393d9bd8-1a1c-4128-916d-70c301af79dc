import React, { useEffect, useState } from 'react'
// import styled from 'styled-components'
import { useSelector } from 'react-redux'

import { Table, message, Button, Row, Col, Divider } from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import { post, get } from '../../../utils/request';
import allUrl from '../../../utils/url';
import { roleJudgment } from '@/utils/authority'
import moment from 'moment'
import { PlusOutlined } from '@ant-design/icons';
import { UniversalOpenWindow } from '@/components/Public/PublicOpenWindow';
import EditConfigInfo from './EditConfigInfo';
import CarConfigDetail from './CarConfigDetail'
import AddConfigInfo from './AddConfigInfo';
import styles from '../antd.module.less'
import publicQueryStyle from '../tableLayout.module.less'

const CarConfigMaintain = (props) => {
    const userInfo = useSelector(state => state.common).userInfo || JSON.parse(localStorage.getItem('userInfo')) || {} 
    const [loading, setLoading] = useState(false)
    const [dataSource, changeDataSource] = useState([])
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)
    const [total, changeTotal] = useState(0)
    // const [tableHeight, setTableHeight] = useState(0)
    const [defaultQuery, setDefaultQuery] = useState({})
    // const [sortCodeList, setSortCode] = useState([])
    // const [ManageInterests, setManageInterests] = useState([])
    // const [DictData, setDictData] = useState([])
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [codeInfoSource, changeCodeInfo] = useState([])
    const [isModalVisibleDetail, setIsModalVisibleDetail] = useState(false);
    const [detailInfoSource, setDetailInfoSource] = useState([])
    const [addDisabled, setAddDisabled] = useState(false);
    const [isAddModalVisible, setIsAddModalVisible] = useState(false);
    const [DictCarData, setDictCarData] = useState([]) // 车型名称接口
    const [powerModelList, setPowerModelList] = useState([]) // 动力方式名称接口

    useEffect(() => {
        if (userInfo) {
            setLoading(true)
            let query = { ...defaultQuery }
            let params = { pageNum: current, pageSize, ...query }
            post(allUrl.CarConfigMaintain.getCarConfigs, { ...params }).then(res => {
                if (res.success && res.resp !== null) {
                    res.resp.map((item, index) => item.key = index + 1)
                    changeDataSource(res.resp)
                    changeTotal(res.total)
                } else {
                    changeDataSource([])
                    changeTotal(0)
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }, [defaultQuery, userInfo, current, pageSize])

    const reloadList = () => {
        if (userInfo) {
            setLoading(true)
            let query = { ...defaultQuery }
            let params = { pageNum: current, pageSize, ...query }
            post(allUrl.CarConfigMaintain.getCarConfigs, { ...params }).then(res => {
                if (res.success && res.resp !== null) {
                    res.resp.map((item, index) => item.key = index + 1)
                    changeDataSource(res.resp)
                    changeTotal(res.total)
                } else {
                    changeDataSource([])
                    changeTotal(0)
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const resetPage = () => {
        changeCurrent(1)
        changePageSize(10)
    }
    const onSearch = (values) => {
        setDefaultQuery(values)
        resetPage()
    }
    // const initPage = (flag) => {
    //     let winH = document.documentElement.clientHeight || document.body.clientHeight;
    //     let h = 0
    //     if (!flag) {
    //         h = winH - 47 - 54 - 345
    //     } else {
    //         h = winH - 47 - 54 - 400
    //     }
    //     setTableHeight(h)
    // }
    useEffect(() => {
        // initPage()
        get(allUrl.common.powerModelList,).then(res => {
            if (res.success) {
                res.resp.forEach(item => {
                    item.name = item.selectValue
                    item.value = item.selectKey
                })
                setPowerModelList(res.resp)
            }
        })

    }, [])
    //查看详情
    const LookAt = (record) => {
        setIsModalVisibleDetail(true)
        let params = {
            id: record.id
        }
        get(allUrl.CarConfigMaintain.getCarConfigObj, { ...params }).then(res => {
            if (res.success) {
                res.resp.map((item, index) => item.key = index + 1)
                setDetailInfoSource(res.resp[0])
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })

    }
    // 创建
    const AddBusinessCode = (record) => {

        setIsAddModalVisible(true);
        // setAddDisabled(false)
        console.log(addDisabled, 'chuangjian')
        // let params= {
        //     detailId:record.detailId
        // }
        // post(allUrl.BusinessCodeMaintain.updateBusinessCode, { ...params }).then(res => {
        //     if (res.success) {
        //         res.resp.map((item, index) => item.key = index + 1)
        //         changeCodeInfo(res.resp)
        //     } else {
        //         // message.error(res.msg)
        //     }
        //     setLoading(false)
        // })
    }

    // 编辑
    const EditAt = (record) => {
        setIsEditModalVisible(true);
        setAddDisabled(true)
        console.log(addDisabled, 'bianji')
        let params = {
            id: record.id
        }
        get(allUrl.CarConfigMaintain.getCarConfigObj, { ...params }).then(res => {
            if (res.success) {
                res.resp.map((item, index) => item.key = index + 1)
                changeCodeInfo(res.resp[0])
            } else {
                // message.error(res.msg)
            }
            setLoading(false)
        })
    }

    const handleCancel = () => {
        setIsEditModalVisible(false);
        setIsModalVisibleDetail(false)
        setIsAddModalVisible(false)
        console.log('quxiao');
    };
    // 车型名称字段接口
    useEffect(() => {
        get(allUrl.common.entryLists, { codes: 'equity_car_type' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                Dt['equity_car_type'].forEach(item => {
                    item.name = item.entryMeaning
                    item.value = item.entryValue
                })
                setDictCarData(Dt['equity_car_type'])
            } else {
                // message.error(res.message)
            }
        })
    }, [])

    let searchList = [
        {
            label: '车型名称', name: 'carType', type: 'Select', placeholder: '请选择', colSpan: 6, data: DictCarData || []
        },
        { label: '车型配置名称', name: 'config', type: 'Input', placeholder: '请输入车型配置名称', colSpan: 6 },
        { label: '车型配置代码', name: 'baseMaterialNo', type: 'Input', placeholder: '请输入车型配置代码', colSpan: 6 },
        { label: '品牌', name: 'brand', type: 'Input', placeholder: '请输入品牌', colSpan: 6 },
        { label: '车型年款', name: 'carOfYear', type: 'Input', placeholder: '请输入车型年款', colSpan: 6 },
        { label: '动力方式', name: 'powerModelCode', type: 'Select', placeholder: '请输入动力方式', colSpan: 6, data: powerModelList || [] },
        { label: '车型市场名称', name: 'carMarketName', type: 'Input', placeholder: '请输入车型市场名称', colSpan: 6 },
    ]
    const InforData = {
        rowKey: record => record.key,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content' },
        columns: [
            { title: '序号', dataIndex: 'index', fixed: 'left', width: 70, render: (text, record, index) => <div style={{ textAlign: 'left' }}>{(current - 1) * pageSize + index + 1}</div> },
            {
                title: '车型名称', dataIndex: 'type', width: 100, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '车型配置名称', dataIndex: 'config', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },

            {
                title: '车型配置代码', dataIndex: 'baseMaterialNo', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '车型市场名称', dataIndex: 'carMarketName', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '品牌', dataIndex: 'brand', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '车型年款', dataIndex: 'carOfYear', width: 110, render: (text) => (text || '-')
            },
            {
                title: '动力方式', dataIndex: 'powerModel', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '汽车厂商', dataIndex: 'manufacturer', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },

            {
                title: '操作', width: 140, fixed: 'right', dataIndex: 'Operation', render: (text, record) => (<div style={{ whiteSpace: 'nowrap' }}>
                    {roleJudgment(userInfo, 'CAR_CONFIG_MAINTAIN_DETAIL') ?
                        <Button type='link' size='small' onClick={() => LookAt(record)}>详情</Button> : null
                    }
                    {roleJudgment(userInfo, 'CAR_CONFIG_MAINTAIN_DETAIL') && roleJudgment(userInfo, 'CAR_CONFIG_MAINTAIN_EDIT') ? <Divider type='vertical' /> : null}
                    {roleJudgment(userInfo, 'CAR_CONFIG_MAINTAIN_EDIT') ?
                        <Button type='link' size='small' onClick={() => EditAt(record)} >编辑 </Button> : null
                    }
                </div>)
            }
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: null,
    };
    return (
        <div className={publicQueryStyle.PublicList}>
            <PublicTableQuery onSearch={onSearch} resetPage={resetPage} searchList={searchList} defaultQuery={defaultQuery} />
            {/* <PublicTableQuery isCatch={true} defaultQuery={defaultpartQuery} isFormDown={false} onSearch={onPartSearch} searchList={partsearchList} /> */}
            <div className="tableData">
                <Row className='tableTitle'>
                    <Col className='text' style={{ color: 'black', fontSize: 20 }}>车型配置列表</Col>
                    {roleJudgment(userInfo, 'CAR_CONFIG_MAINTAIN_BUILD') ?
                        <Col className='bts'>
                            <Button type='primary' icon={<PlusOutlined />} onClick={AddBusinessCode}>创建</Button>
                        </Col> : null}
                </Row>
                <Table {...InforData} className={styles['blue-table']} />
            </div>
            {isAddModalVisible &&
                <AddConfigInfo powerModelList={powerModelList} handleCancel={handleCancel} visible={isAddModalVisible} codeInfoSource={codeInfoSource} reloadList={reloadList} />
            }
            {isEditModalVisible &&
                <EditConfigInfo powerModelList={powerModelList} handleCancel={handleCancel} visible={isEditModalVisible} codeInfoSource={codeInfoSource} reloadList={reloadList} />
            }
            {isModalVisibleDetail &&
                <CarConfigDetail handleCancel={handleCancel} visible={isModalVisibleDetail} detailInfoSource={detailInfoSource} />
            }
        </div>
    )
}
export default CarConfigMaintain
