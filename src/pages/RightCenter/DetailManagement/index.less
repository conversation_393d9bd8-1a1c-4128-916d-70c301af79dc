.detail-PublicList{
  // padding-left: 24px;
  .queryArea{
      margin-left: 24px;
  }
  .ListTiele{
      .add{
          margin-right: 16px;
      }
      .search{
          width: 272px;
      }
  }

}

.PublicDetail{
  background: white;
  .DetailHeader{
      display: flex;
      .DetailHeaderTitle{
          // width: 60%;
          // height: 60px;
          // line-height: 60px;
          padding: 0px 0px 8px 0px;
          font-size: 20px;
          font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
          font-weight: 500;
          // text-align: left;
          color: rgba(0,0,0,0.85);
      }
      // .DetailHeaderTitle:before{
      //     content: "";
      //     display: inline-block;
      //     width: 5px;
      //     height: 25px;
      //     background-color: rgba(31, 27, 250, 0.623);
      //     border-radius: 5px;
      //     margin-right: 8px;
      //     vertical-align: middle;
      // }
      .DetailHeaderButton{
          height: 60px;
          line-height: 60px;
          .ant-btn{
              margin: 0 10px;
          }
      }
  }

  .DetailBox{
      background: #f0f2f5;
      overflow-y: auto;
      height: calc(100vh - 48px - 54px - 36px - 20px);
      .DetailTitle{
          // position: fixed;
          font-size: 20px;
          font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
          font-weight: 500;
          text-align: left;
          color: rgba(0,0,0,0.85);
          padding-bottom: 16px;
          padding-left: 24px;
          background: white;
      }
      .DetailCon{
          margin: 24px;
          background: white;
          .FormRow{
              .KJFW{
                  display: flex;
                  // justify-content: space-between;
                  height: 50px;
                  line-height: 50px;
                  // background: #ededed;
                  padding: 0 32px;
                  margin: 10px 0;
              }
              .KJYM{
                  padding: 20px 32px;
                  .KJYM_Title{
                      height: 54px;
                      line-height: 54px;
                      opacity: 1;
                      background: #fafafa;
                      border-radius: 4px 4px 0px 0px;
                      font-size: 14px;
                      font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                      font-weight: 500;
                      text-align: left;
                      color: rgba(0,0,0,0.65);
                      padding-left: 20px;
                      margin-bottom: 16px;
                  }
                  .KJYM_Con{
                      .KJYM_ConLeft{
                          .ant-tree{
                              height: 460px;
                              overflow: auto;
                          }
                      }
                      .KJYM_ConRight{
                          padding-left: 35px;
                      }
                  }
              }
              .FormRowTitle{
                  font-size: 16px;
                  font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                  font-weight: 500;
                  text-align: left;
                  color: rgba(0,0,0,0.85);
                  padding: 16px 0 16px 32px;
                  border-bottom: solid 1px #E9E9E9;
              }
              .FormRowCon{
                  padding: 24px 32px;
                  .orgTreeBox{
                      .ant-tree{
                          height: 400px;
                          overflow: auto;
                          border: solid 1px #dddedf;
                          padding: 10px;
                          .ant-tree-title{
                              display: flex;
                          }
                      }
                      .orgTreeBoxBtns{
                          padding-top: 16px;
                          text-align: center;
                          .ant-btn{
                              margin: 0 11px;
                          }
                      }
                  }
                  .orgTreeSelected{
                      padding-left: 90px;
                      .orgTreeSelectedTitle{
                          font-size: 14px;
                          font-family: PingFangSC, PingFangSC-Medium, sans-serif; 
                          font-weight: 500;
                          text-align: left;
                          color: rgba(0,0,0,0.65);
                          padding-top: 16px;
                      }
                      .orgTreeSelectedCon{
                          .orgTreeSelectedConItem{
                              padding: 16px;
                              font-size: 14px;
                              font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                              font-weight: 400;
                              text-align: left;
                              color: rgba(0,0,0,0.65);
                              display: flex;
                              justify-content: space-between;
                              border-bottom: solid 1px #dddedf;
                              .orgTreeSelectedConItemTitle{
                                  flex: 1;
                              }
                              .orgTreeSelectedConItem_Clear{
                                  margin-right: 20px;
                                  font-size: 14px;
                                  font-family: PingFangSC, PingFangSC-Regular, sans-serif; 
                                  font-weight: 400;
                                  text-align: left;
                                  color: #1890ff;
                                  cursor: pointer;
                              }
                          }
                      }
                  }
                  .roleList{
                      width: 100%;
                  }
              }
          }
      }
  }
  .DetailBtns{
      height: 56px;
      line-height: 56px;
      text-align: right;
      margin-right: 24px;
      border-top: solid 1px #e8e8e8;
      // box-shadow: 0px 2px 4px -4px #444 inset;
  }
  .PublicList_FormQuery{
    padding-top: 16px;
    padding-left: 24px;
    .ant-col-7{
        .ant-form-item{
            .ant-form-item-control-input{
                width: 90%;
                .ant-form-item-control-input-content{
                    .ant-picker{
                        width: 100%;
                    }
                }
            }
        }
    }
    .FormQuerySubmit{
        display:flex;
        justify-content: flex-end;
        .operationButtons{
            span{
                color: #1890ff;
                cursor: pointer;
                .anticon{
                    margin-left: 6px;
                }
            }
        }
    }
}
}
.modal-notify{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .top-item{
         margin-top: 30px;
         margin-bottom: 43px;
         display: flex;
         .icon{
            // margin-top: -55px;
            margin-right: 8px;
         }
         .text{
            display: inline-block;
            width: 288px;
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: rgba(0,0,0,0.85);
            line-height: 26px;
        }
    }
    .btn{
        width: 150px;
        height: 32px;
    }
    
}