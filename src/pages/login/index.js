import React, { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { useHistory } from "react-router-dom";
import { message } from "antd";
import { post, get } from "../../utils/request";
import allUrl from "../../utils/url";
import { GetUserInfo } from "../../actions/async";
import moment from "moment";
import baseURL from "@/baseURL";
import { useDispatch } from 'react-redux';
import './index.less'
const currentYear = new Date().getFullYear();
function LoginIframe(props) {
  const history = useHistory();
  const dispatch = useDispatch();
  const [isFirstGetTicketLogin, setIsFirstGetTicketLogin] = useState(true)
  console.log('baseURL.SSO_SDK', baseURL)
  // 赛力斯SSO第三方登录登录sdk
    async function loadSSO() {
    if (!window.createLoginComp) {
      const script = document.createElement("script");
      const envUrl = baseURL.SSO_SDK // 此处以开发环境的sdk文件为例，不同环境需要根据环境变量换成不同文件
      script.src = envUrl + '?t=' + Date.now(); // 在 URL 后添加时间戳
      document.head.appendChild(script);
      await new Promise((resolve) => {
        script.onload = resolve;     
      });
      console.log('loadSSO', window.createLoginComp)
    }
    // sdk加载成功后调用初始化方法
    window.createLoginComp({
      info: {
        // 配置参数
        boxId: "boxId", // 必填，登录框外层div标签的id
        appId: "927656", // 必填，该应用的appId
        redirect: window.location.origin, // 必填，该应用的授权回调地址（登录成功后跳转的地址，一般为home页，和接口无关）
        type: 2, // 必填 登录类型，2代表web登录页
      },
      loginSuccessCallback: (e) => {
       // 登录成功后的回调函数，返回的e是一个数据对象。
       // e.message为用于获取用户信息的ticket，用于获取用户信息。       
        console.log('loginSuccessCallback----e', e)
        post(allUrl.Authority.ticketLogin,{
            applicationId: 1,
            clientId: 927656,
            ticket:e.message
        }).then(res=>{
            if (res.success) {
                console.log('res', res)
                setIsFirstGetTicketLogin(false)
                fetchUserInfo()
                let Dt = res.resp[0];
                Cookies.set("scrm_token", Dt.accessToken);
                Cookies.remove("isTransfer");
                sessionStorage.setItem("changePwdStatus", Dt.changePwdStatus);
                // (GetUserInfo()).then((info) => {
                //   console.log("用户信息返回值", info);
                //   window.location.href = '/#/home'
                // });
            } else {
                message.error(res.msg)
            }
            })
        
      },
      messageCallback:(e) => {
        console.log('messageCallback---e', e)
        if (e.type === "success") {
            message.success(e.message);
          }
          if (e.type === "error") {
            message.error(e.message);
          }
      },
    }).then(({ destroy }) => {
        // 存储 loginCompDestroy 函数，以便在组件卸载时调用
        window.destroyLoginComp = destroy;
      })
      .catch((error) => {
        // 处理错误
        console.error("Login Component Error:", error);
      });
  }

  useEffect(() => {
    if( Cookies.get("scrm_token") && isFirstGetTicketLogin) {
      window.location.href = '/#/home'
    } else {
      loadSSO()
    }
  }, [])
//   useEffect(() => {
//     if (Cookies.get('sso_token')) {
//         history.push('/home')
//     } 
//   }, [history]);
const fetchUserInfo = async () => {
    try {
      const info = await dispatch(GetUserInfo());
      console.log("用户信息返回值", info);
      window.location.href = '/#/home'; // 如果需要重定向，可以解除注释
    } catch (error) {
      console.error("获取用户信息时出错", error);
    }
  };

  return (
    <div className="login">
      <div className="login-wrapper">
        <div className="login-header">
          {/* <img className="img" src={seresLog} width='135px' height='40px'></img> */}
          <div className="headerText">SERES协同系统</div>
        </div>
        <div className="login-body">
          <div id="box_wrapper" className="sso">
            <div id="boxId"></div>
          </div>
        </div>
        <div className="login-footer">
          <div>Copyright @2023-{currentYear} 赛力斯汽车销售有限公司</div>
        </div>
      </div>
      {/* <script src="https://scrm-test-oss.oss-cn-shanghai.aliyuncs.com/temp/seres.sso-sdk-1.0.0.js"></script> */}
    </div>
  );
}

export default LoginIframe;
