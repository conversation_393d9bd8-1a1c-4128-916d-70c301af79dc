import React, { useEffect, useState } from 'react'
import { Modal, Table, Row, Col, Button } from 'antd'
import './ResultModal.less'

const statusMAP = {
    0: '未处理',
    1: '成功',
    2: '校验错误',
    3: '处理异常'
}
const ResultModal = (props) => {
    const { defaultQuery } = props
    const [importInfoDTO, setImportInfoDTO] = useState({})
    const [total, setTotal] = useState(0)
    const [successCount, setSuccessCount] = useState(0)
    const [failCount, setFailCount] = useState(0)
    const [loading, setLoading] = useState(true)
    const [dataSource, changeDataSource] = useState([])
    const [current, changeCurrent] = useState(1)
    const [pageSize, changePageSize] = useState(10)

    useEffect(() => {
        let { resultData } = props
        if (resultData) {
            setLoading(false)
            setSuccessCount(resultData.successCount)
            setFailCount(resultData.failCount)
            setTotal(resultData.failCount)
            setImportInfoDTO(resultData.importInfoDTO)
            changeDataSource(resultData.fail || [])
        }
    }, [props])

    const onCancel = () => {
        props.handleCancel()
        props.onSearch({ _t: new Date(), ...defaultQuery })
    }
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const InforData = {
        rowKey: record => record.id,
        bordered: true,
        dataSource,
        loading,
        scroll: { x: 'max-content' },
        columns: [
            {
                title: '序号', dataIndex: 'importBatch', width: 100, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
            {
                title: '车型VIN', dataIndex: 'vin', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },

            {
                title: '导入结果', dataIndex: 'syncStatus', width: 260, render: (text) => {
                    if (Number(text) > 0) {
                        if (text == 1) {
                            return <span><span className='success-dot'></span>{statusMAP[text]}</span>
                        } else {
                            return <span><span className='fail-dot'></span>{statusMAP[text]}</span>
                        }
                    } else {
                        return '-'
                    }
                }
            },
            {
                title: '原因', dataIndex: 'errorMsg', width: 260, render: (text) => {
                    if (text) {
                        return text
                    } else {
                        return <div>
                            {'—'}
                        </div>
                    }
                }
            },
        ],
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: null,
    };

    return (
        <Modal
            width={900}
            visible={props.showResult}
            onCancel={onCancel}
            title="数据上传操作"
            footer={<Button type="primary" onClick={onCancel}>我知道了</Button>}
        >
            <div className='modal-container'>
                <div className='modal-top'>
                    <Row style={{ marginBottom: '25px' }}>
                        <Col span={8}>{`导入批次：${importInfoDTO ? importInfoDTO.importDateBatch : '-'}`}</Col>
                        <Col span={8}>{`导入时间：${importInfoDTO ? importInfoDTO.importTableDate : '-'}`}</Col>
                        <Col span={8}>{`操作人员：${importInfoDTO ? importInfoDTO.createBy : '-'}`}</Col>
                    </Row>
                    <Row>
                        <Col>
                            <span>附件：</span>
                            <img style={{ width: '18px', height: '18px', marginTop: '-3px' }} src={require('../../../assets/img/word-icon.png')}></img>
                            <span className='file-name'>{importInfoDTO ? importInfoDTO.fileName : '-'}</span>
                        </Col>
                    </Row>
                </div>
                <div className='modal-bottom'>
                    <div className='bottom-title'>
                        <span className='black-text'>数据导入失败列表</span>
                        <span className='red-text'>{`导入数据：共计${successCount + failCount}条，成功：${successCount}条，失败${failCount}条，此数据会在数据列表整体保存`}</span>
                    </div>
                    <Table {...InforData} />
                </div>
            </div>
        </Modal>
    )
}
export default ResultModal