import { Button, Row, Spin, SpinProps } from 'antd';
import classnames from 'classnames';
import React, { FC, HtmlHTMLAttributes, PropsWithChildren } from 'react';
import './index.less';

export interface PageContainerProps extends HtmlHTMLAttributes<HTMLDivElement> {
  loading?: boolean;
  spin?: Omit<SpinProps, 'spinning'>;
  /**
   * 是否自动加上 padding
   */
  ghost?: boolean;
  /**
   * 是否可回退
   */
  backable?: boolean;
  /**
   * 是否将头部固定在顶部
   */
  fixedHeader?: boolean;
}

const PageContainer: FC<PropsWithChildren<PageContainerProps>> = ({
  loading = false,
  children,
  spin,
  className,
  ghost = true,
  backable = false,
  fixedHeader,
  ...props
}) => {
  return (
    <div
      className={classnames(
        'page-container',
        {
          'page-container-ghost': ghost,
        },
        className
      )}
      {...props}
    >
      <Spin {...spin} spinning={loading}>
        {loading ? null : backable ? (
          <>
            <Row
              style={{ marginBottom: 16 }}
              justify="end"
              className={classnames({
                'page-container-header': fixedHeader,
              })}
            >
              <Button onClick={() => window.history.back()}>返回</Button>
            </Row>
            {children}
          </>
        ) : (
          children
        )}
      </Spin>
    </div>
  );
};

export default PageContainer;
