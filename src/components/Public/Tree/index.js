import { Tree } from 'antd';
import style from './style.module.less';
import React from 'react';
const treeData = [
  {
    title: 'parent 1',
    key: '0-0',
    children: [
      {
        title: 'parent 1-0',
        key: '0-0-0',
        disabled: true,
        children: [
          {
            title: 'leaf',
            key: '0-0-0-0',
            className: 'leafNode',
          },
          {
            title: 'leaf',
            key: '0-0-0-1',
            className: 'leafNode',
          },
        ],
      },
      {
        title: 'parent 1-1',
        key: '0-0-1',
        children: [
          {
            title: <span style={{ color: '#1890ff' }}>sss</span>,
            key: '0-0-1-0',
            className: 'leafNode',
          },
          {
            title: <span style={{ color: '#1890ff' }}>aaa</span>,
            key: '0-0-1-1',
            className: 'leafNode',
          },
          {
            title: <span style={{ color: '#1890ff' }}>bbb</span>,
            key: '0-0-1-2',
            className: 'leafNode',
          },
          {
            title: <span style={{ color: '#1890ff' }}>ccc</span>,
            key: '0-0-1-3',
            className: 'leafNode',
          },
          {
            title: <span style={{ color: '#1890ff' }}>ddd</span>,
            key: '0-0-1-4',
            className: 'leafNode',
          },
        ],
      },
    ],
  },
];

const Index= () => {
  const onSelect = (selectedKeys, info) => {
    console.log('selected', selectedKeys, info);
  };

  const onCheck= (checkedKeys, info) => {
    console.log('onCheck', checkedKeys, info);
  };

  return (
    <Tree
      className={style.tree}
      checkable
      defaultExpandedKeys={['0-0-0', '0-0-1']}
      defaultSelectedKeys={['0-0-0', '0-0-1']}
      defaultCheckedKeys={['0-0-0', '0-0-1']}
      onSelect={onSelect}
      onCheck={onCheck}
      treeData={treeData}
    />
  );
};

export default Index;
