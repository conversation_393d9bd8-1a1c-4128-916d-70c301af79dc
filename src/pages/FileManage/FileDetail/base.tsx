import {
  getSeresFileOperateGenerateUrl,
  postSeresFileItemOrganizationList,
  postSeresFileItemPostList,
} from '@/apis/file-manage';
import ProTable from '@/components/pro-table';
import PublicTooltip from '@/components/Public/PublicTooltip';
import { usePaginationTableListStyleRequest } from '@/hooks/table';
import { FileType, getFileType } from '@/utils/file';
import { Card, Descriptions, Image, Modal, Space, Typography } from 'antd';
import moment from 'moment';
import React, { FC, useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import {
  FILE_BIZ_TYPE_MAP,
  FILE_DELIVER_TYPE_MAP,
  FILE_PERMISSION_MAP,
  FILE_POST_TYPE_MAP,
  FILE_STATUS_MAP,
  FileBizType,
  FileDeliverType,
  FilePermission,
  FilePostType,
  FileStatus,
} from '../constants';
import { useDeliverTypeLabel } from './hooks';

export interface FileBaseInfoProps {
  file?: FileManage.FileItemDetailVO;
  loading: boolean;
}

const FileBaseInfo: FC<FileBaseInfoProps> = ({ file: data, loading }) => {
  const [storeModalOpen, setStoreModalOpen] = useState(false);
  const [jobModalOpen, setJobModalOpen] = useState(false);

  const storeTableRequest = usePaginationTableListStyleRequest(({ current, pageSize, params }) =>
    postSeresFileItemOrganizationList({ ...params, pageNum: current, pageSize })
  );

  const jobTableRequest = usePaginationTableListStyleRequest(({ current, pageSize, params }) =>
    postSeresFileItemPostList({ ...params, pageNum: current, pageSize })
  );

  const deliverTypeLabel = useDeliverTypeLabel(data);

  const [imageUrl, setImageUrl] = useState<string | undefined>();
  const fileType: FileType = useMemo(() => {
    return getFileType(data?.name as string);
  }, [data]);

  useEffect(() => {
    if (fileType === FileType.IMAGE && data?.ossObjectName) {
      const fetchImageUrl = async () => {
        try {
          const [url] = await getSeresFileOperateGenerateUrl({
            objectName: data?.ossObjectName as string,
          });
          setImageUrl(url);
        } catch (error) {
          console.error('Error fetching the image:', error);
        }
      };
      fetchImageUrl();
    }
  }, [data, fileType]);

  return (
    <Card title="基础信息" loading={loading}>
      {data ? (
        <Descriptions bordered>
          <Descriptions.Item label="业务类型">{FILE_BIZ_TYPE_MAP[data.bizType as FileBizType]}</Descriptions.Item>
          <Descriptions.Item label="目录" contentStyle={{ maxWidth: 300 }}>
            <PublicTooltip title={data.parentDirPath}>{data.parentDirPath}</PublicTooltip>
          </Descriptions.Item>
          <Descriptions.Item label="文件">
            {fileType === FileType.IMAGE ? <Image src={imageUrl} height={100} /> : null}
            {fileType === FileType.PDF ? <Link to={`/FileManage/FileView/${data.id}`}>{data.name}</Link> : null}
          </Descriptions.Item>
          <Descriptions.Item label="失效日期">{moment(data.expireTime).format('YYYY-MM-DD')}</Descriptions.Item>
          <Descriptions.Item label="浏览下载权限">
            {FILE_PERMISSION_MAP[data.permission as FilePermission]}
          </Descriptions.Item>
          <Descriptions.Item label="状态">{FILE_STATUS_MAP[data.status as FileStatus]}</Descriptions.Item>
          <Descriptions.Item label="下发范围">
            <Space align="center">
              {FILE_DELIVER_TYPE_MAP[data.deliverType as FileDeliverType]}
              {[FileDeliverType.SPECIFIC_STORE, FileDeliverType.SPECIFIC_SUPPLIER].includes(
                data.deliverType as FileDeliverType
              ) ? (
                <Typography.Link onClick={() => setStoreModalOpen(true)}>查看</Typography.Link>
              ) : null}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="岗位">
            <Space align="center">
              {FILE_POST_TYPE_MAP[data.postType as FilePostType]}
              {[FilePostType.SPECIFIC_POST].includes(data.postType as FilePostType) ? (
                <Typography.Link onClick={() => setJobModalOpen(true)}>查看</Typography.Link>
              ) : null}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="下发人">{data.sendBy || '-'}</Descriptions.Item>
          <Descriptions.Item label="下发时间">
            {data.sendTime ? moment(data.sendTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="更新人">{data.updateBy || '-'}</Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {moment(data.updateTime).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="总部人员" span={3}>
            {data.seresUser}
          </Descriptions.Item>
          <Descriptions.Item label="供应商指定人员" span={3}>
            {data.specificUserList?.map(user => `${user.userName}/${user.position}`).join('，')}
          </Descriptions.Item>
          <Descriptions.Item label="附件" span={3}>
            <Space wrap>
              {data.attachmentList?.map(attachment => (
                <div
                  style={{
                    display: 'inline-block',
                    maxWidth: 150,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    verticalAlign: 'middle',
                  }}
                >
                  {attachment.linkFileId ? (
                    <>
                      {getFileType(attachment.fileName ?? '') === FileType.IMAGE ? (
                        <Image src={attachment.url} width={80} height={50} />
                      ) : null}
                      {getFileType(attachment.fileName ?? '') === FileType.PDF ? (
                        <Link key={attachment.linkFileId} to={`/FileManage/FileView/${attachment.linkFileId}`}>
                          {attachment.fileName}
                        </Link>
                      ) : null}
                    </>
                  ) : (
                    <a href={attachment.url} download={attachment.fileName}>
                      {attachment.fileName}
                    </a>
                  )}
                </div>
              ))}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      ) : null}
      <Modal
        maskClosable={false}
        title={`查看${deliverTypeLabel}`}
        footer={null}
        open={storeModalOpen}
        onCancel={() => setStoreModalOpen(false)}
      >
        <ProTable
          ghost
          params={{ fileId: data?.id }}
          rowKey="code"
          columns={[
            {
              title: '编号',
              dataIndex: 'code',
            },
            {
              title: '名称',
              dataIndex: 'name',
            },
          ]}
          request={storeTableRequest}
        />
      </Modal>
      <Modal
        maskClosable={false}
        title={`查看岗位`}
        footer={null}
        open={jobModalOpen}
        onCancel={() => setJobModalOpen(false)}
      >
        <ProTable
          ghost
          params={{ fileId: data?.id }}
          rowKey="code"
          columns={[
            {
              title: '编号',
              dataIndex: 'code',
            },
            {
              title: '名称',
              dataIndex: 'name',
            },
          ]}
          request={jobTableRequest}
        />
      </Modal>
    </Card>
  );
};

export default FileBaseInfo;
