.customer-info{
    background:#F0F2F5;
    padding: 24px 24px 0 24px;
    .customer-info-con{
        background:white;
        padding:32px;
        display:flex;
        .customer-info-con-left{
            height:86px;
            width: 86px;
            img{
                height: 100%;
                width: 100%;
                border-radius: 8px;
            }
        }
        .customer-info-con-right{
            flex: 1;
            margin-left: 24px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .title{
                .name{
                    font-size: 16px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: rgba(0, 0, 0, 0.85);
                }
                .type{
                    font-size: 16px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #F8A006;
                    margin-left: 4px;
                }
                .num{
                    font-size: 16px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 0.39);
                }
            }
            .info{
                .name{
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 0.65);
                    .label{
                        font-weight: 600;
                        color: rgba(0, 0, 0, 0.85);
                    }
                    .notice{
                        display: flex;
                        .ant-typography{
                            flex: 1;
                            white-space: break-spaces
                        }
                    }
                }
            }
        }
    }
}