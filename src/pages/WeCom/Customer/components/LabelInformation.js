import React,{useEffect, useState} from 'react'
import { Modal,Table } from 'antd'
import _ from 'lodash'

const LabelInformation = props =>{
    const {visible,onCancel,recordObj} = props
    const [dataSource,setDataSource] = useState([])
    const  changeData = (data,field)=>{
        let count = 0;//重复项的第一项
        let indexCount = 1;//下一项
        while (indexCount<data.length){
            var item = data.slice(count,count+1)[0];//获取没有比较的第一个对象
            if(!item.rowSpan){
                item.rowSpan = 1;//初始化为1
            }
            if(item[field] === data[indexCount][field]){//第一个对象与后面的对象相比，有相同项就累加，并且后面相同项设置为0
                item.rowSpan++;
                data[indexCount].rowSpan = 0;
            }else {
                count = indexCount;
            }
            indexCount++;
        }
        return data
    }
    const columns = [
            {title:'添加人姓名',dataIndex:'addUserName',render:(text,record,index)=>{
                const obj = {
                    children: <div>{text}</div>,
                    props: {
                        rowSpan:record.rowSpan
                    },
                };
                return obj
            }},
            {title:'标签名称',dataIndex:'tagName'},
            {title:'标签类型',dataIndex:'tagName'},
            {title:'分组名称',dataIndex:'groupName'},
        ]
    useEffect(()=>{
        if(Object.values(recordObj).length && recordObj.addUsers && recordObj.addUsers.length){
            let arr = []
            recordObj.addUsers.forEach(item=>{
                if(item.tags && item.tags.length){
                    item.tags.forEach(ele=>{
                        ele.addUserName = item.addUserName
                        arr.push(ele)
                    })
                }
            })
            
            setDataSource(changeData(_.cloneDeep({arr}).arr,'addUserName'))
        }
    },[recordObj])
    return <Modal title='标签信息' visible={visible} onCancel={onCancel} width={600} footer={null} destroyOnClose={true}>
            <Table rowKey='id' bordered columns={columns} dataSource={dataSource} pagination={false} />
    </Modal>
}
export default LabelInformation