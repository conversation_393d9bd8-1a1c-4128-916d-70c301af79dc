import './index.less'
import React,{useEffect, useState,useRef} from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {Row,Col,Table,Button,Divider,Popconfirm, message,Tabs} from 'antd'
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment'
import {UniversalOpenWindow} from '@/components/Public/PublicOpenWindow'
import ClueEdit from './components/ClueEdit'
import ClueVictory from './components/ClueVictory'
import Distribution from './components/Distribution'
import PersonnelModal from './components/PersonnelModal'
import { post ,get} from '@/utils/request';
import allUrl from '@/utils/url'
// import ExportExcel from './components/ExportExcel'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import {getDict} from '../../../actions/async'
import ExtendedColumn from '@/components/Public/ExtendedColumn'
import {roleJudgment} from '@/utils/authority'
import _ from "lodash";
import {trim} from '@/utils'
const { TabPane } = Tabs;
const ClueList = (props) =>{
    const childRef0 = useRef(null);
    const childRef1 = useRef(null);
    const childRef2 = useRef(null);
    const dispatch = useDispatch()
    const {userInfo} = useSelector(state => state.common)
    const [loading,setLoading] = useState(false)
    const [dataSource0,changeDataSource0] = useState([])
    const [dataSource1,changeDataSource1] = useState([])
    const [dataSource2,changeDataSource2] = useState([])
    const [current,changeCurrent] = useState(1)
    const [pageSize,changePageSize] = useState(10)
    const [total,changeTotal] = useState(0)
    const [selectedRowKeys,changeSelectedRowKeys] = useState([])
    const [selectedRows,changeSelectedRows] = useState([])
    const [ClueEditVisible,setClueEditVisible] = useState(false)
    const [ClueVictoryVisible,setClueVictoryVisible] = useState(false)
    const [recordData,setRecordData] = useState({})
    const [userlist,setUserList] = useState([])
    const [clueOriginArr,setClueOriginArr] = useState([])
    const [scrm_lead_planStatus,setscrm_lead_planStatus] = useState([])
    const [scrm_lead_onlyLeadStatus,setscrm_lead_onlyLeadStatus] = useState([])
    const [scrm_clue_carType,setscrm_clue_carType] = useState([])
    const [DealerOptions,setDealerOptions] = useState([])
    const [SalesmanOptions,setSalesmanOptions] = useState([])
    const [PersonnelModalVisible,setPersonnelModalVisible] = useState(false)
    const [DistributionVisible,setDistributionVisible] = useState(false)
    // const [ExportExcelVisible,setExportExcelVisible] = useState(false)
    const [tabsKey,setTabsKey] = useState(sessionStorage.getItem('ClueListTabsKey') || '1')
    const [defaultQuery,setDefaultQuery] = useState({})
    const [PersionType,setPersionType] = useState(0)
    const PageChange = (current, pageSize) => {
        changeCurrent(current)
        changePageSize(pageSize)
    }
    const onSearch = (values) =>{
        changeCurrent(1)
        if (values.createTime && values.createTime.length) {
            values.createTimeStart = moment(values.createTime[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
            values.createTimeEnd = moment(values.createTime[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
            delete values.createTime
        }
        if (values.nextFollowTime && values.nextFollowTime.length) {
            values.nextFollowTimeStart = moment(values.nextFollowTime[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
            values.nextFollowTimeEnd = moment(values.nextFollowTime[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
            delete values.nextFollowTime
        }
        setDefaultQuery(values)

    }
    const LookAt = (record) =>{
            let data = {
                onlyLeadId: record.onlyLeadId,
                dealerCode: record.dealerCode,
                Type: 'LookAt',
                title:'线索详情'
            }
            UniversalOpenWindow({
                JumpUrl:'/ClueManagement/ClueList',data,history:props.history
            })
    }
    const assuredVictory = (record)=>{
        setRecordData(record)
        setClueVictoryVisible(true)
    }
    const RowEdit = (record) =>{
            setRecordData(record)
            setClueEditVisible(true)
    }
    const DistributionClue = (record) =>{
        setPersionType(3)
        setRecordData(record)
        setDistributionVisible(true)
}
    const renderOperation2 = (text, record) => {
        //跟进计划
        return  <div style={{ color: '#1890ff' }}>
            {
                roleJudgment(userInfo,'PERM_CLUELIST_LEADPLAN_DETAIL') ?
                <span style={{ cursor: 'pointer' }} onClick={() => LookAt(record)}>详情</span>
                :null
            }
            {
                roleJudgment(userInfo,'PERM_CLUELIST_TRANSFER')&&record.planStatus !==5&&record.planStatus !==7 ?
                [roleJudgment(userInfo,'PERM_CLUELIST_LEADPLAN_DETAIL') ?
                    <Divider key={1} type="vertical" />:null,
                    <span key={2} style={{cursor:'pointer'}} onClick={()=>{
                        setPersionType(1)
                        setRecordData(record)
                        setPersonnelModalVisible(true)
                    }}>转移</span>,
                ]:null
            }
                        {
                roleJudgment(userInfo,'PERM_CLUELIST_VICTORY_EXAMINE')&&record.planStatus == 6 ?
                [roleJudgment(userInfo,'PERM_CLUELIST_LEADPLAN_DETAIL')|| roleJudgment(userInfo,'PERM_CLUELIST_TRANSFER')?
                    <Divider key={1} type="vertical" />:null,
                    <span key={2} style={{cursor:'pointer'}} onClick={()=>assuredVictory(record)}>战赢审核</span>,
                ]:null
            }
        </div>
    }
    const renderOperation1 = (text, record) => {
        //唯一线索
        return  <div style={{ color: '#1890ff' }}>
            {
                 (roleJudgment(userInfo,'PERM_CLUELIST_STORE_DETAIL')&& tabsKey ==='1') ?
                [
                    <span key={2} style={{cursor:'pointer'}} onClick={()=>LookAt(record)}>详情</span>,
                ]:null
            }
            {
                 (roleJudgment(userInfo,'PERM_CLUELIST_FACTORY_DETAIL')&& tabsKey ==='0') ?
                [
                    <span key={2} style={{cursor:'pointer'}} onClick={()=>LookAt(record)}>详情</span>,
                ]:null
            }
            {
                roleJudgment(userInfo,'PERM_CLUELIST_STORE_DISTRIBUTION')&& tabsKey ==='1'&&record.leadStatus == 2 ?
                [ (roleJudgment(userInfo,'PERM_CLUELIST_STORE_DETAIL')&& tabsKey==='1')  ?
                <Divider key={1} type="vertical" />:null,
                <span style={{ cursor: 'pointer' }} onClick={() => DistributionClue(record)}>分配</span>
                ]
                :null
            }
             {
                roleJudgment(userInfo,'PERM_CLUELIST_EDIT')&& tabsKey ==='0' ?
                [ (roleJudgment(userInfo,'PERM_CLUELIST_FACTORY_DETAIL')&& tabsKey ==='0') ?
                <Divider key={1} type="vertical" />:null,
                <span style={{ cursor: 'pointer' }} onClick={() => RowEdit(record)}>编辑</span>
                ]
                :null
            }
        </div>
    }
    const getData = () =>{
        setLoading(true)
        if(tabsKey ==='0'){ //厂端
            let query = {...defaultQuery}
            let data = {current:current,pageSize,params:{...query}}
            post(allUrl.ClueManagement.queryOnlyLeadList,{...data}).then(res=>{
                if(res.success){
                    changeDataSource0(res.resp[0].list)
                    changeSelectedRowKeys([])
                    changeSelectedRows([])
                    changeTotal(res.resp[0].pagination.total)
                }else{
                    changeDataSource0([])
                    message.error(res.msg)
                }
                setLoading(false)
            })
        }else if(tabsKey ==='1'){//店端
            let data = {current:current,pageSize,params:{...defaultQuery}}
            post(allUrl.ClueManagement.queryLeadDealerList,{...data}).then(res=>{
                if(res.success){
                    changeDataSource1(res.resp[0].list)
                    changeSelectedRowKeys([])
                    changeSelectedRows([])
                    changeTotal(res.resp[0].pagination.total)
                }else{
                    changeDataSource1([])
                    message.error(res.msg)
                }
                setLoading(false)
            })
        }else if(tabsKey ==='2'){//跟进线索计划
            let data = {current:current,pageSize,params:{...defaultQuery}}
            post(allUrl.ClueManagement.queryLeadPlanList,{...data}).then(res=>{
                if(res.success){
                    changeDataSource2(res.resp[0].list)
                    changeSelectedRowKeys([])
                    changeSelectedRows([])
                    changeTotal(res.resp[0].pagination.total)
                }else{
                    changeDataSource2([])
                    message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }
    const tabsCallback = (key) => {
        sessionStorage.setItem('ClueListTabsKey',key)
        console.log(key);
        if(key === '0'){
            if(childRef0.current){
                onSearch(childRef0.current.query)
              }else{
                setDefaultQuery({})
              }
        }
        if(key === '1'){
           if(childRef1.current){
            onSearch(childRef1.current.query)
          }else{
            setDefaultQuery({})
          }
        }
        if (key === '2'){
            if(childRef2.current){
                onSearch(childRef2.current.query)
              }else{
                setDefaultQuery({})
              }
        }
        changeSelectedRowKeys([])
        changeSelectedRows([])
        setTabsKey(key)
        changeCurrent(1)
        changePageSize(10)

    }
    const dealersSearch =_.debounce((value)=>{
        value =trim(value)
        get(allUrl.ClueManagement.findDealersByKeywords, {keywords:value,}).then(res => {
            if (res.success) {
                let Dt =res.resp.map((item,index)=>{
                    return{name :item.name,
                        value :item.dealerCode,key:index+1}
                })
                setDealerOptions([...Dt])
            } else {
                // message.error(res.msg)
            }
        })

    }, 500);
    const SalesmanSearch =_.debounce((value)=>{
        value =trim(value)
        get(allUrl.ClueManagement.findSalesmanByKeywords, {keywords:value,}).then(res => {
            if (res.success) {
                let Dt =res.resp.map((item,index)=>{
                    return{name :item.userName,
                        value :item.userId,key:index+1}
                })
                setSalesmanOptions([...Dt])
            } else {
                // message.error(res.msg)
            }
        })

    }, 500);
    useEffect(()=>{
        getData()
    },[defaultQuery,userInfo,current,pageSize])
    useEffect(()=>{
        get(allUrl.ClueManagement.carTypes).then(res=>{
            // 线索车辆
            console.log(res)
            if(res.success){
                res.resp.forEach(item=>{
                    item.name = item.value
                    item.value = item.key
                })
                setscrm_clue_carType(res.resp)
            }else{
                // message.error(res.msg)
            }
        })
        get(allUrl.ClueManagement.channels).then(res=>{
            // 线索渠道
            console.log(res)
            if(res.success){
                res.resp.forEach(item=>{
                    item.name = item.value
                    item.value = item.code
                })
                setClueOriginArr(res.resp)
            }else{
                // message.error(res.msg)
            }
        })
        dispatch(getDict({codes:'scrm_lead_planStatus'})).then(({payload})=>{
            // 跟进计划状态
            setscrm_lead_planStatus(payload)
        })
        dispatch(getDict({codes:'scrm_lead_onlyLeadStatus'})).then(({payload})=>{
            // 唯一线索状态
            setscrm_lead_onlyLeadStatus(payload)
        })
    },[])
    const [columns2, setColums2] = useState([
        { title: '跟进计划ID', dataIndex: 'id',width:140,},
        { title: '唯一线索ID', dataIndex: 'onlyLeadId', width: 140},
        { title: '姓名', dataIndex: 'customerName', width: 100 },
        { title: '手机号', dataIndex: 'mobilePhone', width: 100 },
        { title: '跟进状态', dataIndex: 'planStatus', width: 140 ,render:text=>text && text==1?'待分配门店':
        text==2?'待分配销售':text==3?'待跟进':text==4?'跟进中':text==5?'战败':text==6?'战赢待确定':text==7?'战赢':''},
        { title: '线索等级', dataIndex: 'intentionLevel', width: 140,render:text=>text && text==3?'低意向':text==2?'中意向':text==1?'高意向':''},
        { title: '线索渠道', dataIndex: 'channelName', width: 100 },
        { title: '意向车型', dataIndex: 'intentionCarType', width: 100 },
        { title: '是否有效', dataIndex: 'leadUseful', width: 100,render:text=>text && text==2?'未确定':text==1?'有效':text==0?'无效':'' },
        { title: '是否邀约', dataIndex: 'isInvitation', isExtend: true, width: 100,render:text=>text && text==1?'是':text==0?'否':''},
        { title: '是否试驾', dataIndex: 'isTestDriver', isExtend: true, width: 100 ,render:text=>text && text==1?'是':text==0?'否':''},
        { title: '是否大定', dataIndex: 'isSettled',  isExtend: true, width: 100,render:text=>text && text==1?'是':text==0?'否':'' },
        { title: '关联订单号', dataIndex: 'orderNo',  isExtend: true, width: 140 },
        { title: '跟进计划创建时间', dataIndex: 'createTime', width: 180 },
        { title: '最近跟进时间', dataIndex: 'lastFollowTime', isExtend: true, width: 140 },
        { title: '下次跟进时间', dataIndex: 'nextFollowTime', isExtend: true, width: 140 },
        { title: '预约试驾时间', dataIndex: 'reservationDateTime', isExtend: true, width: 140 },
        { title: '实际试驾时间', dataIndex: 'beginTime', isExtend: true, width: 140 },
        { title: '大定时间', dataIndex: 'settledTime', isExtend: true, width: 100 },
        { title: '跟进销售', dataIndex: 'saleConsultantName', width: 100 },
        { title: '所属门店', dataIndex: 'followDealerName', width: 200 },
        { title: '操作', width: 180, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation2(text, record) }
    ])
    let newColumns2 = _.cloneDeep({ columns2 }).columns2
    for (let i = 0; i < newColumns2.length; i++) {
        if (JSON.stringify(newColumns2[i]['checked']) !== undefined && !newColumns2[i].checked) {
            newColumns2.splice(i, 1)
            i--
        }
    }
    const InforData = {
        rowKey: record => tabsKey=== '0'? record.onlyLeadId : record.id,
        bordered: true,
        dataSource:tabsKey=== '0'? dataSource0 : tabsKey=== '1'? dataSource1 :tabsKey=== '2'?dataSource2 :[],
        loading,
        scroll: { x: 'max-content' },
        columns:tabsKey === '0' ? 
        [
            { title: '唯一线索ID', dataIndex: 'onlyLeadId', width: 140},
            { title: '姓名', dataIndex: 'customerName', width: 100 },
            { title: '手机号', dataIndex: 'mobilePhone', width: 100 },
            { title: '线索等级', dataIndex: 'intentionLevel', width: 140 ,render:text=>text && text==3?'低意向':text==2?'中意向':text==1?'高意向':''},
            { title: '线索渠道', dataIndex: 'leadChannelName', width: 100 },
            { title: '意向车型', dataIndex: 'intentionCarType', width: 100 },
            { title: '意向门店', dataIndex: 'dealerName', width: 200 },
            { title: '是否有效', dataIndex: 'isEffective', width: 100,render:text=>text && text==2?'未确定':text==1?'有效':text==0?'无效':'' },
            { title: '跟进门店', dataIndex: 'dealerNames', width: 200 },
            { title: '跟进销售', dataIndex: 'saleConsultantNames', width: 100 },
            { title: '关联订单号', dataIndex: 'orderNos', width: 140 },
            { title: '唯一线索生成时间', dataIndex: 'createTime', width: 180 },
            { title: '操作', width: 120, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation1(text, record) }
        ]:tabsKey === '1' ?[
            { title: '唯一线索ID', dataIndex: 'onlyLeadId', width: 140},
            { title: '姓名', dataIndex: 'customerName', width: 100 },
            { title: '手机号', dataIndex: 'mobilePhone', width: 100 },
            { title: '线索状态', dataIndex: 'leadStatus', width: 140 ,render:text=>text && text==1?'待分配门店':
            text==2?'待分配销售':text==3?'待销售跟进':text==4?'销售跟进中':text==5?'销售战败':text==6?'销售战赢待确认':text==7?'销售战赢':''},
            { title: '线索等级', dataIndex: 'intentionLevel', width: 100 ,render:text=>text && text==3?'低意向':text==2?'中意向':text==1?'高意向':''},
            { title: '线索渠道', dataIndex: 'channelName', width: 100 },
            { title: '意向车型', dataIndex: 'intentionCarType', width: 100 },
            { title: '意向门店', dataIndex: 'intentionDealer', width: 200 },
            { title: '是否有效', dataIndex: 'isDelete', width: 100 ,render:text=>text && text==2?'未确定':text==1?'有效':text==0?'无效':''},
            { title: '是否邀约', dataIndex: 'isInvitation', width: 100 ,render:text=>text && text==1?'是':text==0?'否':''},
            { title: '是否试驾', dataIndex: 'isTestDriver', width: 100 ,render:text=>text && text==1?'是':text==0?'否':''},
            { title: '是否大定', dataIndex: 'isSettled', width: 100 ,render:text=>text && text==1?'是':text==0?'否':''},
            { title: '跟进门店', dataIndex: 'followDealerName', width: 180 },
            { title: '跟进销售', dataIndex: 'saleConsultantName', width: 100 },
            { title: '关联订单号', dataIndex: 'orderNo', width: 140 },
            { title: '唯一线索生成时间', dataIndex: 'createTime', width: 200 },
            { title: '操作', width: 120, fixed: 'right', dataIndex: 'Operation', render: (text, record) => renderOperation1(text, record) }
        ]: newColumns2,
        pagination: {
            pageSize: pageSize,
            onChange: PageChange,
            current: current,
            total: total,
            showTotal: () => `共${total}条，${pageSize}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange,
        },
        rowSelection: 
        {
            selectedRowKeys, selectedRows,
            onChange: (selectedRowKeys, selectedRows) => { 
                changeSelectedRowKeys(selectedRowKeys)
                changeSelectedRows(selectedRows)
            },
            getCheckboxProps: record => ({disabled: tabsKey==='0'||(tabsKey === '1'&&record.leadStatus === 2)|| 
            (tabsKey==='2'&&record.planStatus !== 7&& record.planStatus !==5) ? false :true })
        },
    };

    
        const searchList0 =  [
            {label:'唯一线索ID',name:'onlyLeadId',type:'Input',placeholder:'请输入',colSpan:6},
            {label:'姓名',name:'customerName',type:'Input',placeholder:'请输入',colSpan:6},
            {label:'手机号',name:'mobilePhone',type:'Input',placeholder:'请输入',colSpan:6},
            {label:'线索等级',name:'intentionLevels',type:'Select',mode:'multiple',initialValue:[],placeholder:'请选择',colSpan:6,data:
            [ 
                {name:'高意向',value:'1'},
                {name:'中意向',value:'2'},
                {name:'低意向',value:'3'},
            ]},
            {label:'线索渠道',name:'leadChannels',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:clueOriginArr},
            {label:'是否有效',name:'isEffective',type:'Select',placeholder:'请选择',colSpan:6,data:[
                {name:'有效',value:'1'},
                {name:'无效',value:'0'},
                {name:'未确定',value:'2'},
            ]},
            {label:'是否有跟进门店',name:'isfollowDealer',type:'Select',placeholder:'请选择',colSpan:6,data:[
                {name:'是',value:'1'},
                {name:'否',value:'0'},
            ]},
            {label:'意向车型',name:'intentionCarType',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:scrm_clue_carType || []},
            {label:'意向门店',name:'dealerCode',type:'SearchSelect',placeholder:'输入门店简称后选择',showSearch:true,onSearch:dealersSearch,colSpan:6,data:DealerOptions},
            {label:'唯一线索生成时间',name:'createTime',type:'RangePicker',placeholder:['开始时间', '截止时间'],colSpan:6},
        ]
    const searchList1 =  [
        {label:'唯一线索ID',name:'onlyLeadId',type:'Input',placeholder:'请输入',colSpan:6},
        {label:'姓名',name:'customerName',type:'Input',placeholder:'请输入',colSpan:6},
        {label:'手机号',name:'mobilePhone',type:'Input',placeholder:'请输入',colSpan:6},
        {label:'线索状态',name:'planStatus',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:scrm_lead_onlyLeadStatus['scrm_lead_onlyLeadStatus'] || []},
        {label:'线索等级',name:'intentionLevel',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data: [ 
            {name:'高意向',value:'1'},
            {name:'中意向',value:'2'},
            {name:'低意向',value:'3'},
        ]},
        {label:'线索渠道',name:'channel',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:clueOriginArr},
        {label:'是否有效',name:'isDelete',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'有效',value:'1'},
            {name:'无效',value:'0'},
            {name:'未确定',value:'2'},
        ]},
        {label:'是否邀约',name:'isInvitation',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'是',value:'1'},
            {name:'否',value:'0'},
        ]},
        {label:'是否试驾',name:'isTestDriver',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'是',value:'1'},
            {name:'否',value:'0'},
        ]},
        {label:'是否大定',name:'isSettled',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'是',value:'1'},
            {name:'否',value:'0'},
        ]},
        {label:'意向车型',name:'intentionCarType',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:scrm_clue_carType || []},
        {label:'意向门店',name:'intentionDealer',type:'SearchSelect',placeholder:'输入门店简称后选择',showSearch:true,onSearch:dealersSearch,colSpan:6,data:DealerOptions},
        {label:'唯一线索生成时间',name:'createTime',type:'RangePicker',placeholder:['开始时间', '截止时间'],colSpan:6},
    ]
    const searchList2 =  [
        {label:'唯一线索ID',name:'onlyLeadId',type:'Input',placeholder:'请输入',colSpan:6},
        {label:'姓名',name:'customerName',type:'Input',placeholder:'请输入',colSpan:6},
        {label:'手机号',name:'mobilePhone',type:'Input',placeholder:'请输入',colSpan:6},
        {label:'跟进状态',name:'planStatus',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:scrm_lead_planStatus['scrm_lead_planStatus']},
        {label:'试驾状态',name:'testDriveState',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:[
            {name:'待试驾',value:2},
            {name:'已试驾',value:3},
            {name:'邀约取消',value:4},
            {name:'邀约过期',value:5},
        ]},
        {label:'线索等级',name:'intentionLevel',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:
        [ 
            {name:'高意向',value:'1'},
            {name:'中意向',value:'2'},
            {name:'低意向',value:'3'},
        ]},
        {label:'线索渠道',name:'channel',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:clueOriginArr},
        {label:'意向车型',name:'intentionCarType',type:'Select',placeholder:'请选择',mode:'multiple',initialValue:[],colSpan:6,data:scrm_clue_carType || []},
        {label:'是否有效',name:'leadUseful',type:'Select',placeholder:'请选择',colSpan:6,data:[
            {name:'有效',value:'1'},
            {name:'无效',value:'0'},
            
        ]},
        {label:'关联订单号',name:'orderNo',type:'Input',placeholder:'请输入',colSpan:6},
        {label:'跟进计划创建时间',name:'createTime',type:'RangePicker',placeholder:['开始时间', '截止时间'],colSpan:6},
        {label:'下次跟进时间',name:'nextFollowTime',type:'RangePicker',placeholder:['开始时间', '截止时间'],colSpan:6},
        {label:'跟进销售',name:'saleConsultantUserId',type:'SearchSelect',placeholder:'输入关键字后选择',showSearch:true,onSearch:SalesmanSearch,colSpan:6,data:SalesmanOptions},
        {label:'所属门店',name:'dealerCode',type:'SearchSelect',placeholder:'输入门店简称后选择',showSearch:true,onSearch:dealersSearch,colSpan:6,data:DealerOptions},
    ]
    let FPDisabled = false
    if(!selectedRows.length){
        FPDisabled = true
    }
    let ZYDisabled = false
    if(!selectedRows.length){
        ZYDisabled = true
    }

    return(
            <div className='ClueList'>
                <Tabs onChange={tabsCallback} type="card" activeKey={tabsKey} defaultActiveKey={tabsKey} >
                    {
                        roleJudgment(userInfo,'PERM_ONLYLEAD_LIST') ? 
                        <TabPane tab="唯一线索(厂端)" key="0">
                        <PublicTableQuery onSearch={onSearch} searchList={searchList0} defaultQuery={defaultQuery}ref={childRef0}/>
                        <div className='tableData'>
                            <Row className='tableTitle'>
                                <Col className='text'>唯一线索列表</Col>
                                {
                                   <Col className='bts'>
                                        {
                                            roleJudgment(userInfo,'PERM_CLUELIST_FACTORY_DISTRIBUTION') ?
                                            <Button type='primary' disabled={FPDisabled} onClick={()=>{
                                                setPersionType(1)
                                                setDistributionVisible(true)
                                            }}>分配</Button>:null
                                        }
                                    </Col>
                                }
                            </Row>
                            <Table {...InforData} />
                        </div>
                        </TabPane>:null
                    }
                    {
                        roleJudgment(userInfo,'PERM_ONLYLEADDEALER_LIST') ? 
                        <TabPane tab="唯一线索（门店）" key="1">
                        <PublicTableQuery onSearch={onSearch} searchList={searchList1} defaultQuery={defaultQuery} ref={childRef1}/>
                        <div className='tableData'>
                            <Row className='tableTitle'>
                                <Col className='text'>唯一线索列表</Col>
                                {
                                <Col className='bts'>
                                        {
                                            roleJudgment(userInfo,'PERM_CLUELIST_STORE_DISTRIBUTION') ?
                                            <Button type='primary' disabled={FPDisabled} onClick={()=>{
                                                setPersionType(2)
                                                setDistributionVisible(true)
                                            }}>分配</Button>:null
                                        }
                                    </Col>
                                }
                            </Row>
                            <Table {...InforData} />
                        </div>
                        </TabPane>:null
                    }
                    {
                        roleJudgment(userInfo,'PERM_LEADPLAN_LIST') ? 
                        <TabPane tab="线索跟进计划" key="2">
                        <PublicTableQuery onSearch={onSearch} searchList={searchList2} defaultQuery={defaultQuery}ref={childRef2}/>
                        <div className='tableData'>
                        <Row className='tableTitle'>
                            <Col className='text'>跟进计划列表</Col>
                            <Col className='bts'>
                                    {
                                        roleJudgment(userInfo,'PERM_CLUELIST_TRANSFER') ?
                                        <Button type='primary' disabled={ZYDisabled} onClick={()=>{
                                            setPersionType(2)
                                            setPersonnelModalVisible(true)
                                        }}>转移</Button>:null
                                    }
                                    <ExtendedColumn setColums={setColums2} columns={columns2} />
                                </Col>
                            
                        </Row>
                        <Table {...InforData} />
                        </div>
                        </TabPane>:null
                    }
                    
                </Tabs>
                {
                    ClueEditVisible &&
                    <ClueEdit visible={ClueEditVisible} getData={getData} recordData={recordData} title='编辑' onCancel={()=>setClueEditVisible(false)}/>
                }
                {
                    PersonnelModalVisible && 
                    <PersonnelModal PersionType={PersionType} getData={getData} visible={PersonnelModalVisible} recordData={recordData} tableSelectedRows={selectedRows} onCancel={()=>setPersonnelModalVisible(false)}  />
                }
                {/* {
                    ExportExcelVisible &&
                    <ExportExcel visible={ExportExcelVisible} defaultQuery={defaultQuery} onCancel={()=>setExportExcelVisible(false)} />
                } */}
                {
                    ClueVictoryVisible &&
                    <ClueVictory visible={ClueVictoryVisible} getData={getData} recordData={recordData} title='战赢审核' onCancel={()=>setClueVictoryVisible(false)}/>
                }
                {
                    DistributionVisible &&
                    <Distribution PersionType={PersionType} visible={DistributionVisible} getData={getData} recordData={recordData} title='分配给' tableSelectedRows={selectedRows} onCancel={()=>setDistributionVisible(false)}/>
                }
            </div>
    )
}
export default ClueList
