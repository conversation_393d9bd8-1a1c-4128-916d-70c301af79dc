### 升级新增、编辑
接口返回数据格式
```js
relationDetails:[
  {
    detailDisplay:{},// 客户端展示逻辑
    originDetail:{},// 升级明细原明细
    upgradeDetail:{},// 升级明细
    upgradeDetailForEdit:{},// 升级明细用来编辑的数据（数据较全）
  }
]
```
传给接口数据格式
```js
relationDetails:[
  {
    detailDisplay:{},// 客户端展示逻辑
    originDetail:{},// 升级明细原明细
    upgradeDetail:{},// 升级明细
  }
]
```

### 升级明细编辑

### 表单校验时机
1. 升级明细：升级、编辑弹窗中确认按钮
2. 客户端展示逻辑：点击全局页面保存草稿和发布按钮

#### 客户端展示逻辑校验
- 实现方式：antd table中展开属性`expandable`
- 需要注意的点：relationDetails的值取自tableList变量，且展开逻辑中没有保存按钮，故没有明确的保存时机，频繁地修改tableList对象会导致页面死循环，所以这里不主张直接保存在整体对象中，而是在保存或者发布页面时统一做校验表单和赋值操作（`check`方法），
  - 展开dom就算收起也会保留在页面dom元素中，除非`删除` `取消升级`操作，会手动从dom元素中剔除
  - `check` 获取展开dom中的form表单，`validateFields`进行表单校验， `getFieldsValue`进行取值保存操作
  
