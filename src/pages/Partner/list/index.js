import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Timeline, message, Button, Tabs, Row, Col, Tag, Modal } from "antd";
import moment from "moment";
import { UniversalOpenWindow } from "@/components/Public/PublicOpenWindow";
import PublicTableQuery from "@/components/Public/PublicTableQuery";
import PublicTable from "@/components/Public/PublicTable";
import { post, get } from "@/utils/request";
import allUrl from "@/utils/url";
import { roleJudgment } from "@/utils/authority";
import { setStorage, getStorage } from "../../../utils/Storage";
import { getDict } from "../../../actions/async";
import _ from "lodash";
import "./index.less";
import PartnerForm from './detail'

const PartnerList = (props) => {
  //创建ref节点
  const childRef1 = useRef(null);
  const tableRef1 = useRef(null);
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.common);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [current, setCurrent] = useState(1);
  const [tableHeight, setTableHeight] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [addVisible, setAddVisible] = useState(false);
  const [defaultQuery, setDefaultQuery] = useState({
    partnerCode: "",
    partnerName: "",
    cooperateStatus: "",
  });
  const [partnerStatus, setPartnerStatus] = useState([]);
  const [partnerType, setPartnerType] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [selectedPartner, setSelectedPartner] = useState({});
  const [isViewMode, setIsViewMode] = useState(false);
  const [showPhone, setShowPhone] = useState('')
  const [phoneOpen, setPhoneOpen] = useState(false)
  const onSearch = (values) => {
    console.log("values", values);
    setDefaultQuery(values);
  };
  const openForm = (partner) => {
    if(partner) {
        post(allUrl.Partner.showPlaintextInfo, { id: partner.id }).then((res) => {
          if (res.success) {
            console.log("详情", res.resp[0]);
            setSelectedPartner({
              ...res.resp[0],
              ownerPhone: res.resp[0].ownerPhone
            });
              setIsViewMode(false);
              setShowForm(true);
          } else {
            message.error(res.msg);
          }
        });
      } else {
        setSelectedPartner({});
        setIsViewMode(false);
        setShowForm(true);
      }
  };

  const openViewForm = (partner) => {
    setSelectedPartner(partner);
    setIsViewMode(true);
    setShowForm(true);
  };

  const closeForm = () => {
    setSelectedPartner({});
    setIsViewMode(false);
    setShowForm(false);
    setLoading(false)
  };
  const savePartner = (partner) => {
    console.log('partner', partner)
    setLoading(true)
    post(allUrl.Partner.setPartner, { ...partner }).then((res) => {
        if(res.success) {
            message.success('操作成功')
            closeForm();
            tableRef1.current.getTableData()
        } 
    })
    setLoading(false)
  };


  const lookPhoneMumber = (record) => {
    post(allUrl.Partner.showPlaintextInfo, { id: record.id }).then((res) => {
        if (res.success) {
          console.log("详情", res.resp[0], tableRef1.current);
        //   let data = [...tableRef1.current.dataSource]
        //   data.map((i) => {
        //     if(i.id == record.id) {
        //         i.key = record.key+new Date().getTime() // key值不更新，页面不更新！
        //         i.ownerPhone = res.resp[0].ownerPhone
        //     }
            
        //   })
        //   tableRef1.current.updateDataSource(data)
            setShowPhone(res.resp[0].ownerPhone)
            setPhoneOpen(true)
        } else {
          message.error(res.msg);
        }
      });
  } 
  useEffect(() => {
    // showPhone && 
  }, [showPhone]);

  const renderOperation = (text, record) => {
    return (
      <div style={{ color: "#1890ff" }}>
        {roleJudgment(userInfo, "EDIT_PARTNER")
          ? [
              <span
                key={1}
                style={{ cursor: "pointer", marginRight: 10 }}
                onClick={() => openForm(record)}
              >
                编辑
              </span>,
            ]
          : null}
        {
          roleJudgment(userInfo, "PARTNER_DETAIL")
            ? [
                <span key={2} style={{ cursor: "pointer", marginRight: 10 }} onClick={() => openViewForm(record)}>
                  详情
                </span>,
              ]
            : null
        }
        {
          roleJudgment(userInfo, "PARTNER_PHONE")
            ? [
                <span key={3} style={{ cursor: "pointer" }} onClick={() =>lookPhoneMumber(record)}>
                  查看手机号
                </span>,
              ]
            : null
        }
      </div>
    );
  };
  const initPage = () => {
    let winH = document.documentElement.clientHeight || document.body.clientHeight;
    let FormQueryH = document.getElementsByClassName("PublicList_FormQuery")[0].clientHeight;
    let h = winH - FormQueryH - 47 - 54 - 305;
    setTableHeight(h);
  };
  useEffect(() => {
    initPage();
  }, []);

 
  // 获取字典数据
  const getDictData = () => {
    get(allUrl.common.entryLists, { codes: "scrm_partner_status,scrm_partner_type" }).then((res) => {
      if (res.success) {
        let Dt = res.resp[0];
        Dt["scrm_partner_status"].forEach((item) => {
          item.name = item.entryMeaning;
          item.value = Number(item.entryValue);
        });
        setPartnerStatus(Dt["scrm_partner_status"]);
        Dt["scrm_partner_type"].forEach((item) => {
          item.name = item.entryMeaning;
          item.label = item.entryMeaning;
          item.value = item.entryValue;
        });
        setPartnerType(Dt["scrm_partner_type"]);
      }
    });
  };
  /**
   * 根据字典和id返回状态名称
   * @param {Array} dict
   * @param {string} id
   * @returns
   */
  const renderStatusName = (dict, id) => {
    let str = "";
    dict.map((item) => {
      if (item.value == id) {
        str = item.name;
      }
    });
    return str;
  };
  useEffect(() => {
    getDictData();
  }, []);
  const columns1 = [
    { title: "伙伴编号", dataIndex: "partnerCode", width: 120 },
    { title: "伙伴名称", dataIndex: "partnerName", width: 100 },
    { title: "负责人", dataIndex: "owner", width: 100 },
    { title: "负责人联系方式", dataIndex: "ownerPhone", width: 140 },
    {
      title: "伙伴类型",
      dataIndex: "type",
      width: 100,
      render: (text) => renderStatusName(partnerType, text),
    },
    {
      title: "合作状态",
      dataIndex: "cooperateStatus",
      width: 100,
      render: (text) => renderStatusName(partnerStatus, text),
    },
    // {
    //   title: "是否删除",
    //   dataIndex: "isDeleted",
    //   width: 100,
    //   render: (text) => (text == 0 ? "正常" : "删除"),
    // },
    // { title: "创建人", dataIndex: "createBy", width: 150 },
    { title: "创建时间", dataIndex: "createTime", width: 200 },
    // { title: "更新人", dataIndex: "updateBy", width: 100 },
    { title: "更新时间", dataIndex: "updateTime", width: 120 },
    {
      title: "操作",
      width: 100,
      fixed: "right",
      width: 140,
      dataIndex: "Operation",
      render: (text, record) => renderOperation(text, record),
    },
  ];

  let searchList1 = [
    {
      label: "伙伴编号",
      name: "partnerCode",
      type: "Input",
      placeholder: "请输入关键字",
      colSpan: 6,
      labelCol: {span: 8},
      wrapperCol: {span: 12},
    },
    { label: "伙伴企业名称", name: "partnerName", type: "Input", colSpan: 6, labelCol: {span: 8},
    wrapperCol: {span: 14}, },
    {
      label: "伙伴类型",
      name: "type",
      type: "Select",
      placeholder: "请选择",
      colSpan: 6,
      data: partnerType,
    },
    {
      label: "合作状态",
      name: "cooperateStatus",
      type: "Select",
      placeholder: "请选择",
      colSpan: 6,
      data: partnerStatus,
    },
  ];

  return (
    <div className="PartnerList">
      <PublicTableQuery
        onSearch={onSearch}
        searchList={searchList1}
        isCatch={false}
        isFormDown={false}
        ref={childRef1}
      />
      <div className="tableData">
        <Row className="tableTitle">
          <Col className="text">合作伙伴列表</Col>
          <Col style={{ textAlign: "right" }}>
            {roleJudgment(userInfo, "ADD_PARTNER") ? (
              <Button type="primary" onClick={() => openForm()}>
                添加合作伙伴
              </Button>
            ) : null}
          </Col>
        </Row>
        <PublicTable
          url={allUrl.Partner.partnerList}
          scroll={{ x: "max-content", y: tableHeight }}
          isCatch={false}
          columns={columns1}
          type={2}
          ref={tableRef1}
          rowSelection={false}
          setCurrent={setCurrent}
          setPageSize={setPageSize}
          defaultQuery={{ ...defaultQuery }}
        />
      </div>
      {showForm && (
        <PartnerForm
          visible={showForm}
          onCancel={closeForm}
          loading={loading}
          partnerStatus={partnerStatus}
          partnerTypeOptions={partnerType}
          onSave={savePartner}
          renderStatusName={renderStatusName}
          initialValues={selectedPartner}
          isViewMode={isViewMode}
        />
      )}
      {phoneOpen && (<Modal
        centered
        open={phoneOpen}
        onOk={() => setPhoneOpen(false)}
        onCancel={() => setPhoneOpen(false)}
        width={400}
      >
        <p>{showPhone}</p>
      </Modal>
      )}
    </div>
  );
};
export default PartnerList;
