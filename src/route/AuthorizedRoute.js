import React from 'react';
import { Route, Redirect } from 'react-router-dom';
import Cookies from 'js-cookie';
import { CacheRoute, CacheSwitch } from 'react-router-cache-route';
class AuthorizedRoute extends React.Component {
  render() {
    const { component: Component, item } = this.props;
    const isLogged = Cookies.get('scrm_token') ? true : false;
    return isLogged ? (
      item.catch ? (
        <CacheRoute {...this.props} componet={Component} />
      ) : (
        <Route {...this.props} componet={Component} />
      )
    ) : (
      <Redirect to="/" />
    );
  }
}

export default AuthorizedRoute;
