import React, { useState,useEffect,useRef } from 'react'
import allUrl from '@/utils/url';
import BraftEditor from '@/components/Public/BraftEditor'
import baseURL from '@/baseURL'
import UploadFile from '@/components/Public/UploadFile'
import DragableUpload from '@/components/Public/DragableUpload'
import { Form, Button, Input, Modal, Spin, Row, Col, Radio, message, Tooltip } from 'antd'
import { CloseOutlined,PlusOutlined ,DeleteOutlined} from '@ant-design/icons';
import MessageApplication from './MessageApplication'
import SendingRange from './SendingRange'
import AddLink from './AddLink'
import PublicTooltip from '@/components/Public/PublicTooltip'
import { post,get } from '../../../../utils/request';

import _ from 'lodash'
import './SendMessage.less'

const { TextArea } = Input
const SendMessage = (props) => {
    const {Type,ID,setID,setType,getSaveSuccess} = props
    const interValRef = useRef()
    const braftEditorRef = useRef(null) // BrafrEditor element
    const [form] = Form.useForm()
    const [sendApplicationVisible, setSendApplicationVisible] = useState(false)
    const [sendingRangeVisible, setSendingRangeVisible] = useState(false)
    const [fmPic, setFMPic] = useState([])
    const [fmPicLoading, setFMPicLoading] = useState(false)
    const [fileList, setFileList] = useState([])
    const [braftEditorHtmlCon, setBraftEditorHtmlCon] = useState('')
    const [defaultHtmlCon, setDefaultHtmlCon] = useState('')
    const [braftEditorHtmlText, setBraftEditorHtmlText] = useState('')
    const [agentObj,setAgentObj] = useState({})
    const [formData,setFormData] = useState({})
    const [targetList,setTargetList] = useState([])
    const [defaultTargetList,setDefaultTargetList] = useState([])
    const [removeFileList,setRemoveFileList] = useState([])
    const [targetAllPositions, setTargetAllPositions] = useState([])
    const [loading,setLoading] = useState(false)
    const [addLinkVisible,setAddLinkVisible] = useState(false)
    const [addLinkText,setAddLinkText] = useState('')
    // const onChange = (event) => {
    //     console.log(event,999)
    //     // 不论选择是或否，不做处理。
    //     // if (event.target.value === 1) { // 添加首行提示
    //     //     setDefaultHtmlCon( '注：该通知需要您提交回执，请点击蓝字发送回执' + defaultHtmlCon )
    //     // } else { // 删除首行提示
    //     //     setDefaultHtmlCon( '' )
    //     // }
    // }
    const Save = (type) =>{
        form.validateFields().then(values=>{
            console.log(values)
            if(JSON.stringify(agentObj) === '{}'){
                if(type!==3) message.error('请选择发送应用！')
                    return
            }
            if(!targetList.length){
                if(type!==3) message.error('请选择发送范围！')
                return
            }
            if(!values.title){
                if(type!==3) message.error('请输入标题！')
                return
            }
            if(!braftEditorHtmlText){
                if(type!==3) message.error('请输入正文内容！')
                return  
            }
            if(!fmPic.length){
                if(type!==3) message.error('请添加封面图！')
                return
            }


            let removeList_Temp = []
            removeFileList.forEach(item=>{
                removeList_Temp.push(item.id)
            })

            let delTargetList_Temp = []
            let targetListID = [],defaultTargetListID = [];
            targetList.forEach(item=>{
                if(item.id){
                    targetListID.push(item.id)
                }
            })
            defaultTargetList.forEach(item=>{
                if(item.id){
                    defaultTargetListID.push(item.id)
                }
            })
            defaultTargetListID.forEach(item=>{
                if(targetListID.indexOf(item)<=-1){
                    delTargetList_Temp.push(item)
                }
            })
            
            

            let data = {
                id:Type === 'Add' ?null :ID,
                agentId:agentObj.agentId,
                title:values.title || '',
                thumbMediaId:fmPic[fmPic.length-1].mediaId,
                thumbMediaUrl:fmPic[fmPic.length-1].url,
                author:values.author || '',
                content:braftEditorHtmlCon,
                digest:values.digest ? values.digest :braftEditorHtmlText.substr(0,45),
                isReceipt: values.isReceipt,
                safe:values.safe,
                contentSourceUrl:addLinkText || '',
                attachList:fileList,
                targetList,
                delArrachList:removeList_Temp,
                delTargetList:delTargetList_Temp
            }
            console.log(data)
            setLoading(true)
            if(type === 2){
                Modal.confirm({
                    title:'提示',
                    content:'是否确认发送消息！',
                    onOk:()=>{
                        post(allUrl.MessageManage.saveMessage,{...data}).then(res=>{
                            if(res.success){
                                let Dt = res.resp[0]
                                setType('EDIT')
                                setID(Dt.id)
                                if(type === 3){
                                    message.success('已自动保存为草稿！')
                                }else{
                                    if(type === 1){
                                        message.success('草稿保存成功！')
                                    }
                                }
                                if(type === 2){
                                    post(allUrl.MessageManage.sendMessage + '/' + Dt.id).then(res=>{
                                        if(res.success){
                                            message.success(res.msg)
                                            getSaveSuccess()
                                        }else{
                                            // message.error(res.msg)
                                        }
                                        setLoading(false)
                                    })
                                }else{
                                    setLoading(false)
                                }
                            }else{
                                // message.error(res.msg)
                            }
                        })
                    },
                    onCancel:()=>{
                        setLoading(false)
                        return false
                    }
                })
            }else{
                post(allUrl.MessageManage.saveMessage,{...data}).then(res=>{
                    if(res.success){
                        let Dt = res.resp[0]
                        setType('EDIT')
                        setID(Dt.id)
                        if(type === 3){
                            message.success('已自动保存为草稿！')
                        }else{
                            if(type === 1){
                                message.success('草稿保存成功！')
                            }
                        }
                    }else{
                        // message.error(res.msg)
                    }
                    setLoading(false)
                })
            }
            
        })
    }

    const getBraftEditorContent = ({htmlCon,textCon}) =>{
        setBraftEditorHtmlCon(htmlCon)
        setBraftEditorHtmlText(textCon)
    }
    const imgDel = () => {
        setFMPic([])
    }
    const MessageApplicationCB = (data) => {
        setAgentObj({
            agentIdName:data.entryMeaning,
            agentId:data.entryValue,
            url:data.extendField1
        })
    }
    const SendingRangeCB = (data, qwAgentMessageId) => {
        console.log('data', data, qwAgentMessageId)
        let users = []
      
        data.forEach((i) => {
                if(Type === 'EDIT') {
                    users.push({
                         type: 3, // type3为用户
                         targetId: i.userId,
                         targetName: i.userName,
                         orgName: i.orgName,
                         qwId: i.qywxUserId,
                         positionName: i.positionName,
                         qwAgentMessageId
                    })
                } else {
                    users.push({
                        type: 3,// type3为用户
                        targetId: i.userId,
                        targetName: i.userName,
                        orgName: i.orgName,
                        positionName: i.positionName,
                        qwId: i.qywxUserId
                   })
                }
        })
        filterPositionInfo(data)
        setTargetList(users)
    }
    // 获取岗位人数
    const filterPositionInfo = (data) => {
        let postions = [] // 岗位列表
        let postionNames = []
        data.forEach((i) => {
            // 添加展示用的岗位消息
            if(postions.length == 0 || postionNames.indexOf(i.positionName) == -1) {
                postions.push({
                    name: i.positionName,
                    num: 1
                })
                postionNames.push(i.positionName)
            } else { 
                postions.map((j) => {
                if(j.name == i.positionName) {
                    j.num++
                }})
            } 
        })
        setTargetAllPositions(postions)
    }

    const FJPicUploadChange = (info) => {
        const {fileList} = info
        console.log('file', info.file)
        if(info.file.status) {
            setFileList(fileList);
          }
        if (info.file.status === 'done') {
            if (info.file.response && info.file.response.success) {
                message.success(info.file.response.msg)
                fileList.forEach(item=>{
                    if(item.response){
                        let Dt = item.response.resp[0]
                        item.fileName = Dt.originalName
                        item.mediaId = Dt.mediaId
                        item.originalName = Dt.originalName
                        item.fileUrl = Dt.url
                    }
                })
                fileList.forEach(item=>{
                    let obj = {}
                    if(Type!=='Add'){
                        obj.id = item.id
                    }
                    obj.fileName = item.originalName
                    obj.fileUrl = item.url
                    obj.originalName = item.originalName
                    obj.fileType = item.type
                })
                setFileList(fileList)
            } else {
                message.error(info.file.response.msg)
            }
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name}上传失败！`);
        }
    }

    const UploadRemove = (file) =>{
        let temp = [...removeFileList]
        temp.push(file)
        setRemoveFileList(temp)
    }
    const FMPicUploadChange = (info) => {
        const {fileList} = info
        setFMPic([fileList[fileList.length-1]])
        if (info.file.status === 'uploading') {
            setFMPicLoading(true)
        }else if (info.file.status === 'done') {
            setFMPicLoading(false)
            if (info.file.response && info.file.response.success) {
                message.success(info.file.response.msg)
                fileList.forEach(item=>{
                    if(item.response && item.response.success){
                        let Dt = item.response.resp[0]
                        item.fileName = Dt.originalName
                        item.mediaId = Dt.mediaId
                        item.originalName = Dt.originalName
                        item.url = Dt.url
                    }
                })
                setFMPic([fileList[fileList.length-1]])
            } else {
                message.error(info.file.response.msg)
            }
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name}上传失败！`);
            setFMPicLoading(false)
            setFMPic([])
        }else{
            setFMPicLoading(false)
        }
    }
    // 处理页面是否可以操作
    const setPageAvailablity = (type) => {
        console.log(33,this)
        // 如果是编辑，页面可以操作
        if (type === 'EDIT') {
            // code:页面可以操作
            // this.state.display='block'
        }else{
            // code:禁止操作
            // this.state.display='none'
        }
    }
    const initForm = (data) =>{
        form.setFieldsValue({
            title:data.title || null,
            digest:data.digest || null,
            // contentSourceUrl:data.contentSourceUrl || null,
            author:data.author || null,
            isReceipt:String(data.isReceipt) || null,
            safe:String(data.safe )|| null,
        })
        setAddLinkText(data.contentSourceUrl || '')
    }
    const getAgentObj = (data) =>{
        get(allUrl.common.entryLists, {codes:'qw-agent'}).then(res => {
            if (res.success) {
                let Dt = res.resp[0]['qw-agent']
                let target = Dt.filter(item=>item.entryValue === data.agentId)[0]
                console.log('getAgentObj', data, target)
                setAgentObj({
                    agentIdName:target.entryMeaning,
                    agentId:target.entryValue,
                    url:target.extendField1
                })
            } else {
                // message.error(res.message)
            }
        })
    }

    const resetData = () =>{
        form.resetFields()
        setDefaultHtmlCon('')
        setBraftEditorHtmlCon('')
        setFileList([])
        setTargetList([])
        setFMPic([])
        setFormData({})
        setAgentObj({})
    }

    const targetListDel = (row) =>{
        let newTargetList = _.cloneDeep({obj:targetList}).obj
        newTargetList = newTargetList.filter(item=>item.targetId!==row.targetId)
        filterPositionInfo(newTargetList)
        setTargetList(newTargetList)

    }

    const agentObjDel = () =>{
        setAgentObj({})
    }
    const getAddLink = value=>{
        console.log(value)
        setAddLinkText(value)
    }

    useEffect(()=>{
        // 点击编辑或者查看按钮，均走此逻辑
        if (Type === 'EDIT' || Type === 'Check') {
            if(document.getElementById('viewContent')){
                document.getElementById('viewContent').innerHTML = ''
            }
            setLoading(true)
            setPageAvailablity(Type)
            get(allUrl.MessageManage.getMessageDetail + '/' + ID).then(res=>{
                if(res.success){
                    let Dt = res.resp[0]
                    Dt.qwAgentMessageId = Dt.targetList[0].qwAgentMessageId
                    initForm(Dt)
                    if(Type == 'Check'){
                        document.getElementById('viewContent').innerHTML = Dt.content
                    } else {
                        setBraftEditorHtmlCon(Dt.content);
                        setDefaultHtmlCon(Dt.content)
                    }
                    Dt.attachList.forEach((item,index)=>{
                        item.uid = String(item.id)
                        item.name = `${index+1}: ${item.originalName}`
                        item.url = item.fileUrl
                        item.status = 'done'
                    })
                    setFileList(Dt.attachList)
                    let picArr = [{
                        uid:1,
                        status:'done',
                        mediaId:Dt.thumbMediaId,
                        url:Dt.thumbMediaUrl
                    }]
                    filterPositionInfo(Dt.targetList)
                    setTargetList(Dt.targetList)
                    setDefaultTargetList(Dt.targetList)
                    setFMPic(picArr)
                    // setAgentId(Dt.agentId)
                    setFormData(Dt)
                    
                    getAgentObj(Dt)
                }else{
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        } else {
            resetData()
        }
    }, [ID])

    useEffect(()=>{
      const id=setInterval(()=>{
            Save(3)
      },180000);
      interValRef.current=id;
      return()=>{
        clearInterval( interValRef.current)
      }
    },[])

    // Hooks，处理 BraftEditor 逻辑
    useEffect(() => {
        console.log('componentDidMount')
        console.log('---Type',Type) // 判断上面 Type 的值
        const node = braftEditorRef.current // 获取 BraftEditor 节点
    }, [])
    return <div style={{ padding: '32px', background: 'white' }} className='SendMessage'>
        <Spin spinning={loading}>

            {
                Type !== 'Check'?
                <Row style={{ marginBottom: '32px',lineHeight:'34px' }}>
                    <Col><Button onClick={() => {
                        setSendApplicationVisible(true)
                    }}>选择发送应用</Button></Col>
                    <Col style={{ marginLeft: '20px' }}><Button type='primary' onClick={() => {
                        setSendingRangeVisible(true)
                    }}>选择发送范围</Button></Col>
                </Row>:null
            }
            {
                JSON.stringify(agentObj) !== '{}'?
                <Row>
                    <span style={{marginTop:'6px'}}>已选择的应用：</span>
                    <Row style={{lineHeight:'34px',marginBottom:'20px',width:'150px',position:'relative'}}>
                        <img src={agentObj.url} style={{width:'34px',height:'34px'}} /> 
                        <span style={{fontSize:'14px',color:'rgba(0,0,0,0.65)',marginLeft:'10px'}}>{agentObj.agentIdName}</span>
                        {
                            Type !== 'Check'?
                            <span style={{position:'absolute',right:0,color:'#b7b7b7',cursor:'pointer'}} onClick={()=>agentObjDel()}>
                                <CloseOutlined />
                            </span>:null
                        }
                    </Row>
                    {
                        Type !== 'Check'?
                        <span style={{display: Type!=='Check'?'block':'none'}}>
                            <a style={{marginLeft:'20px',display:'inline-block',marginTop:'6px'}} 
                                onClick={()=>{setSendApplicationVisible(true)}}>修改</a>
                        </span>:null
                    }
                </Row>:null
            }
            {
                targetList.length ?
                <Row>
                    <Col span={24} style={{display:'flex', maxHeight: 300, overflowY: 'auto', overflowX:'hidden', marginBottom: 16}}>
                        <span style={{marginTop:'6px'}}>已选择的范围：</span><Row style={{lineHeight:'34px',marginBottom:'20px',flex:1}} gutter={[16,16]}>
                            {
                                targetAllPositions.map((item,index)=>{
                                    return <Col key={index+1} span={3}>
                                            <div style={{background:'#F4FAFF',padding:'2px 22px 2px 10px',overflow:'hidden',textOverflow:'ellipsis',whiteSpace:'nowrap'}}>
                                                {item.name+ '(' + item.num + ')'}
                                            </div>
                                    </Col>
                                })
                            }
                            {
                                Type !== 'Check'?
                                <span><a style={{verticalAlign:'sub',marginLeft:'10px'}} onClick={()=>{
                                    setSendingRangeVisible(true)
                                }}>修改</a></span>:null
                            }
                        </Row>
                    </Col>
                </Row>:null
            }
            <Form form={form}>
                <Row>
                    <Col span={8}>
                        <Form.Item label='标题' name='title'>
                            <Input allowClear={ Type!=='Check' } disabled={ Type==='Check' } />
                        </Form.Item>
                    </Col>
                    
                </Row>
                {
                    Type!=='Check'?
                    <div className={braftEditorRef} ref={braftEditorRef}>
                        <BraftEditor
                            size={100}
                            url={baseURL.Host + allUrl.MessageManage.uploadMedia}
                            cb={getBraftEditorContent}
                            defaultHtmlCon={defaultHtmlCon}
                            disable = { Type!=='EDIT' }
                        />
                    </div>:<div id="viewContent" className='braft-editor-raw-content'>
                        { braftEditorHtmlCon ? braftEditorHtmlCon.replace(/<[^>]+>/ig, '') : '' }
                    </div>
                }
                {
                    Type !== 'Check'?
                    <Form.Item label='添加' wrapperCol={{ span: 6 }} className='labelCol2_Width add'>
                        <DragableUpload
                            showUploadList={true}
                            action={baseURL.Host + allUrl.MessageManage.uploadMedia}
                            size={100}
                            UploadChange={FJPicUploadChange}
                            fileList={fileList}
                            setFileList={setFileList}
                            UploadRemove={UploadRemove}
                            iconRender={()=><div><img src={require('@/assets/img/originalLink2.png')} style={{width:'18px'}} /></div>}
                        >
                            <a><img src={require('@/assets/img/file.png')} style={{ width: '18%' }} />添加附件</a>
                        </DragableUpload>
                        <div style={{padding:'4px 0'}}>
                            {/* <DisconnectOutlined /> */}
                            <img src={require('@/assets/img/originalLink3.png')} style={{width:'18px'}} />
                            <a style={{marginLeft:'4px'}} onClick={()=>setAddLinkVisible(true)}>添加原文链接</a>
                        </div>
                        {
                            addLinkText?
                            <div className='addlink'>
                                {/* <PaperClipOutlined style={{color:'rgba(0, 0, 0, 0.45)'}} target='_blank' /> */}
                                <img src={require('@/assets/img/originalLink4.png')} style={{width:'18px'}} />
                                {/* <a style={{padding:'0 8px'}} href={addLinkText} target='_black' >{addLinkText}</a> */}
                                <DeleteOutlined onClick={()=>setAddLinkText('')} />
                            </div>
                            :null
                        }
                    </Form.Item>:null
                }
                {
                    Type !== 'Check'?
                    <Form.Item style={{ marginLeft: '86px' }} wrapperCol={{ span: 10 }}>
                        <UploadFile
                            showUploadList={false}
                            action={baseURL.Host + allUrl.MessageManage.uploadCoverImg}
                            size={2}
                            extension={['jpg', 'jpeg', 'png']}
                            UploadChange={FMPicUploadChange}
                            fileList={fmPic}
                        >
                            <Button icon={<PlusOutlined />} loading={fmPicLoading}>添加封面图</Button> <span style={{ color: 'rgba(0,0,0,0.25)', marginLeft: '10px' }}>建议尺寸：1068*455</span>
                        </UploadFile>
                    </Form.Item> : <Form.Item label="附件">
                        {
                        fileList.length ? fileList.map(item => {
                            return <div style={{marginBottom: 6}}>
                                <a href={item.fileUrl} target="_blank">{item.name}</a>
                            </div>
                        }) : null
                        }
                    </Form.Item> 
                    
                }
                {
                    fmPic.length && fmPic[0].status === 'done' ?
                        <Form.Item style={{ marginLeft: '80px' }}>
                            <div style={{ position: 'relative', height: '150px', width: '350px' }}>
                                {
                                    Type === 'EDIT'?
                                    <img src={require('@/assets/img/del.png')} style={{ width: '7%', position: 'absolute', cursor: 'pointer', right: '-12px', top: '-12px' }} onClick={imgDel} />:null
                                }
                                <img src={fmPic[0].url} style={{ width: '100%', height: '100%' }} />
                            </div>
                        </Form.Item>
                        : null
                }
                <Row>
                    <Col span={8}>
                        <Form.Item label='摘要' name='digest' labelCol={{ span: 4 }} className='labelCol2_Width'>
                            <TextArea autoSize={{ minRows: 5, maxRows: 8 }} allowClear={ Type!=='Check' } disabled={ Type==='Check' }/>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <span style={{ marginLeft: '10px', color: 'rgba(0,0,0,0.25)', marginTop: '50px', display: 'block' }}>如不填会自动抓取正文前45字</span>
                    </Col>
                </Row>
                {/* <Row>
                    <Col span={8}>
                        <Form.Item label='原文链接' name='contentSourceUrl' labelCol={{ span: 4 }}>
                            <Input allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <span style={{ marginLeft: '10px', color: 'rgba(0,0,0,0.25)', marginTop: '5px', display: 'block' }}>选填</span>
                    </Col>
                </Row> */}
                <Row>
                    <Col span={8}>
                        <Form.Item label='发布者' name='author' labelCol={{ span: 4 }}>
                            <Input allowClear={ Type!=='Check' } disabled={ Type==='Check' } />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <span style={{ marginLeft: '10px', color: 'rgba(0,0,0,0.25)', marginTop: '5px', display: 'block' }}>选填</span>
                    </Col>
                </Row>
                {/* <Row>
                    <Col span={8}>
                        <Form.Item label='发布者'  labelCol={{span:3}}>
                            <Input autoSize={{ minRows: 5, maxRows: 8 }} allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <span style={{ marginLeft: '10px',color: 'rgba(0,0,0,0.25)',marginTop:'5px',display:'block' }}>选填</span>
                    </Col>
                </Row> */}
                <div className='receiptWrapper'>
                    <Form.Item label='是否需要添加回执' name='isReceipt' initialValue={'0'} className='labelCol3_Width'>
                        <Radio.Group disabled={ Type==='Check' }>
                            <Radio value={'1'}>是</Radio>
                            <Radio value={'0'}>否</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <div className='tooltipWrapper'>
                        <Tooltip title='选择“是”，接收人将可点击“发送回执”以确认已收到公告推送；选择“否”，则将发送无回执的公告。'>
                            <img className='icon-receipt-reminder' src={require('@/assets/img/add_receipt_reminder.png')} style={{ width:'18px',cursor:'pointer' }} />
                        </Tooltip>
                    </div>
                </div>
                <Form.Item label='分享设置' name='safe' initialValue={'2'} className='labelCol2_Width'>
                    <Radio.Group disabled={ Type==='Check' }>
                        <Radio value={'2'}>仅限在企业内分享</Radio>
                        <Radio value={'1'}>不能分享且内容显示水印</Radio>
                        <Radio value={'0'}>可对外分享</Radio>
                    </Radio.Group>
                </Form.Item>
                {
                    Type !== 'Check'?
                    <Row>
                        <Col span={24} style={{ textAlign: 'center' }}>
                            <Button type='primary' onClick={()=>Save(2)}>发送</Button>
                            <Button style={{ marginLeft: '20px' }} onClick={()=>Save(1)}>存草稿</Button>
                        </Col>
                    </Row>:null
                }
            </Form>
            <div className='detail_btns'>
                <Button onClick={getSaveSuccess}>取消</Button>
            </div>
            {
                sendApplicationVisible &&
                <MessageApplication agentObj={agentObj} visible={sendApplicationVisible} onCancel={() => setSendApplicationVisible(false)} cb={MessageApplicationCB} />
            }
            {
                sendingRangeVisible &&
                <SendingRange Type={Type} formData={formData} targetList={targetList} visible={sendingRangeVisible} onCancel={() => setSendingRangeVisible(false)} cb={SendingRangeCB} />
            }
            {
                addLinkVisible &&
                <AddLink visible={addLinkVisible} defaultValue={addLinkText} cb={getAddLink} onCancel={()=>setAddLinkVisible(false)} />
            }
        </Spin>
    </div>
}
export default SendMessage