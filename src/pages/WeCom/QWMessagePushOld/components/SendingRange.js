import React, { useState, useEffect } from 'react'
import { Modal, Row, Col, Tabs, Table, Tooltip, Divider, message, Tree, Checkbox, Button } from 'antd'
import PublicTableQuery from '@/components/Public/PublicTableQuery'
import { post, get } from '@/utils/request'
import allUrl from '@/utils/url'
import _ from 'lodash'

import { DeleteOutlined } from '@ant-design/icons';
import './SendingRange.less'
const { TabPane } = Tabs;
const SendingRange = (props) => {
    const { onCancel, visible,Type,cb,targetList,formData, orgNames } = props
    const [loading, setLoading] = useState(false)
    const [pageSize2, setPageSize2] = useState(10)
    const [pageSize3, setPageSize3] = useState(10)
    const [current2, setCurrent2] = useState(1)
    const [current3, setCurrent3] = useState(1)
    const [total2, setTotal2] = useState(0)
    const [total3, setTotal3] = useState(0)
    const [OrgtreeData, setOrgtreeData] = useState([])
    const [userTreeData, setUserTreeData] = useState([]) // 已选择对象列表
    const [dataSource2, setDataSource2] = useState([])
    const [dataSource3, setDataSource3] = useState([])
    const [defaultDataSource3, setDefaultDataSource3] = useState([])
    const [tabsKey, setTabsKey] = useState('1')
    const [defaultQuery, setDefaultQuery] = useState({
        pageNum: 1,
        pageSize: 10,
        positions: []
    })
    const [selectedRowKeys1, setSelectedRowKeys1] = useState([])
    const [selectedRowKeys2, setSelectedRowKeys2] = useState([])
    const [selectedRowKeys3, setSelectedRowKeys3] = useState([])
    const [selectedRows1, setSelectedRows1] = useState([])
    const [selectedRows2, setSelectedRows2] = useState([])
    const [selectedRows3, setSelectedRows3] = useState([])
    const [orgtreeselectedKeys, setOrgtreeselectedKeys] = useState([])
    const [orgtreeSelectedRows, setOrgtreeSelectedRows] = useState([])
    const [allRows, setAllRows] = useState([])
    const [isOrgAll, setIsOrgAll] = useState(false)
    const [OrgTreeDataFlatten, setOrgTreeDataFlatten] = useState([])
    const [entryList, setEntryList] = useState({})
    const [treeOrgData, setTreeOrgData] = useState([]);
    const [checkALLUsers, setCheckAllUsers] = useState([]);
    const [orgId, setOrgId] = useState(undefined);
    const [isChecked, setIsChecked]= useState(false)

    const handleOk = (props) => {
        // 讲选中的row传给父组件，以便多组织账号的组织名回传
        cb(selectedRows3,formData.qwAgentMessageId)
        onCancel()
    }
    const PageChange2 = (current, pageSize) => {
        setCurrent2(current)
        setPageSize2(pageSize)
    }
    const PageChange3 = (current, pageSize) => {
        setCurrent3(current)
        setPageSize3(pageSize)
        setDefaultQuery({
            ...defaultQuery,
            pageNum: current,
            pageSize: pageSize
        })
    }

    // 账号标签页，数据集
    const InforData3 = {
        rowKey: record => record.userId,
        bordered: true,
        dataSource: dataSource3,
        loading,
        // scroll: { x: 'max-content',y:tableHeight },
        scroll: { x: 'max-content', y: 470 },
        columns: [
            { title: '姓名', dataIndex: 'userName' },
            { title: '手机号', dataIndex: 'mobilePhone' },
            { title: '岗位', dataIndex: 'positionName' },
            { title: '所属组织', dataIndex: 'orgName' },
        ],
        size: 'middle',
        pagination: {
            pageSize: pageSize3,
            onChange: PageChange3,
            current: current3,
            total: total3,
            showTotal: () => `共${total3}条，${pageSize3}条/页`,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: PageChange3,
        },
        rowSelection: {
            // selections: [
            //     // Table.SELECTION_ALL,
            //     // Table.SELECTION_INVERT,
            //     // Table.SELECTION_NONE
            // ],
            columnWidth: 60,
            selectedRowKeys: selectedRowKeys3,
            // defaultSelectedRowKeys:selectedRowKeys3,
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys, selectedRows, info) => {
                console.log('selectedRowKeys', selectedRowKeys3, selectedRowKeys)
                console.log('selectedRows', selectedRows3, selectedRows)
                // 在重置或者切换搜索条件的时候，需要记录之前已经勾选过的数据
                let arr0 = selectedRows3.concat(selectedRows)
                arr0 = arr0.filter(i => i !== undefined)
                console.log('arr0', arr0)
                let userArr = []
                // 过滤取消的账号
                defaultDataSource3.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userId)!==-1)userArr.push(item)
                })
                dataSource3.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userId)!==-1)userArr.push(item)
                })
                arr0.forEach(item => {
                    if(selectedRowKeys.indexOf(item.userId)!==-1)userArr.push(item)
                })

                userArr = unique([...new Set(userArr)],'userId')
                console.log('userArr', userArr)
                if(userArr.length) {
                    filterTreeData(userArr)
                    setSelectedRows3(userArr)
                    setSelectedRowKeys3(selectedRowKeys)
                } else {
                    // 没有选中用户时清零
                    setUserTreeData([])
                    setSelectedRows3([])
                    setSelectedRowKeys3([])
                }

            },
            getCheckboxProps:()=>{
                return{disabled:isOrgAll?true:false}
            },
            onSelectAll:(selected, selectedRows, changeRows) => {
                // console.log('全选参数', selected, selectedRows, changeRows)
            }
        },
    };
    // 添加筛选条件后，勾选账号时会出现重复数据，需要给对象数组去重
    const unique = (arr, key) => {
        if (!arr) return arr
        if (key === undefined) return [...new Set(arr)]
        const map = {
            'string': e => e[key],
            'function': e => key(e),
        }
        const fn = map[typeof key]
        const obj = arr.reduce((o,e) => (o[fn(e)]=e, o), {})
        return Object.values(obj)
    }
    const TabsChange = (key) => {
        setDefaultQuery({})
        setTabsKey(key)
    }

    // 删除操作
    const PersonnelDel5 = (row) => {
        // 树结构删除对应内容
        let rows = [...selectedRows3]
        let keys = [...selectedRowKeys3]
        console.log('账号删除', row, rows, userTreeData)
        // 已选择对象按用户删除
        if(row.userId) {
            rows = rows.filter(item => item.userId !== row.userId)
            keys = keys.filter(item => item !== row.userId)
            for (let i = 0; i < userTreeData.length; i++) {
                console.log('i', i)

                userTreeData[i].children.map((j,index2) => {
                    if(row.userId === j.userId) {
                        console.log('j', j)
                        userTreeData[i].children.splice(index2, 1)
                    }
                    // 组织人数减量更新
                    userTreeData[i].title = `${userTreeData[i].key}(${userTreeData[i].children.length})`
                    if(userTreeData[i].children.length === 0) {
                        userTreeData.splice(i, 1)
                        i = i - 1;
                    }
                })
                console.log('j.children', userTreeData)


            }
        } else {
            // 已选择对象按组织删除
            let epetitionRows = [] // 多组织用户id
            for(let i = 0; i < userTreeData.length; i++){
                if(row.key === userTreeData[i].key) {
                    userTreeData[i].children.map((j)=> {
                        const repetitionId = rows.filter(item => item.userId === j.userId)
                        console.log('repetitionId', repetitionId)
                        if(repetitionId.length) {
                            epetitionRows = epetitionRows.concat(repetitionId)
                        }
                        rows = rows.filter(item => item.userId !== j.userId)
                        keys = keys.filter(item => item !== j.userId)
                    })
                    userTreeData.splice(i, 1)
                    i-- // 解决splice导致原数组index改变bug问题

                }

            }
            for(let j = 0; j < userTreeData.length; j++){
                epetitionRows.map((m)=> {
                    userTreeData[j].children = userTreeData[j].children.filter(n => m.userId !== n.userId)
                    console.log('epetitionRows2', m, userTreeData[j].children)
                })
                userTreeData[j].title = `${userTreeData[j].key}(${userTreeData[j].children.length})`
                // 如果组织内没有了用户，删除该组织
                if(userTreeData[j].children.length === 0) {
                    userTreeData.splice(j, 1)
                    j--
                }
            }
        }
        setSelectedRows3(rows)
        setSelectedRowKeys3(keys)
        setUserTreeData([...userTreeData])
    }

    const treeDatafilter = (arr) => {
        let newArr = _.cloneDeep({ obj: arr }).obj
        let res = []  // 用于存储递归结果（扁平数据）
        // 递归函数
        const fn = (source) => {
            source.forEach(el => {
                res.push(el)
                if(el.children && el.children.length > 0){
                    fn(el.children)
                }
            })
        }
        fn(newArr)
        return res
    }
    const onSearch = (values) => {
        const obj = {...values, pageNum: 1, pageSize: 10}
        setIsChecked(false)
        setDefaultQuery(obj)
    }
    // 组织搜索
    const searchOrg = _.debounce((value) => {
        if (value) {
        setOrgId(value);
        GetOrgData(value);
        }
    }, 500);
    // 获取组织列表
    const GetOrgData = (name = "") => {
        post(allUrl.Authority.messageSendOrgList, { name }).then((res) => {
        if (res.success) {
            const arr = res.resp.slice(0);
            setTreeOrgData(arr);
        } else {
            message.error(res.msg);
        }
        });
    };
    // 选择组织id
    const selectOrg = (value) => {
        setOrgId(value);
    };
    // 全选请求数据
    const getAllUser = () => {
        setLoading(true)
        const params = {...defaultQuery}
        delete params.pageNum
        delete params.pageSize
        post(allUrl.MessageManage.getAlluser, { ...params }).then(res => {
            if (res && res.success) {
                const list = res.resp[0]
                // 手机号脱敏
                list.forEach(function(item){
                    if(item && item.mobilePhone){
                        item.mobilePhone=item.mobilePhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                    }
                })
                setCheckAllUsers(list)
                setTotal3(list.length)
                setDataSource3(list)
                if(!defaultQuery.userName || defaultQuery.userName === ''){
                    setDefaultDataSource3(list)
                }
                let ids = list.map(item => item.userId)
                ids = unique((ids.concat(selectedRowKeys3)))
                const rows = unique((list.concat(selectedRows3)), 'userId')
                setSelectedRowKeys3(ids)
                setSelectedRows3(rows)
                console.log('key和row', ids, rows)
                filterTreeData(list, userTreeData)
            }
            setLoading(false)
        })
    }
    /**
     * 设置右侧已选择用户展示数据
     * @param {Array} userList
     * @param {Array} historyData
     * @returns
     */
    const filterTreeData = (userList, historyData) => {
        if(userList.length === 0 ) {
            setUserTreeData([])
            setSelectedRowKeys3([])
            setSelectedRows3([])
            return
        }
        console.log('userList', userList)
        let orgArrs = unique(userList.map((item) => item.orgName))
        orgArrs = orgArrs.filter(i => i !== undefined && i !== null)
        console.log('orgarr', orgArrs)
        let newOrgNameArr = []
        // 处理组织名称列表（一级菜单集合）
        orgArrs.length && orgArrs.map((item => {
            // 多组织名称的拆分加入
            const orgs = item.split(',')
            console.log(orgs)
                if(orgs.length > 1) {
                    orgs.map((j)=> {
                        newOrgNameArr.push(j)
                        })
                } else {
                    newOrgNameArr.push(item)
                }
            })
        )
        // 组织去重
        newOrgNameArr = [...new Set(newOrgNameArr)]
        const newUserTreeData = []
        // 设置组织
            newOrgNameArr.map(((item,index) => {
                newUserTreeData.push({
                title: item,
                key: item,
                children: []
            })
        }))
        // 设置组织下的用户
        userList.forEach((i, index) => {
            newUserTreeData.forEach((j,index2) => {
                // 一个用户只有一个userId，多组织用户需要在多个组织下展示。单组织判断组织名相等，多组织判断是否包含
                if(i.orgName === j.key || i.orgName.indexOf(j.key) > -1) {
                    j.children.push({
                        // 显示成组织@岗位
                        title: `${i.userName || i.targetName}`,
                        positionName: i.positionName,
                        key: i.userId ? `${index2}_${i.userId}` : `${index2}_${i.targetId}`,
                        userId: i.userId ? i.userId : i.targetId,
                        qwId:i.qywxUserId ? i.qywxUserId : i.qwId,
                    })
                    // 更新组织下的用户个数
                    j.title = `${j.key}(${j.children.length})`
                }
            })
        })
        console.log('最后的已选择对象数据：', newUserTreeData)
        setUserTreeData(newUserTreeData)
        if(historyData && historyData.length) {
            let finallyData = []
            console.log('历史已选择对象数据', historyData)
            historyData.forEach((m, index3) => {
               newUserTreeData.forEach((n, index4) => {
                   if(m.key === n.key) {
                       let children = (m.children.concat(n.children))
                       children = unique(children, 'userId')
                       console.log('children', children)
                    historyData[index3].children =children
                    historyData[index3].title =  `${m.key}(${children.length})`
                    newUserTreeData.splice(index4, 1)
                   }
               })
           })

           console.log('newUserTreeData', newUserTreeData)
           finallyData = historyData.concat(newUserTreeData)
           console.log('finallyData', finallyData.length)
           setUserTreeData(finallyData)
        }

    }
    // 点击全选按钮
    const handleSelectAll = (e) => {
        setIsChecked(e.target.checked)
        if(e.target.checked) {
            getAllUser()
        } else {
            setSelectedRowKeys3([])
            setSelectedRows3([])
            setUserTreeData([])
        }
    }
    // 切换tab和默认查询参数
    useEffect(() => {
        setLoading(true)
        if (tabsKey === '3') {
            post(allUrl.Authority.messageSendOrgList, { ...defaultQuery }).then(res => {
                if (res && res.success) {
                    let Dt = res.resp
                    setOrgtreeData(Dt)
                    setOrgTreeDataFlatten(treeDatafilter(Dt))
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
        if (tabsKey === '2') {
            get(allUrl.common.entryLists, { codes: 'scrm_position' }).then(res => {
                if (res.success) {
                    let Dt = res.resp[0]
                    for (let i in Dt) {
                        Dt[i].forEach(item => {
                            item.name = item.entryMeaning
                            item.value = item.entryValue
                            item.entryID = item.id
                            delete item.id
                        })
                    }
                    setDataSource2(Dt['scrm_position'])
                    setTotal2(Dt['scrm_position'].length)
                } else {
                    // message.error(res.message)
                }
                setLoading(false)
            })
        }
        if (tabsKey === '1') {
            console.log('tabsKeys', tabsKey,defaultQuery)
            post(allUrl.MessageManage.getUser, { ...defaultQuery }).then(res => {
                if (res && res.success) {
                    // 手机号脱敏
                    res.resp[0].list.forEach(function(item){
                        if(item && item.mobilePhone){
                            item.mobilePhone=item.mobilePhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                        }
                    })
                    setTotal3(res.resp[0].total)

                    setDataSource3(res.resp[0].list)
                    if(!defaultQuery.userName || defaultQuery.userName === ''){
                        setDefaultDataSource3(res.resp[0].list)
                    }
                } else {
                    // message.error(res.msg)
                }
                setLoading(false)
            })
        }
    }, [defaultQuery, tabsKey])
    useEffect(() => {
       if(selectedRowKeys3.length > 0 && total3 == selectedRowKeys3.length) {
            setIsChecked(true)
        }
    }, [total3]);
    // 初始化
    useEffect(() => {
        // 获取岗位接口数据
        get(allUrl.common.entryLists, { codes: 'scrm_position' }).then(res => {
            if (res.success) {
                let Dt = res.resp[0]
                for (let i in Dt) {
                    Dt[i].forEach(item => {
                        item.name = item.entryMeaning
                        item.value = item.entryValue
                    })
                }
                setEntryList(Dt || {})
            } else {
                // message.error(res.message)
            }
        })
        // 获取组织树列表
        GetOrgData();
    }, [])

    // 父组件用户数据targetList
    useEffect(()=>{
        // 设置组织
        if(targetList.length) {
            filterTreeData(targetList)
            let newRows = []
            targetList.forEach((m,index3) => {
                newRows.push({
                    positionName: m.positionName,
                    type: m.type,
                    qywxUserId: m.qwId,
                    orgName: m.orgName,
                    userId: m.targetId,
                    userName: m.targetName
                })
            })
            // 在重置或者切换搜索条件的时候，需要记录之前已经勾选过的数据
            const userIdArr = unique(targetList.map((item) => +item.targetId))
            setSelectedRows3( unique([...new Set(newRows)]) )
            setSelectedRowKeys3(userIdArr)
        } else {
            // 没有选中用户时清零
            setUserTreeData([])
            setSelectedRows3([])
            setSelectedRowKeys3([])
        }

    },[targetList])

    // 全选按钮变动
    useEffect(() => {
        if(selectedRowKeys3.length < total3) {
            setIsChecked(false)
        }
        if(total3 > 0 && selectedRowKeys3.length == total3) {
            setIsChecked(true)
        }

    }, [selectedRowKeys3]);
    // 筛选条件项
    let searchList = [
        { label: '姓名', name: 'userName', type: 'Input', placeholder: '请输入姓名',labelAlign:'right',labelCol:{span: 9},colSpan: 6 },
        { label: '组织',
        name: 'organizationId',
        type: 'TreeSelect',
        placeholder: '请选择',
        colSpan: 12,
        labelCol:{span: 3},
        wrapperCol: {span: 20},
        data: treeOrgData || [],
        onSearch:searchOrg,
        onChange:selectOrg,
        fieldNames:{
            label: 'title', value: 'key', children: 'children'
        }
        },
        { label: '门店状态', name: 'businessStatus', type: 'Select', placeholder: '请选择', labelCol:{span: 10}, colSpan: 6,
          data: [
            { name:'营业中', value:'899030000' },
            { name:'停业', value:'899030001' },
            { name:'搬迁', value:'899030002' },
            { name:'改建', value:'899030003' },
            { name:'在建', value:'102910000' },
            { name:'拟退网', value:'102910001' }
          ]
        },
        { label: '岗位', name: 'positions', type: 'Select',mode: "multiple", initialValue: [], placeholder: '请选择',labelAlign:'right', colSpan: 18, labelCol:{span: 2},wrapperCol:{span: 20}, data: entryList['scrm_position'] || [] },
    ]
    const renderNode = (item) => {
        return <div className="content_Item">

            <Tooltip title={item.positionName ? `${item.title}@${item.positionName}` : item.title}><div className="name">{item.title}{item.positionName ? <span style={{color: '#1890FF'}}>@{item.positionName}</span> : ''}</div></Tooltip>
            <div className="del">
                <img src={require('../../../../assets/img/trash.png')} onClick={() => PersonnelDel5(item)} />
            </div>
        </div>
    }
    return <Modal wrapClassName='SendingRange' title='选择发消息的范围' visible={visible} onCancel={onCancel} onOk={handleOk} width={1400} maskClosable={false}>
        <Row gutter={[12]}>
            <Col span={16} className='SendingRangeLeft'>
                {/* <Checkbox style={{position:'absolute',top:'14px',right:'0'}} checked={isOrgAll} indeterminate={isIndeterminate} onClick={(e) => OrgCleckAll(e)}>全选</Checkbox> */}
                {/* <Tabs defaultActiveKey="1" onChange={TabsChange} active={tabsKey}> */}
                    {/* <TabPane tab="账号" key="1"> */}
                        <PublicTableQuery tabsKey={tabsKey} onSearch={onSearch} isFormDown={true} searchList={searchList} defaultQuery={defaultQuery} />
                        <Checkbox className="checkALL" onChange={handleSelectAll} checked={isChecked}>全选</Checkbox>
                        <Table {...InforData3} />
                    {/* </TabPane> */}
                {/* </Tabs> */}
            </Col>
            <Col span={8} className='SendingRangeRight'>
                <div className='title'>已选择对象<span className='selectedSum'>（{ selectedRows3.length }）</span></div>
                <div className='contentBox'>
                    {/* 树形结构 */}
                    {
                        <Tree
                            titleRender={renderNode}
                            // onSelect={onSelect}
                            // onCheck={onCheck}
                            treeData={userTreeData}
                        >
                        </Tree>
                    }
                </div>
            </Col>
        </Row>
    </Modal>
}
export default SendingRange
