import React from 'react'
import { Modal, message, Form, Input,Select,Row,Col} from 'antd'
import allUrl from '../../../../utils/url'
import { post, get } from '../../../../utils/request'
import { connect } from 'react-redux'
const {Option} = Select
class ClueEdit extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {

        }
    }
    formRef = React.createRef();
    handleOk = () => {
        const { onCancel ,getData,recordData} = this.props
        this.formRef.current.validateFields().then(values => {
            if (values) {
                let data = {
                    customerName: values.customerName,
                    intentionLevel: values.intentionLevel,
                    isDelete:values.isDelete,
                    onlyLeadId:recordData.onlyLeadId
                }
                post(allUrl.ClueManagement.updateLead,{...data}).then(res=>{
                    if(res.success){
                        message.success(res.msg)
                        onCancel()
                        getData()
                    }else{
                        // message.error(res.msg)
                    }
                })
                
            }
        })
    }

    ininForm = () => {
        const { recordData } = this.props
        setTimeout(() => {
            console.log(this.formRef.current)
            this.formRef.current.setFieldsValue({
                customerName: recordData.customerName,
                intentionLevel: recordData.intentionLevel,
                isDelete:recordData.isEffective,
            })
        }, 200)

    }

    render() {
        const { visible, title,onCancel } = this.props
        const { loading } = this.state
        console.log(this.state.current)
        const layout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 20 },
        };
        
            return (
                <Modal
                    visible={visible}
                    title={title}
                    onOk={this.handleOk}
                    onCancel={onCancel}
                    maskClosable={false} 
                    width={600}
                    // bodyStyle={{ padding: '50px' }}

                >
                    <Form  {...layout} name="vertical" onFinish={this.onFinish} ref={this.formRef}>
                        <Row>
                        <Col span={12}>
                            <Form.Item label="姓名" name="customerName">
                                <Input placeholder='请输入...' allowClear  />
                            </Form.Item>
                            </Col>
                            {/* <Col span={12}>
                            <Form.Item label="手机号"  name="orderStatus">
                                <Input placeholder='请输入...' allowClear  />
                            </Form.Item>
                            </Col> */}
                            <Col span={12}>
                            <Form.Item label="线索等级"  name="intentionLevel">
                                <Select placeholder='请选择...' allowClear>
                                    <Option value={1}>高意向</Option>
                                    <Option value={2}>中意向</Option>
                                    <Option value={3}>低意向</Option>
                                </Select>
                            </Form.Item>
                            </Col>
                            <Col span={12}>
                            <Form.Item label="是否有效" name="isDelete">
                                <Select placeholder='请选择...' allowClear>
                                    <Option value={2}>未确定</Option>
                                    <Option value={1}>有效</Option>
                                    <Option value={0}>无效</Option>
                                </Select>
                            </Form.Item>
                            </Col>
                            </Row>
                        </Form>
                </Modal>
            )
        }
        componentDidMount(){
            this.ininForm()
        }
    }
    export default connect()(ClueEdit)