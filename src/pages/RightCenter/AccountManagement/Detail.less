.detail-PublicList {
    // padding-left: 24px;
    background: #f0f2f5;
    padding: 24px 24px 0 24px;

    .detail-box {
        background: #fff;

        .tow-tab {
            height: 42px;
            line-height: 42px;
            display: flex;
            flex-direction: row;
            background: #f0f2f5;

            .tab-item {
                width: 180px;
                text-align: center;
                background: rgba(0, 0, 0, 0.02);
                border-radius: 2px 2px 0px 0px;
                border: 1px solid rgba(0, 0, 0, 0.15);
                font-size: 17px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.65);
                margin-right: 6px;
            }

            .active {
                background: #1890FF;
                color: #FFFFFF;
            }

        }

        .tab-box {
            border: 1px solid #1890FF;
            background-color: #fff;
            padding: 0 15px 30px 15px;
        }

        .flex {
            display: flex;
            align-items: center;
        }

        .tableTitle {
            background-color: #fff;
            padding: 24px 24px 0px 32px;
            justify-content: space-between;

            .text {
                font-size: 20px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                color: rgba(0, 0, 0, 0.85);
                line-height: 28px;
            }

            .bts {
                .ant-btn {
                    margin: 0 7px;
                }
            }
        }

        .tableData {
            padding-top: 24px;
            padding-bottom: 24px;

            .top-box {
                background-color: #d6ebff;
                display: flex;
                justify-content: space-between;
                height: 50px;
                align-items: center;

                .left-item {
                    font-size: 16px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: rgba(0, 0, 0, 0.85);

                    .icon-box {
                        width: 16px;
                        height: 16px;
                        margin-right: 8px;
                        cursor: pointer;
                        margin-top: -2px;
                    }
                }

                .right-item {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                }

                .margin-left {
                    margin-left: 20px;
                }

                .right-item {
                    margin-right: 20px;
                }
            }

            .tab-content {
                padding: 20px;
                background: #F1F8FF;

                .title {
                    color: #000000;
                    font-size: 15px;
                    font-weight: 500;
                    margin-bottom: 10px;
                }

                .content-div {
                    background-color: #fff;
                    padding: 20px 30px;
                    border-left: 1px solid #D1D6DA;
                    border-right: 1px solid #D1D6DA;
                    border-bottom: 1px solid #D1D6DA;
                }

                .content-div-no {
                    text-align: center;
                }

                .content-div-nopad {
                    padding: 0;
                }
            }

            .info-box {
                background: white;
                padding: 24px;
            }

            .detailPanel {
                padding: 24px 24px 0 24px;
                background-color: #F1F8FF;

                .ant-image {
                    display: none;
                }

                .file-name {
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #1890FF;
                    line-height: 20px;
                    cursor: pointer;

                    &:not(:last-child):after {
                        content: '';
                        display: inline-block;
                        width: 0;
                        height: 14px;
                        border-left: 1px solid #000;
                        margin-left: 10px;
                        margin-right: 10px;
                        vertical-align: middle;
                    }
                }
            }

            .marg {
                margin-top: 24px
            }

            .ant-table-wrapper {
                background-color: white;

                .ant-table {
                    padding: 20px;
                }

                .ant-table-pagination {
                    margin: 16px 24px;
                }
            }
        }
    }

    .backBtn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 24px;
    }

    .part-box {
        .part-content {
            padding: 20px;
            background-color: #F1F8FF;

            .PublicList_FormQuery {
                background-color: #F1F8FF;
                flex: 1;

                .ant-form-item {
                    margin-bottom: 14px !important;
                }
            }

            .FormQuerySubmit {
                display: flex;
                justify-content: flex-end;
                background-color: #F1F8FF;

                .operationButtons {
                    span {
                        color: #1890ff;
                        cursor: pointer;

                        .anticon {
                            margin-left: 6px;
                        }
                    }
                }
            }
        }

    }

    .bottom-btn {
        background-color: #fff;
        padding: 24px 24px 24px 32px;
        margin-left: -24px;
        margin-right: -24px;
        display: flex;
        justify-content: flex-end;
        box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
        z-index: 1000;
    }

    .btns {
        display: flex;

        button {
            margin-left: 15px;
        }
    }

    .border-btn {
        color: #1890FF;
        border-color: #3FA3FF;

        &:disabled {
            color: rgba(0, 0, 0, 0.25);
            border-color: #d9d9d9;
        }
    }
}

.detail-btn-box {
    background-color: #fff;
    padding: 0 0 14px 32px;

    .car-btn {
        cursor: pointer;
        margin-right: 32px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
    }

    .active {
        width: 72px;
        height: 46px;
        border-bottom: 3px solid #1890FF;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #1890FF;
        line-height: 22px;
        padding-top: 12px;
        padding-bottom: 12px;
    }
}

.detail-overflow {
    height: calc(100vh - 48px - 54px - 144px);
    overflow-y: auto;
}

.detail-PublicList .detail-box .tableData .ant-table-wrapper.parts-no-pad .ant-table {
    padding: 0;
}