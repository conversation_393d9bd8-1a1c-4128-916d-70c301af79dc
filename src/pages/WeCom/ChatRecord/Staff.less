.staff {
    .divider {
        display: flex;
        align-items: center;
        width: 100%;
        color: rgba(0, 0, 0, 0.2500);
        font-size: 10px;
        border-color: #e0e0e0;
        border-style: solid;
        border-width: 0;
    }
    .divider:before, .divider:after {
        content: '';
        display: block;
        flex: 1;
        box-sizing: border-box;
        height: 1px;
        transform: matrix(1, 0, 0, 0.5, 0, 0);
        border-style: inherit;
        border-width: 1px 0 0 0;
    }
    .divider:before {
        margin-right: 16px;

    }
    .divider:after {
        margin-left: 16px;
    }
    .flex {
        display: flex;
        align-items: flex-start;
    }
    .flex-align {
        align-items: center;
    }
    .flex-right {
        display: flex;
        justify-content: right;
    }
    .ellipsis{
        overflow:hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .mr10 {
        margin-right: 10px;
    }
    .space-between{
        justify-content: space-between;
    }
    .text-right {
        text-align: right;
    }
    .empty {
        font-size: 14px;
        text-align: center;
        margin-top: 158px;
        &-img {
            margin-bottom: 12px;
        }
        &-content {

        }
    }
    .ant-row {
        flex:1
    }
    .ant-tag {
        margin-bottom: 2px;
    }
    .ant-avatar.ant-avatar-icon > .anticon {
        margin-top: 14px;
    }
    .ant-tabs-top > .ant-tabs-nav::before {
        border: none
    }
    display: flex;
    padding: 24px;
    background: #f0f2f5;
    &-left {
        background-color:#fff;
        &-num {
            font-size: 14px;
            margin-bottom: 6px;
        }
        .search-wrapper {
            padding: 20px 14px;
            border-bottom: 1px solid #eee;
            background-color: #D5E7FF;
            margin-bottom: 10px;
        }
        &-list {
            padding: 0 6px 0 14px;
            height: 520px;
            overflow-y: auto;
            flex-wrap: wrap;
            overflow-x: hidden;
            font-size: 12px;
            .spin {
                width: 100%;
                text-align: center;
            }
        }
       
    }
    .staff-row_wrapper {
        padding: 0 5px;
        .staff-row {
            width: 100%;
            cursor: pointer;
            flex: 1;
            padding: 16px 0;
            
            &-avatar {
                display: block;
                width: 48px;
                height: 48px;
                margin-right: 12px;
                flex-shrink: 0;
            }
            .staff-row-info {
                // width: 100%;
                flex: 1
            }
            .floot1 {
                justify-content: space-between;
                margin-bottom: 4px;
    
                .name {
                    // width: 38%;
                    max-width: 250px;
                    font-weight: 500;
                    line-height: 24px;
                    font-size: 14px;
                    margin-right: 10px;
                }
                .max-width {
                    width: 120px;
                    max-width: 200px;
                }
                .time {
                    font-size: 12px;
                    color: #9b9fa1;
                }
                .lastest-time{
                    color: rgba(0, 0, 0, 0.2500);
                }
            }
            .owner-name {
                margin-right: 10px;
                max-width: 100px;
            }
            .owner-phone {
                margin-right: 10px;
            }
            .position {
                max-width: 100px;
            }
            .group-owner {
                max-width: 50px;
            }
            .group-tags {
                
            }
            .floot2 {
                margin-bottom: 10px;
                line-height: 22px;
                font-size: 12px;
                font-weight: 400;
                
            }
            .floot3 {
                max-width: 160px;
            }
            .tags {
                max-width: 250px;
                height: 22px;
            }
        }
        .staff-row:not(:nth-last-child(2)) {
            // border-bottom: 1px solid #E0E0E0;
        }
       
    }
    .staff-row_wrapper:hover {
        // color:#40a9ff;
        cursor: pointer;
        background-color: rgba(242, 244, 248, 1);
    }
    .staff-row_wrapper-active {
        // color:#40a9ff;
        cursor: pointer;
        background-color: rgba(242, 244, 248, 1);
    }
   
   
    &-right {
        background-color: #fff;
        display: flex;
        padding-left: 14px;
        .customer {
            // margin-right: 10px;
            // padding-right: 10px;
            // border-right:  2px solid #efefef;
            // width: 300px;
            .customer-search {
                margin: 10px 0;
            }
            .customer-list {
                width: 100%;
                height: 598px;
                overflow-y: auto;
                flex-wrap: wrap;
                overflow-x: hidden;
                font-size: 12px;
                .spin {
                    width: 100%;
                    text-align: center;
                }
                .floot1 {
                    margin-bottom: 8px;
                }
              
            }
        }
        .record {
            padding: 24px;
            background-color: #F5F9FE;
            .record-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px;
                .staff-row-avatar {
                    width: 48px;
                    height: 48px;
                }
            }
            .name {
                max-width: 250px;
                margin-bottom: 6px;
            }
            &-list {
                // width: 600px;
                padding: 10px;
                height: 600px;
                overflow-y: auto;
                .flex-end {
                    display: flex;
                    // justify-content: flex-end;
                    justify-content: right;
                }
                .flex {
                    display: flex;
                }
                .row {
                    margin-bottom: 20px;
                    
                    .avatar {
                        margin-right: 10px;
                    }
                    .detail {
                        .userInfo {
                            margin-bottom: 4px;
                            color: rgba(0, 0, 0, 0.4400);
                            font-size: 12px;
                        }
                        .content {
                            border-radius: 5px;
                            // background-color: #eee;
                            // padding: 10px;
                            &-text {
                                font-size: 12px;
                                max-width: 400px;
                                overflow: auto;
                            }
                        }
                    }
                }
            }
        }
    }
}
