import { applyMiddleware, compose, createStore } from 'redux';
import { persistReducer, persistStore } from 'redux-persist';
import storageSession from 'redux-persist/lib/storage/session';
import promiseMiddleware from 'redux-promise';
import thunk from 'redux-thunk';
import rootReducer from '../reducers';

const persistConfig = {
  key: 'root',
  storage: storageSession,
  // blacklist: [], // 不会被存入缓存中，其他会，适用于少部分数据需要实时更新
  // whitelist: ['common'] // 会存入缓存，其他不会存，适用于大多数数据并不会实时从后台拿数据
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

const composeEnhancers = (typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__) || compose;

export default () => {
  // let store = createStore(persistedReducer)
  const store = createStore(persistedReducer, composeEnhancers(applyMiddleware(thunk, promiseMiddleware)));
  const persistor = persistStore(store);
  return { store, persistor };
};
