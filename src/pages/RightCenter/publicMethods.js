import React from 'react';
import styles from './antd.module.less';
export const extendFlag = ({ text, children }) => {
  return (
    <div>
      <div className={`${styles['custom-mark']} ${styles['bg2']}`}>
        <div className={styles['custom-mark-content']}>
          <span>延保</span>
        </div>
      </div>
      <span>{text}</span>
      {children?.()}
    </div>
  );
};

export const groupFlag = ({ text, children }) => {
  return (
    <div>
      <div className={`${styles['custom-mark']} ${styles['bg3']}`}>
        <div className={styles['custom-mark-content']}>
          <span>组合</span>
        </div>
      </div>
      <span>{text}</span>
      {children?.()}
    </div>
  );
};
