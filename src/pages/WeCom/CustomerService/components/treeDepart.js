
import { Input, Tree } from 'antd';
import React, { useMemo, useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { EditOutlined, UserOutlined, CloseOutlined } from '@ant-design/icons';
import { post,get } from '../../../../utils/request';
import allUrl from '../../../../utils/url';
const { Search } = Input;

const getParentKey = (key, tree) => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  
  return parentKey;
};



const TreeDeparts = forwardRef((props, ref) => {
    let defaultData = props.treeDepartData
    let r = props.receptions || []
    const [expandedKeys, setExpandedKeys] = useState(["总部"]);
    const [searchValue, setSearchValue] = useState('');
    const [checkedKeys, setCheckedKeys] = useState([]);
    const [receptions, setReceptions] = useState(r);
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    
    useImperativeHandle(ref, () => ({
        receptions
    }))
    let dataList = [];
    
    const generateList = (data) => {
        for (let i = 0; i < data.length; i++) {
            const node = data[i];
            const { key } = node;
            dataList.push({
                recepObjectId: node.recepObjectId,
                receptionType: node.receptionType,
                recepName: node.name,
            });
            if (node.childs) { generateList(node.childs); }
        }
        
    };
    generateList(defaultData)
    const onExpand = (newExpandedKeys) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };
    const onCheck = (checkedKeysValue) => {
        setCheckedKeys(checkedKeysValue.checked)
        let rece = []
        for (const item of dataList) {
            for (const li of checkedKeysValue.checked) {
                if(item.recepName == li) {
                    rece.push({ receptionType: item.receptionType, recepObjectId: item.recepObjectId, recepName: item.recepName })
                }
            }
        }
        setReceptions(rece);
    };
    const onChange = (e) => {
        const { value } = e.target;
        const newExpandedKeys = dataList
        .map((item) => {
            if (item.recepName.indexOf(value) > -1) {
            return getParentKey(item.recepName, treeData);
            }
            return null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
        setExpandedKeys(newExpandedKeys);
        setSearchValue(value);
        setAutoExpandParent(true);
    };
    const delCheckedKey = (index) => {
        let arrs = JSON.parse(JSON.stringify(checkedKeys)) 
        arrs.splice(index, 1)
        setCheckedKeys(arrs)
    }
    useEffect(()=>{
        let keys = []
        for (const li of props.receptions) {
            keys.push(li.recepName)
        }
        setCheckedKeys(keys)
        setReceptions(props.receptions)
    },[props.receptions])
  const treeData = useMemo(() => {
    const loop = (data) => 
        data.map((item) => {
            const strTitle = item.name;
            const index = strTitle.indexOf(searchValue);
            const beforeStr = strTitle.substring(0, index);
            const afterStr = strTitle.slice(index + searchValue.length);
            const title =
            index > -1 ? (
                <span>
                {beforeStr}
                <span style={{color: '#f50'}}>{searchValue}</span>
                {afterStr}
                </span>
            ) : (
                <span>{strTitle}</span>
            );
            if (item.childs && item.childs.length) {
            return {
                title,
                key: item.name,
                children: loop(item.childs),
            };
            }
            return {
                title,
                key: item.name,
            };
        });
    return loop(defaultData);
  }, [searchValue]);

  return (
        <div className='container'>
            <div className='tree-depart'>
                <Search style={{ marginBottom: 8, }} placeholder="搜索" onChange={onChange} />
                <div className="tree-box">
                    <Tree
                        checkable
                        checkStrictly
                        onExpand={onExpand}
                        checkedKeys={checkedKeys}
                        onCheck={onCheck}
                        expandedKeys={expandedKeys}
                        autoExpandParent={autoExpandParent}
                        treeData={treeData}
                    />
                </div>
            </div>
            <div className='selected-box'>
                <div className="title">已选择的部门或成员</div>
                <div className='list'>
                    {
                        checkedKeys.map((item, index) => {
                            return <div className='item'>
                                <div className='name'><UserOutlined /><span className='name-text'>{ item }</span></div>
                                {/* <div onClick={ () => delCheckedKey(index) }>
                                    <CloseOutlined className='close-icon' />
                                </div> */}
                                
                            </div>
                        })
                    }
                    
                </div>
            </div>
        </div>
  );
});
export default TreeDeparts;