import React, { useEffect } from 'react'
import PropTypes from 'prop-types';
import { Row, Col, Input, DatePicker, Select, InputNumber } from 'antd'

const { Option } = Select
const PublicForm = (props) => {
    const { formItemLayout, formList, defaultForm, Form, form } = props
    const newformItemLayout = formItemLayout ? formItemLayout :
        {
            labelCol: {
                span: 7,
            },
            wrapperCol: {
                span: 18,
            },
        }
    useEffect(() => {
        if (defaultForm && Object.keys(defaultForm).length > 0) {
            form.setFieldsValue(defaultForm)
        }
    }, [defaultForm])
    return <Form
        {...newformItemLayout}
        layout={'vertical'}
        form={form}
        initialValues={{
            layout: 'vertical',
        }}
    >
        <Row>
            {
                formList.map((item, index) => {
                    return <Col key={index} span={item.span ? item.span : 8}>
                        <Form.Item label={item.label} name={item.name} rules={[
                            {
                                required: item.required,
                                message: `${item.type === 'Input' || item.type === 'InputNumber' ? '请输入' : '请选择'}${item.label}！`,
                            },
                        ]}>
                            {
                                item.type === 'Input' ? <Input disabled={item.disabled ? item.disabled : false} allowClear placeholder={'请输入...'} />
                                    : item.type === 'InputNumber' ? <InputNumber style={{ width: '100%' }} placeholder='请输入...' min={item.min} max={item.max} />
                                        : item.type === 'DatePicker' ? <DatePicker disabled={item.disabled ? item.disabled : false} style={{ width: '100%' }} placeholder='请选择...' />
                                            : item.type === 'Select' ?
                                                <Select disabled={item.disabled ? item.disabled : false} allowClear placeholder='请选择...'>
                                                    {
                                                        item.data && item.data.length &&
                                                        item.data.map((ele, index) => {
                                                            return <Option key={index} value={ele.value}>{ele.name}</Option>
                                                        })
                                                    }
                                                </Select>
                                                : null
                            }

                        </Form.Item>
                    </Col>
                })
            }

        </Row>
    </Form>
}
PublicForm.propTypes = {
    formList: PropTypes.array,
    formItemLayout: PropTypes.object,
};
export default PublicForm