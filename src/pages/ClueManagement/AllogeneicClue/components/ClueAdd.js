import React from 'react'
import { Modal, message, Form, Input,Select,Row,Col,DatePicker} from 'antd'
import allUrl from '../../../../utils/url'
import { post, get } from '../../../../utils/request'
import { connect } from 'react-redux'
import moment from 'moment';
const {Option} = Select
class ClueAdd extends React.PureComponent {
    constructor(props) {
        super(props)
        this.state = {
            cityList:[],

        }
    }
    formRef = React.createRef();
    onChange = (value,key) =>{
        const { regionList } = this.props
        // console.log(regionList[key.key],value,111111);
        this.setState(
            {cityList:regionList[key.key].children}
            )
            this.formRef.current.setFieldsValue({
                city:''
            })
    }
    handleOk = () => {
        const { onCancel ,getData} = this.props
        this.formRef.current.validateFields().then(values => {
            if (values) {
                let data = {
                    name: values.name,
                    phone: values.phone,
                    carType: values.carType,
                    province:values.province,
                    city:values.city,
                    infoTime:moment(values.infoTime).format('YYYY-MM-DD HH:mm:ss')
                }
                console.log(moment(values.infoTime).format('YYYY-MM-DD HH:mm:ss'));
                post(allUrl.ClueManagement.saveClueObj,{...data}).then(res=>{
                    if(res.success){
                        message.success(res.msg)
                        onCancel()
                        getData()
                    }else{
                        message.error(res.msg)
                    }
                })
                
            }
        })
    }

    render() {
        const { visible, title,onCancel,regionList } = this.props
        const { loading ,cityList} = this.state
        console.log(this.state.current)
        const layout = {
            labelCol: { span: 24 },
            wrapperCol: { span: 20 },
        };
            return (
                <Modal
                    visible={visible}
                    title={title}
                    onOk={this.handleOk}
                    onCancel={onCancel}
                    maskClosable={false}
                    width={800}
                    // bodyStyle={{ padding: '50px' }}

                >
                    <Form  {...layout} name="vertical" onFinish={this.onFinish} ref={this.formRef}>
                        <Row>
                            <Col span={12}>
                                <Form.Item label="客户姓名" name="name" rules={[{ required: true, message: '请输入客户姓名', }]}>
                                    <Input placeholder='请输入...' allowClear  />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label="联系方式" name="phone" rules={[{ required: true, message: '请输入客户手机号'},
                                        {
                                            validator(_, value) {
                                                if (value && !/^1[3-9]\d{9}$/.test(value)) {
                                                    return Promise.reject(new Error('请输入格式正确的手机号码'));
                                                } else {
                                                    return Promise.resolve();
                                                }
                                            },
                                        }]}>
                                    <Input placeholder='请输入...' allowClear  />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label="意向车型" name="carType"  rules={[{ required: true, message: '请选择意向车型', }]}>
                                    <Select placeholder='请选择...' allowClear>
                                        <Option value={'M9'}>M9</Option>
                                        <Option value={'M5'}>M5</Option>
                                        <Option value={'M5 EV'}>M5 EV</Option>
                                        <Option value={'M7'}>M7</Option>
                                        <Option value={'SF5'}>SF5</Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label="留资时间" name="infoTime"  rules={[{ required: true, message: '请选择留资时间', }]}>
                                <DatePicker style={{ width: '100%' }} showTime />
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label="意向购车省区"  name="province" rules={[{ required: true, message: '请输入意向购车省区', }]} >
                                    <Select placeholder='请选择购车省区'
                                     onChange={this.onChange}
                                    allowClear>
                                        {
                                            regionList&&regionList.map((item, index) =>  <Option key={index} value={item.name}>{item.name}</Option>)
                                        }
                                    </Select>
                                    
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label="意向购车城市"  name="city" rules={[{ required: true, message: '请输入意向购车城市', }]} >
                                    <Select placeholder='请选择省区后选择'
                                    allowClear>
                                         {
                                            cityList&&cityList.map((item, index) =>  <Option key={index} value={item.name}>{item.name}</Option>)
                                        }
                                    </Select>
                                </Form.Item>
                            </Col>
                            </Row>
                        </Form>
                </Modal>
            )
        }
    }
    export default connect()(ClueAdd)