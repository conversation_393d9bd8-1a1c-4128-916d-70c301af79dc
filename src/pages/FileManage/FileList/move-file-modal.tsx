import { postSeresFileDirectoryList, postSeresFileItemMove } from '@/apis/file-manage';
import { modalFormLayout } from '@/constants/form';
import { Form, message, Modal, ModalProps, TreeSelect, TreeSelectProps } from 'antd';
import React, { Key, useCallback, useEffect, useMemo, useState } from 'react';
import { FileBizType, ROOT_DIRECTORY_ID } from '../constants';

export interface MoveFileModalProps extends ModalProps {
  onOk?: (values: any) => void;
  data?: any;
  bizType?: FileBizType;
}
export interface TreeNode extends FileManage.DirectoryDTO {
  isLeaf?: boolean;
}
const MoveFileModal: React.FC<MoveFileModalProps> = ({ onOk, data, bizType, ...restProps }) => {
  const [form] = Form.useForm();
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await postSeresFileItemMove({ fileIdList: data.selectedRowKeys, dirId: values.dirId });
      message.success('移动成功');
      onOk?.(values);
    } catch (error) {}
  };

  const [treeList, setTreeList] = useState<TreeNode[]>([]);
  const treeData = useMemo(() => {
    return treeList?.map((item: any) => {
      return {
        id: item.id,
        pId: item.parentDirId,
        key: item.id,
        value: item.id,
        title: item.name,
        isLeaf: item.isLeaf,
        label: `${item.parentDirPath}${item.name}`,
      };
    });
  }, [treeList]);

  const handleLoadData: TreeSelectProps['loadData'] = useCallback(
    async ({ key = 0 as Key }) => {
      const [{ children: data = [] }] = await postSeresFileDirectoryList({ id: key as number, bizType });
      if (data.length === 0) {
        // 在treeList中找到当前节点，设置isLeaf为true
        const node = treeList.find(item => item.id === key);
        if (node) {
          node.isLeaf = true;
          setTreeList([...treeList]);
        }
      }
      setTreeList(prevData => [...prevData, ...data]);
    },
    [bizType, treeList]
  );

  useEffect(() => {
    restProps.open && setTreeList([]);
    restProps.open &&
      handleLoadData({
        key: ROOT_DIRECTORY_ID,
        props: undefined,
      });
  }, [bizType, restProps.open]);

  return (
    <Modal title="移动文件" maskClosable={false} onOk={handleOk} afterClose={form.resetFields} {...restProps}>
      <Form {...modalFormLayout} form={form}>
        <Form.Item name="dirId" label="目录" rules={[{ required: true, message: '请选择目录' }]}>
          <TreeSelect treeNodeLabelProp="label" treeDataSimpleMode treeData={treeData} loadData={handleLoadData} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default MoveFileModal;
