import React from 'react'
import { Modal, Descriptions, Empty, Button } from 'antd';
import ladderStyle from '@/pages/RightCenter/DetailManagement/maintenanceLadder/ladderPart.module.less'
import moment from 'moment';

const effectMap = {
    1: {
        color: '#CBA90A',
        name: '未生效'
    },
    2: {
        color: '#52C41A',
        name: '已生效'
    },
    3: {
        color: '#872EF1',
        name: '已使用'
    },
    4: {
        color: '#F5222D',
        name: '已失效'
    },
    5: {
        color: '#F87E02',
        name: '激活中'
    }
}

const statusMap = {
    1: {
        color: '#059287',
        name: '未使用'
    },
    2: {
        color: '#F87E02',
        name: '使用中'
    },
    3: {
        color: '#872EF1',
        name: '已使用'
    },
    4: {
        color: '#0398A2',
        name: '已用完'
    },
    5: {
        color: '#7D7D7D',
        name: '不可用'
    },
}

const LadderDetail = (props) => {
    const { isLadderVisible, handleLadderCancle, ladderData } = props
    const LadderPartDetailItem = (item, index) => {
        return <div className={ladderStyle['ladder-part']} key={index}>
            <div className={ladderStyle['num']}>{index + 1}</div>
            <div className={ladderStyle['content']}>
                <Descriptions column={2}>
                    <Descriptions.Item label='阶梯明细编码' span={2}>{item.ladderNo}</Descriptions.Item>
                    <Descriptions.Item label='阶梯生效状态' span={2}>
                        <span style={{ color: effectMap[item.effect]?.color }}>{effectMap[item.effect]?.name}</span>
                    </Descriptions.Item>
                    <Descriptions.Item label='阶梯使用状态' span={2}>
                        <span style={{ color: statusMap[item.status]?.color }}>{statusMap[item.status]?.name}</span>
                    </Descriptions.Item>
                    {
                        ladderData.maintenanceLadderLimitFrame !== 2 &&
                        <Descriptions.Item label='阶梯限制年限' span={2}>
                            {
                                `${item.ladderLimitYear ?? '-'}年${item.ladderLimitMonth ?? '-'}月`
                            }
                        </Descriptions.Item>
                    }
                    {
                        ladderData.maintenanceLadderLimitFrame !== 1 &&
                        <Descriptions.Item label='阶梯限制里程' span={2}>
                            {
                                `${item.ladderMileage ?? '-'}公里`
                            }
                        </Descriptions.Item>
                    }
                    {
                        ladderData.maintenanceLadderLimitFrame !== 2 &&
                        <>
                            <Descriptions.Item label='阶梯开始时间'>{item.ladderStartTime ? moment(item.ladderStartTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Descriptions.Item>
                            <Descriptions.Item label='阶梯结束时间'>{item.ladderEndTime ? moment(item.ladderEndTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Descriptions.Item>
                        </>
                    }
                    {
                        ladderData.maintenanceLadderLimitFrame !== 1 &&
                        <>
                            <Descriptions.Item label='阶梯开始里程'>{item.ladderStartMileage ?? '-'}公里</Descriptions.Item>
                            <Descriptions.Item label='阶梯结束里程'>{item.ladderEndMileage ?? '-'}公里</Descriptions.Item>
                        </>
                    }
                </Descriptions>
            </div>
        </div>

    }

    return (
        <Modal
            title="全保养阶梯策略"
            visible={isLadderVisible}
            maskClosable={false}
            onCancel={handleLadderCancle}
            width={800}
            footer={<Button onClick={handleLadderCancle}>返回</Button>}
        >
            {
                ladderData.resp ?
                    ladderData.resp.map((item, index) => LadderPartDetailItem(item, index)) :
                    <Empty />

            }

        </Modal>

    )
}

export default LadderDetail